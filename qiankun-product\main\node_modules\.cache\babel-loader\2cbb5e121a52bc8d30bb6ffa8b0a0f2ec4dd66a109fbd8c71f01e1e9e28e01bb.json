{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"HelpDocument\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_xyl_global_file = _resolveComponent(\"xyl-global-file\");\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_global_file, {\n    fileData: $setup.fileData\n  }, null, 8 /* PROPS */, [\"fileData\"]), !$setup.fileData.length ? (_openBlock(), _createBlock(_component_el_empty, {\n    key: 0,\n    description: \"暂无帮助文档\"\n  })) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_global_file", "fileData", "$setup", "length", "_createBlock", "_component_el_empty", "key", "description", "_createCommentVNode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\components\\HelpDocument.vue"], "sourcesContent": ["<template>\r\n  <div class=\"HelpDocument\">\r\n    <xyl-global-file :fileData=\"fileData\"></xyl-global-file>\r\n    <el-empty v-if=\"!fileData.length\" description=\"暂无帮助文档\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'HelpDocument' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\n\r\nconst fileData = ref([])\r\nconst getRoleHelpNote = async () => {\r\n  const { data } = await api.reqRoleHelpNote({ isSelectForLoginUser: 1 })\r\n  fileData.value = data?.map(item => item.attachment) || []\r\n}\r\n\r\nonMounted(() => { getRoleHelpNote() })\r\n</script>\r\n<style lang=\"scss\">\r\n.HelpDocument {\r\n  width: 600px;\r\n  padding: 40px;\r\n  min-height: 52vh;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;;;uBAAzBC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAAwDC,0BAAA;IAAtCC,QAAQ,EAAEC,MAAA,CAAAD;EAAQ,uC,CACnBC,MAAA,CAAAD,QAAQ,CAACE,MAAM,I,cAAhCC,YAAA,CAAyDC,mBAAA;IAH7DC,GAAA;IAGsCC,WAAW,EAAC;QAHlDC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}