{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"video-player-container\"\n};\nvar _hoisted_2 = {\n  class: \"player-container\"\n};\nvar _hoisted_3 = {\n  ref: \"videoPlayer\",\n  id: \"video-player\",\n  controls: \"\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"video\", _hoisted_3, null, 512 /* NEED_PATCH */)])]);\n}", "map": {"version": 3, "names": ["class", "ref", "id", "controls", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\components\\VideoPlayer.vue"], "sourcesContent": ["<template>\r\n  <div class=\"video-player-container\">\r\n    <div class=\"player-container\">\r\n      <video ref=\"videoPlayer\" id=\"video-player\" controls></video>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'VideoPlayer' }\r\n</script>\r\n\r\n<script setup>\r\nimport { ref, onBeforeUnmount, onMounted, nextTick, watch } from 'vue'\r\nimport Hls from 'hls.js'\r\n\r\nconst props = defineProps({\r\n  liveUrl: {\r\n    type: String,\r\n    default: ''\r\n  },\r\n  replayUrl: {\r\n    type: String,\r\n    default: ''\r\n  },\r\n  isReplay: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  autoInit: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n})\r\n\r\n// 视频播放器相关\r\nconst videoPlayer = ref(null)\r\nconst player = ref(null)\r\nconst hls = ref(null)\r\nconst isPlayerInitialized = ref(false)\r\n\r\n// 初始化视频播放器\r\nconst initVideoPlayer = async () => {\r\n  console.log('initVideoPlayer: 开始初始化直播播放器')\r\n  console.log('initVideoPlayer: isPlayerInitialized =', isPlayerInitialized.value)\r\n  console.log('initVideoPlayer: videoPlayer.value =', !!videoPlayer.value)\r\n\r\n  if (!videoPlayer.value) {\r\n    console.log('initVideoPlayer: video元素不存在，跳过初始化')\r\n    return\r\n  }\r\n\r\n  // 销毁现有播放器\r\n  destroyVideoPlayer()\r\n\r\n  const video = videoPlayer.value\r\n  player.value = video\r\n  isPlayerInitialized.value = true\r\n\r\n  console.log('initVideoPlayer: 原始直播URL:', props.liveUrl)\r\n\r\n  // HLS视频流地址 - 使用动态的liveUrl\r\n  const hlsUrl = getHlsUrl(props.liveUrl)\r\n\r\n  console.log('initVideoPlayer: 解析后的直播URL:', hlsUrl)\r\n\r\n  if (!hlsUrl) {\r\n    console.warn('initVideoPlayer: 没有推流地址')\r\n    return\r\n  }\r\n\r\n  // 检查浏览器是否原生支持HLS\r\n  if (video.canPlayType('application/vnd.apple.mpegurl')) {\r\n    // 原生支持HLS\r\n    video.src = hlsUrl\r\n    setupVideoEvents()\r\n  } else if (Hls.isSupported()) {\r\n    // 使用HLS.js库\r\n    hls.value = new Hls({\r\n      maxBufferLength: 30,\r\n      maxMaxBufferLength: 60,\r\n      startLevel: -1, // 自动选择适合的初始清晰度\r\n      maxBufferHole: 0.5,\r\n      highLatencyMode: false\r\n    })\r\n\r\n    // 加载视频流\r\n    hls.value.loadSource(hlsUrl)\r\n    hls.value.attachMedia(video)\r\n\r\n    // HLS事件监听\r\n    hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\r\n      console.log('视频流准备就绪，点击播放按钮开始')\r\n    })\r\n\r\n    // 错误处理\r\n    hls.value.on(Hls.Events.ERROR, function (_, data) {\r\n      console.error('HLS错误:', data)\r\n      switch (data.type) {\r\n        case Hls.ErrorTypes.NETWORK_ERROR:\r\n          hls.value.startLoad() // 尝试重新加载\r\n          break\r\n        case Hls.ErrorTypes.MEDIA_ERROR:\r\n          hls.value.recoverMediaError() // 尝试恢复媒体错误\r\n          break\r\n        default:\r\n          // 无法恢复的错误，尝试重新初始化\r\n          setTimeout(initVideoPlayer, 3000)\r\n          break\r\n      }\r\n    })\r\n\r\n    setupVideoEvents()\r\n  }\r\n}\r\n\r\n// 初始化回放播放器\r\nconst initReplayPlayer = async () => {\r\n  console.log('initReplayPlayer: 开始初始化回放播放器')\r\n  console.log('initReplayPlayer: isPlayerInitialized =', isPlayerInitialized.value)\r\n  console.log('initReplayPlayer: videoPlayer.value =', !!videoPlayer.value)\r\n\r\n  if (!videoPlayer.value) {\r\n    console.log('initReplayPlayer: video元素不存在，跳过初始化')\r\n    return\r\n  }\r\n\r\n  // 销毁现有播放器\r\n  destroyVideoPlayer()\r\n\r\n  const video = videoPlayer.value\r\n  player.value = video\r\n  isPlayerInitialized.value = true\r\n\r\n  console.log('initReplayPlayer: 原始回放URL:', props.replayUrl)\r\n\r\n  // 使用回放地址，也需要解析JSON格式\r\n  const replayUrl = getHlsUrl(props.replayUrl)\r\n\r\n  console.log('initReplayPlayer: 解析后的回放URL:', replayUrl)\r\n\r\n  if (!replayUrl) {\r\n    console.warn('initReplayPlayer: 没有回放地址')\r\n    return\r\n  }\r\n\r\n  console.log('initReplayPlayer: 开始播放回放:', replayUrl)\r\n\r\n  // 检查是否是HLS格式\r\n  if (replayUrl.includes('.m3u8') || replayUrl.includes('hls')) {\r\n    // HLS回放\r\n    if (video.canPlayType('application/vnd.apple.mpegurl')) {\r\n      // 原生支持HLS\r\n      video.src = replayUrl\r\n      setupVideoEvents()\r\n    } else if (Hls.isSupported()) {\r\n      // 使用HLS.js库\r\n      hls.value = new Hls({\r\n        maxBufferLength: 30,\r\n        maxMaxBufferLength: 60,\r\n        startLevel: -1,\r\n        maxBufferHole: 0.5,\r\n        highLatencyMode: false\r\n      })\r\n\r\n      hls.value.loadSource(replayUrl)\r\n      hls.value.attachMedia(video)\r\n\r\n      hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\r\n        console.log('回放视频准备就绪')\r\n      })\r\n\r\n      hls.value.on(Hls.Events.ERROR, function (_, data) {\r\n        console.error('HLS回放错误:', data)\r\n      })\r\n\r\n      setupVideoEvents()\r\n    }\r\n  } else {\r\n    // 普通视频格式\r\n    video.src = replayUrl\r\n    setupVideoEvents()\r\n  }\r\n}\r\n\r\n// 设置视频事件监听\r\nconst setupVideoEvents = () => {\r\n  console.log('setupVideoEvents: 开始设置视频事件监听')\r\n  const video = player.value\r\n  if (!video) {\r\n    console.log('setupVideoEvents: player.value不存在')\r\n    return\r\n  }\r\n\r\n  console.log('setupVideoEvents: 添加事件监听器')\r\n\r\n  // 视频可以播放时\r\n  video.addEventListener('canplay', function () {\r\n    console.log('视频准备就绪，点击播放按钮开始')\r\n  })\r\n\r\n  // 播放事件\r\n  video.addEventListener('play', function () {\r\n    console.log('正在播放HLS视频流')\r\n  })\r\n\r\n  // 暂停事件\r\n  video.addEventListener('pause', function () {\r\n    console.log('HLS视频流已暂停')\r\n  })\r\n\r\n  // 视频结束事件\r\n  video.addEventListener('ended', function () {\r\n    console.log('视频播放已结束')\r\n  })\r\n\r\n  // 音量变化事件\r\n  video.addEventListener('volumechange', function () {\r\n    console.log('音量变化')\r\n  })\r\n\r\n  // 添加错误事件监听\r\n  video.addEventListener('error', function (e) {\r\n    console.error('视频播放错误:', e)\r\n  })\r\n\r\n  // 添加加载开始事件\r\n  video.addEventListener('loadstart', function () {\r\n    console.log('开始加载视频')\r\n  })\r\n\r\n  // 添加元数据加载完成事件\r\n  video.addEventListener('loadedmetadata', function () {\r\n    console.log('视频元数据加载完成')\r\n  })\r\n}\r\n\r\n// 销毁视频播放器（包括直播和回放播放器）\r\nconst destroyVideoPlayer = () => {\r\n  console.log('destroyVideoPlayer: 开始销毁播放器')\r\n\r\n  // 销毁 hls.js 实例（直播和回放都可能使用）\r\n  if (hls.value) {\r\n    try {\r\n      console.log('destroyVideoPlayer: 销毁HLS实例')\r\n      hls.value.destroy()\r\n    } catch (error) {\r\n      console.error('销毁HLS实例错误:', error)\r\n    }\r\n    hls.value = null\r\n  }\r\n\r\n  // 销毁video元素播放器（直播和回放共用同一个video元素）\r\n  if (player.value) {\r\n    try {\r\n      console.log('destroyVideoPlayer: 清理player引用')\r\n      player.value.pause() // 停止播放\r\n      player.value.currentTime = 0 // 重置播放时间\r\n      player.value.src = '' // 清空视频源\r\n      player.value.removeAttribute('src') // 移除src属性\r\n      player.value.load() // 重新加载空的video元素\r\n      player.value.muted = false // 静音\r\n    } catch (error) {\r\n      console.error('销毁播放器错误:', error)\r\n    }\r\n    player.value = null\r\n  }\r\n\r\n  // 确保video元素也被清理\r\n  if (videoPlayer.value) {\r\n    try {\r\n      console.log('destroyVideoPlayer: 清理video元素')\r\n      videoPlayer.value.pause()\r\n      videoPlayer.value.currentTime = 0\r\n      videoPlayer.value.src = ''\r\n      videoPlayer.value.removeAttribute('src')\r\n      videoPlayer.value.load()\r\n      videoPlayer.value.muted = true\r\n    } catch (error) {\r\n      console.error('清理video元素错误:', error)\r\n    }\r\n  }\r\n\r\n  // 重置状态\r\n  isPlayerInitialized.value = false\r\n\r\n  console.log('视频播放器已完全销毁')\r\n}\r\n\r\n// 从推流地址中获取HLS地址\r\nconst getHlsUrl = (liveUrl) => {\r\n  if (!liveUrl) return null\r\n\r\n  console.log('原始推流地址:', liveUrl)\r\n\r\n  // 如果liveUrl是JSON格式，解析出HLS地址\r\n  try {\r\n    const urlData = JSON.parse(liveUrl)\r\n    console.log('解析的JSON数据:', urlData)\r\n    const hlsUrl = urlData.hls || urlData.m3u8 || liveUrl\r\n    console.log('提取的HLS地址:', hlsUrl)\r\n    return hlsUrl\r\n  } catch (error) {\r\n    console.log('不是JSON格式，直接使用原地址')\r\n    // 如果不是JSON格式，直接返回原地址\r\n    return liveUrl\r\n  }\r\n}\r\n\r\n// 监听回放状态变化\r\nwatch(() => props.isReplay, (isReplay) => {\r\n  console.log('VideoPlayer: isReplay状态变化为:', isReplay)\r\n  nextTick(() => {\r\n    initPlayer()\r\n  })\r\n})\r\n\r\n// 监听URL变化\r\nwatch(() => [props.liveUrl, props.replayUrl], ([newLiveUrl, newReplayUrl]) => {\r\n  console.log('VideoPlayer: URL变化检测')\r\n  console.log('VideoPlayer: 新的直播URL:', newLiveUrl)\r\n  console.log('VideoPlayer: 新的回放URL:', newReplayUrl)\r\n\r\n  if (props.autoInit) {\r\n    console.log('VideoPlayer: URL变化，重新初始化播放器')\r\n    nextTick(() => {\r\n      initPlayer()\r\n    })\r\n  }\r\n})\r\n\r\n// 统一的初始化方法\r\nconst initPlayer = () => {\r\n  console.log('VideoPlayer: 开始初始化播放器')\r\n  console.log('VideoPlayer: isReplay =', props.isReplay)\r\n  console.log('VideoPlayer: liveUrl =', props.liveUrl)\r\n  console.log('VideoPlayer: replayUrl =', props.replayUrl)\r\n\r\n  // 先销毁现有播放器，确保每次初始化都是干净的\r\n  console.log('VideoPlayer: 先销毁现有播放器')\r\n  destroyVideoPlayer()\r\n\r\n  // 强制重置状态\r\n  console.log('VideoPlayer: 重置播放器状态')\r\n  isPlayerInitialized.value = false\r\n\r\n  // 等待一小段时间确保销毁完成\r\n  setTimeout(() => {\r\n    if (props.isReplay) {\r\n      console.log('VideoPlayer: 初始化回放播放器')\r\n      initReplayPlayer()\r\n    } else {\r\n      console.log('VideoPlayer: 初始化直播播放器')\r\n      initVideoPlayer()\r\n    }\r\n  }, 50)\r\n}\r\n\r\nonMounted(() => {\r\n  console.log('VideoPlayer: 组件已挂载')\r\n  // 组件挂载后自动初始化播放器\r\n  if (props.autoInit) {\r\n    nextTick(() => {\r\n      initPlayer()\r\n    })\r\n  }\r\n})\r\n\r\nonBeforeUnmount(() => {\r\n  destroyVideoPlayer()\r\n})\r\n\r\n// 暴露方法给父组件\r\ndefineExpose({\r\n  initVideoPlayer,\r\n  initReplayPlayer,\r\n  destroyVideoPlayer,\r\n  initPlayer\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.video-player-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2;\r\n\r\n  .player-container {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n    #video-player {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAkB;;EACpBC,GAAG,EAAC,aAAa;EAACC,EAAE,EAAC,cAAc;EAACC,QAAQ,EAAR;;;uBAF/CC,mBAAA,CAIM,OAJNC,UAIM,GAHJC,mBAAA,CAEM,OAFNC,UAEM,GADJD,mBAAA,CAA4D,SAA5DE,UAA4D,8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}