{"ast": null, "code": "import api from '@/api';\nimport { ref, onMounted, onActivated } from 'vue';\nimport { useRoute } from 'vue-router';\nimport utils from 'common/js/utils.js';\nimport { user } from 'common/js/system_var.js';\nimport IEPNG from '../img/IE.png';\nvar __default__ = {\n  name: 'OtherRouter'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var isEnter = true;\n    var url = ref('');\n    onMounted(function () {\n      if (!isEnter) return;\n      if (route.query.url) {\n        if (route.query.menuRouteType === '2') {\n          url.value = api.fileURL(utils.decrypt(route.query.url, new Date().getTime(), '2'));\n        } else {\n          var path = utils.decrypt(route.query.url, new Date().getTime(), '2');\n          url.value = pathData(path);\n          if (route.query.menuRouteType === '4') {\n            window.open(url.value, '_blank');\n          }\n        }\n      }\n    });\n    onActivated(function () {\n      if (isEnter) {\n        return isEnter = false;\n      }\n      if (route.query.url) {\n        if (route.query.menuRouteType === '2') {\n          url.value = api.fileURL(utils.decrypt(route.query.url, new Date().getTime(), '2'));\n        } else {\n          var path = utils.decrypt(route.query.url, new Date().getTime(), '2');\n          url.value = pathData(path);\n          if (route.query.menuRouteType === '4') {\n            window.open(url.value, '_blank');\n          }\n        }\n      }\n    });\n    var pathData = function pathData(path) {\n      var token = sessionStorage.getItem('token') || '';\n      var areaId = sessionStorage.getItem('AreaId') || '';\n      var paramsData = [];\n      if (route.query.token) {\n        paramsData.push(`${route.query.token}=${token}`);\n      }\n      if (route.query.areaId) {\n        paramsData.push(`${route.query.areaId}=${areaId}`);\n      }\n      if (route.query.mobile) {\n        paramsData.push(`${route.query.mobile}=${user.value.mobile}`);\n      }\n      if (route.query.params) {\n        var _paramsData = route.query.params.split(',');\n        for (var i in _paramsData) {\n          _paramsData.push(`${_paramsData[i]}=${route.query[_paramsData[i]]}`);\n        }\n      }\n      var urlPath = path + (path.indexOf('?') !== -1 ? '&' : '?') + paramsData.join('&');\n      var mainUrlPath = api.authorize(`${path}&${paramsData.join('&')}`);\n      return route.query.main === 'true' ? mainUrlPath : urlPath;\n    };\n    var blankOpen = function blankOpen() {\n      window.open(url.value, '_blank');\n    };\n    var __returned__ = {\n      route,\n      get isEnter() {\n        return isEnter;\n      },\n      set isEnter(v) {\n        isEnter = v;\n      },\n      url,\n      pathData,\n      blankOpen,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      onActivated,\n      get useRoute() {\n        return useRoute;\n      },\n      get utils() {\n        return utils;\n      },\n      get user() {\n        return user;\n      },\n      get IEPNG() {\n        return IEPNG;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["api", "ref", "onMounted", "onActivated", "useRoute", "utils", "user", "IEPNG", "__default__", "name", "route", "isEnter", "url", "query", "menuRouteType", "value", "fileURL", "decrypt", "Date", "getTime", "path", "pathData", "window", "open", "token", "sessionStorage", "getItem", "areaId", "paramsData", "push", "mobile", "params", "split", "i", "url<PERSON><PERSON>", "indexOf", "join", "mainUrlPath", "authorize", "main", "blankOpen"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/OtherRouter/OtherRouter.vue"], "sourcesContent": ["<template>\r\n  <div class=\"OtherRouter\">\r\n    <template v-if=\"route.query.menuRouteType === '2'\">\r\n      <el-scrollbar class=\"OtherRouterImg\">\r\n        <img :src=\"url\" />\r\n      </el-scrollbar>\r\n    </template>\r\n    <template v-if=\"route.query.menuRouteType === '3'\">\r\n      <iframe class=\"OtherRouterIframe\" frameborder=\"0\" :src=\"url\"></iframe>\r\n    </template>\r\n    <template v-if=\"route.query.menuRouteType === '4'\">\r\n      <el-empty :image=\"IEPNG\" :description=\"url\">\r\n        <el-button type=\"primary\" @click=\"blankOpen\">打开系统</el-button>\r\n      </el-empty>\r\n    </template>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'OtherRouter' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport utils from 'common/js/utils.js'\r\nimport { user } from 'common/js/system_var.js'\r\nimport IEPNG from '../img/IE.png'\r\nconst route = useRoute()\r\nlet isEnter = true\r\nconst url = ref('')\r\nonMounted(() => {\r\n  if (!isEnter) return\r\n  if (route.query.url) {\r\n    if (route.query.menuRouteType === '2') {\r\n      url.value = api.fileURL(utils.decrypt(route.query.url, new Date().getTime(), '2'))\r\n    } else {\r\n      const path = utils.decrypt(route.query.url, new Date().getTime(), '2')\r\n      url.value = pathData(path)\r\n      if (route.query.menuRouteType === '4') {\r\n        window.open(url.value, '_blank')\r\n      }\r\n    }\r\n  }\r\n})\r\nonActivated(() => {\r\n  if (isEnter) { return isEnter = false }\r\n  if (route.query.url) {\r\n    if (route.query.menuRouteType === '2') {\r\n      url.value = api.fileURL(utils.decrypt(route.query.url, new Date().getTime(), '2'))\r\n    } else {\r\n      const path = utils.decrypt(route.query.url, new Date().getTime(), '2')\r\n      url.value = pathData(path)\r\n      if (route.query.menuRouteType === '4') {\r\n        window.open(url.value, '_blank')\r\n      }\r\n    }\r\n  }\r\n})\r\nconst pathData = (path) => {\r\n  const token = sessionStorage.getItem('token') || ''\r\n  const areaId = sessionStorage.getItem('AreaId') || ''\r\n  var paramsData = []\r\n  if (route.query.token) {\r\n    paramsData.push(`${route.query.token}=${token}`)\r\n  }\r\n  if (route.query.areaId) {\r\n    paramsData.push(`${route.query.areaId}=${areaId}`)\r\n  }\r\n  if (route.query.mobile) {\r\n    paramsData.push(`${route.query.mobile}=${user.value.mobile}`)\r\n  }\r\n  if (route.query.params) {\r\n    const paramsData = route.query.params.split(',')\r\n    for (const i in paramsData) {\r\n      paramsData.push(`${paramsData[i]}=${route.query[paramsData[i]]}`)\r\n    }\r\n  }\r\n  const urlPath = path + (path.indexOf('?') !== -1 ? '&' : '?') + paramsData.join('&')\r\n  const mainUrlPath = api.authorize(`${path}&${paramsData.join('&')}`)\r\n  return route.query.main === 'true' ? mainUrlPath : urlPath\r\n}\r\nconst blankOpen = () => { window.open(url.value, '_blank') }\r\n</script>\r\n<style lang=\"scss\">\r\n.OtherRouter {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n\r\n  .OtherRouterImg {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n    img {\r\n      width: 100%;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .OtherRouterIframe {\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: auto;\r\n  }\r\n\r\n  .zy-el-empty__description {\r\n    display: none;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAqBA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AACjD,SAASC,QAAQ,QAAQ,YAAY;AACrC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,OAAOC,KAAK,MAAM,eAAe;AARjC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAc,CAAC;;;;;IAStC,IAAMC,KAAK,GAAGN,QAAQ,CAAC,CAAC;IACxB,IAAIO,OAAO,GAAG,IAAI;IAClB,IAAMC,GAAG,GAAGX,GAAG,CAAC,EAAE,CAAC;IACnBC,SAAS,CAAC,YAAM;MACd,IAAI,CAACS,OAAO,EAAE;MACd,IAAID,KAAK,CAACG,KAAK,CAACD,GAAG,EAAE;QACnB,IAAIF,KAAK,CAACG,KAAK,CAACC,aAAa,KAAK,GAAG,EAAE;UACrCF,GAAG,CAACG,KAAK,GAAGf,GAAG,CAACgB,OAAO,CAACX,KAAK,CAACY,OAAO,CAACP,KAAK,CAACG,KAAK,CAACD,GAAG,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpF,CAAC,MAAM;UACL,IAAMC,IAAI,GAAGf,KAAK,CAACY,OAAO,CAACP,KAAK,CAACG,KAAK,CAACD,GAAG,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;UACtEP,GAAG,CAACG,KAAK,GAAGM,QAAQ,CAACD,IAAI,CAAC;UAC1B,IAAIV,KAAK,CAACG,KAAK,CAACC,aAAa,KAAK,GAAG,EAAE;YACrCQ,MAAM,CAACC,IAAI,CAACX,GAAG,CAACG,KAAK,EAAE,QAAQ,CAAC;UAClC;QACF;MACF;IACF,CAAC,CAAC;IACFZ,WAAW,CAAC,YAAM;MAChB,IAAIQ,OAAO,EAAE;QAAE,OAAOA,OAAO,GAAG,KAAK;MAAC;MACtC,IAAID,KAAK,CAACG,KAAK,CAACD,GAAG,EAAE;QACnB,IAAIF,KAAK,CAACG,KAAK,CAACC,aAAa,KAAK,GAAG,EAAE;UACrCF,GAAG,CAACG,KAAK,GAAGf,GAAG,CAACgB,OAAO,CAACX,KAAK,CAACY,OAAO,CAACP,KAAK,CAACG,KAAK,CAACD,GAAG,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpF,CAAC,MAAM;UACL,IAAMC,IAAI,GAAGf,KAAK,CAACY,OAAO,CAACP,KAAK,CAACG,KAAK,CAACD,GAAG,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;UACtEP,GAAG,CAACG,KAAK,GAAGM,QAAQ,CAACD,IAAI,CAAC;UAC1B,IAAIV,KAAK,CAACG,KAAK,CAACC,aAAa,KAAK,GAAG,EAAE;YACrCQ,MAAM,CAACC,IAAI,CAACX,GAAG,CAACG,KAAK,EAAE,QAAQ,CAAC;UAClC;QACF;MACF;IACF,CAAC,CAAC;IACF,IAAMM,QAAQ,GAAG,SAAXA,QAAQA,CAAID,IAAI,EAAK;MACzB,IAAMI,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;MACnD,IAAMC,MAAM,GAAGF,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;MACrD,IAAIE,UAAU,GAAG,EAAE;MACnB,IAAIlB,KAAK,CAACG,KAAK,CAACW,KAAK,EAAE;QACrBI,UAAU,CAACC,IAAI,CAAC,GAAGnB,KAAK,CAACG,KAAK,CAACW,KAAK,IAAIA,KAAK,EAAE,CAAC;MAClD;MACA,IAAId,KAAK,CAACG,KAAK,CAACc,MAAM,EAAE;QACtBC,UAAU,CAACC,IAAI,CAAC,GAAGnB,KAAK,CAACG,KAAK,CAACc,MAAM,IAAIA,MAAM,EAAE,CAAC;MACpD;MACA,IAAIjB,KAAK,CAACG,KAAK,CAACiB,MAAM,EAAE;QACtBF,UAAU,CAACC,IAAI,CAAC,GAAGnB,KAAK,CAACG,KAAK,CAACiB,MAAM,IAAIxB,IAAI,CAACS,KAAK,CAACe,MAAM,EAAE,CAAC;MAC/D;MACA,IAAIpB,KAAK,CAACG,KAAK,CAACkB,MAAM,EAAE;QACtB,IAAMH,WAAU,GAAGlB,KAAK,CAACG,KAAK,CAACkB,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;QAChD,KAAK,IAAMC,CAAC,IAAIL,WAAU,EAAE;UAC1BA,WAAU,CAACC,IAAI,CAAC,GAAGD,WAAU,CAACK,CAAC,CAAC,IAAIvB,KAAK,CAACG,KAAK,CAACe,WAAU,CAACK,CAAC,CAAC,CAAC,EAAE,CAAC;QACnE;MACF;MACA,IAAMC,OAAO,GAAGd,IAAI,IAAIA,IAAI,CAACe,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGP,UAAU,CAACQ,IAAI,CAAC,GAAG,CAAC;MACpF,IAAMC,WAAW,GAAGrC,GAAG,CAACsC,SAAS,CAAC,GAAGlB,IAAI,IAAIQ,UAAU,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;MACpE,OAAO1B,KAAK,CAACG,KAAK,CAAC0B,IAAI,KAAK,MAAM,GAAGF,WAAW,GAAGH,OAAO;IAC5D,CAAC;IACD,IAAMM,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MAAElB,MAAM,CAACC,IAAI,CAACX,GAAG,CAACG,KAAK,EAAE,QAAQ,CAAC;IAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}