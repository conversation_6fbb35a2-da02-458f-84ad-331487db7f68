<template>
  <div class="GlobalAiChat" data-no-text-select>
    <div class="GlobalAiChatClose" @click="handleClick">
      <el-icon>
        <Close />
      </el-icon>
    </div>
    <div class="GlobalAiChatLogo">
      <el-image :src="IntelligentAssistant" loading="lazy" fit="cover" draggable="false" />
    </div>
    <div class="GlobalAiChatHead forbidSelect">
      <div class="GlobalAiChatHeadName">您好 {{ user.userName }}!</div>
      <div class="GlobalAiChatHeadText">我是西安政协深度融合DeepSeek的综合助手，内容由 AI 生成，请仔细甄别。</div>
    </div>
    <div class="GlobalAiChatDialogueBody">
      <GlobalAiChatHistory ref="chatHistoryRef" v-model="chatId" @select="handleSelect"></GlobalAiChatHistory>
      <div class="GlobalAiChatDialogue ellipsis">{{ chatContent || '新对话' }}</div>
      <div class="GlobalAiChatDialogueNew" @click="handleNewDialogue"><span v-html="newIcon"></span></div>
    </div>
    <el-scrollbar always ref="scrollRef" class="GlobalAiChatScroll" @scroll="handleScroll">
      <div class="GlobalAiChatBody">
        <div class="GlobalAiChatBodyTipsBody" v-if="aiWords.length">
          <div class="GlobalAiChatBodyTipsVice">您可以试着问我：</div>
          <div class="GlobalAiChatBodyTipsItem" v-for="item in aiWords" :key="item.id">
            <div class="GlobalAiChatBodyTips" @click="handleSendMessage(item.promptWord)">
              <span v-html="tipsIcon"></span>
              {{ item.promptWord }}
            </div>
          </div>
        </div>
        <div :class="[item.type ? 'GlobalAiChatSelfMessage' : 'GlobalAiChatMessage']"
          v-for="(item, index) in chatMessage" :key="item.id">
          <template v-if="item.type">
            <div class="GlobalAiChatSelfMessageFile" v-for="item in item.fileData" :key="item.id"
              @click="handlePreview(item)">
              <div class="globalFileIcon" :class="fileIcon(item?.extName)"></div>
              <div class="GlobalChatMessagesFileName ellipsis">{{ item?.originalFileName || '未知文件' }}</div>
              <div class="GlobalChatMessagesFileSize">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>
            </div>
            <div class="GlobalAiChatSelfMessageInfo">{{ item.content }}</div>
          </template>
          <template v-if="!item.type">
            <!-- <el-image :src="IntelligentAssistant" loading="lazy" fit="cover" draggable="false" /> -->
            <div class="GlobalAiChatMessageInfo">
              <div class="GlobalAiChatMessagePonder forbidSelect" @click="item.ponderShow = !item.ponderShow"
                v-if="item.time">
                <div v-html="ponderIcon"></div>
                已深度思考
                <span v-if="item.time !== '1'">（用时 {{ item.time }}）</span>
                <el-icon>
                  <ArrowUpBold v-if="item.ponderShow" />
                  <ArrowDownBold v-if="!item.ponderShow" />
                </el-icon>
              </div>
              <div class="GlobalAiChatMessagePonderContent" v-show="item.ponderShow">
                <GlobalMarkdown :ref="(el) => getElPonderRef(el, index)" v-model="item.ponderContent"
                  :content="item.ponderContentOld" @update="handleUpdate" />
              </div>
              <div class="GlobalAiChatMessageContent">
                <GlobalMarkdown :ref="(el) => getElRef(el, index)" v-model="item.content" :content="item.contentOld"
                  :on-link-click="handleLinkClick" @update="handleUpdate" />
                <div class="GlobalAiChatMessageDataList">
                  <div class="GlobalAiChatMessageDataItem" v-for="row in item.dataList" @click="handleDataList(row)">
                    {{ row.sourceName }}
                  </div>
                </div>
                <div class="GlobalAiChatMessageChart">
                  <GlobalAiChart v-for="(row, i) in item.chartData" :key="i + 'chartData'" :option="row" />
                </div>
                <div class="GlobalAiChatMessageLoading" v-if="index === chatMessage.length - 1 && loading">
                  <div class="answerLoading" v-html="loadingIcon"></div>
                </div>
                <div class="GlobalAiChatBodyTipsVice" v-if="item.guideWord.length">您是不是想问：</div>
                <div class="GlobalAiChatBodyTipsItem" v-for="(row, i) in item.guideWord" :key="i + 'guideWord'">
                  <div class="GlobalAiChatBodyTips" @click="handleGuideWord(row)">
                    <span v-html="tipsIcon"></span>
                    {{ row.question }}
                  </div>
                </div>
              </div>
              <div class="GlobalAiChatMessageControls" v-if="item.isControls">
                <el-tooltip content="复制" placement="top">
                  <div class="GlobalAiChatMessageControlsItem" @click="handleCopyMessage(item.content, index)">
                    <el-icon>
                      <CopyDocument />
                    </el-icon>
                  </div>
                </el-tooltip>
                <el-tooltip content="重新生成" placement="top">
                  <div class="GlobalAiChatMessageControlsItem" @click="handleRetryMessage(item, index)">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                  </div>
                </el-tooltip>
                <el-tooltip content="满意度调查" placement="top">
                  <div class="GlobalAiChatMessageControlsItem" @click="handleSatisfactionSurvey(item, index)">
                    <el-icon>
                      <Star />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </template>
        </div>
      </div>
    </el-scrollbar>
    <div class="GlobalAiChatDialogueEditor">
      <div class="GlobalAiChatDialogueTools" v-show="!toolId">
        <div class="GlobalAiChatDialogueTool" v-for="item in aiTools" :key="item.id"
          @click="handleToolSendMessage(item)">
          <span v-html="toolIcon"></span>
          {{ item.chatToolName }}
        </div>
      </div>
      <div class="GlobalAiChatDialogueEditorBody">
        <div class="GlobalAiChatToolsActive" v-show="toolId">
          <div class="GlobalAiChatToolsActiveName">{{ toolName }}</div>
          <div class="GlobalAiChatToolsActiveIcon" @click="handleToolClose">
            <el-icon>
              <Close />
            </el-icon>
          </div>
        </div>
        <GlobalAiChatFile :fileList="fileList" :fileData="fileData" @close="handleClose"
          v-show="fileList.length || fileData.length" />
        <GlobalAiChatEditor ref="editorRef" v-model="sendContent" :disabled="disabled" @send="handleSendMessage"
          @stop="handleStopMessage" @uploadCallback="handleFileUpload" @fileCallback="handleFileCallback" />
      </div>
    </div>
    <xyl-popup-window v-model="dataShow" :name="dataName">
      <GlobalAiChatData :data="dataInfo"></GlobalAiChatData>
    </xyl-popup-window>
    <CustomSatisfactionModal v-model="satisfactionSurveyShow" :data="dataInfo"></CustomSatisfactionModal>
  </div>
</template>
<script>
export default { name: 'GlobalAiChat' }
</script>
<script setup>
import api from '@/api'
import { ref, computed, onMounted, onUnmounted, nextTick, watch, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'
import { size2Str } from 'common/js/utils.js'
import http_stream from 'common/http/stream.js'
import { AiChatClass } from 'common/js/GlobalClass.js'
import { globalFileLocation } from 'common/config/location'
import config from 'common/config/index'
import { user, IntelligentAssistant } from 'common/js/system_var.js'
import { ElMessage } from 'element-plus'
const GlobalMarkdown = defineAsyncComponent(() => import('common/components/global-markdown/global-markdown.vue'))
const GlobalAiChatFile = defineAsyncComponent(() => import('./GlobalAiChatFile'))
const GlobalAiChatEditor = defineAsyncComponent(() => import('./GlobalAiChatEditor'))
const GlobalAiChatHistory = defineAsyncComponent(() => import('./GlobalAiChatHistory'))
const GlobalAiChatData = defineAsyncComponent(() => import('./GlobalAiChatData'))
const CustomSatisfactionModal = defineAsyncComponent(() => import('./CustomSatisfactionModal'))
const GlobalAiChart = defineAsyncComponent(() => import('./GlobalAiChart'))
const store = useStore()
const props = defineProps({ modelValue: { type: Boolean, default: false } })
const emit = defineEmits(['update:modelValue'])
const newIcon =
  '<svg t="1741161744028" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3254" width="26" height="26"><path d="M566.464 150.336c22.144 0 39.744 17.216 40.576 39.296a40.448 40.448 0 0 1-38.08 41.792H183.488v432.128h171.2c21.312 0 38.912 16.384 40.576 37.696v52.416l66.368-76.16a39.872 39.872 0 0 1 27.456-13.952h319.04v-190.08c0-21.248 16.384-38.912 37.76-40.512h2.816c21.312 0 38.912 16.384 40.512 37.696v198.208c0 40.576-31.936 73.728-72.064 75.776H510.784l-125.76 143.808a40.832 40.832 0 0 1-71.296-23.808v-119.552H178.176c-40.576 0-73.728-32-75.776-72.128V226.112c0-40.576 32-73.728 72.064-75.776h392z m35.648 340.8c18.816 0 34.816 14.72 35.648 33.92a35.264 35.264 0 0 1-32.768 36.48h-285.44a35.328 35.328 0 0 1-2.944-70.4h285.504zM443.2 349.824c19.2 0 34.816 15.104 35.648 33.92a35.264 35.264 0 0 1-32.768 36.48H319.488a35.328 35.328 0 0 1-2.88-70.4h126.592z m335.424-229.376c18.432 0 34.048 14.336 35.2 32.768v72.896h70.528a35.264 35.264 0 0 1 2.88 70.4h-72.96v70.464c0 18.88-14.72 34.816-33.92 35.648a35.264 35.264 0 0 1-36.48-32.768V296.96h-70.464a35.264 35.264 0 0 1-2.88-70.4h72.96V155.968a34.816 34.816 0 0 1 35.2-35.584z" p-id="3255"></path></svg>'
const tipsIcon =
  '<svg t="1741241762761" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7848" width="14" height="14"><path d="M115.152 356.453c-1.492-9.942-2.486-18.724-3.314-25.849-0.829-7.125-0.995-14.251-0.995-21.541 0-22.867 3.314-38.112 9.611-45.237 6.463-7.125 15.41-10.77 27.01-10.77h198.343L410.596 5.001c40.266 2.818 67.109 8.285 80.863 16.239 13.753 7.954 20.546 17.564 20.546 28.998v15.079L460.141 252.89h239.438L766.522 4.836c40.266 2.817 67.108 8.285 80.862 16.238 13.753 7.954 20.547 17.565 20.547 28.998 0 5.8-0.829 10.771-2.154 15.079l-49.71 187.739h170.34c2.817 8.617 4.309 16.902 4.309 24.855v22.701c0 21.541-3.314 36.289-9.776 44.242-6.463 7.954-15.41 11.765-27.01 11.765H788.063L710.35 643.281h200.498c1.326 10.108 2.485 19.056 3.314 27.01a217.169 217.169 0 0 1 1.159 22.701c0 37.448-12.262 56.007-36.619 56.007H682.181l-73.24 269.595c-40.265-2.816-66.942-8.285-79.867-16.072-12.925-7.954-19.387-17.564-19.387-29.164 0-5.634 0.662-10.107 2.154-12.925l56.006-211.269H326.421l-71.086 269.597c-40.265-2.817-67.606-8.286-82.022-16.074-14.416-7.953-21.541-17.564-21.541-29.163 0-2.816 0.331-4.971 0.994-6.462 0.663-1.326 0.994-3.646 0.994-6.463l58.327-211.269H39.592c-2.817-10.107-4.308-19.056-4.308-27.009V699.62c0-21.541 3.314-36.289 9.776-44.242 6.463-7.954 15.41-11.765 27.01-11.765h168.186l75.394-286.829H115.152v-0.331z m239.272 286.828H595.85l77.714-286.828H432.138l-77.714 286.828z" p-id="7849"></path></svg>'
const loadingIcon =
  '<svg t="1716976607389" viewBox="0 0 1024 1024" version="1.1" p-id="2362" width="60%" height="60%"><path d="M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z" fill="#2c2c2c" p-id="2363"></path><path d="M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z" fill="#2c2c2c" p-id="2364"></path><path d="M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z" fill="#2c2c2c" p-id="2365"></path><path d="M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z" fill="#2c2c2c" p-id="2366"></path><path d="M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z" fill="#2c2c2c" p-id="2367"></path><path d="M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z" fill="#2c2c2c" p-id="2368"></path><path d="M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z" fill="#2c2c2c" p-id="2369"></path><path d="M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z" fill="#2c2c2c" p-id="2370"></path></svg>'
const toolIcon =
  '<svg t="1741338779911" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9993" width="16" height="16"><path d="M707.2 350.976l-198.496 115.264-195.072-111.712c-37.088-21.12-68.736 34.528-31.648 55.616l198.72 113.792v256.736a32 32 0 0 0 64 0v-258.016c0-1.056-0.512-1.952-0.608-3.008l194.752-113.088c37.088-21.056 5.44-76.704-31.648-55.584z" p-id="9994"></path><path d="M880.288 232.48L560.192 45.12a95.648 95.648 0 0 0-96.64 0L143.68 232.48A96.64 96.64 0 0 0 96 315.904v397.664c0 34.784 18.624 66.88 48.736 84l320 181.92a95.52 95.52 0 0 0 94.496 0l320-181.92A96.576 96.576 0 0 0 928 713.568V315.904a96.64 96.64 0 0 0-47.712-83.424zM864 713.568c0 11.584-6.208 22.304-16.256 28l-320 181.92a31.776 31.776 0 0 1-31.488 0l-320-181.92A32.192 32.192 0 0 1 160 713.568V315.904c0-11.456 6.048-22.016 15.904-27.808l319.872-187.36a31.84 31.84 0 0 1 32.192 0l320.128 187.392c9.856 5.728 15.904 16.32 15.904 27.776v397.664z" p-id="9995"></path></svg>'
const ponderIcon =
  '<svg t="1741658991857" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11203" width="16" height="16"><path d="M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z" p-id="11204"></path><path d="M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z" p-id="11205"></path><path d="M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z" p-id="11206"></path></svg>'
const elShow = computed({
  get () {
    return props.modelValue
  },
  set (value) {
    emit('update:modelValue', value)
  }
})
const elRefs = ref([])
const getElRef = (el, i) => {
  const index = (i + 1) / 2 - 1
  if (el) elRefs.value[index] = el
}
const elRef = computed(() => elRefs.value[elRefs.value.length - 1])
const elPonderRefs = ref([])
const getElPonderRef = (el, i) => {
  const index = (i + 1) / 2 - 1
  if (el) elPonderRefs.value[index] = el
}
const elPonderRef = computed(() => elPonderRefs.value[elPonderRefs.value.length - 1])
const AiChatId = ref('')
const AiChatCode = ref('test_chat')
const AiChatModule = ref('')
const AiChatModuleId = ref('')
const elIndex = ref(0)
const AiChatParams = ref({})
const AiChatContent = ref('')
const aiTools = ref([])
const aiWords = ref([])
const chatId = ref('')
const chatObj = ref({})
const chatContent = ref('')
const chatMessage = ref([])
const chatMessageTotal = ref(0)
const chatMessageUpdate = ref(0)
const wrapScrollHeight = ref(0)
const chatHistoryRef = ref()
const scrollRef = ref()
const editorRef = ref()
const fileList = ref([])
const fileData = ref([])
const sendContent = ref('')
const isScroll = ref(false)
const toolId = ref('')
const toolName = ref('')
const toolIsParam = ref(0)
const toolRequired = ref(0)

let currentRequest = null
const loading = ref(false)
const disabled = ref(false)
const isStreaming = ref(false)
let startTime = null
let endTime = null

const dataName = ref('')
const dataInfo = ref({})
const dataShow = ref(false)
const satisfactionSurveyShow = ref(false)

const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
const fileIcon = (fileType) => {
  const IconClass = {
    docx: 'globalFileWord',
    doc: 'globalFileWord',
    wps: 'globalFileWPS',
    xlsx: 'globalFileExcel',
    xls: 'globalFileExcel',
    pdf: 'globalFilePDF',
    pptx: 'globalFilePPT',
    ppt: 'globalFilePPT',
    txt: 'globalFileTXT',
    jpg: 'globalFilePicture',
    png: 'globalFilePicture',
    gif: 'globalFilePicture',
    avi: 'globalFileVideo',
    mp4: 'globalFileVideo',
    zip: 'globalFileCompress',
    rar: 'globalFileCompress'
  }
  return IconClass[fileType] || 'globalFileUnknown'
}
const formatDuring = (mss) => {
  const days = parseInt(mss / (1000 * 60 * 60 * 24))
  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = (mss % (1000 * 60)) / 1000
  var time = ''
  if (days > 0) time += `${days} 天 `
  if (hours > 0) time += `${hours} 小时 `
  if (minutes > 0) time += `${minutes} 分钟 `
  if (seconds > 0) time += `${seconds} 秒 `
  return time
}
const handlePreview = (row) => {
  globalFileLocation({
    name: process.env.VUE_APP_NAME,
    fileId: row.id,
    fileType: row.extName,
    fileName: row.originalFileName,
    fileSize: row.fileSize
  })
}
const handleClick = () => {
  elShow.value = !elShow.value
}
const handleNewDialogue = () => {
  elRefs.value = []
  elPonderRefs.value = []
  chatMessageTotal.value = 0
  handleToolClose()
  handleStopMessage()
  chatId.value = guid()
  chatContent.value = ''
  chatMessage.value = []
}
const handleFileUpload = (data) => {
  fileList.value = data
}
const handleFileCallback = (data) => {
  fileData.value = data
}
const handleClose = (item) => {
  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))
}
const handleSelect = (data, type) => {
  chatMessageTotal.value = 0
  chatContent.value = data.userQuestion
  if (!type) {
    handleStopMessage()
    chatMessage.value = []
    handleChatMessage()
  } else {
    chatObj.value[AiChatCode.value] = { chatId: data.id, businessId: data.businessId || '' }
  }
}
const handleChatMessage = async (value) => {
  const { data, total } = await api.aigptChatLogsList({
    pageNo: value || 1,
    pageSize: value ? 1 : 10,
    isAsc: 1,
    query: { chatId: chatId.value }
  })
  chatMessageTotal.value = total
  if (value) {
    let updateVal = 0
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      if (item.reasoning) updateVal += 1
      if (item.answer) updateVal += 1
      chatMessage.value.unshift({
        id: guid(),
        type: false,
        ponderShow: true,
        isControls: true,
        content: '',
        contentOld: item.answer,
        ponderContent: '',
        ponderContentOld: item.reasoning,
        time: item.reasoning ? '1' : '',
        dataList: [],
        fileData: [],
        chartData: [],
        guideWord: []
      })
      chatMessage.value.unshift({
        id: guid(),
        type: true,
        ponderShow: true,
        isControls: true,
        content: item.userQuestion,
        contentOld: '',
        ponderContent: '',
        ponderContentOld: '',
        time: '',
        dataList: [],
        fileData: item.attachments,
        chartData: [],
        guideWord: []
      })
    }
    chatMessageUpdate.value = updateVal
    nextTick(() => {
      scrollElHeight()
    })
  } else {
    for (let index = 0; index < data.length; index++) {
      const item = data[index]
      chatMessage.value.push({
        id: guid(),
        type: true,
        ponderShow: true,
        isControls: true,
        content: item.userQuestion,
        contentOld: '',
        ponderContent: '',
        ponderContentOld: '',
        time: '',
        dataList: [],
        fileData: item.attachments,
        chartData: [],
        guideWord: []
      })
      chatMessage.value.push({
        id: guid(),
        type: false,
        ponderShow: true,
        isControls: true,
        content: '',
        contentOld: item.answer,
        ponderContent: '',
        ponderContentOld: item.reasoning,
        time: item.reasoning ? '1' : '',
        dataList: [],
        fileData: [],
        chartData: [],
        guideWord: []
      })
    }
    isScroll.value = false
    nextTick(() => {
      scrollDown()
    })
  }
}
const handleUpdate = () => {
  if (!isScroll.value && wrapScrollHeight.value) {
    nextTick(() => {
      scrollElHeight()
    })
    chatMessageUpdate.value = chatMessageUpdate.value - 1
  } else {
    chatMessageUpdate.value = 0
    nextTick(() => {
      scrollDown()
    })
  }
}
const handleScroll = ({ scrollTop }) => {
  if (scrollTop === 0) {
    wrapScrollHeight.value = scrollRef.value.wrapRef.scrollHeight
    if (chatMessageTotal.value && chatMessageTotal.value > chatMessage.value.length / 2) {
      handleChatMessage(chatMessage.value.length / 2 + 1)
    }
  }
  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef
  if (scrollHeight - scrollTop <= clientHeight + 52) {
    isScroll.value = false
  } else {
    isScroll.value = true
  }
}
const scrollDown = () => {
  if (isScroll.value) return
  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight
}
const scrollElHeight = () => {
  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - wrapScrollHeight.value
}
const handleGuideWord = (data) => {
  console.log('[]', data)
  if (isStreaming.value) return ElMessage({ type: 'warning', message: '请先完成上一次对话再进行新对话！' })
  handleCloseMessage()
  loading.value = true
  disabled.value = true
  chatMessage.value.push({
    id: guid(),
    type: true,
    ponderShow: true,
    isControls: true,
    content: data.question,
    contentOld: '',
    ponderContent: '',
    ponderContentOld: '',
    time: '',
    dataList: [],
    fileData: fileData.value,
    chartData: []
  })
  chatMessage.value.push({
    id: guid(),
    type: false,
    ponderShow: true,
    isControls: false,
    content: '',
    contentOld: '',
    ponderContent: '',
    ponderContentOld: '',
    time: '',
    dataList: [],
    fileData: [],
    chartData: [],
    guideWord: []
  })
  nextTick(() => {
    scrollDown()
    handleHttpStream(data)
    editorRef.value?.handleSetFile([])
    handleToolClose()
  })
}
const handleSendMessage = (value) => {
  if (isStreaming.value) return ElMessage({ type: 'warning', message: '请先完成上一次对话再进行新对话！' })
  if (toolRequired.value && !fileData.value.length)
    return ElMessage({ type: 'warning', message: `请先上传相关资料在进行${toolName.value}!` })
  handleCloseMessage()
  loading.value = true
  disabled.value = true
  chatMessage.value.push({
    id: guid(),
    type: true,
    ponderShow: true,
    isControls: true,
    content: value,
    contentOld: '',
    ponderContent: '',
    ponderContentOld: '',
    time: '',
    dataList: [],
    fileData: fileData.value,
    chartData: []
  })
  chatMessage.value.push({
    id: guid(),
    type: false,
    ponderShow: true,
    isControls: false,
    content: '',
    contentOld: '',
    ponderContent: '',
    ponderContentOld: '',
    time: '',
    dataList: [],
    fileData: [],
    chartData: [],
    guideWord: []
  })
  const fileId = fileData.value.map((v) => v.id).join(',')
  const defaultParams = toolId.value
    ? { question: value, tool: toolId.value, attachmentIds: fileId }
    : { question: value, attachmentIds: fileId }
  const params = toolIsParam.value ? { ...defaultParams, param: { pageContent: AiChatContent.value } } : defaultParams
  nextTick(() => {
    scrollDown()
    handleHttpStream(params)
    editorRef.value?.handleSetFile([])
    handleToolClose()
  })
}
const handleTips = (text) => {
  const parts = text.split(/(\{[^}]+\})/)
  const result = parts
    .map((part) => {
      if (part.startsWith('{') && part.endsWith('}')) {
        return { value: part.slice(1, -1), type: true }
      } else if (part.trim() !== '') {
        return { value: part, type: false }
      }
    })
    .filter((item) => item !== undefined)
  return result
}
const handleToolClose = () => {
  toolId.value = ''
  toolName.value = ''
  toolIsParam.value = 0
  toolRequired.value = 0
  editorRef.value?.handleSetFile([])
  editorRef.value?.handleSetContent('')
}
const handleLinkClick = ({ href, text, event }) => {
  console.log('链接被点击:', href, text)
  if (text === '查看原文比对') {
    // store.commit('setOpenRoute', { name: '查看原文比对', path: '/VersionComparisonAi', query: { chatId: chatId.value } })
    const token = sessionStorage.getItem('token') || ''
    window.open(`${config.mainPath}VersionComparisonAi?chatId=${chatId.value}&token=${token}`, '_blank')
  } else {
    window.open(href, '_blank')
  }
}
const handleToolSendMessage = (value) => {
  const { id, chatToolName, isParam, userPromptTip, needTool } = value
  if (userPromptTip) {
    toolId.value = id
    toolName.value = chatToolName
    toolIsParam.value = isParam
    toolRequired.value = needTool
    editorRef.value?.handleInsertPlaceholder(handleTips(userPromptTip))
    return
  }
  if (needTool && !fileData.value.length)
    return ElMessage({ type: 'warning', message: `请先上传相关资料再进行${chatToolName}！` })
  const finallySendContent = isParam ? chatToolName : AiChatContent.value || sendContent.value
  if (!finallySendContent) return ElMessage({ type: 'warning', message: `请先输入内容再进行${chatToolName}！` })
  handleStopMessage()
  loading.value = true
  disabled.value = true
  // chatId.value = guid()
  // chatContent.value = ''
  // chatMessage.value = []
  chatMessage.value.push({
    id: guid(),
    type: true,
    ponderShow: true,
    isControls: true,
    content: chatToolName,
    contentOld: '',
    ponderContent: '',
    ponderContentOld: '',
    time: '',
    dataList: [],
    fileData: fileData.value,
    chartData: [],
    guideWord: []
  })
  chatMessage.value.push({
    id: guid(),
    type: false,
    ponderShow: true,
    isControls: false,
    content: '',
    contentOld: '',
    ponderContent: '',
    ponderContentOld: '',
    time: '',
    dataList: [],
    fileData: [],
    chartData: [],
    guideWord: []
  })
  const fileId = fileData.value.map((v) => v.id).join(',')
  const defaultParams = { question: finallySendContent, tool: id, attachmentIds: fileId }
  const params = isParam ? { ...defaultParams, param: { pageContent: AiChatContent.value } } : defaultParams
  nextTick(() => {
    scrollDown()
    handleHttpStream(params, fileData.value)
    editorRef.value?.handleSetFile([])
    editorRef.value?.handleSetContent('')
  })
}
const stringToJson = (str) => {
  // 替换属性名
  str = str.replace(/(\w+):/g, '"$1":')
  // 替换单引号为双引号
  str = str.replace(/'/g, '"')
  const obj = JSON.parse(str)
  return obj
}
const handleHttpStream = async (params = {}) => {
  isStreaming.value = true
  try {
    let AiChatParam = {}
    if (params.param && AiChatParams.value.param) {
      AiChatParam = { param: { ...params.param, ...AiChatParams.value.param } }
    }
    AiChatParam = { ...params, ...AiChatParams.value, ...AiChatParam }
    startTime = new Date()
    currentRequest = http_stream('/aigpt/chatStream', {
      body: JSON.stringify({
        chatBusinessScene: AiChatCode.value,
        chatId: chatId.value,
        ...AiChatParam
      }),
      onMessage (event) {
        // if (event.data === '{\"status\":\"running\",\"name\":\"AI 对话\"}') loading.value = false
        loading.value = false
        if (event.data !== '[DONE]') {
          const data = JSON.parse(event.data)
          console.log('[]', data)
          if (Array.isArray(data)) {
            // console.log('[]', data)
            let quoteList = []
            let newChartData = []
            let newGuideWord = []
            for (let index = 0; index < data.length; index++) {
              const item = data[index]
              if (typeof item === 'string') {
                newGuideWord.push({ ...AiChatParam, question: item })
              } else {
                if (item?.quoteList?.length) {
                  for (let i = 0; i < item?.quoteList.length; i++) {
                    const row = item?.quoteList[i]
                    quoteList.push({ ...row, markdownContent: row.q })
                  }
                }
                if (item?.echartItem) {
                  newChartData.push(stringToJson(item?.echartItem))
                }
              }
            }
            chatMessage.value[chatMessage.value.length - 1].dataList = quoteList
            chatMessage.value[chatMessage.value.length - 1].chartData = newChartData
            chatMessage.value[chatMessage.value.length - 1].guideWord = newGuideWord
          } else {
            // console.log('{}', data)
            const choice = data?.choices || [{}]
            const details = choice[0]?.delta || {}
            if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {
              elPonderRef.value?.enqueueRender(details.reasoning_content || '')
              if (chatMessage.value[chatMessage.value.length - 1].time) {
                startTime = null
                endTime = null
              } else {
                endTime = new Date()
                const executionTime = endTime - startTime
                chatMessage.value[chatMessage.value.length - 1].time = formatDuring(executionTime)
              }
            }
            if (Object.prototype.hasOwnProperty.call(details, 'content'))
              elRef.value?.enqueueRender(details.content || '')
            nextTick(() => {
              scrollDown()
            })
          }
        } else {
          // console.log(event.data)
          elRef.value?.enqueueRender('')
          nextTick(() => {
            scrollDown()
          })
          disabled.value = false
          isStreaming.value = false
          chatMessage.value[chatMessage.value.length - 1].isControls = true
          if (!chatContent.value) chatHistoryRef.value?.refresh()
        }
      },
      onError (err) {
        console.log('流式接口错误:', err)
      },
      onClose () {
        loading.value = false
        disabled.value = false
        isStreaming.value = false
        console.log('流式接口关闭')
      }
    })
    await currentRequest.promise
  } catch (error) {
    loading.value = false
    disabled.value = false
    isStreaming.value = false
    elRef.value?.enqueueRender('服务器繁忙，请稍后再试。')
    nextTick(() => {
      scrollDown()
    })
    console.error('启动流式接口失败:', error)
  } finally {
    // currentRequest = null
  }
}
const handleDataList = (row) => {
  dataName.value = row.sourceName
  dataInfo.value = row
  dataShow.value = true
}
const handleCopyMessage = (content, i) => {
  const index = (i + 1) / 2 - 1
  const copyContent = elRefs.value[index]?.elRef?.innerText || content
  const textarea = document.createElement('textarea')
  textarea.readOnly = 'readonly'
  textarea.style.position = 'absolute'
  textarea.style.left = '-9999px'
  textarea.value = copyContent
  document.body.appendChild(textarea)
  textarea.select()
  const result = document.execCommand('Copy')
  if (result) {
    ElMessage({ message: '复制成功', type: 'success' })
  }
  document.body.removeChild(textarea)
}
const handleRetryMessage = (item, index) => {
  const length = chatMessage.value.length - 1
  if (index === length && currentRequest) {
    loading.value = true
    disabled.value = true
    isStreaming.value = true
    chatMessage.value[length] = {
      id: guid(),
      type: false,
      ponderShow: true,
      isControls: false,
      content: '',
      contentOld: '',
      ponderContent: '',
      ponderContentOld: '',
      time: '',
      dataList: [],
      fileData: [],
      chartData: [],
      guideWord: []
    }
    nextTick(async () => {
      await currentRequest.retry()
    })
  } else {
    isScroll.value = false
    fileData.value = chatMessage.value[index - 1].fileData
    handleSendMessage(chatMessage.value[index - 1].content)
  }
}
const handleSatisfactionSurvey = (item, index) => {
  // 获取当前回答对应的用户问题
  const questionIndex = index - 1
  const question = chatMessage.value[questionIndex]?.content || ''
  const answerIndex = ((index + 1) / 2) - 1
  const answer = elRefs.value[answerIndex]?.elRef?.innerText || item.content || item.contentOld || ''
  // 构建传递给满意度调查的数据
  dataInfo.value = {
    ...item,
    question: question,
    answer: answer,
    chatId: chatId.value,
    chatContent: chatContent.value
  }
  console.log('dataInfo.value==>', dataInfo.value)
  satisfactionSurveyShow.value = true
}
const handleCloseMessage = () => {
  currentRequest = null
  loading.value = false
  disabled.value = false
  isStreaming.value = false
}
const handleStopMessage = () => {
  if (currentRequest) {
    currentRequest.abort()
    loading.value = false
    disabled.value = false
    isStreaming.value = false
    console.log('启动流式接口停止')
    if (!chatContent.value) chatHistoryRef.value?.refresh()
  }
  handleCloseMessage()
}
const aigptChatSceneDetail = async () => {
  const { data } = await api.aigptChatSceneDetail({ key: guid(), query: { chatSceneCode: AiChatCode.value } })
  aiTools.value = data?.tools || []
  aiWords.value = data?.promptWords || []
  chatObj.value[AiChatCode.value] = { chatId: chatId.value, businessId: AiChatModuleId.value }
}
const handleHistoryMessage = async (code, businessId) => {
  elIndex.value = 1
  chatHistoryRef.value?.handleCurrentChat(code, businessId)
}
const isEmptyObject = (obj) => {
  if (typeof obj !== 'object' || obj === null) return false
  return Object.keys(obj).length !== 0
}
/**
 * AiChatCode 场景code String
 * --主工程调用 store.commit('setAiChatCode', '')
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatCode: '' })
 *
 * AiChatConfig 批量设置参数 Object
 * AiChatConfig可以设置 AiChatWindow AiChatParams AiChatContent AiChatSetContent AiChatAddContent AiChatSendMessage AiChatToolSendMessage
 * --主工程调用 store.commit('setAiChatConfig', { AiChatWindow, AiChatParams, AiChatContent, AiChatSetContent, AiChatAddContent, AiChatSendMessage, AiChatToolSendMessage })
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatConfig: { AiChatWindow, AiChatParams, AiChatContent, AiChatSetContent, AiChatAddContent, AiChatSendMessage, AiChatToolSendMessage } })
 *
 * AiChatWindow 窗口是否显示 Boolean
 * --主工程调用 store.commit('setAiChatWindow', true / false)
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatWindow: true / false })
 *
 * AiChatParams 其他传参 Object
 * --主工程调用 store.commit('setAiChatParams', {})
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatParams: {} })
 *
 * AiChatContent 内容参数 String
 * --主工程调用 store.commit('setAiChatContent', '')
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatContent: '' })
 *
 * AiChatSetContent 替换内容参数 String
 * --主工程调用 store.commit('setAiChatSetContent', '')
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatSetContent: '' })
 *
 * AiChatAddContent 追加内容参数 String
 * --主工程调用 store.commit('setAiChatAddContent', '')
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatAddContent: '' })
 *
 * AiChatSendMessage 发送消息 String
 * --主工程调用 store.commit('setAiChatSendMessage', '')
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatSendMessage: '' })
 *
 * AiChatToolSendMessage 点击工具 Object
 * --主工程调用 store.commit('setAiChatToolSendMessage', {})
 * --子工程调用 qiankunMicro.setGlobalState({ AiChatToolSendMessage: {} })
 */
watch(
  () => store.state.AiChatCode,
  (val) => {
    if (val) {
      AiChatCode.value = val
      handleToolClose()
      handleNewDialogue()
      aigptChatSceneDetail()
    }
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatModuleId,
  (val) => {
    AiChatModuleId.value = val
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatConfig,
  (val) => {
    if (isEmptyObject(val)) {
      if (val.hasOwnProperty('AiChatWindow')) elShow.value = !!val.AiChatWindow
      if (val.hasOwnProperty('AiChatFile')) editorRef.value?.handleSetFile(val.AiChatFile)
      if (val.hasOwnProperty('AiChatParams')) AiChatParams.value = val.AiChatParams
      if (val.hasOwnProperty('AiChatContent')) AiChatContent.value = val.AiChatContent
      if (val.hasOwnProperty('AiChatSetContent')) editorRef.value?.handleSetContent(val.AiChatSetContent)
      if (val.hasOwnProperty('AiChatAddContent')) editorRef.value?.handleAddContent(val.AiChatAddContent)
      if (val.hasOwnProperty('AiChatSendMessage')) if (val.AiChatSendMessage) handleSendMessage(val.AiChatSendMessage)
      if (val.hasOwnProperty('AiChatToolSendMessage'))
        if (isEmptyObject(val.AiChatToolSendMessage)) handleToolSendMessage(val.AiChatToolSendMessage)
    }
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatWindow,
  (val) => {
    elShow.value = !!val
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatFile,
  (val) => {
    editorRef.value?.handleSetFile(val)
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatParams,
  (val) => {
    AiChatParams.value = val
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatContent,
  (val) => {
    AiChatContent.value = val
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatSetContent,
  (val) => {
    editorRef.value?.handleSetContent(val)
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatAddContent,
  (val) => {
    editorRef.value?.handleAddContent(val)
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatSendMessage,
  (val) => {
    if (val) handleSendMessage(val)
  },
  { immediate: true }
)
watch(
  () => store.state.AiChatToolSendMessage,
  (val) => {
    if (isEmptyObject(val)) handleToolSendMessage(val)
  },
  { immediate: true }
)
watch(
  () => elShow.value,
  (val) => {
    if (val && elIndex.value) {
      elIndex.value = 0
      nextTick(() => {
        scrollDown()
      })
    }
  },
  { immediate: true }
)
const handleAiChatConfig = (data) => {
  const {
    AiChatId: newAiChatId = '',
    AiChatCode: newAiChatCode = '',
    AiChatModule: newAiChatModule = '',
    AiChatModuleId: newAiChatModuleId = '',
    AiChatWindow: newAiChatWindow = false,
    AiChatFile: newAiChatFile = [],
    AiChatClearFile: newAiChatClearFile = false,
    AiChatParams: newAiChatParams = {},
    AiChatContent: newAiChatContent = ''
  } = data
  AiChatId.value = newAiChatId
  AiChatCode.value = newAiChatCode
  AiChatModule.value = newAiChatModule
  AiChatModuleId.value = newAiChatModuleId
  elShow.value = typeof newAiChatWindow === 'boolean' ? newAiChatWindow : false
  if ((typeof newAiChatClearFile === 'boolean' ? newAiChatClearFile : false) || newAiChatFile.length) {
    editorRef.value?.handleSetFile(newAiChatFile)
  }
  AiChatParams.value = newAiChatParams
  AiChatContent.value = newAiChatContent
}
const handleAiChatEditor = (type = true, content = '') => {
  const newType = typeof type === 'boolean' ? type : true
  if (newType) editorRef.value?.handleSetContent(content)
  if (!newType) editorRef.value?.handleAddContent(content)
}
const handleAiChatHistory = (newAiChatCode = '', newAiChatModuleId = '') => {
  handleToolClose()
  handleNewDialogue()
  aigptChatSceneDetail()
  if (newAiChatCode) handleHistoryMessage(newAiChatCode, newAiChatModuleId)
}
const handleAiChatSend = (content) => {
  if (content) {
    elShow.value = true
    handleSendMessage(content)
  }
}
const handleAiChatToolSend = (data) => {
  if (isEmptyObject(data)) {
    elShow.value = true
    handleToolSendMessage(data)
  }
}
AiChatClass.prototype.AiChatConfig = handleAiChatConfig
AiChatClass.prototype.AiChatEditor = handleAiChatEditor
AiChatClass.prototype.AiChatHistory = handleAiChatHistory
AiChatClass.prototype.AiChatSend = handleAiChatSend
AiChatClass.prototype.AiChatToolSend = handleAiChatToolSend
onMounted(() => {
  handleNewDialogue()
  aigptChatSceneDetail()
})
onUnmounted(() => { })
</script>

<style lang="scss">
.GlobalAiChat {
  width: 100%;
  height: 100%;
  padding: 12px 0;
  position: relative;
  display: flex;
  flex-direction: column;

  .GlobalAiChatClose {
    width: 38px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border-radius: 2px;
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;

    &:hover {
      color: #fff;
      background-color: rgba($color: red, $alpha: 0.6);
    }
  }

  .GlobalAiChatLogo {
    width: 62px;
    height: 62px;
    position: absolute;
    top: 16px;
    left: 12px;

    .zy-el-image {
      width: 62px;
      height: 62px;
    }
  }

  .GlobalAiChatHead {
    width: 100%;
    height: 68px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 0 12px 0 82px;

    .GlobalAiChatHeadName {
      width: 100%;
      font-weight: bold;
      line-height: var(--zy-line-height);
      font-size: var(--zy-name-font-size);
    }

    .GlobalAiChatHeadText {
      width: 100%;
      display: flex;
      align-items: center;
      min-height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 8px);
      max-height: calc((var(--zy-text-font-size) * var(--zy-line-height)) * 2);
      line-height: var(--zy-line-height);
      font-size: var(--zy-text-font-size);
      color: var(--zy-el-text-color-secondary);
    }
  }

  .GlobalAiChatDialogueBody {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--zy-el-border-color-lighter);
    padding: 0 6px;

    .GlobalAiChatDialogue {
      padding: 0 6px;
      font-weight: bold;
      font-size: var(--zy-name-font-size);
    }

    .GlobalAiChatDialogueNew,
    .GlobalAiChatDialogueHistory {
      width: 38px;
      height: 38px;
      min-width: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 2px;
      cursor: pointer;

      span {
        display: flex;
        align-items: center;
        justify-content: center;

        path {
          fill: var(--zy-el-text-color-regular);
        }
      }
    }

    .GlobalAiChatDialogueNew {
      &:hover {
        background: var(--zy-el-color-primary-light-9);

        span {
          path {
            fill: var(--zy-el-color-primary);
          }
        }
      }
    }

    .GlobalAiChatDialogueHistory {
      &:hover {
        background: var(--zy-el-color-info-light-9);
      }
    }
  }

  .GlobalAiChatScroll {
    width: 100%;
    flex: 1;

    .GlobalAiChatBody {
      width: 100%;
      padding: 0 12px;

      .GlobalAiChatBodyTipsBody {
        width: 100%;
        padding-top: 12px;
      }

      .GlobalAiChatBodyTipsVice {
        width: 100%;
        padding: 6px 0;
        line-height: var(--zy-line-height);
        font-size: var(--zy-text-font-size);
        color: var(--zy-el-text-color-secondary);
      }

      .GlobalAiChatBodyTipsItem {
        width: 100%;
        padding: 6px 0;

        .GlobalAiChatBodyTips {
          display: inline-block;
          padding: 6px 12px;
          border-radius: 6px;
          line-height: var(--zy-line-height);
          color: var(--zy-el-text-color-regular);
          font-size: var(--zy-text-font-size);
          background: var(--zy-el-color-info-light-9);
          cursor: pointer;

          span {
            width: 14px;
            display: inline-block;
            line-height: 1.2;
            margin-right: 6px;
            vertical-align: middle;

            svg {
              vertical-align: top;

              path {
                fill: var(--zy-el-color-primary);
              }
            }
          }
        }
      }

      .GlobalAiChatMessage,
      .GlobalAiChatSelfMessage {
        padding: 12px 0;
      }

      .GlobalAiChatMessage {
        display: flex;
        justify-content: space-between;
        padding-right: 12px;

        .zy-el-image {
          width: 32px;
          height: 32px;
        }

        .GlobalAiChatMessageInfo {
          width: 100%;
          // width: calc(100% - 46px);
          display: inline-block;
          background: #fff;
          position: relative;

          .GlobalAiChatMessageLoading {
            width: 100%;
            height: 32px;
            position: absolute;
            top: 0;
            left: 0;

            @keyframes circleRoate {
              from {
                transform: translateY(-50%) rotate(0deg);
              }

              to {
                transform: translateY(-50%) rotate(360deg);
              }
            }

            .answerLoading {
              width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);
              height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);
              position: absolute;
              top: 50%;
              left: 0;
              z-index: 3;
              display: flex;
              align-items: center;
              justify-content: center;
              animation: circleRoate 1s infinite linear;

              path {
                fill: var(--zy-el-color-primary);
              }
            }

            .answerLoading+.QuestionsAndAnswersChatText {
              color: var(--zy-el-color-primary);
              padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);
            }
          }

          .GlobalAiChatMessagePonder {
            height: 32px;
            display: inline-flex;
            align-items: center;
            padding: 0 12px;
            border-radius: 6px;
            line-height: var(--zy-line-height);
            font-size: var(--zy-text-font-size);
            color: var(--zy-el-text-color-primary);
            background: var(--zy-el-color-info-light-9);
            margin-bottom: 12px;
            cursor: pointer;

            div {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 6px;
            }

            span {
              display: flex;
              align-items: center;
              justify-content: center;
              color: var(--zy-el-text-color-primary);
            }
          }

          .GlobalAiChatMessagePonderContent {
            width: 100%;
            padding-left: 12px;
            border-left: 2px solid var(--zy-el-border-color);

            * {
              color: var(--zy-el-text-color-secondary);
            }
          }

          .GlobalAiChatMessageContent {
            width: 100%;
            padding-top: calc((32px - (var(--zy-line-height) * var(--zy-text-font-size))) / 2);

            .GlobalAiChatMessageDataList {
              padding-top: 12px;

              .GlobalAiChatMessageDataItem {
                color: var(--zy-el-color-primary);
                line-height: var(--zy-line-height);
                font-size: var(--zy-text-font-size);
                cursor: pointer;
              }
            }
          }

          .GlobalAiChatMessageControls {
            width: 100%;
            display: flex;
            align-items: center;
            padding-top: 12px;

            .GlobalAiChatMessageControlsItem {
              width: 32px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 6px;
              cursor: pointer;
              margin-right: 2px;

              &:hover {
                background: var(--zy-el-color-info-light-8);
              }

              .zy-el-icon {
                font-size: 20px;
              }
            }
          }
        }
      }

      .GlobalAiChatSelfMessage {
        width: 100%;
        display: flex;
        align-items: center;
        align-items: flex-end;
        flex-direction: column;
        padding-left: 46px;
        padding-right: 12px;

        .GlobalAiChatSelfMessageFile {
          width: 280px;
          height: 52px;
          display: inline-flex;
          flex-direction: column;
          justify-content: center;
          background: #fff;
          position: relative;
          padding: 0 40px 0 12px;
          border-radius: var(--el-border-radius-base);
          border: 1px solid var(--zy-el-border-color-light);
          background: var(--zy-el-color-info-light-9);
          word-wrap: break-word;
          white-space: pre-wrap;
          cursor: pointer;
          margin-bottom: 12px;

          .GlobalChatMessagesFileName {
            font-size: var(--zy-text-font-size);
            line-height: var(--zy-line-height);
            padding-bottom: 2px;
          }

          .GlobalChatMessagesFileSize {
            color: var(--zy-el-text-color-secondary);
            font-size: calc(var(--zy-text-font-size) - 2px);
          }

          .globalFileIcon {
            width: 28px;
            height: 28px;
            vertical-align: middle;
            position: absolute;
            top: 50%;
            right: 6px;
            transform: translateY(-50%);
          }

          .globalFileUnknown {
            background: url('./img/unknown.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFilePDF {
            background: url('./img/PDF.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFileWord {
            background: url('./img/Word.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFileExcel {
            background: url('./img/Excel.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFilePicture {
            background: url('./img/picture.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFileVideo {
            background: url('./img/video.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFileTXT {
            background: url('./img/TXT.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFileCompress {
            background: url('./img/compress.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFileWPS {
            background: url('./img/WPS.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }

          .globalFilePPT {
            background: url('./img/PPT.png') no-repeat;
            background-size: 100% 100%;
            background-position: center;
          }
        }

        .GlobalAiChatSelfMessageInfo {
          display: inline-block;
          padding: 12px;
          border-radius: 6px;
          line-height: var(--zy-line-height);
          font-size: var(--zy-text-font-size);
          background: var(--zy-el-bg-color-page);
          white-space: pre-wrap;
          word-wrap: break-word;
        }
      }
    }
  }

  .GlobalAiChatDialogueEditor {
    width: 100%;
    padding: 0 12px;

    .GlobalAiChatDialogueTools {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding: 6px;

      .GlobalAiChatDialogueTool {
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 14px;
        padding: 0 16px;
        font-size: var(--zy-text-font-size);
        border: 1px solid var(--zy-el-border-color);
        margin: 3px 6px;
        cursor: pointer;

        &:hover {
          color: var(--zy-el-color-primary);
          border: 1px solid var(--zy-el-color-primary);
          background: var(--zy-el-color-primary-light-9);

          span {
            path {
              fill: var(--zy-el-color-primary);
            }
          }
        }

        span {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 3px;
        }
      }
    }

    .GlobalAiChatDialogueEditorBody {
      width: 100%;
      background: #fff;
      border-radius: 8px;
      box-shadow: var(--zy-el-box-shadow);
      border: 1px solid var(--zy-el-border-color-lighter);

      .GlobalAiChatToolsActive {
        width: 100%;
        height: var(--zy-height);
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px;
        background: var(--zy-el-color-info-light-9);

        div {
          font-weight: bold;
        }

        .GlobalAiChatToolsActiveIcon {
          width: 26px;
          height: 26px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 2px;
          cursor: pointer;

          &:hover {
            background: var(--zy-el-color-info-light-8);
          }

          .zy-el-icon {
            font-size: 18px;
          }
        }
      }
    }
  }
}
</style>
