{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"SubmitAiReportGenera\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: \"报告类型\",\n        prop: \"reportType\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_select, {\n            modelValue: $setup.form.reportType,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.reportType = $event;\n            }),\n            onChange: _ctx.queryChange,\n            placeholder: \"请选择报告类型\",\n            clearable: \"\"\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.reportType, function (item) {\n                return _openBlock(), _createBlock(_component_el_option, {\n                  key: item.id,\n                  label: item.label,\n                  value: item.id\n                }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"报告内容\",\n        prop: \"content\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_TinyMceEditor, {\n            modelValue: $setup.form.content,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.content = $event;\n            })\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "prop", "_component_el_select", "modelValue", "reportType", "_cache", "$event", "onChange", "_ctx", "query<PERSON>hange", "placeholder", "clearable", "_Fragment", "_renderList", "item", "_createBlock", "_component_el_option", "key", "id", "value", "_", "_component_TinyMceEditor", "content", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiReportGenera\\component\\SubmitAiReportGenera.vue"], "sourcesContent": ["<template>\r\n  <div class=\"SubmitAiReportGenera\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"报告类型\" prop=\"reportType\" class=\"globalFormTitle\">\r\n        <el-select v-model=\"form.reportType\" @change=\"queryChange\" placeholder=\"请选择报告类型\" clearable>\r\n          <el-option v-for=\"item in reportType\" :key=\"item.id\" :label=\"item.label\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"报告内容\" prop=\"content\" class=\"globalFormTitle\">\r\n        <TinyMceEditor v-model=\"form.content\" />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SubmitAiReportGenera' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  reportType: '',\r\n  content: ''\r\n})\r\nconst rules = reactive({\r\n  reportType: [{ required: true, message: '请选择报告类型', trigger: ['blur', 'change'] }],\r\n  content: [{ required: true, message: '请选择报告内容', trigger: ['blur', 'change'] }]\r\n})\r\nconst reportType = ref([])\r\n\r\nonMounted(() => {\r\n  aigptReportRecordTypeList()\r\n  if (props.id) { aigptReportRecordInfo() }\r\n})\r\n\r\nconst aigptReportRecordTypeList = async () => {\r\n  const { data } = await api.aigptReportRecordTypeList({ sceneCode: 'ai-general-report-main' })\r\n  reportType.value = data\r\n}\r\nconst aigptReportRecordInfo = async () => {\r\n  const { data } = await api.aigptReportRecordInfo({ detailId: props.id })\r\n  form.reportType = data.reportType\r\n  form.content = data.content\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/aigptReportRecord/edit' : '/aigptReportRecord/add', {\r\n    form: { id: props.id, reportType: form.reportType, content: form.content }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.SubmitAiReportGenera {\r\n  width: 990px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EAUxBA,KAAK,EAAC;AAAkB;;;;;;;;uBAVjCC,mBAAA,CAeM,OAfNC,UAeM,GAdJC,YAAA,CAaUC,kBAAA;IAbDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACV,KAAK,EAAC;;IAF1FW,OAAA,EAAAC,QAAA,CAGM;MAAA,OAIe,CAJfT,YAAA,CAIeU,uBAAA;QAJDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,YAAY;QAACf,KAAK,EAAC;;QAHzDW,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAEY,CAFZT,YAAA,CAEYa,oBAAA;YANpBC,UAAA,EAI4BV,MAAA,CAAAC,IAAI,CAACU,UAAU;YAJ3C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAI4Bb,MAAA,CAAAC,IAAI,CAACU,UAAU,GAAAE,MAAA;YAAA;YAAGC,QAAM,EAAEC,IAAA,CAAAC,WAAW;YAAEC,WAAW,EAAC,SAAS;YAACC,SAAS,EAAT;;YAJzFd,OAAA,EAAAC,QAAA,CAKqB;cAAA,OAA0B,E,kBAArCX,mBAAA,CAA4FyB,SAAA,QALtGC,WAAA,CAKoCpB,MAAA,CAAAW,UAAU,EAL9C,UAK4BU,IAAI;qCAAtBC,YAAA,CAA4FC,oBAAA;kBAArDC,GAAG,EAAEH,IAAI,CAACI,EAAE;kBAAGlB,KAAK,EAAEc,IAAI,CAACd,KAAK;kBAAGmB,KAAK,EAAEL,IAAI,CAACI;;;;YALhGE,CAAA;;;QAAAA,CAAA;UAQM/B,YAAA,CAEeU,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,SAAS;QAACf,KAAK,EAAC;;QARtDW,OAAA,EAAAC,QAAA,CASQ;UAAA,OAAwC,CAAxCT,YAAA,CAAwCgC,wBAAA;YAThDlB,UAAA,EASgCV,MAAA,CAAAC,IAAI,CAAC4B,OAAO;YAT5C,uBAAAjB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OASgCb,MAAA,CAAAC,IAAI,CAAC4B,OAAO,GAAAhB,MAAA;YAAA;;;QAT5Cc,CAAA;UAWMG,mBAAA,CAGM,OAHNC,UAGM,GAFJnC,YAAA,CAAqEoC,oBAAA;QAA1DC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAtB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEb,MAAA,CAAAmC,UAAU,CAACnC,MAAA,CAAAoC,OAAO;QAAA;;QAZ5DhC,OAAA,EAAAC,QAAA,CAY+D;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAZjEyB,gBAAA,CAY+D,IAAE,E;;QAZjEV,CAAA;UAaQ/B,YAAA,CAA4CoC,oBAAA;QAAhCE,OAAK,EAAElC,MAAA,CAAAsC;MAAS;QAbpClC,OAAA,EAAAC,QAAA,CAasC;UAAA,OAAEO,MAAA,QAAAA,MAAA,OAbxCyB,gBAAA,CAasC,IAAE,E;;QAbxCV,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}