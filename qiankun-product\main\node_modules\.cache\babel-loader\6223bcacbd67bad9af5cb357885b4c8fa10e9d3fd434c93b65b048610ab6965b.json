{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, Transition as _Transition } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalRegionSelect\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"GlobalRegionSelectBody\"\n};\nvar _hoisted_3 = {\n  key: 0,\n  class: \"GlobalRegionSelectBodyInfo\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalRegionSelectBodyInfoOld\"\n};\nvar _hoisted_5 = {\n  key: 0,\n  class: \"GlobalRegionSelectColumn\"\n};\nvar _hoisted_6 = [\"onClick\"];\nvar _hoisted_7 = {\n  class: \"GlobalRegionSelectName\"\n};\nvar _hoisted_8 = {\n  key: 2,\n  class: \"GlobalRegionSelectBodyInfo\"\n};\nvar _hoisted_9 = {\n  class: \"GlobalRegionSelectBodyInfoOld\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_tree = _resolveComponent(\"el-tree\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_Transition, {\n    \"enter-active-class\": \"animate__animated animate__zoomIn animate__faster\",\n    \"leave-active-class\": \"animate__animated animate__zoomOut animate__faster\"\n  }, {\n    default: _withCtx(function () {\n      return [$setup.show ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n        class: \"GlobalRegionSelectBodyTitle\"\n      }, \"选择登录地区\", -1 /* HOISTED */)), $setup.regionType === 'list' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, \"上次登录地区：\" + _toDisplayString($setup.oldRegionInfo.name), 1 /* TEXT */), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n        class: \"GlobalRegionSelectBodyInfoTip\"\n      }, \"请在下方选择本次登录地区！\", -1 /* HOISTED */))])) : _createCommentVNode(\"v-if\", true), $setup.regionType === 'list' ? (_openBlock(), _createBlock(_component_el_scrollbar, {\n        key: 1,\n        class: \"GlobalRegionSelectScrollbar\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.regionList, function (item) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"GlobalRegionSelectList\",\n              key: item.id\n            }, [item.id !== 'province' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _toDisplayString(item.name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.data, function (row) {\n              return _openBlock(), _createElementBlock(\"div\", {\n                class: _normalizeClass([\"GlobalRegionSelectItem\", {\n                  'is-active': row.id === $setup.regionId\n                }]),\n                key: row.id,\n                onClick: function onClick($event) {\n                  return $setup.handleRegionClick(row);\n                }\n              }, [_createElementVNode(\"div\", {\n                class: \"GlobalRegionSelectIcon\",\n                innerHTML: $setup.regionIcon\n              }), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(row.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_6);\n            }), 128 /* KEYED_FRAGMENT */))]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.regionType === 'tree' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, \"上次登录地区：\" + _toDisplayString($setup.oldRegionInfo.name), 1 /* TEXT */), _createVNode(_component_el_input, {\n        modelValue: $setup.filterText,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.filterText = $event;\n        }),\n        placeholder: \"请输入地区名称\",\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), $setup.regionType === 'tree' ? (_openBlock(), _createBlock(_component_el_scrollbar, {\n        key: 3,\n        class: \"GlobalRegionSelectTree\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree, {\n            ref: \"treeRef\",\n            \"node-key\": \"id\",\n            data: $setup.region,\n            \"highlight-current\": \"\",\n            props: {\n              label: 'name',\n              children: 'children'\n            },\n            \"filter-node-method\": $setup.filterNode,\n            \"default-expanded-keys\": $setup.regionKeys,\n            onNodeClick: $setup.handleRegionClick\n          }, null, 8 /* PROPS */, [\"data\", \"default-expanded-keys\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_Transition", "default", "_withCtx", "$setup", "show", "_hoisted_2", "_createElementVNode", "regionType", "_hoisted_3", "_hoisted_4", "_toDisplayString", "oldRegionInfo", "name", "_createCommentVNode", "_createBlock", "_component_el_scrollbar", "_Fragment", "_renderList", "regionList", "item", "id", "_hoisted_5", "data", "row", "_normalizeClass", "regionId", "onClick", "$event", "handleRegionClick", "innerHTML", "regionIcon", "_hoisted_7", "_hoisted_6", "_", "_hoisted_8", "_hoisted_9", "_component_el_input", "modelValue", "filterText", "_cache", "placeholder", "clearable", "_component_el_tree", "ref", "region", "props", "label", "children", "filterNode", "regionKeys", "onNodeClick"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\components\\GlobalRegionSelect.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalRegionSelect\">\r\n    <transition enter-active-class=\"animate__animated animate__zoomIn animate__faster\"\r\n      leave-active-class=\"animate__animated animate__zoomOut animate__faster\">\r\n      <div class=\"GlobalRegionSelectBody\" v-if=\"show\">\r\n        <div class=\"GlobalRegionSelectBodyTitle\">选择登录地区</div>\r\n        <div class=\"GlobalRegionSelectBodyInfo\" v-if=\"regionType === 'list'\">\r\n          <div class=\"GlobalRegionSelectBodyInfoOld\">上次登录地区：{{ oldRegionInfo.name }}</div>\r\n          <div class=\"GlobalRegionSelectBodyInfoTip\">请在下方选择本次登录地区！</div>\r\n        </div>\r\n        <el-scrollbar class=\"GlobalRegionSelectScrollbar\" v-if=\"regionType === 'list'\">\r\n          <div class=\"GlobalRegionSelectList\" v-for=\"item in regionList\" :key=\"item.id\">\r\n            <div class=\"GlobalRegionSelectColumn\" v-if=\"item.id !== 'province'\">{{ item.name }}</div>\r\n            <div class=\"GlobalRegionSelectItem\" :class=\"{ 'is-active': row.id === regionId }\" v-for=\"row in item.data\"\r\n              :key=\"row.id\" @click=\"handleRegionClick(row)\">\r\n              <div class=\"GlobalRegionSelectIcon\" v-html=\"regionIcon\"></div>\r\n              <div class=\"GlobalRegionSelectName\">{{ row.name }}</div>\r\n            </div>\r\n          </div>\r\n        </el-scrollbar>\r\n        <div class=\"GlobalRegionSelectBodyInfo\" v-if=\"regionType === 'tree'\">\r\n          <div class=\"GlobalRegionSelectBodyInfoOld\">上次登录地区：{{ oldRegionInfo.name }}</div>\r\n          <el-input v-model=\"filterText\" placeholder=\"请输入地区名称\" clearable />\r\n        </div>\r\n        <el-scrollbar class=\"GlobalRegionSelectTree\" v-if=\"regionType === 'tree'\">\r\n          <el-tree ref=\"treeRef\" node-key=\"id\" :data=\"region\" highlight-current\r\n            :props=\"{ label: 'name', children: 'children' }\" :filter-node-method=\"filterNode\"\r\n            :default-expanded-keys=\"regionKeys\" @node-click=\"handleRegionClick\" />\r\n        </el-scrollbar>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalRegionSelect' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, watch, onMounted, nextTick } from 'vue'\r\nimport { useStore } from 'vuex'\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst store = useStore()\r\nconst user = computed(() => store.getters.getUserFn)\r\nconst region = computed(() => store.getters.getAreaFn)\r\n\r\nconst regionIcon = `<svg t=\"1744792270111\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"19596\" width=\"24\" height=\"24\"><path d=\"M753.810286 731.428571c74.459429 10.678857 137.142857 26.185143 181.76 44.909715 58.002286 24.283429 88.429714 55.588571 88.429714 93.330285 0 29.988571-19.017143 56.027429-55.588571 77.385143-27.209143 15.872-64.731429 29.842286-111.542858 41.398857C764.196571 1011.419429 641.828571 1024 512 1024s-252.196571-12.580571-344.868571-35.547429c-46.811429-11.629714-84.406857-25.6-111.616-41.398857C19.163429 925.769143 0 899.510857 0 869.668571c0-37.814857 30.134857-69.046857 87.917714-93.330285 39.789714-16.676571 93.622857-30.793143 156.818286-41.106286l24.137143-3.657143 10.605714 72.411429c-67.876571 9.728-124.050286 23.478857-162.596571 39.716571-28.598857 12.068571-42.788571 24.137143-42.788572 25.965714 0 6.875429 38.546286 29.769143 111.030857 47.762286C271.872 939.008 387.949714 950.857143 512 950.857143c123.611429 0 239.835429-11.922286 326.875429-33.426286 72.557714-17.993143 111.030857-40.886857 111.030857-47.762286 0-1.828571-14.336-13.897143-43.154286-26.038857-34.523429-14.482286-82.944-26.916571-141.165714-36.352l-22.308572-3.437714 10.532572-72.411429z m15.652571-623.908571a352.841143 352.841143 0 0 1 102.180572 253.44 376.685714 376.685714 0 0 1-99.181715 252.342857l-13.019428 13.458286-213.357715 211.821714a54.198857 54.198857 0 0 1-69.266285 5.632l-6.656-5.632L256.804571 626.834286a376.685714 376.685714 0 0 1-110.445714-265.801143A352.841143 352.841143 0 0 1 248.466286 107.52a369.298286 369.298286 0 0 1 520.923428 0z m-475.721143 46.518857a288.402286 288.402286 0 0 0-83.382857 206.921143 312.100571 312.100571 0 0 0 79.725714 207.140571l12.214858 12.8 206.701714 204.361143 206.701714-204.361143a312.100571 312.100571 0 0 0 91.940572-219.940571 288.402286 288.402286 0 0 0-84.699429-206.994286 304.274286 304.274286 0 0 0-429.202286 0z m215.259429 38.765714a170.642286 170.642286 0 1 1 0 341.357715 170.642286 170.642286 0 0 1 0-341.284572z m0 64.073143a106.642286 106.642286 0 1 0 0 213.284572 106.642286 106.642286 0 0 0 0-213.284572z\" fill=\"#ffffff\" p-id=\"19597\"></path></svg>`\r\nconst show = ref(false)\r\nconst treeRef = ref()\r\nconst regionId = ref('')\r\nconst regionType = ref('')\r\nconst regionList = ref([])\r\nconst regionKeys = ref([])\r\nconst oldRegionInfo = ref({})\r\nconst filterText = ref('')\r\nconst groupAreaDataByLevel = (areaData) => {\r\n  if (!Array.isArray(areaData)) return []\r\n  // 预定义已知的 areaLevel 及其名称\r\n  const levelMap = {\r\n    province: { id: 'province', name: '省', data: [] },\r\n    city: { id: 'city', name: '市', data: [] },\r\n    county: { id: 'county', name: '区县', data: [] },\r\n    other: { id: 'other', name: '其他', data: [] }\r\n  }\r\n  // 使用 reduce 方法处理数据分组\r\n  const groupedData = areaData.reduce((acc, item) => {\r\n    const level = item.areaLevel\r\n    const targetGroup = levelMap[level] || levelMap.other\r\n    targetGroup.data.push(item)\r\n    return acc\r\n  }, levelMap)\r\n  // 只返回 data 数组有数据的对象\r\n  return Object.values(groupedData).filter(group => group.data.length > 0)\r\n}\r\nconst handleMethods = (data) => {\r\n  let newData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    newData.push(item)\r\n    newData = [...newData, ...handleMethods(item.children)]\r\n  }\r\n  return newData\r\n}\r\nconst filterNode = (value, data) => {\r\n  if (!value) return true\r\n  return data.name.includes(value)\r\n}\r\nconst handleRegionClick = (data) => {\r\n  sessionStorage.setItem('isRegionSelect', '1')\r\n  filterText.value = ''\r\n  emit('callback', data)\r\n}\r\nconst loginLastAreaId = async () => {\r\n  const newOldRegionInfo = sessionStorage.getItem('oldRegionInfo') || ''\r\n  if (newOldRegionInfo) {\r\n    oldRegionInfo.value = JSON.parse(newOldRegionInfo)\r\n  } else {\r\n    const { data } = await api.loginLastAreaId()\r\n    oldRegionInfo.value = data\r\n  }\r\n}\r\nonMounted(() => {\r\n  loginLastAreaId()\r\n  setTimeout(() => {\r\n    show.value = true\r\n    nextTick(() => { treeRef.value?.setCurrentKey(regionId.value) })\r\n  }, 99)\r\n})\r\nwatch(() => user.value, () => {\r\n  regionId.value = user.value?.areaId\r\n  nextTick(() => { treeRef.value?.setCurrentKey(regionId.value) })\r\n}, { immediate: true })\r\nwatch(() => region.value, () => {\r\n  const newData = handleMethods(region.value)\r\n  regionType.value = newData.length < 10 ? 'list' : 'tree'\r\n  if (regionType.value === 'list') {\r\n    regionList.value = groupAreaDataByLevel(newData)\r\n  } else if (regionType.value === 'tree') {\r\n    regionKeys.value = [region.value[0].id] || []\r\n    nextTick(() => { treeRef.value?.setCurrentKey(regionId.value) })\r\n  }\r\n}, { immediate: true })\r\nwatch(filterText, (val) => {\r\n  treeRef.value?.filter(val)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalRegionSelect {\r\n  width: 100vw;\r\n  height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  z-index: 999;\r\n\r\n  .GlobalRegionSelectBody {\r\n    width: 560px;\r\n    height: 85%;\r\n    min-height: 420px;\r\n    max-height: 580px;\r\n    padding: 20px 40px;\r\n    background: #fff;\r\n    border-radius: var(--el-border-radius-base);\r\n\r\n    .GlobalRegionSelectBodyTitle {\r\n      width: 100%;\r\n      height: var(--zy-height);\r\n      font-weight: bold;\r\n      text-align: center;\r\n      font-size: var(--zy-navigation-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .GlobalRegionSelectBodyInfo {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding-bottom: var(--zy-distance-five);\r\n\r\n      .GlobalRegionSelectBodyInfoOld {\r\n        height: var(--zy-height);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-height);\r\n      }\r\n\r\n      .GlobalRegionSelectBodyInfoTip {\r\n        height: var(--zy-height);\r\n        color: var(--zy-el-color-primary);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-height);\r\n      }\r\n\r\n      .zy-el-input {\r\n        width: 220px;\r\n        --zy-el-input-height: var(--zy-height-routine);\r\n      }\r\n    }\r\n\r\n    .GlobalRegionSelectScrollbar {\r\n      width: 100%;\r\n      height: calc(100% - (var(--zy-height) * 2));\r\n\r\n      .GlobalRegionSelectList {\r\n        width: 100%;\r\n\r\n        .GlobalRegionSelectColumn {\r\n          width: 100%;\r\n          font-weight: bold;\r\n          padding: var(--zy-distance-five);\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n\r\n        .GlobalRegionSelectItem {\r\n          width: 100%;\r\n          height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2));\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 0 var(--zy-distance-five);\r\n          background: var(--zy-el-color-info-light-9);\r\n          border-radius: var(--el-border-radius-small);\r\n          margin-bottom: var(--zy-distance-five);\r\n          cursor: pointer;\r\n\r\n          &.is-active {\r\n            background: var(--zy-el-color-primary-light-9);\r\n\r\n            .GlobalRegionSelectName {\r\n              color: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n\r\n          .GlobalRegionSelectIcon {\r\n            width: 22px;\r\n            height: 22px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n\r\n            svg {\r\n              width: 20px;\r\n              height: 20px;\r\n\r\n              path {\r\n                fill: var(--zy-el-color-primary);\r\n              }\r\n            }\r\n          }\r\n\r\n          .GlobalRegionSelectName {\r\n            padding: var(--zy-distance-five);\r\n            font-size: var(--zy-text-font-size);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalRegionSelectTree {\r\n      width: 100%;\r\n      height: calc(100% - (var(--zy-height) * 2));\r\n\r\n      .zy-el-tree-node.is-current {\r\n        &>.zy-el-tree-node__content {\r\n          .zy-el-tree-node__label {\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-tree-node__content {\r\n        height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2));\r\n\r\n\r\n        .zy-el-tree-node__label {\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EADjCC,GAAA;EAIWD,KAAK,EAAC;;;EAJjBC,GAAA;EAMaD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA+B;;EAPpDC,GAAA;EAYiBD,KAAK,EAAC;;iBAZvB;;EAgBmBA,KAAK,EAAC;AAAwB;;EAhBjDC,GAAA;EAoBaD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA+B;;;;;uBApBlDE,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJC,YAAA,CA4BaC,WAAA;IA5BD,oBAAkB,EAAC,mDAAmD;IAChF,oBAAkB,EAAC;;IAHzBC,OAAA,EAAAC,QAAA,CAOM;MAAA,OAwCS,CA3CiCC,MAAA,CAAAC,IAAI,I,cAA9CP,mBAAA,CAyBM,OAzBNQ,UAyBM,G,0BAxBJC,mBAAA,CAAqD;QAAhDX,KAAK,EAAC;MAA6B,GAAC,QAAM,sBACDQ,MAAA,CAAAI,UAAU,e,cAAxDV,mBAAA,CAGM,OAHNW,UAGM,GAFJF,mBAAA,CAAgF,OAAhFG,UAAgF,EAArC,SAAO,GAAAC,gBAAA,CAAGP,MAAA,CAAAQ,aAAa,CAACC,IAAI,kB,0BACvEN,mBAAA,CAA8D;QAAzDX,KAAK,EAAC;MAA+B,GAAC,eAAa,qB,KARlEkB,mBAAA,gBAUgEV,MAAA,CAAAI,UAAU,e,cAAlEO,YAAA,CASeC,uBAAA;QAnBvBnB,GAAA;QAUsBD,KAAK,EAAC;;QAV5BM,OAAA,EAAAC,QAAA,CAW8C;UAAA,OAA0B,E,kBAA9DL,mBAAA,CAOMmB,SAAA,QAlBhBC,WAAA,CAW6Dd,MAAA,CAAAe,UAAU,EAXvE,UAWqDC,IAAI;iCAA/CtB,mBAAA,CAOM;cAPDF,KAAK,EAAC,wBAAwB;cAA6BC,GAAG,EAAEuB,IAAI,CAACC;gBAC5BD,IAAI,CAACC,EAAE,mB,cAAnDvB,mBAAA,CAAyF,OAAzFwB,UAAyF,EAAAX,gBAAA,CAAlBS,IAAI,CAACP,IAAI,oBAZ5FC,mBAAA,iB,kBAaYhB,mBAAA,CAIMmB,SAAA,QAjBlBC,WAAA,CAa4GE,IAAI,CAACG,IAAI,EAbrH,UAaqGC,GAAG;mCAA5F1B,mBAAA,CAIM;gBAJDF,KAAK,EAbtB6B,eAAA,EAauB,wBAAwB;kBAAA,aAAwBD,GAAG,CAACH,EAAE,KAAKjB,MAAA,CAAAsB;gBAAQ;gBAC3E7B,GAAG,EAAE2B,GAAG,CAACH,EAAE;gBAAGM,OAAK,WAALA,OAAKA,CAAAC,MAAA;kBAAA,OAAExB,MAAA,CAAAyB,iBAAiB,CAACL,GAAG;gBAAA;kBAC3CjB,mBAAA,CAA8D;gBAAzDX,KAAK,EAAC,wBAAwB;gBAACkC,SAAmB,EAAX1B,MAAA,CAAA2B;kBAC5CxB,mBAAA,CAAwD,OAAxDyB,UAAwD,EAAArB,gBAAA,CAAjBa,GAAG,CAACX,IAAI,iB,yBAhB7DoB,UAAA;;;;QAAAC,CAAA;YAAApB,mBAAA,gBAoBsDV,MAAA,CAAAI,UAAU,e,cAAxDV,mBAAA,CAGM,OAHNqC,UAGM,GAFJ5B,mBAAA,CAAgF,OAAhF6B,UAAgF,EAArC,SAAO,GAAAzB,gBAAA,CAAGP,MAAA,CAAAQ,aAAa,CAACC,IAAI,kBACvEb,YAAA,CAAiEqC,mBAAA;QAtB3EC,UAAA,EAsB6BlC,MAAA,CAAAmC,UAAU;QAtBvC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAZ,MAAA;UAAA,OAsB6BxB,MAAA,CAAAmC,UAAU,GAAAX,MAAA;QAAA;QAAEa,WAAW,EAAC,SAAS;QAACC,SAAS,EAAT;mDAtB/D5B,mBAAA,gBAwB2DV,MAAA,CAAAI,UAAU,e,cAA7DO,YAAA,CAIeC,uBAAA;QA5BvBnB,GAAA;QAwBsBD,KAAK,EAAC;;QAxB5BM,OAAA,EAAAC,QAAA,CAyBU;UAAA,OAEwE,CAFxEH,YAAA,CAEwE2C,kBAAA;YAF/DC,GAAG,EAAC,SAAS;YAAC,UAAQ,EAAC,IAAI;YAAErB,IAAI,EAAEnB,MAAA,CAAAyC,MAAM;YAAE,mBAAiB,EAAjB,EAAiB;YAClEC,KAAK,EAAE;cAAAC,KAAA;cAAAC,QAAA;YAAA,CAAuC;YAAG,oBAAkB,EAAE5C,MAAA,CAAA6C,UAAU;YAC/E,uBAAqB,EAAE7C,MAAA,CAAA8C,UAAU;YAAGC,WAAU,EAAE/C,MAAA,CAAAyB;;;QA3B7DK,CAAA;YAAApB,mBAAA,e,KAAAA,mBAAA,e;;IAAAoB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}