{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = [\"lement-loading-text\"];\nvar _hoisted_2 = {\n  class: \"VersionComparisonFileInfoHead\"\n};\nvar _hoisted_3 = {\n  class: \"VersionComparisonFileInfoHeadText\"\n};\nvar _hoisted_4 = {\n  class: \"VersionComparisonFileInfoHeadItem\"\n};\nvar _hoisted_5 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_upload = _resolveComponent(\"el-upload\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"VersionComparisonFileInfo\",\n    \"lement-loading-text\": $setup.loadingText\n  }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.props.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_upload, {\n    action: \"/\",\n    \"before-upload\": $setup.handleFile,\n    \"http-request\": $setup.fileUpload,\n    \"show-file-list\": false\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"default\",\n        round: \"\"\n      }, {\n        default: _withCtx(function () {\n          return _cache[0] || (_cache[0] = [_createTextVNode(\"上传文件\")]);\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  })])]), _createVNode(_component_el_scrollbar, {\n    class: \"VersionComparisonFileInfoScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        class: \"VersionComparisonFileInfoWord\",\n        innerHTML: $setup.html\n      }, null, 8 /* PROPS */, _hoisted_5)];\n    }),\n    _: 1 /* STABLE */\n  })], 8 /* PROPS */, _hoisted_1)), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "$setup", "loadingText", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "props", "name", "_hoisted_4", "_createVNode", "_component_el_upload", "action", "handleFile", "fileUpload", "default", "_withCtx", "_component_el_button", "type", "size", "round", "_cache", "_createTextVNode", "_", "_component_el_scrollbar", "innerHTML", "html", "_hoisted_5", "_hoisted_1", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\Intelligentize\\VersionComparison\\VersionComparisonFileInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"VersionComparisonFileInfo\"\r\n       v-loading=\"loading\"\r\n       :lement-loading-text=\"loadingText\">\r\n    <div class=\"VersionComparisonFileInfoHead\">\r\n      <div class=\"VersionComparisonFileInfoHeadText\">{{ props.name }}</div>\r\n      <div class=\"VersionComparisonFileInfoHeadItem\">\r\n        <el-upload action=\"/\"\r\n                   :before-upload=\"handleFile\"\r\n                   :http-request=\"fileUpload\"\r\n                   :show-file-list=\"false\">\r\n          <el-button type=\"primary\"\r\n                     size=\"default\"\r\n                     round>上传文件</el-button>\r\n        </el-upload>\r\n      </div>\r\n    </div>\r\n    <el-scrollbar class=\"VersionComparisonFileInfoScrollbar\">\r\n      <div class=\"VersionComparisonFileInfoWord\"\r\n           v-html=\"html\"></div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VersionComparisonFileInfo' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, watch } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' }, name: { type: String, default: '' } })\r\nconst emit = defineEmits(['loadCallback'])\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst html = ref('')\r\n/**\r\n   * 限制上传附件的文件类型\r\n  */\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  const isShow = ['doc', 'docx', 'wps'].includes(fileType)\r\n  if (!isShow) { ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'wps'].join('、')}格式!` }) }\r\n  return isShow\r\n}\r\n/**\r\n   * 上传附件请求方法\r\n  */\r\nconst fileUpload = async (file) => {\r\n  try {\r\n    loading.value = true\r\n    emit('loadCallback', '')\r\n    const param = new FormData()\r\n    param.append('file', file.file)\r\n    const { data } = await api.wordApiWordToHtml(param)\r\n    html.value = data\r\n    loading.value = false\r\n    emit('loadCallback', file.file)\r\n  } catch (err) {\r\n    loading.value = false\r\n    emit('loadCallback', '')\r\n  }\r\n}\r\n/**\r\n   * 上传附件请求方法\r\n  */\r\nconst fileUploadId = async (id) => {\r\n  try {\r\n    loading.value = true\r\n    const param = new FormData()\r\n    param.append('urlPath', api.filePreview(id))\r\n    const { data } = await api.wordApiWordToHtml(param)\r\n    html.value = data\r\n    loading.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nwatch(() => props.id, (val) => {\r\n  if (val) { fileUploadId(props.id) }\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.VersionComparisonFileInfo {\r\n  width: 100%;\r\n  height: calc(50% - 10px);\r\n  margin-bottom: 20px;\r\n  border: 1px solid var(--zy-el-border-color-lighter);\r\n  border-radius: 8px;\r\n  box-shadow: var(--zy-el-box-shadow);\r\n\r\n  .VersionComparisonFileInfoHead {\r\n    width: 100%;\r\n    height: 50px;\r\n    color: #fff;\r\n    background-image: linear-gradient(72deg, var(--zy-el-color-primary), var(--zy-el-color-primary-light-9));\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    padding: 4px 20px;\r\n    border-top-left-radius: 8px;\r\n    border-top-right-radius: 8px;\r\n\r\n\r\n    .VersionComparisonFileInfoHeadText {\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n\r\n    .VersionComparisonFileInfoHeadItem {\r\n      .zy-el-button {\r\n        --zy-el-button-size: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .VersionComparisonFileInfoScrollbar {\r\n    width: 100%;\r\n    height: calc(100% - 60px);\r\n\r\n    .VersionComparisonFileInfoWord {\r\n      padding: 0 20px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";iBAAA;;EAISA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAmC;;EACzCA,KAAK,EAAC;AAAmC;iBANpD;;;;;;wCACEC,mBAAA,CAoBM;IApBDD,KAAK,EAAC,2BAA2B;IAEhC,qBAAmB,EAAEE,MAAA,CAAAC;MACzBC,mBAAA,CAYM,OAZNC,UAYM,GAXJD,mBAAA,CAAqE,OAArEE,UAAqE,EAAAC,gBAAA,CAAnBL,MAAA,CAAAM,KAAK,CAACC,IAAI,kBAC5DL,mBAAA,CASM,OATNM,UASM,GARJC,YAAA,CAOYC,oBAAA;IAPDC,MAAM,EAAC,GAAG;IACT,eAAa,EAAEX,MAAA,CAAAY,UAAU;IACzB,cAAY,EAAEZ,MAAA,CAAAa,UAAU;IACxB,gBAAc,EAAE;;IAVpCC,OAAA,EAAAC,QAAA,CAWU;MAAA,OAEiC,CAFjCN,YAAA,CAEiCO,oBAAA;QAFtBC,IAAI,EAAC,SAAS;QACdC,IAAI,EAAC,SAAS;QACdC,KAAK,EAAL;;QAbrBL,OAAA,EAAAC,QAAA,CAa2B;UAAA,OAAIK,MAAA,QAAAA,MAAA,OAb/BC,gBAAA,CAa2B,MAAI,E;;QAb/BC,CAAA;;;IAAAA,CAAA;UAiBIb,YAAA,CAGec,uBAAA;IAHDzB,KAAK,EAAC;EAAoC;IAjB5DgB,OAAA,EAAAC,QAAA,CAkBM;MAAA,OACyB,CADzBb,mBAAA,CACyB;QADpBJ,KAAK,EAAC,+BAA+B;QACrC0B,SAAa,EAALxB,MAAA,CAAAyB;8BAnBnBC,UAAA,E;;IAAAJ,CAAA;sBAAAK,UAAA,K,qBAEkB3B,MAAA,CAAA4B,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}