{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ChatSendImg\"\n};\nvar _hoisted_2 = {\n  class: \"ChatSendImgUser\"\n};\nvar _hoisted_3 = {\n  class: \"ChatSendImgUserName ellipsis\"\n};\nvar _hoisted_4 = {\n  class: \"ChatSendImgBody\"\n};\nvar _hoisted_5 = {\n  class: \"ChatSendImgButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"ChatSendImgObject\"\n  }, \"发送给：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n    src: $setup.imgUrl($setup.chatInfo.chatObjectInfo.img),\n    fit: \"cover\",\n    draggable: \"false\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.chatInfo.chatObjectInfo.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_image, {\n    src: $setup.fileImg.url,\n    fit: \"cover\",\n    draggable: \"false\"\n  }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_button, {\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return _cache[0] || (_cache[0] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSubmit\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"发送\")]);\n    }),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_image", "src", "$setup", "imgUrl", "chatInfo", "chatObjectInfo", "img", "fit", "draggable", "_hoisted_3", "_toDisplayString", "name", "_hoisted_4", "fileImg", "url", "_hoisted_5", "_component_el_button", "onClick", "handleReset", "default", "_withCtx", "_cache", "_createTextVNode", "_", "type", "handleSubmit"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\ChatSendImg\\ChatSendImg.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ChatSendImg\">\r\n    <div class=\"ChatSendImgObject\">发送给：</div>\r\n    <div class=\"ChatSendImgUser\">\r\n      <el-image :src=\"imgUrl(chatInfo.chatObjectInfo.img)\" fit=\"cover\" draggable=\"false\" />\r\n      <div class=\"ChatSendImgUserName ellipsis\">{{ chatInfo.chatObjectInfo.name }}</div>\r\n    </div>\r\n    <div class=\"ChatSendImgBody\">\r\n      <el-image :src=\"fileImg.url\" fit=\"cover\" draggable=\"false\" />\r\n    </div>\r\n    <div class=\"ChatSendImgButton\">\r\n      <el-button @click=\"handleReset\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">发送</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChatSendImg' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { computed } from 'vue'\r\nconst props = defineProps({\r\n  chatInfo: { type: Object, default: () => ({}) },\r\n  fileImg: { type: Object, default: () => ({}) }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst chatInfo = computed(() => props.chatInfo)\r\nconst fileImg = computed(() => props.fileImg)\r\n// 图片地址拼接组合\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\nconst handleSubmit = () => { emit('callback', true) }\r\nconst handleReset = () => { emit('callback', false) }\r\n</script>\r\n<style lang=\"scss\">\r\n.ChatSendImg {\r\n  width: 360px;\r\n  height: 100%;\r\n  padding: 20px 0;\r\n\r\n  .ChatSendImgObject {\r\n    width: 100%;\r\n    height: 26px;\r\n    line-height: 26px;\r\n    font-size: 16px;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .ChatSendImgUser {\r\n    width: 100%;\r\n    height: 68px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    position: relative;\r\n    padding: 0 20px 10px 20px;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: calc(100% - 40px);\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      position: absolute;\r\n      left: 20px;\r\n      bottom: 10px;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 38px;\r\n      height: 38px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .ChatSendImgUserName {\r\n      width: calc(100% - 52px);\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .ChatSendImgBody {\r\n    width: 100%;\r\n    height: calc(100% - 140px);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .zy-el-image {\r\n      max-width: 80%;\r\n      height: 80%;\r\n    }\r\n  }\r\n\r\n  .ChatSendImgButton {\r\n    width: 100%;\r\n    height: 46px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: center;\r\n\r\n    .zy-el-button {\r\n      width: 120px;\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAA8B;;EAEtCA,KAAK,EAAC;AAAiB;;EAGvBA,KAAK,EAAC;AAAmB;;;;uBAThCC,mBAAA,CAaM,OAbNC,UAaM,G,0BAZJC,mBAAA,CAAyC;IAApCH,KAAK,EAAC;EAAmB,GAAC,MAAI,sBACnCG,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAAqFC,mBAAA;IAA1EC,GAAG,EAAEC,MAAA,CAAAC,MAAM,CAACD,MAAA,CAAAE,QAAQ,CAACC,cAAc,CAACC,GAAG;IAAGC,GAAG,EAAC,OAAO;IAACC,SAAS,EAAC;oCAC3EX,mBAAA,CAAkF,OAAlFY,UAAkF,EAAAC,gBAAA,CAArCR,MAAA,CAAAE,QAAQ,CAACC,cAAc,CAACM,IAAI,iB,GAE3Ed,mBAAA,CAEM,OAFNe,UAEM,GADJb,YAAA,CAA6DC,mBAAA;IAAlDC,GAAG,EAAEC,MAAA,CAAAW,OAAO,CAACC,GAAG;IAAEP,GAAG,EAAC,OAAO;IAACC,SAAS,EAAC;sCAErDX,mBAAA,CAGM,OAHNkB,UAGM,GAFJhB,YAAA,CAA8CiB,oBAAA;IAAlCC,OAAK,EAAEf,MAAA,CAAAgB;EAAW;IAXpCC,OAAA,EAAAC,QAAA,CAWsC;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAXxCC,gBAAA,CAWsC,IAAE,E;;IAXxCC,CAAA;MAYMxB,YAAA,CAA8DiB,oBAAA;IAAnDQ,IAAI,EAAC,SAAS;IAAEP,OAAK,EAAEf,MAAA,CAAAuB;;IAZxCN,OAAA,EAAAC,QAAA,CAYsD;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAZxDC,gBAAA,CAYsD,IAAE,E;;IAZxDC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}