{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { ref, computed, nextTick } from 'vue';\nimport { emotion } from '../js/emotion.js';\nimport { emoteIcon, folderIcon, lineFeedIcon, voteIcon } from '../js/icon.js';\nexport default {\n  __name: 'GlobalChatEditor',\n  props: {\n    isVote: {\n      type: Boolean,\n      default: false\n    },\n    userData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  emits: ['handleFile', 'handleVote', 'handlePasteImg', 'handleSendMessage'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    var props = __props;\n    var emit = __emit;\n    var inputRef = ref();\n    var content = ref('');\n    var show = ref(false);\n    var keyword = ref('');\n    var cursorPos = ref(0);\n    var mentionStyle = ref({\n      top: '0px',\n      left: '0px'\n    });\n    // 存储@用户的位置信息\n    var mentionPositions = ref([]);\n    var mentionIdCounter = 0; // 用于生成唯一的mention ID\n\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    // 根据关键字过滤用户列表\n    var filteredUserList = computed(function () {\n      if (!keyword.value) return props.userData;\n      return props.userData.filter(function (user) {\n        var _user$userName, _keyword$value;\n        return (_user$userName = user.userName) === null || _user$userName === void 0 || (_user$userName = _user$userName.toLowerCase()) === null || _user$userName === void 0 ? void 0 : _user$userName.includes((_keyword$value = keyword.value) === null || _keyword$value === void 0 ? void 0 : _keyword$value.toLowerCase());\n      });\n    });\n    // 检查是否在@用户的位置\n    var isInMention = function isInMention(position) {\n      return mentionPositions.value.some(function (pos) {\n        return position >= pos.start && position <= pos.end + 1;\n      });\n    };\n    // 获取光标所在的@用户位置\n    var getMentionAtPosition = function getMentionAtPosition(position) {\n      return mentionPositions.value.find(function (pos) {\n        return position >= pos.start && position <= pos.end;\n      });\n    };\n    var isMacText = function isMacText() {\n      var userAgent = navigator.userAgent.toLowerCase();\n      return (userAgent === null || userAgent === void 0 ? void 0 : userAgent.includes('macintosh')) || (userAgent === null || userAgent === void 0 ? void 0 : userAgent.includes('mac os x'));\n    };\n    var fileUpload = function fileUpload(file) {\n      emit('handleFile', file);\n    };\n    var handleVote = function handleVote() {\n      emit('handleVote');\n    };\n    // 隐藏用户选择器\n    var hideUserPicker = function hideUserPicker() {\n      show.value = false;\n      keyword.value = '';\n    };\n\n    // 计算@符号的位置\n    var calculateAtPosition = function calculateAtPosition() {\n      if (!inputRef.value) return;\n      var textarea = inputRef.value.$el.querySelector('textarea');\n      var text = textarea.value;\n      var lastAtIndex = text.lastIndexOf('@');\n      if (lastAtIndex === -1) return;\n      // 获取textarea的位置信息\n      var rect = textarea.getBoundingClientRect();\n      var textareaStyle = window.getComputedStyle(textarea);\n      var lineHeight = parseInt(textareaStyle.lineHeight);\n      var paddingTop = parseInt(textareaStyle.paddingTop);\n      var paddingLeft = parseInt(textareaStyle.paddingLeft);\n      // 计算@符号之前的文本有多少行\n      var textBeforeAt = text.substring(0, lastAtIndex);\n      var lines = textBeforeAt.split('\\n');\n      var currentLineIndex = lines.length - 1;\n      // 计算@符号在当前行的位置\n      var currentLine = lines[currentLineIndex];\n      var atPositionInLine = currentLine ? currentLine.length : 0;\n      // 计算位置\n      var top = rect.top + paddingTop + currentLineIndex * lineHeight;\n      var left = rect.left + paddingLeft + atPositionInLine * 8; // 8px 是一个近似的字符宽度\n      // 计算用户选择器的实际高度\n      var userItemHeight = 32; // 每个用户项的高度（包含padding）\n      var maxHeight = 220; // 最大高度\n      var actualHeight = Math.min(filteredUserList.value.length * userItemHeight, maxHeight) + 14;\n      // 设置选择器位置\n      mentionStyle.value = {\n        top: `${top - actualHeight - 5}px`,\n        // 在@符号上方5px的位置\n        left: `${left}px`,\n        height: `${actualHeight}px`,\n        // 使用固定高度而不是maxHeight\n        maxHeight: 'none' // 移除maxHeight限制\n      };\n    };\n\n    // 处理输入\n    var handleInput = function handleInput(value) {\n      if (!props.userData.length) return;\n      var textarea = inputRef.value.$el.querySelector('textarea');\n      var cursorPosition = textarea.selectionStart;\n      // 检查是否在@用户的位置\n      if (isInMention(cursorPosition)) {\n        content.value = value;\n        return;\n      }\n      var lastChar = value.slice(-1);\n      if (lastChar === '@') {\n        show.value = true;\n        keyword.value = '';\n        nextTick(function () {\n          calculateAtPosition();\n        });\n      } else if (show.value) {\n        var lastAtIndex = value.lastIndexOf('@');\n        if (lastAtIndex !== -1) {\n          keyword.value = value.slice(lastAtIndex + 1);\n          nextTick(function () {\n            calculateAtPosition();\n          });\n        } else {\n          show.value = false;\n        }\n      }\n      // 更新所有@用户的位置\n      updateMentionPositions(value);\n    };\n\n    // 更新@用户的位置\n    var updateMentionPositions = function updateMentionPositions(text) {\n      // 保留现有的mention信息，只更新位置\n      var newPositions = [];\n      var currentIndex = 0;\n      var _loop = function _loop() {\n        var atIndex = text.indexOf('@', currentIndex);\n        if (atIndex === -1) return 1; // break\n        // 查找@后面的用户名（直到空格或文本结束）\n        var endIndex = atIndex + 1;\n        while (endIndex < text.length && text[endIndex] !== ' ') {\n          endIndex++;\n        }\n        // 如果找到了完整的@用户（@后面有内容且以空格结束）\n        if (endIndex > atIndex + 1) {\n          var mentionText = text.slice(atIndex, endIndex);\n          // 查找是否已存在相同的mention\n          var existingMention = mentionPositions.value.find(function (m) {\n            return m.text === mentionText && !newPositions.some(function (n) {\n              return n.id === m.id;\n            });\n          });\n          if (existingMention) {\n            // 如果存在，更新位置\n            newPositions.push(_objectSpread(_objectSpread({}, existingMention), {}, {\n              start: atIndex,\n              end: endIndex - 1\n            }));\n          }\n        }\n        currentIndex = endIndex + 1;\n      };\n      while (currentIndex < text.length) {\n        if (_loop()) break;\n      }\n      // 更新位置信息\n      mentionPositions.value = newPositions;\n    };\n    var appendEmotion = function appendEmotion(item) {\n      var textarea = inputRef.value.$el.querySelector('textarea');\n      var cursorPosition = textarea.selectionStart;\n      // 检查是否在@用户的位置\n      if (isInMention(cursorPosition)) return;\n      if (!content.value) {\n        content.value = item.text;\n      } else {\n        var start = content.value.slice(0, cursorPos.value);\n        var end = content.value.slice(cursorPos.value);\n        content.value = `${start}${item.text}${end}`;\n        nextTick(function () {\n          inputRef.value.focus();\n          if (inputRef.value.$el.querySelector('textarea')) {\n            inputRef.value.$el.querySelector('textarea').setSelectionRange(cursorPos.value + item.text.length, cursorPos.value + item.text.length);\n          }\n        });\n      }\n    };\n    var handleBlur = function handleBlur(e) {\n      cursorPos.value = e.srcElement.selectionStart;\n    };\n    // 处理键盘事件\n    var handleKeyDown = function handleKeyDown(e) {\n      if (e.keyCode == 13) {\n        if (!e.ctrlKey && !e.metaKey) {\n          // 处理回车发送\n          e.preventDefault();\n          handleSendMessage();\n        } else {\n          cursorPos.value = e.srcElement.selectionStart;\n          appendEmotion({\n            text: '\\n'\n          });\n        }\n      } else {\n        var textarea = inputRef.value.$el.querySelector('textarea');\n        var cursorPosition = textarea.selectionStart;\n        if (e.key === 'Backspace') {\n          // 检查是否在@用户的位置或紧跟在@用户后面\n          for (var i = mentionPositions.value.length - 1; i >= 0; i--) {\n            var mention = mentionPositions.value[i];\n            if (cursorPosition > mention.start && cursorPosition <= mention.end + 1) {\n              e.preventDefault();\n              // 删除整个@用户（包括末尾空格）\n              var beforeText = content.value.slice(0, mention.start);\n              var afterText = content.value.slice(mention.end + 1);\n              content.value = beforeText + afterText;\n              // 删除当前@用户的位置信息\n              mentionPositions.value.splice(i, 1);\n              // 更新后面的@用户的位置\n              var deleteLength = mention.end - mention.start + 2;\n              for (var j = i; j < mentionPositions.value.length; j++) {\n                mentionPositions.value[j].start -= deleteLength;\n                mentionPositions.value[j].end -= deleteLength;\n              }\n              break;\n            }\n          }\n        } else if (e.key !== 'ArrowLeft' && e.key !== 'ArrowRight' && e.key !== 'ArrowUp' && e.key !== 'ArrowDown') {\n          // 如果不是方向键，检查是否在@用户的位置\n          if (isInMention(cursorPosition)) e.preventDefault();\n        }\n      }\n    };\n\n    // 处理粘贴事件\n    var handlePaste = function handlePaste(e) {\n      var clipboardData = event.clipboardData || window.clipboardData;\n      if (clipboardData) {\n        var _loop2 = function _loop2() {\n          var _item$type;\n          var item = clipboardData.items[i];\n          if (item.kind === 'file' && (_item$type = item.type) !== null && _item$type !== void 0 && _item$type.includes('image/')) {\n            var file = item.getAsFile();\n            var reader = new FileReader();\n            reader.onload = function (e) {\n              emit('handlePasteImg', {\n                id: guid(),\n                url: e.target.result,\n                file: file\n              });\n            };\n            reader.readAsDataURL(file);\n          }\n        };\n        for (var i = 0; i < clipboardData.items.length; i++) {\n          _loop2();\n        }\n      } else {\n        var textarea = inputRef.value.$el.querySelector('textarea');\n        var cursorPosition = textarea.selectionStart;\n        // 检查是否在@用户的位置\n        if (isInMention(cursorPosition)) e.preventDefault();\n      }\n    };\n\n    // 插入@用户\n    var insertUser = function insertUser(user) {\n      var lastAtIndex = content.value.lastIndexOf('@');\n      if (lastAtIndex !== -1) {\n        var mentionText = `@${user.userName} `;\n        var newMessage = content.value.slice(0, lastAtIndex) + mentionText + content.value.slice(lastAtIndex + 1 + keyword.value.length);\n        // 先重置状态\n        show.value = false;\n        keyword.value = '';\n        // 然后更新消息\n        content.value = newMessage;\n        // 添加新的mention信息\n        mentionPositions.value.push({\n          id: ++mentionIdCounter,\n          // 生成新的唯一ID\n          text: mentionText.trim(),\n          start: lastAtIndex,\n          end: lastAtIndex + mentionText.length - 2,\n          // 不包含末尾空格\n          userInfo: user\n        });\n      }\n    };\n    // 发送消息\n    var getMessage = function getMessage() {\n      if (content.value.trim()) {\n        // 构建发送的消息对象\n        return {\n          content: content.value,\n          mentions: mentionPositions.value.map(function (m) {\n            return {\n              id: m.id,\n              userInfo: m.userInfo\n            };\n          })\n        };\n      }\n      return null;\n    };\n    var clearMessage = function clearMessage() {\n      content.value = '';\n      mentionPositions.value = [];\n      mentionIdCounter = 0; // 重置ID计数器\n    };\n    // 发送消息\n    var handleSendMessage = function handleSendMessage() {\n      var messageData = getMessage();\n      if (messageData) {\n        emit('handleSendMessage', messageData);\n        clearMessage();\n      }\n    };\n\n    // 点击外部指令\n    var vClickOutside = {\n      mounted(el, binding) {\n        el._clickOutside = function (event) {\n          if (!(el === event.target || el.contains(event.target))) {\n            binding.value(event);\n          }\n        };\n        document.addEventListener('click', el._clickOutside);\n      },\n      unmounted(el) {\n        document.removeEventListener('click', el._clickOutside);\n      }\n    };\n    __expose({\n      getMessage,\n      clearMessage\n    });\n    var __returned__ = {\n      props,\n      emit,\n      inputRef,\n      content,\n      show,\n      keyword,\n      cursorPos,\n      mentionStyle,\n      mentionPositions,\n      get mentionIdCounter() {\n        return mentionIdCounter;\n      },\n      set mentionIdCounter(v) {\n        mentionIdCounter = v;\n      },\n      guid,\n      filteredUserList,\n      isInMention,\n      getMentionAtPosition,\n      isMacText,\n      fileUpload,\n      handleVote,\n      hideUserPicker,\n      calculateAtPosition,\n      handleInput,\n      updateMentionPositions,\n      appendEmotion,\n      handleBlur,\n      handleKeyDown,\n      handlePaste,\n      insertUser,\n      getMessage,\n      clearMessage,\n      handleSendMessage,\n      vClickOutside,\n      ref,\n      computed,\n      nextTick,\n      get emotion() {\n        return emotion;\n      },\n      get emoteIcon() {\n        return emoteIcon;\n      },\n      get folderIcon() {\n        return folderIcon;\n      },\n      get lineFeedIcon() {\n        return lineFeedIcon;\n      },\n      get voteIcon() {\n        return voteIcon;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "nextTick", "emotion", "emoteIcon", "folderIcon", "lineFeedIcon", "voteIcon", "props", "__props", "emit", "__emit", "inputRef", "content", "show", "keyword", "cursorPos", "mentionStyle", "top", "left", "mentionPositions", "mentionIdCounter", "guid", "replace", "c", "r", "Math", "random", "v", "toString", "filteredUserList", "value", "userData", "filter", "user", "_user$userName", "_keyword$value", "userName", "toLowerCase", "includes", "isInMention", "position", "some", "pos", "start", "end", "getMentionAtPosition", "find", "isMacText", "userAgent", "navigator", "fileUpload", "file", "handleVote", "hideUserPicker", "calculateAtPosition", "textarea", "$el", "querySelector", "text", "lastAtIndex", "lastIndexOf", "rect", "getBoundingClientRect", "textareaStyle", "window", "getComputedStyle", "lineHeight", "parseInt", "paddingTop", "paddingLeft", "textBeforeAt", "substring", "lines", "split", "currentLineIndex", "length", "currentLine", "atPositionInLine", "userItemHeight", "maxHeight", "actualHeight", "min", "height", "handleInput", "cursorPosition", "selectionStart", "lastChar", "slice", "updateMentionPositions", "newPositions", "currentIndex", "_loop", "atIndex", "indexOf", "endIndex", "mentionText", "existingMention", "m", "n", "id", "push", "_objectSpread", "appendEmotion", "item", "focus", "setSelectionRange", "handleBlur", "e", "srcElement", "handleKeyDown", "keyCode", "ctrl<PERSON>ey", "metaKey", "preventDefault", "handleSendMessage", "key", "i", "mention", "beforeText", "afterText", "splice", "deleteLength", "j", "handlePaste", "clipboardData", "event", "_loop2", "_item$type", "items", "kind", "type", "getAsFile", "reader", "FileReader", "onload", "url", "target", "result", "readAsDataURL", "insertUser", "newMessage", "trim", "userInfo", "getMessage", "mentions", "map", "clearMessage", "messageData", "vClickOutside", "mounted", "el", "binding", "_clickOutside", "contains", "document", "addEventListener", "unmounted", "removeEventListener", "__expose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/components/GlobalChatEditor.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatEditor\">\r\n    <div class=\"GlobalChatViewControls\">\r\n      <el-popover placement=\"top\" trigger=\"click\" popper-class=\"GlobalChatEmotionPopover\">\r\n        <template #reference>\r\n          <div class=\"GlobalChatViewControlsItem\" v-html=\"emoteIcon\" title=\"表情\"></div>\r\n        </template>\r\n        <el-scrollbar class=\"GlobalChatEmotionScroll\">\r\n          <div class=\"GlobalChatEmotion\">\r\n            <div class=\"GlobalChatEmotionItem\" v-for=\"item in emotion\" :key=\"item.name\" @click=\"appendEmotion(item)\">\r\n              <div :class=\"item.name\"></div>\r\n            </div>\r\n            <div class=\"GlobalChatEmotionItem\"></div>\r\n            <div class=\"GlobalChatEmotionItem\"></div>\r\n            <div class=\"GlobalChatEmotionItem\"></div>\r\n          </div>\r\n        </el-scrollbar>\r\n      </el-popover>\r\n      <el-upload action=\"/\" :http-request=\"fileUpload\" :show-file-list=\"false\" multiple>\r\n        <div class=\"GlobalChatViewControlsItem\" v-html=\"folderIcon\" title=\"文件\"></div>\r\n      </el-upload>\r\n      <div\r\n        class=\"GlobalChatViewControlsItem\"\r\n        v-html=\"voteIcon\"\r\n        title=\"投票\"\r\n        @click=\"handleVote\"\r\n        v-if=\"props.isVote\"></div>\r\n      <div\r\n        class=\"GlobalChatViewControlsItem is-min\"\r\n        v-html=\"lineFeedIcon\"\r\n        title=\"换行\"\r\n        @click=\"appendEmotion({ text: '\\n' })\"></div>\r\n    </div>\r\n    <el-input\r\n      ref=\"inputRef\"\r\n      v-model=\"content\"\r\n      type=\"textarea\"\r\n      resize=\"none\"\r\n      :rows=\"4\"\r\n      @input=\"handleInput\"\r\n      @keydown=\"handleKeyDown\"\r\n      @paste=\"handlePaste\"\r\n      @blur=\"handleBlur\" />\r\n    <div class=\"GlobalChatViewControlsBotton\">\r\n      <span>Enter 发送 ｜ {{ isMacText() ? 'Command + Enter 换行' : 'Ctrl + Enter 换行' }}</span>\r\n      <el-button type=\"primary\" @click=\"handleSendMessage\">发送</el-button>\r\n    </div>\r\n    <!-- 用户选择器 -->\r\n    <el-scrollbar v-show=\"show\" class=\"GlobalChatUserPicker\" :style=\"mentionStyle\" v-click-outside=\"hideUserPicker\">\r\n      <div class=\"GlobalChatUserPickerList\">\r\n        <div class=\"GlobalChatUserPickerItem\" v-for=\"user in filteredUserList\" :key=\"user.id\" @click=\"insertUser(user)\">\r\n          {{ user.userName }}\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script setup name=\"GlobalChatEditor\">\r\nimport { ref, computed, nextTick } from 'vue'\r\nimport { emotion } from '../js/emotion.js'\r\nimport { emoteIcon, folderIcon, lineFeedIcon, voteIcon } from '../js/icon.js'\r\nconst props = defineProps({ isVote: { type: Boolean, default: false }, userData: { type: Array, default: () => [] } })\r\nconst emit = defineEmits(['handleFile', 'handleVote', 'handlePasteImg', 'handleSendMessage'])\r\n\r\nconst inputRef = ref()\r\nconst content = ref('')\r\n\r\nconst show = ref(false)\r\nconst keyword = ref('')\r\nconst cursorPos = ref(0)\r\nconst mentionStyle = ref({ top: '0px', left: '0px' })\r\n// 存储@用户的位置信息\r\nconst mentionPositions = ref([])\r\nlet mentionIdCounter = 0 // 用于生成唯一的mention ID\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\n// 根据关键字过滤用户列表\r\nconst filteredUserList = computed(() => {\r\n  if (!keyword.value) return props.userData\r\n  return props.userData.filter((user) => user.userName?.toLowerCase()?.includes(keyword.value?.toLowerCase()))\r\n})\r\n// 检查是否在@用户的位置\r\nconst isInMention = (position) => mentionPositions.value.some((pos) => position >= pos.start && position <= pos.end + 1)\r\n// 获取光标所在的@用户位置\r\nconst getMentionAtPosition = (position) =>\r\n  mentionPositions.value.find((pos) => position >= pos.start && position <= pos.end)\r\n\r\nconst isMacText = () => {\r\n  const userAgent = navigator.userAgent.toLowerCase()\r\n  return userAgent?.includes('macintosh') || userAgent?.includes('mac os x')\r\n}\r\nconst fileUpload = (file) => {\r\n  emit('handleFile', file)\r\n}\r\nconst handleVote = () => {\r\n  emit('handleVote')\r\n}\r\n// 隐藏用户选择器\r\nconst hideUserPicker = () => {\r\n  show.value = false\r\n  keyword.value = ''\r\n}\r\n\r\n// 计算@符号的位置\r\nconst calculateAtPosition = () => {\r\n  if (!inputRef.value) return\r\n  const textarea = inputRef.value.$el.querySelector('textarea')\r\n  const text = textarea.value\r\n  const lastAtIndex = text.lastIndexOf('@')\r\n  if (lastAtIndex === -1) return\r\n  // 获取textarea的位置信息\r\n  const rect = textarea.getBoundingClientRect()\r\n  const textareaStyle = window.getComputedStyle(textarea)\r\n  const lineHeight = parseInt(textareaStyle.lineHeight)\r\n  const paddingTop = parseInt(textareaStyle.paddingTop)\r\n  const paddingLeft = parseInt(textareaStyle.paddingLeft)\r\n  // 计算@符号之前的文本有多少行\r\n  const textBeforeAt = text.substring(0, lastAtIndex)\r\n  const lines = textBeforeAt.split('\\n')\r\n  const currentLineIndex = lines.length - 1\r\n  // 计算@符号在当前行的位置\r\n  const currentLine = lines[currentLineIndex]\r\n  const atPositionInLine = currentLine ? currentLine.length : 0\r\n  // 计算位置\r\n  const top = rect.top + paddingTop + currentLineIndex * lineHeight\r\n  const left = rect.left + paddingLeft + atPositionInLine * 8 // 8px 是一个近似的字符宽度\r\n  // 计算用户选择器的实际高度\r\n  const userItemHeight = 32 // 每个用户项的高度（包含padding）\r\n  const maxHeight = 220 // 最大高度\r\n  const actualHeight = Math.min(filteredUserList.value.length * userItemHeight, maxHeight) + 14\r\n  // 设置选择器位置\r\n  mentionStyle.value = {\r\n    top: `${top - actualHeight - 5}px`, // 在@符号上方5px的位置\r\n    left: `${left}px`,\r\n    height: `${actualHeight}px`, // 使用固定高度而不是maxHeight\r\n    maxHeight: 'none' // 移除maxHeight限制\r\n  }\r\n}\r\n\r\n// 处理输入\r\nconst handleInput = (value) => {\r\n  if (!props.userData.length) return\r\n  const textarea = inputRef.value.$el.querySelector('textarea')\r\n  const cursorPosition = textarea.selectionStart\r\n  // 检查是否在@用户的位置\r\n  if (isInMention(cursorPosition)) {\r\n    content.value = value\r\n    return\r\n  }\r\n  const lastChar = value.slice(-1)\r\n  if (lastChar === '@') {\r\n    show.value = true\r\n    keyword.value = ''\r\n    nextTick(() => {\r\n      calculateAtPosition()\r\n    })\r\n  } else if (show.value) {\r\n    const lastAtIndex = value.lastIndexOf('@')\r\n    if (lastAtIndex !== -1) {\r\n      keyword.value = value.slice(lastAtIndex + 1)\r\n      nextTick(() => {\r\n        calculateAtPosition()\r\n      })\r\n    } else {\r\n      show.value = false\r\n    }\r\n  }\r\n  // 更新所有@用户的位置\r\n  updateMentionPositions(value)\r\n}\r\n\r\n// 更新@用户的位置\r\nconst updateMentionPositions = (text) => {\r\n  // 保留现有的mention信息，只更新位置\r\n  const newPositions = []\r\n  let currentIndex = 0\r\n  while (currentIndex < text.length) {\r\n    const atIndex = text.indexOf('@', currentIndex)\r\n    if (atIndex === -1) break\r\n    // 查找@后面的用户名（直到空格或文本结束）\r\n    let endIndex = atIndex + 1\r\n    while (endIndex < text.length && text[endIndex] !== ' ') {\r\n      endIndex++\r\n    }\r\n    // 如果找到了完整的@用户（@后面有内容且以空格结束）\r\n    if (endIndex > atIndex + 1) {\r\n      const mentionText = text.slice(atIndex, endIndex)\r\n      // 查找是否已存在相同的mention\r\n      const existingMention = mentionPositions.value.find(\r\n        (m) => m.text === mentionText && !newPositions.some((n) => n.id === m.id)\r\n      )\r\n      if (existingMention) {\r\n        // 如果存在，更新位置\r\n        newPositions.push({ ...existingMention, start: atIndex, end: endIndex - 1 })\r\n      }\r\n    }\r\n    currentIndex = endIndex + 1\r\n  }\r\n  // 更新位置信息\r\n  mentionPositions.value = newPositions\r\n}\r\n\r\nconst appendEmotion = (item) => {\r\n  const textarea = inputRef.value.$el.querySelector('textarea')\r\n  const cursorPosition = textarea.selectionStart\r\n  // 检查是否在@用户的位置\r\n  if (isInMention(cursorPosition)) return\r\n  if (!content.value) {\r\n    content.value = item.text\r\n  } else {\r\n    const start = content.value.slice(0, cursorPos.value)\r\n    const end = content.value.slice(cursorPos.value)\r\n    content.value = `${start}${item.text}${end}`\r\n    nextTick(() => {\r\n      inputRef.value.focus()\r\n      if (inputRef.value.$el.querySelector('textarea')) {\r\n        inputRef.value.$el\r\n          .querySelector('textarea')\r\n          .setSelectionRange(cursorPos.value + item.text.length, cursorPos.value + item.text.length)\r\n      }\r\n    })\r\n  }\r\n}\r\nconst handleBlur = (e) => {\r\n  cursorPos.value = e.srcElement.selectionStart\r\n}\r\n// 处理键盘事件\r\nconst handleKeyDown = (e) => {\r\n  if (e.keyCode == 13) {\r\n    if (!e.ctrlKey && !e.metaKey) {\r\n      // 处理回车发送\r\n      e.preventDefault()\r\n      handleSendMessage()\r\n    } else {\r\n      cursorPos.value = e.srcElement.selectionStart\r\n      appendEmotion({ text: '\\n' })\r\n    }\r\n  } else {\r\n    const textarea = inputRef.value.$el.querySelector('textarea')\r\n    const cursorPosition = textarea.selectionStart\r\n    if (e.key === 'Backspace') {\r\n      // 检查是否在@用户的位置或紧跟在@用户后面\r\n      for (let i = mentionPositions.value.length - 1; i >= 0; i--) {\r\n        const mention = mentionPositions.value[i]\r\n        if (cursorPosition > mention.start && cursorPosition <= mention.end + 1) {\r\n          e.preventDefault()\r\n          // 删除整个@用户（包括末尾空格）\r\n          const beforeText = content.value.slice(0, mention.start)\r\n          const afterText = content.value.slice(mention.end + 1)\r\n          content.value = beforeText + afterText\r\n          // 删除当前@用户的位置信息\r\n          mentionPositions.value.splice(i, 1)\r\n          // 更新后面的@用户的位置\r\n          const deleteLength = mention.end - mention.start + 2\r\n          for (let j = i; j < mentionPositions.value.length; j++) {\r\n            mentionPositions.value[j].start -= deleteLength\r\n            mentionPositions.value[j].end -= deleteLength\r\n          }\r\n          break\r\n        }\r\n      }\r\n    } else if (e.key !== 'ArrowLeft' && e.key !== 'ArrowRight' && e.key !== 'ArrowUp' && e.key !== 'ArrowDown') {\r\n      // 如果不是方向键，检查是否在@用户的位置\r\n      if (isInMention(cursorPosition)) e.preventDefault()\r\n    }\r\n  }\r\n}\r\n\r\n// 处理粘贴事件\r\nconst handlePaste = (e) => {\r\n  const clipboardData = event.clipboardData || window.clipboardData\r\n  if (clipboardData) {\r\n    for (let i = 0; i < clipboardData.items.length; i++) {\r\n      const item = clipboardData.items[i]\r\n      if (item.kind === 'file' && item.type?.includes('image/')) {\r\n        const file = item.getAsFile()\r\n        const reader = new FileReader()\r\n        reader.onload = (e) => {\r\n          emit('handlePasteImg', { id: guid(), url: e.target.result, file: file })\r\n        }\r\n        reader.readAsDataURL(file)\r\n      }\r\n    }\r\n  } else {\r\n    const textarea = inputRef.value.$el.querySelector('textarea')\r\n    const cursorPosition = textarea.selectionStart\r\n    // 检查是否在@用户的位置\r\n    if (isInMention(cursorPosition)) e.preventDefault()\r\n  }\r\n}\r\n\r\n// 插入@用户\r\nconst insertUser = (user) => {\r\n  const lastAtIndex = content.value.lastIndexOf('@')\r\n  if (lastAtIndex !== -1) {\r\n    const mentionText = `@${user.userName} `\r\n    const newMessage =\r\n      content.value.slice(0, lastAtIndex) + mentionText + content.value.slice(lastAtIndex + 1 + keyword.value.length)\r\n    // 先重置状态\r\n    show.value = false\r\n    keyword.value = ''\r\n    // 然后更新消息\r\n    content.value = newMessage\r\n    // 添加新的mention信息\r\n    mentionPositions.value.push({\r\n      id: ++mentionIdCounter, // 生成新的唯一ID\r\n      text: mentionText.trim(),\r\n      start: lastAtIndex,\r\n      end: lastAtIndex + mentionText.length - 2, // 不包含末尾空格\r\n      userInfo: user\r\n    })\r\n  }\r\n}\r\n// 发送消息\r\nconst getMessage = () => {\r\n  if (content.value.trim()) {\r\n    // 构建发送的消息对象\r\n    return {\r\n      content: content.value,\r\n      mentions: mentionPositions.value.map((m) => ({ id: m.id, userInfo: m.userInfo }))\r\n    }\r\n  }\r\n  return null\r\n}\r\nconst clearMessage = () => {\r\n  content.value = ''\r\n  mentionPositions.value = []\r\n  mentionIdCounter = 0 // 重置ID计数器\r\n}\r\n// 发送消息\r\nconst handleSendMessage = () => {\r\n  const messageData = getMessage()\r\n  if (messageData) {\r\n    emit('handleSendMessage', messageData)\r\n    clearMessage()\r\n  }\r\n}\r\n\r\n// 点击外部指令\r\nconst vClickOutside = {\r\n  mounted(el, binding) {\r\n    el._clickOutside = (event) => {\r\n      if (!(el === event.target || el.contains(event.target))) {\r\n        binding.value(event)\r\n      }\r\n    }\r\n    document.addEventListener('click', el._clickOutside)\r\n  },\r\n  unmounted(el) {\r\n    document.removeEventListener('click', el._clickOutside)\r\n  }\r\n}\r\ndefineExpose({ getMessage, clearMessage })\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalChatEditor {\r\n  width: 100%;\r\n  height: 166px;\r\n  padding: 12px 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  padding-bottom: calc(var(--zy-height-secondary) + 12px);\r\n  border-radius: var(--el-border-radius-base);\r\n  background: #fff;\r\n  position: relative;\r\n\r\n  .GlobalChatViewControls {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 var(--zy-distance-two);\r\n\r\n    & > div {\r\n      width: 22px;\r\n      height: 22px;\r\n      cursor: pointer;\r\n\r\n      & + div {\r\n        margin-left: 12px;\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewControlsItem {\r\n      width: 22px;\r\n      height: 22px;\r\n      cursor: pointer;\r\n\r\n      &.is-min {\r\n        .icon {\r\n          width: 21px;\r\n          height: 21px;\r\n        }\r\n      }\r\n\r\n      .icon {\r\n        width: 22px;\r\n        height: 22px;\r\n\r\n        path {\r\n          fill: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .zy-el-textarea__inner {\r\n    box-shadow: none !important;\r\n    padding: 0 var(--zy-distance-two);\r\n  }\r\n\r\n  .GlobalChatViewControlsBotton {\r\n    width: 100%;\r\n    height: var(--zy-height-secondary);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: 12px;\r\n    padding: 0 var(--zy-distance-two);\r\n\r\n    & > span {\r\n      font-size: 12px;\r\n      color: var(--zy-el-text-color-regular);\r\n      padding: 0 12px;\r\n    }\r\n\r\n    .zy-el-button {\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n\r\n  .GlobalChatUserPicker {\r\n    position: fixed;\r\n    background: #fff;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border-radius: var(--el-border-radius-base);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n    z-index: 2000;\r\n\r\n    .GlobalChatUserPickerList {\r\n      padding: 6px;\r\n\r\n      .GlobalChatUserPickerItem {\r\n        height: 32px;\r\n        line-height: 32px;\r\n        font-size: 12px;\r\n        padding: 0 12px;\r\n        cursor: pointer;\r\n        white-space: nowrap;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        border-radius: var(--el-border-radius-base);\r\n\r\n        &:hover {\r\n          color: var(--zy-el-color-primary);\r\n          background: var(--zy-el-color-primary-light-9);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatEmotionPopover {\r\n  width: 380px !important;\r\n  padding: 8px 0 !important;\r\n\r\n  .GlobalChatEmotionScroll {\r\n    width: 100%;\r\n    height: 360px;\r\n\r\n    .zy-el-scrollbar__view {\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .GlobalChatEmotion {\r\n    width: auto;\r\n    padding: 0 8px;\r\n    cursor: pointer;\r\n\r\n    .GlobalChatEmotionItem {\r\n      width: 38px;\r\n      height: 38px;\r\n      padding: 8px;\r\n\r\n      & > div {\r\n        width: 22px;\r\n        height: 22px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;AA2DA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,KAAK;AAC7C,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,SAAS,EAAEC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;;;;;;;;;;;;;;;;;;;IAC7E,IAAMC,KAAK,GAAGC,OAAwG;IACtH,IAAMC,IAAI,GAAGC,MAAgF;IAE7F,IAAMC,QAAQ,GAAGZ,GAAG,CAAC,CAAC;IACtB,IAAMa,OAAO,GAAGb,GAAG,CAAC,EAAE,CAAC;IAEvB,IAAMc,IAAI,GAAGd,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMe,OAAO,GAAGf,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMgB,SAAS,GAAGhB,GAAG,CAAC,CAAC,CAAC;IACxB,IAAMiB,YAAY,GAAGjB,GAAG,CAAC;MAAEkB,GAAG,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAM,CAAC,CAAC;IACrD;IACA,IAAMC,gBAAgB,GAAGpB,GAAG,CAAC,EAAE,CAAC;IAChC,IAAIqB,gBAAgB,GAAG,CAAC,EAAC;;IAEzB,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAACC,CAAC,EAAK;QACpE,IAAIC,CAAC,GAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9BC,CAAC,GAAGJ,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD;IACA,IAAMC,gBAAgB,GAAG7B,QAAQ,CAAC,YAAM;MACtC,IAAI,CAACc,OAAO,CAACgB,KAAK,EAAE,OAAOvB,KAAK,CAACwB,QAAQ;MACzC,OAAOxB,KAAK,CAACwB,QAAQ,CAACC,MAAM,CAAC,UAACC,IAAI;QAAA,IAAAC,cAAA,EAAAC,cAAA;QAAA,QAAAD,cAAA,GAAKD,IAAI,CAACG,QAAQ,cAAAF,cAAA,gBAAAA,cAAA,GAAbA,cAAA,CAAeG,WAAW,CAAC,CAAC,cAAAH,cAAA,uBAA5BA,cAAA,CAA8BI,QAAQ,EAAAH,cAAA,GAACrB,OAAO,CAACgB,KAAK,cAAAK,cAAA,uBAAbA,cAAA,CAAeE,WAAW,CAAC,CAAC,CAAC;MAAA,EAAC;IAC9G,CAAC,CAAC;IACF;IACA,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIC,QAAQ;MAAA,OAAKrB,gBAAgB,CAACW,KAAK,CAACW,IAAI,CAAC,UAACC,GAAG;QAAA,OAAKF,QAAQ,IAAIE,GAAG,CAACC,KAAK,IAAIH,QAAQ,IAAIE,GAAG,CAACE,GAAG,GAAG,CAAC;MAAA,EAAC;IAAA;IACxH;IACA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIL,QAAQ;MAAA,OACpCrB,gBAAgB,CAACW,KAAK,CAACgB,IAAI,CAAC,UAACJ,GAAG;QAAA,OAAKF,QAAQ,IAAIE,GAAG,CAACC,KAAK,IAAIH,QAAQ,IAAIE,GAAG,CAACE,GAAG;MAAA,EAAC;IAAA;IAEpF,IAAMG,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAMC,SAAS,GAAGC,SAAS,CAACD,SAAS,CAACX,WAAW,CAAC,CAAC;MACnD,OAAO,CAAAW,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEV,QAAQ,CAAC,WAAW,CAAC,MAAIU,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEV,QAAQ,CAAC,UAAU,CAAC;IAC5E,CAAC;IACD,IAAMY,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B1C,IAAI,CAAC,YAAY,EAAE0C,IAAI,CAAC;IAC1B,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB3C,IAAI,CAAC,YAAY,CAAC;IACpB,CAAC;IACD;IACA,IAAM4C,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3BxC,IAAI,CAACiB,KAAK,GAAG,KAAK;MAClBhB,OAAO,CAACgB,KAAK,GAAG,EAAE;IACpB,CAAC;;IAED;IACA,IAAMwB,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;MAChC,IAAI,CAAC3C,QAAQ,CAACmB,KAAK,EAAE;MACrB,IAAMyB,QAAQ,GAAG5C,QAAQ,CAACmB,KAAK,CAAC0B,GAAG,CAACC,aAAa,CAAC,UAAU,CAAC;MAC7D,IAAMC,IAAI,GAAGH,QAAQ,CAACzB,KAAK;MAC3B,IAAM6B,WAAW,GAAGD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC;MACzC,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACxB;MACA,IAAME,IAAI,GAAGN,QAAQ,CAACO,qBAAqB,CAAC,CAAC;MAC7C,IAAMC,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACV,QAAQ,CAAC;MACvD,IAAMW,UAAU,GAAGC,QAAQ,CAACJ,aAAa,CAACG,UAAU,CAAC;MACrD,IAAME,UAAU,GAAGD,QAAQ,CAACJ,aAAa,CAACK,UAAU,CAAC;MACrD,IAAMC,WAAW,GAAGF,QAAQ,CAACJ,aAAa,CAACM,WAAW,CAAC;MACvD;MACA,IAAMC,YAAY,GAAGZ,IAAI,CAACa,SAAS,CAAC,CAAC,EAAEZ,WAAW,CAAC;MACnD,IAAMa,KAAK,GAAGF,YAAY,CAACG,KAAK,CAAC,IAAI,CAAC;MACtC,IAAMC,gBAAgB,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC;MACzC;MACA,IAAMC,WAAW,GAAGJ,KAAK,CAACE,gBAAgB,CAAC;MAC3C,IAAMG,gBAAgB,GAAGD,WAAW,GAAGA,WAAW,CAACD,MAAM,GAAG,CAAC;MAC7D;MACA,IAAM1D,GAAG,GAAG4C,IAAI,CAAC5C,GAAG,GAAGmD,UAAU,GAAGM,gBAAgB,GAAGR,UAAU;MACjE,IAAMhD,IAAI,GAAG2C,IAAI,CAAC3C,IAAI,GAAGmD,WAAW,GAAGQ,gBAAgB,GAAG,CAAC,EAAC;MAC5D;MACA,IAAMC,cAAc,GAAG,EAAE,EAAC;MAC1B,IAAMC,SAAS,GAAG,GAAG,EAAC;MACtB,IAAMC,YAAY,GAAGvD,IAAI,CAACwD,GAAG,CAACpD,gBAAgB,CAACC,KAAK,CAAC6C,MAAM,GAAGG,cAAc,EAAEC,SAAS,CAAC,GAAG,EAAE;MAC7F;MACA/D,YAAY,CAACc,KAAK,GAAG;QACnBb,GAAG,EAAE,GAAGA,GAAG,GAAG+D,YAAY,GAAG,CAAC,IAAI;QAAE;QACpC9D,IAAI,EAAE,GAAGA,IAAI,IAAI;QACjBgE,MAAM,EAAE,GAAGF,YAAY,IAAI;QAAE;QAC7BD,SAAS,EAAE,MAAM,CAAC;MACpB,CAAC;IACH,CAAC;;IAED;IACA,IAAMI,WAAW,GAAG,SAAdA,WAAWA,CAAIrD,KAAK,EAAK;MAC7B,IAAI,CAACvB,KAAK,CAACwB,QAAQ,CAAC4C,MAAM,EAAE;MAC5B,IAAMpB,QAAQ,GAAG5C,QAAQ,CAACmB,KAAK,CAAC0B,GAAG,CAACC,aAAa,CAAC,UAAU,CAAC;MAC7D,IAAM2B,cAAc,GAAG7B,QAAQ,CAAC8B,cAAc;MAC9C;MACA,IAAI9C,WAAW,CAAC6C,cAAc,CAAC,EAAE;QAC/BxE,OAAO,CAACkB,KAAK,GAAGA,KAAK;QACrB;MACF;MACA,IAAMwD,QAAQ,GAAGxD,KAAK,CAACyD,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC,IAAID,QAAQ,KAAK,GAAG,EAAE;QACpBzE,IAAI,CAACiB,KAAK,GAAG,IAAI;QACjBhB,OAAO,CAACgB,KAAK,GAAG,EAAE;QAClB7B,QAAQ,CAAC,YAAM;UACbqD,mBAAmB,CAAC,CAAC;QACvB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIzC,IAAI,CAACiB,KAAK,EAAE;QACrB,IAAM6B,WAAW,GAAG7B,KAAK,CAAC8B,WAAW,CAAC,GAAG,CAAC;QAC1C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;UACtB7C,OAAO,CAACgB,KAAK,GAAGA,KAAK,CAACyD,KAAK,CAAC5B,WAAW,GAAG,CAAC,CAAC;UAC5C1D,QAAQ,CAAC,YAAM;YACbqD,mBAAmB,CAAC,CAAC;UACvB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLzC,IAAI,CAACiB,KAAK,GAAG,KAAK;QACpB;MACF;MACA;MACA0D,sBAAsB,CAAC1D,KAAK,CAAC;IAC/B,CAAC;;IAED;IACA,IAAM0D,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAI9B,IAAI,EAAK;MACvC;MACA,IAAM+B,YAAY,GAAG,EAAE;MACvB,IAAIC,YAAY,GAAG,CAAC;MAAA,IAAAC,KAAA,YAAAA,MAAA,EACe;QACjC,IAAMC,OAAO,GAAGlC,IAAI,CAACmC,OAAO,CAAC,GAAG,EAAEH,YAAY,CAAC;QAC/C,IAAIE,OAAO,KAAK,CAAC,CAAC;QAClB;QACA,IAAIE,QAAQ,GAAGF,OAAO,GAAG,CAAC;QAC1B,OAAOE,QAAQ,GAAGpC,IAAI,CAACiB,MAAM,IAAIjB,IAAI,CAACoC,QAAQ,CAAC,KAAK,GAAG,EAAE;UACvDA,QAAQ,EAAE;QACZ;QACA;QACA,IAAIA,QAAQ,GAAGF,OAAO,GAAG,CAAC,EAAE;UAC1B,IAAMG,WAAW,GAAGrC,IAAI,CAAC6B,KAAK,CAACK,OAAO,EAAEE,QAAQ,CAAC;UACjD;UACA,IAAME,eAAe,GAAG7E,gBAAgB,CAACW,KAAK,CAACgB,IAAI,CACjD,UAACmD,CAAC;YAAA,OAAKA,CAAC,CAACvC,IAAI,KAAKqC,WAAW,IAAI,CAACN,YAAY,CAAChD,IAAI,CAAC,UAACyD,CAAC;cAAA,OAAKA,CAAC,CAACC,EAAE,KAAKF,CAAC,CAACE,EAAE;YAAA,EAAC;UAAA,CAC3E,CAAC;UACD,IAAIH,eAAe,EAAE;YACnB;YACAP,YAAY,CAACW,IAAI,CAAAC,aAAA,CAAAA,aAAA,KAAML,eAAe;cAAErD,KAAK,EAAEiD,OAAO;cAAEhD,GAAG,EAAEkD,QAAQ,GAAG;YAAC,EAAE,CAAC;UAC9E;QACF;QACAJ,YAAY,GAAGI,QAAQ,GAAG,CAAC;MAC7B,CAAC;MArBD,OAAOJ,YAAY,GAAGhC,IAAI,CAACiB,MAAM;QAAA,IAAAgB,KAAA,IAEX;MAAK;MAoB3B;MACAxE,gBAAgB,CAACW,KAAK,GAAG2D,YAAY;IACvC,CAAC;IAED,IAAMa,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9B,IAAMhD,QAAQ,GAAG5C,QAAQ,CAACmB,KAAK,CAAC0B,GAAG,CAACC,aAAa,CAAC,UAAU,CAAC;MAC7D,IAAM2B,cAAc,GAAG7B,QAAQ,CAAC8B,cAAc;MAC9C;MACA,IAAI9C,WAAW,CAAC6C,cAAc,CAAC,EAAE;MACjC,IAAI,CAACxE,OAAO,CAACkB,KAAK,EAAE;QAClBlB,OAAO,CAACkB,KAAK,GAAGyE,IAAI,CAAC7C,IAAI;MAC3B,CAAC,MAAM;QACL,IAAMf,KAAK,GAAG/B,OAAO,CAACkB,KAAK,CAACyD,KAAK,CAAC,CAAC,EAAExE,SAAS,CAACe,KAAK,CAAC;QACrD,IAAMc,GAAG,GAAGhC,OAAO,CAACkB,KAAK,CAACyD,KAAK,CAACxE,SAAS,CAACe,KAAK,CAAC;QAChDlB,OAAO,CAACkB,KAAK,GAAG,GAAGa,KAAK,GAAG4D,IAAI,CAAC7C,IAAI,GAAGd,GAAG,EAAE;QAC5C3C,QAAQ,CAAC,YAAM;UACbU,QAAQ,CAACmB,KAAK,CAAC0E,KAAK,CAAC,CAAC;UACtB,IAAI7F,QAAQ,CAACmB,KAAK,CAAC0B,GAAG,CAACC,aAAa,CAAC,UAAU,CAAC,EAAE;YAChD9C,QAAQ,CAACmB,KAAK,CAAC0B,GAAG,CACfC,aAAa,CAAC,UAAU,CAAC,CACzBgD,iBAAiB,CAAC1F,SAAS,CAACe,KAAK,GAAGyE,IAAI,CAAC7C,IAAI,CAACiB,MAAM,EAAE5D,SAAS,CAACe,KAAK,GAAGyE,IAAI,CAAC7C,IAAI,CAACiB,MAAM,CAAC;UAC9F;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAM+B,UAAU,GAAG,SAAbA,UAAUA,CAAIC,CAAC,EAAK;MACxB5F,SAAS,CAACe,KAAK,GAAG6E,CAAC,CAACC,UAAU,CAACvB,cAAc;IAC/C,CAAC;IACD;IACA,IAAMwB,aAAa,GAAG,SAAhBA,aAAaA,CAAIF,CAAC,EAAK;MAC3B,IAAIA,CAAC,CAACG,OAAO,IAAI,EAAE,EAAE;QACnB,IAAI,CAACH,CAAC,CAACI,OAAO,IAAI,CAACJ,CAAC,CAACK,OAAO,EAAE;UAC5B;UACAL,CAAC,CAACM,cAAc,CAAC,CAAC;UAClBC,iBAAiB,CAAC,CAAC;QACrB,CAAC,MAAM;UACLnG,SAAS,CAACe,KAAK,GAAG6E,CAAC,CAACC,UAAU,CAACvB,cAAc;UAC7CiB,aAAa,CAAC;YAAE5C,IAAI,EAAE;UAAK,CAAC,CAAC;QAC/B;MACF,CAAC,MAAM;QACL,IAAMH,QAAQ,GAAG5C,QAAQ,CAACmB,KAAK,CAAC0B,GAAG,CAACC,aAAa,CAAC,UAAU,CAAC;QAC7D,IAAM2B,cAAc,GAAG7B,QAAQ,CAAC8B,cAAc;QAC9C,IAAIsB,CAAC,CAACQ,GAAG,KAAK,WAAW,EAAE;UACzB;UACA,KAAK,IAAIC,CAAC,GAAGjG,gBAAgB,CAACW,KAAK,CAAC6C,MAAM,GAAG,CAAC,EAAEyC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC3D,IAAMC,OAAO,GAAGlG,gBAAgB,CAACW,KAAK,CAACsF,CAAC,CAAC;YACzC,IAAIhC,cAAc,GAAGiC,OAAO,CAAC1E,KAAK,IAAIyC,cAAc,IAAIiC,OAAO,CAACzE,GAAG,GAAG,CAAC,EAAE;cACvE+D,CAAC,CAACM,cAAc,CAAC,CAAC;cAClB;cACA,IAAMK,UAAU,GAAG1G,OAAO,CAACkB,KAAK,CAACyD,KAAK,CAAC,CAAC,EAAE8B,OAAO,CAAC1E,KAAK,CAAC;cACxD,IAAM4E,SAAS,GAAG3G,OAAO,CAACkB,KAAK,CAACyD,KAAK,CAAC8B,OAAO,CAACzE,GAAG,GAAG,CAAC,CAAC;cACtDhC,OAAO,CAACkB,KAAK,GAAGwF,UAAU,GAAGC,SAAS;cACtC;cACApG,gBAAgB,CAACW,KAAK,CAAC0F,MAAM,CAACJ,CAAC,EAAE,CAAC,CAAC;cACnC;cACA,IAAMK,YAAY,GAAGJ,OAAO,CAACzE,GAAG,GAAGyE,OAAO,CAAC1E,KAAK,GAAG,CAAC;cACpD,KAAK,IAAI+E,CAAC,GAAGN,CAAC,EAAEM,CAAC,GAAGvG,gBAAgB,CAACW,KAAK,CAAC6C,MAAM,EAAE+C,CAAC,EAAE,EAAE;gBACtDvG,gBAAgB,CAACW,KAAK,CAAC4F,CAAC,CAAC,CAAC/E,KAAK,IAAI8E,YAAY;gBAC/CtG,gBAAgB,CAACW,KAAK,CAAC4F,CAAC,CAAC,CAAC9E,GAAG,IAAI6E,YAAY;cAC/C;cACA;YACF;UACF;QACF,CAAC,MAAM,IAAId,CAAC,CAACQ,GAAG,KAAK,WAAW,IAAIR,CAAC,CAACQ,GAAG,KAAK,YAAY,IAAIR,CAAC,CAACQ,GAAG,KAAK,SAAS,IAAIR,CAAC,CAACQ,GAAG,KAAK,WAAW,EAAE;UAC1G;UACA,IAAI5E,WAAW,CAAC6C,cAAc,CAAC,EAAEuB,CAAC,CAACM,cAAc,CAAC,CAAC;QACrD;MACF;IACF,CAAC;;IAED;IACA,IAAMU,WAAW,GAAG,SAAdA,WAAWA,CAAIhB,CAAC,EAAK;MACzB,IAAMiB,aAAa,GAAGC,KAAK,CAACD,aAAa,IAAI5D,MAAM,CAAC4D,aAAa;MACjE,IAAIA,aAAa,EAAE;QAAA,IAAAE,MAAA,YAAAA,OAAA,EACoC;UAAA,IAAAC,UAAA;UACnD,IAAMxB,IAAI,GAAGqB,aAAa,CAACI,KAAK,CAACZ,CAAC,CAAC;UACnC,IAAIb,IAAI,CAAC0B,IAAI,KAAK,MAAM,KAAAF,UAAA,GAAIxB,IAAI,CAAC2B,IAAI,cAAAH,UAAA,eAATA,UAAA,CAAWzF,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACzD,IAAMa,IAAI,GAAGoD,IAAI,CAAC4B,SAAS,CAAC,CAAC;YAC7B,IAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;YAC/BD,MAAM,CAACE,MAAM,GAAG,UAAC3B,CAAC,EAAK;cACrBlG,IAAI,CAAC,gBAAgB,EAAE;gBAAE0F,EAAE,EAAE9E,IAAI,CAAC,CAAC;gBAAEkH,GAAG,EAAE5B,CAAC,CAAC6B,MAAM,CAACC,MAAM;gBAAEtF,IAAI,EAAEA;cAAK,CAAC,CAAC;YAC1E,CAAC;YACDiF,MAAM,CAACM,aAAa,CAACvF,IAAI,CAAC;UAC5B;QACF,CAAC;QAVD,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,aAAa,CAACI,KAAK,CAACrD,MAAM,EAAEyC,CAAC,EAAE;UAAAU,MAAA;QAAA;MAWrD,CAAC,MAAM;QACL,IAAMvE,QAAQ,GAAG5C,QAAQ,CAACmB,KAAK,CAAC0B,GAAG,CAACC,aAAa,CAAC,UAAU,CAAC;QAC7D,IAAM2B,cAAc,GAAG7B,QAAQ,CAAC8B,cAAc;QAC9C;QACA,IAAI9C,WAAW,CAAC6C,cAAc,CAAC,EAAEuB,CAAC,CAACM,cAAc,CAAC,CAAC;MACrD;IACF,CAAC;;IAED;IACA,IAAM0B,UAAU,GAAG,SAAbA,UAAUA,CAAI1G,IAAI,EAAK;MAC3B,IAAM0B,WAAW,GAAG/C,OAAO,CAACkB,KAAK,CAAC8B,WAAW,CAAC,GAAG,CAAC;MAClD,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,IAAMoC,WAAW,GAAG,IAAI9D,IAAI,CAACG,QAAQ,GAAG;QACxC,IAAMwG,UAAU,GACdhI,OAAO,CAACkB,KAAK,CAACyD,KAAK,CAAC,CAAC,EAAE5B,WAAW,CAAC,GAAGoC,WAAW,GAAGnF,OAAO,CAACkB,KAAK,CAACyD,KAAK,CAAC5B,WAAW,GAAG,CAAC,GAAG7C,OAAO,CAACgB,KAAK,CAAC6C,MAAM,CAAC;QACjH;QACA9D,IAAI,CAACiB,KAAK,GAAG,KAAK;QAClBhB,OAAO,CAACgB,KAAK,GAAG,EAAE;QAClB;QACAlB,OAAO,CAACkB,KAAK,GAAG8G,UAAU;QAC1B;QACAzH,gBAAgB,CAACW,KAAK,CAACsE,IAAI,CAAC;UAC1BD,EAAE,EAAE,EAAE/E,gBAAgB;UAAE;UACxBsC,IAAI,EAAEqC,WAAW,CAAC8C,IAAI,CAAC,CAAC;UACxBlG,KAAK,EAAEgB,WAAW;UAClBf,GAAG,EAAEe,WAAW,GAAGoC,WAAW,CAACpB,MAAM,GAAG,CAAC;UAAE;UAC3CmE,QAAQ,EAAE7G;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IACD;IACA,IAAM8G,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAInI,OAAO,CAACkB,KAAK,CAAC+G,IAAI,CAAC,CAAC,EAAE;QACxB;QACA,OAAO;UACLjI,OAAO,EAAEA,OAAO,CAACkB,KAAK;UACtBkH,QAAQ,EAAE7H,gBAAgB,CAACW,KAAK,CAACmH,GAAG,CAAC,UAAChD,CAAC;YAAA,OAAM;cAAEE,EAAE,EAAEF,CAAC,CAACE,EAAE;cAAE2C,QAAQ,EAAE7C,CAAC,CAAC6C;YAAS,CAAC;UAAA,CAAC;QAClF,CAAC;MACH;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBtI,OAAO,CAACkB,KAAK,GAAG,EAAE;MAClBX,gBAAgB,CAACW,KAAK,GAAG,EAAE;MAC3BV,gBAAgB,GAAG,CAAC,EAAC;IACvB,CAAC;IACD;IACA,IAAM8F,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B,IAAMiC,WAAW,GAAGJ,UAAU,CAAC,CAAC;MAChC,IAAII,WAAW,EAAE;QACf1I,IAAI,CAAC,mBAAmB,EAAE0I,WAAW,CAAC;QACtCD,YAAY,CAAC,CAAC;MAChB;IACF,CAAC;;IAED;IACA,IAAME,aAAa,GAAG;MACpBC,OAAOA,CAACC,EAAE,EAAEC,OAAO,EAAE;QACnBD,EAAE,CAACE,aAAa,GAAG,UAAC3B,KAAK,EAAK;UAC5B,IAAI,EAAEyB,EAAE,KAAKzB,KAAK,CAACW,MAAM,IAAIc,EAAE,CAACG,QAAQ,CAAC5B,KAAK,CAACW,MAAM,CAAC,CAAC,EAAE;YACvDe,OAAO,CAACzH,KAAK,CAAC+F,KAAK,CAAC;UACtB;QACF,CAAC;QACD6B,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEL,EAAE,CAACE,aAAa,CAAC;MACtD,CAAC;MACDI,SAASA,CAACN,EAAE,EAAE;QACZI,QAAQ,CAACG,mBAAmB,CAAC,OAAO,EAAEP,EAAE,CAACE,aAAa,CAAC;MACzD;IACF,CAAC;IACDM,QAAY,CAAC;MAAEf,UAAU;MAAEG;IAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}