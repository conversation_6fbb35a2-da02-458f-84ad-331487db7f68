{"ast": null, "code": "import { computed } from 'vue';\nimport { useStore } from 'vuex';\nimport { WorkBenchElement } from './WorkBench';\nvar __default__ = {\n  name: 'WorkBench'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var elCode = computed(function () {\n      var getElCode = store.getters.getWorkBenchElement || 'WorkBenchOne';\n      return WorkBenchElement[getElCode] ? getElCode : 'WorkBenchOne';\n    });\n    var __returned__ = {\n      store,\n      elCode,\n      computed,\n      get useStore() {\n        return useStore;\n      },\n      get WorkBenchElement() {\n        return WorkBenchElement;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["computed", "useStore", "WorkBenchElement", "__default__", "name", "store", "elCode", "getElCode", "getters", "getWorkBenchElement"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/WorkBench/WorkBench.vue"], "sourcesContent": ["<template>\r\n  <component :is=\"WorkBenchElement[elCode]\"></component>\r\n</template>\r\n<script>\r\nexport default { name: 'WorkBench' }\r\n</script>\r\n<script setup>\r\nimport { computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { WorkBenchElement } from './WorkBench'\r\nconst store = useStore()\r\nconst elCode = computed(() => {\r\n  const getElCode = store.getters.getWorkBenchElement || 'WorkBenchOne'\r\n  return WorkBenchElement[getElCode] ? getElCode : 'WorkBenchOne'\r\n})\r\n</script>\r\n"], "mappings": "AAOA,SAASA,QAAQ,QAAQ,KAAK;AAC9B,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,gBAAgB,QAAQ,aAAa;AAL9C,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAY,CAAC;;;;;IAMpC,IAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC;IACxB,IAAMK,MAAM,GAAGN,QAAQ,CAAC,YAAM;MAC5B,IAAMO,SAAS,GAAGF,KAAK,CAACG,OAAO,CAACC,mBAAmB,IAAI,cAAc;MACrE,OAAOP,gBAAgB,CAACK,SAAS,CAAC,GAAGA,SAAS,GAAG,cAAc;IACjE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}