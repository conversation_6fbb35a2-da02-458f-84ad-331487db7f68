{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withKeys as _withKeys, normalizeStyle as _normalizeStyle } from \"vue\";\nimport _imports_0 from '../../img/intelligence.gif';\nimport _imports_1 from '../../img/delectMessage.png';\nimport _imports_2 from '../../img/delect.png';\nimport _imports_3 from '../../img/sending.png';\nvar _hoisted_1 = {\n  key: 0,\n  class: \"DouBao-intelligent-assistant\"\n};\nvar _hoisted_2 = {\n  class: \"DouBaoIntelligentizeBody\"\n};\nvar _hoisted_3 = {\n  key: 0,\n  class: \"DouBaoIntelligentizeBodyTop\"\n};\nvar _hoisted_4 = {\n  class: \"DouBaoIntelligentizeBodyLeft\"\n};\nvar _hoisted_5 = {\n  key: 0,\n  class: \"tableModuleContentKey\"\n};\nvar _hoisted_6 = {\n  class: \"tableModuleContentKeyWord\"\n};\nvar _hoisted_7 = {\n  class: \"tableModuleContentAnswer\"\n};\nvar _hoisted_8 = {\n  class: \"tableModuleItem\"\n};\nvar _hoisted_9 = {\n  key: 0,\n  class: \"tableModuleItemContent\"\n};\nvar _hoisted_10 = {\n  key: 0\n};\nvar _hoisted_11 = {\n  key: 1,\n  class: \"loader3\"\n};\nvar _hoisted_12 = [\"innerHTML\"];\nvar _hoisted_13 = [\"innerHTML\"];\nvar _hoisted_14 = {\n  key: 1,\n  class: \"loader3\"\n};\nvar _hoisted_15 = [\"onClick\"];\nvar _hoisted_16 = {\n  key: 2,\n  class: \"tableModuleContentAnswer\"\n};\nvar _hoisted_17 = {\n  class: \"tableModuleItem\"\n};\nvar _hoisted_18 = [\"innerHTML\"];\nvar _hoisted_19 = {\n  key: 0,\n  class: \"loader3\"\n};\nvar _hoisted_20 = {\n  key: 0,\n  class: \"tableModuleContentFollow\"\n};\nvar _hoisted_21 = [\"onClick\"];\nvar _hoisted_22 = {\n  key: 0,\n  class: \"wordShow\"\n};\nvar _hoisted_23 = {\n  class: \"DouBaoIntelligentizeSeach\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_Document = _resolveComponent(\"Document\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_Right = _resolveComponent(\"Right\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"DouBaoIntelligentize card\",\n    style: _normalizeStyle(`${!$props.typeShow ? 'border-radius: 0;' : ''}`)\n  }, [$props.typeShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_2, [!$props.typeShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[5] || (_cache[5] = _createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"\"\n  }, null, -1 /* HOISTED */)), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"LeftText\"\n  }, \"智能问答\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n    modelValue: $setup.selectTool,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.selectTool = $event;\n    }),\n    class: \"LeftSelect\",\n    placeholder: \"请选择\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.options, function (item) {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: item.value,\n          label: item.label,\n          value: item.value\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", {\n    class: \"DouBaoIntelligentizeBodyRight\"\n  }, [_createElementVNode(\"img\", {\n    onClick: $setup.clearTableData,\n    class: \"RightImg\",\n    src: _imports_1,\n    alt: \"\"\n  }), _createElementVNode(\"img\", {\n    onClick: $setup.checkOut,\n    src: _imports_2,\n    alt: \"\"\n  })])])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_scrollbar, {\n    ref: \"scrollbarRef\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableData, function (item) {\n        var _item$children;\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"tableModule\",\n          key: item.uid\n        }, [item.type === 'title' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 0\n        }, [item.contentKey ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(item.contentKey), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), item.type === 'answer' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 1\n        }, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [$setup.selectTool == 2 || $setup.selectTool == 3 && item.showKey1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [$setup.answerLoading && $setup.answerLoadingUid === item.uid ? (_openBlock(), _createElementBlock(\"span\", _hoisted_10, \"深度思考中\")) : _createCommentVNode(\"v-if\", true), $setup.answerLoading && $setup.answerLoadingUid === item.uid ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, _toConsumableArray(_cache[7] || (_cache[7] = [_createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */)])))) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", {\n          class: \"ContentText\",\n          innerHTML: item.contentKey1\n        }, null, 8 /* PROPS */, _hoisted_12)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", {\n          class: \"ContentTextTow\",\n          innerHTML: item.contentKey\n        }, null, 8 /* PROPS */, _hoisted_13), $setup.answerLoading && $setup.answerLoadingUid === item.uid ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, _toConsumableArray(_cache[8] || (_cache[8] = [_createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */)])))) : _createCommentVNode(\"v-if\", true), $setup.selectTool == 2 || $setup.selectTool == 3 ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 2\n        }, [item.copyShow && !$setup.answerLoading ? (_openBlock(), _createElementBlock(\"button\", {\n          key: 0,\n          class: \"copyButton\",\n          onClick: function onClick($event) {\n            return $setup.copyButtonFun(item);\n          }\n        }, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_Document)];\n          }),\n          _: 1 /* STABLE */\n        }), _cache[9] || (_cache[9] = _createTextVNode(\" 复制 \"))], 8 /* PROPS */, _hoisted_15)) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" <ul class=\\\"tableModuleContentAnswer\\\"\\r\\n                v-if=\\\"item.contentKey\\\">\\r\\n              <li class=\\\"tableModuleItem\\\">{{ item.contentKey }}</li>\\r\\n            </ul> \")]), _createCommentVNode(\" <ul class=\\\"tableModuleContentAnswer\\\"\\r\\n                v-if=\\\"item.children?.length\\\">\\r\\n              <li v-for=\\\"children in item.children\\\"\\r\\n                  :key=\\\"children.uid\\\"\\r\\n                  class=\\\"tableModuleItem\\\">\\r\\n                {{ children.output }}\\r\\n              </li>\\r\\n            </ul> \")], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), item.type === 'answerMin' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"span\", {\n          innerHTML: item.contentKey\n        }, null, 8 /* PROPS */, _hoisted_18), $setup.answerLoading && $setup.answerLoadingUid === item.uid ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, _toConsumableArray(_cache[10] || (_cache[10] = [_createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n          class: \"circle1\"\n        }, null, -1 /* HOISTED */)])))) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true), item.type === 'follow' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 3\n        }, [(_item$children = item.children) !== null && _item$children !== void 0 && _item$children.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.children, function (children) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            class: \"tableModuleContentFollowWord\",\n            key: children,\n            onClick: function onClick($event) {\n              return $setup.textClick(children);\n            }\n          }, [!$props.typeShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, \" # \")) : _createCommentVNode(\"v-if\", true), _createTextVNode(\" \" + _toDisplayString(children) + \" \", 1 /* TEXT */), _createVNode(_component_el_icon, null, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_Right)];\n            }),\n            _: 1 /* STABLE */\n          })], 8 /* PROPS */, _hoisted_21);\n        }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_23, [$props.typeShow ? (_openBlock(), _createBlock(_component_el_input, {\n    key: 0,\n    modelValue: $setup.keyword,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.keyword = $event;\n    }),\n    ref: \"textareaRef\",\n    type: \"textarea\",\n    autosize: {\n      minRows: 1,\n      maxRows: 6\n    },\n    onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n    resize: \"none\"\n  }, null, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true), !$props.typeShow ? (_openBlock(), _createBlock(_component_el_input, {\n    key: 1,\n    modelValue: $setup.keyword,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.keyword = $event;\n    }),\n    ref: \"textareaRef\",\n    type: \"textarea\",\n    maxlength: \"500\",\n    placeholder: \"请输入你的问题\",\n    rows: 3,\n    onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n    resize: \"none\"\n  }, null, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true), $props.typeShow ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 2,\n    type: \"primary\",\n    class: \"DouBaoIntelligentizeButton\",\n    icon: \"Position\",\n    circle: \"\",\n    onClick: _cache[3] || (_cache[3] = function ($event) {\n      return $setup.handleQuery();\n    })\n  })) : _createCommentVNode(\"v-if\", true), !$props.typeShow ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 3,\n    type: \"primary\",\n    class: \"sendingButton\",\n    circle: \"\",\n    onClick: _cache[4] || (_cache[4] = function ($event) {\n      return $setup.handleQuery();\n    })\n  }, {\n    default: _withCtx(function () {\n      return _cache[11] || (_cache[11] = [_createElementVNode(\"img\", {\n        src: _imports_3,\n        alt: \"\"\n      }, null, -1 /* HOISTED */), _createTextVNode(\" 发送\")]);\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)])], 4 /* STYLE */);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "_imports_3", "key", "class", "_createElementBlock", "style", "_normalizeStyle", "$props", "typeShow", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "src", "alt", "_createVNode", "_component_el_select", "modelValue", "$setup", "selectTool", "_cache", "$event", "placeholder", "default", "_withCtx", "_Fragment", "_renderList", "options", "item", "_createBlock", "_component_el_option", "value", "label", "_", "onClick", "clearTableData", "checkOut", "_component_el_scrollbar", "ref", "tableData", "_item$children", "uid", "type", "contentKey", "_hoisted_5", "_hoisted_6", "_toDisplayString", "_hoisted_7", "_hoisted_8", "showKey1", "_hoisted_9", "answerLoading", "answerLoadingUid", "_hoisted_10", "_hoisted_11", "_toConsumableArray", "innerHTML", "contentKey1", "_hoisted_12", "_hoisted_13", "_hoisted_14", "copyShow", "copyButtonFun", "_component_el_icon", "_component_Document", "_createTextVNode", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "children", "length", "_hoisted_20", "textClick", "_hoisted_22", "_component_Right", "_hoisted_21", "_hoisted_23", "_component_el_input", "keyword", "autosize", "minRows", "maxRows", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handleQuery", "resize", "maxlength", "rows", "_component_el_button", "icon", "circle"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\Intelligentize\\Intelligentize\\DouBaoIntelligentize.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DouBaoIntelligentize card\" :style=\"`${!typeShow ? 'border-radius: 0;' : ''}`\">\r\n    <div class=\"DouBao-intelligent-assistant\" v-if=\"typeShow\">\r\n    </div>\r\n\r\n    <div class=\"DouBaoIntelligentizeBody\">\r\n      <div class=\"DouBaoIntelligentizeBodyTop\" v-if=\"!typeShow\">\r\n        <div class=\"DouBaoIntelligentizeBodyLeft\">\r\n          <img src=\"../../img/intelligence.gif\" alt=\"\">\r\n          <div class=\"LeftText\">智能问答</div>\r\n          <el-select v-model=\"selectTool\" class=\"LeftSelect\" placeholder=\"请选择\">\r\n            <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"DouBaoIntelligentizeBodyRight\">\r\n          <img @click=\"clearTableData\" class=\"RightImg\" src=\"../../img/delectMessage.png\" alt=\"\">\r\n          <img @click=\"checkOut\" src=\"../../img/delect.png\" alt=\"\">\r\n        </div>\r\n      </div>\r\n      <el-scrollbar ref=\"scrollbarRef\">\r\n        <div class=\"tableModule\" v-for=\"item in tableData\" :key=\"item.uid\">\r\n          <template v-if=\"item.type === 'title'\">\r\n            <div class=\"tableModuleContentKey\" v-if=\"item.contentKey\">\r\n              <div class=\"tableModuleContentKeyWord\">{{ item.contentKey }}</div>\r\n            </div>\r\n          </template>\r\n          <template v-if=\"item.type === 'answer'\">\r\n            <div class=\"tableModuleContentAnswer\">\r\n              <div class=\"tableModuleItem\">\r\n                <div class=\"tableModuleItemContent\" v-if=\"selectTool == 2 || selectTool == 3 && item.showKey1\">\r\n                  <span v-if=\"answerLoading && (answerLoadingUid === item.uid)\">深度思考中</span>\r\n                  <div class=\"loader3\" v-if=\"answerLoading && (answerLoadingUid === item.uid)\">\r\n                    <div class=\"circle1\"></div>\r\n                    <div class=\"circle1\"></div>\r\n                    <div class=\"circle1\"></div>\r\n                  </div>\r\n                  <span class=\"ContentText\" v-html=\"item.contentKey1\"></span>\r\n                </div>\r\n                <span class=\"ContentTextTow\" v-html=\"item.contentKey\"></span>\r\n                <div class=\"loader3\" v-if=\"answerLoading && (answerLoadingUid === item.uid)\">\r\n                  <div class=\"circle1\"></div>\r\n                  <div class=\"circle1\"></div>\r\n                  <div class=\"circle1\"></div>\r\n                </div>\r\n                <template v-if=\"selectTool == 2 || selectTool == 3\">\r\n                  <button class=\"copyButton\" @click=\"copyButtonFun(item)\" v-if=\"item.copyShow && !answerLoading\">\r\n                    <el-icon>\r\n                      <Document />\r\n                    </el-icon>\r\n                    复制\r\n                  </button>\r\n                </template>\r\n              </div>\r\n              <!-- <ul class=\"tableModuleContentAnswer\"\r\n                v-if=\"item.contentKey\">\r\n              <li class=\"tableModuleItem\">{{ item.contentKey }}</li>\r\n            </ul> -->\r\n            </div>\r\n            <!-- <ul class=\"tableModuleContentAnswer\"\r\n                v-if=\"item.children?.length\">\r\n              <li v-for=\"children in item.children\"\r\n                  :key=\"children.uid\"\r\n                  class=\"tableModuleItem\">\r\n                {{ children.output }}\r\n              </li>\r\n            </ul> -->\r\n          </template>\r\n          <template v-if=\"item.type === 'answerMin'\">\r\n            <div class=\"tableModuleContentAnswer\">\r\n              <div class=\"tableModuleItem\">\r\n                <span v-html=\"item.contentKey\"></span>\r\n                <div class=\"loader3\" v-if=\"answerLoading && (answerLoadingUid === item.uid)\">\r\n                  <div class=\"circle1\"></div>\r\n                  <div class=\"circle1\"></div>\r\n                  <div class=\"circle1\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-if=\"item.type === 'follow'\">\r\n            <div class=\"tableModuleContentFollow\" v-if=\"item.children?.length\">\r\n              <div class=\"tableModuleContentFollowWord\" v-for=\"children in item.children\" :key=\"children\"\r\n                @click=\"textClick(children)\">\r\n                <div v-if=\"!typeShow\" class=\"wordShow\">\r\n                  #\r\n                </div>\r\n                {{ children }}\r\n                <el-icon>\r\n                  <Right />\r\n                </el-icon>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"DouBaoIntelligentizeSeach\">\r\n      <el-input v-model=\"keyword\" v-if=\"typeShow\" ref=\"textareaRef\" type=\"textarea\"\r\n        :autosize=\"{ minRows: 1, maxRows: 6 }\" @keyup.enter=\"handleQuery\" resize=\"none\" />\r\n      <el-input v-model=\"keyword\" ref=\"textareaRef\" v-if=\"!typeShow\" type=\"textarea\" maxlength=\"500\"\r\n        placeholder=\"请输入你的问题\" :rows=\"3\" @keyup.enter=\"handleQuery\" resize=\"none\" />\r\n      <el-button type=\"primary\" v-if=\"typeShow\" class=\"DouBaoIntelligentizeButton\" icon=\"Position\" circle\r\n        @click=\"handleQuery()\"></el-button>\r\n      <el-button type=\"primary\" v-if=\"!typeShow\" class=\"sendingButton\" circle @click=\"handleQuery()\"> <img\r\n          src=\"../../img/sending.png\" alt=\"\"> 发送</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DouBaoIntelligentize' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ElMessage } from 'element-plus'\r\nimport { openConfig } from 'common/js/system_var.js'\r\nimport config from \"common/config/index\"\r\nimport { ref, onMounted, nextTick, onUnmounted, defineEmits, computed } from 'vue'\r\nimport { fetchEventSource } from '@microsoft/fetch-event-source'\r\n// import { useStore } from 'vuex'\r\n// import { useRoute } from 'vue-router'\r\n// const route = useRoute()\r\n// const title = ref(route.query.title)\r\n// const details = ref({})\r\nconst props = defineProps({ keyword: { type: String, default: '' }, typeShow: { type: Boolean, default: '' }, showMessage: { type: String, default: '' } })\r\nconst keyword = ref('')\r\nconst textareaRef = ref()\r\nconst answerLoadingUid = ref('')\r\nconst emit = defineEmits(['checkOutFun'])\r\nconst answerLoading = ref(false)\r\nconst lock = ref(false)\r\nconst followUpLoadingUUid = ref('')\r\nconst tableData = ref([])\r\nconst initShow = ref('0')\r\nconst scrollbarRef = ref()\r\nconst selectTool = ref('1')\r\nconst systemPlatform = computed(() => openConfig.value?.systemPlatform || '')\r\nconst options = ref([{\r\n  value: '1',\r\n  label: 'coze'\r\n}, {\r\n  value: '2',\r\n  label: 'DeepSeek(满血)'\r\n}, {\r\n  value: '3',\r\n  label: 'DeepSeek(本地部署)'\r\n}]\r\n)\r\nvar ctrl = ref(new AbortController())\r\nonMounted(() => {\r\n  globalReadOpenConfig()\r\n})\r\nonUnmounted(() => {\r\n  // 退出页面执行 关闭链接\r\n})\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst globalReadOpenConfig = async () => {  // 没配置智能问答判断不调用接口\r\n  initShow.value = props.showMessage\r\n\r\n  nextTick(() => {\r\n    getFetchEventSource()\r\n  })\r\n}\r\nconst getFetchEventSource = async (text = '') => {\r\n  ctrl.value.abort()\r\n  ctrl.value = new AbortController(); // 创建新的控制器\r\n  const { signal } = ctrl.value;\r\n  const token = sessionStorage.getItem('token') || ''\r\n  if (lock.value == false && !props.typeShow && initShow.value == '1') {\r\n    tableData.value.push({ uid: answerUUid, copyShow: false, contentKey: '<span style=\"font-weight:bold;font-size:1.2em\">您好！我是您的小助手</span><br><span style=\"font-size:1em;color:#464646;\">您可以向我提出问题，我将为您做出内容推荐和解答。</span><br><span style=\"font-size:.9em;color:#a5a6a6;\">您可以试着问我：</span>', children: [], type: 'answer' })\r\n    const { data } = await api.initMessage({ bot_type: systemPlatform.value == 'NPC' ? '6' : '7' })\r\n    tableData.value.push({ uid: followUpUUid, copyShow: false, contentKey: '', children: data, type: 'follow' })\r\n    lock.value = true\r\n  }\r\n  var uuid = guid()\r\n  var answerUUid = guid()\r\n  var followUpUUid = guid()\r\n  tableData.value.push({ uid: uuid, contentKey: text, contentKey1: '', showKey1: true, children: [], type: 'title' })\r\n  tableData.value.push({ uid: answerUUid, contentKey: '', children: [], type: 'answer' })\r\n  tableData.value.push({ uid: followUpUUid, contentKey: '', children: [], type: 'follow' })\r\n  answerLoadingUid.value = answerUUid\r\n  followUpLoadingUUid.value = followUpUUid\r\n  answerLoading.value = true\r\n  tableData.value[tableData.value.length - 2].contentKey1 = ''\r\n  tableData.value[tableData.value.length - 2].showKey1 = true\r\n  await fetchEventSource(`${config.API_URL}${selectTool.value == 1 ? '/chat/intelligentStream' : selectTool.value == 2 ? '/chat/interactiveQnAChat' : '/chat/interactiveQnAEndpoint'}`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      authorization: token,\r\n    },\r\n    body: JSON.stringify({\r\n      content: text,\r\n      //  wordNumber: wordNumber.value || 200,\r\n      bot_type: selectTool.value == 1 ? props.typeShow ? '5' : systemPlatform.value == 'NPC' ? '7' : '6' : ''\r\n    }),\r\n    openWhenHidden: true, // 取消visibilityChange事件\r\n    signal,\r\n    onmessage: (res) => {\r\n      if (selectTool.value == 2 || selectTool.value == 3) {\r\n        if (JSON.parse(res.data).think && JSON.parse(res.data).think != '') {\r\n          tableData.value[tableData.value.length - 2].contentKey1 += (JSON.parse(res.data)?.think || '')\r\n        } else {\r\n          tableData.value[tableData.value.length - 2].contentKey += JSON.parse(res.data)?.response || ''\r\n          tableData.value[tableData.value.length - 2].contentKey = tableData.value[tableData.value.length - 2].contentKey.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n          tableData.value[tableData.value.length - 2].copyShow = true\r\n        }\r\n        nextTick(() => {\r\n          scrollDown()\r\n        })\r\n      } else {\r\n        var resData = JSON.parse(res.data)\r\n        if (['conversation.message.delta'].includes(resData.event) && ['answer'].includes(resData.data.type)) {\r\n          if (answerLoadingUid.value === answerUUid) {\r\n            tableData.value[tableData.value.length - 2].contentKey += JSON.parse(res.data)?.data?.content || ''\r\n            tableData.value[tableData.value.length - 2].contentKey = tableData.value[tableData.value.length - 2].contentKey.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n          }\r\n        } else if (['conversation.message.completed'].includes(resData.event) && ['follow_up'].includes(resData.data.type)) {\r\n          if (followUpLoadingUUid.value === followUpUUid) {\r\n            tableData.value[tableData.value.length - 1].children.push(JSON.parse(res.data)?.data?.content || '')\r\n          }\r\n        }\r\n        nextTick(() => { scrollDown() })\r\n      }\r\n    },\r\n    onclose: (data) => {\r\n      const chineseRegex = /[\\u4e00-\\u9fff]/;\r\n      let showKey1 = chineseRegex.test(tableData.value[tableData.value.length - 2].contentKey1)\r\n      tableData.value[tableData.value.length - 2].showKey1 = showKey1\r\n      answerLoading.value = false\r\n    },\r\n    onerror: (err) => {\r\n      tableData.value.push({ uid: answerUUid, contentKey: '系统繁忙。。。', children: [], type: 'answer' })\r\n      answerLoading.value = false\r\n      throw err\r\n    },\r\n  })\r\n}\r\nconst copyButtonFun = async (text) => {\r\n  try {\r\n    const originalText2 = text.contentKey.replace(/<\\/?think>/g, '')\r\n    const originalText = originalText2.replace(/<\\/?strong>/g, '')\r\n    await navigator.clipboard.writeText(originalText);\r\n    ElMessage({\r\n      message: '复制成功',\r\n      type: 'success',\r\n    })\r\n  } catch (err) {\r\n    // 作为备选方案，可以使用临时元素来复制文本\r\n    const el = document.createElement('textarea');\r\n    el.value = text.contentKey;\r\n    el.setAttribute('readonly', '');\r\n    el.style.position = 'absolute';\r\n    el.style.left = '-9999px';\r\n    document.body.appendChild(el);\r\n    el.select();\r\n    document.execCommand('copy');\r\n    document.body.removeChild(el);\r\n    ElMessage({\r\n      message: '复制成功',\r\n      type: 'success',\r\n    })\r\n  }\r\n}\r\nconst clearTableData = () => {\r\n  tableData.value = []\r\n}\r\nconst checkOut = () => {\r\n  emit('checkOutFun')\r\n}\r\n\r\n// const chatIntelligentAnswer = async (text = '') => {\r\n//   const { data, code } = await api.chatIntelligentAnswer({\r\n//     content: text,\r\n//     //  wordNumber: wordNumber.value || 200,\r\n//     bot_type: '5'\r\n//   })\r\n//   tableData.value.push({ type: 'answer', contentKey: '', uid: guid(), children: data?.filter(v => ['answer'].includes(v.type))?.map(v => ({ ...v, uid: guid() })) || [] })\r\n//   tableData.value.push({ type: 'follow', contentKey: '', uid: guid(), children: data?.filter(v => ['follow'].includes(v.type))?.map(v => ({ ...v, uid: guid() })) || [] })\r\n//   nextTick(() => { scrollDown() })\r\n// }\r\nconst scrollDown = async () => {\r\n  const wrap = scrollbarRef.value.wrapRef\r\n  wrap.scrollTop = wrap.scrollHeight\r\n  // 等待渲染更新\r\n  await nextTick();\r\n}\r\nconst handleQuery = () => {\r\n  if (keyword.value == '') return\r\n  answerLoading.value = false\r\n  ctrl.value.abort();\r\n  ctrl.value = new AbortController(); // 重置以备下次使用\r\n  if (keyword.value) {\r\n    textClick(keyword.value)\r\n  }\r\n}\r\nconst textClick = (text = '') => {\r\n  keyword.value = ''\r\n  ctrl.value.abort()\r\n  nextTick(() => { scrollDown() })\r\n  getFetchEventSource(text)\r\n}\r\n\r\ndefineExpose({ handleQuery, textClick })\r\n</script>\r\n<style lang=\"scss\">\r\n.DouBaoIntelligentize {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 20px;\r\n  box-shadow: var(--zy-el-box-shadow);\r\n  background-color: rgb(249, 250, 251);\r\n  border: 2px solid var(--zy-el-border-color-lighter);\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n\r\n  .DouBao-intelligent-assistant {\r\n    position: absolute;\r\n    top: -44px;\r\n    right: 10px;\r\n    width: 40px;\r\n    height: 40px;\r\n\r\n    .zy-el-image {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .DouBaoIntelligentizeBody {\r\n    padding: 20px 10px 20px 20px;\r\n    width: 100%;\r\n    // border: 1px solid salmon;\r\n    height: 100%;\r\n    align-items: center;\r\n    display: flex;\r\n    flex: 1 1;\r\n    flex-direction: column;\r\n    height: 100%;\r\n    overflow: hidden;\r\n    width: 100%;\r\n\r\n    .zy-el-scrollbar {\r\n      width: 100%;\r\n    }\r\n\r\n    .zy-el-scrollbar__view {\r\n      padding: 0 !important;\r\n    }\r\n\r\n    .tableModule {\r\n      padding: 10px 0;\r\n    }\r\n\r\n    .tableModuleContentKey {\r\n      margin: 10px 0;\r\n      padding: 0 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: flex-end;\r\n      width: 100%;\r\n\r\n      .tableModuleContentKeyWord {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 8px 10px;\r\n        border-radius: 8px;\r\n        overflow-anchor: auto;\r\n        flex-direction: row-reverse;\r\n        background-color: rgba(0, 0, 0, .04);\r\n        white-space: pre-wrap;\r\n        word-wrap: break-word;\r\n        // height: -webkit-fit-content;\r\n        // height: -moz-fit-content;\r\n        // height: fit-content;\r\n      }\r\n    }\r\n\r\n    .tableModuleContentFollow {\r\n      padding-right: 20px;\r\n      margin: 10px 0;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      width: 100%;\r\n\r\n      .tableModuleContentFollowWord {\r\n        cursor: pointer;\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 8px 10px;\r\n        border-radius: 8px;\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: rgba(0, 0, 0, .04);\r\n        margin-bottom: 10px;\r\n\r\n        &:hover {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n\r\n        .wordShow {\r\n          color: var(--zy-el-color-primary);\r\n          font-weight: bold;\r\n          margin-right: 5px;\r\n        }\r\n\r\n        .zy-el-icon {\r\n          margin-left: 5px;\r\n          width: var(--zy-name-font-size);\r\n          height: var(--zy-name-font-size);\r\n\r\n          svg {\r\n            width: var(--zy-name-font-size);\r\n            height: var(--zy-name-font-size);\r\n          }\r\n        }\r\n\r\n        // height: -webkit-fit-content;\r\n        // height: -moz-fit-content;\r\n        // height: fit-content;\r\n      }\r\n    }\r\n\r\n    .tableModuleContentAnswer {\r\n      // padding: 0 20px;\r\n      padding-right: 20px;\r\n      white-space: pre-wrap;\r\n      word-wrap: break-word;\r\n    }\r\n\r\n    .tableModuleItem {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      // margin-bottom: var(--zy-font-name-distance-five);\r\n      margin-bottom: 50px;\r\n\r\n\r\n      .copyButton {\r\n        display: block;\r\n        padding: 5px;\r\n        border: 0;\r\n        border-radius: 5px;\r\n        opacity: .5;\r\n        margin-top: 10px;\r\n\r\n        &:active {\r\n          opacity: .8;\r\n        }\r\n      }\r\n\r\n      .tableModuleItemContent {\r\n        border-left: 1px solid var(--zy-el-border-color);\r\n        padding-left: 10px;\r\n\r\n        .ContentText {\r\n          font-size: 1em;\r\n          opacity: .8;\r\n          color: var(zy-el-text-color-secondary);\r\n        }\r\n      }\r\n\r\n      .ContentTextTow {\r\n        font-size: 1.2em;\r\n        margin-top: -20px;\r\n      }\r\n\r\n      // padding-left: 20px;\r\n    }\r\n\r\n    .tableModuleContentAnswer {\r\n\r\n      /* From Uiverse.io by Satwinder04 */\r\n\r\n\r\n      .circle1 {\r\n        width: 4px;\r\n        height: 4px;\r\n        border-radius: 50%;\r\n        margin: 0 5px;\r\n        background-color: #333;\r\n        animation: circle1 1s ease-in-out infinite;\r\n      }\r\n\r\n      .circle1:nth-child(2) {\r\n        animation-delay: 0.33s;\r\n      }\r\n\r\n      .circle1:nth-child(3) {\r\n        animation-delay: 0.67s;\r\n      }\r\n\r\n      .circle1:nth-child(4) {\r\n        animation-delay: 0.6s;\r\n      }\r\n\r\n      .circle1:nth-child(5) {\r\n        animation-delay: 0.8s;\r\n      }\r\n\r\n      @keyframes circle1 {\r\n        0% {\r\n          transform: scale(1);\r\n          opacity: 1;\r\n        }\r\n\r\n        50% {\r\n          transform: scale(1.5);\r\n          opacity: 0.5;\r\n        }\r\n\r\n        100% {\r\n          transform: scale(1);\r\n          opacity: 1;\r\n        }\r\n      }\r\n\r\n    }\r\n  }\r\n\r\n  .DouBaoIntelligentizeSeach {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    flex-shrink: 0;\r\n    height: -webkit-fit-content;\r\n    height: -moz-fit-content;\r\n    height: fit-content;\r\n    width: 100%;\r\n    position: relative;\r\n\r\n    .zy-el-textarea__inner {\r\n      padding-right: 50px;\r\n      min-height: 36px !important;\r\n      border-top-left-radius: 0;\r\n      border-top-right-radius: 0;\r\n      border-bottom-left-radius: 16px;\r\n      border-bottom-right-radius: 16px;\r\n    }\r\n\r\n    .DouBaoIntelligentizeInputCarpetRows {\r\n      width: 100%;\r\n    }\r\n\r\n    .DouBaoIntelligentizeCarpetButton {\r\n      width: 100%;\r\n    }\r\n\r\n    .DouBaoIntelligentizeButton {\r\n      position: absolute;\r\n      content: \"\";\r\n      bottom: 0;\r\n      right: 10px;\r\n      width: 40px;\r\n    }\r\n\r\n    .sendingButton {\r\n      position: absolute;\r\n      content: \"\";\r\n      bottom: 5px;\r\n      right: 10px;\r\n      width: 55px;\r\n      font-size: 12px;\r\n      height: 25px;\r\n      border-radius: 20px;\r\n\r\n\r\n      img {\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 首页智能问答定制样式\r\n.DouBaoIntelligentizeBodyTop {\r\n  width: 100%;\r\n  margin-top: -10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .DouBaoIntelligentizeBodyLeft {\r\n    display: flex;\r\n    width: 60%;\r\n\r\n    img {\r\n      width: 30px;\r\n      height: 30px;\r\n    }\r\n\r\n    .LeftText {\r\n      margin-left: 5px;\r\n      white-space: nowrap;\r\n    }\r\n\r\n    .LeftSelect {\r\n      width: 200px;\r\n      height: 30px;\r\n      transform: translate(20px, -5px);\r\n    }\r\n  }\r\n\r\n  .DouBaoIntelligentizeBodyRight {\r\n    .RightImg {\r\n      width: 20px;\r\n      height: 20px;\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.loader3 {\r\n  display: inline-flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;OAQeA,UAAgC;OAQSC,UAAiC;OACxDC,UAA0B;OAwFjDC,UAA2B;;EAzGrCC,GAAA;EAESC,KAAK,EAAC;;;EAGNA,KAAK,EAAC;AAA0B;;EALzCD,GAAA;EAMWC,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA8B;;EAPjDD,GAAA;EAuBiBC,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA2B;;EAInCA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAiB;;EA7B1CD,GAAA;EA8BqBC,KAAK,EAAC;;;EA9B3BD,GAAA;AAAA;;EAAAA,GAAA;EAgCuBC,KAAK,EAAC;;kBAhC7B;kBAAA;;EAAAD,GAAA;EAwCqBC,KAAK,EAAC;;kBAxC3B;;EAAAD,GAAA;EAqEiBC,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAiB;kBAtE1C;;EAAAD,GAAA;EAwEqBC,KAAK,EAAC;;;EAxE3BD,GAAA;EAiFiBC,KAAK,EAAC;;kBAjFvB;;EAAAD,GAAA;EAoFsCC,KAAK,EAAC;;;EAanCA,KAAK,EAAC;AAA2B;;;;;;;;;;uBAhGxCC,mBAAA,CA0GM;IA1GDD,KAAK,EAAC,2BAA2B;IAAEE,KAAK,EAD/CC,eAAA,KACqDC,MAAA,CAAAC,QAAQ;MACTD,MAAA,CAAAC,QAAQ,I,cAAxDJ,mBAAA,CACM,OADNK,UACM,KAHVC,mBAAA,gBAKIC,mBAAA,CA2FM,OA3FNC,UA2FM,G,CA1F4CL,MAAA,CAAAC,QAAQ,I,cAAxDJ,mBAAA,CAaM,OAbNS,UAaM,GAZJF,mBAAA,CAOM,OAPNG,UAOM,G,0BANJH,mBAAA,CAA6C;IAAxCI,GAAgC,EAAhCjB,UAAgC;IAACkB,GAAG,EAAC;yDAC1CL,mBAAA,CAAgC;IAA3BR,KAAK,EAAC;EAAU,GAAC,MAAI,sBAC1Bc,YAAA,CAGYC,oBAAA;IAbtBC,UAAA,EAU8BC,MAAA,CAAAC,UAAU;IAVxC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAU8BH,MAAA,CAAAC,UAAU,GAAAE,MAAA;IAAA;IAAEpB,KAAK,EAAC,YAAY;IAACqB,WAAW,EAAC;;IAVzEC,OAAA,EAAAC,QAAA,CAWuB;MAAA,OAAuB,E,kBAAlCtB,mBAAA,CACYuB,SAAA,QAZxBC,WAAA,CAWsCR,MAAA,CAAAS,OAAO,EAX7C,UAW8BC,IAAI;6BAAtBC,YAAA,CACYC,oBAAA;UADwB9B,GAAG,EAAE4B,IAAI,CAACG,KAAK;UAAGC,KAAK,EAAEJ,IAAI,CAACI,KAAK;UAAGD,KAAK,EAAEH,IAAI,CAACG;;;;IAXlGE,CAAA;uCAeQxB,mBAAA,CAGM;IAHDR,KAAK,EAAC;EAA+B,IACxCQ,mBAAA,CAAuF;IAAjFyB,OAAK,EAAEhB,MAAA,CAAAiB,cAAc;IAAElC,KAAK,EAAC,UAAU;IAACY,GAAiC,EAAjChB,UAAiC;IAACiB,GAAG,EAAC;MACpFL,mBAAA,CAAyD;IAAnDyB,OAAK,EAAEhB,MAAA,CAAAkB,QAAQ;IAAEvB,GAA0B,EAA1Bf,UAA0B;IAACgB,GAAG,EAAC;YAjBhEN,mBAAA,gBAoBMO,YAAA,CA2EesB,uBAAA;IA3EDC,GAAG,EAAC;EAAc;IApBtCf,OAAA,EAAAC,QAAA,CAqBiC;MAAA,OAAyB,E,kBAAlDtB,mBAAA,CAyEMuB,SAAA,QA9FdC,WAAA,CAqBgDR,MAAA,CAAAqB,SAAS,EArBzD,UAqBwCX,IAAI;QAAA,IAAAY,cAAA;6BAApCtC,mBAAA,CAyEM;UAzEDD,KAAK,EAAC,aAAa;UAA4BD,GAAG,EAAE4B,IAAI,CAACa;YAC5Cb,IAAI,CAACc,IAAI,gB,cAAzBxC,mBAAA,CAIWuB,SAAA;UA1BrBzB,GAAA;QAAA,IAuBqD4B,IAAI,CAACe,UAAU,I,cAAxDzC,mBAAA,CAEM,OAFN0C,UAEM,GADJnC,mBAAA,CAAkE,OAAlEoC,UAAkE,EAAAC,gBAAA,CAAxBlB,IAAI,CAACe,UAAU,iB,KAxBvEnC,mBAAA,e,+BAAAA,mBAAA,gBA2B0BoB,IAAI,CAACc,IAAI,iB,cAAzBxC,mBAAA,CAwCWuB,SAAA;UAnErBzB,GAAA;QAAA,IA4BYS,mBAAA,CA8BM,OA9BNsC,UA8BM,GA7BJtC,mBAAA,CAwBM,OAxBNuC,UAwBM,GAvBsC9B,MAAA,CAAAC,UAAU,SAASD,MAAA,CAAAC,UAAU,SAASS,IAAI,CAACqB,QAAQ,I,cAA7F/C,mBAAA,CAQM,OARNgD,UAQM,GAPQhC,MAAA,CAAAiC,aAAa,IAAKjC,MAAA,CAAAkC,gBAAgB,KAAKxB,IAAI,CAACa,GAAG,I,cAA3DvC,mBAAA,CAA0E,QA/B5FmD,WAAA,EA+BgF,OAAK,KA/BrF7C,mBAAA,gBAgC6CU,MAAA,CAAAiC,aAAa,IAAKjC,MAAA,CAAAkC,gBAAgB,KAAKxB,IAAI,CAACa,GAAG,I,cAA1EvC,mBAAA,CAIM,OAJNoD,WAIM,EAAAC,kBAAA,CAAAnC,MAAA,QAAAA,MAAA,OAHJX,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,4BACpBQ,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,4BACpBQ,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,2B,OAnCxCO,mBAAA,gBAqCkBC,mBAAA,CAA2D;UAArDR,KAAK,EAAC,aAAa;UAACuD,SAAyB,EAAjB5B,IAAI,CAAC6B;gCArCzDC,WAAA,E,KAAAlD,mBAAA,gBAuCgBC,mBAAA,CAA6D;UAAvDR,KAAK,EAAC,gBAAgB;UAACuD,SAAwB,EAAhB5B,IAAI,CAACe;gCAvC1DgB,WAAA,GAwC2CzC,MAAA,CAAAiC,aAAa,IAAKjC,MAAA,CAAAkC,gBAAgB,KAAKxB,IAAI,CAACa,GAAG,I,cAA1EvC,mBAAA,CAIM,OAJN0D,WAIM,EAAAL,kBAAA,CAAAnC,MAAA,QAAAA,MAAA,OAHJX,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,4BACpBQ,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,4BACpBQ,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,2B,OA3CtCO,mBAAA,gBA6CgCU,MAAA,CAAAC,UAAU,SAASD,MAAA,CAAAC,UAAU,S,cAA7CjB,mBAAA,CAOWuB,SAAA;UApD3BzB,GAAA;QAAA,IA8CgF4B,IAAI,CAACiC,QAAQ,KAAK3C,MAAA,CAAAiC,aAAa,I,cAA7FjD,mBAAA,CAKS;UAnD3BF,GAAA;UA8C0BC,KAAK,EAAC,YAAY;UAAEiC,OAAK,WAALA,OAAKA,CAAAb,MAAA;YAAA,OAAEH,MAAA,CAAA4C,aAAa,CAAClC,IAAI;UAAA;YACnDb,YAAA,CAEUgD,kBAAA;UAjD9BxC,OAAA,EAAAC,QAAA,CAgDsB;YAAA,OAAY,CAAZT,YAAA,CAAYiD,mBAAA,E;;UAhDlC/B,CAAA;sCAAAgC,gBAAA,CAiD8B,MAEZ,G,iBAnDlBC,WAAA,KAAA1D,mBAAA,e,+BAAAA,mBAAA,e,GAsDcA,mBAAA,wLAGO,C,GAETA,mBAAA,wUAOS,C,+BAlErBA,mBAAA,gBAoE0BoB,IAAI,CAACc,IAAI,oB,cACvBxC,mBAAA,CASM,OATNiE,WASM,GARJ1D,mBAAA,CAOM,OAPN2D,WAOM,GANJ3D,mBAAA,CAAsC;UAAhC+C,SAAwB,EAAhB5B,IAAI,CAACe;gCAvEnC0B,WAAA,GAwE2CnD,MAAA,CAAAiC,aAAa,IAAKjC,MAAA,CAAAkC,gBAAgB,KAAKxB,IAAI,CAACa,GAAG,I,cAA1EvC,mBAAA,CAIM,OAJNoE,WAIM,EAAAf,kBAAA,CAAAnC,MAAA,SAAAA,MAAA,QAHJX,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,4BACpBQ,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,4BACpBQ,mBAAA,CAA2B;UAAtBR,KAAK,EAAC;QAAS,2B,OA3EtCO,mBAAA,e,OAAAA,mBAAA,gBAgF0BoB,IAAI,CAACc,IAAI,iB,cAAzBxC,mBAAA,CAaWuB,SAAA;UA7FrBzB,GAAA;QAAA,I,kBAiFwD4B,IAAI,CAAC2C,QAAQ,cAAA/B,cAAA,eAAbA,cAAA,CAAegC,MAAM,I,cAAjEtE,mBAAA,CAWM,OAXNuE,WAWM,I,kBAVJvE,mBAAA,CASMuB,SAAA,QA3FpBC,WAAA,CAkF2EE,IAAI,CAAC2C,QAAQ,EAlFxF,UAkF+DA,QAAQ;+BAAzDrE,mBAAA,CASM;YATDD,KAAK,EAAC,8BAA8B;YAAoCD,GAAG,EAAEuE,QAAQ;YACvFrC,OAAK,WAALA,OAAKA,CAAAb,MAAA;cAAA,OAAEH,MAAA,CAAAwD,SAAS,CAACH,QAAQ;YAAA;eACdlE,MAAA,CAAAC,QAAQ,I,cAApBJ,mBAAA,CAEM,OAFNyE,WAEM,EAFiC,KAEvC,KAtFhBnE,mBAAA,gBAAAyD,gBAAA,CAsFsB,GACN,GAAAnB,gBAAA,CAAGyB,QAAQ,IAAG,GACd,iBAAAxD,YAAA,CAEUgD,kBAAA;YA1F1BxC,OAAA,EAAAC,QAAA,CAyFkB;cAAA,OAAS,CAATT,YAAA,CAAS6D,gBAAA,E;;YAzF3B3C,CAAA;8BAAA4C,WAAA;4CAAArE,mBAAA,e,+BAAAA,mBAAA,e;;;IAAAyB,CAAA;8BAiGIxB,mBAAA,CASM,OATNqE,WASM,GAR8BzE,MAAA,CAAAC,QAAQ,I,cAA1CuB,YAAA,CACoFkD,mBAAA;IAnG1F/E,GAAA;IAAAiB,UAAA,EAkGyBC,MAAA,CAAA8D,OAAO;IAlGhC,uBAAA5D,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAkGyBH,MAAA,CAAA8D,OAAO,GAAA3D,MAAA;IAAA;IAAkBiB,GAAG,EAAC,aAAa;IAACI,IAAI,EAAC,UAAU;IAC1EuC,QAAQ,EAAE;MAAAC,OAAA;MAAAC,OAAA;IAAA,CAA0B;IAAGC,OAAK,EAnGrDC,SAAA,CAmG6DnE,MAAA,CAAAoE,WAAW;IAAEC,MAAM,EAAC;6CAnGjF/E,mBAAA,gB,CAoG2DH,MAAA,CAAAC,QAAQ,I,cAA7DuB,YAAA,CAC6EkD,mBAAA;IArGnF/E,GAAA;IAAAiB,UAAA,EAoGyBC,MAAA,CAAA8D,OAAO;IApGhC,uBAAA5D,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoGyBH,MAAA,CAAA8D,OAAO,GAAA3D,MAAA;IAAA;IAAEiB,GAAG,EAAC,aAAa;IAAkBI,IAAI,EAAC,UAAU;IAAC8C,SAAS,EAAC,KAAK;IAC5FlE,WAAW,EAAC,SAAS;IAAEmE,IAAI,EAAE,CAAC;IAAGL,OAAK,EArG9CC,SAAA,CAqGsDnE,MAAA,CAAAoE,WAAW;IAAEC,MAAM,EAAC;6CArG1E/E,mBAAA,gBAsGsCH,MAAA,CAAAC,QAAQ,I,cAAxCuB,YAAA,CACqC6D,oBAAA;IAvG3C1F,GAAA;IAsGiB0C,IAAI,EAAC,SAAS;IAAiBzC,KAAK,EAAC,4BAA4B;IAAC0F,IAAI,EAAC,UAAU;IAACC,MAAM,EAAN,EAAM;IAChG1D,OAAK,EAAAd,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEH,MAAA,CAAAoE,WAAW;IAAA;QAvG3B9E,mBAAA,gB,CAwGuCH,MAAA,CAAAC,QAAQ,I,cAAzCuB,YAAA,CACsD6D,oBAAA;IAzG5D1F,GAAA;IAwGiB0C,IAAI,EAAC,SAAS;IAAkBzC,KAAK,EAAC,eAAe;IAAC2F,MAAM,EAAN,EAAM;IAAE1D,OAAK,EAAAd,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEH,MAAA,CAAAoE,WAAW;IAAA;;IAxGjG/D,OAAA,EAAAC,QAAA,CAwGsG;MAAA,OACzDJ,MAAA,SAAAA,MAAA,QADyDX,mBAAA,CACzD;QAAnCI,GAA2B,EAA3Bd,UAA2B;QAACe,GAAG,EAAC;kCAzG1CmD,gBAAA,CAyG6C,KAAG,E;;IAzGhDhC,CAAA;QAAAzB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}