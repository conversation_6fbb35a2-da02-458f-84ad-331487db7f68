{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nimport _imports_0 from '../img/AiIcon3.png';\nimport _imports_1 from '../img/AiIcon2.png';\nimport _imports_2 from '../img/AiIcon1.png';\nimport _imports_3 from '../img/tongjiIcon.png';\nvar _hoisted_1 = {\n  class: \"AiUseStatistics-header\"\n};\nvar _hoisted_2 = {\n  class: \"AiUseStatistics-header-box\"\n};\nvar _hoisted_3 = {\n  class: \"AiUseStatistics-header-content\"\n};\nvar _hoisted_4 = {\n  class: \"AiUseStatistics-header-content-item\"\n};\nvar _hoisted_5 = {\n  class: \"AiUseStatistics-header-content-item-left\"\n};\nvar _hoisted_6 = {\n  class: \"AiUseStatistics-header-content-item-left-num\"\n};\nvar _hoisted_7 = {\n  class: \"AiUseStatistics-header-content-item\"\n};\nvar _hoisted_8 = {\n  class: \"AiUseStatistics-header-content-item-left\"\n};\nvar _hoisted_9 = {\n  class: \"AiUseStatistics-header-content-item-left-num\"\n};\nvar _hoisted_10 = {\n  class: \"AiUseStatistics-header-content-item\"\n};\nvar _hoisted_11 = {\n  class: \"AiUseStatistics-header-content-item-left\"\n};\nvar _hoisted_12 = {\n  class: \"AiUseStatistics-header-content-item-left-num\"\n};\nvar _hoisted_13 = {\n  class: \"AiUseStatistics-chart\"\n};\nvar _hoisted_14 = {\n  class: \"AiUseStatistics-chart-timeSelect\"\n};\nvar _hoisted_15 = {\n  class: \"AiUseStatistics-chart-content\"\n};\nvar _hoisted_16 = {\n  class: \"AiUseStatistics-chart-content-left\"\n};\nvar _hoisted_17 = {\n  class: \"AiUseStatistics-chart-content-right-title\"\n};\nvar _hoisted_18 = {\n  class: \"AiUseStatistics-chart-content-right-title-right\"\n};\nvar _hoisted_19 = {\n  class: \"hotWordBox\"\n};\nvar _hoisted_20 = {\n  class: \"hotWordContent\"\n};\nvar _hoisted_21 = {\n  class: \"hotWordBox-content\"\n};\nvar _hoisted_22 = {\n  class: \"hotWordBox-content-title\"\n};\nvar _hoisted_23 = {\n  class: \"hotWordBox-content-list\"\n};\nvar _hoisted_24 = {\n  class: \"hotWordBox-content-list-item-title\"\n};\nvar _hoisted_25 = {\n  class: \"hotWordBox-content-list-item-num\"\n};\nvar _hoisted_26 = {\n  class: \"AiUseStatistics-chart-content-right\"\n};\nvar _hoisted_27 = {\n  class: \"AiUseStatistics-chart-content-right-content\"\n};\nvar _hoisted_28 = {\n  class: \"globalTable\"\n};\nvar _hoisted_29 = {\n  class: \"globalPagination\"\n};\nvar _hoisted_30 = {\n  class: \"AiUseStatistics-detail\"\n};\nvar _hoisted_31 = {\n  class: \"detail-item-title\"\n};\nvar _hoisted_32 = {\n  class: \"detail-item-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_RefreshRight = _resolveComponent(\"RefreshRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_ArrowRight = _resolveComponent(\"ArrowRight\");\n  var _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"AiUseStatistics\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-left\"\n      }, [_createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-left-title\"\n      }, \"AI使用统计分析\"), _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-left-desc\"\n      }, \"实时监控AI服务使用情况与趋势分析\")], -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-right\",\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.aigptStatistics(true);\n        })\n      }, [_cache[6] || (_cache[6] = _createTextVNode(\" 刷新数据 \")), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_RefreshRight)];\n        }),\n        _: 1 /* STABLE */\n      })])]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-left-title\"\n      }, [_createTextVNode(\" 服务总人次 \"), _createCommentVNode(\" <img src=\\\"../img/AiHelp.png\\\" alt=\\\"\\\" /> \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.overviewData.totalServiceTimes || 0), 1 /* TEXT */)]), _cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-right\"\n      }, [_createElementVNode(\"img\", {\n        src: _imports_0,\n        alt: \"\"\n      })], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-left-title\"\n      }, [_createTextVNode(\" 服务总人数 \"), _createCommentVNode(\" <img src=\\\"../img/AiHelp.png\\\" alt=\\\"\\\" /> \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.overviewData.totalUsers || 0), 1 /* TEXT */)]), _cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-right\"\n      }, [_createElementVNode(\"img\", {\n        src: _imports_1,\n        alt: \"\"\n      })], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-left-title\"\n      }, [_createTextVNode(\" 累计问答次数 \"), _createCommentVNode(\" <img src=\\\"../img/AiHelp.png\\\" alt=\\\"\\\" /> \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createTextVNode(_toDisplayString($setup.overviewData.totalAnswerTimes || 0) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", {\n        onClick: $setup.handleDetail\n      }, [_cache[12] || (_cache[12] = _createTextVNode(\" 详情 \")), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowRight)];\n        }),\n        _: 1 /* STABLE */\n      })])])]), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-header-content-item-right\"\n      }, [_createElementVNode(\"img\", {\n        src: _imports_2,\n        alt: \"\"\n      })], -1 /* HOISTED */))])])]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_date_picker, {\n        modelValue: $setup.year,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.year = $event;\n        }),\n        clearable: false,\n        type: \"year\",\n        \"value-format\": \"YYYY\",\n        placeholder: \"选择年份\",\n        onChange: $setup.getLineData\n      }, null, 8 /* PROPS */, [\"modelValue\"])]), _createVNode($setup[\"barAndPie\"], {\n        data: $setup.lineData\n      }, null, 8 /* PROPS */, [\"data\"])]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-chart-content-right-title-left\"\n      }, \"业务线热词分布\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_select, {\n        modelValue: $setup.hottype,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.hottype = $event;\n        }),\n        placeholder: \"请选择业务线\",\n        onChange: $setup.getHotWordData,\n        filterable: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.hottypeList, function (item) {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.id,\n              label: item.name,\n              value: item.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])])]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createVNode($setup[\"wordCloudChart\"], {\n        data: $setup.hotWordData\n      }, null, 8 /* PROPS */, [\"data\"])])]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, _toDisplayString($setup.getHottypeName()), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_23, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.hotWordData, function (item, index) {\n        return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n          class: \"hotWordBox-content-list-item\",\n          key: item.id\n        }, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString(item.hotWord), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, _toDisplayString(item.appearTimes) + \" 次\", 1 /* TEXT */)])), [[_vShow, index < 3]]);\n      }), 128 /* KEYED_FRAGMENT */))])])]), _createElementVNode(\"div\", _hoisted_26, [_cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-chart-content-right-title\"\n      }, [_createElementVNode(\"div\", {\n        class: \"AiUseStatistics-chart-content-right-title-left\"\n      }, \"近期对话记录\"), _createElementVNode(\"div\", {\n        class: \"AiUseStatistics-chart-content-right-title-right\"\n      }, [_createCommentVNode(\" 查看更多\\r\\n            <el-icon><ArrowRight /></el-icon> \")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createVNode(_component_el_table, {\n        ref: \"tableRef\",\n        \"row-key\": \"id\",\n        data: $setup.tableData\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_table_column, {\n            label: \"用户\",\n            prop: \"createUserName\",\n            width: \"100\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"对话内容\",\n            prop: \"promptQuestion\",\n            \"min-width\": \"120\",\n            \"show-overflow-tooltip\": \"\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"业务线\",\n            prop: \"chatBusinessName\",\n            width: \"160\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"时间\",\n            prop: \"createDate\",\n            width: \"160\"\n          }, {\n            default: _withCtx(function (_ref) {\n              var row = _ref.row;\n              return [_createTextVNode(_toDisplayString(row.createDate ? $setup.format(row.createDate, 'YYYY-MM-DD HH:mm') : ''), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }), false ? (_openBlock(), _createBlock(_component_xyl_global_table_button, {\n            key: 0,\n            data: $setup.tableButtonList,\n            onButtonClick: _ctx.handleCommand\n          }, null, 8 /* PROPS */, [\"onButtonClick\"])) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"data\"])]), _createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_pagination, {\n        currentPage: $setup.pageNo,\n        \"onUpdate:currentPage\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.pageNo = $event;\n        }),\n        \"page-size\": $setup.pageSize,\n        \"onUpdate:pageSize\": _cache[4] || (_cache[4] = function ($event) {\n          return $setup.pageSize = $event;\n        }),\n        \"page-sizes\": $setup.pageSizes,\n        layout: \"total, sizes, prev, pager, next, jumper\",\n        onSizeChange: $setup.handleQuery,\n        onCurrentChange: $setup.handleQuery,\n        total: $setup.totals,\n        background: \"\"\n      }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])])])])]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.showMore,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n          return $setup.showMore = $event;\n        }),\n        name: \"详情\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_30, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.detailData, function (item) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"detail-item\",\n              key: item.id\n            }, [_createElementVNode(\"div\", _hoisted_31, [_cache[17] || (_cache[17] = _createElementVNode(\"img\", {\n              src: _imports_3,\n              alt: \"\"\n            }, null, -1 /* HOISTED */)), _createVNode(_component_el_tooltip, {\n              content: item.name,\n              disabled: item.name.length < 10,\n              placement: \"top\"\n            }, {\n              default: _withCtx(function () {\n                return [_createElementVNode(\"span\", null, _toDisplayString(item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name), 1 /* TEXT */)];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"content\", \"disabled\"])]), _createElementVNode(\"div\", _hoisted_32, [_createTextVNode(_toDisplayString(item.count) + \" \", 1 /* TEXT */), _cache[18] || (_cache[18] = _createElementVNode(\"span\", null, \"次\", -1 /* HOISTED */))])]);\n          }), 128 /* KEYED_FRAGMENT */))])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "_imports_3", "class", "_createBlock", "_component_el_scrollbar", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "_hoisted_2", "onClick", "_cache", "$event", "$setup", "aigptStatistics", "_createTextVNode", "_createVNode", "_component_el_icon", "_component_RefreshRight", "_", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_createCommentVNode", "_hoisted_6", "_toDisplayString", "overviewData", "totalServiceTimes", "src", "alt", "_hoisted_7", "_hoisted_8", "_hoisted_9", "totalUsers", "_hoisted_10", "_hoisted_11", "_hoisted_12", "totalAnswerTimes", "handleDetail", "_component_ArrowRight", "_hoisted_13", "_hoisted_14", "_component_el_date_picker", "modelValue", "year", "clearable", "type", "placeholder", "onChange", "getLineData", "data", "lineData", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_component_el_select", "hottype", "getHotWordData", "filterable", "_createElementBlock", "_Fragment", "_renderList", "hottypeList", "item", "_component_el_option", "key", "id", "label", "name", "value", "_hoisted_19", "_hoisted_20", "hotWordData", "_hoisted_21", "_hoisted_22", "getHottypeName", "_hoisted_23", "index", "_hoisted_24", "hotWord", "_hoisted_25", "appearTimes", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_component_el_table", "ref", "tableData", "_component_el_table_column", "prop", "width", "_ref", "row", "createDate", "format", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "_ctx", "handleCommand", "_hoisted_29", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "handleQuery", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "showMore", "_hoisted_30", "detailData", "_hoisted_31", "_component_el_tooltip", "content", "disabled", "length", "placement", "slice", "_hoisted_32", "count"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiUseStatistics\\AiUseStatistics.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"AiUseStatistics\">\r\n    <div class=\"AiUseStatistics-header\">\r\n      <div class=\"AiUseStatistics-header-box\">\r\n        <div class=\"AiUseStatistics-header-left\">\r\n          <div class=\"AiUseStatistics-header-left-title\">AI使用统计分析</div>\r\n          <div class=\"AiUseStatistics-header-left-desc\">实时监控AI服务使用情况与趋势分析</div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-right\" @click=\"aigptStatistics(true)\">\r\n          刷新数据\r\n          <el-icon><RefreshRight /></el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiUseStatistics-header-content\">\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              服务总人次\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">{{ overviewData.totalServiceTimes || 0 }}</div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon3.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              服务总人数\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">{{ overviewData.totalUsers || 0 }}</div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon2.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              累计问答次数\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">\r\n              {{ overviewData.totalAnswerTimes || 0 }}\r\n              <span @click=\"handleDetail\">\r\n                详情\r\n                <el-icon><ArrowRight /></el-icon>\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon1.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"AiUseStatistics-chart\">\r\n      <div class=\"AiUseStatistics-chart-timeSelect\">\r\n        <el-date-picker\r\n          v-model=\"year\"\r\n          :clearable=\"false\"\r\n          type=\"year\"\r\n          value-format=\"YYYY\"\r\n          placeholder=\"选择年份\"\r\n          @change=\"getLineData\" />\r\n      </div>\r\n      <barAndPie :data=\"lineData\" />\r\n    </div>\r\n    <div class=\"AiUseStatistics-chart-content\">\r\n      <div class=\"AiUseStatistics-chart-content-left\">\r\n        <div class=\"AiUseStatistics-chart-content-right-title\">\r\n          <div class=\"AiUseStatistics-chart-content-right-title-left\">业务线热词分布</div>\r\n          <div class=\"AiUseStatistics-chart-content-right-title-right\">\r\n            <el-select v-model=\"hottype\" placeholder=\"请选择业务线\" @change=\"getHotWordData\" filterable>\r\n              <el-option v-for=\"item in hottypeList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"hotWordBox\">\r\n          <div class=\"hotWordContent\">\r\n            <wordCloudChart :data=\"hotWordData\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"hotWordBox-content\">\r\n          <div class=\"hotWordBox-content-title\">\r\n            {{ getHottypeName() }}\r\n          </div>\r\n          <div class=\"hotWordBox-content-list\">\r\n            <div\r\n              class=\"hotWordBox-content-list-item\"\r\n              v-show=\"index < 3\"\r\n              v-for=\"(item, index) in hotWordData\"\r\n              :key=\"item.id\">\r\n              <div class=\"hotWordBox-content-list-item-title\">\r\n                {{ item.hotWord }}\r\n              </div>\r\n              <div class=\"hotWordBox-content-list-item-num\">{{ item.appearTimes }} 次</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiUseStatistics-chart-content-right\">\r\n        <div class=\"AiUseStatistics-chart-content-right-title\">\r\n          <div class=\"AiUseStatistics-chart-content-right-title-left\">近期对话记录</div>\r\n          <div class=\"AiUseStatistics-chart-content-right-title-right\">\r\n            <!-- 查看更多\r\n            <el-icon><ArrowRight /></el-icon> -->\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-chart-content-right-content\">\r\n          <div class=\"globalTable\">\r\n            <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\">\r\n              <el-table-column label=\"用户\" prop=\"createUserName\" width=\"100\"></el-table-column>\r\n              <el-table-column\r\n                label=\"对话内容\"\r\n                prop=\"promptQuestion\"\r\n                min-width=\"120\"\r\n                show-overflow-tooltip></el-table-column>\r\n              <el-table-column label=\"业务线\" prop=\"chatBusinessName\" width=\"160\"></el-table-column>\r\n              <el-table-column label=\"时间\" prop=\"createDate\" width=\"160\">\r\n                <template #default=\"{ row }\">\r\n                  {{ row.createDate ? format(row.createDate, 'YYYY-MM-DD HH:mm') : '' }}\r\n                </template>\r\n              </el-table-column>\r\n              <xyl-global-table-button\r\n                v-if=\"false\"\r\n                :data=\"tableButtonList\"\r\n                @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n            </el-table>\r\n          </div>\r\n          <div class=\"globalPagination\">\r\n            <el-pagination\r\n              v-model:currentPage=\"pageNo\"\r\n              v-model:page-size=\"pageSize\"\r\n              :page-sizes=\"pageSizes\"\r\n              layout=\"total, sizes, prev, pager, next, jumper\"\r\n              @size-change=\"handleQuery\"\r\n              @current-change=\"handleQuery\"\r\n              :total=\"totals\"\r\n              background />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"showMore\" name=\"详情\">\r\n      <div class=\"AiUseStatistics-detail\">\r\n        <div class=\"detail-item\" v-for=\"item in detailData\" :key=\"item.id\">\r\n          <div class=\"detail-item-title\">\r\n            <img src=\"../img/tongjiIcon.png\" alt=\"\" />\r\n            <el-tooltip :content=\"item.name\" :disabled=\"item.name.length < 10\" placement=\"top\">\r\n              <span>{{ item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name }}</span>\r\n            </el-tooltip>\r\n          </div>\r\n          <div class=\"detail-item-content\">\r\n            {{ item.count }}\r\n            <span>次</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </xyl-popup-window>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'AiUseStatistics'\r\n}\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport api from '@/api'\r\nimport barAndPie from './common/barAndPie.vue'\r\nimport wordCloudChart from './common/wordCloudChart.vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nconst tableButtonList = [{ id: 'edit', name: '查看详情', width: 100, has: '' }]\r\n\r\nconst { tableRef, totals, pageNo, pageSize, pageSizes, tableData, handleQuery } = GlobalTable({\r\n  tableApi: 'aigptChatLogsList'\r\n})\r\n\r\nonMounted(() => {\r\n  aigptStatistics()\r\n  getLineData()\r\n  handleQuery()\r\n  getHottypeList()\r\n})\r\nconst overviewData = ref({})\r\nconst aigptStatistics = async (forceRefresh = false) => {\r\n  const res = await api.globalJson('/aigptStatistics/overview', { forceRefresh })\r\n  overviewData.value = res.data\r\n}\r\nconst lineData = ref([])\r\nconst year = ref(new Date().getFullYear() + '')\r\nconst getLineData = async () => {\r\n  const res = await api.globalJson('/aigptStatistics/trend', {\r\n    beginDate: new Date(year.value + '-01-01').getTime(),\r\n    endDate: new Date(year.value + '-12-31').getTime(),\r\n    timeDimension: 'month'\r\n  })\r\n  const arr = [\r\n    {\r\n      name: '对话量',\r\n      data: res.data.map((v) => ({ ...v, value: v.dialogueCount, name: v.bucket })),\r\n      type: 'line',\r\n      color: 'rgba(31, 198, 255, 1)'\r\n    },\r\n    {\r\n      name: '活跃用户',\r\n      data: res.data.map((v) => ({ ...v, value: v.activeUsers, name: v.bucket })),\r\n      type: 'line',\r\n      color: 'rgba(245, 231, 79, 1)'\r\n    }\r\n  ]\r\n  lineData.value = arr\r\n}\r\nconst hottype = ref('')\r\nconst hottypeList = ref([])\r\nconst getHottypeName = () => {\r\n  return hottypeList.value.find((v) => v.id === hottype.value)?.name || '全部业务线'\r\n}\r\nconst getHottypeList = async () => {\r\n  const res = await api.globalJson('/aigptChatScene/selector')\r\n  hottypeList.value = res.data\r\n  hottypeList.value.unshift({ id: '', name: '全部业务线' })\r\n  if (res.data.length > 0) {\r\n    hottype.value = res.data[0].id\r\n    getHotWordData()\r\n  }\r\n}\r\nconst hotWordData = ref([])\r\nconst getHotWordData = async () => {\r\n  const res = await api.globalJson('/aigptStatistics/hotwordTop', {\r\n    chatBusinessScene: hottype.value\r\n  })\r\n  hotWordData.value = res.data\r\n}\r\nconst showMore = ref(false)\r\nconst detailData = ref([])\r\nconst handleDetail = async () => {\r\n  const res = await api.globalJson('/aigptStatistics/overviewdetail')\r\n  detailData.value = res.data\r\n  showMore.value = true\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.AiUseStatistics {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #f0f2f5;\r\n  padding-top: 20px;\r\n  padding-bottom: 20px;\r\n  .AiUseStatistics-header {\r\n    background: #ffffff;\r\n    border-radius: 0px 0px 0px 0px;\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n    .AiUseStatistics-header-box {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: flex-start;\r\n      margin-bottom: 20px;\r\n      .AiUseStatistics-header-left {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        justify-content: center;\r\n      }\r\n      .AiUseStatistics-header-right {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n      .AiUseStatistics-header-left-title {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n        color: #333333;\r\n        margin-bottom: 10px;\r\n      }\r\n      .AiUseStatistics-header-left-desc {\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n      .AiUseStatistics-header-right {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n        font-size: 14px;\r\n        .zy-el-icon {\r\n          font-size: 16px;\r\n          margin-left: 5px;\r\n        }\r\n      }\r\n    }\r\n    .AiUseStatistics-header-content {\r\n      display: flex;\r\n      gap: 20px;\r\n      .AiUseStatistics-header-content-item {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background: #f5f7fa;\r\n        border-radius: 6px 6px 6px 6px;\r\n        padding: 20px;\r\n        .AiUseStatistics-header-content-item-left {\r\n          .AiUseStatistics-header-content-item-left-title {\r\n            color: #474b4f;\r\n            font-size: 16px;\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 10px;\r\n            img {\r\n              width: 16px;\r\n              height: 16px;\r\n              margin-left: 5px;\r\n            }\r\n          }\r\n          .AiUseStatistics-header-content-item-left-num {\r\n            color: var(--zy-el-color-primary);\r\n            font-size: 26px;\r\n            font-weight: 600;\r\n            span {\r\n              margin-left: 20px;\r\n              color: var(--zy-el-color-primary);\r\n              cursor: pointer;\r\n              font-size: 14px;\r\n              .zy-el-icon {\r\n                font-size: 14px;\r\n                margin-left: 5px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .AiUseStatistics-header-content-item-right {\r\n          width: 60px;\r\n          height: 60px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .AiUseStatistics-chart {\r\n    width: 100%;\r\n    height: 334px;\r\n    padding-top: 20px;\r\n    margin-bottom: 20px;\r\n    background: #ffffff;\r\n    position: relative;\r\n    .AiUseStatistics-chart-timeSelect {\r\n      position: absolute;\r\n      right: 200px;\r\n      top: 10px;\r\n      z-index: 10;\r\n    }\r\n  }\r\n  .AiUseStatistics-chart-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    height: 384px;\r\n\r\n    .AiUseStatistics-chart-content-right-title {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 10px;\r\n      .AiUseStatistics-chart-content-right-title-left {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .AiUseStatistics-chart-content-right-title-right {\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n        font-size: 14px;\r\n        .zy-el-select {\r\n          width: 160px;\r\n        }\r\n        .zy-el-icon {\r\n          font-size: 14px;\r\n          margin-left: 5px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .AiUseStatistics-chart-content-left {\r\n      width: 33%;\r\n      flex-shrink: 0;\r\n      background: #ffffff;\r\n      padding: 0 20px;\r\n      .hotWordBox {\r\n        height: 158px;\r\n        background: #f5f7fa;\r\n        .hotWordContent {\r\n          width: 80%;\r\n          height: 158px;\r\n          margin: auto;\r\n          background-image: url('../img/hotwordbg.png');\r\n          background-size: 100% 100%;\r\n          background-repeat: no-repeat;\r\n          background-position: center;\r\n          border-radius: 0px 0px 0px 0px;\r\n          opacity: 0.5;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 20px;\r\n        }\r\n      }\r\n      .hotWordBox-content {\r\n        height: calc(100% - 158px - 62px);\r\n        padding-top: 20px;\r\n        .hotWordBox-content-title {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          margin-bottom: 10px;\r\n        }\r\n        .hotWordBox-content-list {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 10px;\r\n          .hotWordBox-content-list-item {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            .hotWordBox-content-list-item-title {\r\n              padding-left: 16px;\r\n              font-size: 16px;\r\n              font-weight: 600;\r\n              position: relative;\r\n              &:after {\r\n                content: '';\r\n                display: block;\r\n                width: 6px;\r\n                height: 6px;\r\n                background: var(--zy-el-color-primary);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                left: 0;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                z-index: 10;\r\n              }\r\n            }\r\n            .hotWordBox-content-list-item-num {\r\n              font-size: 14px;\r\n              color: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .AiUseStatistics-chart-content-right {\r\n      flex-shrink: 0;\r\n      width: 66%;\r\n      background: #ffffff;\r\n      .AiUseStatistics-chart-content-right-content {\r\n        height: calc(100% - 46px);\r\n        .globalTable {\r\n          height: calc(100% - 42px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.AiUseStatistics-detail {\r\n  width: 990px;\r\n  padding: 24px;\r\n  justify-content: space-between;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n  .detail-item {\r\n    width: 32%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background: #f5f7fa;\r\n    border-radius: 4px 4px 4px 4px;\r\n    padding: 12px 16px;\r\n    .detail-item-title {\r\n      display: flex;\r\n      align-items: center;\r\n      img {\r\n        width: 20px;\r\n        height: 20px;\r\n        margin-right: 6px;\r\n      }\r\n    }\r\n    .detail-item-content {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: var(--zy-el-color-primary);\r\n      span {\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAuBiBA,UAAwB;OAYxBC,UAAwB;OAkBxBC,UAAwB;OAiGxBC,UAA2B;;EApJnCC,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAUlCA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA0C;;EAK9CA,KAAK,EAAC;AAA8C;;EAMxDA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA0C;;EAK9CA,KAAK,EAAC;AAA8C;;EAMxDA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAA0C;;EAK9CA,KAAK,EAAC;AAA8C;;EAc5DA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAkC;;EAW1CA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAoC;;EACxCA,KAAK,EAAC;AAA2C;;EAE/CA,KAAK,EAAC;AAAiD;;EAMzDA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAgB;;EAIxBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAA0B;;EAGhCA,KAAK,EAAC;AAAyB;;EAM3BA,KAAK,EAAC;AAAoC;;EAG1CA,KAAK,EAAC;AAAkC;;EAKhDA,KAAK,EAAC;AAAqC;;EAQzCA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAAa;;EAoBnBA,KAAK,EAAC;AAAkB;;EAe5BA,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAAmB;;EAMzBA,KAAK,EAAC;AAAqB;;;;;;;;;;;;;;;uBA1JxCC,YAAA,CAiKeC,uBAAA;IAjKDF,KAAK,EAAC;EAAiB;IADvCG,OAAA,EAAAC,QAAA,CAEI;MAAA,OAuDM,CAvDNC,mBAAA,CAuDM,OAvDNC,UAuDM,GAtDJD,mBAAA,CASM,OATNE,UASM,G,0BARJF,mBAAA,CAGM;QAHDL,KAAK,EAAC;MAA6B,IACtCK,mBAAA,CAA6D;QAAxDL,KAAK,EAAC;MAAmC,GAAC,UAAQ,GACvDK,mBAAA,CAAqE;QAAhEL,KAAK,EAAC;MAAkC,GAAC,mBAAiB,E,sBAEjEK,mBAAA,CAGM;QAHDL,KAAK,EAAC,8BAA8B;QAAEQ,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEC,MAAA,CAAAC,eAAe;QAAA;oCARzEC,gBAAA,CAQiF,QAEvE,IAAAC,YAAA,CAAmCC,kBAAA;QAV7CZ,OAAA,EAAAC,QAAA,CAUmB;UAAA,OAAgB,CAAhBU,YAAA,CAAgBE,uBAAA,E;;QAVnCC,CAAA;cAaMZ,mBAAA,CA2CM,OA3CNa,UA2CM,GA1CJb,mBAAA,CAWM,OAXNc,UAWM,GAVJd,mBAAA,CAMM,OANNe,UAMM,G,0BALJf,mBAAA,CAGM;QAHDL,KAAK,EAAC;MAAgD,IAhBvEa,gBAAA,CAgBwE,SAE1D,GAAAQ,mBAAA,gDAA+C,C,sBAEjDhB,mBAAA,CAAyG,OAAzGiB,UAAyG,EAAAC,gBAAA,CAA5CZ,MAAA,CAAAa,YAAY,CAACC,iBAAiB,sB,6BAE7FpB,mBAAA,CAEM;QAFDL,KAAK,EAAC;MAA2C,IACpDK,mBAAA,CAAuC;QAAlCqB,GAAwB,EAAxB9B,UAAwB;QAAC+B,GAAG,EAAC;iCAGtCtB,mBAAA,CAWM,OAXNuB,UAWM,GAVJvB,mBAAA,CAMM,OANNwB,UAMM,G,4BALJxB,mBAAA,CAGM;QAHDL,KAAK,EAAC;MAAgD,IA5BvEa,gBAAA,CA4BwE,SAE1D,GAAAQ,mBAAA,gDAA+C,C,sBAEjDhB,mBAAA,CAAkG,OAAlGyB,UAAkG,EAAAP,gBAAA,CAArCZ,MAAA,CAAAa,YAAY,CAACO,UAAU,sB,+BAEtF1B,mBAAA,CAEM;QAFDL,KAAK,EAAC;MAA2C,IACpDK,mBAAA,CAAuC;QAAlCqB,GAAwB,EAAxB7B,UAAwB;QAAC8B,GAAG,EAAC;iCAGtCtB,mBAAA,CAiBM,OAjBN2B,WAiBM,GAhBJ3B,mBAAA,CAYM,OAZN4B,WAYM,G,4BAXJ5B,mBAAA,CAGM;QAHDL,KAAK,EAAC;MAAgD,IAxCvEa,gBAAA,CAwCwE,UAE1D,GAAAQ,mBAAA,gDAA+C,C,sBAEjDhB,mBAAA,CAMM,OANN6B,WAMM,GAlDlBrB,gBAAA,CAAAU,gBAAA,CA6CiBZ,MAAA,CAAAa,YAAY,CAACW,gBAAgB,SAAQ,GACxC,iBAAA9B,mBAAA,CAGO;QAHAG,OAAK,EAAEG,MAAA,CAAAyB;MAAY,I,4BA9CxCvB,gBAAA,CA8C0C,MAE1B,IAAAC,YAAA,CAAiCC,kBAAA;QAhDjDZ,OAAA,EAAAC,QAAA,CAgDyB;UAAA,OAAc,CAAdU,YAAA,CAAcuB,qBAAA,E;;QAhDvCpB,CAAA;4CAoDUZ,mBAAA,CAEM;QAFDL,KAAK,EAAC;MAA2C,IACpDK,mBAAA,CAAuC;QAAlCqB,GAAwB,EAAxB5B,UAAwB;QAAC6B,GAAG,EAAC;qCAK1CtB,mBAAA,CAWM,OAXNiC,WAWM,GAVJjC,mBAAA,CAQM,OARNkC,WAQM,GAPJzB,YAAA,CAM0B0B,yBAAA;QAlElCC,UAAA,EA6DmB9B,MAAA,CAAA+B,IAAI;QA7DvB,uBAAAjC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA6DmBC,MAAA,CAAA+B,IAAI,GAAAhC,MAAA;QAAA;QACZiC,SAAS,EAAE,KAAK;QACjBC,IAAI,EAAC,MAAM;QACX,cAAY,EAAC,MAAM;QACnBC,WAAW,EAAC,MAAM;QACjBC,QAAM,EAAEnC,MAAA,CAAAoC;iDAEbjC,YAAA,CAA8BH,MAAA;QAAlBqC,IAAI,EAAErC,MAAA,CAAAsC;MAAQ,kC,GAE5B5C,mBAAA,CA2EM,OA3EN6C,WA2EM,GA1EJ7C,mBAAA,CA+BM,OA/BN8C,WA+BM,GA9BJ9C,mBAAA,CAOM,OAPN+C,WAOM,G,4BANJ/C,mBAAA,CAAyE;QAApEL,KAAK,EAAC;MAAgD,GAAC,SAAO,sBACnEK,mBAAA,CAIM,OAJNgD,WAIM,GAHJvC,YAAA,CAEYwC,oBAAA;QA7ExBb,UAAA,EA2EgC9B,MAAA,CAAA4C,OAAO;QA3EvC,uBAAA9C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA2EgCC,MAAA,CAAA4C,OAAO,GAAA7C,MAAA;QAAA;QAAEmC,WAAW,EAAC,QAAQ;QAAEC,QAAM,EAAEnC,MAAA,CAAA6C,cAAc;QAAEC,UAAU,EAAV;;QA3EvFtD,OAAA,EAAAC,QAAA,CA4EyB;UAAA,OAA2B,E,kBAAtCsD,mBAAA,CAA4FC,SAAA,QA5E1GC,WAAA,CA4EwCjD,MAAA,CAAAkD,WAAW,EA5EnD,UA4EgCC,IAAI;iCAAtB7D,YAAA,CAA4F8D,oBAAA;cAApDC,GAAG,EAAEF,IAAI,CAACG,EAAE;cAAGC,KAAK,EAAEJ,IAAI,CAACK,IAAI;cAAGC,KAAK,EAAEN,IAAI,CAACG;;;;QA5EpGhD,CAAA;6CAgFQZ,mBAAA,CAIM,OAJNgE,WAIM,GAHJhE,mBAAA,CAEM,OAFNiE,WAEM,GADJxD,YAAA,CAAsCH,MAAA;QAArBqC,IAAI,EAAErC,MAAA,CAAA4D;MAAW,kC,KAGtClE,mBAAA,CAgBM,OAhBNmE,WAgBM,GAfJnE,mBAAA,CAEM,OAFNoE,WAEM,EAAAlD,gBAAA,CADDZ,MAAA,CAAA+D,cAAc,oBAEnBrE,mBAAA,CAWM,OAXNsE,WAWM,I,kBAVJjB,mBAAA,CASMC,SAAA,QAnGlBC,WAAA,CA6FsCjD,MAAA,CAAA4D,WAAW,EA7FjD,UA6FsBT,IAAI,EAAEc,KAAK;8CAHrBlB,mBAAA,CASM;UARJ1D,KAAK,EAAC,8BAA8B;UAGnCgE,GAAG,EAAEF,IAAI,CAACG;YACX5D,mBAAA,CAEM,OAFNwE,WAEM,EAAAtD,gBAAA,CADDuC,IAAI,CAACgB,OAAO,kBAEjBzE,mBAAA,CAA4E,OAA5E0E,WAA4E,EAAAxD,gBAAA,CAA3BuC,IAAI,CAACkB,WAAW,IAAG,IAAE,gB,cAN9DJ,KAAK,M;4CAWrBvE,mBAAA,CAyCM,OAzCN4E,WAyCM,G,4BAxCJ5E,mBAAA,CAMM;QANDL,KAAK,EAAC;MAA2C,IACpDK,mBAAA,CAAwE;QAAnEL,KAAK,EAAC;MAAgD,GAAC,QAAM,GAClEK,mBAAA,CAGM;QAHDL,KAAK,EAAC;MAAiD,IAC1DqB,mBAAA,2DACqC,C,wBAGzChB,mBAAA,CAgCM,OAhCN6E,WAgCM,GA/BJ7E,mBAAA,CAmBM,OAnBN8E,WAmBM,GAlBJrE,YAAA,CAiBWsE,mBAAA;QAjBDC,GAAG,EAAC,UAAU;QAAC,SAAO,EAAC,IAAI;QAAErC,IAAI,EAAErC,MAAA,CAAA2E;;QAjHzDnF,OAAA,EAAAC,QAAA,CAkHc;UAAA,OAAgF,CAAhFU,YAAA,CAAgFyE,0BAAA;YAA/DrB,KAAK,EAAC,IAAI;YAACsB,IAAI,EAAC,gBAAgB;YAACC,KAAK,EAAC;cACxD3E,YAAA,CAI0CyE,0BAAA;YAHxCrB,KAAK,EAAC,MAAM;YACZsB,IAAI,EAAC,gBAAgB;YACrB,WAAS,EAAC,KAAK;YACf,uBAAqB,EAArB;cACF1E,YAAA,CAAmFyE,0BAAA;YAAlErB,KAAK,EAAC,KAAK;YAACsB,IAAI,EAAC,kBAAkB;YAACC,KAAK,EAAC;cAC3D3E,YAAA,CAIkByE,0BAAA;YAJDrB,KAAK,EAAC,IAAI;YAACsB,IAAI,EAAC,YAAY;YAACC,KAAK,EAAC;;YACvCtF,OAAO,EAAAC,QAAA,CAChB,UAAAsF,IAAA;cAAA,IADoBC,GAAG,GAAAD,IAAA,CAAHC,GAAG;cAAA,QA1HzC9E,gBAAA,CAAAU,gBAAA,CA2HqBoE,GAAG,CAACC,UAAU,GAAGjF,MAAA,CAAAkF,MAAM,CAACF,GAAG,CAACC,UAAU,2C;;YA3H3D3E,CAAA;cA+HsB,KAAK,I,cADbhB,YAAA,CAGyD6F,kCAAA;YAjIvE9B,GAAA;YAgIiBhB,IAAI,EAAErC,MAAA,CAAAoF,eAAe;YACrBC,aAAW,EAAEC,IAAA,CAAAC;wDAjI9B7E,mBAAA,e;;QAAAJ,CAAA;qCAoIUZ,mBAAA,CAUM,OAVN8F,WAUM,GATJrF,YAAA,CAQesF,wBAAA;QAPLC,WAAW,EAAE1F,MAAA,CAAA2F,MAAM;QAtIzC,wBAAA7F,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAsImCC,MAAA,CAAA2F,MAAM,GAAA5F,MAAA;QAAA;QACnB,WAAS,EAAEC,MAAA,CAAA4F,QAAQ;QAvIzC,qBAAA9F,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAuIiCC,MAAA,CAAA4F,QAAQ,GAAA7F,MAAA;QAAA;QAC1B,YAAU,EAAEC,MAAA,CAAA6F,SAAS;QACtBC,MAAM,EAAC,yCAAyC;QAC/CC,YAAW,EAAE/F,MAAA,CAAAgG,WAAW;QACxBC,eAAc,EAAEjG,MAAA,CAAAgG,WAAW;QAC3BE,KAAK,EAAElG,MAAA,CAAAmG,MAAM;QACdC,UAAU,EAAV;+HAKVjG,YAAA,CAemBkG,2BAAA;QAjKvBvE,UAAA,EAkJ+B9B,MAAA,CAAAsG,QAAQ;QAlJvC,uBAAAxG,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAkJ+BC,MAAA,CAAAsG,QAAQ,GAAAvG,MAAA;QAAA;QAAEyD,IAAI,EAAC;;QAlJ9ChE,OAAA,EAAAC,QAAA,CAmJM;UAAA,OAaM,CAbNC,mBAAA,CAaM,OAbN6G,WAaM,I,kBAZJxD,mBAAA,CAWMC,SAAA,QA/JdC,WAAA,CAoJgDjD,MAAA,CAAAwG,UAAU,EApJ1D,UAoJwCrD,IAAI;iCAApCJ,mBAAA,CAWM;cAXD1D,KAAK,EAAC,aAAa;cAA6BgE,GAAG,EAAEF,IAAI,CAACG;gBAC7D5D,mBAAA,CAKM,OALN+G,WAKM,G,4BAJJ/G,mBAAA,CAA0C;cAArCqB,GAA2B,EAA3B3B,UAA2B;cAAC4B,GAAG,EAAC;yCACrCb,YAAA,CAEauG,qBAAA;cAFAC,OAAO,EAAExD,IAAI,CAACK,IAAI;cAAGoD,QAAQ,EAAEzD,IAAI,CAACK,IAAI,CAACqD,MAAM;cAAOC,SAAS,EAAC;;cAvJzFtH,OAAA,EAAAC,QAAA,CAwJc;gBAAA,OAAqF,CAArFC,mBAAA,CAAqF,cAAAkB,gBAAA,CAA5EuC,IAAI,CAACK,IAAI,CAACqD,MAAM,QAAQ1D,IAAI,CAACK,IAAI,CAACuD,KAAK,kBAAkB5D,IAAI,CAACK,IAAI,iB;;cAxJzFlD,CAAA;4EA2JUZ,mBAAA,CAGM,OAHNsH,WAGM,GA9JhB9G,gBAAA,CAAAU,gBAAA,CA4JeuC,IAAI,CAAC8D,KAAK,IAAG,GAChB,iB,4BAAAvH,mBAAA,CAAc,cAAR,GAAC,qB;;;QA7JnBY,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}