{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { ref, onMounted } from 'vue';\nimport api from '@/api';\nimport barAndPie from './common/barAndPie.vue';\nimport wordCloudChart from './common/wordCloudChart.vue';\nimport { format } from 'common/js/time.js';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nvar __default__ = {\n  name: 'AiUseStatistics'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var tableButtonList = [{\n      id: 'edit',\n      name: '查看详情',\n      width: 100,\n      has: ''\n    }];\n    var _GlobalTable = GlobalTable({\n        tableApi: 'aigptChatLogsList'\n      }),\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery;\n    onMounted(function () {\n      aigptStatistics();\n      getLineData();\n      handleQuery();\n      getHottypeList();\n    });\n    var overviewData = ref({});\n    var aigptStatistics = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var forceRefresh,\n          res,\n          _args = arguments;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              forceRefresh = _args.length > 0 && _args[0] !== undefined ? _args[0] : false;\n              _context.next = 3;\n              return api.globalJson('/aigptStatistics/overview', {\n                forceRefresh\n              });\n            case 3:\n              res = _context.sent;\n              overviewData.value = res.data;\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function aigptStatistics() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var lineData = ref([]);\n    var year = ref(new Date().getFullYear() + '');\n    var getLineData = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res, arr;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.globalJson('/aigptStatistics/trend', {\n                beginDate: new Date(year.value + '-01-01').getTime(),\n                endDate: new Date(year.value + '-12-31').getTime(),\n                timeDimension: 'month'\n              });\n            case 2:\n              res = _context2.sent;\n              arr = [{\n                name: '对话量',\n                data: res.data.map(function (v) {\n                  return _objectSpread(_objectSpread({}, v), {}, {\n                    value: v.dialogueCount,\n                    name: v.bucket\n                  });\n                }),\n                type: 'line',\n                color: 'rgba(31, 198, 255, 1)'\n              }, {\n                name: '活跃用户',\n                data: res.data.map(function (v) {\n                  return _objectSpread(_objectSpread({}, v), {}, {\n                    value: v.activeUsers,\n                    name: v.bucket\n                  });\n                }),\n                type: 'line',\n                color: 'rgba(245, 231, 79, 1)'\n              }];\n              lineData.value = arr;\n            case 5:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function getLineData() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var hottype = ref('');\n    var hottypeList = ref([]);\n    var getHottypeName = function getHottypeName() {\n      var _hottypeList$value$fi;\n      return ((_hottypeList$value$fi = hottypeList.value.find(function (v) {\n        return v.id === hottype.value;\n      })) === null || _hottypeList$value$fi === void 0 ? void 0 : _hottypeList$value$fi.name) || '全部业务线';\n    };\n    var getHottypeList = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.globalJson('/aigptChatScene/selector');\n            case 2:\n              res = _context3.sent;\n              hottypeList.value = res.data;\n              hottypeList.value.unshift({\n                id: '',\n                name: '全部业务线'\n              });\n              if (res.data.length > 0) {\n                hottype.value = res.data[0].id;\n                getHotWordData();\n              }\n            case 6:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function getHottypeList() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var hotWordData = ref([]);\n    var getHotWordData = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.globalJson('/aigptStatistics/hotwordTop', {\n                chatBusinessScene: hottype.value\n              });\n            case 2:\n              res = _context4.sent;\n              hotWordData.value = res.data;\n            case 4:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function getHotWordData() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var showMore = ref(false);\n    var detailData = ref([]);\n    var handleDetail = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.next = 2;\n              return api.globalJson('/aigptStatistics/overviewdetail');\n            case 2:\n              res = _context5.sent;\n              detailData.value = res.data;\n              showMore.value = true;\n            case 5:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function handleDetail() {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      tableButtonList,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      overviewData,\n      aigptStatistics,\n      lineData,\n      year,\n      getLineData,\n      hottype,\n      hottypeList,\n      getHottypeName,\n      getHottypeList,\n      hotWordData,\n      getHotWordData,\n      showMore,\n      detailData,\n      handleDetail,\n      ref,\n      onMounted,\n      get api() {\n        return api;\n      },\n      barAndPie,\n      wordCloudChart,\n      get format() {\n        return format;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ref", "onMounted", "api", "barAnd<PERSON>ie", "wordCloudChart", "format", "GlobalTable", "__default__", "tableButtonList", "id", "width", "has", "_GlobalTable", "tableApi", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "aigptStatistics", "getLineData", "getHottypeList", "overviewData", "_ref2", "_callee", "forceRefresh", "res", "_args", "_callee$", "_context", "undefined", "globalJson", "data", "lineData", "year", "Date", "getFullYear", "_ref3", "_callee2", "arr", "_callee2$", "_context2", "beginDate", "getTime", "endDate", "timeDimension", "map", "_objectSpread", "dialogueCount", "bucket", "color", "activeUsers", "hottype", "hottypeList", "getHottypeName", "_hottypeList$value$fi", "find", "_ref4", "_callee3", "_callee3$", "_context3", "unshift", "getHotWordData", "hotWordData", "_ref5", "_callee4", "_callee4$", "_context4", "chatBusinessScene", "showMore", "detailData", "handleDetail", "_ref6", "_callee5", "_callee5$", "_context5"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiUseStatistics/AiUseStatistics.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"AiUseStatistics\">\r\n    <div class=\"AiUseStatistics-header\">\r\n      <div class=\"AiUseStatistics-header-box\">\r\n        <div class=\"AiUseStatistics-header-left\">\r\n          <div class=\"AiUseStatistics-header-left-title\">AI使用统计分析</div>\r\n          <div class=\"AiUseStatistics-header-left-desc\">实时监控AI服务使用情况与趋势分析</div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-right\" @click=\"aigptStatistics(true)\">\r\n          刷新数据\r\n          <el-icon><RefreshRight /></el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiUseStatistics-header-content\">\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              服务总人次\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">{{ overviewData.totalServiceTimes || 0 }}</div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon3.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              服务总人数\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">{{ overviewData.totalUsers || 0 }}</div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon2.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-header-content-item\">\r\n          <div class=\"AiUseStatistics-header-content-item-left\">\r\n            <div class=\"AiUseStatistics-header-content-item-left-title\">\r\n              累计问答次数\r\n              <!-- <img src=\"../img/AiHelp.png\" alt=\"\" /> -->\r\n            </div>\r\n            <div class=\"AiUseStatistics-header-content-item-left-num\">\r\n              {{ overviewData.totalAnswerTimes || 0 }}\r\n              <span @click=\"handleDetail\">\r\n                详情\r\n                <el-icon><ArrowRight /></el-icon>\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"AiUseStatistics-header-content-item-right\">\r\n            <img src=\"../img/AiIcon1.png\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"AiUseStatistics-chart\">\r\n      <div class=\"AiUseStatistics-chart-timeSelect\">\r\n        <el-date-picker\r\n          v-model=\"year\"\r\n          :clearable=\"false\"\r\n          type=\"year\"\r\n          value-format=\"YYYY\"\r\n          placeholder=\"选择年份\"\r\n          @change=\"getLineData\" />\r\n      </div>\r\n      <barAndPie :data=\"lineData\" />\r\n    </div>\r\n    <div class=\"AiUseStatistics-chart-content\">\r\n      <div class=\"AiUseStatistics-chart-content-left\">\r\n        <div class=\"AiUseStatistics-chart-content-right-title\">\r\n          <div class=\"AiUseStatistics-chart-content-right-title-left\">业务线热词分布</div>\r\n          <div class=\"AiUseStatistics-chart-content-right-title-right\">\r\n            <el-select v-model=\"hottype\" placeholder=\"请选择业务线\" @change=\"getHotWordData\" filterable>\r\n              <el-option v-for=\"item in hottypeList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"hotWordBox\">\r\n          <div class=\"hotWordContent\">\r\n            <wordCloudChart :data=\"hotWordData\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"hotWordBox-content\">\r\n          <div class=\"hotWordBox-content-title\">\r\n            {{ getHottypeName() }}\r\n          </div>\r\n          <div class=\"hotWordBox-content-list\">\r\n            <div\r\n              class=\"hotWordBox-content-list-item\"\r\n              v-show=\"index < 3\"\r\n              v-for=\"(item, index) in hotWordData\"\r\n              :key=\"item.id\">\r\n              <div class=\"hotWordBox-content-list-item-title\">\r\n                {{ item.hotWord }}\r\n              </div>\r\n              <div class=\"hotWordBox-content-list-item-num\">{{ item.appearTimes }} 次</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiUseStatistics-chart-content-right\">\r\n        <div class=\"AiUseStatistics-chart-content-right-title\">\r\n          <div class=\"AiUseStatistics-chart-content-right-title-left\">近期对话记录</div>\r\n          <div class=\"AiUseStatistics-chart-content-right-title-right\">\r\n            <!-- 查看更多\r\n            <el-icon><ArrowRight /></el-icon> -->\r\n          </div>\r\n        </div>\r\n        <div class=\"AiUseStatistics-chart-content-right-content\">\r\n          <div class=\"globalTable\">\r\n            <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\">\r\n              <el-table-column label=\"用户\" prop=\"createUserName\" width=\"100\"></el-table-column>\r\n              <el-table-column\r\n                label=\"对话内容\"\r\n                prop=\"promptQuestion\"\r\n                min-width=\"120\"\r\n                show-overflow-tooltip></el-table-column>\r\n              <el-table-column label=\"业务线\" prop=\"chatBusinessName\" width=\"160\"></el-table-column>\r\n              <el-table-column label=\"时间\" prop=\"createDate\" width=\"160\">\r\n                <template #default=\"{ row }\">\r\n                  {{ row.createDate ? format(row.createDate, 'YYYY-MM-DD HH:mm') : '' }}\r\n                </template>\r\n              </el-table-column>\r\n              <xyl-global-table-button\r\n                v-if=\"false\"\r\n                :data=\"tableButtonList\"\r\n                @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n            </el-table>\r\n          </div>\r\n          <div class=\"globalPagination\">\r\n            <el-pagination\r\n              v-model:currentPage=\"pageNo\"\r\n              v-model:page-size=\"pageSize\"\r\n              :page-sizes=\"pageSizes\"\r\n              layout=\"total, sizes, prev, pager, next, jumper\"\r\n              @size-change=\"handleQuery\"\r\n              @current-change=\"handleQuery\"\r\n              :total=\"totals\"\r\n              background />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"showMore\" name=\"详情\">\r\n      <div class=\"AiUseStatistics-detail\">\r\n        <div class=\"detail-item\" v-for=\"item in detailData\" :key=\"item.id\">\r\n          <div class=\"detail-item-title\">\r\n            <img src=\"../img/tongjiIcon.png\" alt=\"\" />\r\n            <el-tooltip :content=\"item.name\" :disabled=\"item.name.length < 10\" placement=\"top\">\r\n              <span>{{ item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name }}</span>\r\n            </el-tooltip>\r\n          </div>\r\n          <div class=\"detail-item-content\">\r\n            {{ item.count }}\r\n            <span>次</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </xyl-popup-window>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'AiUseStatistics'\r\n}\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport api from '@/api'\r\nimport barAndPie from './common/barAndPie.vue'\r\nimport wordCloudChart from './common/wordCloudChart.vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nconst tableButtonList = [{ id: 'edit', name: '查看详情', width: 100, has: '' }]\r\n\r\nconst { tableRef, totals, pageNo, pageSize, pageSizes, tableData, handleQuery } = GlobalTable({\r\n  tableApi: 'aigptChatLogsList'\r\n})\r\n\r\nonMounted(() => {\r\n  aigptStatistics()\r\n  getLineData()\r\n  handleQuery()\r\n  getHottypeList()\r\n})\r\nconst overviewData = ref({})\r\nconst aigptStatistics = async (forceRefresh = false) => {\r\n  const res = await api.globalJson('/aigptStatistics/overview', { forceRefresh })\r\n  overviewData.value = res.data\r\n}\r\nconst lineData = ref([])\r\nconst year = ref(new Date().getFullYear() + '')\r\nconst getLineData = async () => {\r\n  const res = await api.globalJson('/aigptStatistics/trend', {\r\n    beginDate: new Date(year.value + '-01-01').getTime(),\r\n    endDate: new Date(year.value + '-12-31').getTime(),\r\n    timeDimension: 'month'\r\n  })\r\n  const arr = [\r\n    {\r\n      name: '对话量',\r\n      data: res.data.map((v) => ({ ...v, value: v.dialogueCount, name: v.bucket })),\r\n      type: 'line',\r\n      color: 'rgba(31, 198, 255, 1)'\r\n    },\r\n    {\r\n      name: '活跃用户',\r\n      data: res.data.map((v) => ({ ...v, value: v.activeUsers, name: v.bucket })),\r\n      type: 'line',\r\n      color: 'rgba(245, 231, 79, 1)'\r\n    }\r\n  ]\r\n  lineData.value = arr\r\n}\r\nconst hottype = ref('')\r\nconst hottypeList = ref([])\r\nconst getHottypeName = () => {\r\n  return hottypeList.value.find((v) => v.id === hottype.value)?.name || '全部业务线'\r\n}\r\nconst getHottypeList = async () => {\r\n  const res = await api.globalJson('/aigptChatScene/selector')\r\n  hottypeList.value = res.data\r\n  hottypeList.value.unshift({ id: '', name: '全部业务线' })\r\n  if (res.data.length > 0) {\r\n    hottype.value = res.data[0].id\r\n    getHotWordData()\r\n  }\r\n}\r\nconst hotWordData = ref([])\r\nconst getHotWordData = async () => {\r\n  const res = await api.globalJson('/aigptStatistics/hotwordTop', {\r\n    chatBusinessScene: hottype.value\r\n  })\r\n  hotWordData.value = res.data\r\n}\r\nconst showMore = ref(false)\r\nconst detailData = ref([])\r\nconst handleDetail = async () => {\r\n  const res = await api.globalJson('/aigptStatistics/overviewdetail')\r\n  detailData.value = res.data\r\n  showMore.value = true\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.AiUseStatistics {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #f0f2f5;\r\n  padding-top: 20px;\r\n  padding-bottom: 20px;\r\n  .AiUseStatistics-header {\r\n    background: #ffffff;\r\n    border-radius: 0px 0px 0px 0px;\r\n    padding: 20px;\r\n    margin-bottom: 20px;\r\n    .AiUseStatistics-header-box {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: flex-start;\r\n      margin-bottom: 20px;\r\n      .AiUseStatistics-header-left {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        justify-content: center;\r\n      }\r\n      .AiUseStatistics-header-right {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n      .AiUseStatistics-header-left-title {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n        color: #333333;\r\n        margin-bottom: 10px;\r\n      }\r\n      .AiUseStatistics-header-left-desc {\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n      .AiUseStatistics-header-right {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n        font-size: 14px;\r\n        .zy-el-icon {\r\n          font-size: 16px;\r\n          margin-left: 5px;\r\n        }\r\n      }\r\n    }\r\n    .AiUseStatistics-header-content {\r\n      display: flex;\r\n      gap: 20px;\r\n      .AiUseStatistics-header-content-item {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background: #f5f7fa;\r\n        border-radius: 6px 6px 6px 6px;\r\n        padding: 20px;\r\n        .AiUseStatistics-header-content-item-left {\r\n          .AiUseStatistics-header-content-item-left-title {\r\n            color: #474b4f;\r\n            font-size: 16px;\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 10px;\r\n            img {\r\n              width: 16px;\r\n              height: 16px;\r\n              margin-left: 5px;\r\n            }\r\n          }\r\n          .AiUseStatistics-header-content-item-left-num {\r\n            color: var(--zy-el-color-primary);\r\n            font-size: 26px;\r\n            font-weight: 600;\r\n            span {\r\n              margin-left: 20px;\r\n              color: var(--zy-el-color-primary);\r\n              cursor: pointer;\r\n              font-size: 14px;\r\n              .zy-el-icon {\r\n                font-size: 14px;\r\n                margin-left: 5px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .AiUseStatistics-header-content-item-right {\r\n          width: 60px;\r\n          height: 60px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .AiUseStatistics-chart {\r\n    width: 100%;\r\n    height: 334px;\r\n    padding-top: 20px;\r\n    margin-bottom: 20px;\r\n    background: #ffffff;\r\n    position: relative;\r\n    .AiUseStatistics-chart-timeSelect {\r\n      position: absolute;\r\n      right: 200px;\r\n      top: 10px;\r\n      z-index: 10;\r\n    }\r\n  }\r\n  .AiUseStatistics-chart-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    height: 384px;\r\n\r\n    .AiUseStatistics-chart-content-right-title {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 10px;\r\n      .AiUseStatistics-chart-content-right-title-left {\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .AiUseStatistics-chart-content-right-title-right {\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n        font-size: 14px;\r\n        .zy-el-select {\r\n          width: 160px;\r\n        }\r\n        .zy-el-icon {\r\n          font-size: 14px;\r\n          margin-left: 5px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .AiUseStatistics-chart-content-left {\r\n      width: 33%;\r\n      flex-shrink: 0;\r\n      background: #ffffff;\r\n      padding: 0 20px;\r\n      .hotWordBox {\r\n        height: 158px;\r\n        background: #f5f7fa;\r\n        .hotWordContent {\r\n          width: 80%;\r\n          height: 158px;\r\n          margin: auto;\r\n          background-image: url('../img/hotwordbg.png');\r\n          background-size: 100% 100%;\r\n          background-repeat: no-repeat;\r\n          background-position: center;\r\n          border-radius: 0px 0px 0px 0px;\r\n          opacity: 0.5;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 20px;\r\n        }\r\n      }\r\n      .hotWordBox-content {\r\n        height: calc(100% - 158px - 62px);\r\n        padding-top: 20px;\r\n        .hotWordBox-content-title {\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          margin-bottom: 10px;\r\n        }\r\n        .hotWordBox-content-list {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 10px;\r\n          .hotWordBox-content-list-item {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            .hotWordBox-content-list-item-title {\r\n              padding-left: 16px;\r\n              font-size: 16px;\r\n              font-weight: 600;\r\n              position: relative;\r\n              &:after {\r\n                content: '';\r\n                display: block;\r\n                width: 6px;\r\n                height: 6px;\r\n                background: var(--zy-el-color-primary);\r\n                border-radius: 50%;\r\n                position: absolute;\r\n                left: 0;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                z-index: 10;\r\n              }\r\n            }\r\n            .hotWordBox-content-list-item-num {\r\n              font-size: 14px;\r\n              color: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .AiUseStatistics-chart-content-right {\r\n      flex-shrink: 0;\r\n      width: 66%;\r\n      background: #ffffff;\r\n      .AiUseStatistics-chart-content-right-content {\r\n        height: calc(100% - 46px);\r\n        .globalTable {\r\n          height: calc(100% - 42px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.AiUseStatistics-detail {\r\n  width: 990px;\r\n  padding: 24px;\r\n  justify-content: space-between;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n  .detail-item {\r\n    width: 32%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background: #f5f7fa;\r\n    border-radius: 4px 4px 4px 4px;\r\n    padding: 12px 16px;\r\n    .detail-item-title {\r\n      display: flex;\r\n      align-items: center;\r\n      img {\r\n        width: 20px;\r\n        height: 20px;\r\n        margin-right: 6px;\r\n      }\r\n    }\r\n    .detail-item-content {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n      color: var(--zy-el-color-primary);\r\n      span {\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;+CA2KA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,SAASE,GAAG,EAAEC,SAAS,QAAQ,KAAK;AACpC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AAVtD,IAAAC,WAAA,GAAe;EACblC,IAAI,EAAE;AACR,CAAC;;;;;IASD,IAAMmC,eAAe,GAAG,CAAC;MAAEC,EAAE,EAAE,MAAM;MAAEpC,IAAI,EAAE,MAAM;MAAEqC,KAAK,EAAE,GAAG;MAAEC,GAAG,EAAE;IAAG,CAAC,CAAC;IAE3E,IAAAC,YAAA,GAAkFN,WAAW,CAAC;QAC5FO,QAAQ,EAAE;MACZ,CAAC,CAAC;MAFMC,QAAQ,GAAAF,YAAA,CAARE,QAAQ;MAAEC,MAAM,GAAAH,YAAA,CAANG,MAAM;MAAEC,MAAM,GAAAJ,YAAA,CAANI,MAAM;MAAEC,QAAQ,GAAAL,YAAA,CAARK,QAAQ;MAAEC,SAAS,GAAAN,YAAA,CAATM,SAAS;MAAEC,SAAS,GAAAP,YAAA,CAATO,SAAS;MAAEC,WAAW,GAAAR,YAAA,CAAXQ,WAAW;IAI7EnB,SAAS,CAAC,YAAM;MACdoB,eAAe,CAAC,CAAC;MACjBC,WAAW,CAAC,CAAC;MACbF,WAAW,CAAC,CAAC;MACbG,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IACF,IAAMC,YAAY,GAAGxB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAMqB,eAAe;MAAA,IAAAI,KAAA,GAAA9B,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAoD,QAAA;QAAA,IAAAC,YAAA;UAAAC,GAAA;UAAAC,KAAA,GAAAjC,SAAA;QAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAAqH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAhD,IAAA,GAAAgD,QAAA,CAAA3E,IAAA;YAAA;cAAOuE,YAAY,GAAAE,KAAA,CAAA5D,MAAA,QAAA4D,KAAA,QAAAG,SAAA,GAAAH,KAAA,MAAG,KAAK;cAAAE,QAAA,CAAA3E,IAAA;cAAA,OAC/B8C,GAAG,CAAC+B,UAAU,CAAC,2BAA2B,EAAE;gBAAEN;cAAa,CAAC,CAAC;YAAA;cAAzEC,GAAG,GAAAG,QAAA,CAAAlF,IAAA;cACT2E,YAAY,CAAC5H,KAAK,GAAGgI,GAAG,CAACM,IAAI;YAAA;YAAA;cAAA,OAAAH,QAAA,CAAA7C,IAAA;UAAA;QAAA,GAAAwC,OAAA;MAAA,CAC9B;MAAA,gBAHKL,eAAeA,CAAA;QAAA,OAAAI,KAAA,CAAA5B,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGpB;IACD,IAAMuC,QAAQ,GAAGnC,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMoC,IAAI,GAAGpC,GAAG,CAAC,IAAIqC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;IAC/C,IAAMhB,WAAW;MAAA,IAAAiB,KAAA,GAAA5C,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkE,SAAA;QAAA,IAAAZ,GAAA,EAAAa,GAAA;QAAA,OAAAvJ,mBAAA,GAAAuB,IAAA,UAAAiI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAAvF,IAAA;YAAA;cAAAuF,SAAA,CAAAvF,IAAA;cAAA,OACA8C,GAAG,CAAC+B,UAAU,CAAC,wBAAwB,EAAE;gBACzDW,SAAS,EAAE,IAAIP,IAAI,CAACD,IAAI,CAACxI,KAAK,GAAG,QAAQ,CAAC,CAACiJ,OAAO,CAAC,CAAC;gBACpDC,OAAO,EAAE,IAAIT,IAAI,CAACD,IAAI,CAACxI,KAAK,GAAG,QAAQ,CAAC,CAACiJ,OAAO,CAAC,CAAC;gBAClDE,aAAa,EAAE;cACjB,CAAC,CAAC;YAAA;cAJInB,GAAG,GAAAe,SAAA,CAAA9F,IAAA;cAKH4F,GAAG,GAAG,CACV;gBACEpE,IAAI,EAAE,KAAK;gBACX6D,IAAI,EAAEN,GAAG,CAACM,IAAI,CAACc,GAAG,CAAC,UAACpH,CAAC;kBAAA,OAAAqH,aAAA,CAAAA,aAAA,KAAWrH,CAAC;oBAAEhC,KAAK,EAAEgC,CAAC,CAACsH,aAAa;oBAAE7E,IAAI,EAAEzC,CAAC,CAACuH;kBAAM;gBAAA,CAAG,CAAC;gBAC7EpI,IAAI,EAAE,MAAM;gBACZqI,KAAK,EAAE;cACT,CAAC,EACD;gBACE/E,IAAI,EAAE,MAAM;gBACZ6D,IAAI,EAAEN,GAAG,CAACM,IAAI,CAACc,GAAG,CAAC,UAACpH,CAAC;kBAAA,OAAAqH,aAAA,CAAAA,aAAA,KAAWrH,CAAC;oBAAEhC,KAAK,EAAEgC,CAAC,CAACyH,WAAW;oBAAEhF,IAAI,EAAEzC,CAAC,CAACuH;kBAAM;gBAAA,CAAG,CAAC;gBAC3EpI,IAAI,EAAE,MAAM;gBACZqI,KAAK,EAAE;cACT,CAAC,CACF;cACDjB,QAAQ,CAACvI,KAAK,GAAG6I,GAAG;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAzD,IAAA;UAAA;QAAA,GAAAsD,QAAA;MAAA,CACrB;MAAA,gBArBKlB,WAAWA,CAAA;QAAA,OAAAiB,KAAA,CAAA1C,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqBhB;IACD,IAAM0D,OAAO,GAAGtD,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMuD,WAAW,GAAGvD,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMwD,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAAA,IAAAC,qBAAA;MAC3B,OAAO,EAAAA,qBAAA,GAAAF,WAAW,CAAC3J,KAAK,CAAC8J,IAAI,CAAC,UAAC9H,CAAC;QAAA,OAAKA,CAAC,CAAC6E,EAAE,KAAK6C,OAAO,CAAC1J,KAAK;MAAA,EAAC,cAAA6J,qBAAA,uBAArDA,qBAAA,CAAuDpF,IAAI,KAAI,OAAO;IAC/E,CAAC;IACD,IAAMkD,cAAc;MAAA,IAAAoC,KAAA,GAAAhE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsF,SAAA;QAAA,IAAAhC,GAAA;QAAA,OAAA1I,mBAAA,GAAAuB,IAAA,UAAAoJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/E,IAAA,GAAA+E,SAAA,CAAA1G,IAAA;YAAA;cAAA0G,SAAA,CAAA1G,IAAA;cAAA,OACH8C,GAAG,CAAC+B,UAAU,CAAC,0BAA0B,CAAC;YAAA;cAAtDL,GAAG,GAAAkC,SAAA,CAAAjH,IAAA;cACT0G,WAAW,CAAC3J,KAAK,GAAGgI,GAAG,CAACM,IAAI;cAC5BqB,WAAW,CAAC3J,KAAK,CAACmK,OAAO,CAAC;gBAAEtD,EAAE,EAAE,EAAE;gBAAEpC,IAAI,EAAE;cAAQ,CAAC,CAAC;cACpD,IAAIuD,GAAG,CAACM,IAAI,CAACjE,MAAM,GAAG,CAAC,EAAE;gBACvBqF,OAAO,CAAC1J,KAAK,GAAGgI,GAAG,CAACM,IAAI,CAAC,CAAC,CAAC,CAACzB,EAAE;gBAC9BuD,cAAc,CAAC,CAAC;cAClB;YAAC;YAAA;cAAA,OAAAF,SAAA,CAAA5E,IAAA;UAAA;QAAA,GAAA0E,QAAA;MAAA,CACF;MAAA,gBARKrC,cAAcA,CAAA;QAAA,OAAAoC,KAAA,CAAA9D,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQnB;IACD,IAAMqE,WAAW,GAAGjE,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMgE,cAAc;MAAA,IAAAE,KAAA,GAAAvE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6F,SAAA;QAAA,IAAAvC,GAAA;QAAA,OAAA1I,mBAAA,GAAAuB,IAAA,UAAA2J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtF,IAAA,GAAAsF,SAAA,CAAAjH,IAAA;YAAA;cAAAiH,SAAA,CAAAjH,IAAA;cAAA,OACH8C,GAAG,CAAC+B,UAAU,CAAC,6BAA6B,EAAE;gBAC9DqC,iBAAiB,EAAEhB,OAAO,CAAC1J;cAC7B,CAAC,CAAC;YAAA;cAFIgI,GAAG,GAAAyC,SAAA,CAAAxH,IAAA;cAGToH,WAAW,CAACrK,KAAK,GAAGgI,GAAG,CAACM,IAAI;YAAA;YAAA;cAAA,OAAAmC,SAAA,CAAAnF,IAAA;UAAA;QAAA,GAAAiF,QAAA;MAAA,CAC7B;MAAA,gBALKH,cAAcA,CAAA;QAAA,OAAAE,KAAA,CAAArE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKnB;IACD,IAAM2E,QAAQ,GAAGvE,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMwE,UAAU,GAAGxE,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMyE,YAAY;MAAA,IAAAC,KAAA,GAAA/E,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqG,SAAA;QAAA,IAAA/C,GAAA;QAAA,OAAA1I,mBAAA,GAAAuB,IAAA,UAAAmK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9F,IAAA,GAAA8F,SAAA,CAAAzH,IAAA;YAAA;cAAAyH,SAAA,CAAAzH,IAAA;cAAA,OACD8C,GAAG,CAAC+B,UAAU,CAAC,iCAAiC,CAAC;YAAA;cAA7DL,GAAG,GAAAiD,SAAA,CAAAhI,IAAA;cACT2H,UAAU,CAAC5K,KAAK,GAAGgI,GAAG,CAACM,IAAI;cAC3BqC,QAAQ,CAAC3K,KAAK,GAAG,IAAI;YAAA;YAAA;cAAA,OAAAiL,SAAA,CAAA3F,IAAA;UAAA;QAAA,GAAAyF,QAAA;MAAA,CACtB;MAAA,gBAJKF,YAAYA,CAAA;QAAA,OAAAC,KAAA,CAAA7E,KAAA,OAAAD,SAAA;MAAA;IAAA,GAIjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}