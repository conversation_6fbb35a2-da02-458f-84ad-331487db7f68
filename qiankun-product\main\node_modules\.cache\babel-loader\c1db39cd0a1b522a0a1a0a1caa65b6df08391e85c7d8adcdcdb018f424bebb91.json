{"ast": null, "code": "import { createVNode as _createVNode, vShow as _vShow, createElementVNode as _createElementVNode, withDirectives as _withDirectives, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalChat\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalChatBody\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalChatBody\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalChatBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"GlobalChatBox\",\n    onContextmenu: _cache[2] || (_cache[2] = _withModifiers(function () {}, [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createVNode($setup[\"GlobalChatNav\"], {\n    modelValue: $setup.navId,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.navId = $event;\n    }),\n    chatTotal: $setup.chatTotal,\n    onChange: $setup.handleChange\n  }, null, 8 /* PROPS */, [\"modelValue\", \"chatTotal\"]), _withDirectives(_createElementVNode(\"div\", _hoisted_2, [_createVNode($setup[\"GlobalChatView\"], {\n    ref: \"chatViewRef\",\n    modelValue: $setup.chatId,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.chatId = $event;\n    }),\n    chatList: $setup.chatList,\n    onTime: $setup.handleTime,\n    onRefresh: $setup.handleRefresh,\n    onSend: $setup.handleSend\n  }, null, 8 /* PROPS */, [\"modelValue\", \"chatList\"])], 512 /* NEED_PATCH */), [[_vShow, $setup.navId === '1']]), _withDirectives(_createElementVNode(\"div\", _hoisted_3, [_createVNode($setup[\"GlobalChatAddressBook\"], {\n    ref: \"addressBookRef\",\n    onSend: $setup.handleSend\n  }, null, 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */), [[_vShow, $setup.navId === '2']]), _withDirectives(_createElementVNode(\"div\", _hoisted_4, [_createVNode($setup[\"GlobalChatGroup\"], {\n    ref: \"groupRef\",\n    onSend: $setup.handleSend\n  }, null, 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */), [[_vShow, $setup.navId === '3']])])], 32 /* NEED_HYDRATION */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "onContextmenu", "_cache", "_withModifiers", "_createElementVNode", "_hoisted_1", "_createVNode", "$setup", "modelValue", "navId", "$event", "chatTotal", "onChange", "handleChange", "_hoisted_2", "ref", "chatId", "chatList", "onTime", "handleTime", "onRefresh", "handleRefresh", "onSend", "handleSend", "_hoisted_3", "_hoisted_4"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\GlobalChat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatBox\" @contextmenu.prevent>\r\n    <div class=\"GlobalChat\">\r\n      <GlobalChatNav v-model=\"navId\" :chatTotal=\"chatTotal\" @change=\"handleChange\"></GlobalChatNav>\r\n      <div class=\"GlobalChatBody\" v-show=\"navId === '1'\">\r\n        <GlobalChatView ref=\"chatViewRef\" v-model=\"chatId\" :chatList=\"chatList\" @time=\"handleTime\"\r\n          @refresh=\"handleRefresh\" @send=\"handleSend\"></GlobalChatView>\r\n      </div>\r\n      <div class=\"GlobalChatBody\" v-show=\"navId === '2'\">\r\n        <GlobalChatAddressBook ref=\"addressBookRef\" @send=\"handleSend\"></GlobalChatAddressBook>\r\n      </div>\r\n      <div class=\"GlobalChatBody\" v-show=\"navId === '3'\">\r\n        <GlobalChatGroup ref=\"groupRef\" @send=\"handleSend\"></GlobalChatGroup>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChat' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, watch, onUnmounted, defineAsyncComponent } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { handleChatId, handleChatList } from './js/ChatMethod.js'\r\nconst GlobalChatNav = defineAsyncComponent(() => import('./components/GlobalChatNav.vue'))\r\nconst GlobalChatView = defineAsyncComponent(() => import('./components/GlobalChatView.vue'))\r\nconst GlobalChatAddressBook = defineAsyncComponent(() => import('./components/GlobalChatAddressBook.vue'))\r\nconst GlobalChatGroup = defineAsyncComponent(() => import('./components/GlobalChatGroup.vue'))\r\nconst store = useStore()\r\nconst emit = defineEmits(['callback'])\r\nconst rongCloudToken = computed(() => store.getters.getRongCloudToken)\r\nconst navId = ref('1')\r\nconst chatId = ref('')\r\nconst chatList = ref([])\r\nconst chatTotal = computed(() => {\r\n  let total = 0\r\n  for (let index = 0; index < chatList.value.length; index++) {\r\n    const item = chatList.value[index]\r\n    if (item.isNotInform !== 1) total += item.count\r\n  }\r\n  emit('callback', total)\r\n  return total\r\n})\r\nconst chatViewRef = ref()\r\nconst addressBookRef = ref()\r\nconst groupRef = ref()\r\nconst refreshTime = ref('')\r\nconst chatObjectInfo = ref([])\r\nconst rongCloudLink = async (token) => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.addEventListener(Events.CONNECTED, () => {\r\n    console.log('链接成功')\r\n    handleEventListener()\r\n    getRongCloudSessionList()\r\n  })\r\n  await RongIMLib.connect(token)\r\n}\r\n// const handleMessages = async (conversationType, targetId) => {\r\n//   const res = await RongIMLib.getConversation({ conversationType, targetId })\r\n//   return res\r\n// }\r\nconst handleEventListener = async () => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.addEventListener(Events.MESSAGES, async (evt) => {\r\n    console.log('新消息来了', evt.messages)\r\n    const newData = []\r\n    const newDataId = []\r\n    for (let index = 0; index < evt.messages.length; index++) {\r\n      const item = evt.messages[index]\r\n      if (!newDataId?.includes(item.targetId)) {\r\n        newDataId.push(item.targetId)\r\n        // const { code, data } = await handleMessages(item.conversationType, item.targetId)\r\n        const { code, data } = await RongIMLib.getConversation({\r\n          conversationType: item.conversationType,\r\n          targetId: item.targetId\r\n        })\r\n        if (data?.targetId === chatId.value) {\r\n          chatViewRef.value?.getNewestMessages()\r\n          if (!code) newData.push({ ...data, unreadMessageCount: 0 })\r\n        } else {\r\n          if (!code) newData.push(data)\r\n        }\r\n      }\r\n    }\r\n    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')\r\n    chatObjectInfo.value = await handleChatId(newData, isRefresh, chatObjectInfo.value)\r\n    chatList.value = await handleChatList(newData, [], chatList.value, chatObjectInfo.value)\r\n    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')\r\n  })\r\n}\r\nconst handleTime = (type) => {\r\n  refreshTime.value = type ? format(new Date(), 'YYYY-MM-DD HH') : ''\r\n}\r\nconst handleChange = (id) => {\r\n  if (id === '2') addressBookRef.value?.refresh()\r\n  if (id === '3') groupRef.value?.refresh()\r\n}\r\nconst handleRefresh = (type, data) => {\r\n  if (type === 'del') chatList.value = chatList.value.filter((v) => v.id !== data.id)\r\n  getRongCloudSessionList()\r\n}\r\nconst getRongCloudSessionList = async () => {\r\n  const { code, data, msg } = await RongIMLib.getConversationList()\r\n  if (code === 0) {\r\n    console.log('获取会话列表成功', data)\r\n    const newTemporary = []\r\n    for (let index = 0; index < chatList.value.length; index++) {\r\n      const item = chatList.value[index]\r\n      if (item.isTemporary) newTemporary.push(item)\r\n    }\r\n    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')\r\n    chatObjectInfo.value = await handleChatId(data, isRefresh, chatObjectInfo.value)\r\n    chatList.value = await handleChatList(data, newTemporary, [], chatObjectInfo.value)\r\n    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')\r\n  } else {\r\n    console.log('获取会话列表失败: ', code, msg)\r\n  }\r\n}\r\nconst handleSend = (data) => {\r\n  const idList = chatList.value.map((v) => v.id)\r\n  if (idList?.includes(data.id)) {\r\n    chatId.value = data.id\r\n    navId.value = '1'\r\n  } else {\r\n    chatId.value = data.id\r\n    chatList.value = [data, ...chatList.value]\r\n    navId.value = '1'\r\n  }\r\n}\r\nonUnmounted(() => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.removeEventListeners(Events.MESSAGES)\r\n  RongIMLib.removeEventListeners(Events.CONNECTED)\r\n  RongIMLib.disconnect().then(() => {\r\n    console.log('成功断开')\r\n  })\r\n})\r\nwatch(\r\n  () => rongCloudToken.value,\r\n  () => {\r\n    if (rongCloudToken.value) rongCloudLink(rongCloudToken.value)\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalChatBox {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .GlobalChat {\r\n    width: 980px;\r\n    min-height: 560px;\r\n    max-height: 720px;\r\n  }\r\n}\r\n\r\n.GlobalChat {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  position: relative;\r\n\r\n  .zy-el-image {\r\n    -webkit-touch-callout: none;\r\n    -webkit-user-select: none;\r\n    -khtml-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n  }\r\n\r\n  .GlobalChatBody {\r\n    width: calc(100% - 72px);\r\n    height: 100%;\r\n\r\n    .GlobalChatView {\r\n      .GlobalChatViewList {\r\n        .GlobalChatViewListHead {\r\n          height: 56px;\r\n        }\r\n\r\n        .GlobalChatViewMessagesList {\r\n          height: calc(100% - 92px);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewDrag {\r\n        height: 56px;\r\n      }\r\n\r\n      .GlobalChatWindow {\r\n        .GlobalChatWindowTitle {\r\n          height: 56px;\r\n\r\n          .GlobalChatWindowMore {\r\n            margin: 0;\r\n          }\r\n        }\r\n\r\n        .GlobalChatWindowScroll {\r\n          height: calc(100% - 222px);\r\n\r\n          &.GlobalChatWindowNoChat {\r\n            height: calc(100% - (56px + var(--zy-height)));\r\n          }\r\n        }\r\n\r\n        .setting-popup-window {\r\n          height: calc(100% - 56px);\r\n          top: 56px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatAddressBook {\r\n      .GlobalChatAddressBookList {\r\n        .GlobalChatAddressBookInput {\r\n          height: 56px;\r\n        }\r\n\r\n        .GlobalChatAddressBookScrollbar {\r\n          height: calc(100% - 56px);\r\n        }\r\n      }\r\n\r\n      .GlobalChatAddressBookDrag {\r\n        height: 56px;\r\n      }\r\n    }\r\n\r\n    .GlobalChatGroup {\r\n      .GlobalChatGroupList {\r\n        .GlobalChatGroupInput {\r\n          height: 56px;\r\n        }\r\n\r\n        .GlobalChatGroupScrollbar {\r\n          height: calc(100% - 56px);\r\n        }\r\n      }\r\n\r\n      .GlobalChatGroupDrag {\r\n        height: 56px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAgB;;EAItBA,KAAK,EAAC;AAAgB;;EAGtBA,KAAK,EAAC;AAAgB;;uBAV/BC,mBAAA,CAcM;IAdDD,KAAK,EAAC,eAAe;IAAEE,aAAW,EAAAC,MAAA,QAAAA,MAAA,MADzCC,cAAA,CAC6B,cAAoB;MAC7CC,mBAAA,CAYM,OAZNC,UAYM,GAXJC,YAAA,CAA6FC,MAAA;IAHnGC,UAAA,EAG8BD,MAAA,CAAAE,KAAK;IAHnC,uBAAAP,MAAA,QAAAA,MAAA,gBAAAQ,MAAA;MAAA,OAG8BH,MAAA,CAAAE,KAAK,GAAAC,MAAA;IAAA;IAAGC,SAAS,EAAEJ,MAAA,CAAAI,SAAS;IAAGC,QAAM,EAAEL,MAAA,CAAAM;wEAC/DT,mBAAA,CAGM,OAHNU,UAGM,GAFJR,YAAA,CAC+DC,MAAA;IAD/CQ,GAAG,EAAC,aAAa;IALzCP,UAAA,EAKmDD,MAAA,CAAAS,MAAM;IALzD,uBAAAd,MAAA,QAAAA,MAAA,gBAAAQ,MAAA;MAAA,OAKmDH,MAAA,CAAAS,MAAM,GAAAN,MAAA;IAAA;IAAGO,QAAQ,EAAEV,MAAA,CAAAU,QAAQ;IAAGC,MAAI,EAAEX,MAAA,CAAAY,UAAU;IACtFC,SAAO,EAAEb,MAAA,CAAAc,aAAa;IAAGC,MAAI,EAAEf,MAAA,CAAAgB;yFAFAhB,MAAA,CAAAE,KAAK,U,mBAIzCL,mBAAA,CAEM,OAFNoB,UAEM,GADJlB,YAAA,CAAuFC,MAAA;IAAhEQ,GAAG,EAAC,gBAAgB;IAAEO,MAAI,EAAEf,MAAA,CAAAgB;oEADjBhB,MAAA,CAAAE,KAAK,U,mBAGzCL,mBAAA,CAEM,OAFNqB,UAEM,GADJnB,YAAA,CAAqEC,MAAA;IAApDQ,GAAG,EAAC,UAAU;IAAEO,MAAI,EAAEf,MAAA,CAAAgB;oEADLhB,MAAA,CAAAE,KAAK,U", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}