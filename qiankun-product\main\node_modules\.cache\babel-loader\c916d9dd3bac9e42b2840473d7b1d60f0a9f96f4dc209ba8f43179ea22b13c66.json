{"ast": null, "code": "import { createVNode as _createVNode, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, createTextVNode as _createTextVNode, resolveDirective as _resolveDirective, createBlock as _createBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"VersionComparisonAi\"\n};\nvar _hoisted_2 = {\n  class: \"VersionComparisonBody\"\n};\nvar _hoisted_3 = {\n  class: \"VersionComparisonWordBody\"\n};\nvar _hoisted_4 = {\n  class: \"VersionComparisonDataBody\"\n};\nvar _hoisted_5 = {\n  class: \"VersionComparisonDataTabs\"\n};\nvar _hoisted_6 = {\n  class: \"VersionComparisonDataContent\"\n};\nvar _hoisted_7 = {\n  class: \"VersionComparisonDataContentList\"\n};\nvar _hoisted_8 = {\n  class: \"VersionComparisonDataContentItemInfoBox\"\n};\nvar _hoisted_9 = {\n  class: \"VersionComparisonDataContentItemInfo\"\n};\nvar _hoisted_10 = {\n  class: \"VersionComparisonDataContentItemInfo\"\n};\nvar _hoisted_11 = {\n  class: \"VersionComparisonDataDownload\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_radio_button = _resolveComponent(\"el-radio-button\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_withDirectives((_openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"VersionComparisonScrollbar\",\n    \"lement-loading-text\": $setup.loadingText\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode($setup[\"VersionComparisonAiFileInfo\"], {\n        name: \"原文\",\n        id: $setup.oldId,\n        showType: $setup.showType,\n        comparisonHtml: $setup.comparisonHtmlOld,\n        onLoadCallback: $setup.oldCallback\n      }, null, 8 /* PROPS */, [\"id\", \"showType\", \"comparisonHtml\"]), _createVNode($setup[\"VersionComparisonAiFileInfo\"], {\n        name: \"润色后\",\n        id: $setup.newId,\n        showType: $setup.showType,\n        comparisonHtml: $setup.comparisonHtmlNew,\n        onLoadCallback: $setup.newCallback\n      }, null, 8 /* PROPS */, [\"id\", \"showType\", \"comparisonHtml\"])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" <div class=\\\"VersionComparisonDataHeader\\\">\\r\\n            <el-button type=\\\"primary\\\"\\r\\n                       @click=\\\"wordApiTextComparison\\\">开始比对</el-button>\\r\\n          </div> \"), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_radio_group, {\n        modelValue: $setup.radio,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.radio = $event;\n        }),\n        size: \"large\",\n        onChange: $setup.handleSelect\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_radio_button, {\n            label: \"全部\",\n            value: \"1\"\n          }), _createVNode(_component_el_radio_button, {\n            label: \"删除\",\n            value: \"2\"\n          }), _createVNode(_component_el_radio_button, {\n            label: \"新增\",\n            value: \"3\"\n          }), _createVNode(_component_el_radio_button, {\n            label: \"修改\",\n            value: \"4\"\n          })];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_scrollbar, {\n        class: \"VersionComparisonDataContentScrollbar\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.comparisonList, function (item, index) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"VersionComparisonDataContentItem\",\n              key: index\n            }, [_createElementVNode(\"div\", {\n              class: \"VersionComparisonDataContentItemTitle\",\n              style: _normalizeStyle({\n                color: item.color\n              })\n            }, _toDisplayString(index + 1) + \".\" + _toDisplayString(item.status === '0' ? '修改' : item.status === '1' ? '新增' : '删除'), 5 /* TEXT, STYLE */), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, \"原文：\" + _toDisplayString(item.old_word), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, \"差异：\" + _toDisplayString(item.new_word), 1 /* TEXT */)])]);\n          }), 128 /* KEYED_FRAGMENT */))])];\n        }),\n        _: 1 /* STABLE */\n      })]), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.handleFileDownload\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"下载比对结果\")]);\n        }),\n        _: 1 /* STABLE */\n      })])])])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"lement-loading-text\"])), [[_directive_loading, $setup.loading]]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"下载\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"DownloadFileName\"], {\n        onCallback: $setup.callback\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createBlock", "_component_el_scrollbar", "$setup", "loadingText", "default", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "name", "id", "oldId", "showType", "comparisonHtml", "comparisonHtmlOld", "onLoadCallback", "oldCallback", "newId", "comparisonHtmlNew", "newCallback", "_hoisted_4", "_createCommentVNode", "_hoisted_5", "_component_el_radio_group", "modelValue", "radio", "_cache", "$event", "size", "onChange", "handleSelect", "_component_el_radio_button", "label", "value", "_", "_hoisted_6", "_hoisted_7", "_Fragment", "_renderList", "comparisonList", "item", "index", "key", "style", "_normalizeStyle", "color", "_toDisplayString", "status", "_hoisted_8", "_hoisted_9", "old_word", "_hoisted_10", "new_word", "_hoisted_11", "_component_el_button", "type", "onClick", "handleFileDownload", "_createTextVNode", "loading", "_component_xyl_popup_window", "show", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\Intelligentize\\VersionComparison\\VersionComparisonAi.vue"], "sourcesContent": ["<template>\r\n  <div class=\"VersionComparisonAi\">\r\n    <el-scrollbar class=\"VersionComparisonScrollbar\"\r\n                  v-loading=\"loading\"\r\n                  :lement-loading-text=\"loadingText\">\r\n      <div class=\"VersionComparisonBody\">\r\n        <div class=\"VersionComparisonWordBody\">\r\n          <VersionComparisonAiFileInfo name=\"原文\"\r\n                                     :id=\"oldId\"\r\n                                     :showType=\"showType\"\r\n                                     :comparisonHtml=\"comparisonHtmlOld\"\r\n                                     @loadCallback=\"oldCallback\"></VersionComparisonAiFileInfo>\r\n          <VersionComparisonAiFileInfo name=\"润色后\"\r\n                                     :id=\"newId\"\r\n                                     :showType=\"showType\"\r\n                                     :comparisonHtml=\"comparisonHtmlNew\"\r\n                                     @loadCallback=\"newCallback\"></VersionComparisonAiFileInfo>\r\n        </div>\r\n        <div class=\"VersionComparisonDataBody\">\r\n          <!-- <div class=\"VersionComparisonDataHeader\">\r\n            <el-button type=\"primary\"\r\n                       @click=\"wordApiTextComparison\">开始比对</el-button>\r\n          </div> -->\r\n          <div class=\"VersionComparisonDataTabs\">\r\n            <el-radio-group v-model=\"radio\"\r\n                            size=\"large\"\r\n                            @change=\"handleSelect\">\r\n              <el-radio-button label=\"全部\"\r\n                               value=\"1\" />\r\n              <el-radio-button label=\"删除\"\r\n                               value=\"2\" />\r\n              <el-radio-button label=\"新增\"\r\n                               value=\"3\" />\r\n              <el-radio-button label=\"修改\"\r\n                               value=\"4\" />\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"VersionComparisonDataContent\">\r\n            <el-scrollbar class=\"VersionComparisonDataContentScrollbar\">\r\n              <div class=\"VersionComparisonDataContentList\">\r\n                <div class=\"VersionComparisonDataContentItem\"\r\n                     v-for=\"(item, index) in comparisonList\"\r\n                     :key=\"index\">\r\n                  <div class=\"VersionComparisonDataContentItemTitle\"\r\n                       :style=\"{ color: item.color}\">{{ index + 1 }}.{{ item.status === '0' ? '修改' : item.status === '1' ? '新增' : '删除' }}</div>\r\n                  <div class=\"VersionComparisonDataContentItemInfoBox\">\r\n                    <div class=\"VersionComparisonDataContentItemInfo\">原文：{{ item.old_word }}</div>\r\n                    <div class=\"VersionComparisonDataContentItemInfo\">差异：{{ item.new_word }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-scrollbar>\r\n          </div>\r\n          <div class=\"VersionComparisonDataDownload\">\r\n            <el-button type=\"primary\"\r\n                       @click=\"handleFileDownload\">下载比对结果</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      name=\"下载\">\r\n      <DownloadFileName @callback=\"callback\"></DownloadFileName>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VersionComparisonAi' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport store from '@/store'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\n// import { extendDownloadFile } from 'common/config/MicroGlobal'\r\nimport { ElMessage } from 'element-plus'\r\nimport VersionComparisonAiFileInfo from './VersionComparisonAiFileInfo'\r\nimport DownloadFileName from './DownloadFileName/DownloadFileName'\r\nconst route = useRoute()\r\nconst show = ref(false)\r\nconst showType = ref(true)\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst comparisonHtmlOld = ref('')\r\nconst comparisonHtmlNew = ref('')\r\nconst radio = ref('1')\r\nconst oldId = ref('')\r\nconst newId = ref('')\r\nconst fileName = ref('')\r\nconst oldFile = ref()\r\nconst newFile = ref()\r\nconst comparisonList = ref([])\r\n\r\nonMounted(() => {\r\n  wordApiTextComparison('1')\r\n})\r\n\r\nconst wordApiTextComparison = async (val) => {\r\n  const param = new FormData()\r\n  param.append('chatId', route.query.chatId )\r\n  const { data } = await api.wordApiTextComparison(param, () => { }, '')\r\n  showType.value = false\r\n  if (data.reviewList) {\r\n    data.reviewList.forEach(item => {\r\n      if (item.status === '0') {\r\n        item.color = '#C4902F'\r\n      } else if (item.status === '1') {\r\n        item.color = '#69A825'\r\n      } else {\r\n        item.color = '#A31818'\r\n      }\r\n    })\r\n  }\r\n   if(val === '1') { // 筛选功能\r\n    comparisonList.value = data.reviewList || []\r\n  }else if(val === '2') {\r\n    comparisonList.value = data.reviewList.filter(item => item.status === '2')\r\n  }else if(val === '3') {\r\n    comparisonList.value = data.reviewList.filter(item => item.status === '1')\r\n  }else if(val === '4') {\r\n    comparisonList.value = data.reviewList.filter(item => item.status === '0')\r\n  }\r\n  comparisonHtmlOld.value = data.oldDoc.html\r\n  comparisonHtmlNew.value = data.newDoc.html\r\n  fileName.value = data.newDoc.fileName\r\n}\r\n\r\nconst handleFileDownload = () => {\r\n  if (!fileName.value) return ElMessage({ type: 'warning', message: '当前没有内容可下载！' })\r\n  show.value = true\r\n}\r\n\r\nconst handleSelect = (val) => {\r\n  wordApiTextComparison(val)\r\n }\r\n\r\nconst oldCallback = (file) => {\r\n  oldFile.value = file\r\n}\r\n\r\nconst newCallback = (file) => {\r\n  newFile.value = file\r\n}\r\n\r\nconst callback = (name) => {\r\n  if (name) {\r\n    store.commit('setExtendDownloadFile', { url: '/wordApi/downloadWord', params: { fileName: fileName.value }, fileSize: 0, fileName: `${name}.docx`, fileType: 'docx' })\r\n   // extendDownloadFile({ url: '/wordApi/downloadWord', params: { fileName: fileName.value }, fileSize: 0, fileName: `${name}.docx`, fileType: 'docx' })\r\n  }\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.VersionComparisonAi {\r\n  width: 100%;\r\n  height: 100vh;\r\n\r\n  .VersionComparisonScrollbar {\r\n    width: 100%;\r\n    height: calc(100vh - 62px);\r\n  }\r\n\r\n  .VersionComparisonBody {\r\n    width: 100%;\r\n    min-width: 1660px;\r\n    height: calc(100vh - 62px);\r\n    background: var(--zy-el-color-info-light-9);\r\n    padding: 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .VersionComparisonWordBody {\r\n      width: calc(100% - 360px);\r\n      margin-right: 10px;\r\n      padding: 10px 0;\r\n      background-color: #fff;\r\n      display: flex;\r\n      justify-content: space-around;\r\n    }\r\n\r\n    .VersionComparisonDataBody {\r\n      width: 350px;\r\n      padding: 10px;\r\n      background-color: #fff;\r\n\r\n      .VersionComparisonDataTabs {\r\n        margin: 10px 0;\r\n      }\r\n\r\n      .VersionComparisonDataContentScrollbar {\r\n        width: 100%;\r\n        height: calc(100vh - 240px);\r\n\r\n        .VersionComparisonDataContentList {\r\n          .VersionComparisonDataContentItem {\r\n            margin-bottom: 10px;\r\n            border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n            .VersionComparisonDataContentItemTitle {\r\n              padding: 10px;\r\n              background: #fafbfb;\r\n              border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n            }\r\n\r\n            .VersionComparisonDataContentItemInfoBox {\r\n              padding: 10px;\r\n              min-height: 60px;\r\n              .VersionComparisonDataContentItemInfo {\r\n                width: 300px;\r\n                margin-bottom: 10px;\r\n                word-wrap: break-word;\r\n                line-height: 20px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .VersionComparisonDataDownload {\r\n        text-align: center;\r\n\r\n        .zy-el-button {\r\n          padding: 10px 30px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAIvBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAA2B;;EAYjCA,KAAK,EAAC;AAA2B;;EAK/BA,KAAK,EAAC;AAA2B;;EAcjCA,KAAK,EAAC;AAA8B;;EAEhCA,KAAK,EAAC;AAAkC;;EAMpCA,KAAK,EAAC;AAAyC;;EAC7CA,KAAK,EAAC;AAAsC;;EAC5CA,KAAK,EAAC;AAAsC;;EAMtDA,KAAK,EAAC;AAA+B;;;;;;;;uBApDlDC,mBAAA,CA+DM,OA/DNC,UA+DM,G,+BA9DJC,YAAA,CAyDeC,uBAAA;IAzDDJ,KAAK,EAAC,4BAA4B;IAEjC,qBAAmB,EAAEK,MAAA,CAAAC;;IAJxCC,OAAA,EAAAC,QAAA,CAKM;MAAA,OAqDM,CArDNC,mBAAA,CAqDM,OArDNC,UAqDM,GApDJD,mBAAA,CAWM,OAXNE,UAWM,GAVJC,YAAA,CAIqFP,MAAA;QAJxDQ,IAAI,EAAC,IAAI;QACVC,EAAE,EAAET,MAAA,CAAAU,KAAK;QACTC,QAAQ,EAAEX,MAAA,CAAAW,QAAQ;QAClBC,cAAc,EAAEZ,MAAA,CAAAa,iBAAiB;QACjCC,cAAY,EAAEd,MAAA,CAAAe;qEAC1CR,YAAA,CAIqFP,MAAA;QAJxDQ,IAAI,EAAC,KAAK;QACXC,EAAE,EAAET,MAAA,CAAAgB,KAAK;QACTL,QAAQ,EAAEX,MAAA,CAAAW,QAAQ;QAClBC,cAAc,EAAEZ,MAAA,CAAAiB,iBAAiB;QACjCH,cAAY,EAAEd,MAAA,CAAAkB;uEAE5Cd,mBAAA,CAuCM,OAvCNe,UAuCM,GAtCJC,mBAAA,4LAGU,EACVhB,mBAAA,CAaM,OAbNiB,UAaM,GAZJd,YAAA,CAWiBe,yBAAA;QAnC7BC,UAAA,EAwBqCvB,MAAA,CAAAwB,KAAK;QAxB1C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAwBqC1B,MAAA,CAAAwB,KAAK,GAAAE,MAAA;QAAA;QACdC,IAAI,EAAC,OAAO;QACXC,QAAM,EAAE5B,MAAA,CAAA6B;;QA1BrC3B,OAAA,EAAAC,QAAA,CA2Bc;UAAA,OAC6B,CAD7BI,YAAA,CAC6BuB,0BAAA;YADZC,KAAK,EAAC,IAAI;YACVC,KAAK,EAAC;cACvBzB,YAAA,CAC6BuB,0BAAA;YADZC,KAAK,EAAC,IAAI;YACVC,KAAK,EAAC;cACvBzB,YAAA,CAC6BuB,0BAAA;YADZC,KAAK,EAAC,IAAI;YACVC,KAAK,EAAC;cACvBzB,YAAA,CAC6BuB,0BAAA;YADZC,KAAK,EAAC,IAAI;YACVC,KAAK,EAAC;;;QAlCrCC,CAAA;2CAqCU7B,mBAAA,CAeM,OAfN8B,UAeM,GAdJ3B,YAAA,CAaeR,uBAAA;QAbDJ,KAAK,EAAC;MAAuC;QAtCvEO,OAAA,EAAAC,QAAA,CAuCc;UAAA,OAWM,CAXNC,mBAAA,CAWM,OAXN+B,UAWM,I,kBAVJvC,mBAAA,CASMwC,SAAA,QAjDtBC,WAAA,CAyC6CrC,MAAA,CAAAsC,cAAc,EAzC3D,UAyC6BC,IAAI,EAAEC,KAAK;iCADxB5C,mBAAA,CASM;cATDD,KAAK,EAAC,kCAAkC;cAEvC8C,GAAG,EAAED;gBACTpC,mBAAA,CAC6H;cADxHT,KAAK,EAAC,uCAAuC;cAC5C+C,KAAK,EA5C7BC,eAAA;gBAAAC,KAAA,EA4CwCL,IAAI,CAACK;cAAK;gCAAMJ,KAAK,QAAO,GAAC,GAAAK,gBAAA,CAAGN,IAAI,CAACO,MAAM,kBAAkBP,IAAI,CAACO,MAAM,+CAC9F1C,mBAAA,CAGM,OAHN2C,UAGM,GAFJ3C,mBAAA,CAA8E,OAA9E4C,UAA8E,EAA5B,KAAG,GAAAH,gBAAA,CAAGN,IAAI,CAACU,QAAQ,kBACrE7C,mBAAA,CAA8E,OAA9E8C,WAA8E,EAA5B,KAAG,GAAAL,gBAAA,CAAGN,IAAI,CAACY,QAAQ,iB;;;QA/CzFlB,CAAA;YAqDU7B,mBAAA,CAGM,OAHNgD,WAGM,GAFJ7C,YAAA,CACyD8C,oBAAA;QAD9CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAEvD,MAAA,CAAAwD;;QAvD/BtD,OAAA,EAAAC,QAAA,CAuDmD;UAAA,OAAMsB,MAAA,QAAAA,MAAA,OAvDzDgC,gBAAA,CAuDmD,QAAM,E;;QAvDzDxB,CAAA;;;IAAAA,CAAA;qEAG6BjC,MAAA,CAAA0D,OAAO,E,GAyDhCnD,YAAA,CAGmBoD,2BAAA;IA/DvBpC,UAAA,EA4D+BvB,MAAA,CAAA4D,IAAI;IA5DnC,uBAAAnC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA4D+B1B,MAAA,CAAA4D,IAAI,GAAAlC,MAAA;IAAA;IACblB,IAAI,EAAC;;IA7D3BN,OAAA,EAAAC,QAAA,CA8DM;MAAA,OAA0D,CAA1DI,YAAA,CAA0DP,MAAA;QAAvC6D,UAAQ,EAAE7D,MAAA,CAAA8D;MAAQ,G;;IA9D3C7B,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}