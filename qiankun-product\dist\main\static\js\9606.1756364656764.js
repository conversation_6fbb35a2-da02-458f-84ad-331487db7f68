"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[9606],{94812:function(e,t,n){n.d(t,{A:function(){return Q}});var r=n(81474),o=(n(76945),n(64352),n(84098)),a=(n(63584),n(44863)),l=(n(4711),n(62427)),c=(n(98773),n(68294)),i=(n(1920),n(8507),n(74061)),u=n(68215),s="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAILSURBVHgB7ZhLUsJAEIYbsNgaT2A8geFVxc54Ar2BeALhBnIC8QR6BD2BLFnwyA0CJzAFO57+HRNMUowkmUClqHybTGYmMz/TPT09EGWcODmSQNd1ZTqdPqCoCLpYxWLxs9frjSkmZyTBbDZ7yeVyjf/6LBYLDY9Hikme5FAT6iNEagW9bDab5nq9tricz+cVrGyHEiBJgR+GYUy4rGmaWigUEhEoa+KDk3qBPhNXq9VnmOqGwqOF6VOpVL4oJPBdo9/vt7bv9CdOh7jQAwVZrVYX8EF7kzg+aFJMMFYJYxlcTsTE+GEdVxyD8pjrKCZYxXO3LNrF74PBIHZwZYbDIZupFbY/3OANj0awPtvFsohMrNTrdZWOCM7snQmHSOA9PrinFLA1MXaORSnElw+Wy+UnCOXgy6tnLznChXFg8bozzxjzdHk+RIDXnQJdnMhvf4gMRR2NRhM6AIGA3kVouw32ycKMLJlAWU5TIF83RfWitrgnU2SBCEEmrpvfiJnNoADUc5tZq9V8iSwnwjiZTHwzoohEEshxi5xrJILqnbdtPp/r9BvcFcTOa2+bm6XzIVAqlS4pAtkmkWXvvRhH0RX8yz4Sl8ulCnPZ9Xj6UjKYeJumB9vgf3vHiy2QL1LeCVzYn9jxPe/etg7aOlHGEyEycWpSr50riP9W2tiJCh0PC1fNNmVkZETnB9e82a+hBsUlAAAAAElFTkSuQmCC",d="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAK5JREFUOE/VlDEOwjAMRbFZGDiPfacOjAwMHTowdORW8YmYbBSJSgHa2IOF1Kz5fom/fwJmdjwkLtgXkJkvZnYSkTniAhFdAeBZSnks+o+Wq0BV74g4icjUgxLRqKojIt7aC/x42Ag3oT3N6lB6Bd6Bm1NeK/Rg1aJubFpAFb896/rr5nCBVmBkWP8FpracOpTU2ESiEQ52+tNj5sHMzmmfQ+SH8TRuDj3A93468AU2mfwVWC+ccgAAAABJRU5ErkJggg==",h="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAAAXNSR0IArs4c6QAAAVJJREFUOE+dk78rhWEUxz/fCMtNBt1Y5BqlDGRWFlkMCgsyKDYmkV+DrqIY+B+u0SKLVRZ/gFLKpJRBkhLH+9zOq+v13vfijM9zzuf7PN9zjvhnmFkzUNBf680sB8wCa8D1rwFm1gJMAFtAqwsf1QSYWUieA9aBhsSLp6sCXHEG2EkpjDndPwBeOA4cAI0JxXegzs+eJeW+AO7qIrCRYuwHcAX0V9ydSxqSmTV50XKVjgTVXWAeCK2LoyhpJQCsRiuHo68Ugd5E3qSkUgC0A2dATyLhFRh05akUkYKk27IHZhbasxA9cd8Tnxw4BuxFvU+a/QDkJdm3CzPLA8GLbaATuADqU9RPJY2E86w5uAG6qvizKSlMZDrAZ+Exw9xRSSdZgAHgEngBSr48lbw2SfdZgD5gFVgKTptZGKBj9+VOUkdMq7lMcaJP6mEEfpMU1rkcn0l9ZBWoPFPKAAAAAElFTkSuQmCC",v=n(4955),p=n(98885),f=(n(35894),n(88609)),m=n(46197),y=n(24296);function A(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */A=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},l=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",i=a.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var a=t&&t.prototype instanceof y?t:y,l=Object.create(a.prototype),c=new T(r||[]);return o(l,"_invoke",{value:V(e,n,c)}),l}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var h="suspendedStart",v="suspendedYield",p="executing",f="completed",m={};function y(){}function g(){}function k(){}var w={};u(w,l,(function(){return this}));var B=Object.getPrototypeOf,E=B&&B(B(K([])));E&&E!==n&&r.call(E,l)&&(w=E);var x=k.prototype=y.prototype=Object.create(w);function C(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function n(o,a,l,c){var i=d(e[o],e,a);if("throw"!==i.type){var u=i.arg,s=u.value;return s&&"object"==typeof s&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,l,c)}),(function(e){n("throw",e,l,c)})):t.resolve(s).then((function(e){u.value=e,l(u)}),(function(e){return n("throw",e,l,c)}))}c(i.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function V(t,n,r){var o=h;return function(a,l){if(o===p)throw Error("Generator is already running");if(o===f){if("throw"===a)throw l;return{value:e,done:!0}}for(r.method=a,r.arg=l;;){var c=r.delegate;if(c){var i=b(c,r);if(i){if(i===m)continue;return i}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=f,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=p;var u=d(t,n,r);if("normal"===u.type){if(o=r.done?f:v,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=f,r.method="throw",r.arg=u.arg)}}}function b(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,b(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var a=d(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,m;var l=a.arg;return l?l.done?(n[t.resultName]=l.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,m):l:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function K(t){if(t||""===t){var n=t[l];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return g.prototype=k,o(x,"constructor",{value:k,configurable:!0}),o(k,"constructor",{value:g,configurable:!0}),g.displayName=u(k,i,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,k):(e.__proto__=k,u(e,i,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},C(N.prototype),u(N.prototype,c,(function(){return this})),t.AsyncIterator=N,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var l=new N(s(e,n,r,o),a);return t.isGeneratorFunction(n)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},C(x),u(x,i,"Generator"),u(x,l,(function(){return this})),u(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=K,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var l=this.tryEntries[a],c=l.completion;if("root"===l.tryLoc)return o("end");if(l.tryLoc<=this.prev){var i=r.call(l,"catchLoc"),u=r.call(l,"finallyLoc");if(i&&u){if(this.prev<l.catchLoc)return o(l.catchLoc,!0);if(this.prev<l.finallyLoc)return o(l.finallyLoc)}else if(i){if(this.prev<l.catchLoc)return o(l.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return o(l.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var l=a?a.completion:{};return l.type=e,l.arg=t,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),L(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:K(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),m}},t}function g(e,t,n,r,o,a,l){try{var c=e[a](l),i=c.value}catch(e){return void n(e)}c.done?t(i):Promise.resolve(i).then(r,o)}function k(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function l(e){g(a,r,o,l,c,"next",e)}function c(e){g(a,r,o,l,c,"throw",e)}l(void 0)}))}}var w={key:0,class:"DouBao-intelligent-assistant"},B={class:"DouBaoIntelligentizeBody"},E={key:0,class:"DouBaoIntelligentizeBodyTop"},x={class:"DouBaoIntelligentizeBodyLeft"},C={key:0,class:"tableModuleContentKey"},N={class:"tableModuleContentKeyWord"},V={key:1,class:"tableModuleContentAnswer"},b={class:"tableModuleItem"},S={key:0,class:"tableModuleItemContent"},L={key:0},T={key:1,class:"loader3"},K=["innerHTML"],O=["innerHTML"],F={key:1,class:"loader3"},R=["onClick"],J={key:2,class:"tableModuleContentAnswer"},U={class:"tableModuleItem"},z=["innerHTML"],P={key:0,class:"loader3"},M={key:0,class:"tableModuleContentFollow"},I=["onClick"],Y={key:0,class:"wordShow"},q={class:"DouBaoIntelligentizeSeach"},D={name:"DouBaoIntelligentize"},W=Object.assign(D,{props:{keyword:{type:String,default:""},typeShow:{type:Boolean,default:""},showMessage:{type:String,default:""}},emits:["checkOutFun"],setup(e,t){var n=t.expose,g=t.emit,D=e,W=(0,i.ref)(""),H=(0,i.ref)(),Q=(0,i.ref)(""),j=g,Z=(0,i.ref)(!1),G=(0,i.ref)(!1),X=(0,i.ref)(""),_=(0,i.ref)([]),$=(0,i.ref)("0"),ee=(0,i.ref)(),te=(0,i.ref)("1"),ne=(0,i.computed)((function(){var e;return(null===(e=f.FR.value)||void 0===e?void 0:e.systemPlatform)||""})),re=(0,i.ref)([{value:"1",label:"coze"},{value:"2",label:"DeepSeek(满血)"},{value:"3",label:"DeepSeek(本地部署)"}]),oe=(0,i.ref)(new AbortController);(0,i.onMounted)((function(){le()})),(0,i.onUnmounted)((function(){}));var ae=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,n="x"==e?t:3&t|8;return n.toString(16)}))},le=function(){var e=k(A().mark((function e(){return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:$.value=D.showMessage,(0,i.nextTick)((function(){ce()}));case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ce=function(){var e=k(A().mark((function e(){var t,n,r,o,a,l,c,u,s=arguments;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]?s[0]:"",oe.value.abort(),oe.value=new AbortController,n=oe.value.signal,r=sessionStorage.getItem("token")||"",0!=G.value||D.typeShow||"1"!=$.value){e.next=13;break}return _.value.push({uid:c,copyShow:!1,contentKey:'<span style="font-weight:bold;font-size:1.2em">您好！我是您的小助手</span><br><span style="font-size:1em;color:#464646;">您可以向我提出问题，我将为您做出内容推荐和解答。</span><br><span style="font-size:.9em;color:#a5a6a6;">您可以试着问我：</span>',children:[],type:"answer"}),e.next=9,v.A.initMessage({bot_type:"NPC"==ne.value?"6":"7"});case 9:o=e.sent,a=o.data,_.value.push({uid:u,copyShow:!1,contentKey:"",children:a,type:"follow"}),G.value=!0;case 13:return l=ae(),c=ae(),u=ae(),_.value.push({uid:l,contentKey:t,contentKey1:"",showKey1:!0,children:[],type:"title"}),_.value.push({uid:c,contentKey:"",children:[],type:"answer"}),_.value.push({uid:u,contentKey:"",children:[],type:"follow"}),Q.value=c,X.value=u,Z.value=!0,_.value[_.value.length-2].contentKey1="",_.value[_.value.length-2].showKey1=!0,e.next=26,(0,y.y)(`${m.A.API_URL}${1==te.value?"/chat/intelligentStream":2==te.value?"/chat/interactiveQnAChat":"/chat/interactiveQnAEndpoint"}`,{method:"POST",headers:{"Content-Type":"application/json",authorization:r},body:JSON.stringify({content:t,bot_type:1==te.value?D.typeShow?"5":"NPC"==ne.value?"7":"6":""}),openWhenHidden:!0,signal:n,onmessage:function(e){if(2==te.value||3==te.value){var t,n;if(JSON.parse(e.data).think&&""!=JSON.parse(e.data).think)_.value[_.value.length-2].contentKey1+=(null===(t=JSON.parse(e.data))||void 0===t?void 0:t.think)||"";else _.value[_.value.length-2].contentKey+=(null===(n=JSON.parse(e.data))||void 0===n?void 0:n.response)||"",_.value[_.value.length-2].contentKey=_.value[_.value.length-2].contentKey.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),_.value[_.value.length-2].copyShow=!0;(0,i.nextTick)((function(){de()}))}else{var r,o=JSON.parse(e.data);if(["conversation.message.delta"].includes(o.event)&&["answer"].includes(o.data.type)){if(Q.value===c)_.value[_.value.length-2].contentKey+=(null===(r=JSON.parse(e.data))||void 0===r||null===(r=r.data)||void 0===r?void 0:r.content)||"",_.value[_.value.length-2].contentKey=_.value[_.value.length-2].contentKey.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>")}else if(["conversation.message.completed"].includes(o.event)&&["follow_up"].includes(o.data.type)){var a;if(X.value===u)_.value[_.value.length-1].children.push((null===(a=JSON.parse(e.data))||void 0===a||null===(a=a.data)||void 0===a?void 0:a.content)||"")}(0,i.nextTick)((function(){de()}))}},onclose:function(e){var t=/[\u4e00-\u9fff]/,n=t.test(_.value[_.value.length-2].contentKey1);_.value[_.value.length-2].showKey1=n,Z.value=!1},onerror:function(e){throw _.value.push({uid:c,contentKey:"系统繁忙。。。",children:[],type:"answer"}),Z.value=!1,e}});case 26:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ie=function(){var e=k(A().mark((function e(t){var n,r,o;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,n=t.contentKey.replace(/<\/?think>/g,""),r=n.replace(/<\/?strong>/g,""),e.next=5,navigator.clipboard.writeText(r);case 5:(0,p.nk)({message:"复制成功",type:"success"}),e.next=20;break;case 8:e.prev=8,e.t0=e["catch"](0),o=document.createElement("textarea"),o.value=t.contentKey,o.setAttribute("readonly",""),o.style.position="absolute",o.style.left="-9999px",document.body.appendChild(o),o.select(),document.execCommand("copy"),document.body.removeChild(o),(0,p.nk)({message:"复制成功",type:"success"});case 20:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),ue=function(){_.value=[]},se=function(){j("checkOutFun")},de=function(){var e=k(A().mark((function e(){var t;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=ee.value.wrapRef,t.scrollTop=t.scrollHeight,e.next=4,(0,i.nextTick)();case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),he=function(){""!=W.value&&(Z.value=!1,oe.value.abort(),oe.value=new AbortController,W.value&&ve(W.value))},ve=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";W.value="",oe.value.abort(),(0,i.nextTick)((function(){de()})),ce(e)};return n({handleQuery:he,textClick:ve}),function(t,n){var v=c.P9,p=c.AV,f=(0,i.resolveComponent)("Document"),m=l.tk,y=(0,i.resolveComponent)("Right"),A=a.kA,g=o.WK,k=r.S2;return(0,i.openBlock)(),(0,i.createElementBlock)("div",{class:"DouBaoIntelligentize card",style:(0,i.normalizeStyle)(""+(e.typeShow?"":"border-radius: 0;"))},[e.typeShow?((0,i.openBlock)(),(0,i.createElementBlock)("div",w)):(0,i.createCommentVNode)("",!0),(0,i.createElementVNode)("div",B,[e.typeShow?(0,i.createCommentVNode)("",!0):((0,i.openBlock)(),(0,i.createElementBlock)("div",E,[(0,i.createElementVNode)("div",x,[n[5]||(n[5]=(0,i.createElementVNode)("img",{src:u,alt:""},null,-1)),n[6]||(n[6]=(0,i.createElementVNode)("div",{class:"LeftText"},"智能问答",-1)),(0,i.createVNode)(p,{modelValue:te.value,"onUpdate:modelValue":n[0]||(n[0]=function(e){return te.value=e}),class:"LeftSelect",placeholder:"请选择"},{default:(0,i.withCtx)((function(){return[((0,i.openBlock)(!0),(0,i.createElementBlock)(i.Fragment,null,(0,i.renderList)(re.value,(function(e){return(0,i.openBlock)(),(0,i.createBlock)(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]),(0,i.createElementVNode)("div",{class:"DouBaoIntelligentizeBodyRight"},[(0,i.createElementVNode)("img",{onClick:ue,class:"RightImg",src:s,alt:""}),(0,i.createElementVNode)("img",{onClick:se,src:d,alt:""})])])),(0,i.createVNode)(A,{ref_key:"scrollbarRef",ref:ee},{default:(0,i.withCtx)((function(){return[((0,i.openBlock)(!0),(0,i.createElementBlock)(i.Fragment,null,(0,i.renderList)(_.value,(function(t){var r;return(0,i.openBlock)(),(0,i.createElementBlock)("div",{class:"tableModule",key:t.uid},["title"===t.type?((0,i.openBlock)(),(0,i.createElementBlock)(i.Fragment,{key:0},[t.contentKey?((0,i.openBlock)(),(0,i.createElementBlock)("div",C,[(0,i.createElementVNode)("div",N,(0,i.toDisplayString)(t.contentKey),1)])):(0,i.createCommentVNode)("",!0)],64)):(0,i.createCommentVNode)("",!0),"answer"===t.type?((0,i.openBlock)(),(0,i.createElementBlock)("div",V,[(0,i.createElementVNode)("div",b,[2==te.value||3==te.value&&t.showKey1?((0,i.openBlock)(),(0,i.createElementBlock)("div",S,[Z.value&&Q.value===t.uid?((0,i.openBlock)(),(0,i.createElementBlock)("span",L,"深度思考中")):(0,i.createCommentVNode)("",!0),Z.value&&Q.value===t.uid?((0,i.openBlock)(),(0,i.createElementBlock)("div",T,n[7]||(n[7]=[(0,i.createElementVNode)("div",{class:"circle1"},null,-1),(0,i.createElementVNode)("div",{class:"circle1"},null,-1),(0,i.createElementVNode)("div",{class:"circle1"},null,-1)]))):(0,i.createCommentVNode)("",!0),(0,i.createElementVNode)("span",{class:"ContentText",innerHTML:t.contentKey1},null,8,K)])):(0,i.createCommentVNode)("",!0),(0,i.createElementVNode)("span",{class:"ContentTextTow",innerHTML:t.contentKey},null,8,O),Z.value&&Q.value===t.uid?((0,i.openBlock)(),(0,i.createElementBlock)("div",F,n[8]||(n[8]=[(0,i.createElementVNode)("div",{class:"circle1"},null,-1),(0,i.createElementVNode)("div",{class:"circle1"},null,-1),(0,i.createElementVNode)("div",{class:"circle1"},null,-1)]))):(0,i.createCommentVNode)("",!0),2==te.value||3==te.value?((0,i.openBlock)(),(0,i.createElementBlock)(i.Fragment,{key:2},[t.copyShow&&!Z.value?((0,i.openBlock)(),(0,i.createElementBlock)("button",{key:0,class:"copyButton",onClick:function(e){return ie(t)}},[(0,i.createVNode)(m,null,{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(f)]})),_:1}),n[9]||(n[9]=(0,i.createTextVNode)(" 复制 "))],8,R)):(0,i.createCommentVNode)("",!0)],64)):(0,i.createCommentVNode)("",!0)])])):(0,i.createCommentVNode)("",!0),"answerMin"===t.type?((0,i.openBlock)(),(0,i.createElementBlock)("div",J,[(0,i.createElementVNode)("div",U,[(0,i.createElementVNode)("span",{innerHTML:t.contentKey},null,8,z),Z.value&&Q.value===t.uid?((0,i.openBlock)(),(0,i.createElementBlock)("div",P,n[10]||(n[10]=[(0,i.createElementVNode)("div",{class:"circle1"},null,-1),(0,i.createElementVNode)("div",{class:"circle1"},null,-1),(0,i.createElementVNode)("div",{class:"circle1"},null,-1)]))):(0,i.createCommentVNode)("",!0)])])):(0,i.createCommentVNode)("",!0),"follow"===t.type?((0,i.openBlock)(),(0,i.createElementBlock)(i.Fragment,{key:3},[null!==(r=t.children)&&void 0!==r&&r.length?((0,i.openBlock)(),(0,i.createElementBlock)("div",M,[((0,i.openBlock)(!0),(0,i.createElementBlock)(i.Fragment,null,(0,i.renderList)(t.children,(function(t){return(0,i.openBlock)(),(0,i.createElementBlock)("div",{class:"tableModuleContentFollowWord",key:t,onClick:function(e){return ve(t)}},[e.typeShow?(0,i.createCommentVNode)("",!0):((0,i.openBlock)(),(0,i.createElementBlock)("div",Y," # ")),(0,i.createTextVNode)(" "+(0,i.toDisplayString)(t)+" ",1),(0,i.createVNode)(m,null,{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(y)]})),_:1})],8,I)})),128))])):(0,i.createCommentVNode)("",!0)],64)):(0,i.createCommentVNode)("",!0)])})),128))]})),_:1},512)]),(0,i.createElementVNode)("div",q,[e.typeShow?((0,i.openBlock)(),(0,i.createBlock)(g,{key:0,modelValue:W.value,"onUpdate:modelValue":n[1]||(n[1]=function(e){return W.value=e}),ref_key:"textareaRef",ref:H,type:"textarea",autosize:{minRows:1,maxRows:6},onKeyup:(0,i.withKeys)(he,["enter"]),resize:"none"},null,8,["modelValue"])):(0,i.createCommentVNode)("",!0),e.typeShow?(0,i.createCommentVNode)("",!0):((0,i.openBlock)(),(0,i.createBlock)(g,{key:1,modelValue:W.value,"onUpdate:modelValue":n[2]||(n[2]=function(e){return W.value=e}),ref_key:"textareaRef",ref:H,type:"textarea",maxlength:"500",placeholder:"请输入你的问题",rows:3,onKeyup:(0,i.withKeys)(he,["enter"]),resize:"none"},null,8,["modelValue"])),e.typeShow?((0,i.openBlock)(),(0,i.createBlock)(k,{key:2,type:"primary",class:"DouBaoIntelligentizeButton",icon:"Position",circle:"",onClick:n[3]||(n[3]=function(e){return he()})})):(0,i.createCommentVNode)("",!0),e.typeShow?(0,i.createCommentVNode)("",!0):((0,i.openBlock)(),(0,i.createBlock)(k,{key:3,type:"primary",class:"sendingButton",circle:"",onClick:n[4]||(n[4]=function(e){return he()})},{default:(0,i.withCtx)((function(){return n[11]||(n[11]=[(0,i.createElementVNode)("img",{src:h,alt:""},null,-1),(0,i.createTextVNode)(" 发送")])})),_:1}))])],4)}}});const H=W;var Q=H},94863:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAARISURBVHgB1VldchNHEO5e/cQOSkWcAOUEkauwizwhVbCTVB4wJ0C5ATmBpBPEnABzgsATIa5E9htYrrJuwHICq8CWsHZ3mu5Zy94/a2d3Zar4qvQzo5npb/pvekcAXwkQCuLj3r1WCb0fSWGTm3VCql8uTmizBFu56qC2Uhthe38MOZGLqCYH9JBQdYCwbjqPgPZLiLurDw6fQ0ZkInpBsMsCW1AAyFpmwb0shI2I0qBVn3iTZ6ySbVgiNOGS1V5tv7HTxlppA04H682pNzleNkkBETTYf9+d/nP3SdrYhURP/13voAcDWRBuEKzVvz6+vttbOOa6H85eb2zzNv4GY2lgg4J9/TkHgWSCJvc1TJZQSvW/++Wol7x8AqaDew3leccmEa0jmTgwtoYH142Z/v/TfV5vR5NOW89Tf9Z+PdqBNKISOOKTJuZGoiffbg2fgiGm/210lYJe2jgiWqttDUfBvpiPnnlnPTOfVL0sJAWrPx/22QQv0sYh4rNYX7ChTc5RCCkQc9c2h+1gn2QHdLDLW9fmZa3YScn9ZNCsV93qSaqMiAuENKo81QUDiE8G27JByQ687W0Qa/ALAVuKYDeaem63R2P+bT9NBpawK5uKERVhLKADqaBxNHD0Bq8JvKhAvQJbBFKB9W+cyuMYUfK8h2AAAhgldHZggcDKpNII9nhE78EACq8OmSuihB2TyVwRhSqg6Su2RApKFfg+1Ea8AwYQ95lboyxvuuGm5zg9mYPldG99MG8rUItzLYEdy7Go/dgIVbd8nz9eaqJld6UpIk1AfqA0wBBkxfMmp5qWIU+tVf54afnCvQbcCFSvFklPUj9kqR3oIki1Rk19JgsshM7qg6MQScksnB+7ZKpOBj8xNPR6cAPwSR4mkcxdiZVh6VC9qCbl1OKiZJDlsSWK5WqUaPfW5lE/2KVrWhePi5AUaKKmCTgN5ECoSNFHa0KBkQXzvG35b5URFAaNa7+HSzPT2mEh0D8JNVGnMrFFEBRA8tFKhZ+zLPLrAk3Ur2isJWg1imJ+GSyArqIe1Qt2+BbkhJwgZ3sbGTKkAQJF9mXUn5ec58XNr+ePIMkNcoDJ7Qa++xDzWxbuQF5w8eG4ztqtzUP9mrmzHwptPFLMhPLoJ2v2NO/i5NCj27+N7HlbvhfZeLSYCREVrRJYfciBaGoSuMo6gDzggyNazMROptrm2x2TZ5oo+ASK1bOYpypjk888J6asxCP0vHz+SCZABsgJpJ+7LiDf2fSZEr4E4wxmIRe6XP+6SSevmo1qqTowvY7xyYJNCkZMup7ralJqha3hH4lrL5qXh2wx0Ngql9aSriEXVk9igpk3a8tO4YsA657rJRYxxjfOH7hcKwH73BfQbtJFWaarcXGFlWr1sfL4Of5GCcddIPe/Ihca5j8cqIWFi484ovdbhf++EUgORbTuKFQNi5ZDGhXVz6tOXw4h+JrwGchS/7UG8w3SAAAAAElFTkSuQmCC"},68215:function(e,t,n){e.exports=n.p+"img/intelligence.be4e0865.gif"}}]);