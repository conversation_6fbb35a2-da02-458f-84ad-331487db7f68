{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ChartThree\",\n  ref: \"elChartRef\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, null, 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\PublicSentimentInfo\\components\\ChartThree.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ChartThree\" ref=\"elChartRef\">\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChartThree' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport * as echarts from 'echarts'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst erd = elementResizeDetectorMaker()\r\nconst ticketType = ref('')\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst tableData = ref([])\r\nconst stateData = computed(() => store.getters.getPublicSentimentInfoFn)\r\nconst randcolor = () => {\r\n  const r = 100 + (Math.random() * 100)\r\n  const g = 135 + (Math.random() * 100)\r\n  const b = 100 + (Math.random() * 100)\r\n  return `rgb(${r}, ${g}, ${b})`\r\n}\r\nconst initChart = () => {\r\n  if (!elChart) { elChart = echarts.init(elChartRef.value) }\r\n  const setOption = {\r\n    tooltip: {\r\n      trigger: 'item',\r\n      formatter (params) {\r\n        const { marker, data } = params\r\n        return `<div class=\"WordCloudChartTooltip\">\r\n          <div class=\"WordCloudChartName\">${marker}${data.name}</div>\r\n          <div class=\"WordCloudChartText\"><span>数量：${data.value}</span></div>\r\n        </div>`\r\n      }\r\n    },\r\n    series: [{\r\n      type: 'wordCloud',\r\n      gridSize: 20,\r\n      sizeRange: [12, 50],\r\n      rotationRange: [0, 0],\r\n      shape: 'circle',\r\n      textStyle: { color: () => randcolor() },\r\n      data: tableData.value\r\n    }]\r\n  }\r\n  elChart.setOption(setOption)\r\n}\r\nconst publicChartAnalysis = async () => {\r\n  if (stateData.value[`ChartThree-${ticketType.value}`]) {\r\n    tableData.value = stateData.value[`ChartThree-${ticketType.value}`]\r\n  } else {\r\n    const { data } = await api.publicChartAnalysis({ ticketType: ticketType.value, chartType: '3' })\r\n    tableData.value = data?.charts?.map(v => ({ key: v.key, name: v.name, value: v.num, percent: v.percent })) || []\r\n    store.commit('setPublicSentimentInfo', { key: `ChartThree-${ticketType.value}`, params: tableData.value })\r\n  }\r\n  initChart()\r\n  nextTick(() => {\r\n    erd.listenTo(elChartRef.value, () => {\r\n      elChart.resize()\r\n    })\r\n  })\r\n}\r\nonMounted(() => {\r\n  ticketType.value = route.query.ticketType || '2'\r\n  publicChartAnalysis()\r\n})\r\nonUnmounted(() => { erd.uninstall(elChartRef.value) })\r\n</script>\r\n<style lang=\"scss\">\r\n.ChartThree {\r\n  width: 680px;\r\n  height: 360px;\r\n  padding: 20px;\r\n  margin: auto;\r\n  position: relative;\r\n\r\n  &::after {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    width: 90%;\r\n    height: calc(100% - 40px);\r\n    background: linear-gradient(180deg, var(--zy-el-color-primary-light-9) 0%, var(--zy-el-color-primary-light-8) 53%, var(--zy-el-color-primary-light-9) 100%);\r\n    border-radius: 100%;\r\n    opacity: 0.2;\r\n    z-index: 1;\r\n  }\r\n\r\n  &::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    width: 60%;\r\n    height: calc(70% - 40px);\r\n    background: linear-gradient(180deg, var(--zy-el-color-primary-light-9) 0%, var(--zy-el-color-primary-light-8) 53%, var(--zy-el-color-primary-light-9) 100%);\r\n    border-radius: 100%;\r\n    opacity: 0.4;\r\n    z-index: 2;\r\n  }\r\n\r\n  &>div {\r\n    z-index: 3;\r\n  }\r\n\r\n  .WordCloudChartTooltip {\r\n    min-width: 180px;\r\n\r\n    .WordCloudChartName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-primary);\r\n      padding-left: 16px;\r\n      position: relative;\r\n\r\n      div {\r\n        display: inline-block;\r\n        font-weight: normal;\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n      }\r\n\r\n      span {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #FFFFFF;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .WordCloudChartText {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: var(--zy-font-name-distance-five);\r\n\r\n      span+span {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,YAAY;EAACC,GAAG,EAAC;;;uBAA5BC,mBAAA,CACM,OADNC,UACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}