{"ast": null, "code": "import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';\nimport * as echarts from 'echarts';\nimport elementResizeDetectorMaker from 'element-resize-detector';\nvar __default__ = {\n  name: 'barAndPie'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    color: {\n      type: String,\n      default: ''\n    },\n    data: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    showNum: {\n      type: Number,\n      default: 0\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var erd = elementResizeDetectorMaker();\n    var props = __props;\n    var elChart = null;\n    var elChartRef = ref();\n    var option = ref();\n    var initChart = function initChart() {\n      if (!elChart) {\n        elChart = echarts.init(elChartRef.value);\n      }\n      getSeroptin();\n      elChart.setOption(option.value);\n      if (props.showNum) {\n        chartfnc();\n      }\n    };\n    var getSeroptin = function getSeroptin() {\n      var setOption = {\n        title: {\n          text: '关注趋势分布',\n          left: '10',\n          top: 0,\n          textStyle: {\n            color: '#000'\n          }\n        },\n        tooltip: {\n          trigger: 'axis',\n          show: true,\n          axisPointer: {\n            type: 'shadow',\n            textStyle: {\n              color: '#fff'\n            }\n          }\n        },\n        legend: {\n          // 图例\n          orient: 'horizontal',\n          //horizontal 水平显示，vertical 垂直显示\n          top: 0,\n          right: 10,\n          itemGap: 20,\n          width: 600,\n          // 单行图例的宽度\n          textStyle: {\n            color: '#666'\n          },\n          data: ['对话量', '活跃用户']\n        },\n        xAxis: {\n          type: 'category',\n          axisLabel: {\n            interval: 0,\n            margin: 12,\n            fontSize: 14,\n            color: '#ccc'\n            // formatter: (value) => (value.length > 6 ? `${value.slice(0, 6)}...` : value)\n          },\n          data: showData.value[0].data.map(function (v) {\n            return v.name;\n          })\n        },\n        yAxis: {\n          type: 'value',\n          splitLine: {\n            show: false\n          }\n        },\n        grid: {\n          bottom: '10%',\n          top: 40,\n          x2: 60,\n          x: 60\n        },\n        series: showData.value.map(function (v) {\n          return {\n            name: v.name,\n            type: v.type,\n            barWidth: 20,\n            smooth: true,\n            itemStyle: {\n              color: v.color\n              // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\n              //   { offset: 0, color: v.color },\n              //   { offset: 1, color: 'rgba(31, 198, 255, 0)' }\n              // ])\n            },\n            label: {\n              show: v.type == 'bar' || showData.value.length == 1,\n              position: 'top',\n              fontSize: 14,\n              fontStyle: 'normal',\n              color: '#fff'\n            },\n            data: v.data.map(function (i) {\n              return i.value;\n            }),\n            areaStyle: {\n              color: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [{\n                  offset: 0,\n                  color: v.color // 0% 处的颜色\n                }, {\n                  offset: 1,\n                  color: 'rgba(44, 249, 255, 0)' // 100% 处的颜色\n                }],\n                global: false // 缺省为 false\n              }\n            }\n          };\n        })\n      };\n      option.value = setOption;\n      console.log('🚀 ~ getSeroptin ~ setOption:', setOption);\n    };\n    onMounted(function () {\n      nextTick(function () {\n        erd.listenTo(elChartRef.value, function (element) {});\n      });\n    });\n    var timer = ref();\n    var startIndex = ref(0);\n    var chartfnc = function chartfnc() {\n      if (props.showNum == 0) return;\n      timer.value = setInterval(function () {\n        var arr = JSON.parse(JSON.stringify(props.data));\n        arr.forEach(function (v) {\n          v.data = v.data.slice(startIndex.value, props.showNum + startIndex.value);\n        });\n        showData.value = arr;\n        getSeroptin();\n        startIndex.value++;\n        elChart.setOption(option.value);\n        if (startIndex.value > props.data[0].data.length - props.showNum) {\n          startIndex.value = 0;\n        }\n      }, 5000);\n    };\n    var lzmouseleave = function lzmouseleave() {\n      chartfnc();\n    };\n    var lzmouseenter = function lzmouseenter() {\n      if (props.showNum == 0) return;\n      clearInterval(timer.value);\n    };\n    onBeforeUnmount(function () {});\n    var showData = ref([]);\n    watch(function () {\n      return props.data;\n    }, function () {\n      if (props.data && props.data.length) {\n        if (props.showNum) {\n          var arr = JSON.parse(JSON.stringify(props.data));\n          arr.forEach(function (v) {\n            v.data = v.data.slice(startIndex.value, props.showNum);\n          });\n          startIndex.value++;\n          showData.value = arr;\n        } else {\n          showData.value = props.data;\n        }\n        initChart();\n      }\n    });\n    var __returned__ = {\n      erd,\n      props,\n      get elChart() {\n        return elChart;\n      },\n      set elChart(v) {\n        elChart = v;\n      },\n      elChartRef,\n      option,\n      initChart,\n      getSeroptin,\n      timer,\n      startIndex,\n      chartfnc,\n      lzmouseleave,\n      lzmouseenter,\n      showData,\n      ref,\n      watch,\n      onMounted,\n      onBeforeUnmount,\n      nextTick,\n      get echarts() {\n        return echarts;\n      },\n      get elementResizeDetectorMaker() {\n        return elementResizeDetectorMaker;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "watch", "onMounted", "onBeforeUnmount", "nextTick", "echarts", "elementResizeDetectorMaker", "__default__", "name", "erd", "props", "__props", "<PERSON><PERSON><PERSON>", "elChartRef", "option", "initChart", "init", "value", "getSeroptin", "setOption", "showNum", "chartfnc", "title", "text", "left", "top", "textStyle", "color", "tooltip", "trigger", "show", "axisPointer", "type", "legend", "orient", "right", "itemGap", "width", "data", "xAxis", "axisLabel", "interval", "margin", "fontSize", "showData", "map", "v", "yAxis", "splitLine", "grid", "bottom", "x2", "x", "series", "<PERSON><PERSON><PERSON><PERSON>", "smooth", "itemStyle", "label", "length", "position", "fontStyle", "i", "areaStyle", "y", "y2", "colorStops", "offset", "global", "console", "log", "listenTo", "element", "timer", "startIndex", "setInterval", "arr", "JSON", "parse", "stringify", "for<PERSON>ach", "slice", "lzmouseleave", "lzmouseenter", "clearInterval"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiUseStatistics/common/barAndPie.vue"], "sourcesContent": ["<template>\r\n  <div class=\"barAndPie\" @mouseenter=\"lzmouseenter\" @mouseleave=\"lzmouseleave\" ref=\"elChartRef\"></div>\r\n</template>\r\n<script>\r\nexport default { name: 'barAnd<PERSON>ie' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({\r\n  color: { type: String, default: '' },\r\n  data: { type: Array, default: () => [] },\r\n  showNum: { type: Number, default: 0 }\r\n})\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst option = ref()\r\nconst initChart = () => {\r\n  if (!elChart) {\r\n    elChart = echarts.init(elChartRef.value)\r\n  }\r\n  getSeroptin()\r\n  elChart.setOption(option.value)\r\n  if (props.showNum) {\r\n    chartfnc()\r\n  }\r\n}\r\n\r\nconst getSeroptin = () => {\r\n  const setOption = {\r\n    title: {\r\n      text: '关注趋势分布',\r\n      left: '10',\r\n      top: 0,\r\n      textStyle: {\r\n        color: '#000'\r\n      }\r\n    },\r\n    tooltip: {\r\n      trigger: 'axis',\r\n      show: true,\r\n      axisPointer: {\r\n        type: 'shadow',\r\n        textStyle: {\r\n          color: '#fff'\r\n        }\r\n      }\r\n    },\r\n    legend: {\r\n      // 图例\r\n      orient: 'horizontal', //horizontal 水平显示，vertical 垂直显示\r\n      top: 0,\r\n      right: 10,\r\n      itemGap: 20,\r\n      width: 600, // 单行图例的宽度\r\n      textStyle: {\r\n        color: '#666'\r\n      },\r\n      data: ['对话量', '活跃用户']\r\n    },\r\n    xAxis: {\r\n      type: 'category',\r\n      axisLabel: {\r\n        interval: 0,\r\n        margin: 12,\r\n        fontSize: 14,\r\n        color: '#ccc'\r\n        // formatter: (value) => (value.length > 6 ? `${value.slice(0, 6)}...` : value)\r\n      },\r\n      data: showData.value[0].data.map((v) => v.name)\r\n    },\r\n    yAxis: { type: 'value', splitLine: { show: false } },\r\n    grid: { bottom: '10%', top: 40, x2: 60, x: 60 },\r\n    series: showData.value.map((v) => {\r\n      return {\r\n        name: v.name,\r\n        type: v.type,\r\n        barWidth: 20,\r\n        smooth: true,\r\n        itemStyle: {\r\n          color: v.color\r\n          // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n          //   { offset: 0, color: v.color },\r\n          //   { offset: 1, color: 'rgba(31, 198, 255, 0)' }\r\n          // ])\r\n        },\r\n        label: {\r\n          show: v.type == 'bar' || showData.value.length == 1,\r\n          position: 'top',\r\n          fontSize: 14,\r\n          fontStyle: 'normal',\r\n          color: '#fff'\r\n        },\r\n        data: v.data.map((i) => i.value),\r\n        areaStyle: {\r\n          color: {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 0,\r\n            y2: 1,\r\n            colorStops: [\r\n              {\r\n                offset: 0,\r\n                color: v.color // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 1,\r\n                color: 'rgba(44, 249, 255, 0)' // 100% 处的颜色\r\n              }\r\n            ],\r\n            global: false // 缺省为 false\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n  option.value = setOption\r\n  console.log('🚀 ~ getSeroptin ~ setOption:', setOption)\r\n}\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    erd.listenTo(elChartRef.value, (element) => {})\r\n  })\r\n})\r\nconst timer = ref()\r\nconst startIndex = ref(0)\r\nconst chartfnc = () => {\r\n  if (props.showNum == 0) return\r\n  timer.value = setInterval(() => {\r\n    const arr = JSON.parse(JSON.stringify(props.data))\r\n    arr.forEach((v) => {\r\n      v.data = v.data.slice(startIndex.value, props.showNum + startIndex.value)\r\n    })\r\n    showData.value = arr\r\n    getSeroptin()\r\n    startIndex.value++\r\n    elChart.setOption(option.value)\r\n    if (startIndex.value > props.data[0].data.length - props.showNum) {\r\n      startIndex.value = 0\r\n    }\r\n  }, 5000)\r\n}\r\nconst lzmouseleave = () => {\r\n  chartfnc()\r\n}\r\nconst lzmouseenter = () => {\r\n  if (props.showNum == 0) return\r\n  clearInterval(timer.value)\r\n}\r\n\r\nonBeforeUnmount(() => {})\r\nconst showData = ref([])\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    if (props.data && props.data.length) {\r\n      if (props.showNum) {\r\n        const arr = JSON.parse(JSON.stringify(props.data))\r\n        arr.forEach((v) => {\r\n          v.data = v.data.slice(startIndex.value, props.showNum)\r\n        })\r\n        startIndex.value++\r\n        showData.value = arr\r\n      } else {\r\n        showData.value = props.data\r\n      }\r\n      initChart()\r\n    }\r\n  }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.barAndPie {\r\n  width: 100%;\r\n  height: 100%;\r\n  margin: auto;\r\n\r\n  .ColumnChartTooltip {\r\n    min-width: 180px;\r\n\r\n    .ColumnChartName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-primary);\r\n      padding-left: 16px;\r\n      position: relative;\r\n\r\n      span {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #ffffff;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ColumnChartText {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: var(--zy-font-name-distance-five);\r\n\r\n      span + span {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAOA,SAASA,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,KAAK;AACtE,OAAO,KAAKC,OAAO,MAAM,SAAS;AAClC,OAAOC,0BAA0B,MAAM,yBAAyB;AALhE,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAY,CAAC;;;;;;;;;;;;;;;;;;;;;IAMpC,IAAMC,GAAG,GAAGH,0BAA0B,CAAC,CAAC;IACxC,IAAMI,KAAK,GAAGC,OAIZ;IACF,IAAIC,OAAO,GAAG,IAAI;IAClB,IAAMC,UAAU,GAAGb,GAAG,CAAC,CAAC;IACxB,IAAMc,MAAM,GAAGd,GAAG,CAAC,CAAC;IACpB,IAAMe,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAI,CAACH,OAAO,EAAE;QACZA,OAAO,GAAGP,OAAO,CAACW,IAAI,CAACH,UAAU,CAACI,KAAK,CAAC;MAC1C;MACAC,WAAW,CAAC,CAAC;MACbN,OAAO,CAACO,SAAS,CAACL,MAAM,CAACG,KAAK,CAAC;MAC/B,IAAIP,KAAK,CAACU,OAAO,EAAE;QACjBC,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;IAED,IAAMH,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAMC,SAAS,GAAG;QAChBG,KAAK,EAAE;UACLC,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE,IAAI;UACVC,GAAG,EAAE,CAAC;UACNC,SAAS,EAAE;YACTC,KAAK,EAAE;UACT;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,IAAI,EAAE,IAAI;UACVC,WAAW,EAAE;YACXC,IAAI,EAAE,QAAQ;YACdN,SAAS,EAAE;cACTC,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDM,MAAM,EAAE;UACN;UACAC,MAAM,EAAE,YAAY;UAAE;UACtBT,GAAG,EAAE,CAAC;UACNU,KAAK,EAAE,EAAE;UACTC,OAAO,EAAE,EAAE;UACXC,KAAK,EAAE,GAAG;UAAE;UACZX,SAAS,EAAE;YACTC,KAAK,EAAE;UACT,CAAC;UACDW,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;QACtB,CAAC;QACDC,KAAK,EAAE;UACLP,IAAI,EAAE,UAAU;UAChBQ,SAAS,EAAE;YACTC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,EAAE;YACVC,QAAQ,EAAE,EAAE;YACZhB,KAAK,EAAE;YACP;UACF,CAAC;UACDW,IAAI,EAAEM,QAAQ,CAAC3B,KAAK,CAAC,CAAC,CAAC,CAACqB,IAAI,CAACO,GAAG,CAAC,UAACC,CAAC;YAAA,OAAKA,CAAC,CAACtC,IAAI;UAAA;QAChD,CAAC;QACDuC,KAAK,EAAE;UAAEf,IAAI,EAAE,OAAO;UAAEgB,SAAS,EAAE;YAAElB,IAAI,EAAE;UAAM;QAAE,CAAC;QACpDmB,IAAI,EAAE;UAAEC,MAAM,EAAE,KAAK;UAAEzB,GAAG,EAAE,EAAE;UAAE0B,EAAE,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAG,CAAC;QAC/CC,MAAM,EAAET,QAAQ,CAAC3B,KAAK,CAAC4B,GAAG,CAAC,UAACC,CAAC,EAAK;UAChC,OAAO;YACLtC,IAAI,EAAEsC,CAAC,CAACtC,IAAI;YACZwB,IAAI,EAAEc,CAAC,CAACd,IAAI;YACZsB,QAAQ,EAAE,EAAE;YACZC,MAAM,EAAE,IAAI;YACZC,SAAS,EAAE;cACT7B,KAAK,EAAEmB,CAAC,CAACnB;cACT;cACA;cACA;cACA;YACF,CAAC;YACD8B,KAAK,EAAE;cACL3B,IAAI,EAAEgB,CAAC,CAACd,IAAI,IAAI,KAAK,IAAIY,QAAQ,CAAC3B,KAAK,CAACyC,MAAM,IAAI,CAAC;cACnDC,QAAQ,EAAE,KAAK;cACfhB,QAAQ,EAAE,EAAE;cACZiB,SAAS,EAAE,QAAQ;cACnBjC,KAAK,EAAE;YACT,CAAC;YACDW,IAAI,EAAEQ,CAAC,CAACR,IAAI,CAACO,GAAG,CAAC,UAACgB,CAAC;cAAA,OAAKA,CAAC,CAAC5C,KAAK;YAAA,EAAC;YAChC6C,SAAS,EAAE;cACTnC,KAAK,EAAE;gBACLK,IAAI,EAAE,QAAQ;gBACdoB,CAAC,EAAE,CAAC;gBACJW,CAAC,EAAE,CAAC;gBACJZ,EAAE,EAAE,CAAC;gBACLa,EAAE,EAAE,CAAC;gBACLC,UAAU,EAAE,CACV;kBACEC,MAAM,EAAE,CAAC;kBACTvC,KAAK,EAAEmB,CAAC,CAACnB,KAAK,CAAC;gBACjB,CAAC,EACD;kBACEuC,MAAM,EAAE,CAAC;kBACTvC,KAAK,EAAE,uBAAuB,CAAC;gBACjC,CAAC,CACF;gBACDwC,MAAM,EAAE,KAAK,CAAC;cAChB;YACF;UACF,CAAC;QACH,CAAC;MACH,CAAC;MACDrD,MAAM,CAACG,KAAK,GAAGE,SAAS;MACxBiD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAElD,SAAS,CAAC;IACzD,CAAC;IAEDjB,SAAS,CAAC,YAAM;MACdE,QAAQ,CAAC,YAAM;QACbK,GAAG,CAAC6D,QAAQ,CAACzD,UAAU,CAACI,KAAK,EAAE,UAACsD,OAAO,EAAK,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAMC,KAAK,GAAGxE,GAAG,CAAC,CAAC;IACnB,IAAMyE,UAAU,GAAGzE,GAAG,CAAC,CAAC,CAAC;IACzB,IAAMqB,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB,IAAIX,KAAK,CAACU,OAAO,IAAI,CAAC,EAAE;MACxBoD,KAAK,CAACvD,KAAK,GAAGyD,WAAW,CAAC,YAAM;QAC9B,IAAMC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACpE,KAAK,CAAC4B,IAAI,CAAC,CAAC;QAClDqC,GAAG,CAACI,OAAO,CAAC,UAACjC,CAAC,EAAK;UACjBA,CAAC,CAACR,IAAI,GAAGQ,CAAC,CAACR,IAAI,CAAC0C,KAAK,CAACP,UAAU,CAACxD,KAAK,EAAEP,KAAK,CAACU,OAAO,GAAGqD,UAAU,CAACxD,KAAK,CAAC;QAC3E,CAAC,CAAC;QACF2B,QAAQ,CAAC3B,KAAK,GAAG0D,GAAG;QACpBzD,WAAW,CAAC,CAAC;QACbuD,UAAU,CAACxD,KAAK,EAAE;QAClBL,OAAO,CAACO,SAAS,CAACL,MAAM,CAACG,KAAK,CAAC;QAC/B,IAAIwD,UAAU,CAACxD,KAAK,GAAGP,KAAK,CAAC4B,IAAI,CAAC,CAAC,CAAC,CAACA,IAAI,CAACoB,MAAM,GAAGhD,KAAK,CAACU,OAAO,EAAE;UAChEqD,UAAU,CAACxD,KAAK,GAAG,CAAC;QACtB;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;IACD,IAAMgE,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB5D,QAAQ,CAAC,CAAC;IACZ,CAAC;IACD,IAAM6D,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAIxE,KAAK,CAACU,OAAO,IAAI,CAAC,EAAE;MACxB+D,aAAa,CAACX,KAAK,CAACvD,KAAK,CAAC;IAC5B,CAAC;IAEDd,eAAe,CAAC,YAAM,CAAC,CAAC,CAAC;IACzB,IAAMyC,QAAQ,GAAG5C,GAAG,CAAC,EAAE,CAAC;IACxBC,KAAK,CACH;MAAA,OAAMS,KAAK,CAAC4B,IAAI;IAAA,GAChB,YAAM;MACJ,IAAI5B,KAAK,CAAC4B,IAAI,IAAI5B,KAAK,CAAC4B,IAAI,CAACoB,MAAM,EAAE;QACnC,IAAIhD,KAAK,CAACU,OAAO,EAAE;UACjB,IAAMuD,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACpE,KAAK,CAAC4B,IAAI,CAAC,CAAC;UAClDqC,GAAG,CAACI,OAAO,CAAC,UAACjC,CAAC,EAAK;YACjBA,CAAC,CAACR,IAAI,GAAGQ,CAAC,CAACR,IAAI,CAAC0C,KAAK,CAACP,UAAU,CAACxD,KAAK,EAAEP,KAAK,CAACU,OAAO,CAAC;UACxD,CAAC,CAAC;UACFqD,UAAU,CAACxD,KAAK,EAAE;UAClB2B,QAAQ,CAAC3B,KAAK,GAAG0D,GAAG;QACtB,CAAC,MAAM;UACL/B,QAAQ,CAAC3B,KAAK,GAAGP,KAAK,CAAC4B,IAAI;QAC7B;QACAvB,SAAS,CAAC,CAAC;MACb;IACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}