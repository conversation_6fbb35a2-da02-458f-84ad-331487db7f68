{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onBeforeUnmount, onMounted, computed, watch, nextTick } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { Close } from '@element-plus/icons-vue';\nimport { ElMessage } from 'element-plus';\nimport CountdownTimer from './components/CountdownTimer.vue';\nimport VideoPlayer from './components/VideoPlayer.vue';\n\n// 通用消息提示函数\n\nvar __default__ = {\n  name: 'LiveBroadcastDetails'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    id: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['callback', 'update:modelValue'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var showMessage = function showMessage(message) {\n      var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'success';\n      ElMessage({\n        message,\n        type,\n        zIndex: 99999\n      });\n    };\n    var props = __props;\n    var emit = __emit;\n    var details = ref({});\n    var logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100');\n    var activeTab = ref('details');\n    var imgUrl = computed(function () {\n      return details.value.coverImg ? api.fileURL(details.value.coverImg) : '';\n    });\n\n    // 组件引用\n    var countdownTimerRef = ref(null);\n    var videoPlayerRef = ref(null);\n\n    // 回放模式标识\n    var isReplayMode = ref(false);\n\n    // 视频播放器相关变量已移至VideoPlayer组件\n\n    // 评论相关数据\n    var comments = ref([]);\n    var newComment = ref('');\n    var replyingTo = ref(null);\n    var replyContent = ref('');\n    var replyToUser = ref('');\n    var replyInputRef = ref(null);\n\n    // 评论定时刷新\n    var commentTimer = null;\n\n    // 监听状态变化\n    watch(function () {\n      return details.value.meetingStatus;\n    }, function (newStatus, oldStatus) {\n      console.log('状态变化:', oldStatus, '->', newStatus);\n      console.log('当前isReplayMode:', isReplayMode.value);\n      if (newStatus === '进行中' && oldStatus !== '进行中') {\n        console.log('切换到进行中状态');\n\n        // 只有在有推流地址时才初始化播放器\n        if (details.value.liveUrl) {\n          console.log('进行中状态且有推流地址：手动触发VideoPlayer初始化');\n          nextTick(function () {\n            if (videoPlayerRef.value) {\n              videoPlayerRef.value.initPlayer();\n            }\n          });\n        } else {\n          console.log('进行中状态但没有推流地址：显示暂无配置提示');\n        }\n      } else if (newStatus === '无推流' && oldStatus !== '无推流') {\n        console.log('切换到无推流状态（倒计时结束但无推流地址）');\n        // 无推流状态不需要初始化播放器，只显示提示\n      } else if (newStatus === '已结束' && oldStatus !== '已结束') {\n        console.log('切换到已结束状态');\n\n        // 如果是回放模式且有回放地址，初始化回放播放器\n        if (isReplayMode.value && details.value.liveReplayUrl) {\n          console.log('回放模式：检测到回放地址，手动触发VideoPlayer初始化');\n          nextTick(function () {\n            if (videoPlayerRef.value) {\n              videoPlayerRef.value.initPlayer();\n            }\n          });\n        }\n      } else if (newStatus !== '进行中' && oldStatus === '进行中' || newStatus !== '已结束' && oldStatus === '已结束' && isReplayMode.value) {\n        // 从进行中或回放状态变为其他状态，销毁播放器\n        if (videoPlayerRef.value) {\n          videoPlayerRef.value.destroyVideoPlayer();\n        }\n      }\n\n      // 从无推流状态切换到其他状态时，不需要特殊处理\n      // 因为无推流状态没有播放器需要销毁\n    });\n\n    // 倒计时结束处理\n    var handleCountdownEnd = function handleCountdownEnd() {\n      console.log('倒计时结束处理');\n      // 倒计时结束后检查是否有推流地址\n      if (details.value.liveUrl && details.value.meetingStatus === '未开始') {\n        console.log('倒计时结束，检测到推流地址，自动切换到进行中状态');\n        isReplayMode.value = false;\n        details.value.meetingStatus = '进行中';\n      } else if (!details.value.liveUrl && details.value.meetingStatus === '未开始') {\n        console.log('倒计时结束，但没有推流地址，设置为无推流状态');\n        isReplayMode.value = false;\n        details.value.meetingStatus = '无推流'; // 新增状态：倒计时结束但无推流地址\n      }\n    };\n\n    // 根据数据判断实际状态\n    var determineActualStatus = function determineActualStatus() {\n      if (!details.value) return;\n      console.log('determineActualStatus: 开始判断实际状态');\n      console.log('当前meetingStatus:', details.value.meetingStatus);\n      console.log('liveUrl:', !!details.value.liveUrl);\n      console.log('liveReplayUrl:', !!details.value.liveReplayUrl);\n\n      // 如果是未开始状态，不要修改，保持倒计时\n      if (details.value.meetingStatus === '未开始') {\n        console.log('保持未开始状态，等待倒计时结束');\n        return;\n      }\n\n      // 新逻辑：根据回放地址和推流地址判断状态\n      if (details.value.liveReplayUrl) {\n        // 1. 如果有回放地址 → 已结束状态\n        console.log('检测到回放地址，设置为已结束状态');\n        details.value.meetingStatus = '已结束';\n        isReplayMode.value = false; // 重置回放模式\n        return;\n      }\n      if (details.value.liveUrl) {\n        // 2. 如果没有回放地址但有推流地址 → 进行中状态（还在直播）\n        console.log('没有回放地址但有推流地址，设置为进行中状态（还在直播）');\n        details.value.meetingStatus = '进行中';\n        isReplayMode.value = false; // 确保是直播模式\n        return;\n      }\n\n      // 3. 如果既没有回放地址也没有推流地址 → 已结束状态\n      console.log('既没有回放地址也没有推流地址，设置为已结束状态');\n      details.value.meetingStatus = '已结束';\n      isReplayMode.value = false;\n    };\n    var applyStatusFromProps = function applyStatusFromProps() {\n      console.log('applyStatusFromProps: 开始应用状态');\n\n      // 先根据数据判断实际状态\n      determineActualStatus();\n      if (details.value.meetingStatus === '进行中') {\n        console.log('当前状态为进行中，准备初始化VideoPlayer');\n        console.log('isReplayMode:', isReplayMode.value);\n        console.log('liveUrl:', !!details.value.liveUrl);\n\n        // 只有在有推流地址时才初始化VideoPlayer\n        if (details.value.liveUrl) {\n          // 延迟一点时间确保VideoPlayer组件已经渲染\n          setTimeout(function () {\n            if (videoPlayerRef.value) {\n              if (isReplayMode.value && details.value.liveReplayUrl) {\n                console.log('强制初始化回放VideoPlayer');\n                videoPlayerRef.value.initPlayer();\n              } else if (!isReplayMode.value && details.value.liveUrl) {\n                console.log('强制初始化直播VideoPlayer');\n                videoPlayerRef.value.initPlayer();\n              }\n            }\n          }, 100);\n        } else {\n          console.log('进行中状态但没有推流地址，不初始化VideoPlayer');\n        }\n      }\n    };\n    onMounted(function () {\n      // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\n      if (props.modelValue && props.id) {\n        getInfo();\n        getCommentData();\n        // 开始评论定时刷新\n        startCommentRefresh();\n      }\n      applyStatusFromProps();\n    });\n\n    // 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\n    watch(function () {\n      return props.modelValue;\n    }, function (newVal, oldVal) {\n      console.log('modelValue变化:', oldVal, '->', newVal, 'id:', props.id);\n      if (newVal && props.id && !oldVal) {\n        // 从 false 变为 true 且有 id 时加载数据\n        console.log('弹窗打开，开始加载数据');\n\n        // 重置状态，确保每次打开都是干净的状态\n        isReplayMode.value = false;\n        activeTab.value = 'details';\n        getInfo();\n        getCommentData();\n        // 开始评论定时刷新\n        startCommentRefresh();\n      } else if (!newVal && oldVal) {\n        // 从显示变为隐藏时，停止评论定时刷新\n        console.log('弹窗关闭，停止定时刷新');\n        stopCommentRefresh();\n      }\n    });\n\n    // 监听 id 变化 - 当 id 改变且组件显示时加载数据\n    watch(function () {\n      return props.id;\n    }, function (newVal, oldVal) {\n      if (newVal && props.modelValue && newVal !== oldVal) {\n        getInfo();\n        getCommentData();\n        // 重新开始评论定时刷新\n        startCommentRefresh();\n      }\n    });\n    var getInfo = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              console.log('getInfo: 开始获取数据，id =', props.id);\n              _context.next = 3;\n              return api.videoConnectionInfo({\n                detailId: props.id\n              });\n            case 3:\n              res = _context.sent;\n              data = res.data;\n              details.value = data;\n              console.log('getInfo: 数据加载完成');\n              console.log('meetingStatus:', data.meetingStatus);\n              console.log('liveUrl:', !!data.liveUrl);\n              console.log('liveReplayUrl:', !!data.liveReplayUrl);\n\n              // 应用状态\n              applyStatusFromProps();\n\n              // 额外的保障：如果是进行中状态，确保VideoPlayer能够初始化\n              if (data.meetingStatus === '进行中') {\n                console.log('getInfo: 检测到进行中状态，延迟初始化VideoPlayer');\n                setTimeout(function () {\n                  if (videoPlayerRef.value) {\n                    if (isReplayMode.value && data.liveReplayUrl) {\n                      console.log('getInfo: 延迟初始化回放VideoPlayer');\n                      videoPlayerRef.value.initPlayer();\n                    } else if (!isReplayMode.value && data.liveUrl) {\n                      console.log('getInfo: 延迟初始化直播VideoPlayer');\n                      videoPlayerRef.value.initPlayer();\n                    }\n                  }\n                }, 200);\n              }\n            case 12:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function getInfo() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n\n    // 获取评论数据\n    var getCommentData = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.twoLevelTree({\n                businessCode: 'liveBroadcast',\n                businessId: props.id,\n                pageNo: 1,\n                pageSize: 99\n              });\n            case 2:\n              res = _context2.sent;\n              console.log('评论数据:', res);\n              comments.value = res.data || [];\n            case 5:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function getCommentData() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n\n    // 开始评论定时刷新\n    var startCommentRefresh = function startCommentRefresh() {\n      if (commentTimer) clearInterval(commentTimer);\n      commentTimer = setInterval(function () {\n        getCommentData();\n      }, 3000); // 每3秒刷新一次\n    };\n\n    // 停止评论定时刷新\n    var stopCommentRefresh = function stopCommentRefresh() {\n      if (commentTimer) {\n        clearInterval(commentTimer);\n        commentTimer = null;\n      }\n    };\n\n    // 打开回放\n    var handleReplay = function handleReplay() {\n      console.log('handleReplay: 开始处理回放请求');\n      console.log('handleReplay: 回放地址:', details.value.liveReplayUrl);\n      if (!details.value.liveReplayUrl) {\n        console.warn('没有回放地址');\n        return;\n      }\n\n      // 设置回放模式\n      console.log('handleReplay: 设置回放模式为true');\n      isReplayMode.value = true;\n\n      // 确保VideoPlayer组件能够正确初始化\n      nextTick(function () {\n        if (videoPlayerRef.value) {\n          console.log('handleReplay: 手动触发VideoPlayer初始化');\n          videoPlayerRef.value.initPlayer();\n        } else {\n          console.warn('handleReplay: videoPlayerRef不存在');\n        }\n      });\n    };\n\n    // 提交新评论\n    var submitComment = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              if (newComment.value.trim()) {\n                _context3.next = 2;\n                break;\n              }\n              return _context3.abrupt(\"return\");\n            case 2:\n              _context3.next = 4;\n              return api.commentNew({\n                form: {\n                  businessCode: 'liveBroadcast',\n                  businessId: props.id,\n                  checkedStatus: 1,\n                  commentContent: newComment.value,\n                  parentId: \"\",\n                  terminalName: 'PC'\n                }\n              });\n            case 4:\n              res = _context3.sent;\n              if (res.code == 200) {\n                showMessage('评论发表成功');\n                getCommentData();\n              } else {\n                showMessage(res.message || '评论发表失败', 'error');\n              }\n              newComment.value = '';\n            case 7:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function submitComment() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n\n    // 点赞功能\n    var toggleLike = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(item) {\n        var res, _res;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              if (!item.hasClickPraises) {\n                _context4.next = 7;\n                break;\n              }\n              _context4.next = 3;\n              return api.praisesDels({\n                businessCode: \"comment\",\n                businessId: item.id\n              });\n            case 3:\n              res = _context4.sent;\n              if (res.code == 200) {\n                item.hasClickPraises = false;\n                item.praisesCount = Math.max(0, (item.praisesCount || 0) - 1);\n                ElMessage({\n                  message: '取消点赞成功',\n                  type: 'success'\n                });\n              } else {\n                ElMessage({\n                  message: res.message || '取消点赞失败',\n                  type: 'error'\n                });\n              }\n              _context4.next = 11;\n              break;\n            case 7:\n              _context4.next = 9;\n              return api.praisesAdd({\n                form: {\n                  businessCode: \"comment\",\n                  businessId: item.id\n                }\n              });\n            case 9:\n              _res = _context4.sent;\n              if (_res.code == 200) {\n                item.hasClickPraises = true;\n                item.praisesCount = (item.praisesCount || 0) + 1;\n                ElMessage({\n                  message: '点赞成功',\n                  type: 'success'\n                });\n              } else {\n                ElMessage({\n                  message: _res.message || '点赞失败',\n                  type: 'error'\n                });\n              }\n            case 11:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function toggleLike(_x) {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n\n    // 显示回复输入框\n    var showReplyInput = function showReplyInput(commentId) {\n      var replyToUsername = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n      replyingTo.value = commentId;\n      replyToUser.value = replyToUsername;\n      replyContent.value = '';\n      nextTick(function () {\n        if (replyInputRef.value) {\n          replyInputRef.value.focus();\n        }\n      });\n    };\n\n    // 取消回复\n    var cancelReply = function cancelReply() {\n      replyingTo.value = null;\n      replyToUser.value = '';\n      replyContent.value = '';\n    };\n\n    // 提交回复\n    var submitReply = /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5(commentId) {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              if (replyContent.value.trim()) {\n                _context5.next = 2;\n                break;\n              }\n              return _context5.abrupt(\"return\");\n            case 2:\n              _context5.next = 4;\n              return api.commentNew({\n                form: {\n                  businessCode: \"liveBroadcast\",\n                  businessId: props.id,\n                  checkedStatus: 1,\n                  commentContent: replyContent.value.trim(),\n                  parentId: commentId,\n                  terminalName: \"PC\"\n                }\n              });\n            case 4:\n              res = _context5.sent;\n              if (res.code == 200) {\n                ElMessage({\n                  message: '回复发表成功',\n                  type: 'success'\n                });\n                getCommentData();\n                cancelReply();\n              } else {\n                ElMessage({\n                  message: res.message || '回复发表失败',\n                  type: 'error'\n                });\n              }\n            case 6:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5);\n      }));\n      return function submitReply(_x2) {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    onBeforeUnmount(function () {\n      // 清理评论定时器\n      stopCommentRefresh();\n      // 销毁视频播放器\n      if (videoPlayerRef.value) {\n        videoPlayerRef.value.destroyVideoPlayer();\n      }\n    });\n    var handleClose = function handleClose() {\n      console.log('handleClose: 开始关闭弹窗');\n\n      // 停止评论定时刷新\n      stopCommentRefresh();\n\n      // 销毁视频播放器\n      if (videoPlayerRef.value) {\n        console.log('handleClose: 销毁VideoPlayer');\n        videoPlayerRef.value.destroyVideoPlayer();\n      }\n\n      // 重置状态\n      console.log('handleClose: 重置组件状态');\n      isReplayMode.value = false;\n      activeTab.value = 'details';\n\n      // 清空数据引用，确保下次打开时重新加载\n      details.value = {};\n      emit('update:modelValue', false);\n      emit('callback');\n    };\n    var __returned__ = {\n      showMessage,\n      props,\n      emit,\n      details,\n      logoSrc,\n      activeTab,\n      imgUrl,\n      countdownTimerRef,\n      videoPlayerRef,\n      isReplayMode,\n      comments,\n      newComment,\n      replyingTo,\n      replyContent,\n      replyToUser,\n      replyInputRef,\n      get commentTimer() {\n        return commentTimer;\n      },\n      set commentTimer(v) {\n        commentTimer = v;\n      },\n      handleCountdownEnd,\n      determineActualStatus,\n      applyStatusFromProps,\n      getInfo,\n      getCommentData,\n      startCommentRefresh,\n      stopCommentRefresh,\n      handleReplay,\n      submitComment,\n      toggleLike,\n      showReplyInput,\n      cancelReply,\n      submitReply,\n      handleClose,\n      get api() {\n        return api;\n      },\n      ref,\n      onBeforeUnmount,\n      onMounted,\n      computed,\n      watch,\n      nextTick,\n      get format() {\n        return format;\n      },\n      get Close() {\n        return Close;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      CountdownTimer,\n      VideoPlayer\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onBeforeUnmount", "onMounted", "computed", "watch", "nextTick", "format", "Close", "ElMessage", "CountdownTimer", "VideoPlayer", "__default__", "showMessage", "message", "undefined", "zIndex", "props", "__props", "emit", "__emit", "details", "logoSrc", "activeTab", "imgUrl", "coverImg", "fileURL", "countdownTimerRef", "videoPlayerRef", "isReplayMode", "comments", "newComment", "replyingTo", "replyContent", "replyToUser", "replyInputRef", "commentTimer", "meetingStatus", "newStatus", "oldStatus", "console", "log", "liveUrl", "initPlayer", "liveReplayUrl", "destroyVideoPlayer", "handleCountdownEnd", "determineActualStatus", "applyStatusFromProps", "setTimeout", "modelValue", "id", "getInfo", "getCommentData", "startCommentRefresh", "newVal", "oldVal", "stopCommentRefresh", "_ref2", "_callee", "res", "data", "_callee$", "_context", "videoConnectionInfo", "detailId", "_ref3", "_callee2", "_callee2$", "_context2", "twoLevelTree", "businessCode", "businessId", "pageNo", "pageSize", "clearInterval", "setInterval", "handleReplay", "warn", "submitComment", "_ref4", "_callee3", "_callee3$", "_context3", "trim", "commentNew", "form", "checkedStatus", "commentContent", "parentId", "terminalName", "code", "toggleLike", "_ref5", "_callee4", "item", "_res", "_callee4$", "_context4", "hasClickPraises", "praises<PERSON><PERSON>", "praisesCount", "Math", "max", "praisesAdd", "_x", "showReplyInput", "commentId", "replyToUsername", "focus", "cancelReply", "submitReply", "_ref6", "_callee5", "_callee5$", "_context5", "_x2", "handleClose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/LiveManagement/LiveBroadcastDetails.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"details-fade\">\r\n    <div v-show=\"modelValue\" class=\"LiveBroadcastDetails\">\r\n      <div class=\"LiveBroadcastDetailsHeader\">\r\n        <img v-if=\"logoSrc\" class=\"LiveBroadcastDetailsEmblem\" :src=\"logoSrc\" alt=\"emblem\" />\r\n        <div class=\"LiveBroadcastDetailsHeadInfo\">\r\n          <div class=\"LiveBroadcastDetailsTitle\">{{ details.theme }}</div>\r\n          <div class=\"LiveBroadcastDetailsTime\">直播时间：{{ format(details.startTime) }} 到 {{\r\n            format(details.endTime) }}</div>\r\n        </div>\r\n        <el-icon class=\"LiveBroadcastDetailsClose\" @click=\"handleClose\">\r\n          <Close />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"LiveBroadcastDetailsBody\">\r\n        <div class=\"LiveBroadcastDetailsCanvas\">\r\n          <!-- 未开始：显示封面图 + 倒计时 -->\r\n          <div v-if=\"details.meetingStatus === '未开始'\" class=\"LiveBroadcastDetailsPoster\">\r\n            <img :src=\"imgUrl\" alt=\"海报/播放画面区域\" style=\"width: 100%;height: 100%;object-fit: contain;\">\r\n            <CountdownTimer :start-time=\"details.startTime\" @countdown-end=\"handleCountdownEnd\"\r\n              ref=\"countdownTimerRef\" />\r\n          </div>\r\n          <!-- 进行中且有推流地址：直播播放器 -->\r\n          <div v-else-if=\"details.meetingStatus === '进行中' && details.liveUrl\" class=\"LiveBroadcastDetailsLiveOverlay\">\r\n            <!-- 显示直播播放器 -->\r\n            <VideoPlayer :key=\"`video-player-live-${props.id}-${details.meetingStatus}`\" :live-url=\"details.liveUrl\"\r\n              :replay-url=\"details.liveReplayUrl\" :is-replay=\"false\" ref=\"videoPlayerRef\" />\r\n          </div>\r\n          <!-- 无推流：倒计时结束但没有推流地址 或 进行中但没有推流地址 -->\r\n          <div v-else-if=\"details.meetingStatus === '无推流' || (details.meetingStatus === '进行中' && !details.liveUrl)\"\r\n            class=\"LiveBroadcastDetailsEnded\">\r\n            <!-- 背景图 -->\r\n            <img :src=\"imgUrl\" alt=\"直播背景\" class=\"LiveBroadcastDetailsEndedBg\">\r\n            <!-- 悬浮的提示内容 -->\r\n            <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n              <div class=\"endedTitle\">暂无配置直播地址</div>\r\n            </div>\r\n          </div>\r\n          <!-- 已结束：回放播放器或背景图 + 悬浮内容 -->\r\n          <div v-else-if=\"details.meetingStatus === '已结束'\" class=\"LiveBroadcastDetailsEnded\">\r\n            <!-- 回放模式：显示回放播放器 -->\r\n            <VideoPlayer v-if=\"isReplayMode && details.liveReplayUrl\"\r\n              :key=\"`video-player-replay-${props.id}-${details.meetingStatus}`\" :live-url=\"details.liveUrl\"\r\n              :replay-url=\"details.liveReplayUrl\" :is-replay=\"true\" ref=\"videoPlayerRef\" />\r\n            <!-- 非回放模式：显示背景图和按钮 -->\r\n            <template v-else>\r\n              <!-- 背景图 -->\r\n              <img :src=\"imgUrl\" alt=\"直播背景\" class=\"LiveBroadcastDetailsEndedBg\">\r\n              <!-- 悬浮的结束状态内容 -->\r\n              <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n                <div class=\"endedTitle\">直播已结束</div>\r\n                <!-- 有回放地址：显示观看回放按钮 -->\r\n                <el-button type=\"primary\" class=\"replayBtn\" v-if=\"details.isReplay == 1\" @click=\"handleReplay\">\r\n                  观看回放\r\n                </el-button>\r\n                <!-- 没有回放地址且没有推流地址：只显示图片，不显示按钮 -->\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </div>\r\n        <div class=\"LiveBroadcastDetailsSidebar\">\r\n          <div class=\"LiveBroadcastDetailsTabs\">\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]\"\r\n              @click=\"activeTab = 'details'\">\r\n              直播详情</div>\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]\"\r\n              @click=\"activeTab = 'interact'\">\r\n              互动</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-if=\"activeTab === 'details'\">\r\n            <div class=\"detailsTitle\">{{ details?.theme || '-' }}</div>\r\n            <div class=\"detailsTimeBox\">\r\n              <img src=\"../../assets/img/icon_live_time.png\" alt=\"\" class=\"detailsTimeIcon\">\r\n              <span class=\"detailsTime\">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</span>\r\n            </div>\r\n            <div class=\"detailsDesc\">{{ details.liveDescribes || '暂无简介' }}</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane interact-pane\" v-else>\r\n            <!-- 评论列表 -->\r\n            <div class=\"comments-container\" ref=\"commentsContainer\">\r\n              <div v-for=\"comment in comments\" :key=\"comment.id\" class=\"comment-item\">\r\n                <!-- 主评论 -->\r\n                <div class=\"comment-main\">\r\n                  <div class=\"comment-content\">\r\n                    <div class=\"comment-header\">\r\n                      <span class=\"comment-nickname\">{{ comment.commentUserName }}</span>\r\n                      <span class=\"comment-time\">{{ format(comment.createDate) }}</span>\r\n                    </div>\r\n                    <div class=\"comment-text\">{{ comment.commentContent }}</div>\r\n                    <div class=\"comment-actions\">\r\n                      <span class=\"action-item\" @click=\"showReplyInput(comment.id)\">跟帖互动</span>\r\n                      <span class=\"action-item like-btn\" @click=\"toggleLike(comment)\"\r\n                        :class=\"{ liked: comment.hasClickPraises }\">\r\n                        <img\r\n                          :src=\"comment.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png')\"\r\n                          alt=\"点赞\" class=\"like-icon\" />\r\n                        <span class=\"like-count\">({{ comment.praisesCount || 0 }})</span>\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 回复列表 -->\r\n                <div v-if=\"comment.replies && comment.replies.length > 0\" class=\"replies-container\">\r\n                  <div v-for=\"reply in comment.replies\" :key=\"reply.id\" class=\"reply-item\">\r\n                    <div class=\"comment-content\">\r\n                      <div class=\"comment-header\">\r\n                        <span class=\"comment-nickname\">{{ reply.nickname }}</span>\r\n                        <span v-if=\"reply.replyTo\" class=\"reply-to\">回复 @{{ reply.replyTo }}</span>\r\n                        <span class=\"comment-time\">{{ format(reply.time) }}</span>\r\n                      </div>\r\n                      <div class=\"comment-text\">{{ reply.content }}</div>\r\n                      <div class=\"comment-actions\">\r\n                        <span class=\"action-item like-btn\" @click=\"toggleLike(reply)\"\r\n                          :class=\"{ liked: reply.hasClickPraises }\">\r\n                          <img\r\n                            :src=\"reply.hasClickPraises ? require('../../assets/img/fabulous.png') : require('../../assets/img/fabulous_o.png')\"\r\n                            alt=\"点赞\" class=\"like-icon\" />\r\n                          <span class=\"like-count\">({{ reply.praisesCount || 0 }})</span>\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 回复输入框 -->\r\n                <div v-if=\"replyingTo === comment.id\" class=\"reply-input-container\">\r\n                  <div class=\"reply-input-wrapper\">\r\n                    <input v-model=\"replyContent\" type=\"text\"\r\n                      :placeholder=\"replyToUser ? `回复 @${replyToUser}:` : '写下你的回复...'\" class=\"reply-input\"\r\n                      @keyup.enter=\"submitReply(comment.id)\" maxlength=\"200\" ref=\"replyInputRef\" />\r\n                    <div class=\"reply-actions\">\r\n                      <button class=\"cancel-btn\" @click=\"cancelReply\">取消</button>\r\n                      <button class=\"submit-btn\" @click=\"submitReply(comment.id)\" :disabled=\"!replyContent.trim()\">\r\n                        回复\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 空状态 -->\r\n              <div v-if=\"comments.length === 0\" class=\"comments-empty\">\r\n                <div class=\"empty-text\">暂无评论</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 发表评论输入框 -->\r\n            <div class=\"comment-input-area\">\r\n              <div class=\"input-wrapper\">\r\n                <textarea v-model=\"newComment\" placeholder=\"说点什么...\" class=\"comment-input\" @keyup.enter=\"submitComment\"\r\n                  maxlength=\"200\" rows=\"3\" style=\"width: 100%;height: 100%;\"></textarea>\r\n                <div class=\"submit-comment-btn\" @click=\"submitComment\">\r\n                  <div class=\"comment-btn\">发送</div>\r\n                  <img src=\"../../assets/img/icon_live_send.png\" alt=\"发送\" class=\"send-icon\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n\r\n</template>\r\n<script>\r\nexport default { name: 'LiveBroadcastDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onBeforeUnmount, onMounted, computed, watch, nextTick } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport { ElMessage } from 'element-plus'\r\n\r\nimport CountdownTimer from './components/CountdownTimer.vue'\r\nimport VideoPlayer from './components/VideoPlayer.vue'\r\n\r\n// 通用消息提示函数\r\nconst showMessage = (message, type = 'success') => {\r\n  ElMessage({\r\n    message,\r\n    type,\r\n    zIndex: 99999\r\n  })\r\n}\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  id: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback', 'update:modelValue'])\r\nconst details = ref({})\r\nconst logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')\r\n\r\nconst activeTab = ref('details')\r\nconst imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')\r\n\r\n// 组件引用\r\nconst countdownTimerRef = ref(null)\r\nconst videoPlayerRef = ref(null)\r\n\r\n// 回放模式标识\r\nconst isReplayMode = ref(false)\r\n\r\n\r\n\r\n// 视频播放器相关变量已移至VideoPlayer组件\r\n\r\n// 评论相关数据\r\nconst comments = ref([])\r\nconst newComment = ref('')\r\nconst replyingTo = ref(null)\r\nconst replyContent = ref('')\r\nconst replyToUser = ref('')\r\nconst replyInputRef = ref(null)\r\n\r\n// 评论定时刷新\r\nlet commentTimer = null\r\n\r\n// 监听状态变化\r\nwatch(() => details.value.meetingStatus, (newStatus, oldStatus) => {\r\n  console.log('状态变化:', oldStatus, '->', newStatus)\r\n  console.log('当前isReplayMode:', isReplayMode.value)\r\n\r\n  if (newStatus === '进行中' && oldStatus !== '进行中') {\r\n    console.log('切换到进行中状态')\r\n\r\n    // 只有在有推流地址时才初始化播放器\r\n    if (details.value.liveUrl) {\r\n      console.log('进行中状态且有推流地址：手动触发VideoPlayer初始化')\r\n      nextTick(() => {\r\n        if (videoPlayerRef.value) {\r\n          videoPlayerRef.value.initPlayer()\r\n        }\r\n      })\r\n    } else {\r\n      console.log('进行中状态但没有推流地址：显示暂无配置提示')\r\n    }\r\n  } else if (newStatus === '无推流' && oldStatus !== '无推流') {\r\n    console.log('切换到无推流状态（倒计时结束但无推流地址）')\r\n    // 无推流状态不需要初始化播放器，只显示提示\r\n  } else if (newStatus === '已结束' && oldStatus !== '已结束') {\r\n    console.log('切换到已结束状态')\r\n\r\n    // 如果是回放模式且有回放地址，初始化回放播放器\r\n    if (isReplayMode.value && details.value.liveReplayUrl) {\r\n      console.log('回放模式：检测到回放地址，手动触发VideoPlayer初始化')\r\n      nextTick(() => {\r\n        if (videoPlayerRef.value) {\r\n          videoPlayerRef.value.initPlayer()\r\n        }\r\n      })\r\n    }\r\n  } else if ((newStatus !== '进行中' && oldStatus === '进行中') ||\r\n    (newStatus !== '已结束' && oldStatus === '已结束' && isReplayMode.value)) {\r\n    // 从进行中或回放状态变为其他状态，销毁播放器\r\n    if (videoPlayerRef.value) {\r\n      videoPlayerRef.value.destroyVideoPlayer()\r\n    }\r\n  }\r\n\r\n  // 从无推流状态切换到其他状态时，不需要特殊处理\r\n  // 因为无推流状态没有播放器需要销毁\r\n})\r\n\r\n\r\n\r\n// 倒计时结束处理\r\nconst handleCountdownEnd = () => {\r\n  console.log('倒计时结束处理')\r\n  // 倒计时结束后检查是否有推流地址\r\n  if (details.value.liveUrl && details.value.meetingStatus === '未开始') {\r\n    console.log('倒计时结束，检测到推流地址，自动切换到进行中状态')\r\n    isReplayMode.value = false\r\n    details.value.meetingStatus = '进行中'\r\n  } else if (!details.value.liveUrl && details.value.meetingStatus === '未开始') {\r\n    console.log('倒计时结束，但没有推流地址，设置为无推流状态')\r\n    isReplayMode.value = false\r\n    details.value.meetingStatus = '无推流'  // 新增状态：倒计时结束但无推流地址\r\n  }\r\n}\r\n\r\n// 根据数据判断实际状态\r\nconst determineActualStatus = () => {\r\n  if (!details.value) return\r\n\r\n  console.log('determineActualStatus: 开始判断实际状态')\r\n  console.log('当前meetingStatus:', details.value.meetingStatus)\r\n  console.log('liveUrl:', !!details.value.liveUrl)\r\n  console.log('liveReplayUrl:', !!details.value.liveReplayUrl)\r\n\r\n  // 如果是未开始状态，不要修改，保持倒计时\r\n  if (details.value.meetingStatus === '未开始') {\r\n    console.log('保持未开始状态，等待倒计时结束')\r\n    return\r\n  }\r\n\r\n  // 新逻辑：根据回放地址和推流地址判断状态\r\n  if (details.value.liveReplayUrl) {\r\n    // 1. 如果有回放地址 → 已结束状态\r\n    console.log('检测到回放地址，设置为已结束状态')\r\n    details.value.meetingStatus = '已结束'\r\n    isReplayMode.value = false  // 重置回放模式\r\n    return\r\n  }\r\n\r\n  if (details.value.liveUrl) {\r\n    // 2. 如果没有回放地址但有推流地址 → 进行中状态（还在直播）\r\n    console.log('没有回放地址但有推流地址，设置为进行中状态（还在直播）')\r\n    details.value.meetingStatus = '进行中'\r\n    isReplayMode.value = false  // 确保是直播模式\r\n    return\r\n  }\r\n\r\n  // 3. 如果既没有回放地址也没有推流地址 → 已结束状态\r\n  console.log('既没有回放地址也没有推流地址，设置为已结束状态')\r\n  details.value.meetingStatus = '已结束'\r\n  isReplayMode.value = false\r\n}\r\n\r\nconst applyStatusFromProps = () => {\r\n  console.log('applyStatusFromProps: 开始应用状态')\r\n\r\n  // 先根据数据判断实际状态\r\n  determineActualStatus()\r\n\r\n  if (details.value.meetingStatus === '进行中') {\r\n    console.log('当前状态为进行中，准备初始化VideoPlayer')\r\n    console.log('isReplayMode:', isReplayMode.value)\r\n    console.log('liveUrl:', !!details.value.liveUrl)\r\n\r\n    // 只有在有推流地址时才初始化VideoPlayer\r\n    if (details.value.liveUrl) {\r\n      // 延迟一点时间确保VideoPlayer组件已经渲染\r\n      setTimeout(() => {\r\n        if (videoPlayerRef.value) {\r\n          if (isReplayMode.value && details.value.liveReplayUrl) {\r\n            console.log('强制初始化回放VideoPlayer')\r\n            videoPlayerRef.value.initPlayer()\r\n          } else if (!isReplayMode.value && details.value.liveUrl) {\r\n            console.log('强制初始化直播VideoPlayer')\r\n            videoPlayerRef.value.initPlayer()\r\n          }\r\n        }\r\n      }, 100)\r\n    } else {\r\n      console.log('进行中状态但没有推流地址，不初始化VideoPlayer')\r\n    }\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\r\n  if (props.modelValue && props.id) {\r\n    getInfo()\r\n    getCommentData()\r\n    // 开始评论定时刷新\r\n    startCommentRefresh()\r\n  }\r\n  applyStatusFromProps()\r\n})\r\n\r\n// 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\r\nwatch(() => props.modelValue, (newVal, oldVal) => {\r\n  console.log('modelValue变化:', oldVal, '->', newVal, 'id:', props.id)\r\n\r\n  if (newVal && props.id && !oldVal) {\r\n    // 从 false 变为 true 且有 id 时加载数据\r\n    console.log('弹窗打开，开始加载数据')\r\n\r\n    // 重置状态，确保每次打开都是干净的状态\r\n    isReplayMode.value = false\r\n    activeTab.value = 'details'\r\n\r\n    getInfo()\r\n    getCommentData()\r\n    // 开始评论定时刷新\r\n    startCommentRefresh()\r\n  } else if (!newVal && oldVal) {\r\n    // 从显示变为隐藏时，停止评论定时刷新\r\n    console.log('弹窗关闭，停止定时刷新')\r\n    stopCommentRefresh()\r\n  }\r\n})\r\n\r\n// 监听 id 变化 - 当 id 改变且组件显示时加载数据\r\nwatch(() => props.id, (newVal, oldVal) => {\r\n  if (newVal && props.modelValue && newVal !== oldVal) {\r\n    getInfo()\r\n    getCommentData()\r\n    // 重新开始评论定时刷新\r\n    startCommentRefresh()\r\n  }\r\n})\r\n\r\nconst getInfo = async () => {\r\n  console.log('getInfo: 开始获取数据，id =', props.id)\r\n\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n\r\n  console.log('getInfo: 数据加载完成')\r\n  console.log('meetingStatus:', data.meetingStatus)\r\n  console.log('liveUrl:', !!data.liveUrl)\r\n  console.log('liveReplayUrl:', !!data.liveReplayUrl)\r\n\r\n  // 应用状态\r\n  applyStatusFromProps()\r\n\r\n  // 额外的保障：如果是进行中状态，确保VideoPlayer能够初始化\r\n  if (data.meetingStatus === '进行中') {\r\n    console.log('getInfo: 检测到进行中状态，延迟初始化VideoPlayer')\r\n    setTimeout(() => {\r\n      if (videoPlayerRef.value) {\r\n        if (isReplayMode.value && data.liveReplayUrl) {\r\n          console.log('getInfo: 延迟初始化回放VideoPlayer')\r\n          videoPlayerRef.value.initPlayer()\r\n        } else if (!isReplayMode.value && data.liveUrl) {\r\n          console.log('getInfo: 延迟初始化直播VideoPlayer')\r\n          videoPlayerRef.value.initPlayer()\r\n        }\r\n      }\r\n    }, 200)\r\n  }\r\n}\r\n\r\n// 获取评论数据\r\nconst getCommentData = async () => {\r\n  const res = await api.twoLevelTree({\r\n    businessCode: 'liveBroadcast',\r\n    businessId: props.id,\r\n    pageNo: 1,\r\n    pageSize: 99,\r\n  })\r\n  console.log('评论数据:', res)\r\n  comments.value = res.data || []\r\n}\r\n\r\n// 开始评论定时刷新\r\nconst startCommentRefresh = () => {\r\n  if (commentTimer) clearInterval(commentTimer)\r\n  commentTimer = setInterval(() => {\r\n    getCommentData()\r\n  }, 3000) // 每3秒刷新一次\r\n}\r\n\r\n// 停止评论定时刷新\r\nconst stopCommentRefresh = () => {\r\n  if (commentTimer) {\r\n    clearInterval(commentTimer)\r\n    commentTimer = null\r\n  }\r\n}\r\n\r\n// 打开回放\r\nconst handleReplay = () => {\r\n  console.log('handleReplay: 开始处理回放请求')\r\n  console.log('handleReplay: 回放地址:', details.value.liveReplayUrl)\r\n\r\n  if (!details.value.liveReplayUrl) {\r\n    console.warn('没有回放地址')\r\n    return\r\n  }\r\n\r\n  // 设置回放模式\r\n  console.log('handleReplay: 设置回放模式为true')\r\n  isReplayMode.value = true\r\n\r\n  // 确保VideoPlayer组件能够正确初始化\r\n  nextTick(() => {\r\n    if (videoPlayerRef.value) {\r\n      console.log('handleReplay: 手动触发VideoPlayer初始化')\r\n      videoPlayerRef.value.initPlayer()\r\n    } else {\r\n      console.warn('handleReplay: videoPlayerRef不存在')\r\n    }\r\n  })\r\n}\r\n\r\n// 提交新评论\r\nconst submitComment = async () => {\r\n  if (!newComment.value.trim()) return\r\n  const res = await api.commentNew({\r\n    form: {\r\n      businessCode: 'liveBroadcast',\r\n      businessId: props.id,\r\n      checkedStatus: 1,\r\n      commentContent: newComment.value,\r\n      parentId: \"\",\r\n      terminalName: 'PC'\r\n    }\r\n  })\r\n\r\n  if (res.code == 200) {\r\n    showMessage('评论发表成功')\r\n    getCommentData()\r\n  } else {\r\n    showMessage(res.message || '评论发表失败', 'error')\r\n  }\r\n  newComment.value = ''\r\n}\r\n\r\n// 点赞功能\r\nconst toggleLike = async (item) => {\r\n  if (item.hasClickPraises) {\r\n    // 取消点赞\r\n    const res = await api.praisesDels({\r\n      businessCode: \"comment\",\r\n      businessId: item.id\r\n    })\r\n\r\n    if (res.code == 200) {\r\n      item.hasClickPraises = false\r\n      item.praisesCount = Math.max(0, (item.praisesCount || 0) - 1)\r\n      ElMessage({\r\n        message: '取消点赞成功',\r\n        type: 'success'\r\n      })\r\n    } else {\r\n      ElMessage({\r\n        message: res.message || '取消点赞失败',\r\n        type: 'error'\r\n      })\r\n    }\r\n  } else {\r\n    // 点赞\r\n    const res = await api.praisesAdd({\r\n      form: {\r\n        businessCode: \"comment\",\r\n        businessId: item.id\r\n      }\r\n    })\r\n\r\n    if (res.code == 200) {\r\n      item.hasClickPraises = true\r\n      item.praisesCount = (item.praisesCount || 0) + 1\r\n      ElMessage({\r\n        message: '点赞成功',\r\n        type: 'success'\r\n      })\r\n    } else {\r\n      ElMessage({\r\n        message: res.message || '点赞失败',\r\n        type: 'error'\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n// 显示回复输入框\r\nconst showReplyInput = (commentId, replyToUsername = '') => {\r\n  replyingTo.value = commentId\r\n  replyToUser.value = replyToUsername\r\n  replyContent.value = ''\r\n\r\n  nextTick(() => {\r\n    if (replyInputRef.value) {\r\n      replyInputRef.value.focus()\r\n    }\r\n  })\r\n}\r\n\r\n// 取消回复\r\nconst cancelReply = () => {\r\n  replyingTo.value = null\r\n  replyToUser.value = ''\r\n  replyContent.value = ''\r\n}\r\n\r\n// 提交回复\r\nconst submitReply = async (commentId) => {\r\n  if (!replyContent.value.trim()) return\r\n  const res = await api.commentNew({\r\n    form: {\r\n      businessCode: \"liveBroadcast\",\r\n      businessId: props.id,\r\n      checkedStatus: 1,\r\n      commentContent: replyContent.value.trim(),\r\n      parentId: commentId,\r\n      terminalName: \"PC\"\r\n    }\r\n  })\r\n  if (res.code == 200) {\r\n    ElMessage({\r\n      message: '回复发表成功',\r\n      type: 'success'\r\n    })\r\n    getCommentData()\r\n    cancelReply()\r\n  } else {\r\n    ElMessage({\r\n      message: res.message || '回复发表失败',\r\n      type: 'error'\r\n    })\r\n  }\r\n}\r\n\r\nonBeforeUnmount(() => {\r\n  // 清理评论定时器\r\n  stopCommentRefresh()\r\n  // 销毁视频播放器\r\n  if (videoPlayerRef.value) {\r\n    videoPlayerRef.value.destroyVideoPlayer()\r\n  }\r\n})\r\n\r\nconst handleClose = () => {\r\n  console.log('handleClose: 开始关闭弹窗')\r\n\r\n  // 停止评论定时刷新\r\n  stopCommentRefresh()\r\n\r\n  // 销毁视频播放器\r\n  if (videoPlayerRef.value) {\r\n    console.log('handleClose: 销毁VideoPlayer')\r\n    videoPlayerRef.value.destroyVideoPlayer()\r\n  }\r\n\r\n  // 重置状态\r\n  console.log('handleClose: 重置组件状态')\r\n  isReplayMode.value = false\r\n  activeTab.value = 'details'\r\n\r\n  // 清空数据引用，确保下次打开时重新加载\r\n  details.value = {}\r\n\r\n  emit('update:modelValue', false)\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveBroadcastDetails {\r\n  position: fixed;\r\n  inset: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: #0F0F0F;\r\n  display: flex;\r\n  flex-direction: column;\r\n  z-index: 1000;\r\n\r\n  .LiveBroadcastDetailsHeader {\r\n    position: relative;\r\n    height: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 16px;\r\n    color: #fff;\r\n    background: #2B2B2B;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.08);\r\n\r\n    .LiveBroadcastDetailsEmblem {\r\n      width: 58px;\r\n      height: 58px;\r\n      margin-right: 14px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsHeadInfo {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTitle {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #D9D9D9;\r\n    }\r\n\r\n    .LiveBroadcastDetailsClose {\r\n      position: absolute;\r\n      right: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      color: #fff;\r\n      cursor: pointer;\r\n      font-size: 18px;\r\n      opacity: .85;\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsBody {\r\n    flex: 1;\r\n    display: grid;\r\n    grid-template-columns: 1fr 360px;\r\n    gap: 16px;\r\n    padding: 16px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .LiveBroadcastDetailsCanvas {\r\n    position: relative;\r\n    height: 100%;\r\n    background: #111;\r\n    overflow: hidden;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LiveBroadcastDetailsPoster {\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #000;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: #d23a2e;\r\n      font-weight: bold;\r\n      font-size: 22px;\r\n    }\r\n\r\n    // 倒计时样式已移至CountdownTimer组件\r\n\r\n    .LiveBroadcastDetailsLiveOverlay {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      // 播放器样式已移至VideoPlayer组件\r\n    }\r\n\r\n    .LiveBroadcastDetailsNoStream {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .no-stream-bg {\r\n        position: absolute;\r\n        inset: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: contain;\r\n        z-index: 1;\r\n      }\r\n\r\n      // 遮罩层覆盖整个图片\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        background: rgba(0, 0, 0, 0.45);\r\n        z-index: 2;\r\n      }\r\n\r\n      .no-stream-wrap {\r\n        position: relative;\r\n        z-index: 3;\r\n        text-align: center;\r\n\r\n        .no-stream-title {\r\n          font-weight: 400;\r\n          font-size: 16px;\r\n          color: #FFFFFF;\r\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsEnded {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .LiveBroadcastDetailsEndedBg {\r\n        position: absolute;\r\n        inset: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: contain;\r\n        z-index: 1;\r\n      }\r\n\r\n      // 遮罩层覆盖整个图片\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        background: rgba(0, 0, 0, 0.45);\r\n        z-index: 2;\r\n      }\r\n\r\n      .LiveBroadcastDetailsEndedWrap {\r\n        position: relative;\r\n        z-index: 3;\r\n        text-align: center;\r\n\r\n        .endedTitle {\r\n          font-weight: 400;\r\n          font-size: 16px;\r\n          color: #FFFFFF;\r\n          margin-bottom: 20px;\r\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\r\n        }\r\n\r\n        .replayBtn {\r\n          background: linear-gradient(90deg, #2F04FF 0%, #00F0FE 100%);\r\n          border-radius: 6px;\r\n          border: none;\r\n          padding: 12px 0;\r\n          font-size: 18px;\r\n          color: #FFFFFF;\r\n          width: 175px;\r\n          height: 44px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsSidebar {\r\n    height: 100%;\r\n    background: #191919;\r\n    border-left: 1px solid rgba(255, 255, 255, 0.05);\r\n    color: #e8e8e8;\r\n    // padding: 14px 16px 16px 16px;\r\n    overflow: auto;\r\n\r\n    .LiveBroadcastDetailsTabs {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      gap: 40px;\r\n      margin-bottom: 10px;\r\n      background: #2B2B2B;\r\n      padding: 14px 16px;\r\n\r\n      .LiveBroadcastDetailsTab {\r\n        cursor: pointer;\r\n        color: #999999;\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        transition: color .2s ease;\r\n        font-weight: 500;\r\n\r\n        &.active {\r\n          color: #ffffff;\r\n          font-weight: 700;\r\n        }\r\n\r\n        &.active::after {\r\n          content: '';\r\n          position: absolute;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: -8px;\r\n          height: 3px;\r\n          background: #54BDFF;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsTabPane {\r\n      padding: 12px 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelTitle {\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      font-size: 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelText {\r\n      font-size: 13px;\r\n      line-height: 1.8;\r\n      color: #cfcfcf;\r\n    }\r\n\r\n    /* 详情tab内样式 */\r\n    .detailsTitle {\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #FFFFFF;\r\n    }\r\n\r\n    .detailsTimeBox {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 14px;\r\n\r\n      .detailsTime {\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #666666;\r\n      }\r\n\r\n      .detailsTimeIcon {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n\r\n\r\n    .detailsDesc {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #FFFFFF;\r\n      line-height: 24px;\r\n      margin-top: 24px;\r\n    }\r\n\r\n    /* 互动评论样式 */\r\n    .interact-pane {\r\n      display: flex;\r\n      flex-direction: column;\r\n      height: calc(100vh - 80px - 60px - 32px);\r\n      padding: 0 !important;\r\n    }\r\n\r\n    .comments-container {\r\n      flex: 1;\r\n      overflow-y: auto;\r\n      padding: 12px 15px;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 4px;\r\n      }\r\n\r\n      &::-webkit-scrollbar-track {\r\n        background: #2B2B2B;\r\n      }\r\n\r\n      &::-webkit-scrollbar-thumb {\r\n        background: #666;\r\n        border-radius: 2px;\r\n      }\r\n    }\r\n\r\n    .comment-item {\r\n      margin-bottom: 20px;\r\n\r\n      .comment-main {\r\n        display: flex;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .comment-content {\r\n        flex: 1;\r\n        min-width: 0;\r\n\r\n        .comment-header {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 6px;\r\n\r\n          .comment-nickname {\r\n            font-size: 13px;\r\n            color: #54BDFF;\r\n            margin-right: 8px;\r\n            font-weight: 500;\r\n          }\r\n\r\n          .reply-to {\r\n            font-size: 12px;\r\n            color: #999999;\r\n            margin-right: 8px;\r\n          }\r\n\r\n          .comment-time {\r\n            font-size: 11px;\r\n            color: #666666;\r\n          }\r\n        }\r\n\r\n        .comment-text {\r\n          font-size: 14px;\r\n          color: #FFFFFF;\r\n          line-height: 20px;\r\n          word-wrap: break-word;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .comment-actions {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 16px;\r\n          margin-bottom: 8px;\r\n\r\n          .action-item {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 4px;\r\n            font-size: 12px;\r\n            color: #999999;\r\n            cursor: pointer;\r\n            transition: color 0.2s;\r\n\r\n            &:hover {\r\n              color: #54BDFF;\r\n            }\r\n\r\n            &.like-btn {\r\n              &.liked {\r\n                color: #54BDFF;\r\n              }\r\n            }\r\n\r\n            .like-icon {\r\n              width: 14px;\r\n              height: 14px;\r\n            }\r\n\r\n            .like-count {\r\n              font-size: 11px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 回复列表样式\r\n    .replies-container {\r\n      margin-left: 42px;\r\n      border-left: 2px solid rgba(255, 255, 255, 0.05);\r\n      padding-left: 12px;\r\n\r\n      .reply-item {\r\n        margin-bottom: 12px;\r\n\r\n        .comment-content {\r\n          .comment-header {\r\n            .comment-nickname {\r\n              font-size: 12px;\r\n            }\r\n          }\r\n\r\n          .comment-text {\r\n            font-size: 13px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 回复输入框样式\r\n    .reply-input-container {\r\n      margin-left: 42px;\r\n      margin-top: 8px;\r\n\r\n      .reply-input-wrapper {\r\n        background: #2B2B2B;\r\n        border-radius: 6px;\r\n        padding: 8px;\r\n\r\n        .reply-input {\r\n          width: 100%;\r\n          height: 32px;\r\n          padding: 0 8px;\r\n          background: #191919;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          border-radius: 4px;\r\n          color: #FFFFFF;\r\n          font-size: 13px;\r\n          outline: none;\r\n          margin-bottom: 8px;\r\n\r\n          &::placeholder {\r\n            color: #666666;\r\n          }\r\n\r\n          &:focus {\r\n            border-color: #54BDFF;\r\n          }\r\n        }\r\n\r\n        .reply-actions {\r\n          display: flex;\r\n          justify-content: flex-end;\r\n          gap: 8px;\r\n\r\n          .cancel-btn,\r\n          .submit-btn {\r\n            height: 28px;\r\n            padding: 0 12px;\r\n            border: none;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            cursor: pointer;\r\n            transition: background-color 0.2s;\r\n          }\r\n\r\n          .cancel-btn {\r\n            background: transparent;\r\n            color: #999999;\r\n\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.05);\r\n            }\r\n          }\r\n\r\n          .submit-btn {\r\n            background: #54BDFF;\r\n            color: #FFFFFF;\r\n\r\n            &:hover:not(:disabled) {\r\n              background: #4AA8E8;\r\n            }\r\n\r\n            &:disabled {\r\n              background: #333333;\r\n              color: #666666;\r\n              cursor: not-allowed;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .comments-empty {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 200px;\r\n\r\n      .empty-text {\r\n        font-size: 14px;\r\n        color: #666666;\r\n      }\r\n    }\r\n\r\n    .comment-input-area {\r\n      border-top: 1px solid rgba(255, 255, 255, 0.08);\r\n      padding: 12px;\r\n\r\n      .input-wrapper {\r\n        background: #4A4A4A;\r\n        border-radius: 8px;\r\n        padding: 12px;\r\n        height: 90px;\r\n        position: relative;\r\n\r\n        .comment-input {\r\n          width: 100%;\r\n          height: calc(100% - 40px);\r\n          padding: 0;\r\n          padding-right: 80px;\r\n          background: transparent;\r\n          border: none;\r\n          color: #FFFFFF;\r\n          font-size: 14px;\r\n          outline: none;\r\n          resize: none;\r\n          font-family: inherit;\r\n          line-height: 1.4;\r\n\r\n          &::placeholder {\r\n            color: #999999;\r\n          }\r\n        }\r\n\r\n        .submit-comment-btn {\r\n          position: absolute;\r\n          bottom: 8px;\r\n          right: 8px;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 4px;\r\n\r\n          .comment-btn {\r\n            color: #54BDFF;\r\n            font-size: 17px;\r\n            font-weight: 500;\r\n          }\r\n\r\n          .send-icon {\r\n            width: 18px;\r\n            height: 18px;\r\n          }\r\n\r\n          &:disabled {\r\n            background: #666666;\r\n            color: #999999;\r\n            cursor: not-allowed;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.details-fade-enter-active,\r\n.details-fade-leave-active {\r\n  transition: opacity .2s ease;\r\n}\r\n\r\n.details-fade-enter-from,\r\n.details-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": "+CA0KA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,KAAK;AAChF,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,SAAS,QAAQ,cAAc;AAExC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,WAAW,MAAM,8BAA8B;;AAEtD;;AAZA,IAAAC,WAAA,GAAe;EAAEvC,IAAI,EAAE;AAAuB,CAAC;;;;;;;;;;;;;;;;;IAa/C,IAAMwC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,OAAO,EAAuB;MAAA,IAArB/F,IAAI,GAAA6E,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAmB,SAAA,GAAAnB,SAAA,MAAG,SAAS;MAC5Ca,SAAS,CAAC;QACRK,OAAO;QACP/F,IAAI;QACJiG,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAA8C;IAC3D,IAAMC,OAAO,GAAGpB,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMqB,OAAO,GAAGrB,GAAG,CAAC,oEAAoE,CAAC;IAEzF,IAAMsB,SAAS,GAAGtB,GAAG,CAAC,SAAS,CAAC;IAChC,IAAMuB,MAAM,GAAGpB,QAAQ,CAAC;MAAA,OAAMiB,OAAO,CAACzH,KAAK,CAAC6H,QAAQ,GAAGzB,GAAG,CAAC0B,OAAO,CAACL,OAAO,CAACzH,KAAK,CAAC6H,QAAQ,CAAC,GAAG,EAAE;IAAA,EAAC;;IAEhG;IACA,IAAME,iBAAiB,GAAG1B,GAAG,CAAC,IAAI,CAAC;IACnC,IAAM2B,cAAc,GAAG3B,GAAG,CAAC,IAAI,CAAC;;IAEhC;IACA,IAAM4B,YAAY,GAAG5B,GAAG,CAAC,KAAK,CAAC;;IAI/B;;IAEA;IACA,IAAM6B,QAAQ,GAAG7B,GAAG,CAAC,EAAE,CAAC;IACxB,IAAM8B,UAAU,GAAG9B,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAM+B,UAAU,GAAG/B,GAAG,CAAC,IAAI,CAAC;IAC5B,IAAMgC,YAAY,GAAGhC,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMiC,WAAW,GAAGjC,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMkC,aAAa,GAAGlC,GAAG,CAAC,IAAI,CAAC;;IAE/B;IACA,IAAImC,YAAY,GAAG,IAAI;;IAEvB;IACA/B,KAAK,CAAC;MAAA,OAAMgB,OAAO,CAACzH,KAAK,CAACyI,aAAa;IAAA,GAAE,UAACC,SAAS,EAAEC,SAAS,EAAK;MACjEC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,SAAS,EAAE,IAAI,EAAED,SAAS,CAAC;MAChDE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEZ,YAAY,CAACjI,KAAK,CAAC;MAElD,IAAI0I,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,EAAE;QAC9CC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;;QAEvB;QACA,IAAIpB,OAAO,CAACzH,KAAK,CAAC8I,OAAO,EAAE;UACzBF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7CnC,QAAQ,CAAC,YAAM;YACb,IAAIsB,cAAc,CAAChI,KAAK,EAAE;cACxBgI,cAAc,CAAChI,KAAK,CAAC+I,UAAU,CAAC,CAAC;YACnC;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLH,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACtC;MACF,CAAC,MAAM,IAAIH,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,EAAE;QACrDC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpC;MACF,CAAC,MAAM,IAAIH,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,EAAE;QACrDC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;;QAEvB;QACA,IAAIZ,YAAY,CAACjI,KAAK,IAAIyH,OAAO,CAACzH,KAAK,CAACgJ,aAAa,EAAE;UACrDJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CnC,QAAQ,CAAC,YAAM;YACb,IAAIsB,cAAc,CAAChI,KAAK,EAAE;cACxBgI,cAAc,CAAChI,KAAK,CAAC+I,UAAU,CAAC,CAAC;YACnC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM,IAAKL,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,IACnDD,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,IAAIV,YAAY,CAACjI,KAAM,EAAE;QACpE;QACA,IAAIgI,cAAc,CAAChI,KAAK,EAAE;UACxBgI,cAAc,CAAChI,KAAK,CAACiJ,kBAAkB,CAAC,CAAC;QAC3C;MACF;;MAEA;MACA;IACF,CAAC,CAAC;;IAIF;IACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/BN,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB;MACA,IAAIpB,OAAO,CAACzH,KAAK,CAAC8I,OAAO,IAAIrB,OAAO,CAACzH,KAAK,CAACyI,aAAa,KAAK,KAAK,EAAE;QAClEG,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvCZ,YAAY,CAACjI,KAAK,GAAG,KAAK;QAC1ByH,OAAO,CAACzH,KAAK,CAACyI,aAAa,GAAG,KAAK;MACrC,CAAC,MAAM,IAAI,CAAChB,OAAO,CAACzH,KAAK,CAAC8I,OAAO,IAAIrB,OAAO,CAACzH,KAAK,CAACyI,aAAa,KAAK,KAAK,EAAE;QAC1EG,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrCZ,YAAY,CAACjI,KAAK,GAAG,KAAK;QAC1ByH,OAAO,CAACzH,KAAK,CAACyI,aAAa,GAAG,KAAK,EAAE;MACvC;IACF,CAAC;;IAED;IACA,IAAMU,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;MAClC,IAAI,CAAC1B,OAAO,CAACzH,KAAK,EAAE;MAEpB4I,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEpB,OAAO,CAACzH,KAAK,CAACyI,aAAa,CAAC;MAC5DG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,CAAC,CAACpB,OAAO,CAACzH,KAAK,CAAC8I,OAAO,CAAC;MAChDF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAACpB,OAAO,CAACzH,KAAK,CAACgJ,aAAa,CAAC;;MAE5D;MACA,IAAIvB,OAAO,CAACzH,KAAK,CAACyI,aAAa,KAAK,KAAK,EAAE;QACzCG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;QAC9B;MACF;;MAEA;MACA,IAAIpB,OAAO,CAACzH,KAAK,CAACgJ,aAAa,EAAE;QAC/B;QACAJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/BpB,OAAO,CAACzH,KAAK,CAACyI,aAAa,GAAG,KAAK;QACnCR,YAAY,CAACjI,KAAK,GAAG,KAAK,EAAE;QAC5B;MACF;MAEA,IAAIyH,OAAO,CAACzH,KAAK,CAAC8I,OAAO,EAAE;QACzB;QACAF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CpB,OAAO,CAACzH,KAAK,CAACyI,aAAa,GAAG,KAAK;QACnCR,YAAY,CAACjI,KAAK,GAAG,KAAK,EAAE;QAC5B;MACF;;MAEA;MACA4I,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACtCpB,OAAO,CAACzH,KAAK,CAACyI,aAAa,GAAG,KAAK;MACnCR,YAAY,CAACjI,KAAK,GAAG,KAAK;IAC5B,CAAC;IAED,IAAMoJ,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjCR,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;MAE3C;MACAM,qBAAqB,CAAC,CAAC;MAEvB,IAAI1B,OAAO,CAACzH,KAAK,CAACyI,aAAa,KAAK,KAAK,EAAE;QACzCG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEZ,YAAY,CAACjI,KAAK,CAAC;QAChD4I,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,CAAC,CAACpB,OAAO,CAACzH,KAAK,CAAC8I,OAAO,CAAC;;QAEhD;QACA,IAAIrB,OAAO,CAACzH,KAAK,CAAC8I,OAAO,EAAE;UACzB;UACAO,UAAU,CAAC,YAAM;YACf,IAAIrB,cAAc,CAAChI,KAAK,EAAE;cACxB,IAAIiI,YAAY,CAACjI,KAAK,IAAIyH,OAAO,CAACzH,KAAK,CAACgJ,aAAa,EAAE;gBACrDJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;gBACjCb,cAAc,CAAChI,KAAK,CAAC+I,UAAU,CAAC,CAAC;cACnC,CAAC,MAAM,IAAI,CAACd,YAAY,CAACjI,KAAK,IAAIyH,OAAO,CAACzH,KAAK,CAAC8I,OAAO,EAAE;gBACvDF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;gBACjCb,cAAc,CAAChI,KAAK,CAAC+I,UAAU,CAAC,CAAC;cACnC;YACF;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLH,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC7C;MACF;IACF,CAAC;IAEDtC,SAAS,CAAC,YAAM;MACd;MACA,IAAIc,KAAK,CAACiC,UAAU,IAAIjC,KAAK,CAACkC,EAAE,EAAE;QAChCC,OAAO,CAAC,CAAC;QACTC,cAAc,CAAC,CAAC;QAChB;QACAC,mBAAmB,CAAC,CAAC;MACvB;MACAN,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEF;IACA3C,KAAK,CAAC;MAAA,OAAMY,KAAK,CAACiC,UAAU;IAAA,GAAE,UAACK,MAAM,EAAEC,MAAM,EAAK;MAChDhB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEe,MAAM,EAAE,IAAI,EAAED,MAAM,EAAE,KAAK,EAAEtC,KAAK,CAACkC,EAAE,CAAC;MAEnE,IAAII,MAAM,IAAItC,KAAK,CAACkC,EAAE,IAAI,CAACK,MAAM,EAAE;QACjC;QACAhB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;QAE1B;QACAZ,YAAY,CAACjI,KAAK,GAAG,KAAK;QAC1B2H,SAAS,CAAC3H,KAAK,GAAG,SAAS;QAE3BwJ,OAAO,CAAC,CAAC;QACTC,cAAc,CAAC,CAAC;QAChB;QACAC,mBAAmB,CAAC,CAAC;MACvB,CAAC,MAAM,IAAI,CAACC,MAAM,IAAIC,MAAM,EAAE;QAC5B;QACAhB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1BgB,kBAAkB,CAAC,CAAC;MACtB;IACF,CAAC,CAAC;;IAEF;IACApD,KAAK,CAAC;MAAA,OAAMY,KAAK,CAACkC,EAAE;IAAA,GAAE,UAACI,MAAM,EAAEC,MAAM,EAAK;MACxC,IAAID,MAAM,IAAItC,KAAK,CAACiC,UAAU,IAAIK,MAAM,KAAKC,MAAM,EAAE;QACnDJ,OAAO,CAAC,CAAC;QACTC,cAAc,CAAC,CAAC;QAChB;QACAC,mBAAmB,CAAC,CAAC;MACvB;IACF,CAAC,CAAC;IAEF,IAAMF,OAAO;MAAA,IAAAM,KAAA,GAAA/D,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqF,QAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAA3K,mBAAA,GAAAuB,IAAA,UAAAqJ,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAhF,IAAA,GAAAgF,QAAA,CAAA3G,IAAA;YAAA;cACdoF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAExB,KAAK,CAACkC,EAAE,CAAC;cAAAY,QAAA,CAAA3G,IAAA;cAAA,OAE3B4C,GAAG,CAACgE,mBAAmB,CAAC;gBAAEC,QAAQ,EAAEhD,KAAK,CAACkC;cAAG,CAAC,CAAC;YAAA;cAA3DS,GAAG,GAAAG,QAAA,CAAAlH,IAAA;cACHgH,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVxC,OAAO,CAACzH,KAAK,GAAGiK,IAAI;cAEpBrB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;cAC9BD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEoB,IAAI,CAACxB,aAAa,CAAC;cACjDG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,CAAC,CAACoB,IAAI,CAACnB,OAAO,CAAC;cACvCF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAACoB,IAAI,CAACjB,aAAa,CAAC;;cAEnD;cACAI,oBAAoB,CAAC,CAAC;;cAEtB;cACA,IAAIa,IAAI,CAACxB,aAAa,KAAK,KAAK,EAAE;gBAChCG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;gBACjDQ,UAAU,CAAC,YAAM;kBACf,IAAIrB,cAAc,CAAChI,KAAK,EAAE;oBACxB,IAAIiI,YAAY,CAACjI,KAAK,IAAIiK,IAAI,CAACjB,aAAa,EAAE;sBAC5CJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;sBAC1Cb,cAAc,CAAChI,KAAK,CAAC+I,UAAU,CAAC,CAAC;oBACnC,CAAC,MAAM,IAAI,CAACd,YAAY,CAACjI,KAAK,IAAIiK,IAAI,CAACnB,OAAO,EAAE;sBAC9CF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;sBAC1Cb,cAAc,CAAChI,KAAK,CAAC+I,UAAU,CAAC,CAAC;oBACnC;kBACF;gBACF,CAAC,EAAE,GAAG,CAAC;cACT;YAAC;YAAA;cAAA,OAAAoB,QAAA,CAAA7E,IAAA;UAAA;QAAA,GAAAyE,OAAA;MAAA,CACF;MAAA,gBA9BKP,OAAOA,CAAA;QAAA,OAAAM,KAAA,CAAA7D,KAAA,OAAAD,SAAA;MAAA;IAAA,GA8BZ;;IAED;IACA,IAAMyD,cAAc;MAAA,IAAAa,KAAA,GAAAvE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6F,SAAA;QAAA,IAAAP,GAAA;QAAA,OAAA1K,mBAAA,GAAAuB,IAAA,UAAA2J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtF,IAAA,GAAAsF,SAAA,CAAAjH,IAAA;YAAA;cAAAiH,SAAA,CAAAjH,IAAA;cAAA,OACH4C,GAAG,CAACsE,YAAY,CAAC;gBACjCC,YAAY,EAAE,eAAe;gBAC7BC,UAAU,EAAEvD,KAAK,CAACkC,EAAE;gBACpBsB,MAAM,EAAE,CAAC;gBACTC,QAAQ,EAAE;cACZ,CAAC,CAAC;YAAA;cALId,GAAG,GAAAS,SAAA,CAAAxH,IAAA;cAMT2F,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEmB,GAAG,CAAC;cACzB9B,QAAQ,CAAClI,KAAK,GAAGgK,GAAG,CAACC,IAAI,IAAI,EAAE;YAAA;YAAA;cAAA,OAAAQ,SAAA,CAAAnF,IAAA;UAAA;QAAA,GAAAiF,QAAA;MAAA,CAChC;MAAA,gBATKd,cAAcA,CAAA;QAAA,OAAAa,KAAA,CAAArE,KAAA,OAAAD,SAAA;MAAA;IAAA,GASnB;;IAED;IACA,IAAM0D,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;MAChC,IAAIlB,YAAY,EAAEuC,aAAa,CAACvC,YAAY,CAAC;MAC7CA,YAAY,GAAGwC,WAAW,CAAC,YAAM;QAC/BvB,cAAc,CAAC,CAAC;MAClB,CAAC,EAAE,IAAI,CAAC,EAAC;IACX,CAAC;;IAED;IACA,IAAMI,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B,IAAIrB,YAAY,EAAE;QAChBuC,aAAa,CAACvC,YAAY,CAAC;QAC3BA,YAAY,GAAG,IAAI;MACrB;IACF,CAAC;;IAED;IACA,IAAMyC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBrC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEpB,OAAO,CAACzH,KAAK,CAACgJ,aAAa,CAAC;MAE/D,IAAI,CAACvB,OAAO,CAACzH,KAAK,CAACgJ,aAAa,EAAE;QAChCJ,OAAO,CAACsC,IAAI,CAAC,QAAQ,CAAC;QACtB;MACF;;MAEA;MACAtC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxCZ,YAAY,CAACjI,KAAK,GAAG,IAAI;;MAEzB;MACA0G,QAAQ,CAAC,YAAM;QACb,IAAIsB,cAAc,CAAChI,KAAK,EAAE;UACxB4I,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/Cb,cAAc,CAAChI,KAAK,CAAC+I,UAAU,CAAC,CAAC;QACnC,CAAC,MAAM;UACLH,OAAO,CAACsC,IAAI,CAAC,iCAAiC,CAAC;QACjD;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAMC,aAAa;MAAA,IAAAC,KAAA,GAAArF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2G,SAAA;QAAA,IAAArB,GAAA;QAAA,OAAA1K,mBAAA,GAAAuB,IAAA,UAAAyK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApG,IAAA,GAAAoG,SAAA,CAAA/H,IAAA;YAAA;cAAA,IACf2E,UAAU,CAACnI,KAAK,CAACwL,IAAI,CAAC,CAAC;gBAAAD,SAAA,CAAA/H,IAAA;gBAAA;cAAA;cAAA,OAAA+H,SAAA,CAAAnI,MAAA;YAAA;cAAAmI,SAAA,CAAA/H,IAAA;cAAA,OACV4C,GAAG,CAACqF,UAAU,CAAC;gBAC/BC,IAAI,EAAE;kBACJf,YAAY,EAAE,eAAe;kBAC7BC,UAAU,EAAEvD,KAAK,CAACkC,EAAE;kBACpBoC,aAAa,EAAE,CAAC;kBAChBC,cAAc,EAAEzD,UAAU,CAACnI,KAAK;kBAChC6L,QAAQ,EAAE,EAAE;kBACZC,YAAY,EAAE;gBAChB;cACF,CAAC,CAAC;YAAA;cATI9B,GAAG,GAAAuB,SAAA,CAAAtI,IAAA;cAWT,IAAI+G,GAAG,CAAC+B,IAAI,IAAI,GAAG,EAAE;gBACnB9E,WAAW,CAAC,QAAQ,CAAC;gBACrBwC,cAAc,CAAC,CAAC;cAClB,CAAC,MAAM;gBACLxC,WAAW,CAAC+C,GAAG,CAAC9C,OAAO,IAAI,QAAQ,EAAE,OAAO,CAAC;cAC/C;cACAiB,UAAU,CAACnI,KAAK,GAAG,EAAE;YAAA;YAAA;cAAA,OAAAuL,SAAA,CAAAjG,IAAA;UAAA;QAAA,GAAA+F,QAAA;MAAA,CACtB;MAAA,gBApBKF,aAAaA,CAAA;QAAA,OAAAC,KAAA,CAAAnF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAoBlB;;IAED;IACA,IAAMgG,UAAU;MAAA,IAAAC,KAAA,GAAAlG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwH,SAAOC,IAAI;QAAA,IAAAnC,GAAA,EAAAoC,IAAA;QAAA,OAAA9M,mBAAA,GAAAuB,IAAA,UAAAwL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnH,IAAA,GAAAmH,SAAA,CAAA9I,IAAA;YAAA;cAAA,KACxB2I,IAAI,CAACI,eAAe;gBAAAD,SAAA,CAAA9I,IAAA;gBAAA;cAAA;cAAA8I,SAAA,CAAA9I,IAAA;cAAA,OAEJ4C,GAAG,CAACoG,WAAW,CAAC;gBAChC7B,YAAY,EAAE,SAAS;gBACvBC,UAAU,EAAEuB,IAAI,CAAC5C;cACnB,CAAC,CAAC;YAAA;cAHIS,GAAG,GAAAsC,SAAA,CAAArJ,IAAA;cAKT,IAAI+G,GAAG,CAAC+B,IAAI,IAAI,GAAG,EAAE;gBACnBI,IAAI,CAACI,eAAe,GAAG,KAAK;gBAC5BJ,IAAI,CAACM,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACR,IAAI,CAACM,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7D5F,SAAS,CAAC;kBACRK,OAAO,EAAE,QAAQ;kBACjB/F,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL0F,SAAS,CAAC;kBACRK,OAAO,EAAE8C,GAAG,CAAC9C,OAAO,IAAI,QAAQ;kBAChC/F,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ;cAACmL,SAAA,CAAA9I,IAAA;cAAA;YAAA;cAAA8I,SAAA,CAAA9I,IAAA;cAAA,OAGiB4C,GAAG,CAACwG,UAAU,CAAC;gBAC/BlB,IAAI,EAAE;kBACJf,YAAY,EAAE,SAAS;kBACvBC,UAAU,EAAEuB,IAAI,CAAC5C;gBACnB;cACF,CAAC,CAAC;YAAA;cALIS,IAAG,GAAAsC,SAAA,CAAArJ,IAAA;cAOT,IAAI+G,IAAG,CAAC+B,IAAI,IAAI,GAAG,EAAE;gBACnBI,IAAI,CAACI,eAAe,GAAG,IAAI;gBAC3BJ,IAAI,CAACM,YAAY,GAAG,CAACN,IAAI,CAACM,YAAY,IAAI,CAAC,IAAI,CAAC;gBAChD5F,SAAS,CAAC;kBACRK,OAAO,EAAE,MAAM;kBACf/F,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL0F,SAAS,CAAC;kBACRK,OAAO,EAAE8C,IAAG,CAAC9C,OAAO,IAAI,MAAM;kBAC9B/F,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ;YAAC;YAAA;cAAA,OAAAmL,SAAA,CAAAhH,IAAA;UAAA;QAAA,GAAA4G,QAAA;MAAA,CAEJ;MAAA,gBA5CKF,UAAUA,CAAAa,EAAA;QAAA,OAAAZ,KAAA,CAAAhG,KAAA,OAAAD,SAAA;MAAA;IAAA,GA4Cf;;IAED;IACA,IAAM8G,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,SAAS,EAA2B;MAAA,IAAzBC,eAAe,GAAAhH,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAmB,SAAA,GAAAnB,SAAA,MAAG,EAAE;MACrDoC,UAAU,CAACpI,KAAK,GAAG+M,SAAS;MAC5BzE,WAAW,CAACtI,KAAK,GAAGgN,eAAe;MACnC3E,YAAY,CAACrI,KAAK,GAAG,EAAE;MAEvB0G,QAAQ,CAAC,YAAM;QACb,IAAI6B,aAAa,CAACvI,KAAK,EAAE;UACvBuI,aAAa,CAACvI,KAAK,CAACiN,KAAK,CAAC,CAAC;QAC7B;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB9E,UAAU,CAACpI,KAAK,GAAG,IAAI;MACvBsI,WAAW,CAACtI,KAAK,GAAG,EAAE;MACtBqI,YAAY,CAACrI,KAAK,GAAG,EAAE;IACzB,CAAC;;IAED;IACA,IAAMmN,WAAW;MAAA,IAAAC,KAAA,GAAArH,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2I,SAAON,SAAS;QAAA,IAAA/C,GAAA;QAAA,OAAA1K,mBAAA,GAAAuB,IAAA,UAAAyM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApI,IAAA,GAAAoI,SAAA,CAAA/J,IAAA;YAAA;cAAA,IAC7B6E,YAAY,CAACrI,KAAK,CAACwL,IAAI,CAAC,CAAC;gBAAA+B,SAAA,CAAA/J,IAAA;gBAAA;cAAA;cAAA,OAAA+J,SAAA,CAAAnK,MAAA;YAAA;cAAAmK,SAAA,CAAA/J,IAAA;cAAA,OACZ4C,GAAG,CAACqF,UAAU,CAAC;gBAC/BC,IAAI,EAAE;kBACJf,YAAY,EAAE,eAAe;kBAC7BC,UAAU,EAAEvD,KAAK,CAACkC,EAAE;kBACpBoC,aAAa,EAAE,CAAC;kBAChBC,cAAc,EAAEvD,YAAY,CAACrI,KAAK,CAACwL,IAAI,CAAC,CAAC;kBACzCK,QAAQ,EAAEkB,SAAS;kBACnBjB,YAAY,EAAE;gBAChB;cACF,CAAC,CAAC;YAAA;cATI9B,GAAG,GAAAuD,SAAA,CAAAtK,IAAA;cAUT,IAAI+G,GAAG,CAAC+B,IAAI,IAAI,GAAG,EAAE;gBACnBlF,SAAS,CAAC;kBACRK,OAAO,EAAE,QAAQ;kBACjB/F,IAAI,EAAE;gBACR,CAAC,CAAC;gBACFsI,cAAc,CAAC,CAAC;gBAChByD,WAAW,CAAC,CAAC;cACf,CAAC,MAAM;gBACLrG,SAAS,CAAC;kBACRK,OAAO,EAAE8C,GAAG,CAAC9C,OAAO,IAAI,QAAQ;kBAChC/F,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ;YAAC;YAAA;cAAA,OAAAoM,SAAA,CAAAjI,IAAA;UAAA;QAAA,GAAA+H,QAAA;MAAA,CACF;MAAA,gBAzBKF,WAAWA,CAAAK,GAAA;QAAA,OAAAJ,KAAA,CAAAnH,KAAA,OAAAD,SAAA;MAAA;IAAA,GAyBhB;IAEDM,eAAe,CAAC,YAAM;MACpB;MACAuD,kBAAkB,CAAC,CAAC;MACpB;MACA,IAAI7B,cAAc,CAAChI,KAAK,EAAE;QACxBgI,cAAc,CAAChI,KAAK,CAACiJ,kBAAkB,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC;IAEF,IAAMwE,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB7E,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;;MAElC;MACAgB,kBAAkB,CAAC,CAAC;;MAEpB;MACA,IAAI7B,cAAc,CAAChI,KAAK,EAAE;QACxB4I,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCb,cAAc,CAAChI,KAAK,CAACiJ,kBAAkB,CAAC,CAAC;MAC3C;;MAEA;MACAL,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClCZ,YAAY,CAACjI,KAAK,GAAG,KAAK;MAC1B2H,SAAS,CAAC3H,KAAK,GAAG,SAAS;;MAE3B;MACAyH,OAAO,CAACzH,KAAK,GAAG,CAAC,CAAC;MAElBuH,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;MAChCA,IAAI,CAAC,UAAU,CAAC;IAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}