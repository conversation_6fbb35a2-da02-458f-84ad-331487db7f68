"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[1465],{1465:function(t,e,r){r.r(e),r.d(e,{default:function(){return N}});var n=r(81474),o=(r(76945),r(64352),r(44863)),a=(r(4711),r(10650)),i=(r(99800),r(49744)),u=(r(98326),r(79471),r(44917)),c=(r(40065),r(84098)),l=(r(63584),r(74061)),f=r(4955),s=r(24652);function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),u=new j(n||[]);return o(i,"_invoke",{value:I(t,r,u)}),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",p="suspendedYield",v="executing",y="completed",m={};function g(){}function w(){}function b(){}var x={};l(x,i,(function(){return this}));var L=Object.getPrototypeOf,N=L&&L(L(T([])));N&&N!==r&&n.call(N,i)&&(x=N);var E=b.prototype=g.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function G(t,e){function r(o,a,i,u){var c=s(t[o],t,a);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(f).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,u)}))}u(c.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function I(e,r,n){var o=d;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var c=_(u,n);if(c){if(c===m)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var l=s(e,r,n);if("normal"===l.type){if(o=n.done?y:p,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function _(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,_(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=s(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function V(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(V,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(typeof e+" is not iterable")}return w.prototype=b,o(E,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=l(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,l(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},k(G.prototype),l(G.prototype,u,(function(){return this})),e.AsyncIterator=G,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new G(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(E),l(E,c,"Generator"),l(E,i,(function(){return this})),l(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;O(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function d(t,e,r,n,o,a,i){try{var u=t[a](i),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){d(a,n,o,i,u,"next",t)}function u(t){d(a,n,o,i,u,"throw",t)}i(void 0)}))}}var v={class:"GlobalGroupTransfer"},y={class:"GlobalGroupTransferInput"},m={class:"GlobalGroupTransferItem"},g={class:"GlobalGroupTransferName ellipsis"},w={class:"GlobalGroupTransferButton"},b={name:"GlobalGroupTransfer"},x=Object.assign(b,{props:{infoId:{type:String,default:""}},emits:["callback"],setup(t,e){var r=e.emit,d=t,b=r,x=(0,l.ref)({}),L=(0,l.ref)(),N=(0,l.ref)(""),E=(0,l.ref)([]),k=(0,l.ref)([]),G=(0,l.ref)(""),I=(0,l.ref)({}),_=function(t){return t?f.A.fileURL(t):f.A.defaultImgURL("default_user_head.jpg")},V=function(){var t;null===(t=L.value)||void 0===t||t.filter(N.value)},O=function(t,e){var r;return!t||(null===(r=e.userName)||void 0===r||null===(r=r.toLowerCase())||void 0===r?void 0:r.includes(null===t||void 0===t?void 0:t.toLowerCase()))},j=function(t){I.value=t},T=function(){var t=p(h().mark((function t(){var e,r,n;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,f.A.chatGroupEdit({form:{id:d.infoId,groupName:x.value.groupName},ownerUserId:G.value,memberUserIds:x.value.memberUserIds});case 2:e=t.sent,r=e.code,200===r&&(n={name:`${I.value.userName} 已成为新群主`,data:`||${I.value.userName}|OUI|${I.value.accountId}|| 已成为新群主`},b("callback",!0,n));case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),C=function(){b("callback",!1)},S=function(){var t=p(h().mark((function t(){var e,r;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,f.A.chatGroupInfo({detailId:d.infoId});case 2:e=t.sent,r=e.data,x.value=r,k.value=[r.ownerUserId];case 6:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),P=function(){var t=p(h().mark((function t(){var e,r;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,f.A.chatGroupMemberList({pageNo:1,pageSize:9999,keyword:N.value,query:{chatGroupId:d.infoId}});case 2:e=t.sent,r=e.data,E.value=r;case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();return(0,l.onMounted)((function(){S(),P()})),function(t,e){var r=c.WK,f=u.Zq,h=a.ll,d=i.q,p=a.MQ,b=o.kA,x=n.S2;return(0,l.openBlock)(),(0,l.createElementBlock)("div",v,[(0,l.createElementVNode)("div",y,[(0,l.createVNode)(r,{modelValue:N.value,"onUpdate:modelValue":e[0]||(e[0]=function(t){return N.value=t}),"prefix-icon":(0,l.unref)(s.Search),placeholder:"搜索",onInput:V,clearable:""},null,8,["modelValue","prefix-icon"])]),(0,l.createVNode)(b,{class:"GlobalGroupTransferScrollbar"},{default:(0,l.withCtx)((function(){return[(0,l.createVNode)(p,{modelValue:G.value,"onUpdate:modelValue":e[1]||(e[1]=function(t){return G.value=t})},{default:(0,l.withCtx)((function(){return[(0,l.createVNode)(d,{ref_key:"treeRef",ref:L,"node-key":"accountId",data:E.value,"filter-node-method":O},{default:(0,l.withCtx)((function(t){var e,r=t.data;return[(0,l.createVNode)(h,{value:r.accountId,disabled:null===(e=k.value)||void 0===e?void 0:e.includes(r.accountId),onChange:function(t){return j(r)}},{default:(0,l.withCtx)((function(){return[(0,l.createElementVNode)("div",m,[(0,l.createVNode)(f,{src:_(r.photo||r.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,l.createElementVNode)("div",g,(0,l.toDisplayString)(r.userName),1)])]})),_:2},1032,["value","disabled","onChange"])]})),_:1},8,["data"])]})),_:1},8,["modelValue"])]})),_:1}),(0,l.createElementVNode)("div",w,[(0,l.createVNode)(x,{onClick:C},{default:(0,l.withCtx)((function(){return e[2]||(e[2]=[(0,l.createTextVNode)("取消")])})),_:1}),(0,l.createVNode)(x,{type:"primary",onClick:T,disabled:!G.value},{default:(0,l.withCtx)((function(){return e[3]||(e[3]=[(0,l.createTextVNode)("完成")])})),_:1},8,["disabled"])])])}}});const L=x;var N=L}}]);