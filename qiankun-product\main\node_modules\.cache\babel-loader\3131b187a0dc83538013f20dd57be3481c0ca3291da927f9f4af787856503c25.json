{"ast": null, "code": "import { resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createBlock(_resolveDynamicComponent($setup.LayoutElement[$setup.elCode]));\n}", "map": {"version": 3, "names": ["_createBlock", "_resolveDynamicComponent", "$setup", "LayoutElement", "elCode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\LayoutContainer.vue"], "sourcesContent": ["<template>\r\n  <component :is=\"LayoutElement[elCode]\"></component>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutContainer' }\r\n</script>\r\n<script setup>\r\nimport { computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { LayoutElement } from './LayoutContainer'\r\nconst store = useStore()\r\nconst elCode = computed(() => {\r\n  const getElCode = store.getters.getLayoutElement || 'LayoutView'\r\n  return LayoutElement[getElCode] ? getElCode : 'LayoutView'\r\n})\r\n</script>\r\n"], "mappings": ";;uBACEA,YAAA,CAAmDC,wBADrD,CACkBC,MAAA,CAAAC,aAAa,CAACD,MAAA,CAAAE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}