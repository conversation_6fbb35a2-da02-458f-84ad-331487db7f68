"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[4966],{24966:function(e,t,r){r.r(t),r.d(t,{default:function(){return z}});var n=r(81474),a=(r(76945),r(64352),r(44863)),o=(r(4711),r(98267)),i=(r(15475),r(97445)),u=(r(10406),r(44917)),l=(r(40065),r(74061)),c=r(4955),s=r(59335),f=r(44500),p=r(88609),v=r(67761),h=r(24652);function d(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */d=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var o=t&&t.prototype instanceof g?t:g,i=Object.create(o.prototype),u=new T(n||[]);return a(i,"_invoke",{value:L(e,r,u)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var p="suspendedStart",v="suspendedYield",h="executing",m="completed",y={};function g(){}function w(){}function b(){}var G={};c(G,i,(function(){return this}));var k=Object.getPrototypeOf,x=k&&k(k(_([])));x&&x!==r&&n.call(x,i)&&(G=x);var N=b.prototype=g.prototype=Object.create(G);function C(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(a,o,i,u){var l=f(e[a],e,o);if("throw"!==l.type){var c=l.arg,s=c.value;return s&&"object"==typeof s&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,i,u)}),(function(e){r("throw",e,i,u)})):t.resolve(s).then((function(e){c.value=e,i(c)}),(function(e){return r("throw",e,i,u)}))}u(l.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function L(t,r,n){var a=p;return function(o,i){if(a===h)throw Error("Generator is already running");if(a===m){if("throw"===o)throw i;return{value:e,done:!0}}for(n.method=o,n.arg=i;;){var u=n.delegate;if(u){var l=I(u,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===p)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=h;var c=f(t,r,n);if("normal"===c.type){if(a=n.done?m:v,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=m,n.method="throw",n.arg=c.arg)}}}function I(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,I(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=f(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var i=o.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function V(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function _(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(typeof t+" is not iterable")}return w.prototype=b,a(N,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:w,configurable:!0}),w.displayName=c(b,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,l,"GeneratorFunction")),e.prototype=Object.create(N),e},t.awrap=function(e){return{__await:e}},C(E.prototype),c(E.prototype,u,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var i=new E(s(e,r,n,a),o);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},C(N),c(N,l,"Generator"),c(N,i,(function(){return this})),c(N,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=_,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(V),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return u.type="throw",u.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],u=i.completion;if("root"===i.tryLoc)return a("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return a(i.catchLoc,!0);if(this.prev<i.finallyLoc)return a(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return a(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return a(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),V(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;V(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:_(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function m(e){return b(e)||w(e)||g(e)||y()}function y(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return G(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?G(e,t):void 0}}function w(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function b(e){if(Array.isArray(e))return G(e)}function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function k(e,t,r,n,a,o,i){try{var u=e[o](i),l=u.value}catch(e){return void r(e)}u.done?t(l):Promise.resolve(l).then(n,a)}function x(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){k(o,n,a,i,u,"next",e)}function u(e){k(o,n,a,i,u,"throw",e)}i(void 0)}))}}var N={class:"GlobalChatGroupList forbidSelect"},C={class:"GlobalChatGroupInput"},E={class:"GlobalChatGroupItem forbidSelect"},L={class:"GlobalChatGroupName ellipsis"},I={class:"GlobalChatGroupScroll"},S=["onClick"],V={class:"GlobalChatGroupName ellipsis"},T={key:0,class:"GlobalChatGroupBody"},_={class:"GlobalChatGroupInfo"},B={class:"GlobalChatGroupName ellipsis"},j={class:"GlobalChatGroupUserBody"},A={key:0,class:"GlobalChatGroupUserLogo forbidSelect"},O={class:"GlobalChatGroupUserName ellipsis"},M={key:1,class:"GlobalChatGroupDrag"},U={name:"GlobalChatGroup"},P=Object.assign(U,{emits:["send"],setup(e,t){var r,y=t.expose,g=t.emit,w=(0,s.useStore)(),b=g,G=(0,l.computed)((function(){return w.getters.getRongCloudUrl})),k=(0,l.computed)((function(){return w.getters.getIsPrivatization})),U=null===(r=window.electron)||void 0===r?void 0:r.isMac,P=(0,l.ref)(),D=(0,l.ref)(!1),z=(0,l.ref)(""),F=(0,l.ref)(1),R=(0,l.ref)(20),$=(0,l.ref)(0),H=(0,l.ref)(!1),Y=(0,l.ref)(!0),q=(0,l.ref)([]),Z=(0,l.ref)(""),J=(0,l.ref)({}),K=(0,l.ref)([]),Q=function(e){return e?c.A.fileURL(e):c.A.defaultImgURL("default_user_head.jpg")},W=function(e){var t=e.scrollTop;if(P.value){var r=P.value.wrapRef,n=r.scrollHeight,a=r.clientHeight;n-t<=a+50&&!D.value&&X()}},X=function(){F.value*R.value>=$.value||(D.value=!0,F.value+=1,ee())},ee=function(){var e=x(d().mark((function e(){var t,r,n;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.A.chatGroupList({isMine:1,keyword:"",pageNo:F.value,pageSize:R.value});case 2:t=e.sent,r=t.data,n=t.total,q.value=[].concat(m(q.value),m(r||[])),$.value=n,Y.value=F.value*R.value<$.value,H.value=F.value*R.value>=$.value,D.value=!1;case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),te=function(){var e=x(d().mark((function e(){var t,r,n;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.A.chatGroupList({isMine:1,keyword:"",pageNo:1,pageSize:q.value.length});case 2:t=e.sent,r=t.data,n=t.total,q.value=r,$.value=n,Y.value=F.value*R.value<$.value,H.value=F.value*R.value>=$.value,D.value=!1;case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),re=function(){var e=x(d().mark((function e(t,r){var n;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t){e.next=6;break}return e.next=3,ne(t);case 3:e.t0=e.sent,e.next=7;break;case 6:e.t0=[];case 7:n=e.t0,r(n);case 9:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),ne=function(){var e=x(d().mark((function e(){var t,r;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.A.chatGroupList({isMine:1,keyword:z.value,pageNo:1,pageSize:999});case 2:return t=e.sent,r=t.data,e.abrupt("return",r);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ae=function(){var e=x(d().mark((function e(t){return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return Z.value=t.id,oe(),e.next=4,(0,v._S)(Z.value);case 4:K.value=e.sent;case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),oe=function(){var e=x(d().mark((function e(){var t,r;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.A.chatGroupInfo({detailId:Z.value});case 2:t=e.sent,r=t.data,J.value=r;case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ie=function(){var e=x(d().mark((function e(t,r){var n;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.getConversation({conversationType:t,targetId:r});case 2:return n=e.sent,e.abrupt("return",n);case 4:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),ue=function(){var e=x(d().mark((function e(){var t,r,n,a,o;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.A.rongCloud(G.value,{type:"createGroup",userIds:null===(t=J.value)||void 0===t||null===(t=t.memberUserIds)||void 0===t?void 0:t.map((function(e){return`${p.TC.value}${e}`})).join(","),groupId:`${p.TC.value}${null===(r=J.value)||void 0===r?void 0:r.id}`,groupName:null===(n=J.value)||void 0===n?void 0:n.groupName,environment:1},k.value);case 2:a=e.sent,o=a.code,200===o&&le();case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),le=function(){var e=x(d().mark((function e(){var t,r,n,a,o,i,u,l,c,s;return d().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=p.TC.value+J.value.id,e.next=3,ie(3,t);case 3:r=e.sent,n=r.code,a=r.data,n||(s={isTemporary:!0,isTop:a.isTop,isNotInform:a.notificationStatus,id:a.targetId,targetId:a.targetId,type:a.conversationType,chatObjectInfo:{uid:a.targetId,id:J.value.id,name:J.value.groupName,img:J.value.groupImg,userIdData:J.value.memberUserIds,chatGroupType:"0"!==(null===(o=J.value)||void 0===o||null===(o=o.chatGroupType)||void 0===o?void 0:o.value)?null===(i=J.value)||void 0===i||null===(i=i.chatGroupType)||void 0===i||null===(i=i.name)||void 0===i?void 0:i.slice(0,2):""},sentTime:(null===(u=a.latestMessage)||void 0===u?void 0:u.sentTime)||Date.parse(new Date),messageType:(null===(l=a.latestMessage)||void 0===l?void 0:l.messageType)||"RC:TxtMsg",content:(null===(c=a.latestMessage)||void 0===c?void 0:c.content)||{content:""},count:a.unreadMessageCount},b("send",s));case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,l.onMounted)((function(){F.value=1,q.value=[],$.value=0,H.value=!1,Y.value=!0,ee()})),y({refresh:te}),function(e,t){var r=u.Zq,c=i.s9,s=o.x0,f=a.kA,p=n.S2;return(0,l.openBlock)(),(0,l.createElementBlock)("div",{class:(0,l.normalizeClass)(["GlobalChatGroup",{GlobalChatMacGroup:(0,l.unref)(U)}])},[(0,l.createElementVNode)("div",N,[(0,l.createElementVNode)("div",C,[(0,l.createVNode)(c,{modelValue:z.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return z.value=e}),"prefix-icon":(0,l.unref)(h.Search),"fetch-suggestions":re,placeholder:"搜索","popper-class":"GlobalChatGroupAutocomplete",clearable:"",onSelect:ae},{default:(0,l.withCtx)((function(e){var t=e.item;return[(0,l.createElementVNode)("div",E,[(0,l.createVNode)(r,{src:Q(t.groupImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,l.createElementVNode)("div",L,(0,l.toDisplayString)(t.groupName),1)])]})),_:1},8,["modelValue","prefix-icon"])]),(0,l.createVNode)(f,{ref_key:"scrollRef",ref:P,class:"GlobalChatGroupScrollbar",onScroll:W},{default:(0,l.withCtx)((function(){return[(0,l.createElementVNode)("div",I,[q.value.length?(0,l.createCommentVNode)("",!0):((0,l.openBlock)(),(0,l.createBlock)(s,{key:0,"image-size":120,description:"暂无数据"})),((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(q.value,(function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("div",{class:(0,l.normalizeClass)(["GlobalChatGroupItem",{"is-active":e.id===Z.value}]),key:e.id,onClick:function(t){return ae(e)}},[(0,l.createVNode)(r,{src:Q(e.groupImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,l.createElementVNode)("div",V,(0,l.toDisplayString)(e.groupName),1)],10,S)})),128))])]})),_:1},512)]),Z.value?((0,l.openBlock)(),(0,l.createElementBlock)("div",T,[(0,l.createElementVNode)("div",_,[(0,l.createVNode)(r,{src:Q(J.value.groupImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,l.createElementVNode)("div",B,(0,l.toDisplayString)(J.value.groupName),1),(0,l.createVNode)(p,{type:"primary",onClick:ue},{default:(0,l.withCtx)((function(){return t[1]||(t[1]=[(0,l.createTextVNode)("发送消息")])})),_:1})]),(0,l.createVNode)(f,{class:"GlobalChatGroupUserScroll"},{default:(0,l.withCtx)((function(){return[(0,l.createElementVNode)("div",j,[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(K.value,(function(e){return(0,l.openBlock)(),(0,l.createElementBlock)("div",{class:"GlobalChatGroupUser",key:e.id},[e.isOwner?((0,l.openBlock)(),(0,l.createElementBlock)("div",A,"群主")):(0,l.createCommentVNode)("",!0),(0,l.createVNode)(r,{src:Q(e.photo||e.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,l.createElementVNode)("div",O,(0,l.toDisplayString)(e.userName),1)])})),128))])]})),_:1})])):(0,l.createCommentVNode)("",!0),Z.value?(0,l.createCommentVNode)("",!0):((0,l.openBlock)(),(0,l.createElementBlock)("div",M))],2)}}});const D=P;var z=D}}]);