"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[8322],{48322:function(t,e,r){r.r(e),r.d(e,{default:function(){return C}});var n=r(31167),o=(r(76945),r(52669),r(44917)),a=(r(40065),r(74061)),i=r(4955),c=r(43992),l=r(59335),u=r(88609),s=r(74269),f=r(43955),h=r(42714),v=r(98885);r(35894),r(50389);function p(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */p=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function s(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),c=new S(n||[]);return o(i,"_invoke",{value:_(t,r,c)}),i}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=s;var h="suspendedStart",v="suspendedYield",d="executing",m="completed",y={};function g(){}function w(){}function b(){}var L={};u(L,i,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(j([])));E&&E!==r&&n.call(E,i)&&(L=E);var N=b.prototype=g.prototype=Object.create(L);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(o,a,i,c){var l=f(t[o],t,a);if("throw"!==l.type){var u=l.arg,s=u.value;return s&&"object"==typeof s&&n.call(s,"__await")?e.resolve(s.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(s).then((function(t){u.value=t,i(u)}),(function(t){return r("throw",t,i,c)}))}c(l.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function _(e,r,n){var o=h;return function(a,i){if(o===d)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var l=G(c,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var u=f(e,r,n);if("normal"===u.type){if(o=n.done?m:v,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=m,n.method="throw",n.arg=u.arg)}}}function G(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,G(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=f(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function j(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(typeof e+" is not iterable")}return w.prototype=b,o(N,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=u(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,u(t,l,"GeneratorFunction")),t.prototype=Object.create(N),t},e.awrap=function(t){return{__await:t}},k(C.prototype),u(C.prototype,c,(function(){return this})),e.AsyncIterator=C,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new C(s(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(N),u(N,l,"Generator"),u(N,i,(function(){return this})),u(N,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=j,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:j(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function d(t,e,r,n,o,a,i){try{var c=t[a](i),l=c.value}catch(t){return void r(t)}c.done?e(l):Promise.resolve(l).then(n,o)}function m(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){d(a,n,o,i,c,"next",t)}function c(t){d(a,n,o,i,c,"throw",t)}i(void 0)}))}}var y={class:"GlobalChatUser"},g=["innerHTML"],w=["onClick"],b=["innerHTML"],L={class:"GlobalChatNavText forbidSelect"},x=["innerHTML"],E={name:"GlobalChatNav"},N=Object.assign(E,{props:{modelValue:[String,Number],chatTotal:{type:Number,default:0},exit:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(t,e){var r,d=e.emit,E=(0,c.useRouter)(),N=(0,l.useStore)(),k=t,C=d,_=null===(r=window.electron)||void 0===r?void 0:r.isMac,G=(0,a.computed)({get(){return k.modelValue},set(t){C("update:modelValue",t)}}),O=(0,a.computed)((function(){return k.chatTotal})),T=(0,a.ref)([{id:"2",name:"通讯录",icon:f.j0},{id:"3",name:"群组",icon:f.$t}]),S=function(t){G.value!==t.id&&(G.value=t.id,C("change",t.id))},j=function(){h.s.confirm("此操作将退出当前系统, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){V("已安全退出！")})).catch((function(){(0,v.nk)({type:"info",message:"已取消退出"})}))},V=function(){var t=m(p().mark((function t(e){var r,n,o,a;return p().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,i.A.loginOut();case 2:r=t.sent,n=r.code,200===n&&(sessionStorage.clear(),o=localStorage.getItem("goal_login_router_path"),o?(a=localStorage.getItem("goal_login_router_query")||"",E.push({path:o,query:a?JSON.parse(a):{}})):E.push({path:"/LoginView"}),N.commit("setUser",{}),N.commit("setMenu",[]),N.commit("setArea",[]),N.commit("setRole",[]),N.commit("setReadConfig",{}),N.commit("setReadOpenConfig",{}),N.commit("setRongCloudToken",""),(0,s.OS)(),(0,v.nk)({message:e,showClose:!0,type:"success"}));case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();return function(t,e){var r=o.Zq,i=n.z_;return(0,a.openBlock)(),(0,a.createElementBlock)("div",{class:(0,a.normalizeClass)(["GlobalChatNav",{GlobalChatMacNav:(0,a.unref)(_)}])},[(0,a.createElementVNode)("div",y,[(0,a.createVNode)(r,{src:(0,a.unref)(u.kQ).image,fit:"cover",draggable:"false"},null,8,["src"])]),(0,a.createVNode)(i,{value:O.value,hidden:!O.value,offset:[-16,0]},{default:(0,a.withCtx)((function(){return[(0,a.createElementVNode)("div",{class:(0,a.normalizeClass)(["GlobalChatNavItem",{"is-active":"1"===G.value}]),onClick:e[0]||(e[0]=function(t){return S({id:"1"})})},[(0,a.createElementVNode)("div",{class:"GlobalChatNavIcon",innerHTML:(0,a.unref)(f.vN)},null,8,g),e[1]||(e[1]=(0,a.createElementVNode)("div",{class:"GlobalChatNavText forbidSelect"},"消息",-1))],2)]})),_:1},8,["value","hidden"]),((0,a.openBlock)(!0),(0,a.createElementBlock)(a.Fragment,null,(0,a.renderList)(T.value,(function(t){return(0,a.openBlock)(),(0,a.createElementBlock)("div",{key:t.id,class:(0,a.normalizeClass)(["GlobalChatNavItem",{"is-active":G.value===t.id}]),onClick:function(e){return S(t)}},[(0,a.createElementVNode)("div",{class:"GlobalChatNavIcon",innerHTML:t.icon},null,8,b),(0,a.createElementVNode)("div",L,(0,a.toDisplayString)(t.name),1)],10,w)})),128)),k.exit?((0,a.openBlock)(),(0,a.createElementBlock)("div",{key:0,class:"GlobalChatNavItem GlobalChatNavExit",onClick:j},[(0,a.createElementVNode)("div",{class:"GlobalChatNavIcon",innerHTML:(0,a.unref)(f.O0)},null,8,x)])):(0,a.createCommentVNode)("",!0)],2)}}});const k=N;var C=k}}]);