"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[2953],{41193:function(e,t,n){n.d(t,{W:function(){return r}});var r=[{name:"Expression_1",text:"[微笑]"},{name:"Expression_2",text:"[撇嘴]"},{name:"Expression_3",text:"[色]"},{name:"Expression_4",text:"[发呆]"},{name:"Expression_5",text:"[得意]"},{name:"Expression_6",text:"[流泪]"},{name:"Expression_7",text:"[害羞]"},{name:"Expression_8",text:"[闭嘴]"},{name:"Expression_9",text:"[睡]"},{name:"Expression_10",text:"[大哭]"},{name:"Expression_11",text:"[尴尬]"},{name:"Expression_12",text:"[发怒]"},{name:"Expression_13",text:"[调皮]"},{name:"Expression_14",text:"[呲牙]"},{name:"Expression_15",text:"[惊讶]"},{name:"Expression_16",text:"[难过]"},{name:"Expression_17",text:"[酷]"},{name:"Expression_18",text:"[冷汗]"},{name:"Expression_19",text:"[抓狂]"},{name:"Expression_20",text:"[吐]"},{name:"Expression_21",text:"[偷笑]"},{name:"Expression_22",text:"[愉快]"},{name:"Expression_23",text:"[白眼]"},{name:"Expression_24",text:"[傲慢]"},{name:"Expression_25",text:"[饥饿]"},{name:"Expression_26",text:"[困]"},{name:"Expression_27",text:"[恐惧]"},{name:"Expression_28",text:"[流汗]"},{name:"Expression_29",text:"[憨笑]"},{name:"Expression_30",text:"[悠闲]"},{name:"Expression_31",text:"[奋斗]"},{name:"Expression_32",text:"[咒骂]"},{name:"Expression_33",text:"[疑问]"},{name:"Expression_34",text:"[嘘]"},{name:"Expression_35",text:"[晕]"},{name:"Expression_36",text:"[疯了]"},{name:"Expression_37",text:"[衰]"},{name:"Expression_38",text:"[骷髅]"},{name:"Expression_39",text:"[敲打]"},{name:"Expression_40",text:"[再见]"},{name:"Expression_41",text:"[擦汗]"},{name:"Expression_42",text:"[抠鼻]"},{name:"Expression_43",text:"[鼓掌]"},{name:"Expression_44",text:"[糗大了]"},{name:"Expression_45",text:"[坏笑]"},{name:"Expression_46",text:"[左哼哼]"},{name:"Expression_47",text:"[右哼哼]"},{name:"Expression_48",text:"[哈欠]"},{name:"Expression_49",text:"[鄙视]"},{name:"Expression_50",text:"[委屈]"},{name:"Expression_51",text:"[快哭了]"},{name:"Expression_52",text:"[阴险]"},{name:"Expression_53",text:"[亲亲]"},{name:"Expression_54",text:"[吓]"},{name:"Expression_55",text:"[可怜]"},{name:"Expression_56",text:"[菜刀]"},{name:"Expression_57",text:"[西瓜]"},{name:"Expression_58",text:"[啤酒]"},{name:"Expression_59",text:"[篮球]"},{name:"Expression_60",text:"[乒乓]"},{name:"Expression_61",text:"[咖啡]"},{name:"Expression_62",text:"[饭]"},{name:"Expression_63",text:"[猪头]"},{name:"Expression_64",text:"[玫瑰]"},{name:"Expression_65",text:"[凋谢]"},{name:"Expression_66",text:"[嘴唇]"},{name:"Expression_67",text:"[爱心]"},{name:"Expression_68",text:"[心碎]"},{name:"Expression_69",text:"[蛋糕]"},{name:"Expression_70",text:"[闪电]"},{name:"Expression_71",text:"[炸弹]"},{name:"Expression_72",text:"[刀]"},{name:"Expression_73",text:"[足球]"},{name:"Expression_74",text:"[瓢虫]"},{name:"Expression_75",text:"[便便]"},{name:"Expression_76",text:"[月亮]"},{name:"Expression_77",text:"[太阳]"},{name:"Expression_78",text:"[礼物]"},{name:"Expression_79",text:"[拥抱]"},{name:"Expression_80",text:"[强]"},{name:"Expression_81",text:"[弱]"},{name:"Expression_82",text:"[握手]"},{name:"Expression_83",text:"[胜利]"},{name:"Expression_84",text:"[抱拳]"},{name:"Expression_85",text:"[勾引]"},{name:"Expression_86",text:"[拳头]"},{name:"Expression_87",text:"[差劲]"},{name:"Expression_88",text:"[爱你]"},{name:"Expression_89",text:"[NO]"},{name:"Expression_90",text:"[OK]"},{name:"Expression_91",text:"[爱情]"},{name:"Expression_92",text:"[飞吻]"},{name:"Expression_93",text:"[跳跳]"},{name:"Expression_94",text:"[发抖]"},{name:"Expression_95",text:"[怄火]"},{name:"Expression_96",text:"[转圈]"},{name:"Expression_97",text:"[磕头]"},{name:"Expression_98",text:"[回头]"},{name:"Expression_99",text:"[跳绳]"},{name:"Expression_100",text:"[投降]"},{name:"Expression_101",text:"[激动]"},{name:"Expression_102",text:"[街舞]"},{name:"Expression_103",text:"[献吻]"},{name:"Expression_104",text:"[左太极]"},{name:"Expression_105",text:"[右太极]"}]},42953:function(e,t,n){n.r(t),n.d(t,{default:function(){return gt}});var r=n(62427),o=(n(76945),n(98773),n(44863)),a=(n(4711),n(97445)),i=(n(10406),n(31167)),l=(n(52669),n(44917)),u=(n(40065),n(74061)),s=n(4955),c=n(44500),d=n(3671),f=n(59429),v=n(29217),p=n(88609),m=n(67761),h=n(41193);function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){x(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function x(e,t,n){return(t=w(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e){var t=b(e,"string");return"symbol"==typeof t?t:t+""}function b(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function C(e){return I(e)||V(e)||E(e)||k()}function k(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function E(e,t){if(e){if("string"==typeof e)return T(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?T(e,t):void 0}}function V(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function I(e){if(Array.isArray(e))return T(e)}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function N(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */N=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var a=t&&t.prototype instanceof g?t:g,i=Object.create(a.prototype),l=new G(r||[]);return o(i,"_invoke",{value:I(e,n,l)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=c;var f="suspendedStart",v="suspendedYield",p="executing",m="completed",h={};function g(){}function y(){}function x(){}var w={};s(w,i,(function(){return this}));var b=Object.getPrototypeOf,C=b&&b(b(O([])));C&&C!==n&&r.call(C,i)&&(w=C);var k=x.prototype=g.prototype=Object.create(w);function E(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function V(e,t){function n(o,a,i,l){var u=d(e[o],e,a);if("throw"!==u.type){var s=u.arg,c=s.value;return c&&"object"==typeof c&&r.call(c,"__await")?t.resolve(c.__await).then((function(e){n("next",e,i,l)}),(function(e){n("throw",e,i,l)})):t.resolve(c).then((function(e){s.value=e,i(s)}),(function(e){return n("throw",e,i,l)}))}l(u.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function I(t,n,r){var o=f;return function(a,i){if(o===p)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var l=r.delegate;if(l){var u=T(l,r);if(u){if(u===h)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=p;var s=d(t,n,r);if("normal"===s.type){if(o=r.done?m:v,s.arg===h)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=m,r.method="throw",r.arg=s.arg)}}}function T(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,T(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var a=d(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,h;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,h):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function M(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function G(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function O(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return y.prototype=x,o(k,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:y,configurable:!0}),y.displayName=s(x,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,s(e,u,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},E(V.prototype),s(V.prototype,l,(function(){return this})),t.AsyncIterator=V,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new V(c(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},E(k),s(k,u,"Generator"),s(k,i,(function(){return this})),s(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=O,G.prototype={constructor:G,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(M),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(u&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),M(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;M(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:O(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),h}},t}function _(e,t,n,r,o,a,i){try{var l=e[a](i),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(r,o)}function M(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){_(a,r,o,i,l,"next",e)}function l(e){_(a,r,o,i,l,"throw",e)}i(void 0)}))}}var G=function(e){var t={docx:"globalFileWord",doc:"globalFileWord",wps:"globalFileWPS",xlsx:"globalFileExcel",xls:"globalFileExcel",pdf:"globalFilePDF",pptx:"globalFilePPT",ppt:"globalFilePPT",txt:"globalFileTXT",jpg:"globalFilePicture",png:"globalFilePicture",gif:"globalFilePicture",avi:"globalFileVideo",mp4:"globalFileVideo",zip:"globalFileCompress",rar:"globalFileCompress"};return t[e]||"globalFileUnknown"},O=function(e){if((0,d.G)(e,"YYYY")!==(0,d.G)(new Date,"YYYY"))return(0,d.G)(e,"YYYY-MM-DD");if((0,d.G)(e,"YYYY-MM-DD")===(0,d.G)(new Date,"YYYY-MM-DD"))return(0,d.G)(e,"HH:mm");var t=new Date(e),n=new Date,r=n-t,o=r/864e5;return o<=7&&o>=-7?(0,d.G)(e,"EEE"):(0,d.G)(e,"MM-DD")},S=function(){var e=M(N().mark((function e(t){var n,r,o;return N().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=JSON.parse(localStorage.getItem(t))||"",!n){e.next=3;break}return e.abrupt("return",n);case 3:return e.next=5,s.A.globalFileInfo(t);case 5:return r=e.sent,o=r.data,localStorage.setItem(t,JSON.stringify(o)),e.abrupt("return",o);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),B=function(){var e=M(N().mark((function e(t){var n,r,o;return N().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=JSON.parse(localStorage.getItem(t))||"",!n){e.next=3;break}return e.abrupt("return",n);case 3:return e.next=5,s.A.VoteInfo({detailId:t});case 5:return r=e.sent,o=r.data,localStorage.setItem(t,JSON.stringify(o)),e.abrupt("return",o);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),L=function(e){var t=/\[([^\]]+)\]/g,n=[],r="";while(r=t.exec(e))n.push(r[1]);return n},j=function(e,t,n){var r=t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return e.replace(new RegExp(r,"g"),n)},D=function(e){for(var t="",n=0;n<h.W.length;n++){var r=h.W[n];r.text===`[${e}]`&&(t=`<div class="GlobalChatEmotionItem"><div class="${r.name}"></div></div>`)}return t||e},A=function(){var e=M(N().mark((function e(t,n){var r,o,a,i,l,u,s,c,d,f,v,m,h,g,x,w,b,k,E;return N().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r=[],o=[],a=0;case 3:if(!(a<t.length)){e.next=51;break}if(i=t[a],l=n[i.senderUserId],u=i.senderUserId===p.TC.value+p.kQ.value.accountId,s=u?"GlobalChatSelfMessages":"GlobalChatMessages","RC:RcCmd"!==i.messageType){e.next=13;break}r.push(null===(c=i.content)||void 0===c?void 0:c.messageUId),o.push({id:i.messageId,uid:i.messageUId,type:i.messageType,direction:i.messageDirection,chatObjectInfo:l,chatObjectInfoType:u,className:"GlobalChatMessagesInform",content:i.content,sentTime:i.sentTime,userName:null===(d=n[i.senderUserId])||void 0===d?void 0:d.name}),e.next=48;break;case 13:if("RC:CmdNtf"!==i.messageType){e.next=17;break}o.push({id:i.messageId,uid:i.messageUId,type:i.messageType,direction:i.messageDirection,chatObjectInfo:l,chatObjectInfoType:u,className:"GlobalChatMessagesInform",content:i.content,sentTime:i.sentTime}),e.next=48;break;case 17:if("RC:TxtMsg"!==i.messageType){e.next=24;break}for(m=C(new Set(L(null===(f=i.content)||void 0===f?void 0:f.content))),h=null===(v=i.content)||void 0===v?void 0:v.content.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"),g=0;g<m.length;g++)x=m[g],w=D(x),h=j(h,`[${x}]`,w);o.push({id:i.messageId,uid:i.messageUId,type:i.messageType,direction:i.messageDirection,chatObjectInfo:l,chatObjectInfoType:u,className:s,content:y(y({},i.content),{},{htmlContent:h}),sentTime:i.sentTime}),e.next=48;break;case 24:if("RC:LBSMsg"!==i.messageType){e.next=28;break}o.push({id:i.messageId,uid:i.messageUId,type:i.messageType,direction:i.messageDirection,chatObjectInfo:l,chatObjectInfoType:u,className:s,customType:"unknown",sentTime:i.sentTime}),e.next=48;break;case 28:if("RC:ImgTextMsg"!==i.messageType){e.next=47;break}if(b=i.content.content.split(","),"[文件]"!==b[0]){e.next=37;break}return e.next=33,S(b[1]);case 33:k=e.sent,o.push({id:i.messageId,uid:i.messageUId,type:i.messageType,direction:i.messageDirection,chatObjectInfo:l,chatObjectInfoType:u,className:s,customType:"file",file:k,sentTime:i.sentTime}),e.next=45;break;case 37:if("[投票]"!==b[0]){e.next=44;break}return e.next=40,B(b[1]);case 40:E=e.sent,o.push({id:i.messageId,uid:i.messageUId,type:i.messageType,direction:i.messageDirection,chatObjectInfo:l,chatObjectInfoType:u,className:s,customType:"vote",vote:E,sentTime:i.sentTime}),e.next=45;break;case 44:o.push({id:i.messageId,uid:i.messageUId,type:i.messageType,direction:i.messageDirection,chatObjectInfo:l,chatObjectInfoType:u,className:s,customType:"unknown",sentTime:i.sentTime});case 45:e.next=48;break;case 47:o.push({id:i.messageId,uid:i.messageUId,type:i.messageType,direction:i.messageDirection,chatObjectInfo:l,chatObjectInfoType:u,className:s,content:i.content,sentTime:i.sentTime});case 48:a++,e.next=3;break;case 51:return e.abrupt("return",{newMessages:o,withdrawId:r});case 52:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),P=function(e){var t=e.lastIndexOf(".");return-1===t?"":e.slice(t)},F=function(e){var t=e.lastIndexOf(".");return-1===t?e:e.slice(0,t)},U=function(e,t){if(null===t||void 0===t||!t.includes(e))return e;var n=P(e),r=F(e),o=e,a=1;while(null!==t&&void 0!==t&&t.includes(o))o=`${r}(${a})${n}`,a++;return o},R=n(43955),H=n(24652),z=n(98885),Y=n(42714);n(35894),n(50389);function Q(e){return K(e)||J(e)||$(e)||W()}function W(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function $(e,t){if(e){if("string"==typeof e)return q(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?q(e,t):void 0}}function J(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function K(e){if(Array.isArray(e))return q(e)}function q(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function X(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?X(Object(n),!0).forEach((function(t){ee(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ee(e,t,n){return(t=te(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function te(e){var t=ne(e,"string");return"symbol"==typeof t?t:t+""}function ne(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function re(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */re=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var a=t&&t.prototype instanceof g?t:g,i=Object.create(a.prototype),l=new M(r||[]);return o(i,"_invoke",{value:I(e,n,l)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=c;var f="suspendedStart",v="suspendedYield",p="executing",m="completed",h={};function g(){}function y(){}function x(){}var w={};s(w,i,(function(){return this}));var b=Object.getPrototypeOf,C=b&&b(b(G([])));C&&C!==n&&r.call(C,i)&&(w=C);var k=x.prototype=g.prototype=Object.create(w);function E(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function V(e,t){function n(o,a,i,l){var u=d(e[o],e,a);if("throw"!==u.type){var s=u.arg,c=s.value;return c&&"object"==typeof c&&r.call(c,"__await")?t.resolve(c.__await).then((function(e){n("next",e,i,l)}),(function(e){n("throw",e,i,l)})):t.resolve(c).then((function(e){s.value=e,i(s)}),(function(e){return n("throw",e,i,l)}))}l(u.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function I(t,n,r){var o=f;return function(a,i){if(o===p)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var l=r.delegate;if(l){var u=T(l,r);if(u){if(u===h)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=p;var s=d(t,n,r);if("normal"===s.type){if(o=r.done?m:v,s.arg===h)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=m,r.method="throw",r.arg=s.arg)}}}function T(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,T(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var a=d(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,h;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,h):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function M(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function G(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return y.prototype=x,o(k,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:y,configurable:!0}),y.displayName=s(x,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,s(e,u,"GeneratorFunction")),e.prototype=Object.create(k),e},t.awrap=function(e){return{__await:e}},E(V.prototype),s(V.prototype,l,(function(){return this})),t.AsyncIterator=V,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new V(c(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},E(k),s(k,u,"Generator"),s(k,i,(function(){return this})),s(k,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=G,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(_),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(u&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),_(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;_(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:G(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),h}},t}function oe(e,t,n,r,o,a,i){try{var l=e[a](i),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(r,o)}function ae(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){oe(a,r,o,i,l,"next",e)}function l(e){oe(a,r,o,i,l,"throw",e)}i(void 0)}))}}var ie={class:"GlobalChatViewList forbidSelect"},le={class:"GlobalChatViewListHead"},ue={class:"GlobalChatViewMessagesItem forbidSelect"},se={class:"GlobalChatViewMessagesInfo"},ce={class:"GlobalChatViewMessagesName"},de={class:"ellipsis"},fe=["innerHTML"],ve={key:1,class:"GlobalChatViewMessagesText ellipsis"},pe={key:2,class:"GlobalChatViewMessagesText ellipsis"},me={key:3,class:"GlobalChatViewMessagesText ellipsis"},he={key:4,class:"GlobalChatViewMessagesText ellipsis"},ge={key:5,class:"GlobalChatViewMessagesText ellipsis"},ye={key:6,class:"GlobalChatViewMessagesText ellipsis"},xe={key:7,class:"GlobalChatViewMessagesText ellipsis"},we=["innerHTML"],be=["onClick","onContextmenu"],Ce={class:"GlobalChatViewMessagesInfo"},ke={class:"GlobalChatViewMessagesName"},Ee={key:0,class:"ellipsis"},Ve={key:1,class:"GlobalChatViewMessagesNameGroup ellipsis"},Ie=["innerHTML"],Te={key:1,class:"GlobalChatViewMessagesText ellipsis"},Ne={key:2,class:"GlobalChatViewMessagesText ellipsis"},_e={key:3,class:"GlobalChatViewMessagesText ellipsis"},Me={key:4,class:"GlobalChatViewMessagesText ellipsis"},Ge={key:5,class:"GlobalChatViewMessagesText ellipsis"},Oe={key:6,class:"GlobalChatViewMessagesText ellipsis"},Se={key:7,class:"GlobalChatViewMessagesText ellipsis"},Be=["innerHTML"],Le={class:"GlobalChatWindowTitle forbidSelect"},je={key:0},De={key:0,class:"GlobalChatGroupAnnouncement"},Ae={class:"GlobalChatGroupAnnouncementTitle"},Pe=["innerHTML"],Fe={class:"GlobalChatGroupAnnouncementContent"},Ue={class:"GlobalChatWindowBody"},Re={class:"GlobalChatWindowUserImg"},He={class:"GlobalChatMessagesInfo"},ze=["onContextmenu"],Ye=["onContextmenu"],Qe=["innerHTML"],We=["onContextmenu","onClick"],$e=["onClick"],Je=["onContextmenu"],Ke=["onContextmenu","onClick"],qe={class:"GlobalChatMessagesCustomName"},Xe={class:"GlobalChatMessagesCustomText"},Ze={class:"GlobalChatMessagesVoteTitleBody"},et={class:"GlobalChatMessagesVoteTitle"},tt=["innerHTML"],nt={class:"GlobalChatMessagesVoteTime"},rt={class:"GlobalChatMessagesVoteInfo"},ot=["innerHTML"],at={class:"GlobalChatMessagesVoteName"},it={key:2,class:"GlobalChatMessagesCustomUnknown"},lt=["onContextmenu"],ut={class:"GlobalChatMessagesFileName"},st={class:"GlobalChatMessagesFileSize"},ct={key:0,class:"GlobalChatViewMenuItem"},dt={key:2,class:"GlobalChatViewMenuLine"},ft={class:"GlobalChatViewNoMessage"},vt={key:1,class:"GlobalChatViewDrag"},pt={name:"GlobalChatView"},mt=Object.assign(pt,{props:{modelValue:[String,Number],chatList:{type:Array,default:function(){return[]}}},emits:["update:modelValue","time","refresh","send"],setup(e,t){var h,g=t.expose,y=t.emit,x=(0,u.defineAsyncComponent)((function(){return n.e(6677).then(n.bind(n,26677))})),w=(0,u.defineAsyncComponent)((function(){return n.e(9815).then(n.bind(n,59815))})),b=(0,u.defineAsyncComponent)((function(){return n.e(6358).then(n.bind(n,36358))})),C=(0,u.defineAsyncComponent)((function(){return n.e(6160).then(n.bind(n,56160))})),k=(0,u.defineAsyncComponent)((function(){return n.e(9527).then(n.bind(n,79527))})),E=(0,u.defineAsyncComponent)((function(){return n.e(7637).then(n.bind(n,7637))})),V=(0,u.defineAsyncComponent)((function(){return n.e(7317).then(n.bind(n,77317))})),I=(0,u.defineAsyncComponent)((function(){return n.e(228).then(n.bind(n,40228))})),T=(0,u.defineAsyncComponent)((function(){return n.e(9056).then(n.bind(n,99056))})),N=(0,u.defineAsyncComponent)((function(){return Promise.all([n.e(7954),n.e(4552)]).then(n.bind(n,34552))})),_=(0,u.defineAsyncComponent)((function(){return n.e(5464).then(n.bind(n,45464))})),M=(0,u.defineAsyncComponent)((function(){return n.e(1465).then(n.bind(n,1465))})),S=(0,u.defineAsyncComponent)((function(){return n.e(6610).then(n.bind(n,86610))})),B=(0,u.defineAsyncComponent)((function(){return Promise.all([n.e(3201),n.e(1388)]).then(n.bind(n,28690))})),L=(0,u.defineAsyncComponent)((function(){return n.e(3273).then(n.bind(n,43273))})),j=(0,u.defineAsyncComponent)((function(){return Promise.all([n.e(3201),n.e(3379)]).then(n.bind(n,63201))})),D=e,P=y,F=null===(h=window.electron)||void 0===h?void 0:h.isMac,W=!!window.electron,$=(0,u.ref)(""),J=(0,u.computed)({get(){return D.modelValue},set(e){P("update:modelValue",e)}}),K=["RC:TxtMsg","RC:ImgTextMsg"],q=(0,u.computed)((function(){return D.chatList})),X=(0,u.ref)(!0),ee=(0,u.ref)({}),te=(0,u.ref)([]),ne=(0,u.ref)([]),oe=(0,u.ref)({}),pt=(0,u.ref)({}),mt=(0,u.ref)([]),ht=(0,u.ref)([]),gt=(0,u.ref)({}),yt=(0,u.ref)({}),xt=(0,u.ref)([]),wt=(0,u.ref)({}),bt=(0,u.ref)(0),Ct=(0,u.ref)(0),kt=(0,u.ref)({}),Et=(0,u.ref)(!1),Vt=(0,u.ref)(!1),It=(0,u.ref)(0),Tt=(0,u.ref)(0),Nt=(0,u.ref)({}),_t=(0,u.ref)(!1),Mt=(0,u.ref)(!1),Gt=(0,u.ref)([]),Ot=(0,u.ref)(!1),St=(0,u.ref)({}),Bt=(0,u.ref)(!1),Lt=(0,u.ref)([]),jt=(0,u.ref)(!1),Dt=(0,u.ref)(""),At=(0,u.ref)(!1),Pt=(0,u.ref)(!1),Ft=(0,u.ref)(!1),Ut=(0,u.ref)(!1),Rt=(0,u.ref)(!1),Ht=(0,u.ref)(!1),zt=(0,u.ref)(!1),Yt=(0,u.ref)(!1),Qt=(0,u.ref)(""),Wt=(0,u.ref)(!1),$t=(0,u.ref)(""),Jt=(0,u.ref)(""),Kt=(0,u.ref)(!1),qt=(0,u.ref)(!1),Xt=(0,u.ref)(!1),Zt=function(e){return e?s.A.fileURL(e):s.A.defaultImgURL("default_user_head.jpg")},en=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,n="x"==e?t:3&t|8;return n.toString(16)}))},tn=(0,u.ref)(),nn=(0,u.ref)(),rn=(0,u.ref)(0),on=(0,u.ref)(0),an=(0,u.ref)(!0),ln=(0,u.ref)([]),un=(0,u.ref)([]);(0,u.onMounted)((function(){}));var sn=function(e){Et.value=!1,_t.value=!1,e.preventDefault()},cn=function(e){Vt.value?Vt.value=!1:Et.value=!1,Mt.value?Mt.value=!1:_t.value=!1,e.preventDefault()},dn=function(e){"file"===e.customType&&(W?fn(e.uid,e.file,!1):wn(e.file)),"vote"===e.customType&&wr(e.vote)},fn=function(){var e=ae(re().mark((function e(t,n){var r,o,a,i,l,u=arguments;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(o=u.length>2&&void 0!==u[2]&&u[2],null===(r=ht.value)||void 0===r||!r.includes(t)){e.next=3;break}return e.abrupt("return");case 3:return a=n.originalFileName,yt.value[t]?a=yt.value[t]:(a=U(a,xt.value),yt.value[t]=a,xt.value.push(a)),i=J.value+"_"+p.kQ.value.accountId+"_file",e.next=8,window.electron.fileExists(i,a);case 8:l=e.sent,l&&(o?hn(a):mn(a)),l||pn(t,n,a,o);case 11:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),vn=function(e,t){var n;if(null!==e&&void 0!==e&&null!==(n=e.event)&&void 0!==n&&n.lengthComputable){var r=(e.loaded/e.total*100).toFixed(0);gt.value[t]=100-parseInt(r)+"%"}},pn=function(){var e=ae(re().mark((function e(t,n,r){var o,a,i,l,u=arguments;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return o=u.length>3&&void 0!==u[3]&&u[3],ht.value.push(t),gt.value[t]="100%",e.next=5,s.A.globalElectronFileDownload(t,n.id,{},vn);case 5:return a=e.sent,ht.value=ht.value.filter((function(e){return e!==t})),delete gt.value[t],i=J.value+"_"+p.kQ.value.accountId+"_file",e.next=11,window.electron.saveFile(i,r,a);case 11:l=e.sent,"success"===l.type&&(o?hn(r):mn(r)),"error"===l.type&&(0,z.nk)({type:"error",message:l.message});case 14:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}(),mn=function(){var e=ae(re().mark((function e(t){var n,r;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=J.value+"_"+p.kQ.value.accountId+"_file",e.next=3,window.electron.openFile(n,t);case 3:r=e.sent,"error"===r.type&&(0,z.nk)({type:"error",message:r.message});case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),hn=function(){var e=ae(re().mark((function e(t){var n,r;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=J.value+"_"+p.kQ.value.accountId+"_file",e.next=3,window.electron.openFolderSelectFile(`chat_files/${n}`,t);case 3:r=e.sent,"error"===r.type&&(0,z.nk)({type:"error",message:r.message});case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),gn=function(){var e=ae(re().mark((function e(){var t,n,r,o;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r=null===(t=Nt.value)||void 0===t?void 0:t.uid,o=null===(n=Nt.value)||void 0===n?void 0:n.file,fn(r,o,!0);case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),yn=function(){var e=ae(re().mark((function e(t,n){var r,o,a;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t+"_"+p.kQ.value.accountId+"_record.txt",o=f.Ay.gm_encrypt(n,"zysoft2017-08-11","zysoft2017-08-11"),e.next=4,window.electron.saveRecordFile("chat_record",r,o);case 4:a=e.sent,console.log(a);case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),xn=function(){var e=ae(re().mark((function e(t){var n,r,o,a,i,l;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t+"_"+p.kQ.value.accountId+"_record.txt",e.next=3,window.electron.readRecordFile("chat_record",n);case 3:if(r=e.sent,"success"===r.type){for(i in o=JSON.parse(f.Ay.gm_decrypt(r.data,"zysoft2017-08-11","zysoft2017-08-11")),a=[],o)Object.prototype.hasOwnProperty.call(o,i)&&(l=o[i],a.push(l));yt.value=o,xt.value=a}case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),wn=function(e){e&&(0,v.r)({name:{NODE_ENV:"production",VUE_APP_CATALOG:"",VUE_APP_MICRO_APP:"",VUE_APP_URL:"https://xaszzx.xa-cppcc.gov.cn:8131/lzt",BASE_URL:"/"}.VUE_APP_NAME,fileId:e.id,fileType:e.extName,fileName:e.originalFileName,fileSize:e.fileSize})},bn=function(e,t){var n=e?q.value.filter((function(t){var n;return null===(n=t.chatObjectInfo)||void 0===n||null===(n=n.name)||void 0===n?void 0:n.toLowerCase().includes(null===e||void 0===e?void 0:e.toLowerCase())})):[];t(n)},Cn=function(){tn.value.wrapRef.scrollTop=tn.value.wrapRef.scrollHeight},kn=function(){tn.value.wrapRef.scrollTop=tn.value.wrapRef.scrollHeight-rn.value},En=function(e){var t,n=e.scrollTop;(on.value=n,0===n)&&(rn.value=tn.value.wrapRef.scrollHeight,Mn((null===(t=ln.value[0])||void 0===t?void 0:t.sentTime)||0))},Vn=function(){var e=ae(re().mark((function e(t){return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(J.value!==t.id){e.next=2;break}return e.abrupt("return");case 2:X.value=!0,J.value=t.id;case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),In=function(){var e=ae(re().mark((function e(t,n){var r;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:Yt.value=!1,ee.value=t,rn.value=0,ln.value=[],mt.value=[],ht.value=[],gt.value={},n||(null===(r=nn.value)||void 0===r||r.clearMessage(),Qt.value="",Wt.value=!1),Mn(),t.isTemporary||_n();case 10:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Tn=function(){Y.s.confirm("此操作将清除所有消息的未读状态, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Nn()})).catch((function(){(0,z.nk)({type:"info",message:"已取消清除"})}))},Nn=function(){var e=ae(re().mark((function e(){var t,n,r;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.clearAllMessagesUnreadStatus();case 2:t=e.sent,n=t.code,r=t.msg,0===n?Mr():console.log(n,r);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),_n=function(){var e=ae(re().mark((function e(t){var n,r,o;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.clearMessagesUnreadStatus({conversationType:ee.value.type,targetId:ee.value.targetId});case 2:n=e.sent,r=n.code,o=n.msg,0===r?t||Mr():console.log(r,o);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Mn=function(){var e=ae(re().mark((function e(){var t,n,r,o,a,i,l,u=arguments;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=u.length>0&&void 0!==u[0]?u[0]:0,n={timestamp:t,count:20,order:0},r={conversationType:ee.value.type,targetId:ee.value.targetId},e.next=5,c.getHistoryMessages(r,n);case 5:if(o=e.sent,a=o.code,i=o.data,l=o.msg,0!==a){e.next=16;break}return e.next=12,Ln();case 12:an.value=0===t,jn(i.list,0===t),e.next=17;break;case 16:console.log(a,l);case 17:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Gn=function(){var e=ae(re().mark((function e(){var t,n,r,o,a,i;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t={timestamp:ee.value.sentTime,count:20,order:1},n={conversationType:ee.value.type,targetId:ee.value.targetId},e.next=4,c.getHistoryMessages(n,t);case 4:r=e.sent,o=r.code,a=r.data,i=r.msg,0===o?(an.value=!0,_n(!0),jn(a.list,!0)):console.log(o,i);case 9:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),On=function(){var e=ae(re().mark((function e(t){var n,r,o,a;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,s.A.chatGroupInfo({detailId:t.slice(p.TC.value.length)});case 2:return n=e.sent,r=n.data,o=JSON.parse(localStorage.getItem("isChatGroupAnnouncement"))||{},a=o.hasOwnProperty(t)?o[t]:{show:!0,callBoard:""},r.callBoard&&a.callBoard!==r.callBoard&&(Qt.value=r.callBoard,Wt.value=!0),e.abrupt("return",r);case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Sn=function(){var e=JSON.parse(localStorage.getItem("isChatGroupAnnouncement"))||{},t=Z({},e);t[ee.value.targetId]={show:!1,callBoard:Qt.value},localStorage.setItem("isChatGroupAnnouncement",JSON.stringify(t)),Qt.value="",Wt.value=!1},Bn=function(e,t){if(e.length!==t.length)return!1;for(var n=Q(e).sort(),r=Q(t).sort(),o=0;o<n.length;o++)if(n[o]!==r[o])return!1;return!0},Ln=function(){var e=ae(re().mark((function e(){var t,n,r,o,a,i,l,u;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t={},3!==ee.value.type){e.next=16;break}return e.next=4,On(ee.value.targetId);case 4:if(n=e.sent,n.id||Y.s.alert("当前群组已解散！","提示",{confirmButtonText:"确定",callback:function(){rn.value=0,ln.value=[],un.value=[],kt.value=ee.value,lr()}}),r=Bn(n.memberUserIds,te.value.map((function(e){return e.accountId}))),r){e.next=11;break}return e.next=10,(0,m._S)(ee.value.targetId.slice(p.TC.value.length));case 10:te.value=e.sent;case 11:for(o=!1,a=0;a<te.value.length;a++)i=te.value[a],i.accountId===p.kQ.value.accountId&&(o=!0),t[p.TC.value+i.accountId]={uid:p.TC.value+i.accountId,id:i.accountId,name:i.userName,img:i.photo||i.headImg,userInfo:{userId:i.id,userName:i.userName,photo:i.photo,headImg:i.headImg}};X.value=!n.id||o,e.next=20;break;case 16:X.value=!0,te.value=[],t[p.TC.value+p.kQ.value.accountId]={uid:p.TC.value+p.kQ.value.accountId,id:p.kQ.value.accountId,name:p.kQ.value.userName,img:p.kQ.value.photo||p.kQ.value.headImg,userInfo:{userId:p.kQ.value.id,userName:p.kQ.value.userName,photo:p.kQ.value.photo,headImg:p.kQ.value.headImg}},t[null===(l=ee.value)||void 0===l?void 0:l.chatObjectInfo.uid]=null===(u=ee.value)||void 0===u?void 0:u.chatObjectInfo;case 20:wt.value=t;case 21:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),jn=function(){var e=ae(re().mark((function e(t,n){var r,o,a;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,A(t,wt.value);case 2:r=e.sent,o=r.newMessages,a=r.withdrawId,console.log(o),n?(ln.value=[].concat(Q(ln.value),Q(o)).filter((function(e){return!(null!==a&&void 0!==a&&a.includes(e.uid))})),Dn(a)):(ln.value=[].concat(Q(o),Q(ln.value)).filter((function(e){return!(null!==a&&void 0!==a&&a.includes(e.uid))})),Dn(a));case 7:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Dn=function(){var e=ae(re().mark((function e(t,n){var r,o,a,i,l,s,c,f,v;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(r=[],o=[],a=[],i=[],l=!1,s=0;s<ln.value.length;s++)f=ln.value[s],null!==(c=f.content)&&void 0!==c&&null!==(c=c.content)&&void 0!==c&&c.includes("群公告：\n")&&(l=!0),null!==i&&void 0!==i&&i.includes(f.uid)||null!==t&&void 0!==t&&t.includes(f.uid)||(i.push(f.uid),null!==(v=r)&&void 0!==v&&v.includes((0,d.G)(f.sentTime))||(r=[(0,d.G)(f.sentTime),(0,d.G)(f.sentTime+6e4)],o.push({id:f.sentTime,type:"time",className:"GlobalChatMessagesTime",content:(0,d.G)(f.sentTime)})),"RC:ImgMsg"===f.type?(o.push(Z(Z({},f),{},{imgIndex:a.length})),a.push(f.content.imageUri)):"RC:HQVCMsg"===f.type?o.push(Z(Z({},f),{},{audio:new Audio(f.content.remoteUrl)})):"RC:ImgTextMsg"===f.type?(o.push(f),mt.value.push(null===f||void 0===f?void 0:f.uid)):o.push(f));l&&On(ee.value.targetId),ne.value=a,un.value=o,n?(0,u.nextTick)((function(){tn.value.wrapRef.scrollTop=on.value})):(0,u.nextTick)((function(){an.value?Cn():kn()}));case 10:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),An=function(e,t){kt.value=t,bt.value=e.pageY,Ct.value=e.pageX,Et.value=!0,Vt.value=!0},Pn=function(e,t){var n,r=window.getSelection(),o=r.toString()||(null===t||void 0===t||null===(n=t.content)||void 0===n?void 0:n.content),a=(new Date).getTime(),i=a-t.sentTime,l=i<18e4;Nt.value=Z(Z({},t),{},{copyContent:o,isWithdraw:l}),It.value=e.pageY,Tt.value=e.pageX,_t.value=!0,Mt.value=!0},Fn=function(e){oe.value.id?oe.value.id===e.id?(pt.value[oe.value.id]=oe.value.audio.currentTime,Hn()):(pt.value[oe.value.id]=oe.value.audio.currentTime,Hn(),oe.value={id:e.id,audio:e.audio},oe.value.audio.currentTime=0,Rn()):(oe.value={id:e.id,audio:e.audio},oe.value.audio.currentTime=0,Rn())},Un=function(e){oe.value.id&&(pt.value[oe.value.id]=oe.value.audio.currentTime,Hn()),oe.value={id:e.id,audio:e.audio},oe.value.audio.currentTime=pt.value[oe.value.id]||0,Rn()},Rn=function(){oe.value.audio.play(),oe.value.audio.addEventListener("ended",(function(){pt.value[oe.value.id]=0,oe.value={}}))},Hn=function(){oe.value.audio.pause(),oe.value={}},zn=function(){an.value&&Cn()},Yn=function(e){e.preventDefault();var t=e.dataTransfer.files;if(t.length){for(var n=[],r=0;r<t.length;r++){var o=t.item(r),a=o.name.substring(o.name.lastIndexOf(".")+1).toLowerCase();n.push({id:en(),name:o.name,extName:a,size:o.size,file:o})}Gt.value=n,Ot.value=!0}},Qn=function(e){if(e)for(var t=0;t<e.length;t++){var n=e[t];Jn(n)}Gt.value=[],Ot.value=!1},Wn=function(e){St.value=e,Bt.value=!0},$n=function(e){e&&Jn(St.value),St.value={},Bt.value=!1},Jn=function(){var e=ae(re().mark((function e(t){var n,r,o,a;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=new FormData,r.append("file",t.file),r.append("isKeepAlive",!0),r.append("uid",t.id||t.uid||en()),e.next=6,s.A.globalUpload(r,(function(){}));case 6:o=e.sent,a=o.data,null!==(n=["png","jpg","jpeg"])&&void 0!==n&&n.includes(a.extName)?tr(s.A.openImgURL(a.newFileName)):(localStorage.setItem(a.id,JSON.stringify(a)),nr(a.id),3===ee.value.type&&Kn(a.id));case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Kn=function(){var e=ae(re().mark((function e(t){return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,s.A.chatGroupFileAdd({form:{chatGroupId:ee.value.targetId.slice(p.TC.value.length),fileId:t}});case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),qn=function(){var e=navigator.userAgent.toLowerCase();return(null===e||void 0===e?void 0:e.includes("macintosh"))||(null===e||void 0===e?void 0:e.includes("mac os x"))},Xn=function(){X.value&&(Yt.value=!Yt.value)},Zn=function(e){var t,n=null===e||void 0===e||null===(t=e.mentions)||void 0===t?void 0:t.map((function(e){return p.TC.value+e.userInfo.accountId})),r=n.length?{mentionedContent:"",type:2,userIdList:Array.from(new Set(n))}:{};er(e.content,r)},er=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e.replace(/^\s+|\s+$/g,"")){var n=new c.TextMessage({content:e,mentionedInfo:t}),r={conversationType:ee.value.type,targetId:ee.value.targetId},o={onSendBefore:function(e){an.value=!0,jn([Z(Z({},e),{},{sentTime:Date.parse(new Date)})],!0)}};or(r,n,o,(function(e,t,n){if(0===e){for(var r=[],o=0;o<ln.value.length;o++){var a=ln.value[o];r.push(a.id===n.messageId?Z(Z({},a),{},{uid:n.messageUId,sentTime:n.sentTime}):a)}ln.value=r,Dn([],!0),console.log("消息发送成功：",n)}else console.log("消息发送失败：",e,t)}))}},tr=function(e){var t=new c.ImageMessage({content:"",imageUri:e}),n={conversationType:ee.value.type,targetId:ee.value.targetId},r={onSendBefore:function(e){an.value=!0,jn([Z(Z({},e),{},{sentTime:Date.parse(new Date)})],!0)}};or(n,t,r,(function(e,t,n){if(0===e){for(var r=[],o=0;o<ln.value.length;o++){var a=ln.value[o];r.push(a.id===n.messageId?Z(Z({},a),{},{uid:n.messageUId,sentTime:n.sentTime}):a)}ln.value=r,Dn([],!0),console.log("消息发送成功：",n)}else console.log("消息发送失败：",e,t)}))},nr=function(e){var t=c.registerMessageType("RC:ImgTextMsg",!0,!0,[],!1),n=new t({content:`[文件],${e}`,title:"[文件]"}),r={conversationType:ee.value.type,targetId:ee.value.targetId},o={onSendBefore:function(e){an.value=!0,jn([Z(Z({},e),{},{sentTime:Date.parse(new Date)})],!0)}};or(r,n,o,(function(e,t,n){if(0===e){for(var r=[],o=0;o<ln.value.length;o++){var a=ln.value[o];r.push(a.id===n.messageId?Z(Z({},a),{},{uid:n.messageUId,sentTime:n.sentTime}):a)}ln.value=r,Dn([],!0),console.log("消息发送成功：",n)}else console.log("消息发送失败：",e,t)}))},rr=function(e){var t=c.registerMessageType("RC:CmdNtf",!0,!0,[],!1),n=new t(e),r={conversationType:ee.value.type,targetId:ee.value.targetId},o={onSendBefore:function(e){an.value=!0,jn([Z(Z({},e),{},{sentTime:Date.parse(new Date)})],!0)}};or(r,n,o,(function(e,t,n){if(0===e){for(var r=[],o=0;o<ln.value.length;o++){var a=ln.value[o];r.push(a.id===n.messageId?Z(Z({},a),{},{uid:n.messageUId,sentTime:n.sentTime}):a)}ln.value=r,Dn([],!0),console.log("消息发送成功：",n)}else console.log("消息发送失败：",e,t)}))},or=function(){var e=ae(re().mark((function e(t,n,r,o){var a,i,l,u;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.sendMessage(t,n,r);case 2:a=e.sent,i=a.code,l=a.msg,u=a.data,0===i&&Mr(),o(i,l,u);case 8:case"end":return e.stop()}}),e)})));return function(t,n,r,o){return e.apply(this,arguments)}}(),ar=function(){var e=ae(re().mark((function e(t){var n,r;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.setConversationNotificationStatus({conversationType:kt.value.type,targetId:kt.value.id},t);case 2:n=e.sent,r=n.code,r||Mr();case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ir=function(){var e=ae(re().mark((function e(t){var n,r;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.setConversationToTop({conversationType:kt.value.type,targetId:kt.value.id},t);case 2:n=e.sent,r=n.code,r||Mr();case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),lr=function(){var e=ae(re().mark((function e(){var t,n,r;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.removeConversation({conversationType:kt.value.type,targetId:kt.value.id});case 2:t=e.sent,n=t.code,r=t.msg,0===n?(console.log("消息删除成功"),Mr("del",kt.value)):console.log("消息删除失败：",n,r);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ur=function(){var e=ae(re().mark((function e(){var t,n,r,o,a;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t={conversationType:ee.value.type,targetId:ee.value.targetId},n=[{messageUId:Nt.value.uid,sentTime:Nt.value.sentTime,messageDirection:Nt.value.direction}],e.next=4,c.deleteMessages(t,n);case 4:r=e.sent,o=r.code,a=r.msg,0===o?(console.log("消息删除成功"),ln.value=ln.value.filter((function(e){return e.uid!==Nt.value.uid})),Dn([],!0)):console.log("消息删除失败：",o,a);case 8:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),sr=function(){var e=ae(re().mark((function e(){var t,n,r,o,a;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t={conversationType:ee.value.type,targetId:ee.value.targetId},n={messageUId:Nt.value.uid,sentTime:Nt.value.sentTime,disableNotification:!0},e.next=4,c.recallMessage(t,n);case 4:r=e.sent,o=r.code,a=r.msg,0===o?(In(ee.value,!0),console.log("消息撤回成功")):console.log("消息撤回失败：",o,a);case 8:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),cr=function(){var e=ae(re().mark((function e(t,n,r){var o,a,i,l,u,s,d,f,v,p,m;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("create"===t&&fr(null===n||void 0===n||null===(o=n.chatObjectInfo)||void 0===o?void 0:o.id),"add"===t&&vr(null===n||void 0===n||null===(a=n.chatObjectInfo)||void 0===a?void 0:a.id),"del"===t&&pr(null===n||void 0===n||null===(i=n.chatObjectInfo)||void 0===i?void 0:i.id),"name"===t&&mr(null===n||void 0===n||null===(l=n.chatObjectInfo)||void 0===l?void 0:l.id),"qr"===t&&hr(null===n||void 0===n||null===(u=n.chatObjectInfo)||void 0===u?void 0:u.id),"announcement"===t&&gr(null===n||void 0===n||null===(s=n.chatObjectInfo)||void 0===s?void 0:s.id,r),"transfer"===t&&yr(null===n||void 0===n||null===(d=n.chatObjectInfo)||void 0===d?void 0:d.id),"quit"!==t){e.next=15;break}return f=c.registerMessageType("RC:CmdNtf",!0,!0,[],!1),v=new f(n),p={conversationType:ee.value.type,targetId:ee.value.targetId},m={onSendBefore:function(){}},e.next=14,c.sendMessage(p,v,m);case 14:dr();case 15:case"end":return e.stop()}}),e)})));return function(t,n,r){return e.apply(this,arguments)}}(),dr=function(){var e=ae(re().mark((function e(){var t,n,r;return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.removeConversation({conversationType:ee.value.type,targetId:ee.value.targetId});case 2:t=e.sent,n=t.code,r=t.msg,0===n?(console.log("消息删除成功"),Mr()):console.log("消息删除失败：",n,r);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),fr=function(e){var t,n;Lt.value=e?[null===(t=p.kQ.value)||void 0===t?void 0:t.accountId,e]:[null===(n=p.kQ.value)||void 0===n?void 0:n.accountId],jt.value=!0},vr=function(e){Dt.value=e,At.value=!0},pr=function(e){Dt.value=e,Pt.value=!0},mr=function(e){Dt.value=e,Ft.value=!0},hr=function(e){Dt.value=e,Ut.value=!0},gr=function(e,t){Dt.value=e,Rt.value=t,Ht.value=!0},yr=function(e){Dt.value=e,zt.value=!0},xr=function(){Dt.value=ee.value.targetId.slice(p.TC.value.length),Kt.value=!0},wr=function(e){$t.value=e.id,Xt.value=!0},br=function(e){Yt.value=!1,e&&P("send",e),jt.value=!1},Cr=function(){var e=ae(re().mark((function e(t,n){return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(Yt.value=!1,!t){e.next=5;break}return e.next=4,Ln();case 4:rr(n);case 5:At.value=!1;case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),kr=function(){var e=ae(re().mark((function e(t,n){return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(Yt.value=!1,!t){e.next=5;break}return e.next=4,Ln();case 4:rr(n);case 5:Pt.value=!1;case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Er=function(){var e=ae(re().mark((function e(t,n){return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(Yt.value=!1,!t){e.next=6;break}return e.next=4,Ln();case 4:_r(),rr(n);case 6:Ft.value=!1;case 7:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Vr=function(){var e=ae(re().mark((function e(t,n){return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:Yt.value=!1,t&&er(n),Ht.value=!1;case 3:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Ir=function(){var e=ae(re().mark((function e(t,n){return re().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(Yt.value=!1,!t){e.next=5;break}return e.next=4,Ln();case 4:rr(n);case 5:zt.value=!1;case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Tr=function(){qt.value=!0},Nr=function(){Jt.value=en(),qt.value=!1,Xt.value=!1},_r=function(){P("time")},Mr=function(e,t){P("refresh",e,t)};return(0,u.onUnmounted)((function(){if(W){var e=JSON.stringify(yt.value);J.value&&yn(J.value,e)}})),(0,u.watch)((function(){return J.value}),(function(e,t){if(W){var n=JSON.stringify(yt.value);yt.value={},xt.value=[],t&&yn(t,n),e&&xn(e)}if(J.value)for(var r=0;r<D.chatList.length;r++){var o=D.chatList[r];J.value===o.id&&In(o)}}),{immediate:!0}),(0,u.watch)((function(){return q.value}),(function(){if(J.value){for(var e=!0,t=0;t<D.chatList.length;t++){var n=D.chatList[t];J.value===n.id&&(e=!1,ee.value=n)}e&&(J.value="",ee.value={})}}),{immediate:!0}),g({getNewestMessages:Gn}),function(e,t){var n,s,c,v=l.Zq,p=i.z_,m=a.s9,h=o.kA,g=(0,u.resolveComponent)("MoreFilled"),y=r.tk,D=(0,u.resolveComponent)("CircleCloseFilled"),A=(0,u.resolveComponent)("Warning"),P=(0,u.resolveDirective)("copy");return(0,u.openBlock)(),(0,u.createElementBlock)("div",{class:(0,u.normalizeClass)(["GlobalChatView",{GlobalChatMacView:(0,u.unref)(F)}]),onClick:(0,u.withModifiers)(sn,["prevent"]),onContextmenu:(0,u.withModifiers)(cn,["prevent"])},[(0,u.createElementVNode)("div",ie,[(0,u.createElementVNode)("div",le,[(0,u.createVNode)(m,{modelValue:$.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return $.value=e}),"prefix-icon":(0,u.unref)(H.Search),"fetch-suggestions":bn,placeholder:"搜索","popper-class":"GlobalChatViewAutocomplete",clearable:"",onSelect:Vn},{default:(0,u.withCtx)((function(e){var t,n,r,o,a,i=e.item;return[(0,u.createElementVNode)("div",ue,[(0,u.createVNode)(p,{value:i.count,hidden:!i.count,"is-dot":1===i.isNotInform},{default:(0,u.withCtx)((function(){var e;return[(0,u.createVNode)(v,{src:Zt(null===(e=i.chatObjectInfo)||void 0===e?void 0:e.img),fit:"cover",draggable:"false"},null,8,["src"])]})),_:2},1032,["value","hidden","is-dot"]),(0,u.createElementVNode)("div",se,[(0,u.createElementVNode)("div",ce,[(0,u.createElementVNode)("div",de,(0,u.toDisplayString)(null===(t=i.chatObjectInfo)||void 0===t?void 0:t.name),1),(0,u.createElementVNode)("span",null,(0,u.toDisplayString)((0,u.unref)(O)(null===i||void 0===i?void 0:i.sentTime)),1)]),1===i.isNotInform?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:0,class:"GlobalChatViewNotInform",innerHTML:(0,u.unref)(R.Lo)},null,8,fe)):(0,u.createCommentVNode)("",!0),"RC:ImgTextMsg"===i.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",ve,(0,u.toDisplayString)(null===i||void 0===i||null===(n=i.content)||void 0===n?void 0:n.title),1)):"RC:ImgMsg"===i.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",pe," [图片] ")):"RC:HQVCMsg"===i.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",me," [语音] "+(0,u.toDisplayString)(null===i||void 0===i||null===(r=i.content)||void 0===r?void 0:r.duration)+'" ',1)):"RC:CmdNtf"===i.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",he,(0,u.toDisplayString)(null===i||void 0===i||null===(o=i.content)||void 0===o?void 0:o.name),1)):"RC:LBSMsg"===i.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",ge," [不支持的消息，请在移动端进行查看] ")):"RC:RcCmd"===i.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",ye,(0,u.toDisplayString)(i.revocationMessage),1)):((0,u.openBlock)(),(0,u.createElementBlock)("div",xe,(0,u.toDisplayString)(null===i||void 0===i||null===(a=i.content)||void 0===a?void 0:a.content),1))])])]})),_:1},8,["modelValue","prefix-icon"]),(0,u.createElementVNode)("div",{class:"GlobalChatViewListHeadIcon",innerHTML:(0,u.unref)(R.dA),onClick:t[1]||(t[1]=function(e){return fr("")})},null,8,we)]),(0,u.createVNode)(h,{class:"GlobalChatViewMessagesList"},{default:(0,u.withCtx)((function(){return[((0,u.openBlock)(!0),(0,u.createElementBlock)(u.Fragment,null,(0,u.renderList)(q.value,(function(e){var t,n,r,o,a,i,l;return(0,u.openBlock)(),(0,u.createElementBlock)("div",{class:(0,u.normalizeClass)(["GlobalChatViewMessagesItem",{"is-top":e.isTop},{"is-active":e.id===J.value}]),key:e.id,onClick:function(t){return Vn(e)},onContextmenu:(0,u.withModifiers)((function(t){return An(t,e)}),["prevent"])},[(0,u.createVNode)(p,{value:e.count,hidden:!e.count,"is-dot":1===e.isNotInform},{default:(0,u.withCtx)((function(){var t;return[(0,u.createVNode)(v,{src:Zt(null===(t=e.chatObjectInfo)||void 0===t?void 0:t.img),fit:"cover",draggable:"false"},null,8,["src"])]})),_:2},1032,["value","hidden","is-dot"]),(0,u.createElementVNode)("div",Ce,[(0,u.createElementVNode)("div",ke,[null!==(t=e.chatObjectInfo)&&void 0!==t&&t.chatGroupType?(0,u.createCommentVNode)("",!0):((0,u.openBlock)(),(0,u.createElementBlock)("div",Ee,(0,u.toDisplayString)(null===(n=e.chatObjectInfo)||void 0===n?void 0:n.name),1)),3===e.type&&e.chatObjectInfo.chatGroupType?((0,u.openBlock)(),(0,u.createElementBlock)("div",Ve,[(0,u.createTextVNode)((0,u.toDisplayString)(null===(r=e.chatObjectInfo)||void 0===r?void 0:r.name)+" ",1),(0,u.createElementVNode)("span",null,(0,u.toDisplayString)(e.chatObjectInfo.chatGroupType),1)])):(0,u.createCommentVNode)("",!0),(0,u.createElementVNode)("span",null,(0,u.toDisplayString)((0,u.unref)(O)(null===e||void 0===e?void 0:e.sentTime)),1)]),1===e.isNotInform?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:0,class:"GlobalChatViewNotInform",innerHTML:(0,u.unref)(R.Lo)},null,8,Ie)):(0,u.createCommentVNode)("",!0),"RC:ImgTextMsg"===e.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",Te,(0,u.toDisplayString)(null===e||void 0===e||null===(o=e.content)||void 0===o?void 0:o.title),1)):"RC:ImgMsg"===e.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",Ne,"[图片]")):"RC:HQVCMsg"===e.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",_e," [语音] "+(0,u.toDisplayString)(null===e||void 0===e||null===(a=e.content)||void 0===a?void 0:a.duration)+'" ',1)):"RC:CmdNtf"===e.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",Me,(0,u.toDisplayString)(null===e||void 0===e||null===(i=e.content)||void 0===i?void 0:i.name),1)):"RC:LBSMsg"===e.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",Ge," [不支持的消息，请在移动端进行查看] ")):"RC:RcCmd"===e.messageType?((0,u.openBlock)(),(0,u.createElementBlock)("div",Oe,(0,u.toDisplayString)(e.revocationMessage),1)):((0,u.openBlock)(),(0,u.createElementBlock)("div",Se,(0,u.toDisplayString)(null===e||void 0===e||null===(l=e.content)||void 0===l?void 0:l.content),1))])],42,be)})),128))]})),_:1}),(0,u.createElementVNode)("div",{class:"GlobalChatClearAway",onClick:Tn},[(0,u.createElementVNode)("div",{innerHTML:(0,u.unref)(R.LH)},null,8,Be),t[20]||(t[20]=(0,u.createTextVNode)(" 清除未读 "))]),(0,u.withDirectives)((0,u.createElementVNode)("div",{class:"GlobalChatViewMenu",style:(0,u.normalizeStyle)({left:Ct.value+"px",top:bt.value+"px"})},[kt.value.isTop?(0,u.createCommentVNode)("",!0):((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:0,class:"GlobalChatViewMenuItem",onClick:t[2]||(t[2]=function(e){return ir(!0)})},"置顶")),kt.value.isTop?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:1,class:"GlobalChatViewMenuItem",onClick:t[3]||(t[3]=function(e){return ir(!1)})},"取消置顶")):(0,u.createCommentVNode)("",!0),2===kt.value.isNotInform?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:2,class:"GlobalChatViewMenuItem",onClick:t[4]||(t[4]=function(e){return ar(1)})}," 消息免打扰 ")):(0,u.createCommentVNode)("",!0),1===kt.value.isNotInform?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:3,class:"GlobalChatViewMenuItem",onClick:t[5]||(t[5]=function(e){return ar(2)})}," 允许消息通知 ")):(0,u.createCommentVNode)("",!0),t[21]||(t[21]=(0,u.createElementVNode)("div",{class:"GlobalChatViewMenuLine"},null,-1)),(0,u.createElementVNode)("div",{class:"GlobalChatViewMenuItem",onClick:lr},"删除")],4),[[u.vShow,Et.value]])]),J.value?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:0,class:"GlobalChatWindow",onDragover:t[7]||(t[7]=(0,u.withModifiers)((function(){}),["prevent"])),onDrop:Yn},[(0,u.createElementVNode)("div",Le,[(0,u.createElementVNode)("div",{class:"ellipsis",onClick:Xn},[(0,u.createTextVNode)((0,u.toDisplayString)(null===(n=ee.value.chatObjectInfo)||void 0===n?void 0:n.name)+" ",1),3===ee.value.type&&te.value.length?((0,u.openBlock)(),(0,u.createElementBlock)("span",je,"（"+(0,u.toDisplayString)(te.value.length)+"）",1)):(0,u.createCommentVNode)("",!0)]),(0,u.createElementVNode)("div",{class:"GlobalChatWindowMore",onClick:Xn},[(0,u.createVNode)(y,null,{default:(0,u.withCtx)((function(){return[(0,u.createVNode)(g)]})),_:1})])]),(0,u.createVNode)(h,{ref_key:"scrollRef",ref:tn,always:"",class:(0,u.normalizeClass)(["GlobalChatWindowScroll",{GlobalChatWindowNoChat:!X.value}]),onScroll:En},{default:(0,u.withCtx)((function(){return[Wt.value?((0,u.openBlock)(),(0,u.createElementBlock)("div",De,[(0,u.createElementVNode)("div",Ae,[(0,u.createElementVNode)("div",null,[(0,u.createElementVNode)("span",{innerHTML:(0,u.unref)(R.Jt)},null,8,Pe),t[22]||(t[22]=(0,u.createTextVNode)(" 群公告 "))]),(0,u.createVNode)(y,{onClick:Sn},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)(D)]})),_:1})]),(0,u.createElementVNode)("div",Fe,(0,u.toDisplayString)(Qt.value),1)])):(0,u.createCommentVNode)("",!0),(0,u.createElementVNode)("div",Ue,[((0,u.openBlock)(!0),(0,u.createElementBlock)(u.Fragment,null,(0,u.renderList)(un.value,(function(e){var n,r,o,a,i,l,s,c,p,m,h,g,x,w,b,C,k,E;return(0,u.openBlock)(),(0,u.createElementBlock)("div",{key:e.id||e.uid,class:(0,u.normalizeClass)(e.className)},["time"===e.type?((0,u.openBlock)(),(0,u.createElementBlock)(u.Fragment,{key:0},[(0,u.createTextVNode)((0,u.toDisplayString)(null===e||void 0===e?void 0:e.content),1)],64)):"RC:CmdNtf"===e.type?((0,u.openBlock)(),(0,u.createElementBlock)(u.Fragment,{key:1},[(0,u.createTextVNode)((0,u.toDisplayString)(null===e||void 0===e||null===(n=e.content)||void 0===n?void 0:n.name),1)],64)):"RC:RcCmd"===e.type?((0,u.openBlock)(),(0,u.createElementBlock)(u.Fragment,{key:2},[(0,u.createTextVNode)((0,u.toDisplayString)(e.chatObjectInfoType?"你":e.userName)+"撤回了一条消息 ",1)],64)):((0,u.openBlock)(),(0,u.createElementBlock)(u.Fragment,{key:3},[(0,u.createElementVNode)("div",Re,[(0,u.createVNode)(v,{src:Zt(null===(r=e.chatObjectInfo)||void 0===r?void 0:r.img),fit:"cover",draggable:"false"},null,8,["src"])]),(0,u.createElementVNode)("div",He,[e.chatObjectInfoType?(0,u.createCommentVNode)("",!0):((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:0,class:"GlobalChatMessagesName ellipsis",onContextmenu:(0,u.withModifiers)((function(t){return Pn(t,e)}),["prevent"])},(0,u.toDisplayString)(null===(o=e.chatObjectInfo)||void 0===o?void 0:o.name),41,ze)),"RC:TxtMsg"===e.type?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:1,class:"GlobalChatMessagesText",onContextmenu:(0,u.withModifiers)((function(t){return Pn(t,e)}),["prevent"])},[t[23]||(t[23]=(0,u.createElementVNode)("span",null,null,-1)),(0,u.createElementVNode)("div",{class:"GlobalChatEmotion",innerHTML:null===e||void 0===e||null===(a=e.content)||void 0===a?void 0:a.htmlContent},null,8,Qe)],40,Ye)):(0,u.createCommentVNode)("",!0),"RC:HQVCMsg"===e.type?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:2,class:"GlobalChatMessagesText",onContextmenu:(0,u.withModifiers)((function(t){return Pn(t,e)}),["prevent"]),onClick:function(t){return Fn(e)}},[t[24]||(t[24]=(0,u.createElementVNode)("span",null,null,-1)),(0,u.createElementVNode)("div",{class:(0,u.normalizeClass)(["GlobalChatVoice",{"is-active":oe.value.id===e.id}]),style:(0,u.normalizeStyle)({width:`${88+((null===e||void 0===e||null===(i=e.content)||void 0===i?void 0:i.duration)||0)}px`})},(0,u.toDisplayString)(null===e||void 0===e||null===(l=e.content)||void 0===l?void 0:l.duration)+'" ',7),oe.value.id!==e.id&&pt.value[e.id]?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:0,class:"GlobalChatVoiceContinue",onClick:(0,u.withModifiers)((function(t){return Un(e)}),["stop"])}," 继续播放 ",8,$e)):(0,u.createCommentVNode)("",!0)],40,We)):(0,u.createCommentVNode)("",!0),"RC:ImgMsg"===e.type?((0,u.openBlock)(),(0,u.createBlock)(v,{key:3,src:null===e||void 0===e||null===(s=e.content)||void 0===s?void 0:s.imageUri,fit:"cover","preview-src-list":ne.value,"initial-index":e.imgIndex,onLoad:zn,onContextmenu:(0,u.withModifiers)((function(t){return Pn(t,e)}),["prevent"])},{error:(0,u.withCtx)((function(){return[(0,u.createElementVNode)("div",{class:"GlobalChatMessagesImgSlot",onContextmenu:(0,u.withModifiers)((function(t){return Pn(t,e)}),["prevent"])},[(0,u.createVNode)(y,null,{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(H.Picture))]})),_:1})],40,Je)]})),_:2},1032,["src","preview-src-list","initial-index","onContextmenu"])):(0,u.createCommentVNode)("",!0),null!==(c=["RC:ImgTextMsg","RC:LBSMsg"])&&void 0!==c&&c.includes(e.type)?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:4,class:(0,u.normalizeClass)(["GlobalChatMessagesCustom",e.customType]),onContextmenu:(0,u.withModifiers)((function(t){return Pn(t,e)}),["prevent"]),onClick:function(t){return dn(e)}},[t[26]||(t[26]=(0,u.createElementVNode)("span",null,null,-1)),"file"===e.customType?((0,u.openBlock)(),(0,u.createElementBlock)(u.Fragment,{key:0},[null!==(p=ht.value)&&void 0!==p&&p.includes(e.file.id)?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:0,class:"GlobalChatMessagesFileDownload",style:(0,u.normalizeStyle)({width:gt.value[e.file.id]})},null,4)):(0,u.createCommentVNode)("",!0),(0,u.createElementVNode)("div",{class:(0,u.normalizeClass)(["globalFileIcon",(0,u.unref)(G)(null===(m=e.file)||void 0===m?void 0:m.extName)])},null,2),(0,u.createElementVNode)("div",qe,(0,u.toDisplayString)((null===(h=e.file)||void 0===h?void 0:h.originalFileName)||"未知文件"),1),(0,u.createElementVNode)("div",Xe,(0,u.toDisplayString)(null!==(g=e.file)&&void 0!==g&&g.fileSize?(0,u.unref)(f.z9)(e.file.fileSize):"0KB"),1)],64)):(0,u.createCommentVNode)("",!0),"vote"===e.customType?((0,u.openBlock)(),(0,u.createElementBlock)(u.Fragment,{key:1},[(0,u.createElementVNode)("div",Ze,[(0,u.createElementVNode)("div",et,[(0,u.createElementVNode)("div",{class:"GlobalChatMessagesVoteIcon",innerHTML:(0,u.unref)(R.SK)},null,8,tt),t[25]||(t[25]=(0,u.createTextVNode)(" 投票 "))]),(0,u.createElementVNode)("div",nt,(0,u.toDisplayString)((0,u.unref)(d.G)(null===(x=e.vote)||void 0===x?void 0:x.createDate)),1)]),(0,u.createElementVNode)("div",rt,[(0,u.createElementVNode)("div",{class:"GlobalChatMessagesVoteInfoIcon",innerHTML:(0,u.unref)(R.F$)},null,8,ot),(0,u.createElementVNode)("div",at,(0,u.toDisplayString)(null===(w=e.vote)||void 0===w?void 0:w.topic),1)])],64)):(0,u.createCommentVNode)("",!0),"unknown"===e.customType?((0,u.openBlock)(),(0,u.createElementBlock)("div",it,"[不支持的消息，请在移动端进行查看]")):(0,u.createCommentVNode)("",!0)],42,Ke)):(0,u.createCommentVNode)("",!0),"RC:FileMsg"===e.type?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:5,class:"GlobalChatMessagesFile",onContextmenu:(0,u.withModifiers)((function(t){return Pn(t,e)}),["prevent"])},[t[27]||(t[27]=(0,u.createElementVNode)("span",null,null,-1)),(0,u.createElementVNode)("div",{class:(0,u.normalizeClass)(["globalFileIcon",(0,u.unref)(G)(null===e||void 0===e||null===(b=e.content)||void 0===b?void 0:b.type)])},null,2),(0,u.createElementVNode)("div",ut,(0,u.toDisplayString)((null===e||void 0===e||null===(C=e.content)||void 0===C?void 0:C.name)||"未知文件"),1),(0,u.createElementVNode)("div",st,(0,u.toDisplayString)(null!==e&&void 0!==e&&null!==(k=e.content)&&void 0!==k&&k.size?(0,u.unref)(f.z9)(null===e||void 0===e||null===(E=e.content)||void 0===E?void 0:E.size):"0KB"),1)],40,lt)):(0,u.createCommentVNode)("",!0)])],64))],2)})),128))])]})),_:1},8,["class"]),(0,u.withDirectives)((0,u.createElementVNode)("div",{class:"GlobalChatViewMenu",style:(0,u.normalizeStyle)({left:Tt.value+"px",top:It.value+"px"})},["RC:TxtMsg"===Nt.value.type?(0,u.withDirectives)(((0,u.openBlock)(),(0,u.createElementBlock)("div",ct,t[28]||(t[28]=[(0,u.createTextVNode)(" 复制 ")]))),[[P,null===(s=Nt.value)||void 0===s?void 0:s.copyContent]]):(0,u.createCommentVNode)("",!0),(0,u.unref)(W)&&"file"===(null===(c=Nt.value)||void 0===c?void 0:c.customType)?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:1,class:"GlobalChatViewMenuItem",onClick:gn},(0,u.toDisplayString)(qn()?"在 Finder 中显示":"在资源管理器中显示"),1)):(0,u.createCommentVNode)("",!0),null!==K&&void 0!==K&&K.includes(Nt.value.type)?((0,u.openBlock)(),(0,u.createElementBlock)("div",dt)):(0,u.createCommentVNode)("",!0),Nt.value.chatObjectInfoType&&Nt.value.isWithdraw?(0,u.createCommentVNode)("",!0):((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:3,class:"GlobalChatViewMenuItem",onClick:ur}," 删除 ")),Nt.value.chatObjectInfoType&&Nt.value.isWithdraw?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:4,class:"GlobalChatViewMenuItem",onClick:sr}," 撤回 ")):(0,u.createCommentVNode)("",!0)],4),[[u.vShow,_t.value]]),(0,u.withDirectives)((0,u.createVNode)((0,u.unref)(x),{ref_key:"editorRef",ref:nn,isVote:3===ee.value.type,userData:te.value,onHandleFile:Jn,onHandleVote:xr,onHandlePasteImg:Wn,onHandleSendMessage:Zn},null,8,["isVote","userData"]),[[u.vShow,X.value]]),(0,u.withDirectives)((0,u.createElementVNode)("div",ft,[(0,u.createVNode)(y,null,{default:(0,u.withCtx)((function(){return[(0,u.createVNode)(A)]})),_:1}),t[29]||(t[29]=(0,u.createTextVNode)(" 无法在已退出的群聊中接收和发送消息 "))],512),[[u.vShow,!X.value]]),(0,u.createVNode)((0,u.unref)(b),{modelValue:Yt.value,"onUpdate:modelValue":t[6]||(t[6]=function(e){return Yt.value=e})},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(S),{chatInfo:ee.value,groupUser:te.value,onRefresh:Mr,onCallback:cr},null,8,["chatInfo","groupUser"])]})),_:1},8,["modelValue"])],32)):(0,u.createCommentVNode)("",!0),J.value?(0,u.createCommentVNode)("",!0):((0,u.openBlock)(),(0,u.createElementBlock)("div",vt)),(0,u.createVNode)((0,u.unref)(w),{modelValue:Ot.value,"onUpdate:modelValue":t[8]||(t[8]=function(e){return Ot.value=e})},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(k),{chatInfo:ee.value,fileList:Gt.value,onCallback:Qn},null,8,["chatInfo","fileList"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:Bt.value,"onUpdate:modelValue":t[9]||(t[9]=function(e){return Bt.value=e})},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(C),{chatInfo:ee.value,fileImg:St.value,onCallback:$n},null,8,["chatInfo","fileImg"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:jt.value,"onUpdate:modelValue":t[10]||(t[10]=function(e){return jt.value=e})},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(E),{userId:Lt.value,onCallback:br},null,8,["userId"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:At.value,"onUpdate:modelValue":t[11]||(t[11]=function(e){return At.value=e})},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(V),{infoId:Dt.value,onCallback:Cr},null,8,["infoId"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:Pt.value,"onUpdate:modelValue":t[12]||(t[12]=function(e){return Pt.value=e})},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(I),{infoId:Dt.value,onCallback:kr},null,8,["infoId"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:Ft.value,"onUpdate:modelValue":t[13]||(t[13]=function(e){return Ft.value=e}),class:"GlobalGroupNamePopupWindow"},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(T),{infoId:Dt.value,onCallback:Er},null,8,["infoId"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:Ut.value,"onUpdate:modelValue":t[14]||(t[14]=function(e){return Ut.value=e}),class:"GlobalGroupQrPopupWindow"},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(N),{infoId:Dt.value},null,8,["infoId"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:Ht.value,"onUpdate:modelValue":t[15]||(t[15]=function(e){return Ht.value=e}),class:"GlobalGroupAnnouncementPopupWindow"},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(_),{infoId:Dt.value,isOwner:Rt.value,onCallback:Vr},null,8,["infoId","isOwner"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:zt.value,"onUpdate:modelValue":t[16]||(t[16]=function(e){return zt.value=e})},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(M),{infoId:Dt.value,onCallback:Ir},null,8,["infoId"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:Kt.value,"onUpdate:modelValue":t[17]||(t[17]=function(e){return Kt.value=e}),class:"GlobalGroupVotePopupWindow"},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(B),{id:Dt.value,refresh:Jt.value,onCallback:Tr,onSendMessage:rr},null,8,["id","refresh"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:qt.value,"onUpdate:modelValue":t[18]||(t[18]=function(e){return qt.value=e})},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(L),{dataId:Dt.value,onCallback:Nr},null,8,["dataId"])]})),_:1},8,["modelValue"]),(0,u.createVNode)((0,u.unref)(w),{modelValue:Xt.value,"onUpdate:modelValue":t[19]||(t[19]=function(e){return Xt.value=e}),class:"GlobalGroupVotePopupWindow"},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)((0,u.unref)(j),{id:$t.value,onCallback:Nr,onSendMessage:rr},null,8,["id"])]})),_:1},8,["modelValue"])],34)}}});const ht=mt;var gt=ht}}]);