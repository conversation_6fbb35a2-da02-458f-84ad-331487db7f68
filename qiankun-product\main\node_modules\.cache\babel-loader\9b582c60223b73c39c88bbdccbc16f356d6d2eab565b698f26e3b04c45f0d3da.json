{"ast": null, "code": "import { resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createBlock(_resolveDynamicComponent($setup.elComponent[$setup.route.query.code]));\n}", "map": {"version": 3, "names": ["_createBlock", "_resolveDynamicComponent", "$setup", "elComponent", "route", "query", "code"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalHome\\GlobalHome.vue"], "sourcesContent": ["<template>\r\n  <component :is=\"elComponent[route.query.code]\"></component>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalHome' }\r\n</script>\r\n<script setup>\r\nimport { useRoute } from 'vue-router'\r\nimport { elComponent } from './elComponent'\r\nconst route = useRoute() \r\n</script>\r\n"], "mappings": ";;uBACEA,YAAA,CAA2DC,wBAD7D,CACkBC,MAAA,CAAAC,WAAW,CAACD,MAAA,CAAAE,KAAK,CAACC,KAAK,CAACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}