{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = [\"lement-loading-text\"];\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_upload = _resolveComponent(\"el-upload\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"AnswerManage\",\n    \"lement-loading-text\": $setup.loadingText\n  }, [_createVNode(_component_xyl_search_button, {\n    searchShow: false\n  }, {\n    button: _withCtx(function () {\n      return [_createVNode(_component_el_upload, {\n        action: \"/\",\n        \"before-upload\": $setup.handleFile,\n        \"http-request\": $setup.fileUpload,\n        \"show-file-list\": false,\n        multiple: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_button, {\n            type: \"primary\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[2] || (_cache[2] = [_createTextVNode(\"上传文件\")]);\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"文件名称\",\n        prop: \"fileName\"\n      }), _createVNode(_component_xyl_global_table_button, {\n        data: $setup.tableButtonList,\n        onButtonClick: $setup.handleCommand\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"total\"])])], 8 /* PROPS */, _hoisted_1)), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "$setup", "loadingText", "_createVNode", "_component_xyl_search_button", "searchShow", "button", "_withCtx", "_component_el_upload", "action", "handleFile", "fileUpload", "multiple", "default", "_component_el_button", "type", "_cache", "_createTextVNode", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "ref", "data", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "_component_el_table_column", "width", "fixed", "label", "prop", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "handleCommand", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "$event", "pageSize", "pageSizes", "layout", "onSizeChange", "handleQuery", "onCurrentChange", "total", "totals", "background", "_hoisted_1", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\QuestionsAndAnswers\\AnswerManage.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AnswerManage\" v-loading=\"loading\" :lement-loading-text=\"loadingText\">\r\n    <xyl-search-button :searchShow=\"false\">\r\n      <template #button>\r\n        <el-upload action=\"/\" :before-upload=\"handleFile\" :http-request=\"fileUpload\" :show-file-list=\"false\" multiple>\r\n          <el-button type=\"primary\">上传文件</el-button>\r\n        </el-upload>\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <el-table-column label=\"文件名称\" prop=\"fileName\" />\r\n        <xyl-global-table-button :data=\"tableButtonList\" @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AnswerManage' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { defaultPageSize, pageSizes } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst route = useRoute()\r\nconst tableButtonList = [\r\n  { id: 'view', name: '查看', width: 100, has: '' },\r\n  { id: 'del', name: '删除', width: 100, has: 'del' }\r\n]\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n\r\nconst tableRef = ref()\r\nconst totals = ref(0)\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(defaultPageSize.value)\r\nconst tableData = ref([])\r\nconst tableDataArray = ref([])\r\nconst handleTableSelect = (selection) => { tableDataArray.value = selection }\r\n\r\nonActivated(() => { handleQuery() })\r\n\r\n/**\r\n   * 限制上传附件的文件类型\r\n  */\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  const isShow = ['doc', 'docx', 'pdf'].includes(fileType)\r\n  if (!isShow) { ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!` }) }\r\n  return isShow\r\n}\r\n/**\r\n   * 上传附件请求方法\r\n  */\r\nconst fileUpload = async (file) => {\r\n  try {\r\n    loading.value = true\r\n    const param = new FormData()\r\n    param.append('files', file.file)\r\n    param.append('zskId', route.query.type)\r\n    const { data } = await api.knowledgeUpload(param)\r\n    handleQuery()\r\n    ElMessage({ type: 'success', message: data.message || '上传成功' })\r\n    loading.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nconst handleQuery = () => {\r\n  knowledgeFiles()\r\n}\r\nconst knowledgeFiles = async () => {\r\n  const { data } = await api.knowledgeFiles({ pageNo: pageNo.value, pageSize: pageSize.value, zskId: route.query.type })\r\n  tableData.value = data?.data?.list || []\r\n  totals.value = data?.data?.totalCount || 0\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'view':\r\n      handleView(row)\r\n      break\r\n    case 'del':\r\n      ElMessageBox.confirm('此操作将当前选中的知识库文件, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => { handleDel(row) }).catch(() => { ElMessage({ type: 'info', message: '已取消删除' }) })\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleView = (row) => {\r\n  window.open(row.filePdfPath)\r\n}\r\nconst handleDel = async (row) => {\r\n  const { code } = await api.knowledgeDelete({ fileId: row.id })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '删除成功' })\r\n    handleQuery()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.AnswerManage {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";iBAAA;;EASSA,KAAK,EAAC;AAAa;;EAQnBA,KAAK,EAAC;AAAkB;;;;;;;;;;wCAhB/BC,mBAAA,CAqBM;IArBDD,KAAK,EAAC,cAAc;IAAsB,qBAAmB,EAAEE,MAAA,CAAAC;MAClEC,YAAA,CAMoBC,4BAAA;IANAC,UAAU,EAAE;EAAK;IACxBC,MAAM,EAAAC,QAAA,CACf;MAAA,OAEY,CAFZJ,YAAA,CAEYK,oBAAA;QAFDC,MAAM,EAAC,GAAG;QAAE,eAAa,EAAER,MAAA,CAAAS,UAAU;QAAG,cAAY,EAAET,MAAA,CAAAU,UAAU;QAAG,gBAAc,EAAE,KAAK;QAAEC,QAAQ,EAAR;;QAJ7GC,OAAA,EAAAN,QAAA,CAKU;UAAA,OAA0C,CAA1CJ,YAAA,CAA0CW,oBAAA;YAA/BC,IAAI,EAAC;UAAS;YALnCF,OAAA,EAAAN,QAAA,CAKoC;cAAA,OAAIS,MAAA,QAAAA,MAAA,OALxCC,gBAAA,CAKoC,MAAI,E;;YALxCC,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA;MASIC,mBAAA,CAOM,OAPNC,UAOM,GANJjB,YAAA,CAKWkB,mBAAA;IALDC,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAAEC,IAAI,EAAEtB,MAAA,CAAAuB,SAAS;IAAGC,QAAM,EAAExB,MAAA,CAAAyB,iBAAiB;IAC/EC,WAAU,EAAE1B,MAAA,CAAAyB;;IAXrBb,OAAA,EAAAN,QAAA,CAYQ;MAAA,OAAuE,CAAvEJ,YAAA,CAAuEyB,0BAAA;QAAtDb,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACc,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/D3B,YAAA,CAAgDyB,0BAAA;QAA/BG,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;UACnC7B,YAAA,CAAwG8B,kCAAA;QAA9EV,IAAI,EAAEtB,MAAA,CAAAiC,eAAe;QAAGC,aAAW,EAAElC,MAAA,CAAAmC;;;IAdvElB,CAAA;iCAiBIC,mBAAA,CAIM,OAJNkB,UAIM,GAHJlC,YAAA,CAE+BmC,wBAAA;IAFRC,WAAW,EAAEtC,MAAA,CAAAuC,MAAM;IAlBhD,wBAAAxB,MAAA,QAAAA,MAAA,gBAAAyB,MAAA;MAAA,OAkB0CxC,MAAA,CAAAuC,MAAM,GAAAC,MAAA;IAAA;IAAU,WAAS,EAAExC,MAAA,CAAAyC,QAAQ;IAlB7E,qBAAA1B,MAAA,QAAAA,MAAA,gBAAAyB,MAAA;MAAA,OAkBqExC,MAAA,CAAAyC,QAAQ,GAAAD,MAAA;IAAA;IAAG,YAAU,EAAExC,MAAA,CAAA0C,SAAS;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAE5C,MAAA,CAAA6C,WAAW;IAAGC,eAAc,EAAE9C,MAAA,CAAA6C,WAAW;IACvGE,KAAK,EAAE/C,MAAA,CAAAgD,MAAM;IAAEC,UAAU,EAAV;kGApBxBC,UAAA,K,qBACuClD,MAAA,CAAAmD,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}