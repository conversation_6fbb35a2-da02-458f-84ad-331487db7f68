{"ast": null, "code": "import { ref, onMounted } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nexport default {\n  __name: 'WarningDetail',\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var router = useRouter();\n    var warningDetail = ref({\n      title: '',\n      source: '',\n      time: '',\n      content: ''\n    });\n\n    // 模拟数据，实际项目中应该从后端获取\n    var warningData = {\n      1: {\n        title: '我国集中发布一系列重大找矿成果',\n        source: '《地质调研时刊》2022年第11期',\n        time: '2024-11-12 17:07',\n        content: `新华社北京1月14日电（记者 王立彬）新发现10个亿吨级油田、19个千亿方级气田，探获10个大型以上铀矿床、铌矿资源量大幅增加——我国一系列重大找矿成果14日集中发布。\n\n自然资源部新一轮找矿\"突破战略行动\"重要成果发布会宣布，我国发现全球第一个超深生产铀矿床和大型铀矿，新增天然气\"探明地质储量超超1000亿立方米，新发现10个亿吨级油田，19个千亿方级气田，深层煤层气\"勘探开发进入突飞猛进阶段，铌矿资源量大幅增加。\n\n我国铌矿资源量大幅增加，为实施铌矿\"部分多斯等5个大型铌矿基地的资源基础，探获10个大型铌矿床：通过公益性地质调查和油气性矿产勘查市场、油勘探场，在自然动力驱动的大型铌矿\"。\n\n自然资源部地质勘查管理司司长何勇力说，我国是全球第一个超深生产铀矿床和铀矿，能源储量出现持续增涨势态突破15年以来最大突破提高，稀有油气和铌矿\"勘探\"更显不断探索发现，深层煤层气、超深水天然气等非常规油气\"实现重大突破，油气储量比例提升上升，铌矿\"资源量大幅增加，清洁能源自给力力度不断提升。\n\n同时，铜、铅、锌等大宗矿产找矿取得重要突破，为制造强国巩固定资源基础。铜、铅铁、氢气、稀土等战略新兴产产业相关矿产取得重大突破，为战略新兴领域产业链供应链安全供给了有力方向。铜、铅、铁、黄金、石膏等优势矿种资源实现较大幅增长，资源优势进一步巩固。\n\n实施新一轮找矿\"突破战略行动以来，我国深科技科技创新和技术装备设3倍，加大相关实验能力度，自然资源部成立立新一轮找矿\"突破战略行动办公室，全向有关部委、地方党委政府和企业协调联动，提高审批效率，释放制度红利。2024年，全国深供找矿探矿产勘查区块超过1400个，较近十年来新高，中央和地方财政加大投入，引领社会资金来积极投入。\"十四五\"以来累计投入找矿资金4000亿元，2023、2024年连续两年超千亿元。`\n      }\n    };\n    onMounted(function () {\n      var warningId = route.query.id;\n      console.log('当前预警ID:', warningId); // 添加日志\n      if (warningId && warningData[warningId]) {\n        warningDetail.value = warningData[warningId];\n      } else {\n        // 如果没有找到对应的数据，可以跳转到404页面或返回上一页\n        router.back();\n      }\n    });\n    var __returned__ = {\n      route,\n      router,\n      warningDetail,\n      warningData,\n      ref,\n      onMounted,\n      get useRoute() {\n        return useRoute;\n      },\n      get useRouter() {\n        return useRouter;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "useRoute", "useRouter", "route", "router", "warningDetail", "title", "source", "time", "content", "warningData", "warningId", "query", "id", "console", "log", "value", "back"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/HotspotPush/WarningDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"warning-detail\">\r\n    <!-- 面包屑导航 -->\r\n    <!-- <div class=\"breadcrumb\">\r\n      <div class=\"back\" @click=\"router.back()\">\r\n        <span>热点推送</span>\r\n        <span class=\"separator\">></span>\r\n        <span>预警信息详情</span>\r\n      </div>\r\n    </div> -->\r\n\r\n    <!-- 分隔线 -->\r\n    <div class=\"divider-line\"></div>\r\n\r\n    <!-- 内容区域 -->\r\n    <div class=\"warning-content\">\r\n      <!-- 标题区域 -->\r\n      <div class=\"title-section\">\r\n        <h1 class=\"title\">{{ warningDetail.title }}</h1>\r\n      </div>\r\n\r\n      <!-- 元信息区域 -->\r\n      <div class=\"meta\">\r\n        <div class=\"meta-left\">\r\n          <div class=\"tag\">预警</div>\r\n          <span class=\"source\">来源：{{ warningDetail.source }}</span>\r\n        </div>\r\n        <span class=\"time\">发布时间：{{ warningDetail.time }}</span>\r\n      </div>\r\n\r\n      <!-- 正文内容 -->\r\n      <div class=\"content\">\r\n        {{ warningDetail.content }}\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\n\r\nconst route = useRoute()\r\nconst router = useRouter()\r\n\r\nconst warningDetail = ref({\r\n  title: '',\r\n  source: '',\r\n  time: '',\r\n  content: ''\r\n})\r\n\r\n// 模拟数据，实际项目中应该从后端获取\r\nconst warningData = {\r\n  1: {\r\n    title: '我国集中发布一系列重大找矿成果',\r\n    source: '《地质调研时刊》2022年第11期',\r\n    time: '2024-11-12 17:07',\r\n    content: `新华社北京1月14日电（记者 王立彬）新发现10个亿吨级油田、19个千亿方级气田，探获10个大型以上铀矿床、铌矿资源量大幅增加——我国一系列重大找矿成果14日集中发布。\r\n\r\n自然资源部新一轮找矿\"突破战略行动\"重要成果发布会宣布，我国发现全球第一个超深生产铀矿床和大型铀矿，新增天然气\"探明地质储量超超1000亿立方米，新发现10个亿吨级油田，19个千亿方级气田，深层煤层气\"勘探开发进入突飞猛进阶段，铌矿资源量大幅增加。\r\n\r\n我国铌矿资源量大幅增加，为实施铌矿\"部分多斯等5个大型铌矿基地的资源基础，探获10个大型铌矿床：通过公益性地质调查和油气性矿产勘查市场、油勘探场，在自然动力驱动的大型铌矿\"。\r\n\r\n自然资源部地质勘查管理司司长何勇力说，我国是全球第一个超深生产铀矿床和铀矿，能源储量出现持续增涨势态突破15年以来最大突破提高，稀有油气和铌矿\"勘探\"更显不断探索发现，深层煤层气、超深水天然气等非常规油气\"实现重大突破，油气储量比例提升上升，铌矿\"资源量大幅增加，清洁能源自给力力度不断提升。\r\n\r\n同时，铜、铅、锌等大宗矿产找矿取得重要突破，为制造强国巩固定资源基础。铜、铅铁、氢气、稀土等战略新兴产产业相关矿产取得重大突破，为战略新兴领域产业链供应链安全供给了有力方向。铜、铅、铁、黄金、石膏等优势矿种资源实现较大幅增长，资源优势进一步巩固。\r\n\r\n实施新一轮找矿\"突破战略行动以来，我国深科技科技创新和技术装备设3倍，加大相关实验能力度，自然资源部成立立新一轮找矿\"突破战略行动办公室，全向有关部委、地方党委政府和企业协调联动，提高审批效率，释放制度红利。2024年，全国深供找矿探矿产勘查区块超过1400个，较近十年来新高，中央和地方财政加大投入，引领社会资金来积极投入。\"十四五\"以来累计投入找矿资金4000亿元，2023、2024年连续两年超千亿元。`\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  const warningId = route.query.id\r\n  console.log('当前预警ID:', warningId) // 添加日志\r\n  if (warningId && warningData[warningId]) {\r\n    warningDetail.value = warningData[warningId]\r\n  } else {\r\n    // 如果没有找到对应的数据，可以跳转到404页面或返回上一页\r\n    router.back()\r\n  }\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.warning-detail {\r\n  min-height: 100vh;\r\n  background: #fff;\r\n}\r\n\r\n.breadcrumb {\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n}\r\n\r\n.back {\r\n  color: #333;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.separator {\r\n  color: #999;\r\n}\r\n\r\n.divider-line {\r\n  height: 1px;\r\n  background-color: #E4E7ED;\r\n  /* margin: 0 20px; */\r\n}\r\n\r\n.warning-content {\r\n  /* max-width: 1200px; */\r\n  margin: 0 auto;\r\n  padding: 24px;\r\n}\r\n\r\n.title-section {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.title {\r\n  font-size: 20px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  line-height: 1.4;\r\n  display: inline-block;\r\n}\r\n\r\n.meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-bottom: 16px;\r\n  margin-bottom: 24px;\r\n  border-bottom: 1px solid #E4E7ED;\r\n}\r\n\r\n.meta-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.tag {\r\n  padding: 2px 8px;\r\n  background: #FF4B4B;\r\n  color: #fff;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.source,\r\n.time {\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n\r\n.content {\r\n  font-size: 14px;\r\n  line-height: 1.8;\r\n  color: #333;\r\n  text-align: justify;\r\n  white-space: pre-wrap;\r\n}\r\n</style>"], "mappings": "AAuCA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAK;AACpC,SAASC,QAAQ,EAAEC,SAAS,QAAQ,YAAY;;;;;;IAEhD,IAAMC,KAAK,GAAGF,QAAQ,CAAC,CAAC;IACxB,IAAMG,MAAM,GAAGF,SAAS,CAAC,CAAC;IAE1B,IAAMG,aAAa,GAAGN,GAAG,CAAC;MACxBO,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE;IACX,CAAC,CAAC;;IAEF;IACA,IAAMC,WAAW,GAAG;MAClB,CAAC,EAAE;QACDJ,KAAK,EAAE,iBAAiB;QACxBC,MAAM,EAAE,mBAAmB;QAC3BC,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE;IACF,CAAC;IAEDT,SAAS,CAAC,YAAM;MACd,IAAMW,SAAS,GAAGR,KAAK,CAACS,KAAK,CAACC,EAAE;MAChCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEJ,SAAS,CAAC,EAAC;MAClC,IAAIA,SAAS,IAAID,WAAW,CAACC,SAAS,CAAC,EAAE;QACvCN,aAAa,CAACW,KAAK,GAAGN,WAAW,CAACC,SAAS,CAAC;MAC9C,CAAC,MAAM;QACL;QACAP,MAAM,CAACa,IAAI,CAAC,CAAC;MACf;IACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}