"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[9815],{59815:function(e,o,t){t.r(o),t.d(o,{default:function(){return i}});var n=t(74061),u={name:"ChatPopupWindow"},a=Object.assign(u,{props:{modelValue:{type:Boolean,default:!1},beforeClose:Function},emits:["update:modelValue"],setup(e,o){var t=o.emit,u=e,a=t,l=(0,n.ref)(u.modelValue),i=(0,n.ref)(!1),c=(0,n.ref)(!1);(0,n.watch)((function(){return u.modelValue}),(function(){l.value=u.modelValue,u.modelValue?(i.value=!0,(0,n.nextTick)((function(){c.value=!0}))):(c.value=!1,setTimeout((function(){i.value=!1}),99))}));var r=function(){"function"===typeof u.beforeClose?u.beforeClose((function(){a("update:modelValue",!1)})):a("update:modelValue",!1)};return function(e,o){return i.value?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,class:"chat-popup-window",onClick:(0,n.withModifiers)(r,["stop"])},[(0,n.createVNode)(n.Transition,{name:"chat-popup-window-fade"},{default:(0,n.withCtx)((function(){return[c.value?((0,n.openBlock)(),(0,n.createElementBlock)("div",{key:0,class:"chat-popup-window-body forbidSelect",onClick:o[0]||(o[0]=(0,n.withModifiers)((function(){}),["stop"]))},[(0,n.renderSlot)(e.$slots,"default")])):(0,n.createCommentVNode)("",!0)]})),_:3})])):(0,n.createCommentVNode)("",!0)}}});const l=a;var i=l}}]);