{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"video-player-container\"\n};\nvar _hoisted_2 = {\n  class: \"player-container\"\n};\nvar _hoisted_3 = {\n  ref: \"videoPlayer\",\n  id: \"video-player\",\n  controls: \"\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"video\", _hoisted_3, null, 512 /* NEED_PATCH */)])]);\n}", "map": {"version": 3, "names": ["class", "ref", "id", "controls", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\components\\VideoPlayer.vue"], "sourcesContent": ["<template>\n  <div class=\"video-player-container\">\n    <div class=\"player-container\">\n      <video ref=\"videoPlayer\" id=\"video-player\" controls></video>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default { name: 'VideoPlayer' }\n</script>\n\n<script setup>\nimport { ref, onBeforeUnmount, onMounted, nextTick, watch } from 'vue'\nimport Hls from 'hls.js'\n\nconst props = defineProps({\n  liveUrl: {\n    type: String,\n    default: ''\n  },\n  replayUrl: {\n    type: String,\n    default: ''\n  },\n  isReplay: {\n    type: Boolean,\n    default: false\n  },\n  autoInit: {\n    type: Boolean,\n    default: true\n  }\n})\n\n// 视频播放器相关\nconst videoPlayer = ref(null)\nconst player = ref(null)\nconst hls = ref(null)\nconst isPlayerInitialized = ref(false)\n\n// 初始化视频播放器\nconst initVideoPlayer = async () => {\n  console.log('initVideoPlayer: 开始初始化直播播放器')\n  console.log('initVideoPlayer: isPlayerInitialized =', isPlayerInitialized.value)\n  console.log('initVideoPlayer: videoPlayer.value =', !!videoPlayer.value)\n\n  if (!videoPlayer.value) {\n    console.log('initVideoPlayer: video元素不存在，跳过初始化')\n    return\n  }\n\n  // 销毁现有播放器\n  destroyVideoPlayer()\n\n  const video = videoPlayer.value\n  player.value = video\n  isPlayerInitialized.value = true\n\n  console.log('initVideoPlayer: 原始直播URL:', props.liveUrl)\n\n  // HLS视频流地址 - 使用动态的liveUrl\n  const hlsUrl = getHlsUrl(props.liveUrl)\n\n  console.log('initVideoPlayer: 解析后的直播URL:', hlsUrl)\n\n  if (!hlsUrl) {\n    console.warn('initVideoPlayer: 没有推流地址')\n    return\n  }\n\n  // 检查浏览器是否原生支持HLS\n  if (video.canPlayType('application/vnd.apple.mpegurl')) {\n    // 原生支持HLS\n    video.src = hlsUrl\n    setupVideoEvents()\n  } else if (Hls.isSupported()) {\n    // 使用HLS.js库\n    hls.value = new Hls({\n      maxBufferLength: 30,\n      maxMaxBufferLength: 60,\n      startLevel: -1, // 自动选择适合的初始清晰度\n      maxBufferHole: 0.5,\n      highLatencyMode: false\n    })\n\n    // 加载视频流\n    hls.value.loadSource(hlsUrl)\n    hls.value.attachMedia(video)\n\n    // HLS事件监听\n    hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\n      console.log('视频流准备就绪，点击播放按钮开始')\n    })\n\n    // 错误处理\n    hls.value.on(Hls.Events.ERROR, function (_, data) {\n      console.error('HLS错误:', data)\n      switch (data.type) {\n        case Hls.ErrorTypes.NETWORK_ERROR:\n          hls.value.startLoad() // 尝试重新加载\n          break\n        case Hls.ErrorTypes.MEDIA_ERROR:\n          hls.value.recoverMediaError() // 尝试恢复媒体错误\n          break\n        default:\n          // 无法恢复的错误，尝试重新初始化\n          setTimeout(initVideoPlayer, 3000)\n          break\n      }\n    })\n\n    setupVideoEvents()\n  }\n}\n\n// 初始化回放播放器\nconst initReplayPlayer = async () => {\n  console.log('initReplayPlayer: 开始初始化回放播放器')\n  console.log('initReplayPlayer: isPlayerInitialized =', isPlayerInitialized.value)\n  console.log('initReplayPlayer: videoPlayer.value =', !!videoPlayer.value)\n\n  if (!videoPlayer.value) {\n    console.log('initReplayPlayer: video元素不存在，跳过初始化')\n    return\n  }\n\n  // 销毁现有播放器\n  destroyVideoPlayer()\n\n  const video = videoPlayer.value\n  player.value = video\n  isPlayerInitialized.value = true\n\n  console.log('initReplayPlayer: 原始回放URL:', props.replayUrl)\n\n  // 使用回放地址，也需要解析JSON格式\n  const replayUrl = getHlsUrl(props.replayUrl)\n\n  console.log('initReplayPlayer: 解析后的回放URL:', replayUrl)\n\n  if (!replayUrl) {\n    console.warn('initReplayPlayer: 没有回放地址')\n    return\n  }\n\n  console.log('initReplayPlayer: 开始播放回放:', replayUrl)\n\n  // 检查是否是HLS格式\n  if (replayUrl.includes('.m3u8') || replayUrl.includes('hls')) {\n    // HLS回放\n    if (video.canPlayType('application/vnd.apple.mpegurl')) {\n      // 原生支持HLS\n      video.src = replayUrl\n      setupVideoEvents()\n    } else if (Hls.isSupported()) {\n      // 使用HLS.js库\n      hls.value = new Hls({\n        maxBufferLength: 30,\n        maxMaxBufferLength: 60,\n        startLevel: -1,\n        maxBufferHole: 0.5,\n        highLatencyMode: false\n      })\n\n      hls.value.loadSource(replayUrl)\n      hls.value.attachMedia(video)\n\n      hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\n        console.log('回放视频准备就绪')\n      })\n\n      hls.value.on(Hls.Events.ERROR, function (_, data) {\n        console.error('HLS回放错误:', data)\n      })\n\n      setupVideoEvents()\n    }\n  } else {\n    // 普通视频格式\n    video.src = replayUrl\n    setupVideoEvents()\n  }\n}\n\n// 设置视频事件监听\nconst setupVideoEvents = () => {\n  console.log('setupVideoEvents: 开始设置视频事件监听')\n  const video = player.value\n  if (!video) {\n    console.log('setupVideoEvents: player.value不存在')\n    return\n  }\n\n  console.log('setupVideoEvents: 添加事件监听器')\n\n  // 视频可以播放时\n  video.addEventListener('canplay', function () {\n    console.log('视频准备就绪，点击播放按钮开始')\n  })\n\n  // 播放事件\n  video.addEventListener('play', function () {\n    console.log('正在播放HLS视频流')\n  })\n\n  // 暂停事件\n  video.addEventListener('pause', function () {\n    console.log('HLS视频流已暂停')\n  })\n\n  // 视频结束事件\n  video.addEventListener('ended', function () {\n    console.log('视频播放已结束')\n  })\n\n  // 音量变化事件\n  video.addEventListener('volumechange', function () {\n    console.log('音量变化')\n  })\n\n  // 添加错误事件监听\n  video.addEventListener('error', function (e) {\n    console.error('视频播放错误:', e)\n  })\n\n  // 添加加载开始事件\n  video.addEventListener('loadstart', function () {\n    console.log('开始加载视频')\n  })\n\n  // 添加元数据加载完成事件\n  video.addEventListener('loadedmetadata', function () {\n    console.log('视频元数据加载完成')\n  })\n}\n\n// 销毁视频播放器（包括直播和回放播放器）\nconst destroyVideoPlayer = () => {\n  console.log('destroyVideoPlayer: 开始销毁播放器')\n\n  // 销毁 hls.js 实例（直播和回放都可能使用）\n  if (hls.value) {\n    try {\n      console.log('destroyVideoPlayer: 销毁HLS实例')\n      hls.value.destroy()\n    } catch (error) {\n      console.error('销毁HLS实例错误:', error)\n    }\n    hls.value = null\n  }\n\n  // 销毁video元素播放器（直播和回放共用同一个video元素）\n  if (player.value) {\n    try {\n      console.log('destroyVideoPlayer: 清理player引用')\n      player.value.pause() // 停止播放\n      player.value.currentTime = 0 // 重置播放时间\n      player.value.src = '' // 清空视频源\n      player.value.load() // 重新加载空的video元素\n      player.value.muted = true // 静音\n    } catch (error) {\n      console.error('销毁播放器错误:', error)\n    }\n    player.value = null\n    isPlayerInitialized.value = false\n  }\n\n  // 确保video元素也被清理\n  if (videoPlayer.value) {\n    try {\n      videoPlayer.value.pause()\n      videoPlayer.value.currentTime = 0\n      videoPlayer.value.src = ''\n      videoPlayer.value.load()\n      videoPlayer.value.muted = true\n    } catch (error) {\n      console.error('清理video元素错误:', error)\n    }\n  }\n\n  console.log('视频播放器已完全销毁')\n}\n\n// 从推流地址中获取HLS地址\nconst getHlsUrl = (liveUrl) => {\n  if (!liveUrl) return null\n\n  console.log('原始推流地址:', liveUrl)\n\n  // 如果liveUrl是JSON格式，解析出HLS地址\n  try {\n    const urlData = JSON.parse(liveUrl)\n    console.log('解析的JSON数据:', urlData)\n    const hlsUrl = urlData.hls || urlData.m3u8 || liveUrl\n    console.log('提取的HLS地址:', hlsUrl)\n    return hlsUrl\n  } catch (error) {\n    console.log('不是JSON格式，直接使用原地址')\n    // 如果不是JSON格式，直接返回原地址\n    return liveUrl\n  }\n}\n\n// 监听回放状态变化\nwatch(() => props.isReplay, (isReplay) => {\n  console.log('VideoPlayer: isReplay状态变化为:', isReplay)\n  nextTick(() => {\n    initPlayer()\n  })\n})\n\n// 监听URL变化\nwatch(() => [props.liveUrl, props.replayUrl], ([newLiveUrl, newReplayUrl]) => {\n  console.log('VideoPlayer: URL变化检测')\n  console.log('VideoPlayer: 新的直播URL:', newLiveUrl)\n  console.log('VideoPlayer: 新的回放URL:', newReplayUrl)\n\n  if (props.autoInit) {\n    console.log('VideoPlayer: URL变化，重新初始化播放器')\n    nextTick(() => {\n      initPlayer()\n    })\n  }\n})\n\n// 统一的初始化方法\nconst initPlayer = () => {\n  console.log('VideoPlayer: 开始初始化播放器')\n  console.log('VideoPlayer: isReplay =', props.isReplay)\n  console.log('VideoPlayer: liveUrl =', props.liveUrl)\n  console.log('VideoPlayer: replayUrl =', props.replayUrl)\n\n  // 强制重置状态，确保每次初始化都是干净的\n  console.log('VideoPlayer: 重置播放器状态')\n  isPlayerInitialized.value = false\n\n  if (props.isReplay) {\n    console.log('VideoPlayer: 初始化回放播放器')\n    initReplayPlayer()\n  } else {\n    console.log('VideoPlayer: 初始化直播播放器')\n    initVideoPlayer()\n  }\n}\n\nonMounted(() => {\n  console.log('VideoPlayer: 组件已挂载')\n  // 组件挂载后自动初始化播放器\n  if (props.autoInit) {\n    nextTick(() => {\n      initPlayer()\n    })\n  }\n})\n\nonBeforeUnmount(() => {\n  destroyVideoPlayer()\n})\n\n// 暴露方法给父组件\ndefineExpose({\n  initVideoPlayer,\n  initReplayPlayer,\n  destroyVideoPlayer,\n  initPlayer\n})\n</script>\n\n<style lang=\"scss\" scoped>\n.video-player-container {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n\n  .player-container {\n    width: 100%;\n    height: 100%;\n    position: relative;\n\n    #video-player {\n      width: 100%;\n      height: 100%;\n      position: relative;\n      z-index: 2;\n      pointer-events: auto;\n\n      &::-webkit-media-controls {\n        z-index: 3;\n      }\n\n      &::-webkit-media-controls-panel {\n        z-index: 3;\n      }\n\n      &::-webkit-media-controls-play-button {\n        z-index: 3;\n      }\n\n      &::-webkit-media-controls-timeline {\n        z-index: 3;\n      }\n\n      &::-webkit-media-controls-volume-slider {\n        z-index: 3;\n      }\n\n      &::-webkit-media-controls-mute-button {\n        z-index: 3;\n      }\n\n      &::-webkit-media-controls-fullscreen-button {\n        z-index: 3;\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAkB;;EACpBC,GAAG,EAAC,aAAa;EAACC,EAAE,EAAC,cAAc;EAACC,QAAQ,EAAR;;;uBAF/CC,mBAAA,CAIM,OAJNC,UAIM,GAHJC,mBAAA,CAEM,OAFNC,UAEM,GADJD,mBAAA,CAA4D,SAA5DE,UAA4D,8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}