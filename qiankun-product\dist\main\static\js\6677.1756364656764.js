"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[6677],{26677:function(e,t,n){n.r(t),n.d(t,{default:function(){return I}});var l=n(81474),r=(n(76945),n(64352),n(84098)),a=(n(63584),n(13776)),o=(n(77213),n(50859)),i=(n(99854),n(44863)),u=(n(4711),n(74061)),c=n(41193),s=n(43955);function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){d(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t,n){return(t=m(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=p(e,"string");return"symbol"==typeof t?t:t+""}function p(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,t||"default");if("object"!=typeof l)return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var x={class:"GlobalChatEditor"},h={class:"GlobalChatViewControls"},g=["innerHTML"],k={class:"GlobalChatEmotion"},y=["onClick"],C=["innerHTML"],b=["innerHTML"],w=["innerHTML"],E={class:"GlobalChatViewControlsBotton"},V={class:"GlobalChatUserPickerList"},S=["onClick"],O={__name:"GlobalChatEditor",props:{isVote:{type:Boolean,default:!1},userData:{type:Array,default:function(){return[]}}},emits:["handleFile","handleVote","handlePasteImg","handleSendMessage"],setup(e,t){var n=t.expose,v=t.emit,d=e,m=v,p=(0,u.ref)(),O=(0,u.ref)(""),N=(0,u.ref)(!1),I=(0,u.ref)(""),B=(0,u.ref)(0),D=(0,u.ref)({top:"0px",left:"0px"}),L=(0,u.ref)([]),G=0,P=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,n="x"==e?t:3&t|8;return n.toString(16)}))},j=(0,u.computed)((function(){return I.value?d.userData.filter((function(e){var t,n;return null===(t=e.userName)||void 0===t||null===(t=t.toLowerCase())||void 0===t?void 0:t.includes(null===(n=I.value)||void 0===n?void 0:n.toLowerCase())})):d.userData})),M=function(e){return L.value.some((function(t){return e>=t.start&&e<=t.end+1}))},T=function(){var e=navigator.userAgent.toLowerCase();return(null===e||void 0===e?void 0:e.includes("macintosh"))||(null===e||void 0===e?void 0:e.includes("mac os x"))},$=function(e){m("handleFile",e)},_=function(){m("handleVote")},H=function(){N.value=!1,I.value=""},A=function(){if(p.value){var e=p.value.$el.querySelector("textarea"),t=e.value,n=t.lastIndexOf("@");if(-1!==n){var l=e.getBoundingClientRect(),r=window.getComputedStyle(e),a=parseInt(r.lineHeight),o=parseInt(r.paddingTop),i=parseInt(r.paddingLeft),u=t.substring(0,n),c=u.split("\n"),s=c.length-1,v=c[s],f=v?v.length:0,d=l.top+o+s*a,m=l.left+i+8*f,x=32,h=220,g=Math.min(j.value.length*x,h)+14;D.value={top:d-g-5+"px",left:`${m}px`,height:`${g}px`,maxHeight:"none"}}}},q=function(e){if(d.userData.length){var t=p.value.$el.querySelector("textarea"),n=t.selectionStart;if(M(n))O.value=e;else{var l=e.slice(-1);if("@"===l)N.value=!0,I.value="",(0,u.nextTick)((function(){A()}));else if(N.value){var r=e.lastIndexOf("@");-1!==r?(I.value=e.slice(r+1),(0,u.nextTick)((function(){A()}))):N.value=!1}F(e)}}},F=function(e){var t=[],n=0,l=function(){var l=e.indexOf("@",n);if(-1===l)return 1;var r=l+1;while(r<e.length&&" "!==e[r])r++;if(r>l+1){var a=e.slice(l,r),o=L.value.find((function(e){return e.text===a&&!t.some((function(t){return t.id===e.id}))}));o&&t.push(f(f({},o),{},{start:l,end:r-1}))}n=r+1};while(n<e.length)if(l())break;L.value=t},R=function(e){var t=p.value.$el.querySelector("textarea"),n=t.selectionStart;if(!M(n))if(O.value){var l=O.value.slice(0,B.value),r=O.value.slice(B.value);O.value=`${l}${e.text}${r}`,(0,u.nextTick)((function(){p.value.focus(),p.value.$el.querySelector("textarea")&&p.value.$el.querySelector("textarea").setSelectionRange(B.value+e.text.length,B.value+e.text.length)}))}else O.value=e.text},U=function(e){B.value=e.srcElement.selectionStart},K=function(e){if(13==e.keyCode)e.ctrlKey||e.metaKey?(B.value=e.srcElement.selectionStart,R({text:"\n"})):(e.preventDefault(),X());else{var t=p.value.$el.querySelector("textarea"),n=t.selectionStart;if("Backspace"===e.key)for(var l=L.value.length-1;l>=0;l--){var r=L.value[l];if(n>r.start&&n<=r.end+1){e.preventDefault();var a=O.value.slice(0,r.start),o=O.value.slice(r.end+1);O.value=a+o,L.value.splice(l,1);for(var i=r.end-r.start+2,u=l;u<L.value.length;u++)L.value[u].start-=i,L.value[u].end-=i;break}}else"ArrowLeft"!==e.key&&"ArrowRight"!==e.key&&"ArrowUp"!==e.key&&"ArrowDown"!==e.key&&M(n)&&e.preventDefault()}},z=function(e){var t=event.clipboardData||window.clipboardData;if(t)for(var n=function(){var e,n=t.items[l];if("file"===n.kind&&null!==(e=n.type)&&void 0!==e&&e.includes("image/")){var r=n.getAsFile(),a=new FileReader;a.onload=function(e){m("handlePasteImg",{id:P(),url:e.target.result,file:r})},a.readAsDataURL(r)}},l=0;l<t.items.length;l++)n();else{var r=p.value.$el.querySelector("textarea"),a=r.selectionStart;M(a)&&e.preventDefault()}},W=function(e){var t=O.value.lastIndexOf("@");if(-1!==t){var n=`@${e.userName} `,l=O.value.slice(0,t)+n+O.value.slice(t+1+I.value.length);N.value=!1,I.value="",O.value=l,L.value.push({id:++G,text:n.trim(),start:t,end:t+n.length-2,userInfo:e})}},J=function(){return O.value.trim()?{content:O.value,mentions:L.value.map((function(e){return{id:e.id,userInfo:e.userInfo}}))}:null},Q=function(){O.value="",L.value=[],G=0},X=function(){var e=J();e&&(m("handleSendMessage",e),Q())},Y={mounted(e,t){e._clickOutside=function(n){e===n.target||e.contains(n.target)||t.value(n)},document.addEventListener("click",e._clickOutside)},unmounted(e){document.removeEventListener("click",e._clickOutside)}};return n({getMessage:J,clearMessage:Q}),function(e,t){var n=i.kA,v=o.Vc,f=a.j5,m=r.WK,I=l.S2;return(0,u.openBlock)(),(0,u.createElementBlock)("div",x,[(0,u.createElementVNode)("div",h,[(0,u.createVNode)(v,{placement:"top",trigger:"click","popper-class":"GlobalChatEmotionPopover"},{reference:(0,u.withCtx)((function(){return[(0,u.createElementVNode)("div",{class:"GlobalChatViewControlsItem",innerHTML:(0,u.unref)(s.Cx),title:"表情"},null,8,g)]})),default:(0,u.withCtx)((function(){return[(0,u.createVNode)(n,{class:"GlobalChatEmotionScroll"},{default:(0,u.withCtx)((function(){return[(0,u.createElementVNode)("div",k,[((0,u.openBlock)(!0),(0,u.createElementBlock)(u.Fragment,null,(0,u.renderList)((0,u.unref)(c.W),(function(e){return(0,u.openBlock)(),(0,u.createElementBlock)("div",{class:"GlobalChatEmotionItem",key:e.name,onClick:function(t){return R(e)}},[(0,u.createElementVNode)("div",{class:(0,u.normalizeClass)(e.name)},null,2)],8,y)})),128)),t[2]||(t[2]=(0,u.createElementVNode)("div",{class:"GlobalChatEmotionItem"},null,-1)),t[3]||(t[3]=(0,u.createElementVNode)("div",{class:"GlobalChatEmotionItem"},null,-1)),t[4]||(t[4]=(0,u.createElementVNode)("div",{class:"GlobalChatEmotionItem"},null,-1))])]})),_:1})]})),_:1}),(0,u.createVNode)(f,{action:"/","http-request":$,"show-file-list":!1,multiple:""},{default:(0,u.withCtx)((function(){return[(0,u.createElementVNode)("div",{class:"GlobalChatViewControlsItem",innerHTML:(0,u.unref)(s.Eh),title:"文件"},null,8,C)]})),_:1}),d.isVote?((0,u.openBlock)(),(0,u.createElementBlock)("div",{key:0,class:"GlobalChatViewControlsItem",innerHTML:(0,u.unref)(s.W),title:"投票",onClick:_},null,8,b)):(0,u.createCommentVNode)("",!0),(0,u.createElementVNode)("div",{class:"GlobalChatViewControlsItem is-min",innerHTML:(0,u.unref)(s._B),title:"换行",onClick:t[0]||(t[0]=function(e){return R({text:"\n"})})},null,8,w)]),(0,u.createVNode)(m,{ref_key:"inputRef",ref:p,modelValue:O.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return O.value=e}),type:"textarea",resize:"none",rows:4,onInput:q,onKeydown:K,onPaste:z,onBlur:U},null,8,["modelValue"]),(0,u.createElementVNode)("div",E,[(0,u.createElementVNode)("span",null,"Enter 发送 ｜ "+(0,u.toDisplayString)(T()?"Command + Enter 换行":"Ctrl + Enter 换行"),1),(0,u.createVNode)(I,{type:"primary",onClick:X},{default:(0,u.withCtx)((function(){return t[5]||(t[5]=[(0,u.createTextVNode)("发送")])})),_:1})]),(0,u.withDirectives)(((0,u.openBlock)(),(0,u.createBlock)(n,{class:"GlobalChatUserPicker",style:(0,u.normalizeStyle)(D.value)},{default:(0,u.withCtx)((function(){return[(0,u.createElementVNode)("div",V,[((0,u.openBlock)(!0),(0,u.createElementBlock)(u.Fragment,null,(0,u.renderList)(j.value,(function(e){return(0,u.openBlock)(),(0,u.createElementBlock)("div",{class:"GlobalChatUserPickerItem",key:e.id,onClick:function(t){return W(e)}},(0,u.toDisplayString)(e.userName),9,S)})),128))])]})),_:1},8,["style"])),[[u.vShow,N.value],[Y,H]])])}}};const N=O;var I=N}}]);