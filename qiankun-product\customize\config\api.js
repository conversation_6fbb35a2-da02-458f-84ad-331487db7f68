// 导入封装的方法
import HTTP from 'common/http'
const customizeApi = {
  loginImg (params) {
    return HTTP.json('/wind/runner/loginImg', params)
  },
  loginImgRegion (params, areaId) {
    // 登录获取用户信息
    return HTTP.json('/wind/runner/loginImg', params, { areaId: areaId })
  },
  readOpenConfigRegion (params, areaId) {
    // 通用公开取配置接口
    return HTTP.json('/config/openRead', params, { areaId: areaId })
  },
  miduDataScribePoll (params) { // 预警接口
    return HTTP.json('/miduData/scribe/poll', params)
  },
  miduDataTopicList (params) { // 榜单列表接口
    return HTTP.json('/miduData/topic/list', params)
  },
  miduDataTopicDetail (params) { // 榜单列表接口
    return HTTP.json('/miduData/topic/detail', params)
  },
  aigptChatSceneDetail (params) { // AI智能场景管理
    return HTTP.json('/aigptChatScene/detail', params)
  },
  aigptChatInitList (params) { // AI智能场景提示语
    return HTTP.json('/aigptChatInit/list', params)
  },
  aigptChatToolsList (params) { // AI智能场景工具管理
    return HTTP.json('/aigptChatTools/list', params)
  },
  aigptChatClusterList (params) { // AI智能场景会话列表
    return HTTP.json('/aigptChatCluster/list', params)
  },
  aigptChatLogsList (params) { // AI智能场景历史会话记录
    return HTTP.json('/aigptChatLogs/list', params)
  },
  aiScheduleExists (params) { // 判断当天是否有会客
    return HTTP.json('/aiSchedule/exists', params)
  },
  aigptReportRecordTypeList (params) { // 智能报告记录
    return HTTP.json('/aigptReportRecord/typeList', params)
  },
  aigptReportRecordList (params) { // 智能报告记录
    return HTTP.json('/aigptReportRecord/list', params)
  },
  aigptReportRecordInfo (params) { // 智能报告记录
    return HTTP.json('/aigptReportRecord/info', params)
  },
  aigptReportRecordDel (params) { // 智能报告记录
    return HTTP.json('/aigptReportRecord/dels', params)
  },
  loginLastAreaId (params) { // 通过编号获取首页数据
    return HTTP.json('/login/lastAreaId', params)
  },
  homePageDocument (params) { // 通过编号获取首页数据
    return HTTP.json('/homepage/load', params)
  },
  noticeHomePage (params) { // 首页公共数据
    return HTTP.json('/notification/list', params)
  },
  newsContentInfo (params) { // 资讯详情数据
    return HTTP.json('/newsContent/info', params)
  },
  newsContentInfo (params) { // 资讯详情数据
    return HTTP.json('/newsContent/info', params)
  },
  NoticeAnnouncementInfo (params) { // 通知公告详情
    return HTTP.json('/notification/info', params)
  },
  reqPersonSuggestionAdd (params) { // 新增个人帮助建议
    return HTTP.json('/personSuggestion/add', params)
  },
  reqPersonSuggestion (params) { // 历史反馈建议
    return HTTP.json('/personSuggestion/list', params)
  },
  reqRoleHelpNote (params) { // 帮助文档
    return HTTP.json('/roleHelpNote/list', params)
  },
  userInfoByAccount (params) { // 获取用户信息
    return HTTP.json('/user/infoByAccount', params, { noErrorTip: true })
  },
  userInfo (params) { // 获取用户信息
    return HTTP.json('/user/info', params, { noErrorTip: true })
  },
  chatGroupInfo (params) { // 获取群组信息
    return HTTP.json('/chatGroup/info', params, { noErrorTip: true })
  },
  chatGroupAdd (params) { // 群组新增
    return HTTP.json('/chatGroup/add', params, { terminal: 'APP' })
  },
  chatGroupEdit (params) { // 群组编辑
    return HTTP.json('/chatGroup/edit', params, { terminal: 'APP' })
  },
  chatGroupDel (params) { // 群组编辑
    return HTTP.json('/chatGroup/dels', params, { terminal: 'APP' })
  },
  chatGroupList (params) { // 获取群组列表
    return HTTP.json('/chatGroup/list', params, { terminal: 'APP' })
  },
  chatGroupMemberList (params) { // 获取群组人员列表
    return HTTP.json('/chatGroupMember/list', params)
  },
  chatGroupFileAdd (params) { // 获取群组人员列表
    return HTTP.json('/chatGroupFile/add', params)
  },
  VoteList (params) { // 投票列表
    return HTTP.json('/voteTopic/list', params)
  },
  VoteInfo (params) { // 投票详情
    return HTTP.json('/voteTopic/info', params)
  },
  VoteDel (params) { // 投票删除
    return HTTP.json('/voteTopic/dels', params)
  },
  VoteCount (params) { // 投票统计
    return HTTP.json('/voteTopic/count', params)
  },
  VoteUserList (params) { // 投票统计
    return HTTP.json('/voteTopicOptionUser/list', params)
  },
  relationBookMemberOftenList (params) { // 获取常用人员列表
    return HTTP.json('/relationBookMember/oftenList', params)
  },
  relationBookMemberSetOften (params) { // 设置常用人员列表
    return HTTP.json('/relationBookMember/setOften', params)
  },
  openClueBankHot (params) { // 对外开放-人大政协线索库热榜分析接口
    return HTTP.json('/open/clueBankHot', params)
  },
  openClueBankLabel (params) { // 对外开放-人大政协线索库类别热点分析接口
    return HTTP.json('/open/clueBankLabel', params)
  },
  openSmartSearch (params) { // 对外开放-人大政协线索库检索接口
    return HTTP.json('/open/smartSearch', params)
  },
  publicOpinionMonitoring (params) { // 对外开放-舆情监督接口
    return HTTP.json('/open/publicOpinionMonitoring', params)
  },
  publicChartAnalysis (params) { // 对外开放-舆情监督统计图表
    return HTTP.json('/open/publicChartAnalysis', params)
  },
  faceDetect (params) { // 法眼-获取人脸识别url
    return HTTP.json('/face/faceDetect', params)
  },
  faceResult (params) { // 法眼-获取人脸识别结果
    return HTTP.json('/face/faceResult', params)
  },
  lawsuitResult (params) { // 法眼-获取涉诉结果
    return HTTP.json('/face/lawsuitResult', params)
  },
  backReview (params) { // 法眼-获取背景审查
    return HTTP.json('/face/backReview', params)
  },
  employRisks (params) { // 法眼-获取用户风险
    return HTTP.json('/face/employRisks', params)
  },
  faceHistory (params) { // 法眼-获取历史记录
    return HTTP.json('/face/history', params)
  },
  knowledgeQuiz (params) { // 对外开放-智能生成知识问答接口
    return HTTP.json('/open/knowledgeQuiz', params)
  },
  knowledgeFiles (params) { // 对外开放-知识库文件上传
    return HTTP.json('/open/knowledgeFiles', params)
  },
  knowledgeUpload (params) { // 对外开放-知识库文件上传
    return HTTP.fileUpload('/open/knowledgeUpload', params, () => { })
  },
  knowledgeDelete (params) { // 对外开放-智能生成知识库文件删除接口
    return HTTP.json('/open/knowledgeDelete', params)
  },
  // 智能生成摘要 接口
  chatIntelligentAnswer (params) { // 智能摘要生成接口———chat工程
    // , { responseType: 'stream' }
    return HTTP.json('/chat/IntelligentAnswer', params)
  },
  wordApiWordToHtml (params) { // word转html
    return HTTP.fileUpload('/wordApi/wordToHtml', params, () => { })
  },
  wordApiPaintedWord (params) { // 版本对比-花脸稿接口
    return HTTP.fileUpload('/wordApi/paintedWord', params, () => { })
  },
  wordApiContrastWord (params) { // 版本对比-对照表接口
    return HTTP.fileUpload('/wordApi/textContrastWord', params, () => { })
  },
  hadoopLawseesList (params) { // 法规检索
    return HTTP.json('/hadoop_api/datax/lawsees/list', params)
  },
  hadoopLawseesInfo (params) { // 法规详情
    return HTTP.json('/hadoop_api/datax/lawsees/info', params)
  },
  hadoopLawseesEffecLevel (params) { // 效力级别统计
    return HTTP.json('/hadoop_api/datax/lawsees/effecLevel', params)
  },
  hadoopLawseesTimeLiness (params) { // 时效性统计
    return HTTP.json('/hadoop_api/datax/lawsees/timeLiness', params)
  },
  hadoopLawseesPublishOffice (params) { // 发布机构(地区)
    return HTTP.json('/hadoop_api/datax/lawsees/publishOffice', params)
  },
  initMessage (params) { // 发布机构(地区)
    return HTTP.json('/chat/questionLists', params)
  },
  filewordhtml (params, callback, id) {
    // word转html
    return HTTP.fileUpload('/file/word2html', params, callback, id)
  },
  wordApiTextComparison (params) { // 智能工具版本对比
    return HTTP.fileUpload('/wordApi/textComparison', params, () => { })
  },
  newsContentList (params) { // 首页资讯
    return HTTP.json('/newsContent/list', params)
  },
  // newsColumnList (params) { // 首页资讯栏目
  //   return HTTP.json('/newsColumn/list', params)
  // },
  newsColumnList (params) { // 首页资讯栏目
    return HTTP.json('/newsColumn/app/list', params)
  },
  userOrganizationList (params) { // 获取用户可访问的组织列表
    return HTTP.json('/uniter/org/manager/find/login', params)
  },
  setUserOrganization (params) { // 设置用户当前组织
    return HTTP.json('/uniterOrganize/list', params)
  },
  aigptChatLogsList (params) { // 对话记录
    return HTTP.json('/aigptStatistics/recentList', params)
  },
  hotWordPlusList (params) { // 加强版热词列表
    return HTTP.json('/hotWordPlus/list', params)
  },
  hotWordPlusInfo (params) { // 加强版热词详情
    return HTTP.json('/hotWordPlus/info', params)
  },
  hotWordPlusDels (params) { // 加强版热词删除
    return HTTP.json('/hotWordPlus/dels', params)
  },
  hotWordPlusEdit (params) { // 加强版热词编辑
    return HTTP.json('/hotWordPlus/edit', params)
  },
  hotWordPlusSignNeutral (params) { // 标记中性词
    return HTTP.json('/hotWordPlus/signNeutral', params)
  },
  hotWordPlusSignShow (params) { // 批显示
    return HTTP.json('/hotWordPlus/signShow', params)
  },
}
export default customizeApi
