{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives, withModifiers as _withModifiers } from \"vue\";\nvar _hoisted_1 = {\n  key: 0,\n  class: \"GlobalLayoutChatButton\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalChatBody\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalChatBody\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalChatBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"GlobalLayoutChat\",\n    onContextmenu: _cache[2] || (_cache[2] = _withModifiers(function () {}, [\"prevent\"]))\n  }, [!$setup.isMac ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n    class: \"GlobalLayoutChatMinimize\",\n    title: \"最小化\",\n    onClick: $setup.handleMinimize\n  }), !$setup.ifMax ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"GlobalLayoutChatMaximize\",\n    title: \"最大化\",\n    onClick: $setup.handleMaximize\n  })) : _createCommentVNode(\"v-if\", true), $setup.ifMax ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"GlobalLayoutChatUnmaximize\",\n    title: \"向下还原\",\n    onClick: $setup.handleUnmaximize\n  })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    class: \"GlobalLayoutChatClose\",\n    title: \"关闭\",\n    onClick: $setup.handleClose\n  })])) : _createCommentVNode(\"v-if\", true), _createVNode($setup[\"GlobalChatNav\"], {\n    modelValue: $setup.navId,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.navId = $event;\n    }),\n    chatTotal: $setup.chatTotal,\n    exit: \"\",\n    onChange: $setup.handleChange\n  }, null, 8 /* PROPS */, [\"modelValue\", \"chatTotal\"]), _withDirectives(_createElementVNode(\"div\", _hoisted_2, [_createVNode($setup[\"GlobalChatView\"], {\n    ref: \"chatViewRef\",\n    modelValue: $setup.chatId,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.chatId = $event;\n    }),\n    chatList: $setup.chatList,\n    onTime: $setup.handleTime,\n    onRefresh: $setup.handleRefresh,\n    onSend: $setup.handleSend\n  }, null, 8 /* PROPS */, [\"modelValue\", \"chatList\"])], 512 /* NEED_PATCH */), [[_vShow, $setup.navId === '1']]), _withDirectives(_createElementVNode(\"div\", _hoisted_3, [_createVNode($setup[\"GlobalChatAddressBook\"], {\n    ref: \"addressBookRef\",\n    onSend: $setup.handleSend\n  }, null, 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */), [[_vShow, $setup.navId === '2']]), _withDirectives(_createElementVNode(\"div\", _hoisted_4, [_createVNode($setup[\"GlobalChatGroup\"], {\n    ref: \"groupRef\",\n    onSend: $setup.handleSend\n  }, null, 512 /* NEED_PATCH */)], 512 /* NEED_PATCH */), [[_vShow, $setup.navId === '3']])], 32 /* NEED_HYDRATION */);\n}", "map": {"version": 3, "names": ["key", "class", "_createElementBlock", "onContextmenu", "_cache", "_withModifiers", "$setup", "isMac", "_hoisted_1", "_createElementVNode", "title", "onClick", "handleMinimize", "ifMax", "handleMaximize", "_createCommentVNode", "handleUnmaximize", "handleClose", "_createVNode", "modelValue", "navId", "$event", "chatTotal", "exit", "onChange", "handleChange", "_hoisted_2", "ref", "chatId", "chatList", "onTime", "handleTime", "onRefresh", "handleRefresh", "onSend", "handleSend", "_hoisted_3", "_hoisted_4"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\GlobalLayoutChat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalLayoutChat\" @contextmenu.prevent>\r\n    <div class=\"GlobalLayoutChatButton\" v-if=\"!isMac\">\r\n      <div class=\"GlobalLayoutChatMinimize\" title=\"最小化\" @click=\"handleMinimize\"></div>\r\n      <div class=\"GlobalLayoutChatMaximize\" title=\"最大化\" @click=\"handleMaximize\" v-if=\"!ifMax\"></div>\r\n      <div class=\"GlobalLayoutChatUnmaximize\" title=\"向下还原\" @click=\"handleUnmaximize\" v-if=\"ifMax\"></div>\r\n      <div class=\"GlobalLayoutChatClose\" title=\"关闭\" @click=\"handleClose\"></div>\r\n    </div>\r\n    <GlobalChatNav v-model=\"navId\" :chatTotal=\"chatTotal\" exit @change=\"handleChange\"></GlobalChatNav>\r\n    <div class=\"GlobalChatBody\" v-show=\"navId === '1'\">\r\n      <GlobalChatView ref=\"chatViewRef\" v-model=\"chatId\" :chatList=\"chatList\" @time=\"handleTime\"\r\n        @refresh=\"handleRefresh\" @send=\"handleSend\"></GlobalChatView>\r\n    </div>\r\n    <div class=\"GlobalChatBody\" v-show=\"navId === '2'\">\r\n      <GlobalChatAddressBook ref=\"addressBookRef\" @send=\"handleSend\"></GlobalChatAddressBook>\r\n    </div>\r\n    <div class=\"GlobalChatBody\" v-show=\"navId === '3'\">\r\n      <GlobalChatGroup ref=\"groupRef\" @send=\"handleSend\"></GlobalChatGroup>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalLayoutChat' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, watch, onMounted, onUnmounted, defineAsyncComponent } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { handleChatId, handleChatList } from './js/ChatMethod.js'\r\nconst GlobalChatNav = defineAsyncComponent(() => import('./components/GlobalChatNav.vue'))\r\nconst GlobalChatView = defineAsyncComponent(() => import('./components/GlobalChatView.vue'))\r\nconst GlobalChatAddressBook = defineAsyncComponent(() => import('./components/GlobalChatAddressBook.vue'))\r\nconst GlobalChatGroup = defineAsyncComponent(() => import('./components/GlobalChatGroup.vue'))\r\nconst store = useStore()\r\nconst isMac = window.electron?.isMac\r\nconst ifMax = ref(false)\r\nconst rongCloudToken = computed(() => store.getters.getRongCloudToken)\r\nconst navId = ref('1')\r\nconst chatId = ref('')\r\nconst chatList = ref([])\r\nconst chatTotal = computed(() => {\r\n  let total = 0\r\n  for (let index = 0; index < chatList.value.length; index++) {\r\n    const item = chatList.value[index]\r\n    total += item.count\r\n  }\r\n  if (window.electron) window.electron.sendMessage({ key: 'chat-message', value: total ? total + '' : '' })\r\n  return total\r\n})\r\nconst chatViewRef = ref()\r\nconst addressBookRef = ref()\r\nconst groupRef = ref()\r\nconst refreshTime = ref('')\r\nconst chatObjectInfo = ref([])\r\nonMounted(() => {\r\n  if (window.electron)\r\n    window.electron.setConfig({\r\n      resizable: true,\r\n      maximizable: true,\r\n      minimumSize: [880, 680],\r\n      size: [880, 680],\r\n      center: true\r\n    })\r\n  if (window.electron)\r\n    window.electron.onMaximize(() => {\r\n      ifMax.value = true\r\n    })\r\n  if (window.electron)\r\n    window.electron.onUnmaximize(() => {\r\n      ifMax.value = false\r\n    })\r\n})\r\nconst handleMinimize = () => {\r\n  if (window.electron) window.electron.minimize()\r\n}\r\nconst handleMaximize = () => {\r\n  if (window.electron) window.electron.maximize()\r\n}\r\nconst handleUnmaximize = () => {\r\n  if (window.electron) window.electron.unmaximize()\r\n}\r\nconst handleClose = () => {\r\n  if (window.electron) window.electron.close()\r\n}\r\nconst rongCloudLink = async (token) => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.addEventListener(Events.CONNECTED, () => {\r\n    console.log('链接成功')\r\n    handleEventListener()\r\n    getRongCloudSessionList()\r\n  })\r\n  await RongIMLib.connect(token)\r\n}\r\n// const handleMessages = async (conversationType, targetId) => {\r\n//   const res = await RongIMLib.getConversation({ conversationType, targetId })\r\n//   return res\r\n// }\r\nconst handleEventListener = async () => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.addEventListener(Events.MESSAGES, async (evt) => {\r\n    console.log('新消息来了', evt.messages)\r\n    const newData = []\r\n    const newDataId = []\r\n    for (let index = 0; index < evt.messages.length; index++) {\r\n      const item = evt.messages[index]\r\n      if (!newDataId?.includes(item.targetId)) {\r\n        newDataId.push(item.targetId)\r\n        // const { code, data } = await handleMessages(item.conversationType, item.targetId)\r\n        const { code, data } = await RongIMLib.getConversation({\r\n          conversationType: item.conversationType,\r\n          targetId: item.targetId\r\n        })\r\n        if (data?.targetId === chatId.value) {\r\n          chatViewRef.value?.getNewestMessages()\r\n          if (!code) newData.push({ ...data, unreadMessageCount: 0 })\r\n        } else {\r\n          if (!code) newData.push(data)\r\n        }\r\n      }\r\n    }\r\n    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')\r\n    chatObjectInfo.value = await handleChatId(newData, isRefresh, chatObjectInfo.value)\r\n    chatList.value = await handleChatList(newData, [], chatList.value, chatObjectInfo.value)\r\n    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')\r\n  })\r\n}\r\nconst handleTime = (type) => {\r\n  refreshTime.value = type ? format(new Date(), 'YYYY-MM-DD HH') : ''\r\n}\r\nconst handleChange = (id) => {\r\n  if (id === '2') addressBookRef.value?.refresh()\r\n  if (id === '3') groupRef.value?.refresh()\r\n}\r\nconst handleRefresh = (type, data) => {\r\n  if (type === 'del') chatList.value = chatList.value.filter((v) => v.id !== data.id)\r\n  getRongCloudSessionList()\r\n}\r\nconst getRongCloudSessionList = async () => {\r\n  const { code, data, msg } = await RongIMLib.getConversationList()\r\n  if (code === 0) {\r\n    // console.log('获取会话列表成功', data)\r\n    const newTemporary = []\r\n    for (let index = 0; index < chatList.value.length; index++) {\r\n      const item = chatList.value[index]\r\n      if (item.isTemporary) newTemporary.push(item)\r\n    }\r\n    const isRefresh = refreshTime.value === format(new Date(), 'YYYY-MM-DD HH')\r\n    chatObjectInfo.value = await handleChatId(data, isRefresh, chatObjectInfo.value)\r\n    chatList.value = await handleChatList(data, newTemporary, [], chatObjectInfo.value)\r\n    refreshTime.value = format(new Date(), 'YYYY-MM-DD HH')\r\n  } else {\r\n    console.log('获取会话列表失败: ', code, msg)\r\n  }\r\n}\r\nconst handleSend = (data) => {\r\n  const idList = chatList.value.map((v) => v.id)\r\n  if (idList?.includes(data.id)) {\r\n    chatId.value = data.id\r\n    navId.value = '1'\r\n  } else {\r\n    chatId.value = data.id\r\n    chatList.value = [data, ...chatList.value]\r\n    navId.value = '1'\r\n  }\r\n}\r\nonUnmounted(() => {\r\n  const Events = RongIMLib.Events\r\n  RongIMLib.removeEventListeners(Events.MESSAGES)\r\n  RongIMLib.removeEventListeners(Events.CONNECTED)\r\n  RongIMLib.disconnect().then(() => {\r\n    console.log('成功断开')\r\n  })\r\n})\r\nwatch(\r\n  () => rongCloudToken.value,\r\n  () => {\r\n    if (rongCloudToken.value) rongCloudLink(rongCloudToken.value)\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalLayoutChat {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  border: 1px solid #ccc;\r\n  position: relative;\r\n\r\n  .zy-el-image {\r\n    -webkit-touch-callout: none;\r\n    -webkit-user-select: none;\r\n    -khtml-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n  }\r\n\r\n  .GlobalLayoutChatButton {\r\n    width: 96px;\r\n    height: 28px;\r\n    display: flex;\r\n    align-items: center;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    z-index: 99;\r\n    pointer-events: auto;\r\n    -webkit-app-region: no-drag;\r\n\r\n    .GlobalLayoutChatMinimize {\r\n      width: 32px;\r\n      height: 28px;\r\n      background: url('./img/minimize.png') no-repeat;\r\n      background-size: 16px 16px;\r\n      background-position: center center;\r\n      border-radius: 2px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background-color: rgba($color: #999, $alpha: 0.6);\r\n      }\r\n    }\r\n\r\n    .GlobalLayoutChatMaximize {\r\n      width: 32px;\r\n      height: 28px;\r\n      background: url('./img/max.png') no-repeat;\r\n      background-size: 14px 14px;\r\n      background-position: center center;\r\n      border-radius: 2px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background-color: rgba($color: #999, $alpha: 0.6);\r\n      }\r\n    }\r\n\r\n    .GlobalLayoutChatUnmaximize {\r\n      width: 32px;\r\n      height: 28px;\r\n      background: url('./img/min.png') no-repeat;\r\n      background-size: 14px 14px;\r\n      background-position: center center;\r\n      border-radius: 2px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background-color: rgba($color: #999, $alpha: 0.6);\r\n      }\r\n    }\r\n\r\n    .GlobalLayoutChatClose {\r\n      width: 32px;\r\n      height: 28px;\r\n      background: url('./img/close.png') no-repeat;\r\n      background-size: 16px 16px;\r\n      background-position: center center;\r\n      border-radius: 2px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background-color: rgba($color: red, $alpha: 0.6);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatBody {\r\n    width: calc(100% - 72px);\r\n    height: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAAAA,GAAA;EAESC,KAAK,EAAC;;;EAONA,KAAK,EAAC;AAAgB;;EAItBA,KAAK,EAAC;AAAgB;;EAGtBA,KAAK,EAAC;AAAgB;;uBAf7BC,mBAAA,CAkBM;IAlBDD,KAAK,EAAC,kBAAkB;IAAEE,aAAW,EAAAC,MAAA,QAAAA,MAAA,MAD5CC,cAAA,CACgC,cAAoB;OACLC,MAAA,CAAAC,KAAK,I,cAAhDL,mBAAA,CAKM,OALNM,UAKM,GAJJC,mBAAA,CAAgF;IAA3ER,KAAK,EAAC,0BAA0B;IAACS,KAAK,EAAC,KAAK;IAAEC,OAAK,EAAEL,MAAA,CAAAM;OACuBN,MAAA,CAAAO,KAAK,I,cAAtFX,mBAAA,CAA8F;IAJpGF,GAAA;IAIWC,KAAK,EAAC,0BAA0B;IAACS,KAAK,EAAC,KAAK;IAAEC,OAAK,EAAEL,MAAA,CAAAQ;QAJhEC,mBAAA,gBAK2FT,MAAA,CAAAO,KAAK,I,cAA1FX,mBAAA,CAAkG;IALxGF,GAAA;IAKWC,KAAK,EAAC,4BAA4B;IAACS,KAAK,EAAC,MAAM;IAAEC,OAAK,EAAEL,MAAA,CAAAU;QALnED,mBAAA,gBAMMN,mBAAA,CAAyE;IAApER,KAAK,EAAC,uBAAuB;IAACS,KAAK,EAAC,IAAI;IAAEC,OAAK,EAAEL,MAAA,CAAAW;UAN5DF,mBAAA,gBAQIG,YAAA,CAAkGZ,MAAA;IARtGa,UAAA,EAQ4Bb,MAAA,CAAAc,KAAK;IARjC,uBAAAhB,MAAA,QAAAA,MAAA,gBAAAiB,MAAA;MAAA,OAQ4Bf,MAAA,CAAAc,KAAK,GAAAC,MAAA;IAAA;IAAGC,SAAS,EAAEhB,MAAA,CAAAgB,SAAS;IAAEC,IAAI,EAAJ,EAAI;IAAEC,QAAM,EAAElB,MAAA,CAAAmB;wEACpEhB,mBAAA,CAGM,OAHNiB,UAGM,GAFJR,YAAA,CAC+DZ,MAAA;IAD/CqB,GAAG,EAAC,aAAa;IAVvCR,UAAA,EAUiDb,MAAA,CAAAsB,MAAM;IAVvD,uBAAAxB,MAAA,QAAAA,MAAA,gBAAAiB,MAAA;MAAA,OAUiDf,MAAA,CAAAsB,MAAM,GAAAP,MAAA;IAAA;IAAGQ,QAAQ,EAAEvB,MAAA,CAAAuB,QAAQ;IAAGC,MAAI,EAAExB,MAAA,CAAAyB,UAAU;IACtFC,SAAO,EAAE1B,MAAA,CAAA2B,aAAa;IAAGC,MAAI,EAAE5B,MAAA,CAAA6B;yFAFA7B,MAAA,CAAAc,KAAK,U,mBAIzCX,mBAAA,CAEM,OAFN2B,UAEM,GADJlB,YAAA,CAAuFZ,MAAA;IAAhEqB,GAAG,EAAC,gBAAgB;IAAEO,MAAI,EAAE5B,MAAA,CAAA6B;oEADjB7B,MAAA,CAAAc,KAAK,U,mBAGzCX,mBAAA,CAEM,OAFN4B,UAEM,GADJnB,YAAA,CAAqEZ,MAAA;IAApDqB,GAAG,EAAC,UAAU;IAAEO,MAAI,EAAE5B,MAAA,CAAA6B;oEADL7B,MAAA,CAAAc,KAAK,U", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}