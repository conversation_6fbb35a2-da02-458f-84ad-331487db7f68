"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[4829],{23306:function(e,t,n){n.d(t,{A:function(){return h}});var r=n(74061),o={class:"unauthorized-wrapper"},u={class:"unauthorized"},a={class:"unauthorized-title"},i={name:"Unauthorized"},l=Object.assign(i,{props:{name:{type:String,default:""},cancelCallback:{type:Function}},setup(e){var t=e,n=(0,r.ref)(!1);(0,r.onMounted)((function(){n.value=!0}));var i=function(){n.value=!1,setTimeout((function(){t.cancelCallback()}),300)};return function(e,l){return(0,r.openBlock)(),(0,r.createElementBlock)("div",o,[(0,r.createVNode)(r.Transition,{name:"unauthorized-fade"},{default:(0,r.withCtx)((function(){return[(0,r.withDirectives)((0,r.createElementVNode)("div",u,[l[1]||(l[1]=(0,r.createElementVNode)("div",{class:"unauthorized-icon"},null,-1)),(0,r.createElementVNode)("div",a,"抱歉，“"+(0,r.toDisplayString)(t.name)+"”地区暂未获得软件正版授权",1),l[2]||(l[2]=(0,r.createElementVNode)("div",{class:"unauthorized-text"},[(0,r.createTextVNode)(" 唯一指定官方授权： "),(0,r.createElementVNode)("div",{class:"official-logo"})],-1)),(0,r.createElementVNode)("div",{class:"unauthorized-close",onClick:i},l[0]||(l[0]=[(0,r.createElementVNode)("svg",{viewBox:"0 0 1024 1024"},[(0,r.createElementVNode)("path",{fill:"currentColor",d:"M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"})],-1)]))],512),[[r.vShow,n.value]])]})),_:1})])}}}),c=n(42218);const f=(0,c.A)(l,[["__scopeId","data-v-21157d48"]]);var v=f,s=(0,r.createVNode)("div",{class:"zy-confirm-container"});(0,r.render)(s,document.body);var d=s.el,h=function(e){var t=e.name,n=e.callback,o=function(){(0,r.render)(null,d),n&&n()},u=(0,r.createVNode)(v,{name:t,cancelCallback:o});(0,r.render)(u,d)}},14829:function(e,t,n){n.d(t,{AU:function(){return C},ET:function(){return N},U2:function(){return F},UO:function(){return _},Wn:function(){return j},he:function(){return L}});var r=n(74061),o=n(4955),u=n(59335),a=n(46197),i=n(8583),l=n(43886),c=n(74269),f=n(59429),v=n(61372),s=n.n(v),d=n(23306),h=n(98885),p=n(42714);n(35894),n(50389);function m(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=w(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,i=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){i=!0,u=e},f:function(){try{a||null==n.return||n.return()}finally{if(i)throw u}}}}function g(e){return x(e)||b(e)||w(e)||y()}function y(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function w(e,t){if(e){if("string"==typeof e)return P(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?P(e,t):void 0}}function b(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function x(e){if(Array.isArray(e))return P(e)}function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function S(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?S(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function k(e,t,n){return(t=T(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e){var t=E(e,"string");return"symbol"==typeof t?t:t+""}function E(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function I(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */I=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},u="function"==typeof Symbol?Symbol:{},a=u.iterator||"@@iterator",i=u.asyncIterator||"@@asyncIterator",l=u.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var u=t&&t.prototype instanceof g?t:g,a=Object.create(u.prototype),i=new L(r||[]);return o(a,"_invoke",{value:T(e,n,i)}),a}function v(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var s="suspendedStart",d="suspendedYield",h="executing",p="completed",m={};function g(){}function y(){}function w(){}var b={};c(b,a,(function(){return this}));var x=Object.getPrototypeOf,P=x&&x(x(C([])));P&&P!==n&&r.call(P,a)&&(b=P);var S=w.prototype=g.prototype=Object.create(b);function O(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function n(o,u,a,i){var l=v(e[o],e,u);if("throw"!==l.type){var c=l.arg,f=c.value;return f&&"object"==typeof f&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,a,i)}),(function(e){n("throw",e,a,i)})):t.resolve(f).then((function(e){c.value=e,a(c)}),(function(e){return n("throw",e,a,i)}))}i(l.arg)}var u;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return u=u?u.then(o,o):o()}})}function T(t,n,r){var o=s;return function(u,a){if(o===h)throw Error("Generator is already running");if(o===p){if("throw"===u)throw a;return{value:e,done:!0}}for(r.method=u,r.arg=a;;){var i=r.delegate;if(i){var l=E(i,r);if(l){if(l===m)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===s)throw o=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var c=v(t,n,r);if("normal"===c.type){if(o=r.done?p:d,c.arg===m)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=p,r.method="throw",r.arg=c.arg)}}}function E(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,E(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var u=v(o,t.iterator,n.arg);if("throw"===u.type)return n.method="throw",n.arg=u.arg,n.delegate=null,m;var a=u.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,m):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function R(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(R,this),this.reset(!0)}function C(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,u=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return u.next=u}}throw new TypeError(typeof t+" is not iterable")}return y.prototype=w,o(S,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:y,configurable:!0}),y.displayName=c(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,l,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},O(k.prototype),c(k.prototype,i,(function(){return this})),t.AsyncIterator=k,t.async=function(e,n,r,o,u){void 0===u&&(u=Promise);var a=new k(f(e,n,r,o),u);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},O(S),c(S,l,"Generator"),c(S,a,(function(){return this})),c(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=C,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(A),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return i.type="throw",i.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var u=this.tryEntries.length-1;u>=0;--u){var a=this.tryEntries[u],i=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var u=o;break}}u&&("break"===e||"continue"===e)&&u.tryLoc<=t&&t<=u.finallyLoc&&(u=null);var a=u?u.completion:{};return a.type=e,a.arg=t,u?(this.method="next",this.next=u.finallyLoc,m):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;A(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:C(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),m}},t}function R(e,t,n,r,o,u,a){try{var i=e[u](a),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,o)}function A(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var u=e.apply(t,n);function a(e){R(u,r,o,a,i,"next",e)}function i(e){R(u,r,o,a,i,"throw",e)}a(void 0)}))}}var L='<svg t="1743990581190" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="52145" width="30" height="30"><path d="M512 128a384 384 0 1 1 0 768A384 384 0 0 1 512 128z m0 48.192C335.296 176.192 174.08 335.36 174.08 512c0 176.704 161.216 336.512 337.92 336.512S849.152 688.704 849.152 512 688.704 176.192 512 176.192z" fill="#ffffff" p-id="52146"></path><path d="M244.352 506.688h73.6c0-123.2 86.016-220.032 196.48-215.68 36.8 0 69.568 13.248 102.272 30.848l-36.8 39.68c-20.48-8.96-45.056-17.664-69.568-17.664-77.76 0-143.232 70.4-143.232 162.816h73.6l-98.176 110.08-98.176-110.08zM705.088 515.84c0 117.248-86.848 217.28-194.368 217.28-37.248 0-70.4-17.344-103.424-34.752l37.184-39.04c20.736 8.704 45.568 17.344 70.4 17.344 78.592 0 144.768-69.504 144.768-160.768H581.056l99.328-108.608 99.2 108.544h-74.496z" fill="#ffffff" p-id="52147"></path></svg>',C=function(e,t){var n=(0,u.useStore)(),v=`${a.A.API_URL}/pageImg/open/menuIcon`,y=s()(),w=(0,r.ref)(0),b=(0,r.ref)(""),x=(0,r.ref)(null),P=(0,r.ref)(null),S=(0,r.ref)(!1),k=(0,r.ref)(""),T=(0,r.ref)(!1),E=(0,r.ref)([]),R=(0,r.ref)({}),L=(0,r.computed)((function(){return n.getters.getUserFn})),C=(0,r.computed)((function(){return n.getters.getAreaFn})),_=(0,r.computed)((function(){return n.getters.getRoleFn})),j=(0,r.computed)((function(){return n.getters.getReadOpenConfig})),N=(0,r.ref)(!1);(0,r.onMounted)((function(){F(),(0,r.nextTick)((function(){y.listenTo(x.value,(function(e){w.value=e.offsetWidth})),y.listenTo(P.value,(function(e){b.value=`width: calc(100% - ${e.offsetWidth+16}px);`}))}))}));var F=function(){var e,t,n=sessionStorage.getItem("oldRegionInfo")||"",r=sessionStorage.getItem("isRegionSelect")||"";"1"!==(null===(e=L.value)||void 0===e?void 0:e.accountId)&&(null===(t=L.value)||void 0===t?void 0:t.areaTotal)>1&&n&&!r&&(U.value=!0)},W=function(e){if("task"===e)n.commit("setGlobalCentralControlObj",{show:!0});else if("refresh"===e)window.location.href=`${a.A.mainPath}?v=${(new Date).getTime()}`;else if("locale"===e){if("cn"===(0,f.kU)())return(0,f.mY)("tw");if("tw"===(0,f.kU)())return(0,f.mY)("cn")}else"help"===e?N.value=!0:"edit_password"===e?(k.value="",S.value=!0):"exit"===e?V():(0,h.nk)({type:"info",message:"正在开发中！"})},M=function(e){e&&D("请使用新密码重新登录！"),S.value=!1},V=function(){p.s.confirm("此操作将退出当前系统, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){D("已安全退出！")})).catch((function(){(0,h.nk)({type:"info",message:"已取消退出"})}))},D=function(){var e=A(I().mark((function e(r){var u,a,i,l;return I().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o.A.loginOut();case 2:u=e.sent,a=u.code,200===a&&(sessionStorage.clear(),i=localStorage.getItem("goal_login_router_path"),i?(l=localStorage.getItem("goal_login_router_query")||"",t.push({path:i,query:l?JSON.parse(l):{}})):t.push({path:"/LoginView"}),n.commit("setState"),(0,c.OS)(),(0,h.nk)({message:r,showClose:!0,type:"success"}));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),G=(0,r.ref)(""),B=(0,r.ref)(""),U=(0,r.ref)(!1),z=function(e){var t;B.value=e.name,U.value=!1,sessionStorage.setItem("AreaRow",JSON.stringify(e)),(null===(t=L.value)||void 0===t?void 0:t.areaId)!==e.id&&q(e)},q=function(){var e=A(I().mark((function e(t){var r,u,a;return I().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o.A.verifyLoginUser({},t.id);case 2:r=e.sent,u=r.code,200===u?($.value="",sessionStorage.setItem("AreaId",t.id),(0,c.OS)(),n.dispatch("loginUser","login"),n.commit("setBoxMessageRefresh",!0),n.commit("setPersonalDoRefresh",!0)):(G.value=null===(a=L.value)||void 0===a?void 0:a.areaId,(0,d.A)({name:t.name}));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();(0,r.watch)((function(){return L.value}),(function(){var e,t;if(G.value=null===(e=L.value)||void 0===e?void 0:e.areaId,null!==(t=L.value)&&void 0!==t&&t.accountId){var n,o,u=sessionStorage.getItem("verify");if(u&&!Number(u)&&"1"!==(null===(n=L.value)||void 0===n?void 0:n.accountId))"true"===(null===(o=j.value)||void 0===o?void 0:o.forbidWeakPassword)?(0,r.nextTick)((function(){k.value="yes",T.value=!0})):(0,r.nextTick)((function(){k.value="no",S.value=!0}))}}),{immediate:!0});var J=function(e){for(var t=[],n=0,r=e.length;n<r;n++)t.push({id:e[n].menuId,name:e[n].name,routePath:e[n].routePath,menuFunction:e[n].menuFunction,menuRouteType:e[n].menuRouteType,icon:e[n].iconUrl?`${o.A.fileURL(e[n].iconUrl)}`:v,has:e[n].permissions,children:J(e[n].children||[])});return t},$=(0,r.ref)(""),H=(0,r.computed)((function(){return J(n.getters.getMenuFn||[])})),Y=(0,r.ref)(!1),K=(0,r.ref)(!1),Q=(0,r.ref)(!1),X=(0,r.ref)(""),Z=(0,r.ref)({}),ee=(0,r.ref)(""),te=(0,r.ref)({}),ne=(0,r.ref)([]),re=(0,r.ref)({});(0,r.watch)((function(){return H.value}),(function(){if(Y.value=!1,K.value=!1,te.value={},ne.value=[],re.value={},H.value.length){var e=JSON.parse(sessionStorage.getItem("query"))||{};e.openPageValue?Ce({key:e.openPageKey||"id",value:e.openPageValue}):(0,r.nextTick)((function(){var e;$.value=null===(e=H.value[0])||void 0===e?void 0:e.id,oe()}))}}),{immediate:!0});var oe=function(){var e=j.value.DetectionVersion||"";"true"!==e&&(0,l.a)(),ce.value="",fe.value=[],he.value=[],pe.value=[],ye.value=[],(0,r.nextTick)((function(){for(var e,n=function(){var e=H.value[o];if($.value===e.id)if(sessionStorage.setItem("has",JSON.stringify(e)),["/WorkBench","/WorkBenchCopy"].includes(e.routePath))Y.value=!1,t.push({path:e.routePath,query:Se(e.routePath)}),te.value=e,ne.value=e.children,(0,r.nextTick)((function(){if(ee.value){ee.value===X.value&&(X.value="",Z.value={});for(var t=0,n=e.children.length;t<n;t++)e.children[t].id===ee.value&&ae(e.children[t])}}));else{if(e.routePath.includes("/GlobalHome"))return X.value?(te.value=e,ae(e,!1)):(Y.value=!1,t.push({path:e.routePath,query:Se(e.routePath)})),{v:void 0};e.children&&e.children.length?ae(e,!0):["3","4"].includes(e.menuRouteType.value)?(Y.value=!1,K.value=!1,t.push({path:e.routePath,query:O(O({},Se(e.routePath)),{},{menuRouteType:e.menuRouteType.value})})):ae(e,!0)}},o=0,u=H.value.length;o<u;o++)if(e=n(),e)return e.v}))},ue=function(e,t){$.value=e,ce.value="",fe.value=[],he.value=[],pe.value=[],ye.value=[],ae(t)},ae=function(e,n){if(e.children&&e.children.length){n?(Y.value=!0,K.value=!1):(Y.value=!0,K.value=!0,re.value=e),fe.value=e.children;var o=X.value?le(e.children):ie(e.children);ce.value=o.id,ve(o),(0,r.nextTick)((function(){X.value="",ee.value=""}))}else if(["3","4"].includes(e.menuRouteType.value))Y.value=!1,K.value=!0,re.value=e,t.push({path:e.routePath,query:O(O({},Se(e.routePath)),{},{menuRouteType:e.menuRouteType.value})});else{n?(Y.value=!1,K.value=!1):(Y.value=!1,K.value=!0,re.value=e),fe.value=[e];var u=X.value?le([e]):ie([e]);ce.value=u.id,ve(u),(0,r.nextTick)((function(){X.value="",ee.value=""}))}},ie=function(e){for(var t={},n=0,r=e.length;n<r;n++)0===n&&(t=0===e[n].children.length?e[n]:ie(e[n].children));return t},le=function(e){for(var t={},n=0,r=e.length;n<r;n++)if(X.value===e[n].id&&(t=e[n]),e[n].children.length){var o=le(e[n].children);t=o.id?o:t}return t},ce=(0,r.ref)(""),fe=(0,r.ref)([]),ve=function(e){Ae(e.id),he.value.length||i.FE.setGlobalState({keepAliveRoute:[]}),he.value.map((function(e){return e.id})).includes(e.id)||he.value.push(e),be()},se=function(){K.value&&(Y.value=!1,K.value=!1,oe())},de=function(e,t){if(t+1!==he.value.length){var n=he.value.slice(0,t+1),r=he.value.slice(t+1).map((function(e){return e.id}));he.value=n,pe.value=pe.value.filter((function(e){return!r.includes(e.id)}));var o=he.value.filter((function(e){return"/"===Oe(e.routePath.substring(0,e.routePath.indexOf("?"))||e.routePath)})),u=Array.from(new Set(o.map((function(e){return Pe(e.routePath)}))));ye.value=u,ce.value=e.id,be()}},he=(0,r.ref)([]),pe=(0,r.ref)([]),me=(0,r.ref)(""),ge=(0,r.ref)([]),ye=(0,r.ref)([]),we=function(e,t){return e.filter((function(e){return-1===t.indexOf(e)}))},be=function(){var e=Object.keys(a.A.microApp),t=Array.from(new Set(he.value.map((function(e){return Oe(e.routePath)})))).filter((function(t){return e.includes(t)})),o=we(t,E.value);E.value=[].concat(g(E.value),g(o)),o.length?(0,r.nextTick)((function(){for(var e=function(){var e=o[t];R.value[e]||(R.value[e]=(0,i.wE)(e),R.value[e].loadPromise.then((function(){R.value[e].mountPromise.then((function(){i.FE.setGlobalState({theme:n.getters.getThemeFn,user:n.getters.getUserFn,menu:n.getters.getMenuFn,area:n.getters.getAreaFn,role:n.getters.getRoleFn,readConfig:n.getters.getReadConfig,readOpenConfig:n.getters.getReadOpenConfig})}))})).catch((function(t){ge.value.push(e)})))},t=0,r=o.length;t<r;t++)e();setTimeout((function(){xe()}),52)})):xe()},xe=function(){for(var e=0,n=he.value.length;e<n;e++){var r=he.value[e];if(ce.value===r.id){sessionStorage.setItem("has",JSON.stringify(r));var o=r.routePath.substring(0,r.routePath.indexOf("?"))||r.routePath,u=Oe(o);if(me.value=u,E.value.includes(u)&&!ge.value.includes(u)){var a,l=O(O({menuRouteType:null===(a=r.menuRouteType)||void 0===a?void 0:a.value},Se(r.routePath)),r.query);t.push({path:r.routePath,query:l}),R.value[u].mountPromise.then((function(){i.FE.setGlobalState({keepAliveRoute:he.value.map((function(e){return e.routePath}))})}))}else if("/"===u){var c,f=O(O({menuRouteType:null===(c=r.menuRouteType)||void 0===c?void 0:c.value},Se(r.routePath)),r.query);t.push({path:r.routePath,query:f});var v=he.value.filter((function(e){return"/"===Oe(e.routePath.substring(0,e.routePath.indexOf("?"))||e.routePath)})),s=Array.from(new Set(v.map((function(e){return Pe(e.routePath)}))));ye.value=s}else t.push({path:"/NotFoundPage"})}}},Pe=function(e){var t="",n=e.indexOf("/")+1,r=e.indexOf("?");return t=-1===r?e.substring(1):e.substring(n,r),t},Se=function(e){var t={};e=e.substring(e.indexOf("?")+1);var n=e.split("&");return n.forEach((function(e){var n=e.split("=");t[n[0]]=n[1]})),t},Oe=function(e){var t="",n=e.indexOf("/")+1,r=e.indexOf(" ",n),o=e.indexOf("/",n);return t=-1===o?e.substring(1,r):e.substring(1,o),t};(0,r.watch)((function(){return n.state.openRoute}),(function(e){e.path&&ke(e)}),{immediate:!0}),(0,r.watch)((function(){return n.state.closeOpenRoute}),(function(e){e.closeId&&Te(e)}),{immediate:!0});var ke=function(e){if(pe.value.map((function(e){return e.isData})).includes(JSON.stringify(e)))for(var t=0,n=pe.value.length;t<n;t++){var r=pe.value[t];r.isData===JSON.stringify(e)&&(ce.value=r.id,be())}else{var o=je();pe.value.push({id:o,isData:JSON.stringify(e)}),he.value.push({id:o,name:e.name,routePath:e.path,query:O(O({},e.query),{},{routeId:o,oldRouteId:ce.value})}),ce.value=o,be()}i.FE.setGlobalState({openRoute:{name:"",path:"",query:{}}})},Te=function(e){e.openId?(pe.value=pe.value.filter((function(t){return t.id!==e.closeId})),he.value=he.value.filter((function(t){return t.id!==e.closeId})),ce.value=e.openId,be()):Re(e.closeId),i.FE.setGlobalState({closeOpenRoute:{openId:"",closeId:""}})},Ee=(0,r.ref)(!0),Ie=function(t){if("main"===e.meta.moduleName)ye.value=ye.value.filter((function(t){return t!==e.name})),Ee.value=!1,setTimeout((function(){ye.value.push(e.name),Ee.value=!0}),200);else for(var n=0,r=he.value.length;n<r;n++){var o=he.value[n];o.id===t&&(i.FE.setGlobalState({refreshRoute:o.routePath}),setTimeout((function(){i.FE.setGlobalState({refreshRoute:""})}),222))}},Re=function(e){if(ce.value===e)for(var t=0,n=he.value.length;t<n;t++){var r=he.value[t];r.id===e&&(ce.value=he.value[t?t-1:1].id,be())}pe.value=pe.value.filter((function(t){return t.id!==e})),he.value=he.value.filter((function(t){return t.id!==e}))},Ae=function(e){pe.value=pe.value.filter((function(t){return t.id===e})),he.value=he.value.filter((function(t){return t.id===e})),ce.value=e,be()},Le=function(e,t,n){if(!e||!e.length)return[];var r,o=[],u=m(e);try{for(u.s();!(r=u.n()).done;){var a=r.value;a[t]!==n||X.value||(X.value=a.id,Z.value=a),a=Object.assign({},a);var i=Le(a.children,t,n);(i&&i.length||a[t]===n)&&(i.length&&(a.children=i),o.push(a))}}catch(l){u.e(l)}finally{u.f()}return o.length?o:[]},Ce=function(e){var t=e.key,n=void 0===t?"id":t,o=e.value;if(!Q.value){Q.value=!0;var u=Le(H.value,n,o)[0]||{};if(u.id)if($.value===u.id)if("/WorkBench"===u.routePath){var a;if(u.id===X.value)return X.value="",Z.value={},void se();(null===(a=u.children[0])||void 0===a?void 0:a.id)===re.value.id?(ce.value=X.value,ve(Z.value),X.value="",Z.value={},(0,r.nextTick)((function(){Q.value&&(Q.value=!1)}))):(se(),setTimeout((function(){(0,r.nextTick)((function(){var e;ee.value=null===(e=u.children[0])||void 0===e?void 0:e.id,oe(),(0,r.nextTick)((function(){Q.value&&(Q.value=!1)}))}))}),200))}else{if(u.routePath.includes("/GlobalHome"))return oe(),void(0,r.nextTick)((function(){Q.value&&(Q.value=!1)}));ce.value=X.value,ve(Z.value),X.value="",Z.value={},(0,r.nextTick)((function(){Q.value&&(Q.value=!1)}))}else if(K.value)se(),setTimeout((function(){(0,r.nextTick)((function(){var e;($.value=u.id,u.id===X.value)?(X.value="",Z.value={}):ee.value=null===(e=u.children[0])||void 0===e?void 0:e.id;oe(),(0,r.nextTick)((function(){Q.value&&(Q.value=!1)}))}))}),200);else{var i;if($.value=u.id,u.id===X.value)X.value="",Z.value={};else ee.value=null===(i=u.children[0])||void 0===i?void 0:i.id;oe(),(0,r.nextTick)((function(){Q.value&&(Q.value=!1)}))}else Q.value=!1,(0,h.nk)({type:"warning",message:"未检测到你有此菜单！"})}},_e=function(e){X.value=e},je=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,n="x"==e?t:3&t|8;return n.toString(16)}))};return(0,r.onUnmounted)((function(){for(var e=function(){var e=E.value[t];R.value[e].unmount(),R.value[e].unmountPromise.then((function(){R.value[e]=null}))},t=0,n=E.value.length;t<n;t++)e();E.value=[]})),(0,r.provide)("WorkBenchList",ne),(0,r.provide)("WorkBenchMenu",ue),(0,r.provide)("leftMenuData",ae),(0,r.provide)("setOpenPageId",_e),(0,r.provide)("openPage",Ce),(0,r.provide)("openRoute",ke),(0,r.provide)("delRoute",Te),(0,r.provide)("regionId",G),(0,r.provide)("regionSelect",z),(0,r.provide)("area",C),{user:L,area:C,role:_,left:w,width:b,LayoutViewBox:x,LayoutViewInfo:P,helpShow:N,handleCommand:W,editPassWordShow:S,verifyEditPassWord:k,verifyEditPassWordShow:T,editPassWordCallback:M,regionId:G,regionName:B,regionSelect:z,isRegionSelectShow:U,isView:Y,isChildView:K,tabMenu:$,tabMenuData:H,handleClick:oe,menuId:ce,menuData:fe,menuClick:ve,handleBreadcrumb:de,WorkBenchObj:te,WorkBenchList:ne,childData:re,WorkBenchReturn:se,isRefresh:Ee,keepAliveRoute:ye,tabData:he,tabClick:be,handleRefresh:Ie,handleClose:Re,handleCloseOther:Ae,isMicroApp:me,MicroApp:E,openPage:Ce,leftMenuData:ae}},_=function(e){var t=(0,r.ref)(!1),n=function(){t.value="main"===e.meta.moduleName};return(0,r.watch)((function(){return e}),(function(){n()}),{deep:!0,immediate:!0}),(0,r.onMounted)((function(){n(e)})),{isMain:t}},j=function(){var e=(0,u.useStore)(),t=(0,r.computed)((function(){return e.getters.getRongCloudToken}));return{rongCloudToken:t}},N=function(){var e=(0,u.useStore)(),t=(0,r.computed)((function(){return e.state.AiChatWidth})),n=(0,r.computed)((function(){return`${t.value}px`})),o=(0,r.ref)(!1),a=(0,r.ref)(!1),i=function(){if(window.innerWidth>1680){var t=window.innerWidth-1280>520?520:400;e.commit("setAiChatWidth",t),o.value||(a.value=!1),o.value=!0}else e.commit("setAiChatWidth",400),o.value&&(a.value=!1),o.value=!1};return(0,r.onMounted)((function(){i(),window.addEventListener("resize",i)})),(0,r.onUnmounted)((function(){window.removeEventListener("resize",i)})),{AiChatTargetWidth:n,AiChatViewType:o,AiChatWindowShow:a}},F=function(){var e=(0,u.useStore)(),t=(0,r.computed)((function(){return e.state.user})),n=(0,r.computed)((function(){return e.getters.getReadOpenConfig})),a=(0,r.computed)((function(){var e;return null===(e=n.value)||void 0===e?void 0:e.systemPlatform})),i=(0,r.ref)(!1),l=function(e,t){var n=new Set(e);return t.some((function(e){return n.has(e)}))},c=function(){var e=A(I().mark((function e(){var t,n;return I().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o.A.globalReadOpenConfig({codes:["loginPopShow"]});case 2:t=e.sent,n=t.data,n.loginPopShow?i.value="true"==n.loginPopShow:i.value=!1;case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,r.onMounted)((function(){})),(0,r.watch)((function(){return t.value}),(function(){if(t.value.id){var e=t.value.specialRoleKeys||[],n=["npc_contact_committee","suggestion_office_user","delegation_manager","npc_member"],r=["proposal_committee","suggestion_office_user","cppcc_member"],o="CPPCC"===a.value?r:n;i.value=l(e,o),i.value&&c()}}),{immediate:!0}),{loginHintShow:i}}}}]);