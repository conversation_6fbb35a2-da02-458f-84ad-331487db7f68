{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AllInformationDetailsBody\"\n};\nvar _hoisted_2 = {\n  class: \"detailsName\"\n};\nvar _hoisted_3 = {\n  class: \"detailsDeputyName\"\n};\nvar _hoisted_4 = {\n  class: \"detailsInfo\"\n};\nvar _hoisted_5 = {\n  class: \"detailsText\"\n};\nvar _hoisted_6 = {\n  class: \"detailsText\"\n};\nvar _hoisted_7 = {\n  class: \"detailsText\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  class: \"detailsContent\"\n};\nvar _hoisted_9 = [\"innerHTML\"];\nvar _hoisted_10 = {\n  key: 2,\n  class: \"detailsContent\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_xyl_global_file = _resolveComponent(\"xyl-global-file\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"AllInformationDetails\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, _toDisplayString($setup.details.infoTitle), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.details.infoSubtitle), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, \"来源：\" + _toDisplayString($setup.details.infoSource), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, \"发布时间：\" + _toDisplayString($setup.format($setup.details.pubTime)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, \"阅读量：\" + _toDisplayString($setup.quantity), 1 /* TEXT */)]), $setup.details.infoVideoType && $setup.videoUrl ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [$setup.videoUrl ? (_openBlock(), _createBlock($setup[\"XylVideoItem\"], {\n        key: 0,\n        id: $setup.videoUrl\n      }, null, 8 /* PROPS */, [\"id\"])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), $setup.details.contentType === 1 ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 1,\n        class: \"detailsContent\",\n        innerHTML: $setup.details.infoContent\n      }, null, 8 /* PROPS */, _hoisted_9)) : _createCommentVNode(\"v-if\", true), $setup.details.contentType === 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_cache[0] || (_cache[0] = _createElementVNode(\"span\", {\n        class: \"detailsContentLinks\"\n      }, \"外部链接： \", -1 /* HOISTED */)), _createVNode(_component_el_link, {\n        type: \"primary\",\n        href: $setup.details.linkUrl,\n        target: \"_blank\"\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.details.linkUrl), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"href\"])])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_xyl_global_file, {\n        fileData: $setup.details.attachments\n      }, null, 8 /* PROPS */, [\"fileData\"])])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_el_scrollbar", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_toDisplayString", "$setup", "details", "infoTitle", "_hoisted_3", "infoSubtitle", "_hoisted_4", "_hoisted_5", "infoSource", "_hoisted_6", "format", "pubTime", "_hoisted_7", "quantity", "infoVideoType", "videoUrl", "_createElementBlock", "_hoisted_8", "id", "_createCommentVNode", "contentType", "innerHTML", "infoContent", "_hoisted_9", "_hoisted_10", "_createVNode", "_component_el_link", "type", "href", "linkUrl", "target", "_createTextVNode", "_", "_component_xyl_global_file", "fileData", "attachments"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\homePage\\components\\AllInformationDetail.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"AllInformationDetails\">\r\n    <div class=\"AllInformationDetailsBody\">\r\n      <div class=\"detailsName\">{{ details.infoTitle }}</div>\r\n      <div class=\"detailsDeputyName\">{{ details.infoSubtitle }}</div>\r\n      <div class=\"detailsInfo\">\r\n        <div class=\"detailsText\">来源：{{ details.infoSource }}</div>\r\n        <div class=\"detailsText\">发布时间：{{ format(details.pubTime) }}</div>\r\n        <div class=\"detailsText\">阅读量：{{ quantity }}</div>\r\n      </div>\r\n      <div class=\"detailsContent\"\r\n           v-if=\"details.infoVideoType && videoUrl\">\r\n        <xyl-video-item :id=\"videoUrl\"\r\n                        v-if=\"videoUrl\"></xyl-video-item>\r\n      </div>\r\n\r\n      <div class=\"detailsContent\"\r\n           v-html=\"details.infoContent\"\r\n           v-if=\"details.contentType === 1\"></div>\r\n\r\n      <div class=\"detailsContent\"\r\n           v-if=\"details.contentType === 2\">\r\n        <span class=\"detailsContentLinks\">外部链接： </span>\r\n        <el-link type=\"primary\"\r\n                 :href=\"details.linkUrl\"\r\n                 target=\"_blank\">{{ details.linkUrl }}</el-link>\r\n      </div>\r\n      <xyl-global-file :fileData=\"details.attachments\"></xyl-global-file>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'AllInformationDetail' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport XylVideoItem from 'common/components/xyl-upload-video/xyl-video-item.vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\nimport { ref, onMounted } from 'vue'\r\n\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst route = useRoute()\r\nconst quantity = ref(0)\r\nconst details = ref({})\r\nconst videoUrl = ref('')\r\n\r\nonMounted(() => {\r\n  clickRecord()\r\n  newsContentInfo()\r\n})\r\n\r\nconst newsContentInfo = async () => {\r\n  var params = {\r\n    detailId: props.id\r\n  }\r\n  const res = await api.newsContentInfo(params)\r\n  var { data } = res\r\n  details.value = data\r\n  if (data.contentType === 2) {\r\n    goLinks(data.linkUrl)\r\n  }\r\n  VideoFilter(data)\r\n}\r\n\r\nconst goLinks = (linkUrl) => {\r\n  window.open(linkUrl, '_blank')\r\n}\r\n\r\nconst VideoFilter = (data) => {\r\n  if (data.infoVideoType === '2') {\r\n    videoUrl.value = data.infoVideo\r\n  }\r\n  if (data.infoVideoType === '1') {\r\n    videoUrl.value = data.linkDetailVos?.filter(item => item.useType === '2').length > 0 ? data.linkDetailVos.filter(item => item.useType === '2')[0].fileId : ''\r\n  }\r\n}\r\n\r\nconst clickRecord = async () => {\r\n  const { code } = await api.clickRecord(`information_content/${props.id}`)\r\n  if (code === 200) {\r\n    clickAcquire()\r\n  }\r\n}\r\nconst clickAcquire = async () => {\r\n  const { data } = await api.clickAcquire(`information_content/${props.id}`)\r\n  quantity.value = data\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.AllInformationDetails {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .zy-el-scrollbar__view {\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .AllInformationDetailsBody {\r\n    max-width: 990px;\r\n    min-height: 300px;\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    position: relative;\r\n    padding: 20px 40px;\r\n\r\n    .detailsName {\r\n      font-size: 26px;\r\n      text-align: center;\r\n      padding: 0 40px;\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .detailsDeputyName {\r\n      font-size: var(--zy-name-font-size);\r\n      text-align: center;\r\n      padding: 0 10px;\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .detailsInfo {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 20px 0 10px 0;\r\n      border-bottom: 1px solid #eee;\r\n\r\n      .detailsType {\r\n        color: var(--zy-el-color-primary);\r\n        border: 1px solid var(--zy-el-color-primary);\r\n        font-size: var(--zy-text-font-size);\r\n        margin-right: 12px;\r\n        padding: 0 6px;\r\n        border-radius: 2px;\r\n      }\r\n\r\n      .detailsText {\r\n        margin-right: 58px;\r\n        font-size: var(--zy-text-font-size);\r\n      }\r\n    }\r\n\r\n    .detailsContent {\r\n      padding: 20px 0;\r\n      overflow: hidden;\r\n      line-height: var(--zy-line-height);\r\n\r\n      img,\r\n      video {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n\r\n    .detailsContentLinks {\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": ";;EAESA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EARhCC,GAAA;EAUWD,KAAK,EAAC;;iBAVjB;;EAAAC,GAAA;EAoBWD,KAAK,EAAC;;;;;;uBAnBfE,YAAA,CA4BeC,uBAAA;IA5BDH,KAAK,EAAC;EAAuB;IAD7CI,OAAA,EAAAC,QAAA,CAEI;MAAA,OA0BM,CA1BNC,mBAAA,CA0BM,OA1BNC,UA0BM,GAzBJD,mBAAA,CAAsD,OAAtDE,UAAsD,EAAAC,gBAAA,CAA1BC,MAAA,CAAAC,OAAO,CAACC,SAAS,kBAC7CN,mBAAA,CAA+D,OAA/DO,UAA+D,EAAAJ,gBAAA,CAA7BC,MAAA,CAAAC,OAAO,CAACG,YAAY,kBACtDR,mBAAA,CAIM,OAJNS,UAIM,GAHJT,mBAAA,CAA0D,OAA1DU,UAA0D,EAAjC,KAAG,GAAAP,gBAAA,CAAGC,MAAA,CAAAC,OAAO,CAACM,UAAU,kBACjDX,mBAAA,CAAiE,OAAjEY,UAAiE,EAAxC,OAAK,GAAAT,gBAAA,CAAGC,MAAA,CAAAS,MAAM,CAACT,MAAA,CAAAC,OAAO,CAACS,OAAO,mBACvDd,mBAAA,CAAiD,OAAjDe,UAAiD,EAAxB,MAAI,GAAAZ,gBAAA,CAAGC,MAAA,CAAAY,QAAQ,iB,GAG/BZ,MAAA,CAAAC,OAAO,CAACY,aAAa,IAAIb,MAAA,CAAAc,QAAQ,I,cAD5CC,mBAAA,CAIM,OAJNC,UAIM,GADkBhB,MAAA,CAAAc,QAAQ,I,cAD9BtB,YAAA,CACiDQ,MAAA;QAbzDT,GAAA;QAYyB0B,EAAE,EAAEjB,MAAA,CAAAc;yCAZ7BI,mBAAA,e,KAAAA,mBAAA,gBAkBiBlB,MAAA,CAAAC,OAAO,CAACkB,WAAW,U,cAF9BJ,mBAAA,CAE4C;QAlBlDxB,GAAA;QAgBWD,KAAK,EAAC,gBAAgB;QACtB8B,SAA4B,EAApBpB,MAAA,CAAAC,OAAO,CAACoB;8BAjB3BC,UAAA,KAAAJ,mBAAA,gBAqBiBlB,MAAA,CAAAC,OAAO,CAACkB,WAAW,U,cAD9BJ,mBAAA,CAMM,OANNQ,WAMM,G,0BAJJ3B,mBAAA,CAA+C;QAAzCN,KAAK,EAAC;MAAqB,GAAC,QAAM,sBACxCkC,YAAA,CAEwDC,kBAAA;QAF/CC,IAAI,EAAC,SAAS;QACbC,IAAI,EAAE3B,MAAA,CAAAC,OAAO,CAAC2B,OAAO;QACtBC,MAAM,EAAC;;QAzBxBnC,OAAA,EAAAC,QAAA,CAyBiC;UAAA,OAAqB,CAzBtDmC,gBAAA,CAAA/B,gBAAA,CAyBoCC,MAAA,CAAAC,OAAO,CAAC2B,OAAO,iB;;QAzBnDG,CAAA;uCAAAb,mBAAA,gBA2BMM,YAAA,CAAmEQ,0BAAA;QAAjDC,QAAQ,EAAEjC,MAAA,CAAAC,OAAO,CAACiC;;;IA3B1CH,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}