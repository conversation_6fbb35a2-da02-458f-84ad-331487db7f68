{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, inject, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue';\nimport { useStore } from 'vuex';\nimport { user } from 'common/js/system_var.js';\nimport { ElMessage } from 'element-plus';\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue';\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue';\nvar __default__ = {\n  name: 'AssistedWriting'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var proposalIcon = '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#230;&#148;&#191;&#229;&#141;&#143;&#230;&#143;&#144;&#230;&#161;&#136;\" clip-path=\"url(#clip0_40_267)\"><g id=\"&#230;&#142;&#140;&#228;&#184;&#138;&#230;&#143;&#144;&#230;&#161;&#136;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6276\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_40_267)\"/><g id=\"&#231;&#187;&#132; 2742\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6277\" d=\"M35.4666 8.2666H12.5332C11.355 8.2666 10.3999 9.22173 10.3999 10.3999V37.5999C10.3999 38.7781 11.355 39.7333 12.5332 39.7333H35.4666C36.6448 39.7333 37.5999 38.7781 37.5999 37.5999V10.3999C37.5999 9.22173 36.6448 8.2666 35.4666 8.2666Z\" fill=\"white\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 342 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 13.8664C14.1333 13.4246 14.4915 13.0664 14.9333 13.0664H29.8666C30.3085 13.0664 30.6666 13.4246 30.6666 13.8664C30.6666 14.3082 30.3085 14.6664 29.8666 14.6664H14.9333C14.4915 14.6664 14.1333 14.3082 14.1333 13.8664Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 19.8664C14.1333 19.4246 14.4915 19.0664 14.9333 19.0664H22.9333C23.3751 19.0664 23.7333 19.4246 23.7333 19.8664C23.7333 20.3082 23.3751 20.6664 22.9333 20.6664H14.9333C14.4915 20.6664 14.1333 20.3082 14.1333 19.8664Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 344 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 25.8664C14.1333 25.4246 14.4915 25.0664 14.9333 25.0664H25.6C26.0418 25.0664 26.4 25.4246 26.4 25.8664C26.4 26.3082 26.0418 26.6664 25.6 26.6664H14.9333C14.4915 26.6664 14.1333 26.3082 14.1333 25.8664Z\" fill=\"#0964E3\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 797\" d=\"M28.9067 36.2952L36.2805 25.764C36.3438 25.674 36.3887 25.5724 36.4127 25.4649C36.4367 25.3575 36.4392 25.2464 36.4203 25.138C36.4013 25.0296 36.3612 24.9259 36.3021 24.833C36.2431 24.7401 36.1664 24.6598 36.0763 24.5966L33.9179 23.0851C33.8277 23.022 33.726 22.9773 33.6186 22.9535C33.5112 22.9297 33.4001 22.9272 33.2918 22.9463C33.1834 22.9654 33.0799 23.0057 32.9871 23.0649C32.8943 23.124 32.814 23.2008 32.7509 23.291L25.376 33.8222C25.3014 33.9287 25.2528 34.0513 25.2341 34.18L28.6213 36.5523C28.7352 36.4901 28.8329 36.402 28.9067 36.2952Z\" fill=\"url(#paint1_linear_40_267)\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 798\" d=\"M28.6218 36.5514L25.5604 37.7685C25.5189 37.7849 25.4746 37.793 25.43 37.7923C25.3854 37.7916 25.3413 37.7821 25.3003 37.7644C25.2594 37.7467 25.2223 37.7211 25.1913 37.689C25.1602 37.6569 25.1358 37.619 25.1194 37.5775C25.1023 37.5334 25.0943 37.4862 25.0959 37.4389L25.2378 34.1855L28.6218 36.5514Z\" fill=\"url(#paint2_linear_40_267)\"/></g></g></g><defs><linearGradient id=\"paint0_linear_40_267\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_40_267\" x1=\"30.8335\" y1=\"22.9336\" x2=\"30.8335\" y2=\"36.5523\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><linearGradient id=\"paint2_linear_40_267\" x1=\"26.8587\" y1=\"34.1855\" x2=\"26.8587\" y2=\"37.7924\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F1F7FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_40_267\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>';\n    var socialIcon = '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#231;&#164;&#190;&#230;&#131;&#133;&#230;&#176;&#145;&#230;&#132;&#143;\" clip-path=\"url(#clip0_40_278)\"><g id=\"&#231;&#164;&#190;&#230;&#131;&#133;&#230;&#176;&#145;&#230;&#132;&#143;_2\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6278\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_40_278)\"/><g id=\"&#231;&#187;&#132; 2744\"><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1794\" d=\"M36.9018 40.0001H11.0981C10.3701 40.0001 9.67185 39.7109 9.15705 39.1961C8.64225 38.6813 8.35303 37.9831 8.35303 37.2551V27.3351L24 20.7041L39.6469 27.3324V37.2524C39.6473 37.6131 39.5765 37.9704 39.4387 38.3037C39.3009 38.6371 39.0988 38.94 38.8438 39.1952C38.5889 39.4504 38.2862 39.6528 37.9529 39.7909C37.6197 39.9291 37.2625 40.0001 36.9018 40.0001Z\" fill=\"#BBD7FE\"/><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6279\" d=\"M34.241 8H13.7588C12.5806 8 11.6255 8.95513 11.6255 10.1333V34.4976C11.6255 35.6758 12.5806 36.6309 13.7588 36.6309H34.241C35.4192 36.6309 36.3743 35.6758 36.3743 34.4976V10.1333C36.3743 8.95513 35.4192 8 34.241 8Z\" fill=\"white\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 345 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 19.2219C15.5129 18.78 15.8711 18.4219 16.3129 18.4219H29.8121C30.254 18.4219 30.6121 18.78 30.6121 19.2219C30.6121 19.6637 30.254 20.0219 29.8121 20.0219H16.3129C15.8711 20.0219 15.5129 19.6637 15.5129 19.2219Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 346 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 24.5651C15.5129 24.1233 15.8711 23.7651 16.3129 23.7651H25.3124C25.7542 23.7651 26.1124 24.1233 26.1124 24.5651C26.1124 25.007 25.7542 25.3651 25.3124 25.3651H16.3129C15.8711 25.3651 15.5129 25.007 15.5129 24.5651Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 347 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 13.8786C15.5129 13.4368 15.8711 13.0786 16.3129 13.0786H29.2495C29.6913 13.0786 30.0495 13.4368 30.0495 13.8786C30.0495 14.3204 29.6913 14.6786 29.2495 14.6786H16.3129C15.8711 14.6786 15.5129 14.3204 15.5129 13.8786Z\" fill=\"#0964E3\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1795\" d=\"M36.9018 40.0002H11.0981C10.3701 40.0002 9.67185 39.711 9.15705 39.1962C8.64225 38.6814 8.35303 37.9832 8.35303 37.2552V27.3352L24 30.764L39.6469 27.3325V37.2525C39.6473 37.6132 39.5765 37.9705 39.4387 38.3038C39.3009 38.6372 39.0988 38.9401 38.8438 39.1953C38.5889 39.4505 38.2862 39.6529 37.9529 39.791C37.6197 39.9292 37.2625 40.0002 36.9018 40.0002Z\" fill=\"url(#paint1_linear_40_278)\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 348 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M20.3877 35.5334C20.3877 35.0916 20.7459 34.7334 21.1877 34.7334H26.8122C27.2541 34.7334 27.6122 35.0916 27.6122 35.5334C27.6122 35.9752 27.2541 36.3334 26.8122 36.3334H21.1877C20.7459 36.3334 20.3877 35.9752 20.3877 35.5334Z\" fill=\"white\"/></g></g></g><defs><linearGradient id=\"paint0_linear_40_278\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_40_278\" x1=\"24\" y1=\"27.3325\" x2=\"24\" y2=\"40.0002\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_40_278\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>';\n    var openPage = inject('openPage');\n    var toolId = ref('');\n    var toolInfo = ref({});\n    var toolData = ref([]);\n    var toolIconData = {\n      proposal: proposalIcon,\n      social: socialIcon\n    };\n    var editorRef = ref();\n    var fileList = ref([]);\n    var fileData = ref([]);\n    var sendContent = ref('');\n    var handleFileUpload = function handleFileUpload(data) {\n      fileList.value = data;\n    };\n    var handleFileCallback = function handleFileCallback(data) {\n      fileData.value = data;\n    };\n    var handleClose = function handleClose(item) {\n      var _editorRef$value;\n      (_editorRef$value = editorRef.value) === null || _editorRef$value === void 0 || _editorRef$value.handleSetFile(fileData.value.filter(function (v) {\n        return v.id !== item.id;\n      }));\n    };\n    var handleTips = function handleTips(text) {\n      var parts = text.split(/(\\{[^}]+\\})/);\n      var result = parts.map(function (part) {\n        if (part.startsWith('{') && part.endsWith('}')) {\n          return {\n            value: part.slice(1, -1),\n            type: true\n          };\n        } else if (part.trim() !== '') {\n          return {\n            value: part,\n            type: false\n          };\n        }\n      }).filter(function (item) {\n        return item !== undefined;\n      });\n      return result;\n    };\n    var handleTool = function handleTool(item) {\n      var _editorRef$value2, _editorRef$value3;\n      toolInfo.value = item;\n      toolId.value = item.chatToolCode;\n      (_editorRef$value2 = editorRef.value) === null || _editorRef$value2 === void 0 || _editorRef$value2.handleSetFile([]);\n      (_editorRef$value3 = editorRef.value) === null || _editorRef$value3 === void 0 || _editorRef$value3.handleSetContent('');\n      nextTick(function () {\n        var _editorRef$value4;\n        (_editorRef$value4 = editorRef.value) === null || _editorRef$value4 === void 0 || _editorRef$value4.handleInsertPlaceholder(handleTips(item.userPromptTip));\n      });\n    };\n    var handleSendMessage = function handleSendMessage(value) {\n      var _editorRef$value5;\n      if (!toolId.value) return ElMessage({\n        type: 'warning',\n        message: '请先选择文档撰写类型！'\n      });\n      var openAiParams = {\n        toolId: toolInfo.value.id,\n        toolCode: toolId.value,\n        toolContent: value,\n        fileData: fileData.value\n      };\n      sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams));\n      if (toolId.value === 'proposal') openPage({\n        key: 'routePath',\n        value: '/proposal/SubmitSuggest?utype=1'\n      });\n      if (toolId.value === 'social') openPage({\n        key: 'routePath',\n        value: '/publicOpinion/PublicOpinionNew'\n      });\n      (_editorRef$value5 = editorRef.value) === null || _editorRef$value5 === void 0 || _editorRef$value5.handleSetFile([]);\n    };\n    var aigptChatSceneDetail = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _data$tools;\n        var _yield$api$aigptChatS, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.aigptChatSceneDetail({\n                query: {\n                  chatSceneCode: 'ai-assisted-writing-chat'\n                }\n              });\n            case 2:\n              _yield$api$aigptChatS = _context.sent;\n              data = _yield$api$aigptChatS.data;\n              toolData.value = (data === null || data === void 0 || (_data$tools = data.tools) === null || _data$tools === void 0 ? void 0 : _data$tools.filter(function (v) {\n                return v.isUsing;\n              })) || [];\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function aigptChatSceneDetail() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    onActivated(function () {\n      aigptChatSceneDetail();\n      store.commit('setAiChatElShow', false);\n    });\n    onDeactivated(function () {\n      store.commit('setAiChatElShow', true);\n    });\n    onUnmounted(function () {\n      store.commit('setAiChatElShow', true);\n    });\n    var __returned__ = {\n      store,\n      proposalIcon,\n      socialIcon,\n      openPage,\n      toolId,\n      toolInfo,\n      toolData,\n      toolIconData,\n      editorRef,\n      fileList,\n      fileData,\n      sendContent,\n      handleFileUpload,\n      handleFileCallback,\n      handleClose,\n      handleTips,\n      handleTool,\n      handleSendMessage,\n      aigptChatSceneDetail,\n      get api() {\n        return api;\n      },\n      ref,\n      inject,\n      nextTick,\n      onActivated,\n      onDeactivated,\n      onUnmounted,\n      get useStore() {\n        return useStore;\n      },\n      get user() {\n        return user;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      GlobalAiChatFile,\n      GlobalAiChatEditor\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "inject", "nextTick", "onActivated", "onDeactivated", "onUnmounted", "useStore", "user", "ElMessage", "GlobalAiChatFile", "GlobalAiChatEditor", "__default__", "store", "proposalIcon", "socialIcon", "openPage", "toolId", "toolInfo", "toolData", "toolIconData", "proposal", "social", "editor<PERSON><PERSON>", "fileList", "fileData", "send<PERSON><PERSON><PERSON>", "handleFileUpload", "data", "handleFileCallback", "handleClose", "item", "_editorRef$value", "handleSetFile", "filter", "id", "handleTips", "text", "parts", "split", "result", "map", "part", "startsWith", "endsWith", "trim", "undefined", "handleTool", "_editorRef$value2", "_editorRef$value3", "chatToolCode", "handleSetContent", "_editorRef$value4", "handleInsertPlaceholder", "userPromptTip", "handleSendMessage", "_editorRef$value5", "message", "openAiParams", "toolCode", "toolContent", "sessionStorage", "setItem", "JSON", "stringify", "key", "aigptChatSceneDetail", "_ref2", "_callee", "_data$tools", "_yield$api$aigptChatS", "_callee$", "_context", "query", "chatSceneCode", "tools", "isUsing", "commit"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AssistedWriting/AssistedWriting.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AssistedWriting\">\r\n    <div class=\"AssistedWritingBody\">\r\n      <div class=\"AssistedWritingUserBody\">\r\n        <div class=\"AssistedWritingUser\">\r\n          <el-image :src=\"user.image\" fit=\"cover\" />\r\n          <div class=\"AssistedWritingUserInfo\">\r\n            <div class=\"AssistedWritingUserName\">{{ user.userName }}</div>\r\n            <div class=\"AssistedWritingUserText\">{{ user.position }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AssistedWritingTitle\">文档撰写类型</div>\r\n      <div class=\"AssistedWritingList\">\r\n        <div\r\n          class=\"AssistedWritingItem\"\r\n          :class=\"{ 'is-active': toolId === item.chatToolCode }\"\r\n          v-for=\"item in toolData\"\r\n          :key=\"item.chatToolCode\"\r\n          @click=\"handleTool(item)\">\r\n          <div class=\"AssistedWritingIcon\" v-html=\"toolIconData[item.chatToolCode]\"></div>\r\n          <div class=\"AssistedWritingName\">{{ item.chatToolName }}</div>\r\n        </div>\r\n        <div class=\"AssistedWritingPlaceholder\" v-for=\"item in 2\" :key=\"item + '_placeholder'\"></div>\r\n      </div>\r\n      <div class=\"AssistedWritingEditorBody\">\r\n        <GlobalAiChatFile\r\n          :fileList=\"fileList\"\r\n          :fileData=\"fileData\"\r\n          @close=\"handleClose\"\r\n          v-show=\"fileList.length || fileData.length\" />\r\n        <GlobalAiChatEditor\r\n          ref=\"editorRef\"\r\n          v-model=\"sendContent\"\r\n          @send=\"handleSendMessage\"\r\n          @uploadCallback=\"handleFileUpload\"\r\n          @fileCallback=\"handleFileCallback\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AssistedWriting' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, inject, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue'\r\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue'\r\nconst store = useStore()\r\nconst proposalIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#230;&#148;&#191;&#229;&#141;&#143;&#230;&#143;&#144;&#230;&#161;&#136;\" clip-path=\"url(#clip0_40_267)\"><g id=\"&#230;&#142;&#140;&#228;&#184;&#138;&#230;&#143;&#144;&#230;&#161;&#136;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6276\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_40_267)\"/><g id=\"&#231;&#187;&#132; 2742\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6277\" d=\"M35.4666 8.2666H12.5332C11.355 8.2666 10.3999 9.22173 10.3999 10.3999V37.5999C10.3999 38.7781 11.355 39.7333 12.5332 39.7333H35.4666C36.6448 39.7333 37.5999 38.7781 37.5999 37.5999V10.3999C37.5999 9.22173 36.6448 8.2666 35.4666 8.2666Z\" fill=\"white\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 342 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 13.8664C14.1333 13.4246 14.4915 13.0664 14.9333 13.0664H29.8666C30.3085 13.0664 30.6666 13.4246 30.6666 13.8664C30.6666 14.3082 30.3085 14.6664 29.8666 14.6664H14.9333C14.4915 14.6664 14.1333 14.3082 14.1333 13.8664Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 19.8664C14.1333 19.4246 14.4915 19.0664 14.9333 19.0664H22.9333C23.3751 19.0664 23.7333 19.4246 23.7333 19.8664C23.7333 20.3082 23.3751 20.6664 22.9333 20.6664H14.9333C14.4915 20.6664 14.1333 20.3082 14.1333 19.8664Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 344 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 25.8664C14.1333 25.4246 14.4915 25.0664 14.9333 25.0664H25.6C26.0418 25.0664 26.4 25.4246 26.4 25.8664C26.4 26.3082 26.0418 26.6664 25.6 26.6664H14.9333C14.4915 26.6664 14.1333 26.3082 14.1333 25.8664Z\" fill=\"#0964E3\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 797\" d=\"M28.9067 36.2952L36.2805 25.764C36.3438 25.674 36.3887 25.5724 36.4127 25.4649C36.4367 25.3575 36.4392 25.2464 36.4203 25.138C36.4013 25.0296 36.3612 24.9259 36.3021 24.833C36.2431 24.7401 36.1664 24.6598 36.0763 24.5966L33.9179 23.0851C33.8277 23.022 33.726 22.9773 33.6186 22.9535C33.5112 22.9297 33.4001 22.9272 33.2918 22.9463C33.1834 22.9654 33.0799 23.0057 32.9871 23.0649C32.8943 23.124 32.814 23.2008 32.7509 23.291L25.376 33.8222C25.3014 33.9287 25.2528 34.0513 25.2341 34.18L28.6213 36.5523C28.7352 36.4901 28.8329 36.402 28.9067 36.2952Z\" fill=\"url(#paint1_linear_40_267)\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 798\" d=\"M28.6218 36.5514L25.5604 37.7685C25.5189 37.7849 25.4746 37.793 25.43 37.7923C25.3854 37.7916 25.3413 37.7821 25.3003 37.7644C25.2594 37.7467 25.2223 37.7211 25.1913 37.689C25.1602 37.6569 25.1358 37.619 25.1194 37.5775C25.1023 37.5334 25.0943 37.4862 25.0959 37.4389L25.2378 34.1855L28.6218 36.5514Z\" fill=\"url(#paint2_linear_40_267)\"/></g></g></g><defs><linearGradient id=\"paint0_linear_40_267\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_40_267\" x1=\"30.8335\" y1=\"22.9336\" x2=\"30.8335\" y2=\"36.5523\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><linearGradient id=\"paint2_linear_40_267\" x1=\"26.8587\" y1=\"34.1855\" x2=\"26.8587\" y2=\"37.7924\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F1F7FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_40_267\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>'\r\nconst socialIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#231;&#164;&#190;&#230;&#131;&#133;&#230;&#176;&#145;&#230;&#132;&#143;\" clip-path=\"url(#clip0_40_278)\"><g id=\"&#231;&#164;&#190;&#230;&#131;&#133;&#230;&#176;&#145;&#230;&#132;&#143;_2\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6278\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_40_278)\"/><g id=\"&#231;&#187;&#132; 2744\"><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1794\" d=\"M36.9018 40.0001H11.0981C10.3701 40.0001 9.67185 39.7109 9.15705 39.1961C8.64225 38.6813 8.35303 37.9831 8.35303 37.2551V27.3351L24 20.7041L39.6469 27.3324V37.2524C39.6473 37.6131 39.5765 37.9704 39.4387 38.3037C39.3009 38.6371 39.0988 38.94 38.8438 39.1952C38.5889 39.4504 38.2862 39.6528 37.9529 39.7909C37.6197 39.9291 37.2625 40.0001 36.9018 40.0001Z\" fill=\"#BBD7FE\"/><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6279\" d=\"M34.241 8H13.7588C12.5806 8 11.6255 8.95513 11.6255 10.1333V34.4976C11.6255 35.6758 12.5806 36.6309 13.7588 36.6309H34.241C35.4192 36.6309 36.3743 35.6758 36.3743 34.4976V10.1333C36.3743 8.95513 35.4192 8 34.241 8Z\" fill=\"white\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 345 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 19.2219C15.5129 18.78 15.8711 18.4219 16.3129 18.4219H29.8121C30.254 18.4219 30.6121 18.78 30.6121 19.2219C30.6121 19.6637 30.254 20.0219 29.8121 20.0219H16.3129C15.8711 20.0219 15.5129 19.6637 15.5129 19.2219Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 346 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 24.5651C15.5129 24.1233 15.8711 23.7651 16.3129 23.7651H25.3124C25.7542 23.7651 26.1124 24.1233 26.1124 24.5651C26.1124 25.007 25.7542 25.3651 25.3124 25.3651H16.3129C15.8711 25.3651 15.5129 25.007 15.5129 24.5651Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 347 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 13.8786C15.5129 13.4368 15.8711 13.0786 16.3129 13.0786H29.2495C29.6913 13.0786 30.0495 13.4368 30.0495 13.8786C30.0495 14.3204 29.6913 14.6786 29.2495 14.6786H16.3129C15.8711 14.6786 15.5129 14.3204 15.5129 13.8786Z\" fill=\"#0964E3\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1795\" d=\"M36.9018 40.0002H11.0981C10.3701 40.0002 9.67185 39.711 9.15705 39.1962C8.64225 38.6814 8.35303 37.9832 8.35303 37.2552V27.3352L24 30.764L39.6469 27.3325V37.2525C39.6473 37.6132 39.5765 37.9705 39.4387 38.3038C39.3009 38.6372 39.0988 38.9401 38.8438 39.1953C38.5889 39.4505 38.2862 39.6529 37.9529 39.791C37.6197 39.9292 37.2625 40.0002 36.9018 40.0002Z\" fill=\"url(#paint1_linear_40_278)\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 348 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M20.3877 35.5334C20.3877 35.0916 20.7459 34.7334 21.1877 34.7334H26.8122C27.2541 34.7334 27.6122 35.0916 27.6122 35.5334C27.6122 35.9752 27.2541 36.3334 26.8122 36.3334H21.1877C20.7459 36.3334 20.3877 35.9752 20.3877 35.5334Z\" fill=\"white\"/></g></g></g><defs><linearGradient id=\"paint0_linear_40_278\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_40_278\" x1=\"24\" y1=\"27.3325\" x2=\"24\" y2=\"40.0002\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_40_278\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>'\r\n\r\nconst openPage = inject('openPage')\r\nconst toolId = ref('')\r\nconst toolInfo = ref({})\r\nconst toolData = ref([])\r\nconst toolIconData = { proposal: proposalIcon, social: socialIcon }\r\n\r\nconst editorRef = ref()\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst sendContent = ref('')\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleTips = (text) => {\r\n  const parts = text.split(/(\\{[^}]+\\})/)\r\n  const result = parts\r\n    .map((part) => {\r\n      if (part.startsWith('{') && part.endsWith('}')) {\r\n        return { value: part.slice(1, -1), type: true }\r\n      } else if (part.trim() !== '') {\r\n        return { value: part, type: false }\r\n      }\r\n    })\r\n    .filter((item) => item !== undefined)\r\n  return result\r\n}\r\nconst handleTool = (item) => {\r\n  toolInfo.value = item\r\n  toolId.value = item.chatToolCode\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n  nextTick(() => {\r\n    editorRef.value?.handleInsertPlaceholder(handleTips(item.userPromptTip))\r\n  })\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!toolId.value) return ElMessage({ type: 'warning', message: '请先选择文档撰写类型！' })\r\n  const openAiParams = {\r\n    toolId: toolInfo.value.id,\r\n    toolCode: toolId.value,\r\n    toolContent: value,\r\n    fileData: fileData.value\r\n  }\r\n  sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams))\r\n  if (toolId.value === 'proposal') openPage({ key: 'routePath', value: '/proposal/SubmitSuggest?utype=1' })\r\n  if (toolId.value === 'social') openPage({ key: 'routePath', value: '/publicOpinion/PublicOpinionNew' })\r\n  editorRef.value?.handleSetFile([])\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const { data } = await api.aigptChatSceneDetail({ query: { chatSceneCode: 'ai-assisted-writing-chat' } })\r\n  toolData.value = data?.tools?.filter((v) => v.isUsing) || []\r\n}\r\nonActivated(() => {\r\n  aigptChatSceneDetail()\r\n  store.commit('setAiChatElShow', false)\r\n})\r\nonDeactivated(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\nonUnmounted(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.AssistedWriting {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  position: relative;\r\n\r\n  .AssistedWritingBody {\r\n    padding: var(--zy-distance-two);\r\n  }\r\n\r\n  .AssistedWritingUserBody {\r\n    width: 800px;\r\n    border-radius: 6px 6px 6px 6px;\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .AssistedWritingUser {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: var(--zy-distance-two);\r\n\r\n      .zy-el-image {\r\n        width: 62px;\r\n        height: 62px;\r\n        border-radius: 50%;\r\n      }\r\n\r\n      .AssistedWritingUserInfo {\r\n        width: calc(100% - 62px);\r\n        height: 58px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        padding-left: var(--zy-distance-two);\r\n\r\n        .AssistedWritingUserName {\r\n          font-weight: bold;\r\n          line-height: var(--zy-line-height);\r\n          font-size: calc(var(--zy-name-font-size) + 2px);\r\n        }\r\n\r\n        .AssistedWritingUserText {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-name-font-size);\r\n          color: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .AssistedWritingTitle {\r\n    width: 100%;\r\n    font-weight: bold;\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-name-font-size);\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .AssistedWritingList {\r\n    width: 800px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .AssistedWritingPlaceholder {\r\n      width: 185px;\r\n      height: 126px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .AssistedWritingItem {\r\n      width: 185px;\r\n      height: 126px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      border-radius: 6px 6px 6px 6px;\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n      padding: 20px;\r\n      margin-bottom: 20px;\r\n      cursor: pointer;\r\n\r\n      &.is-active {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AssistedWritingName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AssistedWritingName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .AssistedWritingIcon {\r\n        width: 48px;\r\n        height: 48px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n\r\n      .AssistedWritingName {\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n    }\r\n  }\r\n\r\n  .AssistedWritingEditorBody {\r\n    width: 800px;\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: 20px;\r\n    transform: translateX(-50%);\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalAiChatEditorContainer {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA8CA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,QAAQ,KAAK;AACpF,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,kBAAkB,MAAM,wCAAwC;AATvE,IAAAC,WAAA,GAAe;EAAEvC,IAAI,EAAE;AAAkB,CAAC;;;;;IAU1C,IAAMwC,KAAK,GAAGN,QAAQ,CAAC,CAAC;IACxB,IAAMO,YAAY,GAChB,0mHAA0mH;IAC5mH,IAAMC,UAAU,GACd,8jHAA8jH;IAEhkH,IAAMC,QAAQ,GAAGd,MAAM,CAAC,UAAU,CAAC;IACnC,IAAMe,MAAM,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMiB,QAAQ,GAAGjB,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAMkB,QAAQ,GAAGlB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMmB,YAAY,GAAG;MAAEC,QAAQ,EAAEP,YAAY;MAAEQ,MAAM,EAAEP;IAAW,CAAC;IAEnE,IAAMQ,SAAS,GAAGtB,GAAG,CAAC,CAAC;IACvB,IAAMuB,QAAQ,GAAGvB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMwB,QAAQ,GAAGxB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMyB,WAAW,GAAGzB,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAM0B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;MACjCJ,QAAQ,CAAC5H,KAAK,GAAGgI,IAAI;IACvB,CAAC;IACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAID,IAAI,EAAK;MACnCH,QAAQ,CAAC7H,KAAK,GAAGgI,IAAI;IACvB,CAAC;IACD,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAAA,IAAAC,gBAAA;MAC5B,CAAAA,gBAAA,GAAAT,SAAS,CAAC3H,KAAK,cAAAoI,gBAAA,eAAfA,gBAAA,CAAiBC,aAAa,CAACR,QAAQ,CAAC7H,KAAK,CAACsI,MAAM,CAAC,UAACtG,CAAC;QAAA,OAAKA,CAAC,CAACuG,EAAE,KAAKJ,IAAI,CAACI,EAAE;MAAA,EAAC,CAAC;IAChF,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B,IAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,aAAa,CAAC;MACvC,IAAMC,MAAM,GAAGF,KAAK,CACjBG,GAAG,CAAC,UAACC,IAAI,EAAK;QACb,IAAIA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,IAAID,IAAI,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC9C,OAAO;YAAEhJ,KAAK,EAAE8I,IAAI,CAACzD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAAElE,IAAI,EAAE;UAAK,CAAC;QACjD,CAAC,MAAM,IAAI2H,IAAI,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAC7B,OAAO;YAAEjJ,KAAK,EAAE8I,IAAI;YAAE3H,IAAI,EAAE;UAAM,CAAC;QACrC;MACF,CAAC,CAAC,CACDmH,MAAM,CAAC,UAACH,IAAI;QAAA,OAAKA,IAAI,KAAKe,SAAS;MAAA,EAAC;MACvC,OAAON,MAAM;IACf,CAAC;IACD,IAAMO,UAAU,GAAG,SAAbA,UAAUA,CAAIhB,IAAI,EAAK;MAAA,IAAAiB,iBAAA,EAAAC,iBAAA;MAC3B/B,QAAQ,CAACtH,KAAK,GAAGmI,IAAI;MACrBd,MAAM,CAACrH,KAAK,GAAGmI,IAAI,CAACmB,YAAY;MAChC,CAAAF,iBAAA,GAAAzB,SAAS,CAAC3H,KAAK,cAAAoJ,iBAAA,eAAfA,iBAAA,CAAiBf,aAAa,CAAC,EAAE,CAAC;MAClC,CAAAgB,iBAAA,GAAA1B,SAAS,CAAC3H,KAAK,cAAAqJ,iBAAA,eAAfA,iBAAA,CAAiBE,gBAAgB,CAAC,EAAE,CAAC;MACrChD,QAAQ,CAAC,YAAM;QAAA,IAAAiD,iBAAA;QACb,CAAAA,iBAAA,GAAA7B,SAAS,CAAC3H,KAAK,cAAAwJ,iBAAA,eAAfA,iBAAA,CAAiBC,uBAAuB,CAACjB,UAAU,CAACL,IAAI,CAACuB,aAAa,CAAC,CAAC;MAC1E,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAI3J,KAAK,EAAK;MAAA,IAAA4J,iBAAA;MACnC,IAAI,CAACvC,MAAM,CAACrH,KAAK,EAAE,OAAO6G,SAAS,CAAC;QAAE1F,IAAI,EAAE,SAAS;QAAE0I,OAAO,EAAE;MAAc,CAAC,CAAC;MAChF,IAAMC,YAAY,GAAG;QACnBzC,MAAM,EAAEC,QAAQ,CAACtH,KAAK,CAACuI,EAAE;QACzBwB,QAAQ,EAAE1C,MAAM,CAACrH,KAAK;QACtBgK,WAAW,EAAEhK,KAAK;QAClB6H,QAAQ,EAAEA,QAAQ,CAAC7H;MACrB,CAAC;MACDiK,cAAc,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACN,YAAY,CAAC,CAAC;MACpE,IAAIzC,MAAM,CAACrH,KAAK,KAAK,UAAU,EAAEoH,QAAQ,CAAC;QAAEiD,GAAG,EAAE,WAAW;QAAErK,KAAK,EAAE;MAAkC,CAAC,CAAC;MACzG,IAAIqH,MAAM,CAACrH,KAAK,KAAK,QAAQ,EAAEoH,QAAQ,CAAC;QAAEiD,GAAG,EAAE,WAAW;QAAErK,KAAK,EAAE;MAAkC,CAAC,CAAC;MACvG,CAAA4J,iBAAA,GAAAjC,SAAS,CAAC3H,KAAK,cAAA4J,iBAAA,eAAfA,iBAAA,CAAiBvB,aAAa,CAAC,EAAE,CAAC;IACpC,CAAC;IACD,IAAMiC,oBAAoB;MAAA,IAAAC,KAAA,GAAAxE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8F,QAAA;QAAA,IAAAC,WAAA;QAAA,IAAAC,qBAAA,EAAA1C,IAAA;QAAA,OAAA1I,mBAAA,GAAAuB,IAAA,UAAA8J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAzF,IAAA,GAAAyF,QAAA,CAAApH,IAAA;YAAA;cAAAoH,QAAA,CAAApH,IAAA;cAAA,OACJ4C,GAAG,CAACkE,oBAAoB,CAAC;gBAAEO,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAA2B;cAAE,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAE,QAAA,CAAA3H,IAAA;cAAjG+E,IAAI,GAAA0C,qBAAA,CAAJ1C,IAAI;cACZT,QAAQ,CAACvH,KAAK,GAAG,CAAAgI,IAAI,aAAJA,IAAI,gBAAAyC,WAAA,GAAJzC,IAAI,CAAE+C,KAAK,cAAAN,WAAA,uBAAXA,WAAA,CAAanC,MAAM,CAAC,UAACtG,CAAC;gBAAA,OAAKA,CAAC,CAACgJ,OAAO;cAAA,EAAC,KAAI,EAAE;YAAA;YAAA;cAAA,OAAAJ,QAAA,CAAAtF,IAAA;UAAA;QAAA,GAAAkF,OAAA;MAAA,CAC7D;MAAA,gBAHKF,oBAAoBA,CAAA;QAAA,OAAAC,KAAA,CAAAtE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGzB;IACDQ,WAAW,CAAC,YAAM;MAChB8D,oBAAoB,CAAC,CAAC;MACtBrD,KAAK,CAACgE,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;IACxC,CAAC,CAAC;IACFxE,aAAa,CAAC,YAAM;MAClBQ,KAAK,CAACgE,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC;IACFvE,WAAW,CAAC,YAAM;MAChBO,KAAK,CAACgE,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}