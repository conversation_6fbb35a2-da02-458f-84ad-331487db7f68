{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport config from 'common/config/index';\nimport { ref, onActivated, onDeactivated, onUnmounted, watch } from 'vue';\nimport { useStore } from 'vuex';\nimport { fetchEventSource } from '@microsoft/fetch-event-source';\nvar __default__ = {\n  name: 'complianceReviewTopics'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var content = ref('');\n    var loading = ref(false);\n    var isRewrite = ref(false); // 是否改写\n    var startIndex = ref(0); // 进度条\n    var timer = ref(null); // 定时器\n    var reviewStaus = ref(''); // 审查状态\n    var resultContent = ref(''); //  生成的结果\n    var isFluid = ref(true); // 是否流式输出\n    var store = useStore();\n\n    // 监听content变化，无论是手动输入还是文件导入都会触发\n    watch(content, function (newValue) {\n      store.commit('setAiChatContent', newValue);\n    });\n    var handleFile = function handleFile(file) {\n      var fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();\n      var isShow = ['doc', 'docx'].includes(fileType);\n      if (!isShow) {\n        ElMessage({\n          type: 'warning',\n          message: `仅支持word格式!`\n        });\n      }\n      return isShow;\n    };\n    onActivated(function () {\n      store.commit('setAiChatWidth', window.innerWidth - 1280);\n      store.commit('setAiChatWindow', true);\n      store.commit('setAiChatCode', 'compliance_chat');\n      store.commit('setAiChatParams', {\n        filterHtml: '1'\n      });\n    });\n    onDeactivated(function () {\n      var width = window.innerWidth - 1280 > 520 ? 520 : 400;\n      store.commit('setAiChatWidth', width);\n      store.commit('setAiChatWindow', false);\n      store.commit('setAiChatCode', 'test_chat');\n      store.commit('setAiChatParams', {});\n      store.commit('setAiChatContent', '');\n    });\n    onUnmounted(function () {\n      var width = window.innerWidth - 1280 > 520 ? 520 : 400;\n      store.commit('setAiChatWidth', width);\n      store.commit('setAiChatWindow', false);\n      store.commit('setAiChatCode', 'test_chat');\n      store.commit('setAiChatParams', {});\n      store.commit('setAiChatContent', '');\n    });\n    var fileWordUpload = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(file) {\n        var param, _yield$api$fileword2h, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              reviewStaus.value = '';\n              content.value = '';\n              startIndex.value = 0;\n              _context.prev = 3;\n              param = new FormData();\n              param.append('file', file.file);\n              _context.next = 8;\n              return api.fileword2html(param);\n            case 8:\n              _yield$api$fileword2h = _context.sent;\n              data = _yield$api$fileword2h.data;\n              content.value = data.replace(/<\\/?html[^>]*>/g, '').replace(/<head\\b[^<]*(?:(?!<\\/head>)<[^<]*)*<\\/head>/gi, '').replace(/<\\/?body[^>]*>/g, '').replace(/<\\/?div[^>]*>/g, '');\n              loading.value = false;\n              _context.next = 17;\n              break;\n            case 14:\n              _context.prev = 14;\n              _context.t0 = _context[\"catch\"](3);\n              loading.value = false;\n            case 17:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[3, 14]]);\n      }));\n      return function fileWordUpload(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var ctrl = ref(new AbortController());\n    var startReview = function startReview() {\n      startIndex.value = 0;\n      startIndex.value++;\n      resultContent.value = '';\n      reviewStaus.value = 'progressing';\n      if (isFluid) getFetchEventSource();\n      if (!isFluid) chatStream();\n      timer.value = setInterval(function () {\n        startIndex.value++;\n        if (startIndex.value >= 98) {\n          clearInterval(timer.value);\n        }\n      }, 30);\n    };\n    var chatStream = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _yield$api$globalJson, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              _context2.next = 3;\n              return api.globalJson('/aigpt/chat', {\n                chatBusinessScene: 'compliance_chat',\n                chatId: 'compliance_id' + guid(),\n                question: content.value,\n                dataId: guid(),\n                tool: '',\n                attachmentIds: ''\n              });\n            case 3:\n              _yield$api$globalJson = _context2.sent;\n              data = _yield$api$globalJson.data;\n              reviewStaus.value = 'end';\n              startIndex.value = 100;\n              resultContent.value = (data.choices || []).length ? data.choices[0].message.content : '';\n              _context2.next = 15;\n              break;\n            case 10:\n              _context2.prev = 10;\n              _context2.t0 = _context2[\"catch\"](0);\n              clearInterval(timer.value);\n              reviewStaus.value = '';\n              startIndex.value = 0;\n            case 15:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 10]]);\n      }));\n      return function chatStream() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var getFetchEventSource = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var signal, token;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              ctrl.value.abort();\n              ctrl.value = new AbortController(); // 创建新的控制器\n              signal = ctrl.value.signal;\n              token = sessionStorage.getItem('token') || '';\n              _context3.next = 6;\n              return fetchEventSource(`${config.API_URL}/aigpt/chatStream`, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  authorization: token\n                },\n                body: JSON.stringify({\n                  chatBusinessScene: 'compliance_chat',\n                  chatId: 'compliance_id' + guid(),\n                  question: content.value,\n                  dataId: guid(),\n                  tool: '',\n                  attachmentIds: ''\n                }),\n                openWhenHidden: true,\n                // 取消visibilityChange事件\n                signal,\n                onmessage: function onmessage(res) {\n                  console.log('🚀 ~ getFetchEventSource ~ res:', res);\n                  if (res.data === '[DONE]') {\n                    reviewStaus.value = 'end';\n                    startIndex.value = 100;\n                  } else {\n                    var data = JSON.parse(res.data);\n                    resultContent.value += data.choices[0].delta.content;\n                  }\n                },\n                onclose: function onclose(data) {},\n                onerror: function onerror(err) {\n                  console.log(err);\n                  throw err;\n                }\n              });\n            case 6:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function getFetchEventSource() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var rewriteHanld = function rewriteHanld() {\n      isRewrite.value = true;\n    };\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var __returned__ = {\n      content,\n      loading,\n      isRewrite,\n      startIndex,\n      timer,\n      reviewStaus,\n      resultContent,\n      isFluid,\n      store,\n      handleFile,\n      fileWordUpload,\n      get ctrl() {\n        return ctrl;\n      },\n      set ctrl(v) {\n        ctrl = v;\n      },\n      startReview,\n      chatStream,\n      getFetchEventSource,\n      rewriteHanld,\n      guid,\n      get api() {\n        return api;\n      },\n      get config() {\n        return config;\n      },\n      ref,\n      onActivated,\n      onDeactivated,\n      onUnmounted,\n      watch,\n      get useStore() {\n        return useStore;\n      },\n      get fetchEventSource() {\n        return fetchEventSource;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "config", "ref", "onActivated", "onDeactivated", "onUnmounted", "watch", "useStore", "fetchEventSource", "__default__", "content", "loading", "isRewrite", "startIndex", "timer", "reviewStaus", "resultContent", "isFluid", "store", "newValue", "commit", "handleFile", "file", "fileType", "substring", "lastIndexOf", "toLowerCase", "isShow", "includes", "ElMessage", "message", "window", "innerWidth", "filterHtml", "width", "fileWordUpload", "_ref2", "_callee", "param", "_yield$api$fileword2h", "data", "_callee$", "_context", "FormData", "append", "fileword2html", "replace", "t0", "_x", "ctrl", "AbortController", "startReview", "getFetchEventSource", "chatStream", "setInterval", "clearInterval", "_ref3", "_callee2", "_yield$api$globalJson", "_callee2$", "_context2", "globalJson", "chatBusinessScene", "chatId", "guid", "question", "dataId", "tool", "attachmentIds", "choices", "_ref4", "_callee3", "signal", "token", "_callee3$", "_context3", "abort", "sessionStorage", "getItem", "API_URL", "headers", "authorization", "body", "JSON", "stringify", "openWhenHidden", "onmessage", "res", "console", "log", "parse", "delta", "onclose", "onerror", "err", "rewriteHanld", "Math", "random", "toString"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/complianceReviewTopics/complianceReviewTopics.vue"], "sourcesContent": ["<template>\r\n  <div class=\"complianceReviewTopics\">\r\n    <div class=\"complianceReviewTopicsLeft\">\r\n      <div class=\"buttonBox\">\r\n        <div class=\"complianceReviewTopicsLeftTitle\">{{ !isRewrite ? '内容编辑' : '原文内容' }}</div>\r\n        <el-upload\r\n          action=\"/\"\r\n          v-if=\"!isRewrite\"\r\n          :before-upload=\"handleFile\"\r\n          :http-request=\"fileWordUpload\"\r\n          :show-file-list=\"false\">\r\n          <el-button type=\"primary\">\r\n            <el-icon class=\"el-icon--right\">\r\n              <Download />\r\n            </el-icon>\r\n            文档导入\r\n          </el-button>\r\n        </el-upload>\r\n      </div>\r\n      <TinyMceEditor v-if=\"!isRewrite\" v-model=\"content\" />\r\n      <el-scrollbar class=\"rewriteBox\" v-else>\r\n        <div v-html=\"content\"></div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"complianceReviewTopicsRight\" v-if=\"false\">\r\n      <div class=\"buttonBox\">\r\n        <div class=\"complianceReviewTopicsLeftTitle\">符合性审查</div>\r\n        <div>\r\n          <el-button type=\"primary\" v-if=\"startIndex == 0\" :disabled=\"content == ''\" @click=\"startReview\">\r\n            开始审查\r\n          </el-button>\r\n          <el-button type=\"info\" link v-if=\"reviewStaus == 'progressing'\">正在检查中...</el-button>\r\n          <!-- <el-button v-if=\"reviewStaus == 'end'\">导出结果</el-button> -->\r\n          <el-button type=\"primary\" @click=\"startReview\" v-if=\"reviewStaus == 'end'\">重新生成</el-button>\r\n          <el-button type=\"primary\" v-if=\"reviewStaus == 'end'\" @click=\"rewriteHanld\">智能改写</el-button>\r\n        </div>\r\n      </div>\r\n      <div class=\"rightContent\">\r\n        <div class=\"emptyBox\" v-if=\"(startIndex != 100 && !isFluid) || startIndex == 0\">\r\n          <img v-if=\"reviewStaus == ''\" src=\"../img/complianceReviewTopics/empty.png\" alt=\"\" />\r\n          <img\r\n            v-if=\"reviewStaus == 'progressing' && !isFluid\"\r\n            src=\"../img/complianceReviewTopics/uploadIng.png\"\r\n            alt=\"\" />\r\n          <div v-if=\"reviewStaus == ''\">\r\n            请先在左侧输入内容或导入word文档\r\n            <br />\r\n            才可进行审查\r\n          </div>\r\n          <div v-if=\"reviewStaus == 'progressing' && !isFluid\" class=\"progressingText\">\r\n            正在进行检查，请耐心等待！\r\n            <br />\r\n            <span>{{ startIndex }}.0%</span>\r\n          </div>\r\n        </div>\r\n        <el-scrollbar class=\"resultBox\" v-if=\"reviewStaus == 'progressing' && isFluid\">\r\n          <span v-html=\"resultContent\"></span>\r\n          <span class=\"loader3\" v-if=\"reviewStaus == 'progressing' && isFluid\">\r\n            <div class=\"circle1\"></div>\r\n            <div class=\"circle1\"></div>\r\n            <div class=\"circle1\"></div>\r\n          </span>\r\n        </el-scrollbar>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'complianceReviewTopics' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport config from 'common/config/index'\r\nimport { ref, onActivated, onDeactivated, onUnmounted, watch } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { fetchEventSource } from '@microsoft/fetch-event-source'\r\nconst content = ref('')\r\nconst loading = ref(false)\r\nconst isRewrite = ref(false) // 是否改写\r\nconst startIndex = ref(0) // 进度条\r\nconst timer = ref(null) // 定时器\r\nconst reviewStaus = ref('') // 审查状态\r\nconst resultContent = ref('') //  生成的结果\r\nconst isFluid = ref(true) // 是否流式输出\r\nconst store = useStore()\r\n\r\n// 监听content变化，无论是手动输入还是文件导入都会触发\r\nwatch(content, (newValue) => {\r\n  store.commit('setAiChatContent', newValue)\r\n})\r\n\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()\r\n  const isShow = ['doc', 'docx'].includes(fileType)\r\n  if (!isShow) {\r\n    ElMessage({ type: 'warning', message: `仅支持word格式!` })\r\n  }\r\n  return isShow\r\n}\r\nonActivated(() => {\r\n  store.commit('setAiChatWidth', window.innerWidth - 1280)\r\n  store.commit('setAiChatWindow', true)\r\n  store.commit('setAiChatCode', 'compliance_chat')\r\n  store.commit('setAiChatParams', { filterHtml: '1' })\r\n})\r\nonDeactivated(() => {\r\n  const width = window.innerWidth - 1280 > 520 ? 520 : 400\r\n  store.commit('setAiChatWidth', width)\r\n  store.commit('setAiChatWindow', false)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatParams', {})\r\n  store.commit('setAiChatContent', '')\r\n})\r\nonUnmounted(() => {\r\n  const width = window.innerWidth - 1280 > 520 ? 520 : 400\r\n  store.commit('setAiChatWidth', width)\r\n  store.commit('setAiChatWindow', false)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatParams', {})\r\n  store.commit('setAiChatContent', '')\r\n})\r\nconst fileWordUpload = async (file) => {\r\n  reviewStaus.value = ''\r\n  content.value = ''\r\n  startIndex.value = 0\r\n  try {\r\n    const param = new FormData()\r\n    param.append('file', file.file)\r\n    const { data } = await api.fileword2html(param)\r\n    content.value = data\r\n      .replace(/<\\/?html[^>]*>/g, '')\r\n      .replace(/<head\\b[^<]*(?:(?!<\\/head>)<[^<]*)*<\\/head>/gi, '')\r\n      .replace(/<\\/?body[^>]*>/g, '')\r\n      .replace(/<\\/?div[^>]*>/g, '')\r\n    loading.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\nvar ctrl = ref(new AbortController())\r\nconst startReview = () => {\r\n  startIndex.value = 0\r\n  startIndex.value++\r\n  resultContent.value = ''\r\n  reviewStaus.value = 'progressing'\r\n  if (isFluid) getFetchEventSource()\r\n  if (!isFluid) chatStream()\r\n  timer.value = setInterval(() => {\r\n    startIndex.value++\r\n    if (startIndex.value >= 98) {\r\n      clearInterval(timer.value)\r\n    }\r\n  }, 30)\r\n}\r\n\r\nconst chatStream = async () => {\r\n  try {\r\n    const { data } = await api.globalJson('/aigpt/chat', {\r\n      chatBusinessScene: 'compliance_chat',\r\n      chatId: 'compliance_id' + guid(),\r\n      question: content.value,\r\n      dataId: guid(),\r\n      tool: '',\r\n      attachmentIds: ''\r\n    })\r\n    reviewStaus.value = 'end'\r\n    startIndex.value = 100\r\n    resultContent.value = (data.choices || []).length ? data.choices[0].message.content : ''\r\n  } catch (err) {\r\n    clearInterval(timer.value)\r\n    reviewStaus.value = ''\r\n    startIndex.value = 0\r\n  }\r\n}\r\n\r\nconst getFetchEventSource = async () => {\r\n  ctrl.value.abort()\r\n  ctrl.value = new AbortController() // 创建新的控制器\r\n  const { signal } = ctrl.value\r\n  const token = sessionStorage.getItem('token') || ''\r\n  await fetchEventSource(`${config.API_URL}/aigpt/chatStream`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      authorization: token\r\n    },\r\n    body: JSON.stringify({\r\n      chatBusinessScene: 'compliance_chat',\r\n      chatId: 'compliance_id' + guid(),\r\n      question: content.value,\r\n      dataId: guid(),\r\n      tool: '',\r\n      attachmentIds: ''\r\n    }),\r\n    openWhenHidden: true, // 取消visibilityChange事件\r\n    signal,\r\n    onmessage: (res) => {\r\n      console.log('🚀 ~ getFetchEventSource ~ res:', res)\r\n      if (res.data === '[DONE]') {\r\n        reviewStaus.value = 'end'\r\n        startIndex.value = 100\r\n      } else {\r\n        const data = JSON.parse(res.data)\r\n        resultContent.value += data.choices[0].delta.content\r\n      }\r\n    },\r\n    onclose: (data) => {},\r\n    onerror: (err) => {\r\n      console.log(err)\r\n      throw err\r\n    }\r\n  })\r\n}\r\nconst rewriteHanld = () => {\r\n  isRewrite.value = true\r\n}\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.complianceReviewTopics {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  background: #f4f4f4;\r\n\r\n  .buttonBox {\r\n    height: 56px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .complianceReviewTopicsLeftTitle {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .el-icon--right {\r\n      margin-right: 6px;\r\n    }\r\n  }\r\n\r\n  .complianceReviewTopicsLeft {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 10px;\r\n    background: #fff;\r\n\r\n    .rewriteBox {\r\n      height: calc(100% - 56px);\r\n      padding: 16px;\r\n    }\r\n\r\n    .TinyMceEditor {\r\n      height: calc(100% - 56px);\r\n\r\n      .tox-tinymce {\r\n        height: 100% !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .complianceReviewTopicsRight {\r\n    width: 49%;\r\n    height: 100%;\r\n    background: #fff;\r\n    padding: 10px;\r\n\r\n    .rightContent {\r\n      height: calc(100% - 56px);\r\n      border-top: 1px solid transparent;\r\n\r\n      .emptyBox {\r\n        margin-top: 300px;\r\n        text-align: center;\r\n        line-height: 26px;\r\n        user-select: none;\r\n\r\n        .progressingText {\r\n          color: #262626;\r\n          font-weight: 600;\r\n\r\n          span {\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n\r\n        img {\r\n          width: 60px;\r\n          margin-bottom: 20px;\r\n        }\r\n      }\r\n\r\n      .resultBox {\r\n        height: 100%;\r\n        padding: 16px;\r\n\r\n        .loader3 {\r\n          display: inline-flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n        }\r\n\r\n        .circle1 {\r\n          width: 4px;\r\n          height: 4px;\r\n          border-radius: 50%;\r\n          margin: 0 5px;\r\n          background-color: #333;\r\n          animation: circle1 1s ease-in-out infinite;\r\n        }\r\n\r\n        .circle1:nth-child(2) {\r\n          animation-delay: 0.33s;\r\n        }\r\n\r\n        .circle1:nth-child(3) {\r\n          animation-delay: 0.67s;\r\n        }\r\n\r\n        .circle1:nth-child(4) {\r\n          animation-delay: 0.6s;\r\n        }\r\n\r\n        .circle1:nth-child(5) {\r\n          animation-delay: 0.8s;\r\n        }\r\n\r\n        @keyframes circle1 {\r\n          0% {\r\n            transform: scale(1);\r\n            opacity: 1;\r\n          }\r\n\r\n          50% {\r\n            transform: scale(1.5);\r\n            opacity: 0.5;\r\n          }\r\n\r\n          100% {\r\n            transform: scale(1);\r\n            opacity: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAyEA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,GAAG,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,KAAK,QAAQ,KAAK;AACzE,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,gBAAgB,QAAQ,+BAA+B;AARhE,IAAAC,WAAA,GAAe;EAAEpC,IAAI,EAAE;AAAyB,CAAC;;;;;IASjD,IAAMqC,OAAO,GAAGR,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMS,OAAO,GAAGT,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMU,SAAS,GAAGV,GAAG,CAAC,KAAK,CAAC,EAAC;IAC7B,IAAMW,UAAU,GAAGX,GAAG,CAAC,CAAC,CAAC,EAAC;IAC1B,IAAMY,KAAK,GAAGZ,GAAG,CAAC,IAAI,CAAC,EAAC;IACxB,IAAMa,WAAW,GAAGb,GAAG,CAAC,EAAE,CAAC,EAAC;IAC5B,IAAMc,aAAa,GAAGd,GAAG,CAAC,EAAE,CAAC,EAAC;IAC9B,IAAMe,OAAO,GAAGf,GAAG,CAAC,IAAI,CAAC,EAAC;IAC1B,IAAMgB,KAAK,GAAGX,QAAQ,CAAC,CAAC;;IAExB;IACAD,KAAK,CAACI,OAAO,EAAE,UAACS,QAAQ,EAAK;MAC3BD,KAAK,CAACE,MAAM,CAAC,kBAAkB,EAAED,QAAQ,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B,IAAMC,QAAQ,GAAGD,IAAI,CAACjD,IAAI,CAACmD,SAAS,CAACF,IAAI,CAACjD,IAAI,CAACoD,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAClF,IAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAAC;MACjD,IAAI,CAACI,MAAM,EAAE;QACXE,SAAS,CAAC;UAAE9G,IAAI,EAAE,SAAS;UAAE+G,OAAO,EAAE;QAAa,CAAC,CAAC;MACvD;MACA,OAAOH,MAAM;IACf,CAAC;IACDxB,WAAW,CAAC,YAAM;MAChBe,KAAK,CAACE,MAAM,CAAC,gBAAgB,EAAEW,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC;MACxDd,KAAK,CAACE,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;MACrCF,KAAK,CAACE,MAAM,CAAC,eAAe,EAAE,iBAAiB,CAAC;MAChDF,KAAK,CAACE,MAAM,CAAC,iBAAiB,EAAE;QAAEa,UAAU,EAAE;MAAI,CAAC,CAAC;IACtD,CAAC,CAAC;IACF7B,aAAa,CAAC,YAAM;MAClB,IAAM8B,KAAK,GAAGH,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;MACxDd,KAAK,CAACE,MAAM,CAAC,gBAAgB,EAAEc,KAAK,CAAC;MACrChB,KAAK,CAACE,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;MACtCF,KAAK,CAACE,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC;MAC1CF,KAAK,CAACE,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;MACnCF,KAAK,CAACE,MAAM,CAAC,kBAAkB,EAAE,EAAE,CAAC;IACtC,CAAC,CAAC;IACFf,WAAW,CAAC,YAAM;MAChB,IAAM6B,KAAK,GAAGH,MAAM,CAACC,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;MACxDd,KAAK,CAACE,MAAM,CAAC,gBAAgB,EAAEc,KAAK,CAAC;MACrChB,KAAK,CAACE,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;MACtCF,KAAK,CAACE,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC;MAC1CF,KAAK,CAACE,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;MACnCF,KAAK,CAACE,MAAM,CAAC,kBAAkB,EAAE,EAAE,CAAC;IACtC,CAAC,CAAC;IACF,IAAMe,cAAc;MAAA,IAAAC,KAAA,GAAAzC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+D,QAAOf,IAAI;QAAA,IAAAgB,KAAA,EAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAAtJ,mBAAA,GAAAuB,IAAA,UAAAgI,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA3D,IAAA,GAAA2D,QAAA,CAAAtF,IAAA;YAAA;cAChC2D,WAAW,CAACnH,KAAK,GAAG,EAAE;cACtB8G,OAAO,CAAC9G,KAAK,GAAG,EAAE;cAClBiH,UAAU,CAACjH,KAAK,GAAG,CAAC;cAAA8I,QAAA,CAAA3D,IAAA;cAEZuD,KAAK,GAAG,IAAIK,QAAQ,CAAC,CAAC;cAC5BL,KAAK,CAACM,MAAM,CAAC,MAAM,EAAEtB,IAAI,CAACA,IAAI,CAAC;cAAAoB,QAAA,CAAAtF,IAAA;cAAA,OACR4C,GAAG,CAAC6C,aAAa,CAACP,KAAK,CAAC;YAAA;cAAAC,qBAAA,GAAAG,QAAA,CAAA7F,IAAA;cAAvC2F,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZ9B,OAAO,CAAC9G,KAAK,GAAG4I,IAAI,CACjBM,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAC9BA,OAAO,CAAC,+CAA+C,EAAE,EAAE,CAAC,CAC5DA,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAC9BA,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;cAChCnC,OAAO,CAAC/G,KAAK,GAAG,KAAK;cAAA8I,QAAA,CAAAtF,IAAA;cAAA;YAAA;cAAAsF,QAAA,CAAA3D,IAAA;cAAA2D,QAAA,CAAAK,EAAA,GAAAL,QAAA;cAErB/B,OAAO,CAAC/G,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAA8I,QAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAmD,OAAA;MAAA,CAExB;MAAA,gBAjBKF,cAAcA,CAAAa,EAAA;QAAA,OAAAZ,KAAA,CAAAvC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAiBnB;IAED,IAAIqD,IAAI,GAAG/C,GAAG,CAAC,IAAIgD,eAAe,CAAC,CAAC,CAAC;IACrC,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBtC,UAAU,CAACjH,KAAK,GAAG,CAAC;MACpBiH,UAAU,CAACjH,KAAK,EAAE;MAClBoH,aAAa,CAACpH,KAAK,GAAG,EAAE;MACxBmH,WAAW,CAACnH,KAAK,GAAG,aAAa;MACjC,IAAIqH,OAAO,EAAEmC,mBAAmB,CAAC,CAAC;MAClC,IAAI,CAACnC,OAAO,EAAEoC,UAAU,CAAC,CAAC;MAC1BvC,KAAK,CAAClH,KAAK,GAAG0J,WAAW,CAAC,YAAM;QAC9BzC,UAAU,CAACjH,KAAK,EAAE;QAClB,IAAIiH,UAAU,CAACjH,KAAK,IAAI,EAAE,EAAE;UAC1B2J,aAAa,CAACzC,KAAK,CAAClH,KAAK,CAAC;QAC5B;MACF,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IAED,IAAMyJ,UAAU;MAAA,IAAAG,KAAA,GAAA7D,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAmF,SAAA;QAAA,IAAAC,qBAAA,EAAAlB,IAAA;QAAA,OAAAtJ,mBAAA,GAAAuB,IAAA,UAAAkJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7E,IAAA,GAAA6E,SAAA,CAAAxG,IAAA;YAAA;cAAAwG,SAAA,CAAA7E,IAAA;cAAA6E,SAAA,CAAAxG,IAAA;cAAA,OAEQ4C,GAAG,CAAC6D,UAAU,CAAC,aAAa,EAAE;gBACnDC,iBAAiB,EAAE,iBAAiB;gBACpCC,MAAM,EAAE,eAAe,GAAGC,IAAI,CAAC,CAAC;gBAChCC,QAAQ,EAAEvD,OAAO,CAAC9G,KAAK;gBACvBsK,MAAM,EAAEF,IAAI,CAAC,CAAC;gBACdG,IAAI,EAAE,EAAE;gBACRC,aAAa,EAAE;cACjB,CAAC,CAAC;YAAA;cAAAV,qBAAA,GAAAE,SAAA,CAAA/G,IAAA;cAPM2F,IAAI,GAAAkB,qBAAA,CAAJlB,IAAI;cAQZzB,WAAW,CAACnH,KAAK,GAAG,KAAK;cACzBiH,UAAU,CAACjH,KAAK,GAAG,GAAG;cACtBoH,aAAa,CAACpH,KAAK,GAAG,CAAC4I,IAAI,CAAC6B,OAAO,IAAI,EAAE,EAAEpG,MAAM,GAAGuE,IAAI,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACvC,OAAO,CAACpB,OAAO,GAAG,EAAE;cAAAkD,SAAA,CAAAxG,IAAA;cAAA;YAAA;cAAAwG,SAAA,CAAA7E,IAAA;cAAA6E,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAExFL,aAAa,CAACzC,KAAK,CAAClH,KAAK,CAAC;cAC1BmH,WAAW,CAACnH,KAAK,GAAG,EAAE;cACtBiH,UAAU,CAACjH,KAAK,GAAG,CAAC;YAAA;YAAA;cAAA,OAAAgK,SAAA,CAAA1E,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA,CAEvB;MAAA,gBAlBKJ,UAAUA,CAAA;QAAA,OAAAG,KAAA,CAAA3D,KAAA,OAAAD,SAAA;MAAA;IAAA,GAkBf;IAED,IAAMwD,mBAAmB;MAAA,IAAAkB,KAAA,GAAA3E,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiG,SAAA;QAAA,IAAAC,MAAA,EAAAC,KAAA;QAAA,OAAAvL,mBAAA,GAAAuB,IAAA,UAAAiK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5F,IAAA,GAAA4F,SAAA,CAAAvH,IAAA;YAAA;cAC1B6F,IAAI,CAACrJ,KAAK,CAACgL,KAAK,CAAC,CAAC;cAClB3B,IAAI,CAACrJ,KAAK,GAAG,IAAIsJ,eAAe,CAAC,CAAC,EAAC;cAC3BsB,MAAM,GAAKvB,IAAI,CAACrJ,KAAK,CAArB4K,MAAM;cACRC,KAAK,GAAGI,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;cAAAH,SAAA,CAAAvH,IAAA;cAAA,OAC7CoD,gBAAgB,CAAC,GAAGP,MAAM,CAAC8E,OAAO,mBAAmB,EAAE;gBAC3DrI,MAAM,EAAE,MAAM;gBACdsI,OAAO,EAAE;kBACP,cAAc,EAAE,kBAAkB;kBAClCC,aAAa,EAAER;gBACjB,CAAC;gBACDS,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;kBACnBtB,iBAAiB,EAAE,iBAAiB;kBACpCC,MAAM,EAAE,eAAe,GAAGC,IAAI,CAAC,CAAC;kBAChCC,QAAQ,EAAEvD,OAAO,CAAC9G,KAAK;kBACvBsK,MAAM,EAAEF,IAAI,CAAC,CAAC;kBACdG,IAAI,EAAE,EAAE;kBACRC,aAAa,EAAE;gBACjB,CAAC,CAAC;gBACFiB,cAAc,EAAE,IAAI;gBAAE;gBACtBb,MAAM;gBACNc,SAAS,EAAE,SAAXA,SAASA,CAAGC,GAAG,EAAK;kBAClBC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,GAAG,CAAC;kBACnD,IAAIA,GAAG,CAAC/C,IAAI,KAAK,QAAQ,EAAE;oBACzBzB,WAAW,CAACnH,KAAK,GAAG,KAAK;oBACzBiH,UAAU,CAACjH,KAAK,GAAG,GAAG;kBACxB,CAAC,MAAM;oBACL,IAAM4I,IAAI,GAAG2C,IAAI,CAACO,KAAK,CAACH,GAAG,CAAC/C,IAAI,CAAC;oBACjCxB,aAAa,CAACpH,KAAK,IAAI4I,IAAI,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACsB,KAAK,CAACjF,OAAO;kBACtD;gBACF,CAAC;gBACDkF,OAAO,EAAE,SAATA,OAAOA,CAAGpD,IAAI,EAAK,CAAC,CAAC;gBACrBqD,OAAO,EAAE,SAATA,OAAOA,CAAGC,GAAG,EAAK;kBAChBN,OAAO,CAACC,GAAG,CAACK,GAAG,CAAC;kBAChB,MAAMA,GAAG;gBACX;cACF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAnB,SAAA,CAAAzF,IAAA;UAAA;QAAA,GAAAqF,QAAA;MAAA,CACH;MAAA,gBArCKnB,mBAAmBA,CAAA;QAAA,OAAAkB,KAAA,CAAAzE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqCxB;IACD,IAAMmG,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBnF,SAAS,CAAChH,KAAK,GAAG,IAAI;IACxB,CAAC;IACD,IAAMoK,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAAClB,OAAO,CAAC,OAAO,EAAE,UAAC7I,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAI2M,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9BrK,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAACsK,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}