{"ast": null, "code": "import { computed, defineAsyncComponent } from 'vue';\nvar __default__ = {\n  name: 'GlobalAiChatData'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var GlobalMarkdown = defineAsyncComponent(function () {\n      return import('common/components/global-markdown/global-markdown.vue');\n    });\n    var props = __props;\n    var details = computed(function () {\n      return props.data;\n    });\n    var __returned__ = {\n      GlobalMarkdown,\n      props,\n      details,\n      computed,\n      defineAsyncComponent\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["computed", "defineAsyncComponent", "__default__", "name", "GlobalMarkdown", "props", "__props", "details", "data"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalAiChat/GlobalAiChatData.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChatData\">\r\n    <GlobalMarkdown :content=\"details.markdownContent\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChatData' }\r\n</script>\r\n<script setup>\r\nimport { computed, defineAsyncComponent } from 'vue'\r\nconst GlobalMarkdown = defineAsyncComponent(() => import('common/components/global-markdown/global-markdown.vue'))\r\nconst props = defineProps({ data: { type: Object, default: () => ({}) } })\r\nconst details = computed(() => props.data)\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiChatData {\r\n  width: 680px;\r\n  height: calc(85vh - 52px);\r\n  padding: 20px 40px;\r\n}\r\n</style>\r\n"], "mappings": "AASA,SAASA,QAAQ,EAAEC,oBAAoB,QAAQ,KAAK;AAHpD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAmB,CAAC;;;;;;;;;;;;;IAI3C,IAAMC,cAAc,GAAGH,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,uDAAuD,CAAC;IAAA,EAAC;IAClH,IAAMI,KAAK,GAAGC,OAA4D;IAC1E,IAAMC,OAAO,GAAGP,QAAQ,CAAC;MAAA,OAAMK,KAAK,CAACG,IAAI;IAAA,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}