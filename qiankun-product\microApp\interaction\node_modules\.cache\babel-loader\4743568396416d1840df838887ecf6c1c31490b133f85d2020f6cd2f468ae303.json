{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nimport api from '@/api';\nimport { reactive, ref, onMounted } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { ElMessage } from 'element-plus';\nimport GlobalCropper from 'common/components/global-cropper/global-cropper.vue';\nvar __default__ = {\n  name: 'CreateVideoMeeting'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var formRef = ref();\n    var form = reactive({\n      theme: '',\n      startDate: '',\n      startTime: '',\n      duration: '',\n      isLive: 0,\n      // 是否开启直播\n      joinUserIds: [],\n      liveDescribes: '',\n      // 直播简介\n      liveUrl: '',\n      // 直播推流地址\n      liveReplayUrl: '',\n      // 直播回放地址\n      isReplay: 0,\n      // 是否开启直播回放\n      isPublic: 0,\n      // 是否推送群众端查看\n      isInteraction: 1,\n      // 是否开启直播互动\n      coverImg: '',\n      // 封面必填校验\n      liveNumber: '' // 直播设备号\n    });\n    var imgFileRef = ref();\n    var imgParams = ref({\n      imgFileName: '',\n      imgFileData: []\n    });\n    var cropperFile = ref({});\n    var cropperShow = ref(false);\n    var cropperCallback = ref();\n    var rules = reactive({\n      theme: [{\n        required: true,\n        message: '请输入会议主题',\n        trigger: ['blur', 'change']\n      }],\n      startTime: [{\n        required: true,\n        message: '请选择会议开始时间',\n        trigger: ['blur', 'change']\n      }],\n      duration: [{\n        required: true,\n        message: '请选择会议时长',\n        trigger: ['blur', 'change']\n      }],\n      joinUserIds: [{\n        required: true,\n        message: '请选择参会人员',\n        trigger: ['blur', 'change']\n      }],\n      liveDescribes: [{\n        required: true,\n        message: '请输入直播简介',\n        trigger: ['blur', 'change']\n      }],\n      coverImg: [{\n        required: true,\n        message: '请上传封面',\n        trigger: ['blur', 'change']\n      }]\n    });\n    var disabledDate = function disabledDate(time) {\n      return time.getTime() < Date.now() - 8.64e7;\n    };\n    var VideoMeetingTime = ref([]);\n    /**\r\n     * 处理自动生成的推荐日期\r\n     */\n    var getStartTime = function getStartTime() {\n      var newDate = new Date().getTime();\n      var _format$split = format(newDate, 'HH:mm').split(':'),\n        _format$split2 = _slicedToArray(_format$split, 2),\n        hours = _format$split2[0],\n        minutes = _format$split2[1]; // 处理时分\n      if (parseInt(minutes) >= 0 && parseInt(minutes) < 15) {\n        minutes = '15';\n      } else if (parseInt(minutes) >= 15 && parseInt(minutes) < 30) {\n        minutes = '30';\n      } else if (parseInt(minutes) >= 30 && parseInt(minutes) < 45) {\n        minutes = '45';\n      } else if (parseInt(minutes) >= 45 && parseInt(minutes) < 60) {\n        minutes = '00';\n        hours = parseInt(hours) + 1 > 23 ? '00' : (parseInt(hours) + 1).toString();\n        if (parseInt(hours) + 1 > 23) {\n          newDate = newDate + 24 * 60 * 60 * 1000;\n        }\n      }\n      form.startDate = format(newDate, 'YYYY-MM-DD'); // 处理日期\n      form.startTime = hours + ':' + minutes; // 处理时间\n    };\n    onMounted(function () {\n      getStartTime();\n      dictionaryData();\n      if (props.id) {\n        videoConnectionInfo();\n      }\n    });\n    var dictionaryData = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$dictionary, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.dictionaryData({\n                dictCodes: ['video_meeting_time']\n              });\n            case 2:\n              _yield$api$dictionary = _context.sent;\n              data = _yield$api$dictionary.data;\n              VideoMeetingTime.value = data.video_meeting_time || [];\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function dictionaryData() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var videoConnectionInfo = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _data$joinUsers;\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.videoConnectionInfo({\n                detailId: props.id\n              });\n            case 2:\n              res = _context2.sent;\n              data = res.data;\n              form.theme = data.theme;\n              form.duration = String(data.during);\n              form.isLive = data.isLive;\n              form.startDate = format(data.startTime, 'YYYY-MM-DD');\n              form.startTime = format(data.startTime, 'HH:mm');\n              form.joinUserIds = (_data$joinUsers = data.joinUsers) === null || _data$joinUsers === void 0 ? void 0 : _data$joinUsers.map(function (v) {\n                return v.userId;\n              });\n              form.liveDescribes = data.liveDescribes;\n              form.liveUrl = data.liveUrl;\n              form.liveReplayUrl = data.liveReplayUrl;\n              form.isReplay = data.isReplay;\n              form.isPublic = data.isPublic;\n              form.isInteraction = data.isInteraction;\n              form.coverImg = data.coverImg;\n              form.liveNumber = data.liveNumber;\n            case 18:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function videoConnectionInfo() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var userCallback = function userCallback() {\n      if (formRef.value) {\n        formRef.value.validateField('memberUserIds');\n      }\n    };\n    var submitForm = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(formEl) {\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              if (formEl) {\n                _context3.next = 2;\n                break;\n              }\n              return _context3.abrupt(\"return\");\n            case 2:\n              _context3.next = 4;\n              return formEl.validate(function (valid, fields) {\n                if (valid) {\n                  globalJson();\n                } else {\n                  ElMessage({\n                    type: 'warning',\n                    message: '请根据提示信息完善字段内容！'\n                  });\n                }\n              });\n            case 4:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function submitForm(_x) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var globalJson = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _yield$api$globalJson, code;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.globalJson(props.id ? '/videoConnection/edit' : '/videoConnection/add', {\n                form: {\n                  id: props.id,\n                  theme: form.theme,\n                  during: form.duration,\n                  isLive: form.isLive,\n                  startTime: new Date(form.startDate + ' ' + form.startTime).getTime(),\n                  endTime: new Date(form.startDate + ' ' + form.startTime).getTime() + 60000 * Number(form.duration),\n                  liveDescribes: form.liveDescribes,\n                  liveUrl: form.liveUrl,\n                  liveReplayUrl: form.liveReplayUrl,\n                  isReplay: form.isReplay,\n                  isPublic: form.isPublic,\n                  isInteraction: form.isInteraction,\n                  coverImg: form.livecoverImgDescribes,\n                  liveNumber: form.liveNumber\n                },\n                joinUserIds: form.joinUserIds\n              });\n            case 2:\n              _yield$api$globalJson = _context4.sent;\n              code = _yield$api$globalJson.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: props.id ? '编辑成功' : '新增成功'\n                });\n                emit('callback');\n              }\n            case 5:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function globalJson() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var resetForm = function resetForm() {\n      emit('callback');\n    };\n    var handleBeforeUpload = function handleBeforeUpload(file, callbackFn) {\n      cropperFile.value = file;\n      cropperShow.value = true;\n      cropperCallback.value = callbackFn;\n    };\n    var handleCropper = function handleCropper(file) {\n      cropperShow.value = false;\n      if (file) cropperCallback.value(file);\n    };\n    var imgGlobalUpload = function imgGlobalUpload(file) {\n      form.imgFileFlag = '';\n      if (file.newFileName) {\n        imgParams.value.imgFileName = file.newFileName;\n        form.imgFileFlag = '1';\n      } else {\n        imgParams.value.imgFileName = '';\n      }\n      formRef.value.validateField('imgFileFlag');\n    };\n    var __returned__ = {\n      props,\n      emit,\n      formRef,\n      form,\n      imgFileRef,\n      imgParams,\n      cropperFile,\n      cropperShow,\n      cropperCallback,\n      rules,\n      disabledDate,\n      VideoMeetingTime,\n      getStartTime,\n      dictionaryData,\n      videoConnectionInfo,\n      userCallback,\n      submitForm,\n      globalJson,\n      resetForm,\n      handleBeforeUpload,\n      handleCropper,\n      imgGlobalUpload,\n      get api() {\n        return api;\n      },\n      reactive,\n      ref,\n      onMounted,\n      get format() {\n        return format;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      GlobalCropper\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "api", "reactive", "ref", "onMounted", "format", "ElMessage", "GlobalCropper", "__default__", "props", "__props", "emit", "__emit", "formRef", "form", "theme", "startDate", "startTime", "duration", "isLive", "joinUserIds", "liveDescribes", "liveUrl", "liveReplayUrl", "isReplay", "isPublic", "isInteraction", "coverImg", "liveNumber", "imgFileRef", "imgParams", "imgFileName", "imgFileData", "cropperFile", "cropperShow", "cropperCallback", "rules", "required", "message", "trigger", "disabledDate", "time", "getTime", "Date", "now", "VideoMeetingTime", "getStartTime", "newDate", "_format$split", "split", "_format$split2", "hours", "minutes", "parseInt", "dictionaryData", "id", "videoConnectionInfo", "_ref2", "_callee", "_yield$api$dictionary", "data", "_callee$", "_context", "dictCodes", "video_meeting_time", "_ref3", "_callee2", "_data$joinUsers", "res", "_callee2$", "_context2", "detailId", "String", "during", "joinUsers", "map", "userId", "userCallback", "validateField", "submitForm", "_ref4", "_callee3", "formEl", "_callee3$", "_context3", "validate", "valid", "fields", "globalJson", "_x", "_ref5", "_callee4", "_yield$api$globalJson", "code", "_callee4$", "_context4", "endTime", "Number", "livecoverImgDescribes", "resetForm", "handleBeforeUpload", "file", "callbackFn", "handleCropper", "imgGlobalUpload", "imgFileFlag", "newFileName"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/VideoMeeting/component/CreateVideoMeeting.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CreateVideoMeeting\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"会议主题\" prop=\"theme\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.theme\" placeholder=\"请输入会议主题\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"会议开始时间\" prop=\"startTime\" class=\"InitVideoMeetingTime\">\r\n        <xyl-date-picker v-model=\"form.startDate\" type=\"date\" :disabled-date=\"disabledDate\" placeholder=\"请选择\"\r\n          value-format=\"YYYY-MM-DD\" format=\"YYYY-MM-DD\" :teleported=\"false\" :clearable=\"false\" />\r\n        <el-time-select v-model=\"form.startTime\" start=\"00:00\" step=\"00:15\" end=\"23:45\" placeholder=\"请选择\"\r\n          :teleported=\"false\" :clearable=\"false\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"会议时长\" prop=\"duration\">\r\n        <el-select v-model=\"form.duration\" placeholder=\"请选择会议时长\" :teleported=\"false\" clearable>\r\n          <el-option v-for=\"item in VideoMeetingTime\" :key=\"item.key\" :label=\"item.name\" :value=\"item.key\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"是否开启直播\">\r\n        <el-radio-group v-model=\"form.isLive\">\r\n          <el-radio :label=\"1\">是</el-radio>\r\n          <el-radio :label=\"0\">否</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n      <el-form-item label=\"参会人员\" prop=\"joinUserIds\" class=\"globalFormTitle\">\r\n        <business-select-person v-model=\"form.joinUserIds\" @callback=\"userCallback\"></business-select-person>\r\n      </el-form-item>\r\n      <template v-if=\"form.isLive === 1\">\r\n        <div class=\"globalFormName\">直播相关配置</div>\r\n        <el-form-item label=\"直播简介\" prop=\"liveDescribes\" class=\"globalFormTitle\">\r\n          <el-input v-model=\"form.liveDescribes\" type=\"textarea\" placeholder=\"请输入直播简介\" clearable rows=\"4\"\r\n            :disabled=\"props.id != ''\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"直播推流地址\" class=\"globalFormTitle\">\r\n          <el-input v-model=\"form.liveUrl\" placeholder=\"请输入直播推流地址\" clearable :disabled=\"props.id != ''\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"直播回放地址\" class=\"globalFormTitle\">\r\n          <el-input v-model=\"form.liveReplayUrl\" placeholder=\"请输入直播回放地址\" clearable :disabled=\"props.id != ''\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否开启直播回放\">\r\n          <el-radio-group v-model=\"form.isReplay\" :disabled=\"props.id != ''\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否推送群众端查看\">\r\n          <el-radio-group v-model=\"form.isPublic\" :disabled=\"props.id != ''\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否开启直播互动\">\r\n          <el-radio-group v-model=\"form.isInteraction\" :disabled=\"props.id != ''\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item prop=\"imgFileFlag\">\r\n          <template #label>\r\n            上传封面\r\n            <span class=\"label-gary-tips\"> (建议宽高尺寸比为16:9) </span>\r\n          </template>\r\n          <div>\r\n            <div class=\"informationDetailsImgBox\">\r\n              <xyl-upload-img :max=\"1\" ref=\"imgFileRef\" :fileId=\"imgParams.imgFileName\"\r\n                :fileData=\"imgParams.imgFileData\" :beforeUpload=\"handleBeforeUpload\" @fileUpload=\"imgGlobalUpload\"\r\n                height=\"90px\" :disabled=\"props.id != ''\" />\r\n            </div>\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item label=\"直播设备号\">\r\n          <el-input v-model=\"form.liveNumber\" placeholder=\"请输入直播设备号\" clearable :disabled=\"props.id != ''\" />\r\n        </el-form-item>\r\n      </template>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n  <xyl-popup-window v-model=\"cropperShow\" name=\"封面\">\r\n    <global-cropper :enlarge=\"3\" :width=\"375\" :height=\"210\" :cropperStyle=\"{ width: '520px', height: '360px' }\"\r\n      :file=\"cropperFile\" @callback=\"handleCropper\"></global-cropper>\r\n  </xyl-popup-window>\r\n</template>\r\n<script>\r\nexport default { name: 'CreateVideoMeeting' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalCropper from 'common/components/global-cropper/global-cropper.vue'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  theme: '',\r\n  startDate: '',\r\n  startTime: '',\r\n  duration: '',\r\n  isLive: 0, // 是否开启直播\r\n  joinUserIds: [],\r\n  liveDescribes: '', // 直播简介\r\n  liveUrl: '', // 直播推流地址\r\n  liveReplayUrl: '', // 直播回放地址\r\n  isReplay: 0, // 是否开启直播回放\r\n  isPublic: 0, // 是否推送群众端查看\r\n  isInteraction: 1, // 是否开启直播互动\r\n  coverImg: '', // 封面必填校验\r\n  liveNumber: '', // 直播设备号\r\n})\r\nconst imgFileRef = ref()\r\nconst imgParams = ref({\r\n  imgFileName: '',\r\n  imgFileData: []\r\n})\r\nconst cropperFile = ref({})\r\nconst cropperShow = ref(false)\r\nconst cropperCallback = ref()\r\nconst rules = reactive({\r\n  theme: [{ required: true, message: '请输入会议主题', trigger: ['blur', 'change'] }],\r\n  startTime: [{ required: true, message: '请选择会议开始时间', trigger: ['blur', 'change'] }],\r\n  duration: [{ required: true, message: '请选择会议时长', trigger: ['blur', 'change'] }],\r\n  joinUserIds: [{ required: true, message: '请选择参会人员', trigger: ['blur', 'change'] }],\r\n  liveDescribes: [{ required: true, message: '请输入直播简介', trigger: ['blur', 'change'] }],\r\n  coverImg: [{ required: true, message: '请上传封面', trigger: ['blur', 'change'] }],\r\n})\r\nconst disabledDate = (time) => time.getTime() < Date.now() - 8.64e7\r\nconst VideoMeetingTime = ref([])\r\n/**\r\n * 处理自动生成的推荐日期\r\n */\r\nconst getStartTime = () => {\r\n  var newDate = new Date().getTime()\r\n  var [hours, minutes] = format(newDate, 'HH:mm').split(':') // 处理时分\r\n  if (parseInt(minutes) >= 0 && parseInt(minutes) < 15) {\r\n    minutes = '15'\r\n  } else if (parseInt(minutes) >= 15 && parseInt(minutes) < 30) {\r\n    minutes = '30'\r\n  } else if (parseInt(minutes) >= 30 && parseInt(minutes) < 45) {\r\n    minutes = '45'\r\n  } else if (parseInt(minutes) >= 45 && parseInt(minutes) < 60) {\r\n    minutes = '00'\r\n    hours = (parseInt(hours) + 1) > 23 ? '00' : (parseInt(hours) + 1).toString()\r\n    if ((parseInt(hours) + 1) > 23) {\r\n      newDate = newDate + 24 * 60 * 60 * 1000\r\n    }\r\n  }\r\n  form.startDate = format(newDate, 'YYYY-MM-DD') // 处理日期\r\n  form.startTime = hours + ':' + minutes // 处理时间\r\n}\r\nonMounted(() => {\r\n  getStartTime()\r\n  dictionaryData()\r\n  if (props.id) { videoConnectionInfo() }\r\n})\r\nconst dictionaryData = async () => {\r\n  const { data } = await api.dictionaryData({ dictCodes: ['video_meeting_time'] })\r\n  VideoMeetingTime.value = data.video_meeting_time || []\r\n}\r\nconst videoConnectionInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  form.theme = data.theme\r\n  form.duration = String(data.during)\r\n  form.isLive = data.isLive\r\n  form.startDate = format(data.startTime, 'YYYY-MM-DD')\r\n  form.startTime = format(data.startTime, 'HH:mm')\r\n  form.joinUserIds = data.joinUsers?.map(v => v.userId)\r\n  form.liveDescribes = data.liveDescribes\r\n  form.liveUrl = data.liveUrl\r\n  form.liveReplayUrl = data.liveReplayUrl\r\n  form.isReplay = data.isReplay\r\n  form.isPublic = data.isPublic\r\n  form.isInteraction = data.isInteraction\r\n  form.coverImg = data.coverImg\r\n  form.liveNumber = data.liveNumber\r\n}\r\nconst userCallback = () => {\r\n  if (formRef.value) {\r\n    formRef.value.validateField('memberUserIds')\r\n  }\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson(props.id ? '/videoConnection/edit' : '/videoConnection/add', {\r\n    form: {\r\n      id: props.id,\r\n      theme: form.theme,\r\n      during: form.duration,\r\n      isLive: form.isLive,\r\n      startTime: new Date(form.startDate + ' ' + form.startTime).getTime(),\r\n      endTime: new Date(form.startDate + ' ' + form.startTime).getTime() + (60000 * Number(form.duration)),\r\n      liveDescribes: form.liveDescribes,\r\n      liveUrl: form.liveUrl,\r\n      liveReplayUrl: form.liveReplayUrl,\r\n      isReplay: form.isReplay,\r\n      isPublic: form.isPublic,\r\n      isInteraction: form.isInteraction,\r\n      coverImg: form.livecoverImgDescribes,\r\n      liveNumber: form.liveNumber\r\n    },\r\n    joinUserIds: form.joinUserIds\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    emit('callback')\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback') }\r\nconst handleBeforeUpload = (file, callbackFn) => {\r\n  cropperFile.value = file\r\n  cropperShow.value = true\r\n  cropperCallback.value = callbackFn\r\n}\r\nconst handleCropper = (file) => {\r\n  cropperShow.value = false\r\n  if (file) cropperCallback.value(file)\r\n}\r\nconst imgGlobalUpload = (file) => {\r\n  form.imgFileFlag = ''\r\n  if (file.newFileName) {\r\n    imgParams.value.imgFileName = file.newFileName\r\n    form.imgFileFlag = '1'\r\n  } else {\r\n    imgParams.value.imgFileName = ''\r\n  }\r\n  formRef.value.validateField('imgFileFlag')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.CreateVideoMeeting {\r\n  width: 990px;\r\n\r\n  .InitVideoMeetingTime {\r\n    width: calc(580px + var(--zy-distance-two));\r\n\r\n    .zy-el-form-item__content {\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAyFA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AAAA,SAAAE,eAAA3G,CAAA,EAAAF,CAAA,WAAA8G,eAAA,CAAA5G,CAAA,KAAA6G,qBAAA,CAAA7G,CAAA,EAAAF,CAAA,KAAAgH,2BAAA,CAAA9G,CAAA,EAAAF,CAAA,KAAAiH,gBAAA;AAAA,SAAAA,iBAAA,cAAAlD,SAAA;AAAA,SAAAiD,4BAAA9G,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAAgH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAAkH,QAAA,CAAArF,IAAA,CAAA5B,CAAA,EAAA4F,KAAA,6BAAA7F,CAAA,IAAAC,CAAA,CAAA+E,WAAA,KAAAhF,CAAA,GAAAC,CAAA,CAAA+E,WAAA,CAAAC,IAAA,aAAAjF,CAAA,cAAAA,CAAA,GAAAmH,KAAA,CAAAC,IAAA,CAAAnH,CAAA,oBAAAD,CAAA,+CAAAqH,IAAA,CAAArH,CAAA,IAAAiH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA;AAAA,SAAAsG,kBAAAhH,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA4E,MAAA,MAAAlE,CAAA,GAAAV,CAAA,CAAA4E,MAAA,YAAA9E,CAAA,MAAAK,CAAA,GAAA+G,KAAA,CAAAxG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AAAA,SAAA0G,sBAAA7G,CAAA,EAAA8B,CAAA,QAAA/B,CAAA,WAAAC,CAAA,gCAAAS,MAAA,IAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,KAAAX,CAAA,4BAAAD,CAAA,QAAAD,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAM,CAAA,EAAAJ,CAAA,OAAAqB,CAAA,OAAA1B,CAAA,iBAAAG,CAAA,IAAAT,CAAA,GAAAA,CAAA,CAAA6B,IAAA,CAAA5B,CAAA,GAAA+D,IAAA,QAAAjC,CAAA,QAAA7B,MAAA,CAAAF,CAAA,MAAAA,CAAA,UAAAgC,CAAA,uBAAAA,CAAA,IAAAjC,CAAA,GAAAU,CAAA,CAAAoB,IAAA,CAAA7B,CAAA,GAAAqD,IAAA,MAAA1C,CAAA,CAAA6D,IAAA,CAAAzE,CAAA,CAAAS,KAAA,GAAAG,CAAA,CAAAkE,MAAA,KAAA9C,CAAA,GAAAC,CAAA,iBAAA/B,CAAA,IAAAK,CAAA,OAAAF,CAAA,GAAAH,CAAA,yBAAA+B,CAAA,YAAAhC,CAAA,CAAA6D,MAAA,KAAA9C,CAAA,GAAAf,CAAA,CAAA6D,MAAA,IAAA3D,MAAA,CAAAa,CAAA,MAAAA,CAAA,2BAAAT,CAAA,QAAAF,CAAA,aAAAO,CAAA;AAAA,SAAAkG,gBAAA5G,CAAA,QAAAkH,KAAA,CAAAG,OAAA,CAAArH,CAAA,UAAAA,CAAA;AADA,OAAOsH,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,QAAQ,KAAK;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,aAAa,MAAM,qDAAqD;AAP/E,IAAAC,WAAA,GAAe;EAAE7C,IAAI,EAAE;AAAqB,CAAC;;;;;;;;;;;;;IAQ7C,IAAM8C,KAAK,GAAGC,OAAkD;IAChE,IAAMC,IAAI,GAAGC,MAAyB;IAEtC,IAAMC,OAAO,GAAGV,GAAG,CAAC,CAAC;IACrB,IAAMW,IAAI,GAAGZ,QAAQ,CAAC;MACpBa,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,CAAC;MAAE;MACXC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,EAAE;MAAE;MACnBC,OAAO,EAAE,EAAE;MAAE;MACbC,aAAa,EAAE,EAAE;MAAE;MACnBC,QAAQ,EAAE,CAAC;MAAE;MACbC,QAAQ,EAAE,CAAC;MAAE;MACbC,aAAa,EAAE,CAAC;MAAE;MAClBC,QAAQ,EAAE,EAAE;MAAE;MACdC,UAAU,EAAE,EAAE,CAAE;IAClB,CAAC,CAAC;IACF,IAAMC,UAAU,GAAG1B,GAAG,CAAC,CAAC;IACxB,IAAM2B,SAAS,GAAG3B,GAAG,CAAC;MACpB4B,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE;IACf,CAAC,CAAC;IACF,IAAMC,WAAW,GAAG9B,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAM+B,WAAW,GAAG/B,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAMgC,eAAe,GAAGhC,GAAG,CAAC,CAAC;IAC7B,IAAMiC,KAAK,GAAGlC,QAAQ,CAAC;MACrBa,KAAK,EAAE,CAAC;QAAEsB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC5EtB,SAAS,EAAE,CAAC;QAAEoB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAClFrB,QAAQ,EAAE,CAAC;QAAEmB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAC/EnB,WAAW,EAAE,CAAC;QAAEiB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MAClFlB,aAAa,EAAE,CAAC;QAAEgB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC,CAAC;MACpFZ,QAAQ,EAAE,CAAC;QAAEU,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ;MAAE,CAAC;IAC9E,CAAC,CAAC;IACF,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,IAAI;MAAA,OAAKA,IAAI,CAACC,OAAO,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,MAAM;IAAA;IACnE,IAAMC,gBAAgB,GAAG1C,GAAG,CAAC,EAAE,CAAC;IAChC;AACA;AACA;IACA,IAAM2C,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAIC,OAAO,GAAG,IAAIJ,IAAI,CAAC,CAAC,CAACD,OAAO,CAAC,CAAC;MAClC,IAAAM,aAAA,GAAuB3C,MAAM,CAAC0C,OAAO,EAAE,OAAO,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;QAAAC,cAAA,GAAA5D,cAAA,CAAA0D,aAAA;QAArDG,KAAK,GAAAD,cAAA;QAAEE,OAAO,GAAAF,cAAA,IAAuC,CAAC;MAC3D,IAAIG,QAAQ,CAACD,OAAO,CAAC,IAAI,CAAC,IAAIC,QAAQ,CAACD,OAAO,CAAC,GAAG,EAAE,EAAE;QACpDA,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM,IAAIC,QAAQ,CAACD,OAAO,CAAC,IAAI,EAAE,IAAIC,QAAQ,CAACD,OAAO,CAAC,GAAG,EAAE,EAAE;QAC5DA,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM,IAAIC,QAAQ,CAACD,OAAO,CAAC,IAAI,EAAE,IAAIC,QAAQ,CAACD,OAAO,CAAC,GAAG,EAAE,EAAE;QAC5DA,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM,IAAIC,QAAQ,CAACD,OAAO,CAAC,IAAI,EAAE,IAAIC,QAAQ,CAACD,OAAO,CAAC,GAAG,EAAE,EAAE;QAC5DA,OAAO,GAAG,IAAI;QACdD,KAAK,GAAIE,QAAQ,CAACF,KAAK,CAAC,GAAG,CAAC,GAAI,EAAE,GAAG,IAAI,GAAG,CAACE,QAAQ,CAACF,KAAK,CAAC,GAAG,CAAC,EAAEvD,QAAQ,CAAC,CAAC;QAC5E,IAAKyD,QAAQ,CAACF,KAAK,CAAC,GAAG,CAAC,GAAI,EAAE,EAAE;UAC9BJ,OAAO,GAAGA,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QACzC;MACF;MACAjC,IAAI,CAACE,SAAS,GAAGX,MAAM,CAAC0C,OAAO,EAAE,YAAY,CAAC,EAAC;MAC/CjC,IAAI,CAACG,SAAS,GAAGkC,KAAK,GAAG,GAAG,GAAGC,OAAO,EAAC;IACzC,CAAC;IACDhD,SAAS,CAAC,YAAM;MACd0C,YAAY,CAAC,CAAC;MACdQ,cAAc,CAAC,CAAC;MAChB,IAAI7C,KAAK,CAAC8C,EAAE,EAAE;QAAEC,mBAAmB,CAAC,CAAC;MAAC;IACxC,CAAC,CAAC;IACF,IAAMF,cAAc;MAAA,IAAAG,KAAA,GAAAxE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8F,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAApL,mBAAA,GAAAuB,IAAA,UAAA8J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAzF,IAAA,GAAAyF,QAAA,CAAApH,IAAA;YAAA;cAAAoH,QAAA,CAAApH,IAAA;cAAA,OACEuD,GAAG,CAACqD,cAAc,CAAC;gBAAES,SAAS,EAAE,CAAC,oBAAoB;cAAE,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAG,QAAA,CAAA3H,IAAA;cAAxEyH,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZf,gBAAgB,CAAC3J,KAAK,GAAG0K,IAAI,CAACI,kBAAkB,IAAI,EAAE;YAAA;YAAA;cAAA,OAAAF,QAAA,CAAAtF,IAAA;UAAA;QAAA,GAAAkF,OAAA;MAAA,CACvD;MAAA,gBAHKJ,cAAcA,CAAA;QAAA,OAAAG,KAAA,CAAAtE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGnB;IACD,IAAMsE,mBAAmB;MAAA,IAAAS,KAAA,GAAAhF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsG,SAAA;QAAA,IAAAC,eAAA;QAAA,IAAAC,GAAA,EAAAR,IAAA;QAAA,OAAApL,mBAAA,GAAAuB,IAAA,UAAAsK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjG,IAAA,GAAAiG,SAAA,CAAA5H,IAAA;YAAA;cAAA4H,SAAA,CAAA5H,IAAA;cAAA,OACRuD,GAAG,CAACuD,mBAAmB,CAAC;gBAAEe,QAAQ,EAAE9D,KAAK,CAAC8C;cAAG,CAAC,CAAC;YAAA;cAA3Da,GAAG,GAAAE,SAAA,CAAAnI,IAAA;cACHyH,IAAI,GAAKQ,GAAG,CAAZR,IAAI;cACV9C,IAAI,CAACC,KAAK,GAAG6C,IAAI,CAAC7C,KAAK;cACvBD,IAAI,CAACI,QAAQ,GAAGsD,MAAM,CAACZ,IAAI,CAACa,MAAM,CAAC;cACnC3D,IAAI,CAACK,MAAM,GAAGyC,IAAI,CAACzC,MAAM;cACzBL,IAAI,CAACE,SAAS,GAAGX,MAAM,CAACuD,IAAI,CAAC3C,SAAS,EAAE,YAAY,CAAC;cACrDH,IAAI,CAACG,SAAS,GAAGZ,MAAM,CAACuD,IAAI,CAAC3C,SAAS,EAAE,OAAO,CAAC;cAChDH,IAAI,CAACM,WAAW,IAAA+C,eAAA,GAAGP,IAAI,CAACc,SAAS,cAAAP,eAAA,uBAAdA,eAAA,CAAgBQ,GAAG,CAAC,UAAAzJ,CAAC;gBAAA,OAAIA,CAAC,CAAC0J,MAAM;cAAA,EAAC;cACrD9D,IAAI,CAACO,aAAa,GAAGuC,IAAI,CAACvC,aAAa;cACvCP,IAAI,CAACQ,OAAO,GAAGsC,IAAI,CAACtC,OAAO;cAC3BR,IAAI,CAACS,aAAa,GAAGqC,IAAI,CAACrC,aAAa;cACvCT,IAAI,CAACU,QAAQ,GAAGoC,IAAI,CAACpC,QAAQ;cAC7BV,IAAI,CAACW,QAAQ,GAAGmC,IAAI,CAACnC,QAAQ;cAC7BX,IAAI,CAACY,aAAa,GAAGkC,IAAI,CAAClC,aAAa;cACvCZ,IAAI,CAACa,QAAQ,GAAGiC,IAAI,CAACjC,QAAQ;cAC7Bb,IAAI,CAACc,UAAU,GAAGgC,IAAI,CAAChC,UAAU;YAAA;YAAA;cAAA,OAAA0C,SAAA,CAAA9F,IAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA,CAClC;MAAA,gBAjBKV,mBAAmBA,CAAA;QAAA,OAAAS,KAAA,CAAA9E,KAAA,OAAAD,SAAA;MAAA;IAAA,GAiBxB;IACD,IAAM2F,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB,IAAIhE,OAAO,CAAC3H,KAAK,EAAE;QACjB2H,OAAO,CAAC3H,KAAK,CAAC4L,aAAa,CAAC,eAAe,CAAC;MAC9C;IACF,CAAC;IACD,IAAMC,UAAU;MAAA,IAAAC,KAAA,GAAA/F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqH,SAAOC,MAAM;QAAA,OAAA1M,mBAAA,GAAAuB,IAAA,UAAAoL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/G,IAAA,GAAA+G,SAAA,CAAA1I,IAAA;YAAA;cAAA,IACzBwI,MAAM;gBAAAE,SAAA,CAAA1I,IAAA;gBAAA;cAAA;cAAA,OAAA0I,SAAA,CAAA9I,MAAA;YAAA;cAAA8I,SAAA,CAAA1I,IAAA;cAAA,OACLwI,MAAM,CAACG,QAAQ,CAAC,UAACC,KAAK,EAAEC,MAAM,EAAK;gBACvC,IAAID,KAAK,EAAE;kBAAEE,UAAU,CAAC,CAAC;gBAAC,CAAC,MAAM;kBAAElF,SAAS,CAAC;oBAAEjG,IAAI,EAAE,SAAS;oBAAEiI,OAAO,EAAE;kBAAiB,CAAC,CAAC;gBAAC;cAC/F,CAAC,CAAC;YAAA;YAAA;cAAA,OAAA8C,SAAA,CAAA5G,IAAA;UAAA;QAAA,GAAAyG,QAAA;MAAA,CACH;MAAA,gBALKF,UAAUA,CAAAU,EAAA;QAAA,OAAAT,KAAA,CAAA7F,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKf;IACD,IAAMsG,UAAU;MAAA,IAAAE,KAAA,GAAAzG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+H,SAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAArN,mBAAA,GAAAuB,IAAA,UAAA+L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1H,IAAA,GAAA0H,SAAA,CAAArJ,IAAA;YAAA;cAAAqJ,SAAA,CAAArJ,IAAA;cAAA,OACMuD,GAAG,CAACuF,UAAU,CAAC/E,KAAK,CAAC8C,EAAE,GAAG,uBAAuB,GAAG,sBAAsB,EAAE;gBACjGzC,IAAI,EAAE;kBACJyC,EAAE,EAAE9C,KAAK,CAAC8C,EAAE;kBACZxC,KAAK,EAAED,IAAI,CAACC,KAAK;kBACjB0D,MAAM,EAAE3D,IAAI,CAACI,QAAQ;kBACrBC,MAAM,EAAEL,IAAI,CAACK,MAAM;kBACnBF,SAAS,EAAE,IAAI0B,IAAI,CAAC7B,IAAI,CAACE,SAAS,GAAG,GAAG,GAAGF,IAAI,CAACG,SAAS,CAAC,CAACyB,OAAO,CAAC,CAAC;kBACpEsD,OAAO,EAAE,IAAIrD,IAAI,CAAC7B,IAAI,CAACE,SAAS,GAAG,GAAG,GAAGF,IAAI,CAACG,SAAS,CAAC,CAACyB,OAAO,CAAC,CAAC,GAAI,KAAK,GAAGuD,MAAM,CAACnF,IAAI,CAACI,QAAQ,CAAE;kBACpGG,aAAa,EAAEP,IAAI,CAACO,aAAa;kBACjCC,OAAO,EAAER,IAAI,CAACQ,OAAO;kBACrBC,aAAa,EAAET,IAAI,CAACS,aAAa;kBACjCC,QAAQ,EAAEV,IAAI,CAACU,QAAQ;kBACvBC,QAAQ,EAAEX,IAAI,CAACW,QAAQ;kBACvBC,aAAa,EAAEZ,IAAI,CAACY,aAAa;kBACjCC,QAAQ,EAAEb,IAAI,CAACoF,qBAAqB;kBACpCtE,UAAU,EAAEd,IAAI,CAACc;gBACnB,CAAC;gBACDR,WAAW,EAAEN,IAAI,CAACM;cACpB,CAAC,CAAC;YAAA;cAAAwE,qBAAA,GAAAG,SAAA,CAAA5J,IAAA;cAlBM0J,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cAmBZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBvF,SAAS,CAAC;kBAAEjG,IAAI,EAAE,SAAS;kBAAEiI,OAAO,EAAE7B,KAAK,CAAC8C,EAAE,GAAG,MAAM,GAAG;gBAAO,CAAC,CAAC;gBACnE5C,IAAI,CAAC,UAAU,CAAC;cAClB;YAAC;YAAA;cAAA,OAAAoF,SAAA,CAAAvH,IAAA;UAAA;QAAA,GAAAmH,QAAA;MAAA,CACF;MAAA,gBAxBKH,UAAUA,CAAA;QAAA,OAAAE,KAAA,CAAAvG,KAAA,OAAAD,SAAA;MAAA;IAAA,GAwBf;IACD,IAAMiH,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MAAExF,IAAI,CAAC,UAAU,CAAC;IAAC,CAAC;IAC5C,IAAMyF,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,IAAI,EAAEC,UAAU,EAAK;MAC/CrE,WAAW,CAAC/I,KAAK,GAAGmN,IAAI;MACxBnE,WAAW,CAAChJ,KAAK,GAAG,IAAI;MACxBiJ,eAAe,CAACjJ,KAAK,GAAGoN,UAAU;IACpC,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIF,IAAI,EAAK;MAC9BnE,WAAW,CAAChJ,KAAK,GAAG,KAAK;MACzB,IAAImN,IAAI,EAAElE,eAAe,CAACjJ,KAAK,CAACmN,IAAI,CAAC;IACvC,CAAC;IACD,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAIH,IAAI,EAAK;MAChCvF,IAAI,CAAC2F,WAAW,GAAG,EAAE;MACrB,IAAIJ,IAAI,CAACK,WAAW,EAAE;QACpB5E,SAAS,CAAC5I,KAAK,CAAC6I,WAAW,GAAGsE,IAAI,CAACK,WAAW;QAC9C5F,IAAI,CAAC2F,WAAW,GAAG,GAAG;MACxB,CAAC,MAAM;QACL3E,SAAS,CAAC5I,KAAK,CAAC6I,WAAW,GAAG,EAAE;MAClC;MACAlB,OAAO,CAAC3H,KAAK,CAAC4L,aAAa,CAAC,aAAa,CAAC;IAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}