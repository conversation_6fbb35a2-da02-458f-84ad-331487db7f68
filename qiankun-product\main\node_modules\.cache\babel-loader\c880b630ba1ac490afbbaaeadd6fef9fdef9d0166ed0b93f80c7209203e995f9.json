{"ast": null, "code": "import { defineAsyncComponent } from 'vue';\nvar LayoutView = defineAsyncComponent(function () {\n  return import('customize/LayoutView/LayoutView.vue');\n});\nvar LayoutViewOne = defineAsyncComponent(function () {\n  return import('customize/LayoutViewOne/LayoutViewOne.vue');\n});\nvar LayoutViewUnitedFront = defineAsyncComponent(function () {\n  return import('customize/LayoutViewUnitedFront/LayoutViewUnitedFront.vue');\n});\nexport var LayoutElement = {\n  LayoutView,\n  LayoutViewOne,\n  LayoutViewUnitedFront\n};", "map": {"version": 3, "names": ["defineAsyncComponent", "LayoutView", "LayoutViewOne", "LayoutViewUnitedFront", "LayoutElement"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutContainer/LayoutContainer.js"], "sourcesContent": ["import { defineAsyncComponent } from 'vue'\r\nconst LayoutView = defineAsyncComponent(() => import('customize/LayoutView/LayoutView.vue'))\r\nconst LayoutViewOne = defineAsyncComponent(() => import('customize/LayoutViewOne/LayoutViewOne.vue'))\r\nconst LayoutViewUnitedFront = defineAsyncComponent(() =>\r\n  import('customize/LayoutViewUnitedFront/LayoutViewUnitedFront.vue')\r\n)\r\nexport const LayoutElement = {\r\n  LayoutView,\r\n  LayoutViewOne,\r\n  LayoutViewUnitedFront\r\n}\r\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,KAAK;AAC1C,IAAMC,UAAU,GAAGD,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,qCAAqC,CAAC;AAAA,EAAC;AAC5F,IAAME,aAAa,GAAGF,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,2CAA2C,CAAC;AAAA,EAAC;AACrG,IAAMG,qBAAqB,GAAGH,oBAAoB,CAAC;EAAA,OACjD,MAAM,CAAC,2DAA2D,CAAC;AAAA,CACrE,CAAC;AACD,OAAO,IAAMI,aAAa,GAAG;EAC3BH,UAAU;EACVC,aAAa;EACbC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}