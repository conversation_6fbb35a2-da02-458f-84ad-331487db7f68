{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { user, appOnlyHeader } from 'common/js/system_var.js';\nexport var handleUserInfo = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(id) {\n    var _yield$api$userInfoBy, data;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _context.prev = 0;\n          _context.next = 3;\n          return api.userInfoByAccount({\n            accountId: id.slice(appOnlyHeader.value.length)\n          });\n        case 3:\n          _yield$api$userInfoBy = _context.sent;\n          data = _yield$api$userInfoBy.data;\n          return _context.abrupt(\"return\", {\n            uid: id,\n            id: data.accountId,\n            name: data.userName,\n            img: data.photo || data.headImg,\n            userInfo: {\n              userId: data.id,\n              userName: data.userName,\n              photo: data.photo,\n              headImg: data.headImg\n            }\n          });\n        case 8:\n          _context.prev = 8;\n          _context.t0 = _context[\"catch\"](0);\n          return _context.abrupt(\"return\", '');\n        case 11:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee, null, [[0, 8]]);\n  }));\n  return function handleUserInfo(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport var chatGroupInfo = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(id) {\n    var _data$chatGroupType, _data$chatGroupType2;\n    var _yield$api$chatGroupI, data;\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          _context2.next = 2;\n          return api.chatGroupInfo({\n            detailId: id.slice(appOnlyHeader.value.length)\n          });\n        case 2:\n          _yield$api$chatGroupI = _context2.sent;\n          data = _yield$api$chatGroupI.data;\n          return _context2.abrupt(\"return\", {\n            uid: id,\n            id: data.id,\n            name: data.groupName,\n            img: data.groupImg,\n            userIdData: data.memberUserIds,\n            chatGroupType: (data === null || data === void 0 || (_data$chatGroupType = data.chatGroupType) === null || _data$chatGroupType === void 0 ? void 0 : _data$chatGroupType.value) !== '0' ? data === null || data === void 0 || (_data$chatGroupType2 = data.chatGroupType) === null || _data$chatGroupType2 === void 0 || (_data$chatGroupType2 = _data$chatGroupType2.name) === null || _data$chatGroupType2 === void 0 ? void 0 : _data$chatGroupType2.slice(0, 2) : ''\n          });\n        case 5:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  }));\n  return function chatGroupInfo(_x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport var userInfoByAccount = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(ids) {\n    var _yield$api$userInfoBy2, data, newData, index, item;\n    return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          _context3.prev = 0;\n          _context3.next = 3;\n          return api.userInfoByAccount({\n            accountIds: ids\n          });\n        case 3:\n          _yield$api$userInfoBy2 = _context3.sent;\n          data = _yield$api$userInfoBy2.data;\n          newData = [];\n          for (index = 0; index < data.length; index++) {\n            item = data[index];\n            newData.push({\n              uid: appOnlyHeader.value + item.accountId,\n              id: item.accountId,\n              name: item.userName,\n              img: item.photo || item.headImg,\n              userInfo: {\n                userId: data.id,\n                userName: data.userName,\n                photo: data.photo,\n                headImg: data.headImg\n              }\n            });\n          }\n          return _context3.abrupt(\"return\", newData);\n        case 10:\n          _context3.prev = 10;\n          _context3.t0 = _context3[\"catch\"](0);\n          return _context3.abrupt(\"return\", []);\n        case 13:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3, null, [[0, 10]]);\n  }));\n  return function userInfoByAccount(_x3) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nexport var chatGroupList = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(ids) {\n    var _yield$api$chatGroupL, data, newData, index, _item$chatGroupType, _item$chatGroupType2, item;\n    return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _context4.prev = 0;\n          _context4.next = 3;\n          return api.chatGroupList({\n            ids: ids\n          });\n        case 3:\n          _yield$api$chatGroupL = _context4.sent;\n          data = _yield$api$chatGroupL.data;\n          newData = [];\n          for (index = 0; index < data.length; index++) {\n            item = data[index];\n            newData.push({\n              uid: appOnlyHeader.value + item.id,\n              id: item.id,\n              name: item.groupName,\n              img: item.groupImg,\n              chatGroupType: (item === null || item === void 0 || (_item$chatGroupType = item.chatGroupType) === null || _item$chatGroupType === void 0 ? void 0 : _item$chatGroupType.value) !== '0' ? item === null || item === void 0 || (_item$chatGroupType2 = item.chatGroupType) === null || _item$chatGroupType2 === void 0 || (_item$chatGroupType2 = _item$chatGroupType2.name) === null || _item$chatGroupType2 === void 0 ? void 0 : _item$chatGroupType2.slice(0, 2) : ''\n            });\n          }\n          return _context4.abrupt(\"return\", newData);\n        case 10:\n          _context4.prev = 10;\n          _context4.t0 = _context4[\"catch\"](0);\n          return _context4.abrupt(\"return\", []);\n        case 13:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4, null, [[0, 10]]);\n  }));\n  return function chatGroupList(_x4) {\n    return _ref4.apply(this, arguments);\n  };\n}();\nexport var chatGroupMemberList = /*#__PURE__*/function () {\n  var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5(groupId) {\n    var _yield$api$chatGroupM, data;\n    return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n      while (1) switch (_context5.prev = _context5.next) {\n        case 0:\n          _context5.next = 2;\n          return api.chatGroupMemberList({\n            pageNo: 1,\n            pageSize: 9999,\n            query: {\n              chatGroupId: groupId\n            }\n          });\n        case 2:\n          _yield$api$chatGroupM = _context5.sent;\n          data = _yield$api$chatGroupM.data;\n          return _context5.abrupt(\"return\", data);\n        case 5:\n        case \"end\":\n          return _context5.stop();\n      }\n    }, _callee5);\n  }));\n  return function chatGroupMemberList(_x5) {\n    return _ref5.apply(this, arguments);\n  };\n}();\nexport var handleChatId = /*#__PURE__*/function () {\n  var _ref6 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee6(data, type, oldData) {\n    var isNewId, userIds, groupIds, oldUid, index, item, userData, groupData;\n    return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n      while (1) switch (_context6.prev = _context6.next) {\n        case 0:\n          isNewId = true;\n          userIds = [];\n          groupIds = [];\n          oldUid = oldData.map(function (v) {\n            return v.uid;\n          });\n          for (index = 0; index < data.length; index++) {\n            item = data[index];\n            if (!(oldUid !== null && oldUid !== void 0 && oldUid.includes(item.targetId))) isNewId = false;\n            if (item.conversationType === 1) {\n              userIds.push(item.targetId.slice(appOnlyHeader.value.length));\n            }\n            if (item.conversationType === 3) {\n              groupIds.push(item.targetId.slice(appOnlyHeader.value.length));\n            }\n          }\n          if (!(isNewId && type)) {\n            _context6.next = 9;\n            break;\n          }\n          return _context6.abrupt(\"return\", oldData);\n        case 9:\n          _context6.next = 11;\n          return userInfoByAccount(userIds);\n        case 11:\n          userData = _context6.sent;\n          _context6.next = 14;\n          return chatGroupList(groupIds);\n        case 14:\n          groupData = _context6.sent;\n          return _context6.abrupt(\"return\", [].concat(_toConsumableArray(userData), _toConsumableArray(groupData)));\n        case 16:\n        case \"end\":\n          return _context6.stop();\n      }\n    }, _callee6);\n  }));\n  return function handleChatId(_x6, _x7, _x8) {\n    return _ref6.apply(this, arguments);\n  };\n}();\nexport var handleChatList = /*#__PURE__*/function () {\n  var _ref7 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee7(data, temporary) {\n    var oldData,\n      chatObjectInfoData,\n      chatObjectInfoObj,\n      index,\n      item,\n      newChatId,\n      isTopChat,\n      newChatList,\n      _index,\n      _item,\n      chatObjectInfo,\n      _item$latestMessage,\n      revocationMessage,\n      _item$latestMessage2,\n      chatObjectInfoType,\n      _item$latestMessage3,\n      userInfo,\n      _item$latestMessage4,\n      _item$latestMessage5,\n      _item$latestMessage6,\n      _item$latestMessage7,\n      _item$latestMessage8,\n      _item$latestMessage9,\n      _index2,\n      _item2,\n      _index3,\n      _item3,\n      allChat,\n      _args7 = arguments;\n    return _regeneratorRuntime().wrap(function _callee7$(_context7) {\n      while (1) switch (_context7.prev = _context7.next) {\n        case 0:\n          oldData = _args7.length > 2 && _args7[2] !== undefined ? _args7[2] : [];\n          chatObjectInfoData = _args7.length > 3 ? _args7[3] : undefined;\n          chatObjectInfoObj = {};\n          for (index = 0; index < chatObjectInfoData.length; index++) {\n            item = chatObjectInfoData[index];\n            chatObjectInfoObj[item.uid] = item;\n          }\n          newChatId = [];\n          isTopChat = [];\n          newChatList = [];\n          _index = 0;\n        case 8:\n          if (!(_index < data.length)) {\n            _context7.next = 28;\n            break;\n          }\n          _item = data[_index];\n          chatObjectInfo = chatObjectInfoObj[_item.targetId] || '';\n          if (!chatObjectInfo) {\n            _context7.next = 25;\n            break;\n          }\n          newChatId.push(_item.targetId);\n          revocationMessage = '';\n          if (!(((_item$latestMessage = _item.latestMessage) === null || _item$latestMessage === void 0 ? void 0 : _item$latestMessage.messageType) === 'RC:RcCmd')) {\n            _context7.next = 24;\n            break;\n          }\n          chatObjectInfoType = ((_item$latestMessage2 = _item.latestMessage) === null || _item$latestMessage2 === void 0 ? void 0 : _item$latestMessage2.senderUserId) === appOnlyHeader.value + user.value.accountId;\n          if (!chatObjectInfoType) {\n            _context7.next = 20;\n            break;\n          }\n          revocationMessage = '你撤回了一条消息';\n          _context7.next = 24;\n          break;\n        case 20:\n          _context7.next = 22;\n          return handleUserInfo((_item$latestMessage3 = _item.latestMessage) === null || _item$latestMessage3 === void 0 ? void 0 : _item$latestMessage3.senderUserId);\n        case 22:\n          userInfo = _context7.sent;\n          revocationMessage = userInfo.name + '撤回了一条消息';\n        case 24:\n          if (_item.isTop) {\n            isTopChat.push({\n              isTop: _item.isTop,\n              isNotInform: _item.notificationStatus,\n              id: _item.targetId,\n              targetId: _item.targetId,\n              type: _item.conversationType,\n              chatObjectInfo: chatObjectInfo,\n              sentTime: (_item$latestMessage4 = _item.latestMessage) === null || _item$latestMessage4 === void 0 ? void 0 : _item$latestMessage4.sentTime,\n              messageType: ((_item$latestMessage5 = _item.latestMessage) === null || _item$latestMessage5 === void 0 ? void 0 : _item$latestMessage5.messageType) || 'RC:TxtMsg',\n              content: (_item$latestMessage6 = _item.latestMessage) === null || _item$latestMessage6 === void 0 ? void 0 : _item$latestMessage6.content,\n              revocationMessage,\n              count: _item.unreadMessageCount\n            });\n          } else {\n            newChatList.push({\n              isTop: _item.isTop,\n              isNotInform: _item.notificationStatus,\n              id: _item.targetId,\n              targetId: _item.targetId,\n              type: _item.conversationType,\n              chatObjectInfo: chatObjectInfo,\n              sentTime: (_item$latestMessage7 = _item.latestMessage) === null || _item$latestMessage7 === void 0 ? void 0 : _item$latestMessage7.sentTime,\n              messageType: ((_item$latestMessage8 = _item.latestMessage) === null || _item$latestMessage8 === void 0 ? void 0 : _item$latestMessage8.messageType) || 'RC:TxtMsg',\n              content: (_item$latestMessage9 = _item.latestMessage) === null || _item$latestMessage9 === void 0 ? void 0 : _item$latestMessage9.content,\n              revocationMessage,\n              count: _item.unreadMessageCount\n            });\n          }\n        case 25:\n          _index++;\n          _context7.next = 8;\n          break;\n        case 28:\n          for (_index2 = 0; _index2 < temporary.length; _index2++) {\n            _item2 = temporary[_index2];\n            if (!(newChatId !== null && newChatId !== void 0 && newChatId.includes(_item2.id))) _item2.isTop ? isTopChat.push(_item2) : newChatList.push(_item2);\n          }\n          for (_index3 = 0; _index3 < oldData.length; _index3++) {\n            _item3 = oldData[_index3];\n            if (!(newChatId !== null && newChatId !== void 0 && newChatId.includes(_item3.id))) _item3.isTop ? isTopChat.push(_item3) : newChatList.push(_item3);\n          }\n          allChat = [].concat(_toConsumableArray(isTopChat.sort(function (a, b) {\n            return b.sentTime - a.sentTime;\n          })), _toConsumableArray(newChatList.sort(function (a, b) {\n            return b.sentTime - a.sentTime;\n          })));\n          console.log(allChat);\n          return _context7.abrupt(\"return\", allChat);\n        case 33:\n        case \"end\":\n          return _context7.stop();\n      }\n    }, _callee7);\n  }));\n  return function handleChatList(_x9, _x10) {\n    return _ref7.apply(this, arguments);\n  };\n}();", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "user", "appOnly<PERSON>eader", "handleUserInfo", "_ref", "_callee", "id", "_yield$api$userInfoBy", "data", "_callee$", "_context", "userInfoByAccount", "accountId", "uid", "userName", "img", "photo", "headImg", "userInfo", "userId", "t0", "_x", "chatGroupInfo", "_ref2", "_callee2", "_data$chatGroupType", "_data$chatGroupType2", "_yield$api$chatGroupI", "_callee2$", "_context2", "detailId", "groupName", "groupImg", "userIdData", "memberUserIds", "chatGroupType", "_x2", "_ref3", "_callee3", "ids", "_yield$api$userInfoBy2", "newData", "index", "item", "_callee3$", "_context3", "accountIds", "_x3", "chatGroupList", "_ref4", "_callee4", "_yield$api$chatGroupL", "_item$chatGroupType", "_item$chatGroupType2", "_callee4$", "_context4", "_x4", "chatGroupMemberList", "_ref5", "_callee5", "groupId", "_yield$api$chatGroupM", "_callee5$", "_context5", "pageNo", "pageSize", "query", "chatGroupId", "_x5", "handleChatId", "_ref6", "_callee6", "oldData", "isNewId", "userIds", "groupIds", "oldUid", "userData", "groupData", "_callee6$", "_context6", "map", "includes", "targetId", "conversationType", "concat", "_toConsumableArray", "_x6", "_x7", "_x8", "handleChatList", "_ref7", "_callee7", "temporary", "chatObjectInfoData", "chatObjectInfoObj", "newChatId", "isTopChat", "newChatList", "_index", "_item", "chatObjectInfo", "_item$latestMessage", "revocationMessage", "_item$latestMessage2", "chatObjectInfoType", "_item$latestMessage3", "_item$latestMessage4", "_item$latestMessage5", "_item$latestMessage6", "_item$latestMessage7", "_item$latestMessage8", "_item$latestMessage9", "_index2", "_item2", "_index3", "_item3", "allChat", "_args7", "_callee7$", "_context7", "undefined", "latestMessage", "messageType", "senderUserId", "isTop", "isNotInform", "notificationStatus", "sentTime", "content", "count", "unreadMessageCount", "sort", "b", "console", "log", "_x9", "_x10"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/js/ChatMethod.js"], "sourcesContent": ["import api from '@/api'\r\nimport { user, appOnlyHeader } from 'common/js/system_var.js'\r\n\r\nexport const handleUserInfo = async (id) => {\r\n  try {\r\n    const { data } = await api.userInfoByAccount({ accountId: id.slice(appOnlyHeader.value.length) })\r\n    return { uid: id, id: data.accountId, name: data.userName, img: data.photo || data.headImg, userInfo: { userId: data.id, userName: data.userName, photo: data.photo, headImg: data.headImg } }\r\n  } catch (error) {\r\n    return ''\r\n  }\r\n}\r\nexport const chatGroupInfo = async (id) => {\r\n  const { data } = await api.chatGroupInfo({ detailId: id.slice(appOnlyHeader.value.length) })\r\n  return { uid: id, id: data.id, name: data.groupName, img: data.groupImg, userIdData: data.memberUserIds, chatGroupType: data?.chatGroupType?.value !== '0' ? data?.chatGroupType?.name?.slice(0, 2) : '' }\r\n}\r\nexport const userInfoByAccount = async (ids) => {\r\n  try {\r\n    const { data } = await api.userInfoByAccount({ accountIds: ids })\r\n    const newData = []\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      newData.push({ uid: appOnlyHeader.value + item.accountId, id: item.accountId, name: item.userName, img: item.photo || item.headImg, userInfo: { userId: data.id, userName: data.userName, photo: data.photo, headImg: data.headImg } })\r\n    }\r\n    return newData\r\n  } catch (error) {\r\n    return []\r\n  }\r\n}\r\nexport const chatGroupList = async (ids) => {\r\n  try {\r\n    const { data } = await api.chatGroupList({ ids: ids })\r\n    const newData = []\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      newData.push({ uid: appOnlyHeader.value + item.id, id: item.id, name: item.groupName, img: item.groupImg, chatGroupType: item?.chatGroupType?.value !== '0' ? item?.chatGroupType?.name?.slice(0, 2) : '' })\r\n    }\r\n    return newData\r\n  } catch (error) {\r\n    return []\r\n  }\r\n}\r\nexport const chatGroupMemberList = async (groupId) => {\r\n  const { data } = await api.chatGroupMemberList({ pageNo: 1, pageSize: 9999, query: { chatGroupId: groupId } })\r\n  return data\r\n}\r\nexport const handleChatId = async (data, type, oldData) => {\r\n  let isNewId = true\r\n  const userIds = []\r\n  const groupIds = []\r\n  const oldUid = oldData.map(v => v.uid)\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (!oldUid?.includes(item.targetId)) isNewId = false\r\n    if (item.conversationType === 1) {\r\n      userIds.push(item.targetId.slice(appOnlyHeader.value.length))\r\n    }\r\n    if (item.conversationType === 3) {\r\n      groupIds.push(item.targetId.slice(appOnlyHeader.value.length))\r\n    }\r\n  }\r\n  if (isNewId && type) {\r\n    return oldData\r\n  } else {\r\n    const userData = await userInfoByAccount(userIds)\r\n    const groupData = await chatGroupList(groupIds)\r\n    return [...userData, ...groupData]\r\n  }\r\n}\r\nexport const handleChatList = async (data, temporary, oldData = [], chatObjectInfoData) => {\r\n  const chatObjectInfoObj = {}\r\n  for (let index = 0; index < chatObjectInfoData.length; index++) {\r\n    const item = chatObjectInfoData[index]\r\n    chatObjectInfoObj[item.uid] = item\r\n  }\r\n  const newChatId = []\r\n  const isTopChat = []\r\n  const newChatList = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    const chatObjectInfo = chatObjectInfoObj[item.targetId] || ''\r\n    if (chatObjectInfo) {\r\n      newChatId.push(item.targetId)\r\n      let revocationMessage = ''\r\n      if (item.latestMessage?.messageType === 'RC:RcCmd') {\r\n        const chatObjectInfoType = item.latestMessage?.senderUserId === appOnlyHeader.value + user.value.accountId\r\n        if (chatObjectInfoType) {\r\n          revocationMessage = '你撤回了一条消息'\r\n        } else {\r\n          const userInfo = await handleUserInfo(item.latestMessage?.senderUserId)\r\n          revocationMessage = userInfo.name + '撤回了一条消息'\r\n        }\r\n      }\r\n      if (item.isTop) {\r\n        isTopChat.push({ isTop: item.isTop, isNotInform: item.notificationStatus, id: item.targetId, targetId: item.targetId, type: item.conversationType, chatObjectInfo: chatObjectInfo, sentTime: item.latestMessage?.sentTime, messageType: item.latestMessage?.messageType || 'RC:TxtMsg', content: item.latestMessage?.content, revocationMessage, count: item.unreadMessageCount })\r\n      } else {\r\n        newChatList.push({ isTop: item.isTop, isNotInform: item.notificationStatus, id: item.targetId, targetId: item.targetId, type: item.conversationType, chatObjectInfo: chatObjectInfo, sentTime: item.latestMessage?.sentTime, messageType: item.latestMessage?.messageType || 'RC:TxtMsg', content: item.latestMessage?.content, revocationMessage, count: item.unreadMessageCount })\r\n      }\r\n    }\r\n  }\r\n  for (let index = 0; index < temporary.length; index++) {\r\n    const item = temporary[index]\r\n    if (!newChatId?.includes(item.id)) item.isTop ? isTopChat.push(item) : newChatList.push(item)\r\n  }\r\n  for (let index = 0; index < oldData.length; index++) {\r\n    const item = oldData[index]\r\n    if (!newChatId?.includes(item.id)) item.isTop ? isTopChat.push(item) : newChatList.push(item)\r\n  }\r\n  const allChat = [...(isTopChat.sort((a, b) => b.sentTime - a.sentTime)), ...(newChatList.sort((a, b) => b.sentTime - a.sentTime))]\r\n  console.log(allChat)\r\n  return allChat\r\n}"], "mappings": ";;;;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,IAAI,EAAEC,aAAa,QAAQ,yBAAyB;AAE7D,OAAO,IAAMC,cAAc;EAAA,IAAAC,IAAA,GAAAT,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+B,QAAOC,EAAE;IAAA,IAAAC,qBAAA,EAAAC,IAAA;IAAA,OAAAtH,mBAAA,GAAAuB,IAAA,UAAAgG,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAA3B,IAAA,GAAA2B,QAAA,CAAAtD,IAAA;QAAA;UAAAsD,QAAA,CAAA3B,IAAA;UAAA2B,QAAA,CAAAtD,IAAA;UAAA,OAEZ4C,GAAG,CAACW,iBAAiB,CAAC;YAAEC,SAAS,EAAEN,EAAE,CAACrB,KAAK,CAACiB,aAAa,CAACtG,KAAK,CAACqE,MAAM;UAAE,CAAC,CAAC;QAAA;UAAAsC,qBAAA,GAAAG,QAAA,CAAA7D,IAAA;UAAzF2D,IAAI,GAAAD,qBAAA,CAAJC,IAAI;UAAA,OAAAE,QAAA,CAAA1D,MAAA,WACL;YAAE6D,GAAG,EAAEP,EAAE;YAAEA,EAAE,EAAEE,IAAI,CAACI,SAAS;YAAEvC,IAAI,EAAEmC,IAAI,CAACM,QAAQ;YAAEC,GAAG,EAAEP,IAAI,CAACQ,KAAK,IAAIR,IAAI,CAACS,OAAO;YAAEC,QAAQ,EAAE;cAAEC,MAAM,EAAEX,IAAI,CAACF,EAAE;cAAEQ,QAAQ,EAAEN,IAAI,CAACM,QAAQ;cAAEE,KAAK,EAAER,IAAI,CAACQ,KAAK;cAAEC,OAAO,EAAET,IAAI,CAACS;YAAQ;UAAE,CAAC;QAAA;UAAAP,QAAA,CAAA3B,IAAA;UAAA2B,QAAA,CAAAU,EAAA,GAAAV,QAAA;UAAA,OAAAA,QAAA,CAAA1D,MAAA,WAEvL,EAAE;QAAA;QAAA;UAAA,OAAA0D,QAAA,CAAAxB,IAAA;MAAA;IAAA,GAAAmB,OAAA;EAAA,CAEZ;EAAA,gBAPYF,cAAcA,CAAAkB,EAAA;IAAA,OAAAjB,IAAA,CAAAP,KAAA,OAAAD,SAAA;EAAA;AAAA,GAO1B;AACD,OAAO,IAAM0B,aAAa;EAAA,IAAAC,KAAA,GAAA5B,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkD,SAAOlB,EAAE;IAAA,IAAAmB,mBAAA,EAAAC,oBAAA;IAAA,IAAAC,qBAAA,EAAAnB,IAAA;IAAA,OAAAtH,mBAAA,GAAAuB,IAAA,UAAAmH,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA9C,IAAA,GAAA8C,SAAA,CAAAzE,IAAA;QAAA;UAAAyE,SAAA,CAAAzE,IAAA;UAAA,OACb4C,GAAG,CAACsB,aAAa,CAAC;YAAEQ,QAAQ,EAAExB,EAAE,CAACrB,KAAK,CAACiB,aAAa,CAACtG,KAAK,CAACqE,MAAM;UAAE,CAAC,CAAC;QAAA;UAAA0D,qBAAA,GAAAE,SAAA,CAAAhF,IAAA;UAApF2D,IAAI,GAAAmB,qBAAA,CAAJnB,IAAI;UAAA,OAAAqB,SAAA,CAAA7E,MAAA,WACL;YAAE6D,GAAG,EAAEP,EAAE;YAAEA,EAAE,EAAEE,IAAI,CAACF,EAAE;YAAEjC,IAAI,EAAEmC,IAAI,CAACuB,SAAS;YAAEhB,GAAG,EAAEP,IAAI,CAACwB,QAAQ;YAAEC,UAAU,EAAEzB,IAAI,CAAC0B,aAAa;YAAEC,aAAa,EAAE,CAAA3B,IAAI,aAAJA,IAAI,gBAAAiB,mBAAA,GAAJjB,IAAI,CAAE2B,aAAa,cAAAV,mBAAA,uBAAnBA,mBAAA,CAAqB7H,KAAK,MAAK,GAAG,GAAG4G,IAAI,aAAJA,IAAI,gBAAAkB,oBAAA,GAAJlB,IAAI,CAAE2B,aAAa,cAAAT,oBAAA,gBAAAA,oBAAA,GAAnBA,oBAAA,CAAqBrD,IAAI,cAAAqD,oBAAA,uBAAzBA,oBAAA,CAA2BzC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG;UAAG,CAAC;QAAA;QAAA;UAAA,OAAA4C,SAAA,CAAA3C,IAAA;MAAA;IAAA,GAAAsC,QAAA;EAAA,CAC3M;EAAA,gBAHYF,aAAaA,CAAAc,GAAA;IAAA,OAAAb,KAAA,CAAA1B,KAAA,OAAAD,SAAA;EAAA;AAAA,GAGzB;AACD,OAAO,IAAMe,iBAAiB;EAAA,IAAA0B,KAAA,GAAA1C,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgE,SAAOC,GAAG;IAAA,IAAAC,sBAAA,EAAAhC,IAAA,EAAAiC,OAAA,EAAAC,KAAA,EAAAC,IAAA;IAAA,OAAAzJ,mBAAA,GAAAuB,IAAA,UAAAmI,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA9D,IAAA,GAAA8D,SAAA,CAAAzF,IAAA;QAAA;UAAAyF,SAAA,CAAA9D,IAAA;UAAA8D,SAAA,CAAAzF,IAAA;UAAA,OAEhB4C,GAAG,CAACW,iBAAiB,CAAC;YAAEmC,UAAU,EAAEP;UAAI,CAAC,CAAC;QAAA;UAAAC,sBAAA,GAAAK,SAAA,CAAAhG,IAAA;UAAzD2D,IAAI,GAAAgC,sBAAA,CAAJhC,IAAI;UACNiC,OAAO,GAAG,EAAE;UAClB,KAASC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGlC,IAAI,CAACvC,MAAM,EAAEyE,KAAK,EAAE,EAAE;YAC1CC,IAAI,GAAGnC,IAAI,CAACkC,KAAK,CAAC;YACxBD,OAAO,CAAC7E,IAAI,CAAC;cAAEiD,GAAG,EAAEX,aAAa,CAACtG,KAAK,GAAG+I,IAAI,CAAC/B,SAAS;cAAEN,EAAE,EAAEqC,IAAI,CAAC/B,SAAS;cAAEvC,IAAI,EAAEsE,IAAI,CAAC7B,QAAQ;cAAEC,GAAG,EAAE4B,IAAI,CAAC3B,KAAK,IAAI2B,IAAI,CAAC1B,OAAO;cAAEC,QAAQ,EAAE;gBAAEC,MAAM,EAAEX,IAAI,CAACF,EAAE;gBAAEQ,QAAQ,EAAEN,IAAI,CAACM,QAAQ;gBAAEE,KAAK,EAAER,IAAI,CAACQ,KAAK;gBAAEC,OAAO,EAAET,IAAI,CAACS;cAAQ;YAAE,CAAC,CAAC;UACzO;UAAC,OAAA4B,SAAA,CAAA7F,MAAA,WACMyF,OAAO;QAAA;UAAAI,SAAA,CAAA9D,IAAA;UAAA8D,SAAA,CAAAzB,EAAA,GAAAyB,SAAA;UAAA,OAAAA,SAAA,CAAA7F,MAAA,WAEP,EAAE;QAAA;QAAA;UAAA,OAAA6F,SAAA,CAAA3D,IAAA;MAAA;IAAA,GAAAoD,QAAA;EAAA,CAEZ;EAAA,gBAZY3B,iBAAiBA,CAAAoC,GAAA;IAAA,OAAAV,KAAA,CAAAxC,KAAA,OAAAD,SAAA;EAAA;AAAA,GAY7B;AACD,OAAO,IAAMoD,aAAa;EAAA,IAAAC,KAAA,GAAAtD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4E,SAAOX,GAAG;IAAA,IAAAY,qBAAA,EAAA3C,IAAA,EAAAiC,OAAA,EAAAC,KAAA,EAAAU,mBAAA,EAAAC,oBAAA,EAAAV,IAAA;IAAA,OAAAzJ,mBAAA,GAAAuB,IAAA,UAAA6I,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAxE,IAAA,GAAAwE,SAAA,CAAAnG,IAAA;QAAA;UAAAmG,SAAA,CAAAxE,IAAA;UAAAwE,SAAA,CAAAnG,IAAA;UAAA,OAEZ4C,GAAG,CAACgD,aAAa,CAAC;YAAET,GAAG,EAAEA;UAAI,CAAC,CAAC;QAAA;UAAAY,qBAAA,GAAAI,SAAA,CAAA1G,IAAA;UAA9C2D,IAAI,GAAA2C,qBAAA,CAAJ3C,IAAI;UACNiC,OAAO,GAAG,EAAE;UAClB,KAASC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGlC,IAAI,CAACvC,MAAM,EAAEyE,KAAK,EAAE,EAAE;YAC1CC,IAAI,GAAGnC,IAAI,CAACkC,KAAK,CAAC;YACxBD,OAAO,CAAC7E,IAAI,CAAC;cAAEiD,GAAG,EAAEX,aAAa,CAACtG,KAAK,GAAG+I,IAAI,CAACrC,EAAE;cAAEA,EAAE,EAAEqC,IAAI,CAACrC,EAAE;cAAEjC,IAAI,EAAEsE,IAAI,CAACZ,SAAS;cAAEhB,GAAG,EAAE4B,IAAI,CAACX,QAAQ;cAAEG,aAAa,EAAE,CAAAQ,IAAI,aAAJA,IAAI,gBAAAS,mBAAA,GAAJT,IAAI,CAAER,aAAa,cAAAiB,mBAAA,uBAAnBA,mBAAA,CAAqBxJ,KAAK,MAAK,GAAG,GAAG+I,IAAI,aAAJA,IAAI,gBAAAU,oBAAA,GAAJV,IAAI,CAAER,aAAa,cAAAkB,oBAAA,gBAAAA,oBAAA,GAAnBA,oBAAA,CAAqBhF,IAAI,cAAAgF,oBAAA,uBAAzBA,oBAAA,CAA2BpE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG;YAAG,CAAC,CAAC;UAC9M;UAAC,OAAAsE,SAAA,CAAAvG,MAAA,WACMyF,OAAO;QAAA;UAAAc,SAAA,CAAAxE,IAAA;UAAAwE,SAAA,CAAAnC,EAAA,GAAAmC,SAAA;UAAA,OAAAA,SAAA,CAAAvG,MAAA,WAEP,EAAE;QAAA;QAAA;UAAA,OAAAuG,SAAA,CAAArE,IAAA;MAAA;IAAA,GAAAgE,QAAA;EAAA,CAEZ;EAAA,gBAZYF,aAAaA,CAAAQ,GAAA;IAAA,OAAAP,KAAA,CAAApD,KAAA,OAAAD,SAAA;EAAA;AAAA,GAYzB;AACD,OAAO,IAAM6D,mBAAmB;EAAA,IAAAC,KAAA,GAAA/D,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqF,SAAOC,OAAO;IAAA,IAAAC,qBAAA,EAAArD,IAAA;IAAA,OAAAtH,mBAAA,GAAAuB,IAAA,UAAAqJ,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAhF,IAAA,GAAAgF,SAAA,CAAA3G,IAAA;QAAA;UAAA2G,SAAA,CAAA3G,IAAA;UAAA,OACxB4C,GAAG,CAACyD,mBAAmB,CAAC;YAAEO,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,KAAK,EAAE;cAAEC,WAAW,EAAEP;YAAQ;UAAE,CAAC,CAAC;QAAA;UAAAC,qBAAA,GAAAE,SAAA,CAAAlH,IAAA;UAAtG2D,IAAI,GAAAqD,qBAAA,CAAJrD,IAAI;UAAA,OAAAuD,SAAA,CAAA/G,MAAA,WACLwD,IAAI;QAAA;QAAA;UAAA,OAAAuD,SAAA,CAAA7E,IAAA;MAAA;IAAA,GAAAyE,QAAA;EAAA,CACZ;EAAA,gBAHYF,mBAAmBA,CAAAW,GAAA;IAAA,OAAAV,KAAA,CAAA7D,KAAA,OAAAD,SAAA;EAAA;AAAA,GAG/B;AACD,OAAO,IAAMyE,YAAY;EAAA,IAAAC,KAAA,GAAA3E,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiG,SAAO/D,IAAI,EAAEzF,IAAI,EAAEyJ,OAAO;IAAA,IAAAC,OAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,MAAA,EAAAlC,KAAA,EAAAC,IAAA,EAAAkC,QAAA,EAAAC,SAAA;IAAA,OAAA5L,mBAAA,GAAAuB,IAAA,UAAAsK,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAjG,IAAA,GAAAiG,SAAA,CAAA5H,IAAA;QAAA;UAChDqH,OAAO,GAAG,IAAI;UACZC,OAAO,GAAG,EAAE;UACZC,QAAQ,GAAG,EAAE;UACbC,MAAM,GAAGJ,OAAO,CAACS,GAAG,CAAC,UAAArJ,CAAC;YAAA,OAAIA,CAAC,CAACiF,GAAG;UAAA,EAAC;UACtC,KAAS6B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGlC,IAAI,CAACvC,MAAM,EAAEyE,KAAK,EAAE,EAAE;YAC1CC,IAAI,GAAGnC,IAAI,CAACkC,KAAK,CAAC;YACxB,IAAI,EAACkC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEM,QAAQ,CAACvC,IAAI,CAACwC,QAAQ,CAAC,GAAEV,OAAO,GAAG,KAAK;YACrD,IAAI9B,IAAI,CAACyC,gBAAgB,KAAK,CAAC,EAAE;cAC/BV,OAAO,CAAC9G,IAAI,CAAC+E,IAAI,CAACwC,QAAQ,CAAClG,KAAK,CAACiB,aAAa,CAACtG,KAAK,CAACqE,MAAM,CAAC,CAAC;YAC/D;YACA,IAAI0E,IAAI,CAACyC,gBAAgB,KAAK,CAAC,EAAE;cAC/BT,QAAQ,CAAC/G,IAAI,CAAC+E,IAAI,CAACwC,QAAQ,CAAClG,KAAK,CAACiB,aAAa,CAACtG,KAAK,CAACqE,MAAM,CAAC,CAAC;YAChE;UACF;UAAC,MACGwG,OAAO,IAAI1J,IAAI;YAAAiK,SAAA,CAAA5H,IAAA;YAAA;UAAA;UAAA,OAAA4H,SAAA,CAAAhI,MAAA,WACVwH,OAAO;QAAA;UAAAQ,SAAA,CAAA5H,IAAA;UAAA,OAESuD,iBAAiB,CAAC+D,OAAO,CAAC;QAAA;UAA3CG,QAAQ,GAAAG,SAAA,CAAAnI,IAAA;UAAAmI,SAAA,CAAA5H,IAAA;UAAA,OACU4F,aAAa,CAAC2B,QAAQ,CAAC;QAAA;UAAzCG,SAAS,GAAAE,SAAA,CAAAnI,IAAA;UAAA,OAAAmI,SAAA,CAAAhI,MAAA,cAAAqI,MAAA,CAAAC,kBAAA,CACJT,QAAQ,GAAAS,kBAAA,CAAKR,SAAS;QAAA;QAAA;UAAA,OAAAE,SAAA,CAAA9F,IAAA;MAAA;IAAA,GAAAqF,QAAA;EAAA,CAEpC;EAAA,gBAtBYF,YAAYA,CAAAkB,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAnB,KAAA,CAAAzE,KAAA,OAAAD,SAAA;EAAA;AAAA,GAsBxB;AACD,OAAO,IAAM8F,cAAc;EAAA,IAAAC,KAAA,GAAAhG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsH,SAAOpF,IAAI,EAAEqF,SAAS;IAAA,IAAArB,OAAA;MAAAsB,kBAAA;MAAAC,iBAAA;MAAArD,KAAA;MAAAC,IAAA;MAAAqD,SAAA;MAAAC,SAAA;MAAAC,WAAA;MAAAC,MAAA;MAAAC,KAAA;MAAAC,cAAA;MAAAC,mBAAA;MAAAC,iBAAA;MAAAC,oBAAA;MAAAC,kBAAA;MAAAC,oBAAA;MAAAxF,QAAA;MAAAyF,oBAAA;MAAAC,oBAAA;MAAAC,oBAAA;MAAAC,oBAAA;MAAAC,oBAAA;MAAAC,oBAAA;MAAAC,OAAA;MAAAC,MAAA;MAAAC,OAAA;MAAAC,MAAA;MAAAC,OAAA;MAAAC,MAAA,GAAA1H,SAAA;IAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAA8M,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAzI,IAAA,GAAAyI,SAAA,CAAApK,IAAA;QAAA;UAAEoH,OAAO,GAAA8C,MAAA,CAAArJ,MAAA,QAAAqJ,MAAA,QAAAG,SAAA,GAAAH,MAAA,MAAG,EAAE;UAAExB,kBAAkB,GAAAwB,MAAA,CAAArJ,MAAA,OAAAqJ,MAAA,MAAAG,SAAA;UAC9E1B,iBAAiB,GAAG,CAAC,CAAC;UAC5B,KAASrD,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGoD,kBAAkB,CAAC7H,MAAM,EAAEyE,KAAK,EAAE,EAAE;YACxDC,IAAI,GAAGmD,kBAAkB,CAACpD,KAAK,CAAC;YACtCqD,iBAAiB,CAACpD,IAAI,CAAC9B,GAAG,CAAC,GAAG8B,IAAI;UACpC;UACMqD,SAAS,GAAG,EAAE;UACdC,SAAS,GAAG,EAAE;UACdC,WAAW,GAAG,EAAE;UACbxD,MAAK,GAAG,CAAC;QAAA;UAAA,MAAEA,MAAK,GAAGlC,IAAI,CAACvC,MAAM;YAAAuJ,SAAA,CAAApK,IAAA;YAAA;UAAA;UAC/BuF,KAAI,GAAGnC,IAAI,CAACkC,MAAK,CAAC;UAClB2D,cAAc,GAAGN,iBAAiB,CAACpD,KAAI,CAACwC,QAAQ,CAAC,IAAI,EAAE;UAAA,KACzDkB,cAAc;YAAAmB,SAAA,CAAApK,IAAA;YAAA;UAAA;UAChB4I,SAAS,CAACpI,IAAI,CAAC+E,KAAI,CAACwC,QAAQ,CAAC;UACzBoB,iBAAiB,GAAG,EAAE;UAAA,MACtB,EAAAD,mBAAA,GAAA3D,KAAI,CAAC+E,aAAa,cAAApB,mBAAA,uBAAlBA,mBAAA,CAAoBqB,WAAW,MAAK,UAAU;YAAAH,SAAA,CAAApK,IAAA;YAAA;UAAA;UAC1CqJ,kBAAkB,GAAG,EAAAD,oBAAA,GAAA7D,KAAI,CAAC+E,aAAa,cAAAlB,oBAAA,uBAAlBA,oBAAA,CAAoBoB,YAAY,MAAK1H,aAAa,CAACtG,KAAK,GAAGqG,IAAI,CAACrG,KAAK,CAACgH,SAAS;UAAA,KACtG6F,kBAAkB;YAAAe,SAAA,CAAApK,IAAA;YAAA;UAAA;UACpBmJ,iBAAiB,GAAG,UAAU;UAAAiB,SAAA,CAAApK,IAAA;UAAA;QAAA;UAAAoK,SAAA,CAAApK,IAAA;UAAA,OAEP+C,cAAc,EAAAuG,oBAAA,GAAC/D,KAAI,CAAC+E,aAAa,cAAAhB,oBAAA,uBAAlBA,oBAAA,CAAoBkB,YAAY,CAAC;QAAA;UAAjE1G,QAAQ,GAAAsG,SAAA,CAAA3K,IAAA;UACd0J,iBAAiB,GAAGrF,QAAQ,CAAC7C,IAAI,GAAG,SAAS;QAAA;UAGjD,IAAIsE,KAAI,CAACkF,KAAK,EAAE;YACd5B,SAAS,CAACrI,IAAI,CAAC;cAAEiK,KAAK,EAAElF,KAAI,CAACkF,KAAK;cAAEC,WAAW,EAAEnF,KAAI,CAACoF,kBAAkB;cAAEzH,EAAE,EAAEqC,KAAI,CAACwC,QAAQ;cAAEA,QAAQ,EAAExC,KAAI,CAACwC,QAAQ;cAAEpK,IAAI,EAAE4H,KAAI,CAACyC,gBAAgB;cAAEiB,cAAc,EAAEA,cAAc;cAAE2B,QAAQ,GAAArB,oBAAA,GAAEhE,KAAI,CAAC+E,aAAa,cAAAf,oBAAA,uBAAlBA,oBAAA,CAAoBqB,QAAQ;cAAEL,WAAW,EAAE,EAAAf,oBAAA,GAAAjE,KAAI,CAAC+E,aAAa,cAAAd,oBAAA,uBAAlBA,oBAAA,CAAoBe,WAAW,KAAI,WAAW;cAAEM,OAAO,GAAApB,oBAAA,GAAElE,KAAI,CAAC+E,aAAa,cAAAb,oBAAA,uBAAlBA,oBAAA,CAAoBoB,OAAO;cAAE1B,iBAAiB;cAAE2B,KAAK,EAAEvF,KAAI,CAACwF;YAAmB,CAAC,CAAC;UACpX,CAAC,MAAM;YACLjC,WAAW,CAACtI,IAAI,CAAC;cAAEiK,KAAK,EAAElF,KAAI,CAACkF,KAAK;cAAEC,WAAW,EAAEnF,KAAI,CAACoF,kBAAkB;cAAEzH,EAAE,EAAEqC,KAAI,CAACwC,QAAQ;cAAEA,QAAQ,EAAExC,KAAI,CAACwC,QAAQ;cAAEpK,IAAI,EAAE4H,KAAI,CAACyC,gBAAgB;cAAEiB,cAAc,EAAEA,cAAc;cAAE2B,QAAQ,GAAAlB,oBAAA,GAAEnE,KAAI,CAAC+E,aAAa,cAAAZ,oBAAA,uBAAlBA,oBAAA,CAAoBkB,QAAQ;cAAEL,WAAW,EAAE,EAAAZ,oBAAA,GAAApE,KAAI,CAAC+E,aAAa,cAAAX,oBAAA,uBAAlBA,oBAAA,CAAoBY,WAAW,KAAI,WAAW;cAAEM,OAAO,GAAAjB,oBAAA,GAAErE,KAAI,CAAC+E,aAAa,cAAAV,oBAAA,uBAAlBA,oBAAA,CAAoBiB,OAAO;cAAE1B,iBAAiB;cAAE2B,KAAK,EAAEvF,KAAI,CAACwF;YAAmB,CAAC,CAAC;UACtX;QAAC;UAnBoCzF,MAAK,EAAE;UAAA8E,SAAA,CAAApK,IAAA;UAAA;QAAA;UAsBhD,KAASsF,OAAK,GAAG,CAAC,EAAEA,OAAK,GAAGmD,SAAS,CAAC5H,MAAM,EAAEyE,OAAK,EAAE,EAAE;YAC/CC,MAAI,GAAGkD,SAAS,CAACnD,OAAK,CAAC;YAC7B,IAAI,EAACsD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,QAAQ,CAACvC,MAAI,CAACrC,EAAE,CAAC,GAAEqC,MAAI,CAACkF,KAAK,GAAG5B,SAAS,CAACrI,IAAI,CAAC+E,MAAI,CAAC,GAAGuD,WAAW,CAACtI,IAAI,CAAC+E,MAAI,CAAC;UAC/F;UACA,KAASD,OAAK,GAAG,CAAC,EAAEA,OAAK,GAAG8B,OAAO,CAACvG,MAAM,EAAEyE,OAAK,EAAE,EAAE;YAC7CC,MAAI,GAAG6B,OAAO,CAAC9B,OAAK,CAAC;YAC3B,IAAI,EAACsD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,QAAQ,CAACvC,MAAI,CAACrC,EAAE,CAAC,GAAEqC,MAAI,CAACkF,KAAK,GAAG5B,SAAS,CAACrI,IAAI,CAAC+E,MAAI,CAAC,GAAGuD,WAAW,CAACtI,IAAI,CAAC+E,MAAI,CAAC;UAC/F;UACM0E,OAAO,MAAAhC,MAAA,CAAAC,kBAAA,CAAQW,SAAS,CAACmC,IAAI,CAAC,UAACrO,CAAC,EAAEsO,CAAC;YAAA,OAAKA,CAAC,CAACL,QAAQ,GAAGjO,CAAC,CAACiO,QAAQ;UAAA,EAAC,GAAA1C,kBAAA,CAAOY,WAAW,CAACkC,IAAI,CAAC,UAACrO,CAAC,EAAEsO,CAAC;YAAA,OAAKA,CAAC,CAACL,QAAQ,GAAGjO,CAAC,CAACiO,QAAQ;UAAA,EAAC;UAChIM,OAAO,CAACC,GAAG,CAAClB,OAAO,CAAC;UAAA,OAAAG,SAAA,CAAAxK,MAAA,WACbqK,OAAO;QAAA;QAAA;UAAA,OAAAG,SAAA,CAAAtI,IAAA;MAAA;IAAA,GAAA0G,QAAA;EAAA,CACf;EAAA,gBA1CYF,cAAcA,CAAA8C,GAAA,EAAAC,IAAA;IAAA,OAAA9C,KAAA,CAAA9F,KAAA,OAAAD,SAAA;EAAA;AAAA,GA0C1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}