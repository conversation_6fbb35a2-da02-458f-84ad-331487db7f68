{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nvar _elAttr = function elAttr(elList, id) {\n  var elArr = [];\n  var styleObj = {};\n  for (var index = 0; index < elList.length; index++) {\n    var item = elList[index];\n    if (item.nodeName !== '#text') {\n      if (item.getAttribute('data-umpos') === id) {\n        var elParent = item.parentNode;\n        var is = 0;\n        while (elParent.style[is]) {\n          var elParentStyleKey = elParent.style[is].replace(/-([a-z])/g, function (p, m) {\n            return m.toUpperCase();\n          });\n          styleObj[elParent.style[is]] = elParent.style[elParentStyleKey];\n          is++;\n        }\n        elArr.push(item);\n      }\n    }\n    if (item.childNodes.length) {\n      var obj = _elAttr(item.childNodes, id);\n      elArr = [].concat(_toConsumableArray(elArr), _toConsumableArray(obj.elArr));\n      styleObj = _objectSpread(_objectSpread({}, styleObj), obj.styleObj);\n    }\n  }\n  return {\n    elArr,\n    styleObj\n  };\n};\nexport { _elAttr as elAttr };", "map": {"version": 3, "names": ["elAttr", "elList", "id", "el<PERSON>rr", "styleObj", "index", "length", "item", "nodeName", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "is", "style", "elParentStyleKey", "replace", "p", "m", "toUpperCase", "push", "childNodes", "obj", "concat", "_toConsumableArray", "_objectSpread", "_elAttr"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiToolBoxFunction/IntelligentErrorCorrection/IntelligentErrorCorrection.js"], "sourcesContent": ["export const elAttr = (elList, id) => {\r\n  let elArr = []\r\n  let styleObj = {}\r\n  for (let index = 0; index < elList.length; index++) {\r\n    const item = elList[index]\r\n    if (item.nodeName !== '#text') {\r\n      if (item.getAttribute('data-umpos') === id) {\r\n        const elParent = item.parentNode\r\n        let is = 0\r\n        while (elParent.style[is]) {\r\n          const elParentStyleKey = elParent.style[is].replace(/-([a-z])/g, (p, m) => m.toUpperCase())\r\n          styleObj[elParent.style[is]] = elParent.style[elParentStyleKey]\r\n          is++\r\n        }\r\n        elArr.push(item)\r\n      }\r\n    }\r\n    if (item.childNodes.length) {\r\n      const obj = elAttr(item.childNodes, id)\r\n      elArr = [...elArr, ...obj.elArr]\r\n      styleObj = { ...styleObj, ...obj.styleObj }\r\n    }\r\n  }\r\n\r\n  return { elArr, styleObj }\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAO,IAAMA,OAAM,GAAG,SAATA,MAAMA,CAAIC,MAAM,EAAEC,EAAE,EAAK;EACpC,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGJ,MAAM,CAACK,MAAM,EAAED,KAAK,EAAE,EAAE;IAClD,IAAME,IAAI,GAAGN,MAAM,CAACI,KAAK,CAAC;IAC1B,IAAIE,IAAI,CAACC,QAAQ,KAAK,OAAO,EAAE;MAC7B,IAAID,IAAI,CAACE,YAAY,CAAC,YAAY,CAAC,KAAKP,EAAE,EAAE;QAC1C,IAAMQ,QAAQ,GAAGH,IAAI,CAACI,UAAU;QAChC,IAAIC,EAAE,GAAG,CAAC;QACV,OAAOF,QAAQ,CAACG,KAAK,CAACD,EAAE,CAAC,EAAE;UACzB,IAAME,gBAAgB,GAAGJ,QAAQ,CAACG,KAAK,CAACD,EAAE,CAAC,CAACG,OAAO,CAAC,WAAW,EAAE,UAACC,CAAC,EAAEC,CAAC;YAAA,OAAKA,CAAC,CAACC,WAAW,CAAC,CAAC;UAAA,EAAC;UAC3Fd,QAAQ,CAACM,QAAQ,CAACG,KAAK,CAACD,EAAE,CAAC,CAAC,GAAGF,QAAQ,CAACG,KAAK,CAACC,gBAAgB,CAAC;UAC/DF,EAAE,EAAE;QACN;QACAT,KAAK,CAACgB,IAAI,CAACZ,IAAI,CAAC;MAClB;IACF;IACA,IAAIA,IAAI,CAACa,UAAU,CAACd,MAAM,EAAE;MAC1B,IAAMe,GAAG,GAAGrB,OAAM,CAACO,IAAI,CAACa,UAAU,EAAElB,EAAE,CAAC;MACvCC,KAAK,MAAAmB,MAAA,CAAAC,kBAAA,CAAOpB,KAAK,GAAAoB,kBAAA,CAAKF,GAAG,CAAClB,KAAK,EAAC;MAChCC,QAAQ,GAAAoB,aAAA,CAAAA,aAAA,KAAQpB,QAAQ,GAAKiB,GAAG,CAACjB,QAAQ,CAAE;IAC7C;EACF;EAEA,OAAO;IAAED,KAAK;IAAEC;EAAS,CAAC;AAC5B,CAAC;AAAA,SAAAqB,OAAA,IAAAzB,MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}