{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalAiChart\",\n  ref: \"elChartRef\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, null, 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalAiChat\\GlobalAiChart.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChart\" ref=\"elChartRef\"></div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChart' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nimport * as echarts from 'echarts'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({ option: { type: Object, default: () => ({}) } })\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst initChart = () => {\r\n  if (!elChart) { elChart = echarts.init(elChartRef.value) }\r\n  elChart.setOption(props.option)\r\n}\r\nonMounted(() => {\r\n  initChart()\r\n  nextTick(() => {\r\n    erd.listenTo(elChartRef.value, (element) => {\r\n      elChart?.resize()\r\n    })\r\n  })\r\n})\r\nonUnmounted(() => { erd.uninstall(elChartRef.value) })\r\nwatch(() => props.option, () => {\r\n  initChart()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiChart {\r\n  width: 100%;\r\n  height: 320px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,eAAe;EAACC,GAAG,EAAC;;;uBAA/BC,mBAAA,CAAkD,OAAlDC,UAAkD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}