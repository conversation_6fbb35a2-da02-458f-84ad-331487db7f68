{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport api from '@/api';\nimport { ref, computed, watch, onMounted, nextTick } from 'vue';\nimport { useStore } from 'vuex';\nvar __default__ = {\n  name: 'GlobalRegionSelect'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var emit = __emit;\n    var store = useStore();\n    var user = computed(function () {\n      return store.getters.getUserFn;\n    });\n    var region = computed(function () {\n      return store.getters.getAreaFn;\n    });\n    var regionIcon = `<svg t=\"1744792270111\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"19596\" width=\"24\" height=\"24\"><path d=\"M753.810286 731.428571c74.459429 10.678857 137.142857 26.185143 181.76 44.909715 58.002286 24.283429 88.429714 55.588571 88.429714 93.330285 0 29.988571-19.017143 56.027429-55.588571 77.385143-27.209143 15.872-64.731429 29.842286-111.542858 41.398857C764.196571 1011.419429 641.828571 1024 512 1024s-252.196571-12.580571-344.868571-35.547429c-46.811429-11.629714-84.406857-25.6-111.616-41.398857C19.163429 925.769143 0 899.510857 0 869.668571c0-37.814857 30.134857-69.046857 87.917714-93.330285 39.789714-16.676571 93.622857-30.793143 156.818286-41.106286l24.137143-3.657143 10.605714 72.411429c-67.876571 9.728-124.050286 23.478857-162.596571 39.716571-28.598857 12.068571-42.788571 24.137143-42.788572 25.965714 0 6.875429 38.546286 29.769143 111.030857 47.762286C271.872 939.008 387.949714 950.857143 512 950.857143c123.611429 0 239.835429-11.922286 326.875429-33.426286 72.557714-17.993143 111.030857-40.886857 111.030857-47.762286 0-1.828571-14.336-13.897143-43.154286-26.038857-34.523429-14.482286-82.944-26.916571-141.165714-36.352l-22.308572-3.437714 10.532572-72.411429z m15.652571-623.908571a352.841143 352.841143 0 0 1 102.180572 253.44 376.685714 376.685714 0 0 1-99.181715 252.342857l-13.019428 13.458286-213.357715 211.821714a54.198857 54.198857 0 0 1-69.266285 5.632l-6.656-5.632L256.804571 626.834286a376.685714 376.685714 0 0 1-110.445714-265.801143A352.841143 352.841143 0 0 1 248.466286 107.52a369.298286 369.298286 0 0 1 520.923428 0z m-475.721143 46.518857a288.402286 288.402286 0 0 0-83.382857 206.921143 312.100571 312.100571 0 0 0 79.725714 207.140571l12.214858 12.8 206.701714 204.361143 206.701714-204.361143a312.100571 312.100571 0 0 0 91.940572-219.940571 288.402286 288.402286 0 0 0-84.699429-206.994286 304.274286 304.274286 0 0 0-429.202286 0z m215.259429 38.765714a170.642286 170.642286 0 1 1 0 341.357715 170.642286 170.642286 0 0 1 0-341.284572z m0 64.073143a106.642286 106.642286 0 1 0 0 213.284572 106.642286 106.642286 0 0 0 0-213.284572z\" fill=\"#ffffff\" p-id=\"19597\"></path></svg>`;\n    var show = ref(false);\n    var treeRef = ref();\n    var regionId = ref('');\n    var regionType = ref('');\n    var regionList = ref([]);\n    var regionKeys = ref([]);\n    var oldRegionInfo = ref({});\n    var filterText = ref('');\n    var groupAreaDataByLevel = function groupAreaDataByLevel(areaData) {\n      if (!Array.isArray(areaData)) return [];\n      // 预定义已知的 areaLevel 及其名称\n      var levelMap = {\n        province: {\n          id: 'province',\n          name: '省',\n          data: []\n        },\n        city: {\n          id: 'city',\n          name: '市',\n          data: []\n        },\n        county: {\n          id: 'county',\n          name: '区县',\n          data: []\n        },\n        other: {\n          id: 'other',\n          name: '其他',\n          data: []\n        }\n      };\n      // 使用 reduce 方法处理数据分组\n      var groupedData = areaData.reduce(function (acc, item) {\n        var level = item.areaLevel;\n        var targetGroup = levelMap[level] || levelMap.other;\n        targetGroup.data.push(item);\n        return acc;\n      }, levelMap);\n      // 只返回 data 数组有数据的对象\n      return Object.values(groupedData).filter(function (group) {\n        return group.data.length > 0;\n      });\n    };\n    var _handleMethods = function handleMethods(data) {\n      var newData = [];\n      for (var index = 0; index < data.length; index++) {\n        var item = data[index];\n        newData.push(item);\n        newData = [].concat(_toConsumableArray(newData), _toConsumableArray(_handleMethods(item.children)));\n      }\n      return newData;\n    };\n    var filterNode = function filterNode(value, data) {\n      if (!value) return true;\n      return data.name.includes(value);\n    };\n    var handleRegionClick = function handleRegionClick(data) {\n      sessionStorage.setItem('isRegionSelect', '1');\n      filterText.value = '';\n      emit('callback', data);\n    };\n    var loginLastAreaId = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var newOldRegionInfo, _yield$api$loginLastA, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              newOldRegionInfo = sessionStorage.getItem('oldRegionInfo') || '';\n              if (!newOldRegionInfo) {\n                _context.next = 5;\n                break;\n              }\n              oldRegionInfo.value = JSON.parse(newOldRegionInfo);\n              _context.next = 10;\n              break;\n            case 5:\n              _context.next = 7;\n              return api.loginLastAreaId();\n            case 7:\n              _yield$api$loginLastA = _context.sent;\n              data = _yield$api$loginLastA.data;\n              oldRegionInfo.value = data;\n            case 10:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function loginLastAreaId() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    onMounted(function () {\n      loginLastAreaId();\n      setTimeout(function () {\n        show.value = true;\n        nextTick(function () {\n          var _treeRef$value;\n          (_treeRef$value = treeRef.value) === null || _treeRef$value === void 0 || _treeRef$value.setCurrentKey(regionId.value);\n        });\n      }, 99);\n    });\n    watch(function () {\n      return user.value;\n    }, function () {\n      var _user$value;\n      regionId.value = (_user$value = user.value) === null || _user$value === void 0 ? void 0 : _user$value.areaId;\n      nextTick(function () {\n        var _treeRef$value2;\n        (_treeRef$value2 = treeRef.value) === null || _treeRef$value2 === void 0 || _treeRef$value2.setCurrentKey(regionId.value);\n      });\n    }, {\n      immediate: true\n    });\n    watch(function () {\n      return region.value;\n    }, function () {\n      var newData = _handleMethods(region.value);\n      regionType.value = newData.length < 10 ? 'list' : 'tree';\n      if (regionType.value === 'list') {\n        regionList.value = groupAreaDataByLevel(newData);\n      } else if (regionType.value === 'tree') {\n        regionKeys.value = [region.value[0].id] || [];\n        nextTick(function () {\n          var _treeRef$value3;\n          (_treeRef$value3 = treeRef.value) === null || _treeRef$value3 === void 0 || _treeRef$value3.setCurrentKey(regionId.value);\n        });\n      }\n    }, {\n      immediate: true\n    });\n    watch(filterText, function (val) {\n      var _treeRef$value4;\n      (_treeRef$value4 = treeRef.value) === null || _treeRef$value4 === void 0 || _treeRef$value4.filter(val);\n    });\n    var __returned__ = {\n      emit,\n      store,\n      user,\n      region,\n      regionIcon,\n      show,\n      treeRef,\n      regionId,\n      regionType,\n      regionList,\n      regionKeys,\n      oldRegionInfo,\n      filterText,\n      groupAreaDataByLevel,\n      handleMethods: _handleMethods,\n      filterNode,\n      handleRegionClick,\n      loginLastAreaId,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      watch,\n      onMounted,\n      nextTick,\n      get useStore() {\n        return useStore;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "api", "ref", "computed", "watch", "onMounted", "nextTick", "useStore", "__default__", "emit", "__emit", "store", "user", "getters", "getUserFn", "region", "getAreaFn", "regionIcon", "show", "treeRef", "regionId", "regionType", "regionList", "regionKeys", "oldRegionInfo", "filterText", "groupAreaDataByLevel", "areaData", "levelMap", "province", "id", "data", "city", "county", "other", "groupedData", "reduce", "acc", "item", "level", "areaLevel", "targetGroup", "filter", "group", "handleMethods", "newData", "index", "concat", "children", "filterNode", "includes", "handleRegionClick", "sessionStorage", "setItem", "loginLastAreaId", "_ref2", "_callee", "newOldRegionInfo", "_yield$api$loginLastA", "_callee$", "_context", "getItem", "JSON", "parse", "setTimeout", "_treeRef$value", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "_user$value", "areaId", "_treeRef$value2", "immediate", "_treeRef$value3", "val", "_treeRef$value4"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutContainer/components/GlobalRegionSelect.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalRegionSelect\">\r\n    <transition enter-active-class=\"animate__animated animate__zoomIn animate__faster\"\r\n      leave-active-class=\"animate__animated animate__zoomOut animate__faster\">\r\n      <div class=\"GlobalRegionSelectBody\" v-if=\"show\">\r\n        <div class=\"GlobalRegionSelectBodyTitle\">选择登录地区</div>\r\n        <div class=\"GlobalRegionSelectBodyInfo\" v-if=\"regionType === 'list'\">\r\n          <div class=\"GlobalRegionSelectBodyInfoOld\">上次登录地区：{{ oldRegionInfo.name }}</div>\r\n          <div class=\"GlobalRegionSelectBodyInfoTip\">请在下方选择本次登录地区！</div>\r\n        </div>\r\n        <el-scrollbar class=\"GlobalRegionSelectScrollbar\" v-if=\"regionType === 'list'\">\r\n          <div class=\"GlobalRegionSelectList\" v-for=\"item in regionList\" :key=\"item.id\">\r\n            <div class=\"GlobalRegionSelectColumn\" v-if=\"item.id !== 'province'\">{{ item.name }}</div>\r\n            <div class=\"GlobalRegionSelectItem\" :class=\"{ 'is-active': row.id === regionId }\" v-for=\"row in item.data\"\r\n              :key=\"row.id\" @click=\"handleRegionClick(row)\">\r\n              <div class=\"GlobalRegionSelectIcon\" v-html=\"regionIcon\"></div>\r\n              <div class=\"GlobalRegionSelectName\">{{ row.name }}</div>\r\n            </div>\r\n          </div>\r\n        </el-scrollbar>\r\n        <div class=\"GlobalRegionSelectBodyInfo\" v-if=\"regionType === 'tree'\">\r\n          <div class=\"GlobalRegionSelectBodyInfoOld\">上次登录地区：{{ oldRegionInfo.name }}</div>\r\n          <el-input v-model=\"filterText\" placeholder=\"请输入地区名称\" clearable />\r\n        </div>\r\n        <el-scrollbar class=\"GlobalRegionSelectTree\" v-if=\"regionType === 'tree'\">\r\n          <el-tree ref=\"treeRef\" node-key=\"id\" :data=\"region\" highlight-current\r\n            :props=\"{ label: 'name', children: 'children' }\" :filter-node-method=\"filterNode\"\r\n            :default-expanded-keys=\"regionKeys\" @node-click=\"handleRegionClick\" />\r\n        </el-scrollbar>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalRegionSelect' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, watch, onMounted, nextTick } from 'vue'\r\nimport { useStore } from 'vuex'\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst store = useStore()\r\nconst user = computed(() => store.getters.getUserFn)\r\nconst region = computed(() => store.getters.getAreaFn)\r\n\r\nconst regionIcon = `<svg t=\"1744792270111\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"19596\" width=\"24\" height=\"24\"><path d=\"M753.810286 731.428571c74.459429 10.678857 137.142857 26.185143 181.76 44.909715 58.002286 24.283429 88.429714 55.588571 88.429714 93.330285 0 29.988571-19.017143 56.027429-55.588571 77.385143-27.209143 15.872-64.731429 29.842286-111.542858 41.398857C764.196571 1011.419429 641.828571 1024 512 1024s-252.196571-12.580571-344.868571-35.547429c-46.811429-11.629714-84.406857-25.6-111.616-41.398857C19.163429 925.769143 0 899.510857 0 869.668571c0-37.814857 30.134857-69.046857 87.917714-93.330285 39.789714-16.676571 93.622857-30.793143 156.818286-41.106286l24.137143-3.657143 10.605714 72.411429c-67.876571 9.728-124.050286 23.478857-162.596571 39.716571-28.598857 12.068571-42.788571 24.137143-42.788572 25.965714 0 6.875429 38.546286 29.769143 111.030857 47.762286C271.872 939.008 387.949714 950.857143 512 950.857143c123.611429 0 239.835429-11.922286 326.875429-33.426286 72.557714-17.993143 111.030857-40.886857 111.030857-47.762286 0-1.828571-14.336-13.897143-43.154286-26.038857-34.523429-14.482286-82.944-26.916571-141.165714-36.352l-22.308572-3.437714 10.532572-72.411429z m15.652571-623.908571a352.841143 352.841143 0 0 1 102.180572 253.44 376.685714 376.685714 0 0 1-99.181715 252.342857l-13.019428 13.458286-213.357715 211.821714a54.198857 54.198857 0 0 1-69.266285 5.632l-6.656-5.632L256.804571 626.834286a376.685714 376.685714 0 0 1-110.445714-265.801143A352.841143 352.841143 0 0 1 248.466286 107.52a369.298286 369.298286 0 0 1 520.923428 0z m-475.721143 46.518857a288.402286 288.402286 0 0 0-83.382857 206.921143 312.100571 312.100571 0 0 0 79.725714 207.140571l12.214858 12.8 206.701714 204.361143 206.701714-204.361143a312.100571 312.100571 0 0 0 91.940572-219.940571 288.402286 288.402286 0 0 0-84.699429-206.994286 304.274286 304.274286 0 0 0-429.202286 0z m215.259429 38.765714a170.642286 170.642286 0 1 1 0 341.357715 170.642286 170.642286 0 0 1 0-341.284572z m0 64.073143a106.642286 106.642286 0 1 0 0 213.284572 106.642286 106.642286 0 0 0 0-213.284572z\" fill=\"#ffffff\" p-id=\"19597\"></path></svg>`\r\nconst show = ref(false)\r\nconst treeRef = ref()\r\nconst regionId = ref('')\r\nconst regionType = ref('')\r\nconst regionList = ref([])\r\nconst regionKeys = ref([])\r\nconst oldRegionInfo = ref({})\r\nconst filterText = ref('')\r\nconst groupAreaDataByLevel = (areaData) => {\r\n  if (!Array.isArray(areaData)) return []\r\n  // 预定义已知的 areaLevel 及其名称\r\n  const levelMap = {\r\n    province: { id: 'province', name: '省', data: [] },\r\n    city: { id: 'city', name: '市', data: [] },\r\n    county: { id: 'county', name: '区县', data: [] },\r\n    other: { id: 'other', name: '其他', data: [] }\r\n  }\r\n  // 使用 reduce 方法处理数据分组\r\n  const groupedData = areaData.reduce((acc, item) => {\r\n    const level = item.areaLevel\r\n    const targetGroup = levelMap[level] || levelMap.other\r\n    targetGroup.data.push(item)\r\n    return acc\r\n  }, levelMap)\r\n  // 只返回 data 数组有数据的对象\r\n  return Object.values(groupedData).filter(group => group.data.length > 0)\r\n}\r\nconst handleMethods = (data) => {\r\n  let newData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    newData.push(item)\r\n    newData = [...newData, ...handleMethods(item.children)]\r\n  }\r\n  return newData\r\n}\r\nconst filterNode = (value, data) => {\r\n  if (!value) return true\r\n  return data.name.includes(value)\r\n}\r\nconst handleRegionClick = (data) => {\r\n  sessionStorage.setItem('isRegionSelect', '1')\r\n  filterText.value = ''\r\n  emit('callback', data)\r\n}\r\nconst loginLastAreaId = async () => {\r\n  const newOldRegionInfo = sessionStorage.getItem('oldRegionInfo') || ''\r\n  if (newOldRegionInfo) {\r\n    oldRegionInfo.value = JSON.parse(newOldRegionInfo)\r\n  } else {\r\n    const { data } = await api.loginLastAreaId()\r\n    oldRegionInfo.value = data\r\n  }\r\n}\r\nonMounted(() => {\r\n  loginLastAreaId()\r\n  setTimeout(() => {\r\n    show.value = true\r\n    nextTick(() => { treeRef.value?.setCurrentKey(regionId.value) })\r\n  }, 99)\r\n})\r\nwatch(() => user.value, () => {\r\n  regionId.value = user.value?.areaId\r\n  nextTick(() => { treeRef.value?.setCurrentKey(regionId.value) })\r\n}, { immediate: true })\r\nwatch(() => region.value, () => {\r\n  const newData = handleMethods(region.value)\r\n  regionType.value = newData.length < 10 ? 'list' : 'tree'\r\n  if (regionType.value === 'list') {\r\n    regionList.value = groupAreaDataByLevel(newData)\r\n  } else if (regionType.value === 'tree') {\r\n    regionKeys.value = [region.value[0].id] || []\r\n    nextTick(() => { treeRef.value?.setCurrentKey(regionId.value) })\r\n  }\r\n}, { immediate: true })\r\nwatch(filterText, (val) => {\r\n  treeRef.value?.filter(val)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalRegionSelect {\r\n  width: 100vw;\r\n  height: 100vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  z-index: 999;\r\n\r\n  .GlobalRegionSelectBody {\r\n    width: 560px;\r\n    height: 85%;\r\n    min-height: 420px;\r\n    max-height: 580px;\r\n    padding: 20px 40px;\r\n    background: #fff;\r\n    border-radius: var(--el-border-radius-base);\r\n\r\n    .GlobalRegionSelectBodyTitle {\r\n      width: 100%;\r\n      height: var(--zy-height);\r\n      font-weight: bold;\r\n      text-align: center;\r\n      font-size: var(--zy-navigation-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .GlobalRegionSelectBodyInfo {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding-bottom: var(--zy-distance-five);\r\n\r\n      .GlobalRegionSelectBodyInfoOld {\r\n        height: var(--zy-height);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-height);\r\n      }\r\n\r\n      .GlobalRegionSelectBodyInfoTip {\r\n        height: var(--zy-height);\r\n        color: var(--zy-el-color-primary);\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-height);\r\n      }\r\n\r\n      .zy-el-input {\r\n        width: 220px;\r\n        --zy-el-input-height: var(--zy-height-routine);\r\n      }\r\n    }\r\n\r\n    .GlobalRegionSelectScrollbar {\r\n      width: 100%;\r\n      height: calc(100% - (var(--zy-height) * 2));\r\n\r\n      .GlobalRegionSelectList {\r\n        width: 100%;\r\n\r\n        .GlobalRegionSelectColumn {\r\n          width: 100%;\r\n          font-weight: bold;\r\n          padding: var(--zy-distance-five);\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n\r\n        .GlobalRegionSelectItem {\r\n          width: 100%;\r\n          height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2));\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 0 var(--zy-distance-five);\r\n          background: var(--zy-el-color-info-light-9);\r\n          border-radius: var(--el-border-radius-small);\r\n          margin-bottom: var(--zy-distance-five);\r\n          cursor: pointer;\r\n\r\n          &.is-active {\r\n            background: var(--zy-el-color-primary-light-9);\r\n\r\n            .GlobalRegionSelectName {\r\n              color: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n\r\n          .GlobalRegionSelectIcon {\r\n            width: 22px;\r\n            height: 22px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n\r\n            svg {\r\n              width: 20px;\r\n              height: 20px;\r\n\r\n              path {\r\n                fill: var(--zy-el-color-primary);\r\n              }\r\n            }\r\n          }\r\n\r\n          .GlobalRegionSelectName {\r\n            padding: var(--zy-distance-five);\r\n            font-size: var(--zy-text-font-size);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalRegionSelectTree {\r\n      width: 100%;\r\n      height: calc(100% - (var(--zy-height) * 2));\r\n\r\n      .zy-el-tree-node.is-current {\r\n        &>.zy-el-tree-node__content {\r\n          .zy-el-tree-node__label {\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-tree-node__content {\r\n        height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2));\r\n\r\n\r\n        .zy-el-tree-node__label {\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAsCA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AAAA,SAAAE,mBAAA3G,CAAA,WAAA4G,kBAAA,CAAA5G,CAAA,KAAA6G,gBAAA,CAAA7G,CAAA,KAAA8G,2BAAA,CAAA9G,CAAA,KAAA+G,kBAAA;AAAA,SAAAA,mBAAA,cAAAlD,SAAA;AAAA,SAAAiD,4BAAA9G,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAAgH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAAkH,QAAA,CAAArF,IAAA,CAAA5B,CAAA,EAAA4F,KAAA,6BAAA7F,CAAA,IAAAC,CAAA,CAAA+E,WAAA,KAAAhF,CAAA,GAAAC,CAAA,CAAA+E,WAAA,CAAAC,IAAA,aAAAjF,CAAA,cAAAA,CAAA,GAAAmH,KAAA,CAAAC,IAAA,CAAAnH,CAAA,oBAAAD,CAAA,+CAAAqH,IAAA,CAAArH,CAAA,IAAAiH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA;AAAA,SAAAmG,iBAAA7G,CAAA,8BAAAS,MAAA,YAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,aAAAX,CAAA,uBAAAkH,KAAA,CAAAC,IAAA,CAAAnH,CAAA;AAAA,SAAA4G,mBAAA5G,CAAA,QAAAkH,KAAA,CAAAG,OAAA,CAAArH,CAAA,UAAAgH,iBAAA,CAAAhH,CAAA;AAAA,SAAAgH,kBAAAhH,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA4E,MAAA,MAAAlE,CAAA,GAAAV,CAAA,CAAA4E,MAAA,YAAA9E,CAAA,MAAAK,CAAA,GAAA+G,KAAA,CAAAxG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AADA,OAAOmH,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC/D,SAASC,QAAQ,QAAQ,MAAM;AAL/B,IAAAC,WAAA,GAAe;EAAE7C,IAAI,EAAE;AAAqB,CAAC;;;;;;;IAM7C,IAAM8C,IAAI,GAAGC,MAAyB;IAEtC,IAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC;IACxB,IAAMK,IAAI,GAAGT,QAAQ,CAAC;MAAA,OAAMQ,KAAK,CAACE,OAAO,CAACC,SAAS;IAAA,EAAC;IACpD,IAAMC,MAAM,GAAGZ,QAAQ,CAAC;MAAA,OAAMQ,KAAK,CAACE,OAAO,CAACG,SAAS;IAAA,EAAC;IAEtD,IAAMC,UAAU,GAAG,8nEAA8nE;IACjpE,IAAMC,IAAI,GAAGhB,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMiB,OAAO,GAAGjB,GAAG,CAAC,CAAC;IACrB,IAAMkB,QAAQ,GAAGlB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMmB,UAAU,GAAGnB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMoB,UAAU,GAAGpB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMqB,UAAU,GAAGrB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMsB,aAAa,GAAGtB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAMuB,UAAU,GAAGvB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMwB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,QAAQ,EAAK;MACzC,IAAI,CAAC9B,KAAK,CAACG,OAAO,CAAC2B,QAAQ,CAAC,EAAE,OAAO,EAAE;MACvC;MACA,IAAMC,QAAQ,GAAG;QACfC,QAAQ,EAAE;UAAEC,EAAE,EAAE,UAAU;UAAEnE,IAAI,EAAE,GAAG;UAAEoE,IAAI,EAAE;QAAG,CAAC;QACjDC,IAAI,EAAE;UAAEF,EAAE,EAAE,MAAM;UAAEnE,IAAI,EAAE,GAAG;UAAEoE,IAAI,EAAE;QAAG,CAAC;QACzCE,MAAM,EAAE;UAAEH,EAAE,EAAE,QAAQ;UAAEnE,IAAI,EAAE,IAAI;UAAEoE,IAAI,EAAE;QAAG,CAAC;QAC9CG,KAAK,EAAE;UAAEJ,EAAE,EAAE,OAAO;UAAEnE,IAAI,EAAE,IAAI;UAAEoE,IAAI,EAAE;QAAG;MAC7C,CAAC;MACD;MACA,IAAMI,WAAW,GAAGR,QAAQ,CAACS,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAK;QACjD,IAAMC,KAAK,GAAGD,IAAI,CAACE,SAAS;QAC5B,IAAMC,WAAW,GAAGb,QAAQ,CAACW,KAAK,CAAC,IAAIX,QAAQ,CAACM,KAAK;QACrDO,WAAW,CAACV,IAAI,CAAC7E,IAAI,CAACoF,IAAI,CAAC;QAC3B,OAAOD,GAAG;MACZ,CAAC,EAAET,QAAQ,CAAC;MACZ;MACA,OAAOhJ,MAAM,CAACuC,MAAM,CAACgH,WAAW,CAAC,CAACO,MAAM,CAAC,UAAAC,KAAK;QAAA,OAAIA,KAAK,CAACZ,IAAI,CAACxE,MAAM,GAAG,CAAC;MAAA,EAAC;IAC1E,CAAC;IACD,IAAMqF,cAAa,GAAG,SAAhBA,aAAaA,CAAIb,IAAI,EAAK;MAC9B,IAAIc,OAAO,GAAG,EAAE;MAChB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGf,IAAI,CAACxE,MAAM,EAAEuF,KAAK,EAAE,EAAE;QAChD,IAAMR,IAAI,GAAGP,IAAI,CAACe,KAAK,CAAC;QACxBD,OAAO,CAAC3F,IAAI,CAACoF,IAAI,CAAC;QAClBO,OAAO,MAAAE,MAAA,CAAAzD,kBAAA,CAAOuD,OAAO,GAAAvD,kBAAA,CAAKsD,cAAa,CAACN,IAAI,CAACU,QAAQ,CAAC,EAAC;MACzD;MACA,OAAOH,OAAO;IAChB,CAAC;IACD,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CAAI/J,KAAK,EAAE6I,IAAI,EAAK;MAClC,IAAI,CAAC7I,KAAK,EAAE,OAAO,IAAI;MACvB,OAAO6I,IAAI,CAACpE,IAAI,CAACuF,QAAQ,CAAChK,KAAK,CAAC;IAClC,CAAC;IACD,IAAMiK,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIpB,IAAI,EAAK;MAClCqB,cAAc,CAACC,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC;MAC7C5B,UAAU,CAACvI,KAAK,GAAG,EAAE;MACrBuH,IAAI,CAAC,UAAU,EAAEsB,IAAI,CAAC;IACxB,CAAC;IACD,IAAMuB,eAAe;MAAA,IAAAC,KAAA,GAAAtE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4F,QAAA;QAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAA3B,IAAA;QAAA,OAAAvJ,mBAAA,GAAAuB,IAAA,UAAA4J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAvF,IAAA,GAAAuF,QAAA,CAAAlH,IAAA;YAAA;cAChB+G,gBAAgB,GAAGL,cAAc,CAACS,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE;cAAA,KAClEJ,gBAAgB;gBAAAG,QAAA,CAAAlH,IAAA;gBAAA;cAAA;cAClB8E,aAAa,CAACtI,KAAK,GAAG4K,IAAI,CAACC,KAAK,CAACN,gBAAgB,CAAC;cAAAG,QAAA,CAAAlH,IAAA;cAAA;YAAA;cAAAkH,QAAA,CAAAlH,IAAA;cAAA,OAE3BuD,GAAG,CAACqD,eAAe,CAAC,CAAC;YAAA;cAAAI,qBAAA,GAAAE,QAAA,CAAAzH,IAAA;cAApC4F,IAAI,GAAA2B,qBAAA,CAAJ3B,IAAI;cACZP,aAAa,CAACtI,KAAK,GAAG6I,IAAI;YAAA;YAAA;cAAA,OAAA6B,QAAA,CAAApF,IAAA;UAAA;QAAA,GAAAgF,OAAA;MAAA,CAE7B;MAAA,gBARKF,eAAeA,CAAA;QAAA,OAAAC,KAAA,CAAApE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQpB;IACDmB,SAAS,CAAC,YAAM;MACdiD,eAAe,CAAC,CAAC;MACjBU,UAAU,CAAC,YAAM;QACf9C,IAAI,CAAChI,KAAK,GAAG,IAAI;QACjBoH,QAAQ,CAAC,YAAM;UAAA,IAAA2D,cAAA;UAAE,CAAAA,cAAA,GAAA9C,OAAO,CAACjI,KAAK,cAAA+K,cAAA,eAAbA,cAAA,CAAeC,aAAa,CAAC9C,QAAQ,CAAClI,KAAK,CAAC;QAAC,CAAC,CAAC;MAClE,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,CAAC;IACFkH,KAAK,CAAC;MAAA,OAAMQ,IAAI,CAAC1H,KAAK;IAAA,GAAE,YAAM;MAAA,IAAAiL,WAAA;MAC5B/C,QAAQ,CAAClI,KAAK,IAAAiL,WAAA,GAAGvD,IAAI,CAAC1H,KAAK,cAAAiL,WAAA,uBAAVA,WAAA,CAAYC,MAAM;MACnC9D,QAAQ,CAAC,YAAM;QAAA,IAAA+D,eAAA;QAAE,CAAAA,eAAA,GAAAlD,OAAO,CAACjI,KAAK,cAAAmL,eAAA,eAAbA,eAAA,CAAeH,aAAa,CAAC9C,QAAQ,CAAClI,KAAK,CAAC;MAAC,CAAC,CAAC;IAClE,CAAC,EAAE;MAAEoL,SAAS,EAAE;IAAK,CAAC,CAAC;IACvBlE,KAAK,CAAC;MAAA,OAAMW,MAAM,CAAC7H,KAAK;IAAA,GAAE,YAAM;MAC9B,IAAM2J,OAAO,GAAGD,cAAa,CAAC7B,MAAM,CAAC7H,KAAK,CAAC;MAC3CmI,UAAU,CAACnI,KAAK,GAAG2J,OAAO,CAACtF,MAAM,GAAG,EAAE,GAAG,MAAM,GAAG,MAAM;MACxD,IAAI8D,UAAU,CAACnI,KAAK,KAAK,MAAM,EAAE;QAC/BoI,UAAU,CAACpI,KAAK,GAAGwI,oBAAoB,CAACmB,OAAO,CAAC;MAClD,CAAC,MAAM,IAAIxB,UAAU,CAACnI,KAAK,KAAK,MAAM,EAAE;QACtCqI,UAAU,CAACrI,KAAK,GAAG,CAAC6H,MAAM,CAAC7H,KAAK,CAAC,CAAC,CAAC,CAAC4I,EAAE,CAAC,IAAI,EAAE;QAC7CxB,QAAQ,CAAC,YAAM;UAAA,IAAAiE,eAAA;UAAE,CAAAA,eAAA,GAAApD,OAAO,CAACjI,KAAK,cAAAqL,eAAA,eAAbA,eAAA,CAAeL,aAAa,CAAC9C,QAAQ,CAAClI,KAAK,CAAC;QAAC,CAAC,CAAC;MAClE;IACF,CAAC,EAAE;MAAEoL,SAAS,EAAE;IAAK,CAAC,CAAC;IACvBlE,KAAK,CAACqB,UAAU,EAAE,UAAC+C,GAAG,EAAK;MAAA,IAAAC,eAAA;MACzB,CAAAA,eAAA,GAAAtD,OAAO,CAACjI,KAAK,cAAAuL,eAAA,eAAbA,eAAA,CAAe/B,MAAM,CAAC8B,GAAG,CAAC;IAC5B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}