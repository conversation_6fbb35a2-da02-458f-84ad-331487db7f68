{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeStyle as _normalizeStyle, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, withCtx as _withCtx, createBlock as _createBlock, normalizeClass as _normalizeClass, vShow as _vShow, withDirectives as _withDirectives, resolveDynamicComponent as _resolveDynamicComponent, KeepAlive as _KeepAlive, Transition as _Transition } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LayoutViewLogo\"\n};\nvar _hoisted_2 = {\n  class: \"LayoutViewName\"\n};\nvar _hoisted_3 = {\n  class: \"LayoutViewHeaderMenuItem forbidSelect\"\n};\nvar _hoisted_4 = {\n  class: \"LayoutViewInfo\",\n  ref: \"LayoutViewInfo\"\n};\nvar _hoisted_5 = {\n  class: \"LayoutViewOrganization\"\n};\nvar _hoisted_6 = {\n  class: \"LayoutViewOrganizationTrigger\"\n};\nvar _hoisted_7 = {\n  class: \"LayoutViewOrganizationText\"\n};\nvar _hoisted_8 = {\n  class: \"LayoutViewUser\"\n};\nvar _hoisted_9 = {\n  class: \"forbidSelect\"\n};\nvar _hoisted_10 = [\"innerHTML\"];\nvar _hoisted_11 = {\n  key: 0,\n  class: \"LayoutViewBreadcrumb\"\n};\nvar _hoisted_12 = {\n  class: \"LayoutViewBody\"\n};\nvar _hoisted_13 = {\n  key: 0,\n  class: \"ConstraintEditPassWord\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  var _component_el_tabs = _resolveComponent(\"el-tabs\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  var _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  var _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  var _component_xyl_region = _resolveComponent(\"xyl-region\");\n  var _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  var _component_el_header = _resolveComponent(\"el-header\");\n  var _component_xyl_menu = _resolveComponent(\"xyl-menu\");\n  var _component_el_aside = _resolveComponent(\"el-aside\");\n  var _component_xyl_tab_item = _resolveComponent(\"xyl-tab-item\");\n  var _component_xyl_tab = _resolveComponent(\"xyl-tab\");\n  var _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  var _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  var _component_router_view = _resolveComponent(\"router-view\");\n  var _component_el_main = _resolveComponent(\"el-main\");\n  var _component_el_container = _resolveComponent(\"el-container\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createVNode(_component_el_container, {\n    class: \"LayoutViewUnitedFront\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_header, {\n        class: \"LayoutViewHeader\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", {\n            class: \"LayoutViewBox\",\n            ref: \"LayoutViewBox\",\n            onClick: _cache[0] || (_cache[0] = function () {\n              return $setup.WorkBenchReturn && $setup.WorkBenchReturn.apply($setup, arguments);\n            }),\n            style: _normalizeStyle(`background: url('${$setup.layoutNameBg}') no-repeat;background-size: auto 100%;background-position: right;`)\n          }, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_image, {\n            src: $setup.systemLogo,\n            fit: \"cover\"\n          }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_2, _toDisplayString($setup.systemNameAreaPrefix === 'true' ? $setup.regionName : '') + _toDisplayString($setup.systemName), 1 /* TEXT */)], 4 /* STYLE */), $setup.isChildView ? (_openBlock(), _createElementBlock(\"div\", {\n            key: 0,\n            class: \"LayoutViewChildView\",\n            style: _normalizeStyle(`left:${$setup.left - 88}px;background: url('${$setup.layoutChildNameBg}') no-repeat;background-size: auto 100%;background-position: right;`)\n          }, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n            class: \"LayoutViewChildViewIcon\"\n          }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.childData.name), 1 /* TEXT */)], 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n            class: _normalizeClass([\"LayoutViewHeaderMenu\", {\n              isLayoutViewHeaderMenu: $setup.isChildView\n            }]),\n            style: _normalizeStyle(`${$setup.width}padding-left:${$setup.left}px;background: url('${$setup.isChildView ? $setup.layoutChildBg : ''}') no-repeat;background-size: 287px 65px;background-position: right bottom;`)\n          }, [_createVNode(_component_el_tabs, {\n            modelValue: $setup.tabMenu,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.tabMenu = $event;\n            }),\n            onTabChange: $setup.handleClick\n          }, {\n            default: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tabMenuData, function (item) {\n                return _openBlock(), _createBlock(_component_el_tab_pane, {\n                  key: item.id,\n                  name: item.id\n                }, {\n                  label: _withCtx(function () {\n                    return [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n                      src: item.icon,\n                      fit: \"cover\"\n                    }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", null, _toDisplayString(item.name), 1 /* TEXT */)])];\n                  }),\n                  _: 2 /* DYNAMIC */\n                }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"name\"]);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"onTabChange\"])], 6 /* CLASS, STYLE */), _createElementVNode(\"div\", _hoisted_4, [$setup.isChildView ? (_openBlock(), _createElementBlock(\"div\", {\n            key: 0,\n            class: \"WorkBenchReturn\",\n            onClick: _cache[2] || (_cache[2] = function () {\n              return $setup.WorkBenchReturn && $setup.WorkBenchReturn.apply($setup, arguments);\n            })\n          }, [_cache[12] || (_cache[12] = _createElementVNode(\"span\", null, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($setup.WorkBenchObj.name), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _withDirectives(_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_dropdown, {\n            onCommand: $setup.handleOrganizationChange,\n            trigger: \"click\"\n          }, {\n            dropdown: _withCtx(function () {\n              return [_createVNode(_component_el_dropdown_menu, null, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.organizationList, function (item) {\n                    var _$setup$currentOrgani, _$setup$currentOrgani2;\n                    return _openBlock(), _createBlock(_component_el_dropdown_item, {\n                      key: item.id || item.orgId,\n                      command: item,\n                      class: _normalizeClass({\n                        'is-active': (((_$setup$currentOrgani = $setup.currentOrganization) === null || _$setup$currentOrgani === void 0 ? void 0 : _$setup$currentOrgani.id) || ((_$setup$currentOrgani2 = $setup.currentOrganization) === null || _$setup$currentOrgani2 === void 0 ? void 0 : _$setup$currentOrgani2.orgId)) === (item.id || item.orgId)\n                      })\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.orgName), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"command\", \"class\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            default: _withCtx(function () {\n              var _$setup$currentOrgani3;\n              return [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"span\", _hoisted_7, _toDisplayString(((_$setup$currentOrgani3 = $setup.currentOrganization) === null || _$setup$currentOrgani3 === void 0 ? void 0 : _$setup$currentOrgani3.orgName) || '请选择组织'), 1 /* TEXT */), _createVNode(_component_el_icon, {\n                class: \"LayoutViewOrganizationIcon\"\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode($setup[\"ArrowDown\"])];\n                }),\n                _: 1 /* STABLE */\n              })])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onCommand\"])], 512 /* NEED_PATCH */), [[_vShow, !$setup.isChildView]]), _withDirectives(_createVNode(_component_xyl_region, {\n            modelValue: $setup.regionId,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.regionId = $event;\n            }),\n            data: $setup.area,\n            onSelect: $setup.regionSelect,\n            props: {\n              label: 'name',\n              children: 'children'\n            }\n          }, null, 8 /* PROPS */, [\"modelValue\", \"data\", \"onSelect\"]), [[_vShow, !$setup.isChildView]]), _createVNode(_component_el_tooltip, {\n            placement: \"top\",\n            effect: \"light\",\n            offset: 6,\n            disabled: !$setup.role.length\n          }, {\n            content: _withCtx(function () {\n              return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.role, function (item, index) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  class: \"LayoutViewRoleItem\",\n                  key: index\n                }, _toDisplayString(item), 1 /* TEXT */);\n              }), 128 /* KEYED_FRAGMENT */))];\n            }),\n            default: _withCtx(function () {\n              return [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_image, {\n                src: $setup.user.image,\n                fit: \"cover\"\n              }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", _hoisted_9, _toDisplayString($setup.user.userName), 1 /* TEXT */)])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"disabled\"]), _createVNode($setup[\"LayoutPersonalDoList\"]), _createVNode($setup[\"LayoutBoxMessage\"]), _createElementVNode(\"div\", {\n            class: \"LayoutViewRefresh\",\n            innerHTML: $setup.refreshIcon,\n            onClick: _cache[4] || (_cache[4] = function ($event) {\n              return $setup.handleCommand('refresh');\n            }),\n            title: \"重新加载平台\"\n          }, null, 8 /* PROPS */, _hoisted_10), _createVNode(_component_el_dropdown, {\n            onCommand: $setup.handleCommand\n          }, {\n            dropdown: _withCtx(function () {\n              return [_createVNode(_component_el_dropdown_menu, null, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_el_dropdown_item, {\n                    command: \"task\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[13] || (_cache[13] = [_createTextVNode(\"系统任务管理器\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createCommentVNode(\" <el-dropdown-item command=\\\"refresh\\\">重新加载平台</el-dropdown-item> \"), _createCommentVNode(\" <el-dropdown-item command=\\\"locale\\\">简繁切换</el-dropdown-item>\\r\\n              <el-dropdown-item command=\\\"help\\\">帮助文档</el-dropdown-item> \"), _createVNode(_component_el_dropdown_item, {\n                    command: \"edit_password\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[14] || (_cache[14] = [_createTextVNode(\"修改密码\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  }), _createVNode(_component_el_dropdown_item, {\n                    command: \"exit\"\n                  }, {\n                    default: _withCtx(function () {\n                      return _cache[15] || (_cache[15] = [_createTextVNode(\"安全退出\")]);\n                    }),\n                    _: 1 /* STABLE */\n                  })];\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            default: _withCtx(function () {\n              return [_cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n                class: \"LayoutOperation\"\n              }, null, -1 /* HOISTED */))];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onCommand\"])], 512 /* NEED_PATCH */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_container, {\n        class: \"LayoutViewContainer\"\n      }, {\n        default: _withCtx(function () {\n          return [_withDirectives(_createVNode(_component_el_aside, {\n            class: \"LayoutViewAside\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_xyl_menu, {\n                modelValue: $setup.menuId,\n                \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n                  return $setup.menuId = $event;\n                }),\n                menuData: $setup.menuData,\n                onSelect: $setup.menuClick\n              }, null, 8 /* PROPS */, [\"modelValue\", \"menuData\", \"onSelect\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */), [[_vShow, $setup.isView]]), _createVNode(_component_el_main, {\n            class: _normalizeClass([\"LayoutViewMain\", {\n              LayoutViewMainView: !$setup.isView,\n              LayoutViewMainBreadcrumb: !$setup.isView && $setup.tabData.length > 1\n            }])\n          }, {\n            default: _withCtx(function () {\n              return [_withDirectives(_createVNode(_component_xyl_tab, {\n                modelValue: $setup.menuId,\n                \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n                  return $setup.menuId = $event;\n                }),\n                onTabClick: $setup.tabClick,\n                onRefresh: $setup.handleRefresh,\n                onClose: $setup.handleClose,\n                onCloseOther: $setup.handleCloseOther\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tabData, function (item) {\n                    return _openBlock(), _createBlock(_component_xyl_tab_item, {\n                      key: item.id,\n                      value: item.id\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\", \"onTabClick\", \"onRefresh\", \"onClose\", \"onCloseOther\"]), [[_vShow, $setup.isView]]), !$setup.isView && $setup.tabData.length > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode(_component_el_breadcrumb, {\n                \"separator-icon\": $setup.ArrowRight\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tabData, function (item, index) {\n                    return _openBlock(), _createBlock(_component_el_breadcrumb_item, {\n                      key: `key-${item.id}`,\n                      onClick: function onClick($event) {\n                        return $setup.handleBreadcrumb(item, index);\n                      }\n                    }, {\n                      default: _withCtx(function () {\n                        return [_createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)];\n                      }),\n                      _: 2 /* DYNAMIC */\n                    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"separator-icon\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_router_view, null, {\n                default: _withCtx(function (_ref) {\n                  var Component = _ref.Component;\n                  return [(_openBlock(), _createBlock(_KeepAlive, {\n                    include: $setup.keepAliveRoute\n                  }, [$setup.isMain && $setup.isRefresh ? (_openBlock(), _createBlock(_resolveDynamicComponent(Component), {\n                    key: _ctx.$route.fullPath\n                  })) : _createCommentVNode(\"v-if\", true)], 1032 /* PROPS, DYNAMIC_SLOTS */, [\"include\"]))];\n                }),\n                _: 1 /* STABLE */\n              }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.MicroApp, function (item) {\n                return _withDirectives((_openBlock(), _createBlock($setup[\"SubAppViewport\"], {\n                  key: item,\n                  name: item\n                }, null, 8 /* PROPS */, [\"name\"])), [[_vShow, !$setup.isMain && $setup.isMicroApp === item]]);\n              }), 128 /* KEYED_FRAGMENT */))])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"class\"]), $setup.whetherAiChat ? (_openBlock(), _createBlock(_component_el_aside, {\n            key: 0,\n            class: \"LayoutViewFloatingWindow\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_Transition, {\n                name: \"width-animation\"\n              }, {\n                default: _withCtx(function () {\n                  return [$setup.AiChatViewType ? _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n                    key: 0,\n                    class: \"LayoutViewFloatingWindowBody\",\n                    style: _normalizeStyle({\n                      '--ai-chat-target-width': $setup.AiChatTargetWidth\n                    })\n                  }, [_createVNode($setup[\"GlobalAiChat\"], {\n                    modelValue: $setup.AiChatWindowShow,\n                    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n                      return $setup.AiChatWindowShow = $event;\n                    })\n                  }, null, 8 /* PROPS */, [\"modelValue\"])], 4 /* STYLE */)), [[_vShow, $setup.AiChatWindowShow]]) : _createCommentVNode(\"v-if\", true)];\n                }),\n                _: 1 /* STABLE */\n              })];\n            }),\n            _: 1 /* STABLE */\n          })) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.helpShow,\n        \"onUpdate:modelValue\": _cache[8] || (_cache[8] = function ($event) {\n          return $setup.helpShow = $event;\n        }),\n        name: \"帮助文档\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"HelpDocument\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n        modelValue: $setup.editPassWordShow,\n        \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n          return $setup.editPassWordShow = $event;\n        }),\n        name: \"修改密码\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"EditPassWord\"], {\n            type: $setup.verifyEditPassWord,\n            onCallback: $setup.editPassWordCallback\n          }, null, 8 /* PROPS */, [\"type\", \"onCallback\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), $setup.verifyEditPassWordShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode($setup[\"EditPassWord\"], {\n        type: $setup.verifyEditPassWord,\n        onCallback: $setup.editPassWordCallback\n      }, null, 8 /* PROPS */, [\"type\", \"onCallback\"])])) : _createCommentVNode(\"v-if\", true), $setup.isRegionSelectShow ? (_openBlock(), _createBlock($setup[\"GlobalRegionSelect\"], {\n        key: 1,\n        onCallback: $setup.regionSelect\n      }, null, 8 /* PROPS */, [\"onCallback\"])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode($setup[\"qusetionAnswering\"]), $setup.rongCloudToken ? (_openBlock(), _createBlock($setup[\"GlobalChatFloating\"], {\n    key: 0\n  })) : _createCommentVNode(\"v-if\", true), $setup.whetherAiChat ? (_openBlock(), _createBlock($setup[\"GlobalFloatingWindow\"], {\n    key: 1,\n    modelValue: $setup.AiChatWindowShow,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = function ($event) {\n      return $setup.AiChatWindowShow = $event;\n    }),\n    disabled: $setup.AiChatViewType\n  }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])) : _createCommentVNode(\"v-if\", true), $setup.whetherAiChat ? (_openBlock(), _createBlock($setup[\"GlobalAiControls\"], {\n    key: 2\n  })) : _createCommentVNode(\"v-if\", true), $setup.isMain && $setup.suggestPopShow ? (_openBlock(), _createBlock($setup[\"suggestPop\"], {\n    key: 3\n  })) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "ref", "key", "_createElementBlock", "_Fragment", "_createVNode", "_component_el_container", "default", "_withCtx", "_component_el_header", "_createElementVNode", "onClick", "_cache", "$setup", "WorkBenchReturn", "apply", "arguments", "style", "_normalizeStyle", "layoutNameBg", "_hoisted_1", "_component_el_image", "src", "systemLogo", "fit", "_hoisted_2", "_toDisplayString", "systemNameAreaPrefix", "regionName", "systemName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left", "layoutChildNameBg", "_createTextVNode", "childData", "name", "_createCommentVNode", "_normalizeClass", "isLayoutViewHeaderMenu", "width", "layoutChildBg", "_component_el_tabs", "modelValue", "tabMenu", "$event", "onTabChange", "handleClick", "_renderList", "tabMenuData", "item", "_createBlock", "_component_el_tab_pane", "id", "label", "_hoisted_3", "icon", "_", "_hoisted_4", "WorkBenchObj", "_hoisted_5", "_component_el_dropdown", "onCommand", "handleOrganizationChange", "trigger", "dropdown", "_component_el_dropdown_menu", "organizationList", "_$setup$currentOrgani", "_$setup$currentOrgani2", "_component_el_dropdown_item", "orgId", "command", "currentOrganization", "orgName", "_$setup$currentOrgani3", "_hoisted_6", "_hoisted_7", "_component_el_icon", "_component_xyl_region", "regionId", "data", "area", "onSelect", "regionSelect", "props", "children", "_component_el_tooltip", "placement", "effect", "offset", "disabled", "role", "length", "content", "index", "_hoisted_8", "user", "image", "_hoisted_9", "userName", "innerHTML", "refreshIcon", "handleCommand", "title", "_hoisted_10", "_component_el_aside", "_component_xyl_menu", "menuId", "menuData", "menuClick", "<PERSON><PERSON><PERSON><PERSON>", "_component_el_main", "LayoutViewMainView", "LayoutViewMainBreadcrumb", "tabData", "_component_xyl_tab", "onTabClick", "tabClick", "onRefresh", "handleRefresh", "onClose", "handleClose", "onCloseOther", "handleCloseOther", "_component_xyl_tab_item", "value", "_hoisted_11", "_component_el_breadcrumb", "ArrowRight", "_component_el_breadcrumb_item", "handleBreadcrumb", "_hoisted_12", "_component_router_view", "_ref", "Component", "_KeepAlive", "include", "keepAliveRoute", "is<PERSON><PERSON>", "isRefresh", "_resolveDynamicComponent", "_ctx", "$route", "fullPath", "MicroApp", "isMicroApp", "whetherAiChat", "_Transition", "AiChatViewType", "AiChatTargetWidth", "AiChatWindowShow", "_component_xyl_popup_window", "helpShow", "editPassWordShow", "type", "verifyEditPassWord", "onCallback", "editPassWordCallback", "verifyEditPassWordShow", "_hoisted_13", "isRegionSelectShow", "rongCloudToken", "suggestPopShow"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutViewUnitedFront\\LayoutViewUnitedFront.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"LayoutViewUnitedFront\">\r\n    <el-header class=\"LayoutViewHeader\">\r\n      <div class=\"LayoutViewBox\" ref=\"LayoutViewBox\" @click=\"WorkBenchReturn\"\r\n        :style=\"`background: url('${layoutNameBg}') no-repeat;background-size: auto 100%;background-position: right;`\">\r\n        <div class=\"LayoutViewLogo\">\r\n          <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n        </div>\r\n        <div class=\"LayoutViewName\">{{ systemNameAreaPrefix === 'true' ? regionName : '' }}{{ systemName }}</div>\r\n      </div>\r\n      <div class=\"LayoutViewChildView\" :style=\"`left:${left - 88\r\n        }px;background: url('${layoutChildNameBg}') no-repeat;background-size: auto 100%;background-position: right;`\"\r\n        v-if=\"isChildView\">\r\n        <div class=\"LayoutViewChildViewIcon\"></div>\r\n        {{ childData.name }}\r\n      </div>\r\n      <div class=\"LayoutViewHeaderMenu\" :class=\"{ isLayoutViewHeaderMenu: isChildView }\" :style=\"`${width}padding-left:${left}px;background: url('${isChildView ? layoutChildBg : ''\r\n        }') no-repeat;background-size: 287px 65px;background-position: right bottom;`\">\r\n        <el-tabs v-model=\"tabMenu\" @tab-change=\"handleClick\">\r\n          <el-tab-pane v-for=\"item in tabMenuData\" :key=\"item.id\" :name=\"item.id\">\r\n            <template #label>\r\n              <div class=\"LayoutViewHeaderMenuItem forbidSelect\">\r\n                <el-image :src=\"item.icon\" fit=\"cover\" />\r\n                <span>{{ item.name }}</span>\r\n              </div>\r\n            </template>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"LayoutViewInfo\" ref=\"LayoutViewInfo\">\r\n        <div class=\"WorkBenchReturn\" @click=\"WorkBenchReturn\" v-if=\"isChildView\">\r\n          <span></span>\r\n          {{ WorkBenchObj.name }}\r\n        </div>\r\n        <div class=\"LayoutViewOrganization\" v-show=\"!isChildView\">\r\n          <el-dropdown @command=\"handleOrganizationChange\" trigger=\"click\">\r\n            <div class=\"LayoutViewOrganizationTrigger\">\r\n              <span class=\"LayoutViewOrganizationText\">{{ currentOrganization?.orgName || '请选择组织' }}</span>\r\n              <el-icon class=\"LayoutViewOrganizationIcon\">\r\n                <ArrowDown />\r\n              </el-icon>\r\n            </div>\r\n            <template #dropdown>\r\n              <el-dropdown-menu>\r\n                <el-dropdown-item v-for=\"item in organizationList\" :key=\"item.id || item.orgId\" :command=\"item\"\r\n                  :class=\"{ 'is-active': (currentOrganization?.id || currentOrganization?.orgId) === (item.id || item.orgId) }\">\r\n                  {{ item.orgName }}\r\n                </el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </template>\r\n          </el-dropdown>\r\n        </div>\r\n        <xyl-region v-model=\"regionId\" :data=\"area\" @select=\"regionSelect\" v-show=\"!isChildView\"\r\n          :props=\"{ label: 'name', children: 'children' }\"></xyl-region>\r\n        <el-tooltip placement=\"top\" effect=\"light\" :offset=\"6\" :disabled=\"!role.length\">\r\n          <template #content>\r\n            <div class=\"LayoutViewRoleItem\" v-for=\"(item, index) in role\" :key=\"index\">{{ item }}</div>\r\n          </template>\r\n          <div class=\"LayoutViewUser\">\r\n            <el-image :src=\"user.image\" fit=\"cover\" />\r\n            <span class=\"forbidSelect\">{{ user.userName }}</span>\r\n          </div>\r\n        </el-tooltip>\r\n        <LayoutPersonalDoList></LayoutPersonalDoList>\r\n        <LayoutBoxMessage></LayoutBoxMessage>\r\n        <div class=\"LayoutViewRefresh\" v-html=\"refreshIcon\" @click=\"handleCommand('refresh')\" title=\"重新加载平台\"></div>\r\n        <el-dropdown @command=\"handleCommand\">\r\n          <div class=\"LayoutOperation\"></div>\r\n          <template #dropdown>\r\n            <el-dropdown-menu>\r\n              <el-dropdown-item command=\"task\">系统任务管理器</el-dropdown-item>\r\n              <!-- <el-dropdown-item command=\"refresh\">重新加载平台</el-dropdown-item> -->\r\n              <!-- <el-dropdown-item command=\"locale\">简繁切换</el-dropdown-item>\r\n              <el-dropdown-item command=\"help\">帮助文档</el-dropdown-item> -->\r\n              <el-dropdown-item command=\"edit_password\">修改密码</el-dropdown-item>\r\n              <el-dropdown-item command=\"exit\">安全退出</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </template>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"LayoutViewContainer\">\r\n      <el-aside class=\"LayoutViewAside\" v-show=\"isView\">\r\n        <xyl-menu v-model=\"menuId\" :menuData=\"menuData\" @select=\"menuClick\"></xyl-menu>\r\n      </el-aside>\r\n      <el-main class=\"LayoutViewMain\"\r\n        :class=\"{ LayoutViewMainView: !isView, LayoutViewMainBreadcrumb: !isView && tabData.length > 1 }\">\r\n        <xyl-tab v-model=\"menuId\" @tab-click=\"tabClick\" @refresh=\"handleRefresh\" @close=\"handleClose\"\r\n          @closeOther=\"handleCloseOther\" v-show=\"isView\">\r\n          <xyl-tab-item v-for=\"item in tabData\" :key=\"item.id\" :value=\"item.id\">{{ item.name }}</xyl-tab-item>\r\n        </xyl-tab>\r\n        <div class=\"LayoutViewBreadcrumb\" v-if=\"!isView && tabData.length > 1\">\r\n          <el-breadcrumb :separator-icon=\"ArrowRight\">\r\n            <el-breadcrumb-item v-for=\"(item, index) in tabData\" :key=\"`key-${item.id}`\"\r\n              @click=\"handleBreadcrumb(item, index)\">\r\n              {{ item.name }}\r\n            </el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"LayoutViewBody\">\r\n          <router-view v-slot=\"{ Component }\">\r\n            <keep-alive :include=\"keepAliveRoute\">\r\n              <component v-if=\"isMain && isRefresh\" :key=\"$route.fullPath\" :is=\"Component\"></component>\r\n            </keep-alive>\r\n          </router-view>\r\n          <SubAppViewport v-for=\"item in MicroApp\" :key=\"item\" v-show=\"!isMain && isMicroApp === item\" :name=\"item\">\r\n          </SubAppViewport>\r\n        </div>\r\n      </el-main>\r\n      <el-aside class=\"LayoutViewFloatingWindow\" v-if=\"whetherAiChat\">\r\n        <transition name=\"width-animation\">\r\n          <div class=\"LayoutViewFloatingWindowBody\" :style=\"{ '--ai-chat-target-width': AiChatTargetWidth }\"\r\n            v-if=\"AiChatViewType\" v-show=\"AiChatWindowShow\">\r\n            <GlobalAiChat v-model=\"AiChatWindowShow\"></GlobalAiChat>\r\n          </div>\r\n        </transition>\r\n      </el-aside>\r\n    </el-container>\r\n    <xyl-popup-window v-model=\"helpShow\" name=\"帮助文档\">\r\n      <HelpDocument></HelpDocument>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"editPassWordShow\" name=\"修改密码\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </xyl-popup-window>\r\n    <div class=\"ConstraintEditPassWord\" v-if=\"verifyEditPassWordShow\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </div>\r\n    <GlobalRegionSelect v-if=\"isRegionSelectShow\" @callback=\"regionSelect\"></GlobalRegionSelect>\r\n  </el-container>\r\n  <qusetionAnswering></qusetionAnswering>\r\n  <GlobalChatFloating v-if=\"rongCloudToken\"></GlobalChatFloating>\r\n  <GlobalFloatingWindow v-model=\"AiChatWindowShow\" :disabled=\"AiChatViewType\" v-if=\"whetherAiChat\" />\r\n  <GlobalAiControls v-if=\"whetherAiChat\" />\r\n  <suggestPop v-if=\"isMain && suggestPopShow\" />\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutViewUnitedFront' }\r\n</script>\r\n<script setup>\r\nimport { defineAsyncComponent } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { qiankun, LayoutViewUnitedFront, ChatMethod, AiChatMethod, refreshIcon } from './LayoutViewUnitedFront.js'\r\nimport {\r\n  systemLogo,\r\n  systemName,\r\n  whetherAiChat,\r\n  systemNameAreaPrefix,\r\n  layoutNameBg,\r\n  layoutChildBg,\r\n  layoutChildNameBg\r\n} from 'common/js/system_var.js'\r\nimport { ArrowRight, ArrowDown } from '@element-plus/icons-vue'\r\nconst HelpDocument = defineAsyncComponent(() => import('../LayoutContainer/components/HelpDocument'))\r\nconst EditPassWord = defineAsyncComponent(() => import('../LayoutContainer/components/EditPassWord'))\r\nconst LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))\r\nconst LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))\r\nconst GlobalRegionSelect = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalRegionSelect'))\r\nconst GlobalChatFloating = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalChatFloating'))\r\nconst GlobalFloatingWindow = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalFloatingWindow'))\r\nconst GlobalAiControls = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalAiControls'))\r\nconst GlobalAiChat = defineAsyncComponent(() => import('../GlobalAiChat/GlobalAiChat'))\r\nconst suggestPop = defineAsyncComponent(() => import('./component/suggestPop'))\r\nconst qusetionAnswering = defineAsyncComponent(() => import('./component/question-answering.vue'))\r\nconst SubAppViewport = {\r\n  name: 'SubAppViewport',\r\n  props: ['name'],\r\n  template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\r\n}\r\nconst { isMain } = qiankun(useRoute())\r\nconst {\r\n  user,\r\n  area,\r\n  role,\r\n  left,\r\n  width,\r\n  LayoutViewBox,\r\n  LayoutViewInfo,\r\n  helpShow,\r\n  handleCommand,\r\n  editPassWordShow,\r\n  verifyEditPassWord,\r\n  verifyEditPassWordShow,\r\n  editPassWordCallback,\r\n  regionId,\r\n  regionName,\r\n  regionSelect,\r\n  isRegionSelectShow,\r\n  isOrganizationSelectShow,\r\n  isView,\r\n  isChildView,\r\n  tabMenu,\r\n  tabMenuData,\r\n  handleClick,\r\n  menuId,\r\n  menuData,\r\n  menuClick,\r\n  handleBreadcrumb,\r\n  WorkBenchObj,\r\n  childData,\r\n  WorkBenchReturn,\r\n  isRefresh,\r\n  keepAliveRoute,\r\n  tabData,\r\n  tabClick,\r\n  handleRefresh,\r\n  handleClose,\r\n  handleCloseOther,\r\n  isMicroApp,\r\n  MicroApp,\r\n  suggestPopShow,\r\n  currentOrganization,\r\n  organizationList,\r\n  handleOrganizationChange\r\n} = LayoutViewUnitedFront(useRoute(), useRouter())\r\n\r\nconst { rongCloudToken } = ChatMethod()\r\nconst { AiChatTargetWidth, AiChatViewType, AiChatWindowShow } = AiChatMethod()\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutViewUnitedFront {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .LayoutViewHeader {\r\n    height: 62px;\r\n    background-color: var(--zy-el-color-primary);\r\n    position: relative;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .LayoutViewBox {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      z-index: 9;\r\n      height: 77px;\r\n      display: flex;\r\n      align-items: center;\r\n      // pointer-events: none;\r\n      padding: 0 88px 15px 20px;\r\n      cursor: pointer;\r\n\r\n      .LayoutViewLogo {\r\n        width: 52px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .LayoutViewName {\r\n        font-size: var(--zy-system-font-size);\r\n        line-height: var(--zy-line-height);\r\n        font-weight: bold;\r\n        color: var(--zy-el-color-primary);\r\n        padding-left: 12px;\r\n      }\r\n    }\r\n\r\n    .LayoutViewChildView {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      height: 76px;\r\n      display: flex;\r\n      padding: 0 88px;\r\n      padding-bottom: 14px;\r\n      z-index: 3;\r\n      pointer-events: none;\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: var(--zy-navigation-font-size);\r\n      font-weight: bold;\r\n      color: #fff;\r\n\r\n      .LayoutViewChildViewIcon {\r\n        width: 52px;\r\n        height: calc(var(--zy-navigation-font-size) * var(--zy-line-height));\r\n        background: url('../img/layout_view_child_view_icon.png') no-repeat;\r\n        background-size: 21px 11px;\r\n        background-position: center center;\r\n      }\r\n    }\r\n\r\n    .LayoutViewHeaderMenu {\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      overflow: hidden;\r\n\r\n      .zy-el-tabs {\r\n        width: 100%;\r\n\r\n        .zy-el-tabs__header {\r\n          margin: 0;\r\n\r\n          .zy-el-tabs__nav-next,\r\n          .zy-el-tabs__nav-prev {\r\n            color: #fff;\r\n            font-size: var(--zy-navigation-font-size);\r\n            line-height: 48px;\r\n\r\n            .zy-el-icon {\r\n              color: #fff;\r\n            }\r\n          }\r\n\r\n          .zy-el-tabs__nav-wrap {\r\n            &::after {\r\n              background-color: transparent;\r\n            }\r\n\r\n            .zy-el-tabs__item {\r\n              height: 43px;\r\n              line-height: 43px;\r\n              font-size: var(--zy-navigation-font-size);\r\n\r\n              .LayoutViewHeaderMenuItem {\r\n                display: flex;\r\n                align-items: center;\r\n                vertical-align: middle;\r\n\r\n                .zy-el-image {\r\n                  width: 20px;\r\n                  height: 20px;\r\n                  margin-right: 6px;\r\n                }\r\n\r\n                &>span {\r\n                  color: #fff;\r\n                }\r\n              }\r\n            }\r\n\r\n            .is-active {\r\n              font-weight: bold;\r\n            }\r\n\r\n            .zy-el-tabs__active-bar {\r\n              height: 3px;\r\n              background-color: #fff;\r\n            }\r\n          }\r\n        }\r\n\r\n        .zy-el-tabs__content {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n\r\n    .isLayoutViewHeaderMenu {\r\n      .zy-el-tabs {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .LayoutViewInfo {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .WorkBenchReturn {\r\n        height: 36px;\r\n        padding: 0 12px;\r\n        border: 1px solid #ffffff;\r\n        border-radius: var(--el-border-radius-base);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n        margin-right: 22px;\r\n\r\n        span {\r\n          width: 22px;\r\n          height: 22px;\r\n          display: inline-block;\r\n          background: url('../img/work_bench_return.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          margin-right: 6px;\r\n        }\r\n      }\r\n\r\n      .LayoutViewUser {\r\n        display: flex;\r\n        align-items: center;\r\n        cursor: pointer;\r\n\r\n        .zy-el-image {\r\n          height: 38px;\r\n          width: 38px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        span {\r\n          color: #fff;\r\n          margin-left: 8px;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n      }\r\n\r\n      .zy-el-badge {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      .zy-el-dropdown {\r\n        margin-left: 18px;\r\n      }\r\n\r\n      .LayoutViewRefresh {\r\n        width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-left: 18px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .LayoutPersonalDoList {\r\n        width: 26px;\r\n        height: 26px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_personal_do_list.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutBoxMessage {\r\n        width: 26px;\r\n        height: 26px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_box_message.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutOperation {\r\n        width: 26px;\r\n        height: 26px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_operation.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutViewOrganization {\r\n        margin-right: 22px;\r\n\r\n        .LayoutViewOrganizationTrigger {\r\n          height: 36px;\r\n          padding: 0 12px;\r\n          border: 1px solid #ffffff;\r\n          border-radius: var(--el-border-radius-base);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          cursor: pointer;\r\n          min-width: 120px;\r\n\r\n          &:hover {\r\n            background-color: rgba(255, 255, 255, 0.1);\r\n          }\r\n\r\n          .LayoutViewOrganizationText {\r\n            color: #ffffff;\r\n            font-size: var(--zy-name-font-size);\r\n            line-height: var(--zy-line-height);\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            max-width: 100px;\r\n          }\r\n\r\n          .LayoutViewOrganizationIcon {\r\n            color: #ffffff;\r\n            font-size: 12px;\r\n            margin-left: 8px;\r\n            transition: transform 0.3s;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LayoutViewContainer {\r\n    width: 100%;\r\n    height: calc(100% - 62px);\r\n    background: var(--zy-el-color-info-light-9);\r\n\r\n    .LayoutViewFloatingWindow {\r\n      width: auto;\r\n      height: 100%;\r\n\r\n      .LayoutViewFloatingWindowBody {\r\n        width: var(--ai-chat-target-width);\r\n        height: 100%;\r\n        background: #fff;\r\n        box-sizing: border-box;\r\n        transform-origin: left center;\r\n        border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      /* 进入动画 */\r\n      .width-animation-enter-active {\r\n        animation: widen 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 离开动画 */\r\n      .width-animation-leave-active {\r\n        animation: narrow 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 定义进入动画 */\r\n      @keyframes widen {\r\n        from {\r\n          width: 0;\r\n        }\r\n\r\n        to {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n      }\r\n\r\n      /* 定义离开动画 */\r\n      @keyframes narrow {\r\n        from {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n\r\n        to {\r\n          width: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewAside {\r\n      width: auto;\r\n    }\r\n\r\n    .LayoutViewMain {\r\n      height: 100%;\r\n      padding: var(--zy-distance-three) var(--zy-distance-three) 0 0;\r\n\r\n      .LayoutViewBreadcrumb {\r\n        width: 100%;\r\n        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px);\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: #fff;\r\n        padding: 0 var(--zy-distance-two);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .zy-el-breadcrumb {\r\n          font-size: var(--zy-name-font-size);\r\n\r\n          .zy-el-breadcrumb__inner {\r\n            cursor: pointer;\r\n            font-weight: bold;\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n\r\n          .zy-el-breadcrumb__item {\r\n            &:last-child {\r\n              .zy-el-breadcrumb__inner {\r\n                cursor: text;\r\n                font-weight: normal;\r\n                color: var(--zy-el-text-color-regular);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .LayoutViewBody {\r\n        width: 100%;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n        background-color: #fff;\r\n\r\n        .subApp-viewport {\r\n          width: 100%;\r\n          height: 100%;\r\n\r\n          >div {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewMainView {\r\n      width: 100%;\r\n      padding: 0;\r\n      background: #f8f8f8;\r\n\r\n      .LayoutViewBody {\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .LayoutViewMainBreadcrumb {\r\n      width: 100%;\r\n\r\n      .LayoutViewBody {\r\n        position: relative;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n      }\r\n    }\r\n  }\r\n\r\n  .ConstraintEditPassWord {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 999;\r\n    background-color: #fff;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .EditPassWord {\r\n      box-shadow: 0px 2px 40px rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.LayoutViewRoleItem {\r\n  font-size: var(--zy-text-font-size);\r\n  line-height: var(--zy-line-height);\r\n}\r\n</style>\r\n"], "mappings": ";;EAKaA,KAAK,EAAC;AAAgB;;EAGtBA,KAAK,EAAC;AAAgB;;EAahBA,KAAK,EAAC;AAAuC;;EAQrDA,KAAK,EAAC,gBAAgB;EAACC,GAAG,EAAC;;;EAKzBD,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAA+B;;EAClCA,KAAK,EAAC;AAA4B;;EAqBvCA,KAAK,EAAC;AAAgB;;EAEnBA,KAAK,EAAC;AAAc;kBA5DtC;;EAAAE,GAAA;EA2FaF,KAAK,EAAC;;;EAQNA,KAAK,EAAC;AAAgB;;EAnGnCE,GAAA;EA4HSF,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;;;uBA5HfG,mBAAA,CAAAC,SAAA,SACEC,YAAA,CA+HeC,uBAAA;IA/HDN,KAAK,EAAC;EAAuB;IAD7CO,OAAA,EAAAC,QAAA,CAEI;MAAA,OA8EY,CA9EZH,YAAA,CA8EYI,oBAAA;QA9EDT,KAAK,EAAC;MAAkB;QAFvCO,OAAA,EAAAC,QAAA,CAGM;UAAA,OAMM,CANNE,mBAAA,CAMM;YANDV,KAAK,EAAC,eAAe;YAACC,GAAG,EAAC,eAAe;YAAEU,OAAK,EAAAC,MAAA,QAAAA,MAAA;cAAA,OAAEC,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAF,MAAA,EAAAG,SAAA,CAAe;YAAA;YACnEC,KAAK,EAJdC,eAAA,qBAIoCL,MAAA,CAAAM,YAAY;cACxCT,mBAAA,CAEM,OAFNU,UAEM,GADJf,YAAA,CAA0CgB,mBAAA;YAA/BC,GAAG,EAAET,MAAA,CAAAU,UAAU;YAAEC,GAAG,EAAC;8CAElCd,mBAAA,CAAyG,OAAzGe,UAAyG,EAAAC,gBAAA,CAA1Eb,MAAA,CAAAc,oBAAoB,cAAcd,MAAA,CAAAe,UAAU,SAAAF,gBAAA,CAAWb,MAAA,CAAAgB,UAAU,iB,kBAI1FhB,MAAA,CAAAiB,WAAW,I,cAFnB3B,mBAAA,CAKM;YAfZD,GAAA;YAUWF,KAAK,EAAC,qBAAqB;YAAEiB,KAAK,EAV7CC,eAAA,SAUuDL,MAAA,CAAAkB,IAAI,4BAAsClB,MAAA,CAAAmB,iBAAiB;0CAG1GtB,mBAAA,CAA2C;YAAtCV,KAAK,EAAC;UAAyB,6BAb5CiC,gBAAA,CAamD,GAC3C,GAAAP,gBAAA,CAAGb,MAAA,CAAAqB,SAAS,CAACC,IAAI,iB,oBAdzBC,mBAAA,gBAgBM1B,mBAAA,CAYM;YAZDV,KAAK,EAhBhBqC,eAAA,EAgBiB,sBAAsB;cAAAC,sBAAA,EAAmCzB,MAAA,CAAAiB;YAAW;YAAKb,KAAK,EAhB/FC,eAAA,IAgBoGL,MAAA,CAAA0B,KAAK,gBAAgB1B,MAAA,CAAAkB,IAAI,uBAAuBlB,MAAA,CAAAiB,WAAW,GAAGjB,MAAA,CAAA2B,aAAa,kF;cAEvKnC,YAAA,CASUoC,kBAAA;YA3BlBC,UAAA,EAkB0B7B,MAAA,CAAA8B,OAAO;YAlBjC,uBAAA/B,MAAA,QAAAA,MAAA,gBAAAgC,MAAA;cAAA,OAkB0B/B,MAAA,CAAA8B,OAAO,GAAAC,MAAA;YAAA;YAAGC,WAAU,EAAEhC,MAAA,CAAAiC;;YAlBhDvC,OAAA,EAAAC,QAAA,CAmBuB;cAAA,OAA2B,E,kBAAxCL,mBAAA,CAOcC,SAAA,QA1BxB2C,WAAA,CAmBsClC,MAAA,CAAAmC,WAAW,EAnBjD,UAmB8BC,IAAI;qCAAxBC,YAAA,CAOcC,sBAAA;kBAP4BjD,GAAG,EAAE+C,IAAI,CAACG,EAAE;kBAAGjB,IAAI,EAAEc,IAAI,CAACG;;kBACvDC,KAAK,EAAA7C,QAAA,CACd;oBAAA,OAGM,CAHNE,mBAAA,CAGM,OAHN4C,UAGM,GAFJjD,YAAA,CAAyCgB,mBAAA;sBAA9BC,GAAG,EAAE2B,IAAI,CAACM,IAAI;sBAAE/B,GAAG,EAAC;sDAC/Bd,mBAAA,CAA4B,cAAAgB,gBAAA,CAAnBuB,IAAI,CAACd,IAAI,iB;;kBAvBlCqB,CAAA;;;;YAAAA,CAAA;oFA6BM9C,mBAAA,CAkDM,OAlDN+C,UAkDM,GAjDwD5C,MAAA,CAAAiB,WAAW,I,cAAvE3B,mBAAA,CAGM;YAjCdD,GAAA;YA8BaF,KAAK,EAAC,iBAAiB;YAAEW,OAAK,EAAAC,MAAA,QAAAA,MAAA;cAAA,OAAEC,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAF,MAAA,EAAAG,SAAA,CAAe;YAAA;0CAClDN,mBAAA,CAAa,wCA/BvBuB,gBAAA,CA+BuB,GACb,GAAAP,gBAAA,CAAGb,MAAA,CAAA6C,YAAY,CAACvB,IAAI,iB,KAhC9BC,mBAAA,gB,gBAkCQ1B,mBAAA,CAiBM,OAjBNiD,UAiBM,GAhBJtD,YAAA,CAecuD,sBAAA;YAfAC,SAAO,EAAEhD,MAAA,CAAAiD,wBAAwB;YAAEC,OAAO,EAAC;;YAO5CC,QAAQ,EAAAxD,QAAA,CACjB;cAAA,OAKmB,CALnBH,YAAA,CAKmB4D,2BAAA;gBAhDjC1D,OAAA,EAAAC,QAAA,CA4CkC;kBAAA,OAAgC,E,kBAAlDL,mBAAA,CAGmBC,SAAA,QA/CnC2C,WAAA,CA4CiDlC,MAAA,CAAAqD,gBAAgB,EA5CjE,UA4CyCjB,IAAI;oBAAA,IAAAkB,qBAAA,EAAAC,sBAAA;yCAA7BlB,YAAA,CAGmBmB,2BAAA;sBAHiCnE,GAAG,EAAE+C,IAAI,CAACG,EAAE,IAAIH,IAAI,CAACqB,KAAK;sBAAGC,OAAO,EAAEtB,IAAI;sBAC3FjD,KAAK,EA7CxBqC,eAAA;wBAAA,cA6C0C,EAAA8B,qBAAA,GAAAtD,MAAA,CAAA2D,mBAAmB,cAAAL,qBAAA,uBAAnBA,qBAAA,CAAqBf,EAAE,OAAAgB,sBAAA,GAAIvD,MAAA,CAAA2D,mBAAmB,cAAAJ,sBAAA,uBAAnBA,sBAAA,CAAqBE,KAAK,QAAOrB,IAAI,CAACG,EAAE,IAAIH,IAAI,CAACqB,KAAK;sBAAA;;sBA7C3H/D,OAAA,EAAAC,QAAA,CA8CkB;wBAAA,OAAkB,CA9CpCyB,gBAAA,CAAAP,gBAAA,CA8CqBuB,IAAI,CAACwB,OAAO,iB;;sBA9CjCjB,CAAA;;;;gBAAAA,CAAA;;;YAAAjD,OAAA,EAAAC,QAAA,CAoCY;cAAA,IAAAkE,sBAAA;cAAA,OAKM,CALNhE,mBAAA,CAKM,OALNiE,UAKM,GAJJjE,mBAAA,CAA6F,QAA7FkE,UAA6F,EAAAlD,gBAAA,CAAjD,EAAAgD,sBAAA,GAAA7D,MAAA,CAAA2D,mBAAmB,cAAAE,sBAAA,uBAAnBA,sBAAA,CAAqBD,OAAO,8BACxEpE,YAAA,CAEUwE,kBAAA;gBAFD7E,KAAK,EAAC;cAA4B;gBAtCzDO,OAAA,EAAAC,QAAA,CAuCgB;kBAAA,OAAa,CAAbH,YAAA,CAAaQ,MAAA,e;;gBAvC7B2C,CAAA;;;YAAAA,CAAA;+EAkCqD3C,MAAA,CAAAiB,WAAW,E,mBAkBxDzB,YAAA,CACgEyE,qBAAA;YArDxEpC,UAAA,EAoD6B7B,MAAA,CAAAkE,QAAQ;YApDrC,uBAAAnE,MAAA,QAAAA,MAAA,gBAAAgC,MAAA;cAAA,OAoD6B/B,MAAA,CAAAkE,QAAQ,GAAAnC,MAAA;YAAA;YAAGoC,IAAI,EAAEnE,MAAA,CAAAoE,IAAI;YAAGC,QAAM,EAAErE,MAAA,CAAAsE,YAAY;YAC9DC,KAAK,EAAE;cAAA/B,KAAA;cAAAgC,QAAA;YAAA;kFADkExE,MAAA,CAAAiB,WAAW,E,GAEvFzB,YAAA,CAQaiF,qBAAA;YARDC,SAAS,EAAC,KAAK;YAACC,MAAM,EAAC,OAAO;YAAEC,MAAM,EAAE,CAAC;YAAGC,QAAQ,GAAG7E,MAAA,CAAA8E,IAAI,CAACC;;YAC3DC,OAAO,EAAArF,QAAA,CACgB;cAAA,OAA6B,E,kBAA7DL,mBAAA,CAA2FC,SAAA,QAxDvG2C,WAAA,CAwDoElC,MAAA,CAAA8E,IAAI,EAxDxE,UAwDoD1C,IAAI,EAAE6C,KAAK;qCAAnD3F,mBAAA,CAA2F;kBAAtFH,KAAK,EAAC,oBAAoB;kBAAgCE,GAAG,EAAE4F;oCAAU7C,IAAI;;;YAxD9F1C,OAAA,EAAAC,QAAA,CA0DU;cAAA,OAGM,CAHNE,mBAAA,CAGM,OAHNqF,UAGM,GAFJ1F,YAAA,CAA0CgB,mBAAA;gBAA/BC,GAAG,EAAET,MAAA,CAAAmF,IAAI,CAACC,KAAK;gBAAEzE,GAAG,EAAC;gDAChCd,mBAAA,CAAqD,QAArDwF,UAAqD,EAAAxE,gBAAA,CAAvBb,MAAA,CAAAmF,IAAI,CAACG,QAAQ,iB;;YA5DvD3C,CAAA;2CA+DQnD,YAAA,CAA6CQ,MAAA,2BAC7CR,YAAA,CAAqCQ,MAAA,uBACrCH,mBAAA,CAA2G;YAAtGV,KAAK,EAAC,mBAAmB;YAACoG,SAAoB,EAAZvF,MAAA,CAAAwF,WAAW;YAAG1F,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAgC,MAAA;cAAA,OAAE/B,MAAA,CAAAyF,aAAa;YAAA;YAAaC,KAAK,EAAC;kCAjEpGC,WAAA,GAkEQnG,YAAA,CAYcuD,sBAAA;YAZAC,SAAO,EAAEhD,MAAA,CAAAyF;UAAa;YAEvBtC,QAAQ,EAAAxD,QAAA,CACjB;cAAA,OAOmB,CAPnBH,YAAA,CAOmB4D,2BAAA;gBA5E/B1D,OAAA,EAAAC,QAAA,CAsEc;kBAAA,OAA2D,CAA3DH,YAAA,CAA2DgE,2BAAA;oBAAzCE,OAAO,EAAC;kBAAM;oBAtE9ChE,OAAA,EAAAC,QAAA,CAsE+C;sBAAA,OAAOI,MAAA,SAAAA,MAAA,QAtEtDqB,gBAAA,CAsE+C,SAAO,E;;oBAtEtDuB,CAAA;sBAuEcpB,mBAAA,qEAAsE,EACtEA,mBAAA,8IAC4D,EAC5D/B,YAAA,CAAiEgE,2BAAA;oBAA/CE,OAAO,EAAC;kBAAe;oBA1EvDhE,OAAA,EAAAC,QAAA,CA0EwD;sBAAA,OAAII,MAAA,SAAAA,MAAA,QA1E5DqB,gBAAA,CA0EwD,MAAI,E;;oBA1E5DuB,CAAA;sBA2EcnD,YAAA,CAAwDgE,2BAAA;oBAAtCE,OAAO,EAAC;kBAAM;oBA3E9ChE,OAAA,EAAAC,QAAA,CA2E+C;sBAAA,OAAII,MAAA,SAAAA,MAAA,QA3EnDqB,gBAAA,CA2E+C,MAAI,E;;oBA3EnDuB,CAAA;;;gBAAAA,CAAA;;;YAAAjD,OAAA,EAAAC,QAAA,CAmEU;cAAA,OAAmC,C,4BAAnCE,mBAAA,CAAmC;gBAA9BV,KAAK,EAAC;cAAiB,4B;;YAnEtCwD,CAAA;;;QAAAA,CAAA;UAiFInD,YAAA,CAoCeC,uBAAA;QApCDN,KAAK,EAAC;MAAqB;QAjF7CO,OAAA,EAAAC,QAAA,CAkFM;UAAA,OAEW,C,gBAFXH,YAAA,CAEWoG,mBAAA;YAFDzG,KAAK,EAAC;UAAiB;YAlFvCO,OAAA,EAAAC,QAAA,CAmFQ;cAAA,OAA+E,CAA/EH,YAAA,CAA+EqG,mBAAA;gBAnFvFhE,UAAA,EAmF2B7B,MAAA,CAAA8F,MAAM;gBAnFjC,uBAAA/F,MAAA,QAAAA,MAAA,gBAAAgC,MAAA;kBAAA,OAmF2B/B,MAAA,CAAA8F,MAAM,GAAA/D,MAAA;gBAAA;gBAAGgE,QAAQ,EAAE/F,MAAA,CAAA+F,QAAQ;gBAAG1B,QAAM,EAAErE,MAAA,CAAAgG;;;YAnFjErD,CAAA;8CAkFgD3C,MAAA,CAAAiG,MAAM,E,GAGhDzG,YAAA,CAuBU0G,kBAAA;YAvBD/G,KAAK,EArFpBqC,eAAA,EAqFqB,gBAAgB;cAAA2E,kBAAA,GACEnG,MAAA,CAAAiG,MAAM;cAAAG,wBAAA,GAA6BpG,MAAA,CAAAiG,MAAM,IAAIjG,MAAA,CAAAqG,OAAO,CAACtB,MAAM;YAAA;;YAtFlGrF,OAAA,EAAAC,QAAA,CAuFQ;cAAA,OAGU,C,gBAHVH,YAAA,CAGU8G,kBAAA;gBA1FlBzE,UAAA,EAuF0B7B,MAAA,CAAA8F,MAAM;gBAvFhC,uBAAA/F,MAAA,QAAAA,MAAA,gBAAAgC,MAAA;kBAAA,OAuF0B/B,MAAA,CAAA8F,MAAM,GAAA/D,MAAA;gBAAA;gBAAGwE,UAAS,EAAEvG,MAAA,CAAAwG,QAAQ;gBAAGC,SAAO,EAAEzG,MAAA,CAAA0G,aAAa;gBAAGC,OAAK,EAAE3G,MAAA,CAAA4G,WAAW;gBACzFC,YAAU,EAAE7G,MAAA,CAAA8G;;gBAxFvBpH,OAAA,EAAAC,QAAA,CAyFwB;kBAAA,OAAuB,E,kBAArCL,mBAAA,CAAoGC,SAAA,QAzF9G2C,WAAA,CAyFuClC,MAAA,CAAAqG,OAAO,EAzF9C,UAyF+BjE,IAAI;yCAAzBC,YAAA,CAAoG0E,uBAAA;sBAA7D1H,GAAG,EAAE+C,IAAI,CAACG,EAAE;sBAAGyE,KAAK,EAAE5E,IAAI,CAACG;;sBAzF5E7C,OAAA,EAAAC,QAAA,CAyFgF;wBAAA,OAAe,CAzF/FyB,gBAAA,CAAAP,gBAAA,CAyFmFuB,IAAI,CAACd,IAAI,iB;;sBAzF5FqB,CAAA;;;;gBAAAA,CAAA;iHAwFiD3C,MAAA,CAAAiG,MAAM,E,IAGNjG,MAAA,CAAAiG,MAAM,IAAIjG,MAAA,CAAAqG,OAAO,CAACtB,MAAM,Q,cAAjEzF,mBAAA,CAOM,OAPN2H,WAOM,GANJzH,YAAA,CAKgB0H,wBAAA;gBALA,gBAAc,EAAElH,MAAA,CAAAmH;cAAU;gBA5FpDzH,OAAA,EAAAC,QAAA,CA6FgC;kBAAA,OAAgC,E,kBAApDL,mBAAA,CAGqBC,SAAA,QAhGjC2C,WAAA,CA6FwDlC,MAAA,CAAAqG,OAAO,EA7F/D,UA6FwCjE,IAAI,EAAE6C,KAAK;yCAAvC5C,YAAA,CAGqB+E,6BAAA;sBAHiC/H,GAAG,SAAS+C,IAAI,CAACG,EAAE;sBACtEzC,OAAK,WAALA,OAAKA,CAAAiC,MAAA;wBAAA,OAAE/B,MAAA,CAAAqH,gBAAgB,CAACjF,IAAI,EAAE6C,KAAK;sBAAA;;sBA9FlDvF,OAAA,EAAAC,QAAA,CA+Fc;wBAAA,OAAe,CA/F7ByB,gBAAA,CAAAP,gBAAA,CA+FiBuB,IAAI,CAACd,IAAI,iB;;sBA/F1BqB,CAAA;;;;gBAAAA,CAAA;yDAAApB,mBAAA,gBAmGQ1B,mBAAA,CAQM,OARNyH,WAQM,GAPJ9H,YAAA,CAIc+H,sBAAA;gBAxGxB7H,OAAA,EAAAC,QAAA,CAqGY,UAAA6H,IAAA;kBAAA,IADqBC,SAAS,GAAAD,IAAA,CAATC,SAAS;kBAAA,S,cAC9BpF,YAAA,CAEaqF,UAAA;oBAFAC,OAAO,EAAE3H,MAAA,CAAA4H;kBAAc,IACjB5H,MAAA,CAAA6H,MAAM,IAAI7H,MAAA,CAAA8H,SAAS,I,cAApCzF,YAAA,CAAyF0F,wBAtGvG,CAsGgFN,SAAS;oBAApCpI,GAAG,EAAE2I,IAAA,CAAAC,MAAM,CAACC;wBAtGjE3G,mBAAA,e;;gBAAAoB,CAAA;qCAyGUrD,mBAAA,CACiBC,SAAA,QA1G3B2C,WAAA,CAyGyClC,MAAA,CAAAmI,QAAQ,EAzGjD,UAyGiC/F,IAAI;sDAA3BC,YAAA,CACiBrC,MAAA;kBADyBX,GAAG,EAAE+C,IAAI;kBAA2Cd,IAAI,EAAEc;+DAAtCpC,MAAA,CAAA6H,MAAM,IAAI7H,MAAA,CAAAoI,UAAU,KAAKhG,IAAI,E;;;YAzGrGO,CAAA;wCA6GuD3C,MAAA,CAAAqI,aAAa,I,cAA9DhG,YAAA,CAOWuD,mBAAA;YApHjBvG,GAAA;YA6GgBF,KAAK,EAAC;;YA7GtBO,OAAA,EAAAC,QAAA,CA8GQ;cAAA,OAKa,CALbH,YAAA,CAKa8I,WAAA;gBALDhH,IAAI,EAAC;cAAiB;gBA9G1C5B,OAAA,EAAAC,QAAA,CA2GkyG;kBAAA,OAAwP,CAKxgHK,MAAA,CAAAuI,cAAc,G,+BADtBjJ,mBAAA,CAGM;oBAlHhBD,GAAA;oBA+GeF,KAAK,EAAC,8BAA8B;oBAAEiB,KAAK,EA/G1DC,eAAA;sBAAA,0BA+GwFL,MAAA,CAAAwI;oBAAiB;sBAE7FhJ,YAAA,CAAwDQ,MAAA;oBAjHpE6B,UAAA,EAiHmC7B,MAAA,CAAAyI,gBAAgB;oBAjHnD,uBAAA1I,MAAA,QAAAA,MAAA,gBAAAgC,MAAA;sBAAA,OAiHmC/B,MAAA,CAAAyI,gBAAgB,GAAA1G,MAAA;oBAAA;uFADT/B,MAAA,CAAAyI,gBAAgB,E,IAhH1DlH,mBAAA,e;;gBAAAoB,CAAA;;;YAAAA,CAAA;gBAAApB,mBAAA,e;;QAAAoB,CAAA;UAsHInD,YAAA,CAEmBkJ,2BAAA;QAxHvB7G,UAAA,EAsH+B7B,MAAA,CAAA2I,QAAQ;QAtHvC,uBAAA5I,MAAA,QAAAA,MAAA,gBAAAgC,MAAA;UAAA,OAsH+B/B,MAAA,CAAA2I,QAAQ,GAAA5G,MAAA;QAAA;QAAET,IAAI,EAAC;;QAtH9C5B,OAAA,EAAAC,QAAA,CAuHM;UAAA,OAA6B,CAA7BH,YAAA,CAA6BQ,MAAA,kB;;QAvHnC2C,CAAA;yCAyHInD,YAAA,CAEmBkJ,2BAAA;QA3HvB7G,UAAA,EAyH+B7B,MAAA,CAAA4I,gBAAgB;QAzH/C,uBAAA7I,MAAA,QAAAA,MAAA,gBAAAgC,MAAA;UAAA,OAyH+B/B,MAAA,CAAA4I,gBAAgB,GAAA7G,MAAA;QAAA;QAAET,IAAI,EAAC;;QAzHtD5B,OAAA,EAAAC,QAAA,CA0HM;UAAA,OAAyF,CAAzFH,YAAA,CAAyFQ,MAAA;YAA1E6I,IAAI,EAAE7I,MAAA,CAAA8I,kBAAkB;YAAGC,UAAQ,EAAE/I,MAAA,CAAAgJ;;;QA1H1DrG,CAAA;yCA4H8C3C,MAAA,CAAAiJ,sBAAsB,I,cAAhE3J,mBAAA,CAEM,OAFN4J,WAEM,GADJ1J,YAAA,CAAyFQ,MAAA;QAA1E6I,IAAI,EAAE7I,MAAA,CAAA8I,kBAAkB;QAAGC,UAAQ,EAAE/I,MAAA,CAAAgJ;2DA7H1DzH,mBAAA,gBA+H8BvB,MAAA,CAAAmJ,kBAAkB,I,cAA5C9G,YAAA,CAA4FrC,MAAA;QA/HhGX,GAAA;QA+HmD0J,UAAQ,EAAE/I,MAAA,CAAAsE;iDA/H7D/C,mBAAA,e;;IAAAoB,CAAA;MAiIEnD,YAAA,CAAuCQ,MAAA,wBACbA,MAAA,CAAAoJ,cAAc,I,cAAxC/G,YAAA,CAA+DrC,MAAA;IAlIjEX,GAAA;EAAA,MAAAkC,mBAAA,gBAmIoFvB,MAAA,CAAAqI,aAAa,I,cAA/FhG,YAAA,CAAmGrC,MAAA;IAnIrGX,GAAA;IAAAwC,UAAA,EAmIiC7B,MAAA,CAAAyI,gBAAgB;IAnIjD,uBAAA1I,MAAA,SAAAA,MAAA,iBAAAgC,MAAA;MAAA,OAmIiC/B,MAAA,CAAAyI,gBAAgB,GAAA1G,MAAA;IAAA;IAAG8C,QAAQ,EAAE7E,MAAA,CAAAuI;yDAnI9DhH,mBAAA,gBAoI0BvB,MAAA,CAAAqI,aAAa,I,cAArChG,YAAA,CAAyCrC,MAAA;IApI3CX,GAAA;EAAA,MAAAkC,mBAAA,gBAqIoBvB,MAAA,CAAA6H,MAAM,IAAI7H,MAAA,CAAAqJ,cAAc,I,cAA1ChH,YAAA,CAA8CrC,MAAA;IArIhDX,GAAA;EAAA,MAAAkC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}