{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"NoticeAnnouncementDetailsBody\"\n};\nvar _hoisted_2 = {\n  class: \"NoticeAnnouncementDetailsPrint\",\n  ref: \"printRef\"\n};\nvar _hoisted_3 = {\n  class: \"NoticeAnnouncementDetailsName\"\n};\nvar _hoisted_4 = {\n  class: \"NoticeAnnouncementDetailsInfo\"\n};\nvar _hoisted_5 = {\n  class: \"NoticeAnnouncementDetailsType no-print\"\n};\nvar _hoisted_6 = {\n  class: \"NoticeAnnouncementDetailsText\"\n};\nvar _hoisted_7 = {\n  class: \"NoticeAnnouncementDetailsText\"\n};\nvar _hoisted_8 = {\n  class: \"NoticeAnnouncementDetailsText no-print\"\n};\nvar _hoisted_9 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_xyl_global_file = _resolveComponent(\"xyl-global-file\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"NoticeAnnouncementDetails\"\n  }, {\n    default: _withCtx(function () {\n      return [_createCommentVNode(\" <div class=\\\"detailsFunctionBox\\\">\\r\\n      <div class=\\\"detailsFunction\\\">\\r\\n        <div class=\\\"detailsPrint\\\"\\r\\n             @click=\\\"handlePrint\\\">打印</div>\\r\\n        <div class=\\\"detailsExportWord\\\"\\r\\n             @click=\\\"handleExportWord\\\">导出Word</div>\\r\\n      </div>\\r\\n    </div> \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.details.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.details.channelName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, \"发布部门：\" + _toDisplayString($setup.details.publishOfficeName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, \"发布时间：\" + _toDisplayString($setup.format($setup.details.publishTime)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, \"阅读量：\" + _toDisplayString($setup.quantity), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n        class: \"NoticeAnnouncementDetailsContent\",\n        innerHTML: $setup.details.content\n      }, null, 8 /* PROPS */, _hoisted_9)], 512 /* NEED_PATCH */), _createVNode(_component_xyl_global_file, {\n        fileData: $setup.details.attachments\n      }, null, 8 /* PROPS */, [\"fileData\"])])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "ref", "_createBlock", "_component_el_scrollbar", "default", "_withCtx", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$setup", "details", "theme", "_hoisted_4", "_hoisted_5", "channelName", "_hoisted_6", "publishOfficeName", "_hoisted_7", "format", "publishTime", "_hoisted_8", "quantity", "innerHTML", "content", "_hoisted_9", "_createVNode", "_component_xyl_global_file", "fileData", "attachments", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\homePage\\components\\NoticeAnnouncementDetails.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"NoticeAnnouncementDetails\">\r\n    <!-- <div class=\"detailsFunctionBox\">\r\n      <div class=\"detailsFunction\">\r\n        <div class=\"detailsPrint\"\r\n             @click=\"handlePrint\">打印</div>\r\n        <div class=\"detailsExportWord\"\r\n             @click=\"handleExportWord\">导出Word</div>\r\n      </div>\r\n    </div> -->\r\n    <div class=\"NoticeAnnouncementDetailsBody\">\r\n      <div class=\"NoticeAnnouncementDetailsPrint\" ref=\"printRef\">\r\n        <div class=\"NoticeAnnouncementDetailsName\">{{ details.theme }}</div>\r\n        <div class=\"NoticeAnnouncementDetailsInfo\">\r\n          <div class=\"NoticeAnnouncementDetailsType no-print\">{{ details.channelName }}</div>\r\n          <div class=\"NoticeAnnouncementDetailsText\">发布部门：{{ details.publishOfficeName }}</div>\r\n          <div class=\"NoticeAnnouncementDetailsText\">发布时间：{{ format(details.publishTime) }}</div>\r\n          <div class=\"NoticeAnnouncementDetailsText no-print\">阅读量：{{ quantity }}</div>\r\n        </div>\r\n        <div class=\"NoticeAnnouncementDetailsContent\" v-html=\"details.content\"></div>\r\n      </div>\r\n      <xyl-global-file :fileData=\"details.attachments\"></xyl-global-file>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'NoticeAnnouncementDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\nimport { exportWordHtmlObj } from 'common/config/MicroGlobal'\r\nimport { Print } from 'common/js/print'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst route = useRoute()\r\nconst printRef = ref()\r\nconst quantity = ref(0)\r\nconst details = ref({})\r\nonMounted(() => {\r\n  clickRecord()\r\n  NoticeAnnouncementInfo()\r\n})\r\nconst NoticeAnnouncementInfo = async () => {\r\n  const res = await api.NoticeAnnouncementInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n}\r\nconst clickRecord = async () => {\r\n  const { code } = await api.clickRecord(`notification/${props.id}`)\r\n  if (code === 200) {\r\n    clickAcquire()\r\n  }\r\n}\r\nconst clickAcquire = async () => {\r\n  const { data } = await api.clickAcquire(`notification/${props.id}`)\r\n  quantity.value = data\r\n}\r\n// const handleExportWord = () => {\r\n//   if (JSON.stringify(details.value) === '{}') return ElMessage({ type: 'warning', message: '请等待通知公告详情加载完成再进行导出！' })\r\n//   exportWordHtmlObj({ code: 'noticeAnnouncementDetails', name: details.value.theme, key: 'content', data: { ...details.value, publishTime: format(details.value.publishTime) } })\r\n// }\r\n// const handlePrint = () => {\r\n//   Print.init(printRef.value)\r\n// }\r\n</script>\r\n<style lang=\"scss\">\r\n.NoticeAnnouncementDetails {\r\n  width: 990px;\r\n  height: 100%;\r\n\r\n  .zy-el-scrollbar__view {\r\n    padding: 0 20px;\r\n  }\r\n\r\n  // .detailsFunctionBox {\r\n  //   width: 990px;\r\n  //   position: absolute;\r\n  //   top: 20px;\r\n  //   left: 50%;\r\n  //   transform: translateX(-50%);\r\n  //   margin-right: 200px;\r\n\r\n  //   .detailsFunction {\r\n  //     position: absolute;\r\n  //     top: 0;\r\n  //     right: 0;\r\n  //     transform: translateX(112%);\r\n\r\n  //     .detailsPrint,\r\n  //     .detailsExportWord {\r\n  //       font-size: var(--zy-text-font-size);\r\n  //       line-height: var(--zy-line-height);\r\n  //       padding-left: 30px;\r\n  //       margin-bottom: 20px;\r\n  //       position: relative;\r\n  //       cursor: pointer;\r\n  //     }\r\n\r\n  //     .detailsPrint {\r\n  //       background: url(\"../../assets/img/details_print.png\") no-repeat;\r\n  //       background-size: 20px 20px;\r\n  //       background-position: left center;\r\n  //     }\r\n\r\n  //     .detailsExportWord {\r\n  //       background: url(\"../../assets/img/details_export_word.png\") no-repeat;\r\n  //       background-size: 20px 20px;\r\n  //       background-position: left center;\r\n  //     }\r\n  //   }\r\n  // }\r\n\r\n  .NoticeAnnouncementDetailsBody {\r\n    max-width: 990px;\r\n    background-color: #fff;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    position: relative;\r\n    padding: 20px 40px;\r\n    margin: 20px auto;\r\n  }\r\n}\r\n\r\n.NoticeAnnouncementDetailsPrint {\r\n  width: 100%;\r\n\r\n  .NoticeAnnouncementDetailsName {\r\n    font-size: var(--zy-title-font-size);\r\n    text-align: center;\r\n    padding: 0 40px;\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .NoticeAnnouncementDetailsInfo {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 20px 0 10px 0;\r\n    border-bottom: 1px solid #eee;\r\n\r\n    .NoticeAnnouncementDetailsType {\r\n      color: var(--zy-el-color-primary);\r\n      border: 1px solid var(--zy-el-color-primary);\r\n      font-size: var(--zy-text-font-size);\r\n      margin-right: 12px;\r\n      padding: 0 6px;\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .NoticeAnnouncementDetailsText {\r\n      margin-right: 58px;\r\n      font-size: var(--zy-text-font-size);\r\n    }\r\n  }\r\n\r\n  .NoticeAnnouncementDetailsContent {\r\n    padding: 20px 0;\r\n    overflow: hidden;\r\n    line-height: var(--zy-line-height);\r\n\r\n    img,\r\n    video {\r\n      max-width: 100%;\r\n      height: auto !important;\r\n    }\r\n\r\n    table {\r\n      border-collapse: collapse;\r\n      border-spacing: 0;\r\n\r\n      tr {\r\n        page-break-inside: avoid;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAUSA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC,gCAAgC;EAACC,GAAG,EAAC;;;EACzCD,KAAK,EAAC;AAA+B;;EACrCA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAwC;;EAC9CA,KAAK,EAAC;AAA+B;;EACrCA,KAAK,EAAC;AAA+B;;EACrCA,KAAK,EAAC;AAAwC;iBAjB7D;;;;uBACEE,YAAA,CAsBeC,uBAAA;IAtBDH,KAAK,EAAC;EAA2B;IADjDI,OAAA,EAAAC,QAAA,CAEI;MAAA,OAOU,CAPVC,mBAAA,2SAOU,EACVC,mBAAA,CAYM,OAZNC,UAYM,GAXJD,mBAAA,CASM,OATNE,UASM,GARJF,mBAAA,CAAoE,OAApEG,UAAoE,EAAAC,gBAAA,CAAtBC,MAAA,CAAAC,OAAO,CAACC,KAAK,kBAC3DP,mBAAA,CAKM,OALNQ,UAKM,GAJJR,mBAAA,CAAmF,OAAnFS,UAAmF,EAAAL,gBAAA,CAA5BC,MAAA,CAAAC,OAAO,CAACI,WAAW,kBAC1EV,mBAAA,CAAqF,OAArFW,UAAqF,EAA1C,OAAK,GAAAP,gBAAA,CAAGC,MAAA,CAAAC,OAAO,CAACM,iBAAiB,kBAC5EZ,mBAAA,CAAuF,OAAvFa,UAAuF,EAA5C,OAAK,GAAAT,gBAAA,CAAGC,MAAA,CAAAS,MAAM,CAACT,MAAA,CAAAC,OAAO,CAACS,WAAW,mBAC7Ef,mBAAA,CAA4E,OAA5EgB,UAA4E,EAAxB,MAAI,GAAAZ,gBAAA,CAAGC,MAAA,CAAAY,QAAQ,iB,GAErEjB,mBAAA,CAA6E;QAAxEP,KAAK,EAAC,kCAAkC;QAACyB,SAAwB,EAAhBb,MAAA,CAAAC,OAAO,CAACa;8BAnBtEC,UAAA,E,yBAqBMC,YAAA,CAAmEC,0BAAA;QAAjDC,QAAQ,EAAElB,MAAA,CAAAC,OAAO,CAACkB;;;IArB1CC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}