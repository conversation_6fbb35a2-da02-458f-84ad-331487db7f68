{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport store from '@/store';\nimport { ref, onMounted } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport DownloadFileName from './DownloadFileName/DownloadFileName';\nimport VersionComparisonFileInfo from './VersionComparisonFileInfo';\nvar __default__ = {\n  name: 'VersionComparison'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var loading = ref(false);\n    var loadingText = ref('');\n    // const tabId = ref('4')\n    var oldId = ref('');\n    var newId = ref('');\n    var oldFile = ref();\n    var newFile = ref();\n    var url = ref('');\n    var fileName = ref('');\n    var html = ref('');\n    var show = ref(false);\n    onMounted(function () {\n      if (route.query.ids) {\n        var idList = route.query.ids.split(',');\n        oldId.value = idList[0];\n        newId.value = idList[1];\n      }\n    });\n\n    // const tabClick = (row) => { tabId.value = row }\n    var oldCallback = function oldCallback(file) {\n      oldFile.value = file;\n    };\n    var newCallback = function newCallback(file) {\n      newFile.value = file;\n    };\n    // const loadCallback = (type) => {\n    //   loading.value = type\n    // }\n    var handleButton = function handleButton(type) {\n      if (oldId.value && newId.value || oldFile.value && newFile.value) {\n        if (type) {\n          wordApiPaintedWord();\n        } else {\n          wordApiContrastWord();\n        }\n      } else {\n        ElMessage({\n          type: 'warning',\n          message: '请先上传需要对比的两个原始法规！'\n        });\n      }\n    };\n    /**\r\n       * 版本对比-花脸稿接口\r\n      */\n    var wordApiPaintedWord = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var AreaId, param, _yield$api$wordApiPai, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              loading.value = true;\n              AreaId = sessionStorage.getItem('AreaId') || ''; // 用户地区\n              param = new FormData();\n              if (oldId.value && newId.value) {\n                param.append('urlPath1', api.filePreview(oldId.value));\n                param.append('urlPath2', api.filePreview(newId.value));\n              } else if (oldFile.value && newFile.value) {\n                param.append('file1', oldFile.value);\n                param.append('file2', newFile.value);\n              }\n              param.append('areaCode', AreaId);\n              _context.next = 8;\n              return api.wordApiPaintedWord(param, function () {}, '');\n            case 8:\n              _yield$api$wordApiPai = _context.sent;\n              data = _yield$api$wordApiPai.data;\n              loading.value = false;\n              fileName.value = data.fileName;\n              html.value = data.html;\n              url.value = URL.createObjectURL(new Blob([dataURLtoBlob(data.pdfBase64)]));\n              _context.next = 19;\n              break;\n            case 16:\n              _context.prev = 16;\n              _context.t0 = _context[\"catch\"](0);\n              loading.value = false;\n            case 19:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 16]]);\n      }));\n      return function wordApiPaintedWord() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    /**\r\n       * 版本对比-对照表接口\r\n      */\n    var wordApiContrastWord = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var AreaId, param, _yield$api$wordApiCon, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.prev = 0;\n              loading.value = true;\n              AreaId = sessionStorage.getItem('AreaId') || ''; // 用户地区\n              param = new FormData();\n              if (oldId.value && newId.value) {\n                param.append('urlPath1', api.filePreview(oldId.value));\n                param.append('urlPath2', api.filePreview(newId.value));\n              } else if (oldFile.value && newFile.value) {\n                param.append('file1', oldFile.value);\n                param.append('file2', newFile.value);\n              }\n              param.append('areaCode', AreaId);\n              _context2.next = 8;\n              return api.wordApiContrastWord(param, function () {}, '');\n            case 8:\n              _yield$api$wordApiCon = _context2.sent;\n              data = _yield$api$wordApiCon.data;\n              loading.value = false;\n              fileName.value = data.fileName;\n              html.value = data.html;\n              url.value = URL.createObjectURL(new Blob([dataURLtoBlob(data.pdfBase64)]));\n              _context2.next = 19;\n              break;\n            case 16:\n              _context2.prev = 16;\n              _context2.t0 = _context2[\"catch\"](0);\n              loading.value = false;\n            case 19:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, null, [[0, 16]]);\n      }));\n      return function wordApiContrastWord() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    function dataURLtoBlob(str) {\n      var bstr = window.atob(str);\n      var n = bstr.length;\n      var u8arr = new Uint8Array(n);\n      while (n--) {\n        u8arr[n] = bstr.charCodeAt(n);\n      }\n      return new Blob([u8arr], {\n        type: 'application/pdf'\n      });\n    }\n    var handleFileDownload = function handleFileDownload() {\n      if (!fileName.value) return ElMessage({\n        type: 'warning',\n        message: '当前没有内容可下载！'\n      });\n      show.value = true;\n    };\n    var callback = function callback(name) {\n      if (name) {\n        store.commit('setExtendDownloadFile', {\n          url: '/wordApi/downloadWord',\n          params: {\n            fileName: fileName.value\n          },\n          fileSize: 0,\n          fileName: `${name}.docx`,\n          fileType: 'docx'\n        });\n        // extendDownloadFile({ url: '/wordApi/downloadWord', params: { fileName: fileName.value }, fileSize: 0, fileName: `${name}.docx`, fileType: 'docx' })\n      }\n      show.value = false;\n    };\n    var __returned__ = {\n      route,\n      loading,\n      loadingText,\n      oldId,\n      newId,\n      oldFile,\n      newFile,\n      url,\n      fileName,\n      html,\n      show,\n      oldCallback,\n      newCallback,\n      handleButton,\n      wordApiPaintedWord,\n      wordApiContrastWord,\n      dataURLtoBlob,\n      handleFileDownload,\n      callback,\n      get api() {\n        return api;\n      },\n      get store() {\n        return store;\n      },\n      ref,\n      onMounted,\n      get useRoute() {\n        return useRoute;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get DownloadFileName() {\n        return DownloadFileName;\n      },\n      get VersionComparisonFileInfo() {\n        return VersionComparisonFileInfo;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "store", "ref", "onMounted", "useRoute", "ElMessage", "DownloadFileName", "VersionComparisonFileInfo", "__default__", "route", "loading", "loadingText", "oldId", "newId", "oldFile", "newFile", "url", "fileName", "html", "show", "query", "ids", "idList", "split", "oldCallback", "file", "newCallback", "handleButton", "wordApiPaintedWord", "wordApiContrastWord", "message", "_ref2", "_callee", "AreaId", "param", "_yield$api$wordApiPai", "data", "_callee$", "_context", "sessionStorage", "getItem", "FormData", "append", "filePreview", "URL", "createObjectURL", "Blob", "dataURLtoBlob", "pdfBase64", "t0", "_ref3", "_callee2", "_yield$api$wordApiCon", "_callee2$", "_context2", "str", "bstr", "window", "atob", "u8arr", "Uint8Array", "charCodeAt", "handleFileDownload", "callback", "commit", "params", "fileSize", "fileType"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/Intelligentize/VersionComparison/VersionComparison.vue"], "sourcesContent": ["<template>\r\n  <div class=\"VersionComparison\">\r\n    <el-scrollbar class=\"VersionComparisonScrollbar\"\r\n                  v-loading=\"loading\"\r\n                  :lement-loading-text=\"loadingText\"\r\n                  always>\r\n      <div class=\"VersionComparisonBody\">\r\n        <div class=\"VersionComparisonDataBody\">\r\n          <div class=\"VersionComparisonDataFile\">\r\n            <VersionComparisonFileInfo name=\"旧版本\"\r\n                                       :id=\"oldId\"\r\n                                       @loadCallback=\"oldCallback\"></VersionComparisonFileInfo>\r\n            <VersionComparisonFileInfo name=\"新版本\"\r\n                                       :id=\"newId\"\r\n                                       @loadCallback=\"newCallback\"></VersionComparisonFileInfo>\r\n          </div>\r\n        </div>\r\n        <div class=\"VersionComparisonWordBody\">\r\n          <div class=\"VersionComparisonWordButton\">\r\n            <div class=\"VersionComparisonWordButtonItem\">\r\n              对照表\r\n            </div>\r\n            <div class=\"VersionComparisonWordButtonItem\">\r\n              <el-button @click=\"handleButton(false)\"\r\n                         type=\"primary\"\r\n                         round>生成对照表</el-button>\r\n              <el-button @click=\"handleFileDownload\"\r\n                         type=\"primary\"\r\n                         round>下载</el-button>\r\n            </div>\r\n          </div>\r\n          <el-scrollbar class=\"VersionComparisonWordScrollbar\"\r\n                        always>\r\n            <div class=\"VersionComparisonWord\"\r\n                 v-html=\"html\"></div>\r\n          </el-scrollbar>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      name=\"下载\">\r\n      <DownloadFileName @callback=\"callback\"></DownloadFileName>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template >\r\n<script>\r\nexport default { name: 'VersionComparison' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport store from '@/store'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport DownloadFileName from './DownloadFileName/DownloadFileName'\r\nimport VersionComparisonFileInfo from './VersionComparisonFileInfo'\r\nconst route = useRoute()\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n// const tabId = ref('4')\r\nconst oldId = ref('')\r\nconst newId = ref('')\r\nconst oldFile = ref()\r\nconst newFile = ref()\r\nconst url = ref('')\r\nconst fileName = ref('')\r\nconst html = ref('')\r\nconst show = ref(false)\r\n\r\nonMounted(() => {\r\n  if (route.query.ids) {\r\n    const idList = route.query.ids.split(',')\r\n    oldId.value = idList[0]\r\n    newId.value = idList[1]\r\n  }\r\n})\r\n\r\n// const tabClick = (row) => { tabId.value = row }\r\nconst oldCallback = (file) => {\r\n  oldFile.value = file\r\n}\r\nconst newCallback = (file) => {\r\n  newFile.value = file\r\n}\r\n// const loadCallback = (type) => {\r\n//   loading.value = type\r\n// }\r\nconst handleButton = (type) => {\r\n  if ((oldId.value && newId.value) || (oldFile.value && newFile.value)) {\r\n    if (type) { wordApiPaintedWord() } else { wordApiContrastWord() }\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请先上传需要对比的两个原始法规！' })\r\n  }\r\n}\r\n/**\r\n   * 版本对比-花脸稿接口\r\n  */\r\nconst wordApiPaintedWord = async () => {\r\n  try {\r\n    loading.value = true\r\n    const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区\r\n    const param = new FormData()\r\n    if (oldId.value && newId.value) {\r\n      param.append('urlPath1', api.filePreview(oldId.value))\r\n      param.append('urlPath2', api.filePreview(newId.value))\r\n    } else if (oldFile.value && newFile.value) {\r\n      param.append('file1', oldFile.value)\r\n      param.append('file2', newFile.value)\r\n    }\r\n    param.append('areaCode', AreaId)\r\n    const { data } = await api.wordApiPaintedWord(param, () => { }, '')\r\n    loading.value = false\r\n    fileName.value = data.fileName\r\n    html.value = data.html\r\n    url.value = URL.createObjectURL(new Blob([dataURLtoBlob(data.pdfBase64)]))\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\n/**\r\n   * 版本对比-对照表接口\r\n  */\r\nconst wordApiContrastWord = async () => {\r\n  try {\r\n    loading.value = true\r\n    const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区\r\n    const param = new FormData()\r\n    if (oldId.value && newId.value) {\r\n      param.append('urlPath1', api.filePreview(oldId.value))\r\n      param.append('urlPath2', api.filePreview(newId.value))\r\n    } else if (oldFile.value && newFile.value) {\r\n      param.append('file1', oldFile.value)\r\n      param.append('file2', newFile.value)\r\n    }\r\n    param.append('areaCode', AreaId)\r\n    const { data } = await api.wordApiContrastWord(param, () => { }, '')\r\n    loading.value = false\r\n    fileName.value = data.fileName\r\n    html.value = data.html\r\n    url.value = URL.createObjectURL(new Blob([dataURLtoBlob(data.pdfBase64)]))\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nfunction dataURLtoBlob (str) {\r\n  const bstr = window.atob(str)\r\n  var n = bstr.length\r\n  const u8arr = new Uint8Array(n)\r\n  while (n--) {\r\n    u8arr[n] = bstr.charCodeAt(n)\r\n  }\r\n  return new Blob([u8arr], { type: 'application/pdf' })\r\n}\r\nconst handleFileDownload = () => {\r\n  if (!fileName.value) return ElMessage({ type: 'warning', message: '当前没有内容可下载！' })\r\n  show.value = true\r\n}\r\nconst callback = (name) => {\r\n  if (name) {\r\n    store.commit('setExtendDownloadFile', { url: '/wordApi/downloadWord', params: { fileName: fileName.value }, fileSize: 0, fileName: `${name}.docx`, fileType: 'docx' })\r\n    // extendDownloadFile({ url: '/wordApi/downloadWord', params: { fileName: fileName.value }, fileSize: 0, fileName: `${name}.docx`, fileType: 'docx' })\r\n  }\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.VersionComparison {\r\n  width: 100%;\r\n  height: 100%;\r\n  // height: 100vh;\r\n  background: var(--zy-el-color-info-light-9);\r\n\r\n  .VersionComparisonScrollbar {\r\n    width: 100%;\r\n    height: calc(100vh - 62px);\r\n  }\r\n\r\n  .VersionComparisonBody {\r\n    width: 100%;\r\n    max-width: 1660px;\r\n    height: calc(100vh - 62px);\r\n    margin: auto;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .VersionComparisonWordBody {\r\n      width: 880px;\r\n      height: 100%;\r\n      border-radius: 8px;\r\n      box-shadow: var(--zy-el-box-shadow);\r\n\r\n\r\n      .VersionComparisonWordButton {\r\n        width: 100%;\r\n        height: 50px;\r\n        color: #fff;\r\n        background-image: linear-gradient(72deg, var(--zy-el-color-primary), var(--zy-el-color-primary-light-9));\r\n        // background-color: var(--zy-el-color-primary-light-5);\r\n        // margin-bottom: 10px;\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 0 20px;\r\n        border-top-left-radius: 8px;\r\n        border-top-right-radius: 8px;\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .VersionComparisonWordButtonItem {\r\n          display: flex;\r\n\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n        }\r\n      }\r\n\r\n      .VersionComparisonWordScrollbar {\r\n        width: 100%;\r\n        height: calc(100% - 50px);\r\n        background-color: #fff;\r\n        border-bottom-left-radius: 8px;\r\n        border-bottom-right-radius: 8px;\r\n\r\n        .VersionComparisonWord {\r\n          width: 595.3pt;\r\n          min-height: 842pt;\r\n          box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n          margin: 20px auto;\r\n          padding: 44.9pt 73pt;\r\n        }\r\n      }\r\n    }\r\n\r\n    .VersionComparisonDataBody {\r\n      width: calc(100% - 890px);\r\n      // margin-left: 10px;\r\n\r\n      .VersionComparisonDataTab {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        height: 60px;\r\n        padding: 0 20px;\r\n        padding-bottom: 10px;\r\n\r\n        .VersionComparisonDataTabItem {\r\n          width: 25%;\r\n          height: var(--zy-height);\r\n          box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.16);\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-weight: bold;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .VersionComparisonDataTabItem.is-active {\r\n          color: #fff;\r\n          background-color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .VersionComparisonDataFile {\r\n        width: 100%;\r\n        // height: calc(100% - 60px);\r\n        height: calc(100%);\r\n        padding-right: 10px;\r\n\r\n        .VersionComparisonFileInfo {\r\n          background-color: #fff;\r\n        }\r\n\r\n        .LawRetrieval {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAkDA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,GAAG,EAAEC,SAAS,QAAQ,KAAK;AACpC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,yBAAyB,MAAM,6BAA6B;AATnE,IAAAC,WAAA,GAAe;EAAEnC,IAAI,EAAE;AAAoB,CAAC;;;;;IAU5C,IAAMoC,KAAK,GAAGL,QAAQ,CAAC,CAAC;IACxB,IAAMM,OAAO,GAAGR,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMS,WAAW,GAAGT,GAAG,CAAC,EAAE,CAAC;IAC3B;IACA,IAAMU,KAAK,GAAGV,GAAG,CAAC,EAAE,CAAC;IACrB,IAAMW,KAAK,GAAGX,GAAG,CAAC,EAAE,CAAC;IACrB,IAAMY,OAAO,GAAGZ,GAAG,CAAC,CAAC;IACrB,IAAMa,OAAO,GAAGb,GAAG,CAAC,CAAC;IACrB,IAAMc,GAAG,GAAGd,GAAG,CAAC,EAAE,CAAC;IACnB,IAAMe,QAAQ,GAAGf,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMgB,IAAI,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACpB,IAAMiB,IAAI,GAAGjB,GAAG,CAAC,KAAK,CAAC;IAEvBC,SAAS,CAAC,YAAM;MACd,IAAIM,KAAK,CAACW,KAAK,CAACC,GAAG,EAAE;QACnB,IAAMC,MAAM,GAAGb,KAAK,CAACW,KAAK,CAACC,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;QACzCX,KAAK,CAAChH,KAAK,GAAG0H,MAAM,CAAC,CAAC,CAAC;QACvBT,KAAK,CAACjH,KAAK,GAAG0H,MAAM,CAAC,CAAC,CAAC;MACzB;IACF,CAAC,CAAC;;IAEF;IACA,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAC5BX,OAAO,CAAClH,KAAK,GAAG6H,IAAI;IACtB,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAID,IAAI,EAAK;MAC5BV,OAAO,CAACnH,KAAK,GAAG6H,IAAI;IACtB,CAAC;IACD;IACA;IACA;IACA,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAI5G,IAAI,EAAK;MAC7B,IAAK6F,KAAK,CAAChH,KAAK,IAAIiH,KAAK,CAACjH,KAAK,IAAMkH,OAAO,CAAClH,KAAK,IAAImH,OAAO,CAACnH,KAAM,EAAE;QACpE,IAAImB,IAAI,EAAE;UAAE6G,kBAAkB,CAAC,CAAC;QAAC,CAAC,MAAM;UAAEC,mBAAmB,CAAC,CAAC;QAAC;MAClE,CAAC,MAAM;QACLxB,SAAS,CAAC;UAAEtF,IAAI,EAAE,SAAS;UAAE+G,OAAO,EAAE;QAAmB,CAAC,CAAC;MAC7D;IACF,CAAC;IACD;AACA;AACA;IACA,IAAMF,kBAAkB;MAAA,IAAAG,KAAA,GAAApC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0D,QAAA;QAAA,IAAAC,MAAA,EAAAC,KAAA,EAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAAlJ,mBAAA,GAAAuB,IAAA,UAAA4H,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAvD,IAAA,GAAAuD,QAAA,CAAAlF,IAAA;YAAA;cAAAkF,QAAA,CAAAvD,IAAA;cAEvB2B,OAAO,CAAC9G,KAAK,GAAG,IAAI;cACdqI,MAAM,GAAGM,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAC;cAChDN,KAAK,GAAG,IAAIO,QAAQ,CAAC,CAAC;cAC5B,IAAI7B,KAAK,CAAChH,KAAK,IAAIiH,KAAK,CAACjH,KAAK,EAAE;gBAC9BsI,KAAK,CAACQ,MAAM,CAAC,UAAU,EAAE1C,GAAG,CAAC2C,WAAW,CAAC/B,KAAK,CAAChH,KAAK,CAAC,CAAC;gBACtDsI,KAAK,CAACQ,MAAM,CAAC,UAAU,EAAE1C,GAAG,CAAC2C,WAAW,CAAC9B,KAAK,CAACjH,KAAK,CAAC,CAAC;cACxD,CAAC,MAAM,IAAIkH,OAAO,CAAClH,KAAK,IAAImH,OAAO,CAACnH,KAAK,EAAE;gBACzCsI,KAAK,CAACQ,MAAM,CAAC,OAAO,EAAE5B,OAAO,CAAClH,KAAK,CAAC;gBACpCsI,KAAK,CAACQ,MAAM,CAAC,OAAO,EAAE3B,OAAO,CAACnH,KAAK,CAAC;cACtC;cACAsI,KAAK,CAACQ,MAAM,CAAC,UAAU,EAAET,MAAM,CAAC;cAAAK,QAAA,CAAAlF,IAAA;cAAA,OACT4C,GAAG,CAAC4B,kBAAkB,CAACM,KAAK,EAAE,YAAM,CAAE,CAAC,EAAE,EAAE,CAAC;YAAA;cAAAC,qBAAA,GAAAG,QAAA,CAAAzF,IAAA;cAA3DuF,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZ1B,OAAO,CAAC9G,KAAK,GAAG,KAAK;cACrBqH,QAAQ,CAACrH,KAAK,GAAGwI,IAAI,CAACnB,QAAQ;cAC9BC,IAAI,CAACtH,KAAK,GAAGwI,IAAI,CAAClB,IAAI;cACtBF,GAAG,CAACpH,KAAK,GAAGgJ,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACC,aAAa,CAACX,IAAI,CAACY,SAAS,CAAC,CAAC,CAAC,CAAC;cAAAV,QAAA,CAAAlF,IAAA;cAAA;YAAA;cAAAkF,QAAA,CAAAvD,IAAA;cAAAuD,QAAA,CAAAW,EAAA,GAAAX,QAAA;cAE1E5B,OAAO,CAAC9G,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAA0I,QAAA,CAAApD,IAAA;UAAA;QAAA,GAAA8C,OAAA;MAAA,CAExB;MAAA,gBArBKJ,kBAAkBA,CAAA;QAAA,OAAAG,KAAA,CAAAlC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqBvB;IACD;AACA;AACA;IACA,IAAMiC,mBAAmB;MAAA,IAAAqB,KAAA,GAAAvD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA6E,SAAA;QAAA,IAAAlB,MAAA,EAAAC,KAAA,EAAAkB,qBAAA,EAAAhB,IAAA;QAAA,OAAAlJ,mBAAA,GAAAuB,IAAA,UAAA4I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAlG,IAAA;YAAA;cAAAkG,SAAA,CAAAvE,IAAA;cAExB2B,OAAO,CAAC9G,KAAK,GAAG,IAAI;cACdqI,MAAM,GAAGM,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAC;cAChDN,KAAK,GAAG,IAAIO,QAAQ,CAAC,CAAC;cAC5B,IAAI7B,KAAK,CAAChH,KAAK,IAAIiH,KAAK,CAACjH,KAAK,EAAE;gBAC9BsI,KAAK,CAACQ,MAAM,CAAC,UAAU,EAAE1C,GAAG,CAAC2C,WAAW,CAAC/B,KAAK,CAAChH,KAAK,CAAC,CAAC;gBACtDsI,KAAK,CAACQ,MAAM,CAAC,UAAU,EAAE1C,GAAG,CAAC2C,WAAW,CAAC9B,KAAK,CAACjH,KAAK,CAAC,CAAC;cACxD,CAAC,MAAM,IAAIkH,OAAO,CAAClH,KAAK,IAAImH,OAAO,CAACnH,KAAK,EAAE;gBACzCsI,KAAK,CAACQ,MAAM,CAAC,OAAO,EAAE5B,OAAO,CAAClH,KAAK,CAAC;gBACpCsI,KAAK,CAACQ,MAAM,CAAC,OAAO,EAAE3B,OAAO,CAACnH,KAAK,CAAC;cACtC;cACAsI,KAAK,CAACQ,MAAM,CAAC,UAAU,EAAET,MAAM,CAAC;cAAAqB,SAAA,CAAAlG,IAAA;cAAA,OACT4C,GAAG,CAAC6B,mBAAmB,CAACK,KAAK,EAAE,YAAM,CAAE,CAAC,EAAE,EAAE,CAAC;YAAA;cAAAkB,qBAAA,GAAAE,SAAA,CAAAzG,IAAA;cAA5DuF,IAAI,GAAAgB,qBAAA,CAAJhB,IAAI;cACZ1B,OAAO,CAAC9G,KAAK,GAAG,KAAK;cACrBqH,QAAQ,CAACrH,KAAK,GAAGwI,IAAI,CAACnB,QAAQ;cAC9BC,IAAI,CAACtH,KAAK,GAAGwI,IAAI,CAAClB,IAAI;cACtBF,GAAG,CAACpH,KAAK,GAAGgJ,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACC,aAAa,CAACX,IAAI,CAACY,SAAS,CAAC,CAAC,CAAC,CAAC;cAAAM,SAAA,CAAAlG,IAAA;cAAA;YAAA;cAAAkG,SAAA,CAAAvE,IAAA;cAAAuE,SAAA,CAAAL,EAAA,GAAAK,SAAA;cAE1E5C,OAAO,CAAC9G,KAAK,GAAG,KAAK;YAAA;YAAA;cAAA,OAAA0J,SAAA,CAAApE,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA,CAExB;MAAA,gBArBKtB,mBAAmBA,CAAA;QAAA,OAAAqB,KAAA,CAAArD,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqBxB;IACD,SAASmD,aAAaA,CAAEQ,GAAG,EAAE;MAC3B,IAAMC,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC;MAC7B,IAAI/J,CAAC,GAAGgK,IAAI,CAACvF,MAAM;MACnB,IAAM0F,KAAK,GAAG,IAAIC,UAAU,CAACpK,CAAC,CAAC;MAC/B,OAAOA,CAAC,EAAE,EAAE;QACVmK,KAAK,CAACnK,CAAC,CAAC,GAAGgK,IAAI,CAACK,UAAU,CAACrK,CAAC,CAAC;MAC/B;MACA,OAAO,IAAIsJ,IAAI,CAAC,CAACa,KAAK,CAAC,EAAE;QAAE5I,IAAI,EAAE;MAAkB,CAAC,CAAC;IACvD;IACA,IAAM+I,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B,IAAI,CAAC7C,QAAQ,CAACrH,KAAK,EAAE,OAAOyG,SAAS,CAAC;QAAEtF,IAAI,EAAE,SAAS;QAAE+G,OAAO,EAAE;MAAa,CAAC,CAAC;MACjFX,IAAI,CAACvH,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMmK,QAAQ,GAAG,SAAXA,QAAQA,CAAI1F,IAAI,EAAK;MACzB,IAAIA,IAAI,EAAE;QACR4B,KAAK,CAAC+D,MAAM,CAAC,uBAAuB,EAAE;UAAEhD,GAAG,EAAE,uBAAuB;UAAEiD,MAAM,EAAE;YAAEhD,QAAQ,EAAEA,QAAQ,CAACrH;UAAM,CAAC;UAAEsK,QAAQ,EAAE,CAAC;UAAEjD,QAAQ,EAAE,GAAG5C,IAAI,OAAO;UAAE8F,QAAQ,EAAE;QAAO,CAAC,CAAC;QACtK;MACF;MACAhD,IAAI,CAACvH,KAAK,GAAG,KAAK;IACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}