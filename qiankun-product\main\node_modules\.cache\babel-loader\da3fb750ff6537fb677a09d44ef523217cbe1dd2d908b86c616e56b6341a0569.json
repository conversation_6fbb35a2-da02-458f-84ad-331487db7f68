{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, vShow as _vShow, createVNode as _createVNode, withDirectives as _withDirectives, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, withCtx as _withCtx, createBlock as _createBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LayoutMenu\"\n};\nvar _hoisted_2 = {\n  class: \"xyl-menu-text\"\n};\nvar _hoisted_3 = {\n  class: \"xyl-menu-text\"\n};\nvar _hoisted_4 = {\n  class: \"xyl-menu-icon\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_sub_menu = _resolveComponent(\"el-sub-menu\");\n  var _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  var _component_el_menu = _resolveComponent(\"el-menu\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_ArrowRight = _resolveComponent(\"ArrowRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_ArrowLeft = _resolveComponent(\"ArrowLeft\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_scrollbar, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_menu, {\n        class: _normalizeClass(['xyl-menu-body', $setup.isCollapse ? 'xyl-menu-collapse' : 'xyl-menu-unfold']),\n        collapse: $setup.isCollapse,\n        \"default-active\": $setup.menuId,\n        onSelect: $setup.handleSelect,\n        onOpen: $setup.handleOpen,\n        ref: \"menuRef\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.menuData, function (item) {\n            return _openBlock(), _createElementBlock(_Fragment, null, [item.children.length ? (_openBlock(), _createBlock(_component_el_sub_menu, {\n              index: item.id,\n              key: item.id,\n              \"popper-class\": \"xyl-menu-popper\"\n            }, {\n              title: _withCtx(function () {\n                return [_withDirectives(_createVNode(_component_el_image, {\n                  fit: \"cover\",\n                  src: item.icon\n                }, null, 8 /* PROPS */, [\"src\"]), [[_vShow, $setup.isCollapse]]), _createElementVNode(\"span\", _hoisted_2, _toDisplayString(item.name), 1 /* TEXT */)];\n              }),\n              default: _withCtx(function () {\n                return [_createVNode($setup[\"XylMenuItem\"], {\n                  menuData: item.children\n                }, null, 8 /* PROPS */, [\"menuData\"])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"index\"])) : _createCommentVNode(\"v-if\", true), !item.children.length ? (_openBlock(), _createBlock(_component_el_menu_item, {\n              index: item.id,\n              key: item.id\n            }, {\n              title: _withCtx(function () {\n                return [_createElementVNode(\"span\", _hoisted_3, _toDisplayString(item.name), 1 /* TEXT */)];\n              }),\n              default: _withCtx(function () {\n                return [_withDirectives(_createVNode(_component_el_image, {\n                  fit: \"cover\",\n                  src: item.icon\n                }, null, 8 /* PROPS */, [\"src\"]), [[_vShow, $setup.isCollapse]])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"index\"])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n          }), 256 /* UNKEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"class\", \"collapse\", \"default-active\"])];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", {\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.isCollapse = !$setup.isCollapse;\n    })\n  }, [$setup.isCollapse ? (_openBlock(), _createBlock(_component_el_icon, {\n    key: 0\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_ArrowRight)];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), !$setup.isCollapse ? (_openBlock(), _createBlock(_component_el_icon, {\n    key: 1\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_ArrowLeft)];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_scrollbar", "default", "_withCtx", "_component_el_menu", "_normalizeClass", "$setup", "isCollapse", "collapse", "menuId", "onSelect", "handleSelect", "onOpen", "handleOpen", "ref", "_Fragment", "_renderList", "menuData", "item", "children", "length", "_createBlock", "_component_el_sub_menu", "index", "id", "key", "title", "_component_el_image", "fit", "src", "icon", "_createElementVNode", "_hoisted_2", "_toDisplayString", "name", "_", "_createCommentVNode", "_component_el_menu_item", "_hoisted_3", "_hoisted_4", "onClick", "_cache", "$event", "_component_el_icon", "_component_ArrowRight", "_component_ArrowLeft"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutView\\component\\LayoutMenu\\LayoutMenu.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LayoutMenu\">\r\n    <el-scrollbar>\r\n      <el-menu :class=\"['xyl-menu-body', isCollapse ? 'xyl-menu-collapse' : 'xyl-menu-unfold']\" :collapse=\"isCollapse\"\r\n        :default-active=\"menuId\" @select=\"handleSelect\" @open=\"handleOpen\" ref=\"menuRef\">\r\n        <template v-for=\"item in menuData\">\r\n          <el-sub-menu :index=\"item.id\" :key=\"item.id\" popper-class=\"xyl-menu-popper\" v-if=\"item.children.length\">\r\n            <template #title>\r\n              <el-image fit=\"cover\" :src=\"item.icon\" v-show=\"isCollapse\" />\r\n              <span class=\"xyl-menu-text\">{{ item.name }}</span>\r\n            </template>\r\n            <xyl-menu-item :menuData=\"item.children\"></xyl-menu-item>\r\n          </el-sub-menu>\r\n          <el-menu-item :index=\"item.id\" :key=\"item.id\" v-if=\"!item.children.length\">\r\n            <el-image fit=\"cover\" :src=\"item.icon\" v-show=\"isCollapse\" />\r\n            <template #title>\r\n              <span class=\"xyl-menu-text\">{{ item.name }}</span>\r\n            </template>\r\n          </el-menu-item>\r\n        </template>\r\n      </el-menu>\r\n    </el-scrollbar>\r\n    <div class=\"xyl-menu-icon\">\r\n      <span @click=\"isCollapse = !isCollapse\">\r\n        <el-icon v-if=\"isCollapse\">\r\n          <ArrowRight />\r\n        </el-icon>\r\n        <el-icon v-if=\"!isCollapse\">\r\n          <ArrowLeft />\r\n        </el-icon>\r\n      </span>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutMenu' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch } from 'vue'\r\nimport XylMenuItem from './LayoutMenuItem.vue'\r\nconst props = defineProps({\r\n  modelValue: [String, Number],\r\n  menuData: { type: Array, default: () => [] }\r\n})\r\nconst emit = defineEmits(['update:modelValue', 'select'])\r\n\r\nwatch(() => props.modelValue, () => { menuId.value = props.modelValue })\r\nwatch(() => props.menuData, () => { menuData.value = props.menuData })\r\n\r\nconst isCollapse = ref(false)\r\nconst menuId = ref(props.modelValue)\r\nconst menuData = ref(props.menuData)\r\nconst menuRef = ref(null)\r\nconst currentOpenMenu = ref('')\r\nconst handleSelect = (key) => {\r\n  emit('update:modelValue', key)\r\n  selectData(props.menuData, key)\r\n}\r\nconst handleOpen = (index) => {\r\n  const isFirstLevel = props.menuData.some(item => item.id === index)\r\n  if (isFirstLevel) {\r\n    if (currentOpenMenu.value) {\r\n      const findTopParent = (items, targetId, topParent = null) => {\r\n        for (const item of items) {\r\n          if (item.id === targetId) {\r\n            return topParent || item.id\r\n          }\r\n          if (item.children.length > 0) {\r\n            const result = findTopParent(item.children, targetId, item.id)\r\n            if (result) return result\r\n          }\r\n        }\r\n        return null\r\n      }\r\n      const topParentId = findTopParent(props.menuData, currentOpenMenu.value)\r\n      if (topParentId && topParentId !== index) {\r\n        menuRef.value?.close(topParentId)\r\n      }\r\n    }\r\n    if (currentOpenMenu.value && currentOpenMenu.value !== index) {\r\n      menuRef.value?.close(currentOpenMenu.value)\r\n    }\r\n  }\r\n  currentOpenMenu.value = index\r\n}\r\nconst selectData = (data, id) => {\r\n  data.forEach(item => {\r\n    if (item.children.length === 0) {\r\n      if (item.id === id) { emit('select', item) }\r\n    } else {\r\n      selectData(item.children, id)\r\n    }\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutMenu {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n\r\n  .zy-el-scrollbar {\r\n    padding-top: 16px;\r\n    padding-left: 20px;\r\n    padding-right: 20px;\r\n    background-color: #fff;\r\n  }\r\n\r\n  &:hover {\r\n    .xyl-menu-icon {\r\n      span {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n\r\n  .xyl-menu-body:not(.zy-el-menu--collapse) {\r\n    width: 200px;\r\n  }\r\n\r\n  .zy-el-menu {\r\n    border-right: 0;\r\n    --zy-el-menu-bg-color: #fff;\r\n\r\n    .zy-el-menu-item.is-active {\r\n      font-weight: bold;\r\n      position: relative;\r\n      background: var(--zy-el-color-primary-light-9);\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 3px;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 24px;\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .zy-el-menu-item,\r\n    .zy-el-sub-menu__title {\r\n      padding-top: 10px !important;\r\n      padding-bottom: 10px !important;\r\n    }\r\n\r\n    .zy-el-sub-menu {\r\n      >.zy-el-menu-item {\r\n        margin: 0;\r\n        padding: 10px 40px;\r\n      }\r\n\r\n      .zy-el-sub-menu .zy-el-menu-item {\r\n        padding: 10px 50px;\r\n      }\r\n    }\r\n\r\n    // 一级菜单\r\n    >.zy-el-menu-item,\r\n    >.zy-el-sub-menu>.zy-el-sub-menu__title {\r\n      padding-left: 20px !important;\r\n    }\r\n\r\n    // 二级菜单\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-menu-item,\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu__title {\r\n      padding-left: 35px !important;\r\n    }\r\n\r\n    // 三级菜单\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-menu-item,\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu__title {\r\n      padding-left: 45px !important;\r\n    }\r\n\r\n    // 四级菜单\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-menu-item,\r\n    .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu>.zy-el-menu .zy-el-sub-menu__title {\r\n      padding-left: 55px !important;\r\n    }\r\n  }\r\n\r\n  .xyl-menu-unfold {\r\n\r\n    .zy-el-menu-item,\r\n    >.zy-el-sub-menu>.zy-el-sub-menu__title {\r\n      min-width: 200px;\r\n      height: auto;\r\n      min-height: calc(var(--zy-el-menu-sub-item-height));\r\n      line-height: 1;\r\n\r\n      &:not(.zy-el-sub-menu .zy-el-menu-item) {\r\n        margin: 20px 0 10px 0;\r\n        background: url(\"../../../img/icon_layout_menu_bg.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        color: #fff;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .xyl-menu-text {\r\n          font-size: 16px;\r\n          text-align: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .zy-el-sub-menu__title {\r\n      min-width: 200px;\r\n      height: auto;\r\n      min-height: calc(var(--zy-el-menu-sub-item-height));\r\n      line-height: 1;\r\n      padding-right: 40px;\r\n    }\r\n\r\n    .xyl-menu-text {\r\n      display: inline-block;\r\n      width: 100%;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: var(--zy-distance-four) 0;\r\n      text-overflow: clip;\r\n      white-space: normal;\r\n    }\r\n  }\r\n\r\n  .zy-el-menu--collapse {\r\n    .zy-el-menu-item {\r\n      min-width: auto !important;\r\n      height: calc(var(--zy-el-menu-sub-item-height));\r\n      min-height: auto;\r\n      line-height: calc(var(--zy-el-menu-sub-item-height));\r\n    }\r\n\r\n    .zy-el-sub-menu__title {\r\n      min-width: auto !important;\r\n      height: calc(var(--zy-el-menu-sub-item-height));\r\n      min-height: auto;\r\n      line-height: calc(var(--zy-el-menu-sub-item-height));\r\n      padding-right: calc(var(--zy-el-menu-base-level-padding));\r\n    }\r\n\r\n    .xyl-menu-text {\r\n      text-overflow: initial;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .xyl-menu-icon {\r\n    width: 17px;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    span {\r\n      display: none;\r\n      width: 17px;\r\n      height: 83px;\r\n      line-height: 83px;\r\n      background: url(\"../../../img/menu_icon.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n      background-position: center;\r\n      cursor: pointer;\r\n\r\n      .zy-el-icon {\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.xyl-menu-popper {\r\n  .zy-el-menu {\r\n    width: 200px;\r\n  }\r\n\r\n  .zy-el-menu-item.is-active {\r\n    font-weight: bold;\r\n    position: relative;\r\n    background: var(--zy-el-color-primary-light-9);\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 3px;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .zy-el-menu-item {\r\n    min-width: 200px;\r\n    height: auto;\r\n    min-height: calc(var(--zy-el-menu-sub-item-height));\r\n    line-height: 1;\r\n  }\r\n\r\n  .zy-el-sub-menu__title {\r\n    min-width: 200px;\r\n    height: auto;\r\n    min-height: calc(var(--zy-el-menu-sub-item-height));\r\n    line-height: 1;\r\n    padding-right: 40px;\r\n  }\r\n\r\n  .xyl-menu-text {\r\n    display: inline-block;\r\n    width: 100%;\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding: var(--zy-distance-four) 0;\r\n    text-overflow: clip;\r\n    white-space: normal;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAQLA,KAAK,EAAC;AAAe;;EAOrBA,KAAK,EAAC;AAAe;;EAMhCA,KAAK,EAAC;AAAe;;;;;;;;;;uBArB5BC,mBAAA,CA+BM,OA/BNC,UA+BM,GA9BJC,YAAA,CAmBeC,uBAAA;IArBnBC,OAAA,EAAAC,QAAA,CAGM;MAAA,OAiBU,CAjBVH,YAAA,CAiBUI,kBAAA;QAjBAP,KAAK,EAHrBQ,eAAA,mBAGyCC,MAAA,CAAAC,UAAU;QAA8CC,QAAQ,EAAEF,MAAA,CAAAC,UAAU;QAC5G,gBAAc,EAAED,MAAA,CAAAG,MAAM;QAAGC,QAAM,EAAEJ,MAAA,CAAAK,YAAY;QAAGC,MAAI,EAAEN,MAAA,CAAAO,UAAU;QAAEC,GAAG,EAAC;;QAJ/EZ,OAAA,EAAAC,QAAA,CAKkB;UAAA,OAAwB,E,kBAAlCL,mBAAA,CAcWiB,SAAA,QAnBnBC,WAAA,CAKiCV,MAAA,CAAAW,QAAQ,EALzC,UAKyBC,IAAI;iCAL7BpB,mBAAA,CAAAiB,SAAA,SAM4FG,IAAI,CAACC,QAAQ,CAACC,MAAM,I,cAAtGC,YAAA,CAMcC,sBAAA;cANAC,KAAK,EAAEL,IAAI,CAACM,EAAE;cAAGC,GAAG,EAAEP,IAAI,CAACM,EAAE;cAAE,cAAY,EAAC;;cAC7CE,KAAK,EAAAvB,QAAA,CACd;gBAAA,OAA6D,C,gBAA7DH,YAAA,CAA6D2B,mBAAA;kBAAnDC,GAAG,EAAC,OAAO;kBAAEC,GAAG,EAAEX,IAAI,CAACY;4DAAcxB,MAAA,CAAAC,UAAU,E,GACzDwB,mBAAA,CAAkD,QAAlDC,UAAkD,EAAAC,gBAAA,CAAnBf,IAAI,CAACgB,IAAI,iB;;cATtDhC,OAAA,EAAAC,QAAA,CAWY;gBAAA,OAAyD,CAAzDH,YAAA,CAAyDM,MAAA;kBAAzCW,QAAQ,EAAEC,IAAI,CAACC;;;cAX3CgB,CAAA;8DAAAC,mBAAA,gB,CAa+DlB,IAAI,CAACC,QAAQ,CAACC,MAAM,I,cAAzEC,YAAA,CAKegB,uBAAA;cALAd,KAAK,EAAEL,IAAI,CAACM,EAAE;cAAGC,GAAG,EAAEP,IAAI,CAACM;;cAE7BE,KAAK,EAAAvB,QAAA,CACd;gBAAA,OAAkD,CAAlD4B,mBAAA,CAAkD,QAAlDO,UAAkD,EAAAL,gBAAA,CAAnBf,IAAI,CAACgB,IAAI,iB;;cAhBtDhC,OAAA,EAAAC,QAAA,CAcY;gBAAA,OAA6D,C,gBAA7DH,YAAA,CAA6D2B,mBAAA;kBAAnDC,GAAG,EAAC,OAAO;kBAAEC,GAAG,EAAEX,IAAI,CAACY;4DAAcxB,MAAA,CAAAC,UAAU,E;;cAdrE4B,CAAA;8DAAAC,mBAAA,e;;;QAAAD,CAAA;;;IAAAA,CAAA;MAsBIJ,mBAAA,CASM,OATNQ,UASM,GARJR,mBAAA,CAOO;IAPAS,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEpC,MAAA,CAAAC,UAAU,IAAID,MAAA,CAAAC,UAAU;IAAA;MACrBD,MAAA,CAAAC,UAAU,I,cAAzBc,YAAA,CAEUsB,kBAAA;IA1BlBlB,GAAA;EAAA;IAAAvB,OAAA,EAAAC,QAAA,CAyBU;MAAA,OAAc,CAAdH,YAAA,CAAc4C,qBAAA,E;;IAzBxBT,CAAA;QAAAC,mBAAA,gB,CA2BwB9B,MAAA,CAAAC,UAAU,I,cAA1Bc,YAAA,CAEUsB,kBAAA;IA7BlBlB,GAAA;EAAA;IAAAvB,OAAA,EAAAC,QAAA,CA4BU;MAAA,OAAa,CAAbH,YAAA,CAAa6C,oBAAA,E;;IA5BvBV,CAAA;QAAAC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}