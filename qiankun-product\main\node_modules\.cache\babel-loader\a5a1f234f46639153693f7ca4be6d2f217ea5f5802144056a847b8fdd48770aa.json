{"ast": null, "code": "var __default__ = {\n  name: 'DetailsTitle'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    className: {\n      type: String,\n      default: 'left'\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var __returned__ = {\n      props\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["__default__", "name", "props", "__props"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/BackgroundCheck/components/DetailsTitle.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DetailsTitle\" :class=\"props.className\">\r\n    <div class=\"DetailsTitleIcon left\">\r\n      <el-icon>\r\n        <DArrowRight />\r\n      </el-icon>\r\n    </div>\r\n    <slot></slot>\r\n    <div class=\"DetailsTitleIcon right\">\r\n      <el-icon>\r\n        <DArrowRight />\r\n      </el-icon>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DetailsTitle' }\r\n</script>\r\n<script setup>\r\nconst props = defineProps({ className: { type: String, default: 'left' } })\r\n</script>\r\n<style lang=\"scss\">\r\n.DetailsTitle {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: var(--zy-distance-two) 0;\r\n  font-size: var(--zy-name-font-size);\r\n  position: relative;\r\n\r\n  &.left {\r\n    padding-left: 40px;\r\n\r\n    .DetailsTitleIcon.left {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      transform: translateY(-50%);\r\n    }\r\n\r\n    .DetailsTitleIcon.right {\r\n      display: none;\r\n    }\r\n  }\r\n\r\n  &.center {\r\n    justify-content: center;\r\n\r\n    .DetailsTitleIcon {\r\n      position: relative;\r\n    }\r\n\r\n    .DetailsTitleIcon.right {\r\n      transform: rotate(180deg);\r\n    }\r\n  }\r\n\r\n  .DetailsTitleIcon {\r\n    min-width: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding-left: 20px;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 2px;\r\n      width: 14px;\r\n      height: 14px;\r\n      border-radius: 2px;\r\n      transform: translateY(-50%) rotate(45deg);\r\n      background-color: var(--zy-el-color-primary-light-5);\r\n      z-index: 1;\r\n    }\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 8px;\r\n      width: 14px;\r\n      height: 14px;\r\n      border-radius: 2px;\r\n      transform: translateY(-50%) rotate(45deg);\r\n      background-color: var(--zy-el-color-primary);\r\n      z-index: 2;\r\n    }\r\n\r\n    .zy-el-icon {\r\n      font-size: var(--zy-name-font-size);\r\n      color: var(--zy-el-color-primary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAgBA,IAAAA,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAe,CAAC;;;;;;;;;;;IAGvC,IAAMC,KAAK,GAAGC,OAA6D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}