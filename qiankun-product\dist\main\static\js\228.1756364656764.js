"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[228],{40228:function(e,t,r){r.r(t),r.d(t,{default:function(){return S}});var n=r(81474),o=(r(76945),r(64352),r(62427)),a=(r(98773),r(44863)),i=(r(4711),r(71928)),u=(r(31584),r(49744)),l=(r(98326),r(15934),r(44917)),c=(r(40065),r(84098)),s=(r(63584),r(74061)),f=r(4955),d=r(59335),h=r(88609),v=r(42714),p=r(98885),m=(r(35894),r(50389),r(24652));function y(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */y=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var a=t&&t.prototype instanceof g?t:g,i=Object.create(a.prototype),u=new _(n||[]);return o(i,"_invoke",{value:L(e,r,u)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var d="suspendedStart",h="suspendedYield",v="executing",p="completed",m={};function g(){}function w(){}function b(){}var N={};c(N,i,(function(){return this}));var x=Object.getPrototypeOf,G=x&&x(x(D([])));G&&G!==r&&n.call(G,i)&&(N=G);var I=b.prototype=g.prototype=Object.create(N);function k(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function r(o,a,i,u){var l=f(e[o],e,a);if("throw"!==l.type){var c=l.arg,s=c.value;return s&&"object"==typeof s&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,i,u)}),(function(e){r("throw",e,i,u)})):t.resolve(s).then((function(e){c.value=e,i(c)}),(function(e){return r("throw",e,i,u)}))}u(l.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function L(t,r,n){var o=d;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===p){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var l=U(u,n);if(l){if(l===m)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=p,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=f(t,r,n);if("normal"===c.type){if(o=n.done?p:h,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=p,n.method="throw",n.arg=c.arg)}}}function U(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,U(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=f(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function V(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(V,this),this.reset(!0)}function D(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return w.prototype=b,o(I,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=c(b,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,l,"GeneratorFunction")),e.prototype=Object.create(I),e},t.awrap=function(e){return{__await:e}},k(E.prototype),c(E.prototype,u,(function(){return this})),t.AsyncIterator=E,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new E(s(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},k(I),c(I,l,"Generator"),c(I,i,(function(){return this})),c(I,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=D,_.prototype={constructor:_,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return u.type="throw",u.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),C(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:D(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function g(e,t,r,n,o,a,i){try{var u=e[a](i),l=u.value}catch(e){return void r(e)}u.done?t(l):Promise.resolve(l).then(n,o)}function w(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){g(a,n,o,i,u,"next",e)}function u(e){g(a,n,o,i,u,"throw",e)}i(void 0)}))}}var b={class:"GlobalGroupDelUser"},N={class:"GlobalGroupDelUserList"},x={class:"GlobalGroupDelUserInput"},G={class:"GlobalGroupDelUserItem"},I={class:"GlobalGroupDelUserName ellipsis"},k={class:"GlobalGroupDelUserBody"},E={class:"GlobalGroupDelUserInfo"},L={class:"GlobalGroupDelUserInfoName"},U={class:"GlobalGroupDelUserUserBody"},V=["onClick"],C={class:"GlobalGroupDelUserUserName ellipsis"},_={class:"GlobalGroupDelUserButton"},D={name:"GlobalGroupDelUser"},j=Object.assign(D,{props:{infoId:{type:String,default:""}},emits:["callback"],setup(e,t){var r=t.emit,g=(0,d.useStore)(),D=e,j=r,O=(0,s.computed)((function(){return g.getters.getRongCloudUrl})),S=(0,s.computed)((function(){return g.getters.getIsPrivatization})),B=(0,s.ref)({}),T=(0,s.ref)(""),$=(0,s.ref)(),P=(0,s.ref)([]),A=(0,s.ref)([]),F=(0,s.ref)([]),q=(0,s.ref)([]),R=function(e){return e?f.A.fileURL(e):f.A.defaultImgURL("default_user_head.jpg")},Q=function(){var e;null===(e=$.value)||void 0===e||e.filter(T.value)},z=function(e,t){var r;return!e||(null===(r=t.userName)||void 0===r||null===(r=r.toLowerCase())||void 0===r?void 0:r.includes(null===e||void 0===e?void 0:e.toLowerCase()))},M=function(e){var t;null!==(t=F.value)&&void 0!==t&&t.includes(e.accountId)?q.value.push(e):q.value=q.value.filter((function(t){return t.accountId!==e.accountId}))},Y=function(e){F.value=F.value.filter((function(t){return t!==e.accountId})),q.value=q.value.filter((function(t){return t.accountId!==e.accountId}))},K=function(){v.s.confirm(`此操作将移出当前选中的${F.value.length}位用户, 是否继续?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){W()})).catch((function(){(0,p.nk)({type:"info",message:"已取消移出"})}))},W=function(){var e=w(y().mark((function e(){var t,r,n;return y().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=B.value.memberUserIds.filter((function(e){var t;return!(null!==(t=F.value)&&void 0!==t&&t.includes(e))})),e.next=3,f.A.chatGroupEdit({form:{id:D.infoId,groupName:B.value.groupName},ownerUserId:B.value.ownerUserId,memberUserIds:t});case 3:r=e.sent,n=r.code,200===n&&Z();case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Z=function(){var e=w(y().mark((function e(){var t,r,n,o,a,i,u,l;return y().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.rongCloud(O.value,{type:"quitGroup",userIds:q.value.map((function(e){return`${h.TC.value}${e.id}`})).join(","),groupId:`${h.TC.value}${D.infoId}`,groupName:B.value.groupName,environment:1},S.value);case 2:t=e.sent,r=t.code,200===r&&(i=q.value.map((function(e){return e.userName})).join("、"),u=q.value.map((function(e){return`||${e.userName}|OUI|${e.accountId}||`})).join("、"),l={name:`${null===(n=h.kQ.value)||void 0===n?void 0:n.userName} 将 ${i} 移出群聊`,data:`${null===(o=h.kQ.value)||void 0===o?void 0:o.userName}|OUI|${null===(a=h.kQ.value)||void 0===a?void 0:a.accountId}|| 将 ${u} 移出群聊`},j("callback",!0,l));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),H=function(){j("callback",!1)},J=function(){var e=w(y().mark((function e(){var t,r;return y().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.chatGroupInfo({detailId:D.infoId});case 2:t=e.sent,r=t.data,B.value=r,A.value=[r.ownerUserId];case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),X=function(){var e=w(y().mark((function e(){var t,r;return y().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.chatGroupMemberList({pageNo:1,pageSize:9999,keyword:T.value,query:{chatGroupId:D.infoId}});case 2:t=e.sent,r=t.data,P.value=r;case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,s.onMounted)((function(){J(),X()})),function(e,t){var r=c.WK,f=l.Zq,d=i.dI,h=u.q,v=i.o5,p=a.kA,y=(0,s.resolveComponent)("CircleCloseFilled"),g=o.tk,w=n.S2;return(0,s.openBlock)(),(0,s.createElementBlock)("div",b,[(0,s.createElementVNode)("div",N,[(0,s.createElementVNode)("div",x,[(0,s.createVNode)(r,{modelValue:T.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return T.value=e}),"prefix-icon":(0,s.unref)(m.Search),placeholder:"搜索",onInput:Q,clearable:""},null,8,["modelValue","prefix-icon"])]),(0,s.createVNode)(p,{class:"GlobalGroupDelUserScrollbar"},{default:(0,s.withCtx)((function(){return[(0,s.createVNode)(v,{modelValue:F.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return F.value=e})},{default:(0,s.withCtx)((function(){return[(0,s.createVNode)(h,{ref_key:"treeRef",ref:$,"node-key":"accountId",data:P.value,"filter-node-method":z},{default:(0,s.withCtx)((function(e){var t,r=e.data;return[(0,s.createVNode)(d,{value:r.accountId,disabled:null===(t=A.value)||void 0===t?void 0:t.includes(r.accountId),onChange:function(e){return M(r)}},{default:(0,s.withCtx)((function(){return[(0,s.createElementVNode)("div",G,[(0,s.createVNode)(f,{src:R(r.photo||r.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,s.createElementVNode)("div",I,(0,s.toDisplayString)(r.userName),1)])]})),_:2},1032,["value","disabled","onChange"])]})),_:1},8,["data"])]})),_:1},8,["modelValue"])]})),_:1})]),(0,s.createElementVNode)("div",k,[(0,s.createElementVNode)("div",E,[(0,s.createElementVNode)("div",L,[t[2]||(t[2]=(0,s.createTextVNode)("移出成员 ")),(0,s.createElementVNode)("span",null,"已选择"+(0,s.toDisplayString)(q.value.length)+"位联系人",1)])]),(0,s.createVNode)(p,{class:"GlobalGroupDelUserUserScroll"},{default:(0,s.withCtx)((function(){return[(0,s.createElementVNode)("div",U,[((0,s.openBlock)(!0),(0,s.createElementBlock)(s.Fragment,null,(0,s.renderList)(q.value,(function(e){var t;return(0,s.openBlock)(),(0,s.createElementBlock)("div",{class:"GlobalGroupDelUserUser",key:e.accountId},[null!==(t=A.value)&&void 0!==t&&t.includes(e.accountId)?(0,s.createCommentVNode)("",!0):((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:0,class:"GlobalGroupDelUserUserDel",onClick:function(t){return Y(e)}},[(0,s.createVNode)(g,null,{default:(0,s.withCtx)((function(){return[(0,s.createVNode)(y)]})),_:1})],8,V)),(0,s.createVNode)(f,{src:R(e.photo||e.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,s.createElementVNode)("div",C,(0,s.toDisplayString)(e.userName),1)])})),128))])]})),_:1}),(0,s.createElementVNode)("div",_,[(0,s.createVNode)(w,{onClick:H},{default:(0,s.withCtx)((function(){return t[3]||(t[3]=[(0,s.createTextVNode)("取消")])})),_:1}),(0,s.createVNode)(w,{type:"primary",onClick:K,disabled:!q.value.length},{default:(0,s.withCtx)((function(){return t[4]||(t[4]=[(0,s.createTextVNode)("完成")])})),_:1},8,["disabled"])])])])}}});const O=j;var S=O}}]);