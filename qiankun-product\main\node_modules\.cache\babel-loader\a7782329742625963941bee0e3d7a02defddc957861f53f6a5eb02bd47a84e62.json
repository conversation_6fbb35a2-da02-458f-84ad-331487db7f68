{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AiReportGenera\"\n};\nvar _hoisted_2 = {\n  class: \"AiReportGeneraBody\"\n};\nvar _hoisted_3 = {\n  class: \"AiReportGeneraTitle\"\n};\nvar _hoisted_4 = {\n  class: \"AiReportGeneraReportBody\"\n};\nvar _hoisted_5 = [\"onClick\"];\nvar _hoisted_6 = {\n  class: \"AiReportGeneraReportIcon\"\n};\nvar _hoisted_7 = {\n  class: \"AiReportGeneraReportInfo\"\n};\nvar _hoisted_8 = {\n  class: \"AiReportGeneraReportName\"\n};\nvar _hoisted_9 = {\n  class: \"AiReportGeneraReportTime\"\n};\nvar _hoisted_10 = {\n  class: \"AiReportGeneraItemList\"\n};\nvar _hoisted_11 = [\"onClick\"];\nvar _hoisted_12 = [\"innerHTML\"];\nvar _hoisted_13 = {\n  class: \"AiReportGeneraName\"\n};\nvar _hoisted_14 = {\n  class: \"AiReportGeneraEditorBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_ArrowRight = _resolveComponent(\"ArrowRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_Document = _resolveComponent(\"Document\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[3] || (_cache[3] = _createTextVNode(\" 我的生成报告 \")), _createElementVNode(\"span\", {\n    onClick: $setup.handleGeneraList\n  }, [_cache[2] || (_cache[2] = _createTextVNode(\" 查看全部 \")), _createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_ArrowRight)];\n    }),\n    _: 1 /* STABLE */\n  })])]), _createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.reportData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"AiReportGeneraReportItem\",\n      key: item.id,\n      onClick: function onClick($event) {\n        return $setup.handleView(item);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(function () {\n        return [_createVNode(_component_Document)];\n      }),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString(item.reportTypeName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.format(item.createDate)), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_5);\n  }), 128 /* KEYED_FRAGMENT */))]), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"AiReportGeneraTitle\"\n  }, \"文档撰写类型\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_10, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.toolData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"AiReportGeneraItem\", {\n        'is-active': $setup.toolId === item.chatToolCode\n      }]),\n      key: item.chatToolCode,\n      onClick: function onClick($event) {\n        return $setup.handleTool(item);\n      }\n    }, [_createElementVNode(\"div\", {\n      class: \"AiReportGeneraIcon\",\n      innerHTML: $setup.toolIconData[item.chatToolCode]\n    }, null, 8 /* PROPS */, _hoisted_12), _createElementVNode(\"div\", _hoisted_13, _toDisplayString(item.chatToolName), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_11);\n  }), 128 /* KEYED_FRAGMENT */)), (_openBlock(), _createElementBlock(_Fragment, null, _renderList(2, function (item) {\n    return _createElementVNode(\"div\", {\n      class: \"AiReportGeneraPlaceholder\",\n      key: item + '_placeholder'\n    });\n  }), 64 /* STABLE_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_14, [_withDirectives(_createVNode($setup[\"GlobalAiChatFile\"], {\n    fileList: $setup.fileList,\n    fileData: $setup.fileData,\n    onClose: $setup.handleClose\n  }, null, 8 /* PROPS */, [\"fileList\", \"fileData\"]), [[_vShow, $setup.fileList.length || $setup.fileData.length]]), _createVNode($setup[\"GlobalAiChatEditor\"], {\n    ref: \"editorRef\",\n    modelValue: $setup.sendContent,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.sendContent = $event;\n    }),\n    onSend: $setup.handleSendMessage,\n    onUploadCallback: $setup.handleFileUpload,\n    onFileCallback: $setup.handleFileCallback\n  }, null, 8 /* PROPS */, [\"modelValue\"])])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"报告详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"AiReportGeneraDetails\"], {\n        id: $setup.id\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createTextVNode", "onClick", "$setup", "handleGeneraList", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_ArrowRight", "_", "_hoisted_4", "_Fragment", "_renderList", "reportData", "item", "key", "id", "$event", "handleView", "_hoisted_6", "_component_Document", "_hoisted_7", "_hoisted_8", "_toDisplayString", "reportTypeName", "_hoisted_9", "format", "createDate", "_hoisted_5", "_hoisted_10", "toolData", "_normalizeClass", "toolId", "chatToolCode", "handleTool", "innerHTML", "toolIconData", "_hoisted_12", "_hoisted_13", "chatToolName", "_hoisted_11", "_hoisted_14", "fileList", "fileData", "onClose", "handleClose", "length", "ref", "modelValue", "send<PERSON><PERSON><PERSON>", "_cache", "onSend", "handleSendMessage", "onUploadCallback", "handleFileUpload", "onFileCallback", "handleFileCallback", "_component_xyl_popup_window", "show", "name"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiReportGenera\\AiReportGenera.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AiReportGenera\">\r\n    <div class=\"AiReportGeneraBody\">\r\n      <div class=\"AiReportGeneraTitle\">\r\n        我的生成报告\r\n        <span @click=\"handleGeneraList\">\r\n          查看全部\r\n          <el-icon>\r\n            <ArrowRight />\r\n          </el-icon>\r\n        </span>\r\n      </div>\r\n      <div class=\"AiReportGeneraReportBody\">\r\n        <div class=\"AiReportGeneraReportItem\" v-for=\"item in reportData\" :key=\"item.id\" @click=\"handleView(item)\">\r\n          <div class=\"AiReportGeneraReportIcon\">\r\n            <el-icon>\r\n              <Document />\r\n            </el-icon>\r\n          </div>\r\n          <div class=\"AiReportGeneraReportInfo\">\r\n            <div class=\"AiReportGeneraReportName\">{{ item.reportTypeName }}</div>\r\n            <div class=\"AiReportGeneraReportTime\">{{ format(item.createDate) }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiReportGeneraTitle\">文档撰写类型</div>\r\n      <div class=\"AiReportGeneraItemList\">\r\n        <div\r\n          class=\"AiReportGeneraItem\"\r\n          :class=\"{ 'is-active': toolId === item.chatToolCode }\"\r\n          v-for=\"item in toolData\"\r\n          :key=\"item.chatToolCode\"\r\n          @click=\"handleTool(item)\">\r\n          <div class=\"AiReportGeneraIcon\" v-html=\"toolIconData[item.chatToolCode]\"></div>\r\n          <div class=\"AiReportGeneraName\">{{ item.chatToolName }}</div>\r\n        </div>\r\n        <div class=\"AiReportGeneraPlaceholder\" v-for=\"item in 2\" :key=\"item + '_placeholder'\"></div>\r\n      </div>\r\n      <div class=\"AiReportGeneraEditorBody\">\r\n        <GlobalAiChatFile\r\n          :fileList=\"fileList\"\r\n          :fileData=\"fileData\"\r\n          @close=\"handleClose\"\r\n          v-show=\"fileList.length || fileData.length\" />\r\n        <GlobalAiChatEditor\r\n          ref=\"editorRef\"\r\n          v-model=\"sendContent\"\r\n          @send=\"handleSendMessage\"\r\n          @uploadCallback=\"handleFileUpload\"\r\n          @fileCallback=\"handleFileCallback\" />\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"报告详情\">\r\n      <AiReportGeneraDetails :id=\"id\"></AiReportGeneraDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AiReportGenera' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nimport { openConfig } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue'\r\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue'\r\nimport AiReportGeneraDetails from './component/AiReportGeneraDetails'\r\nconst store = useStore()\r\nconst IssueAnalysisIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#232;&#174;&#174;&#233;&#162;&#152;&#229;&#136;&#134;&#230;&#158;&#144;&#230;&#138;&#165;&#229;&#145;&#138;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_505)\"/><g id=\"Vector\"><path d=\"M38.5453 30.4309H20.864H12.823H10.4115C9.77195 30.4309 9.15857 30.685 8.70632 31.1373C8.25407 31.5895 8 32.2029 8 32.8425V36.0578C8 36.3745 8.06238 36.688 8.18358 36.9806C8.30478 37.2731 8.48242 37.5389 8.70636 37.7628C8.9303 37.9867 9.19614 38.1642 9.48872 38.2854C9.78129 38.4065 10.0949 38.4688 10.4115 38.4687H38.5453C38.8505 38.4686 39.1493 38.3817 39.4069 38.218C39.6645 38.0544 39.8702 37.8208 40 37.5445C39.5449 37.1674 39.1786 36.6945 38.9271 36.1596C38.6757 35.6247 38.5453 35.0409 38.5453 34.4498C38.5453 33.8588 38.6757 33.275 38.9271 32.74C39.1786 32.2051 39.5449 31.7322 40 31.3551C39.8702 31.0789 39.6645 30.8453 39.4069 30.6816C39.1493 30.518 38.8505 30.431 38.5453 30.4309Z\" fill=\"white\"/><path d=\"M38.5453 19.981H20.864H12.823H10.4115C9.77195 19.981 9.15857 20.2351 8.70632 20.6873C8.25407 21.1396 8 21.753 8 22.3925V25.6079C8 26.2475 8.25407 26.8609 8.70632 27.3131C9.15857 27.7653 9.77195 28.0194 10.4115 28.0194H12.823H20.864H38.5453C38.8505 28.0193 39.1493 27.9324 39.4069 27.7687C39.6645 27.6051 39.8702 27.3715 40 27.0953C39.5449 26.7181 39.1785 26.2451 38.9271 25.7102C38.6756 25.1752 38.5453 24.5913 38.5453 24.0002C38.5453 23.4091 38.6756 22.8253 38.9271 22.2903C39.1785 21.7553 39.5449 21.2824 40 20.9052C39.8702 20.6289 39.6645 20.3954 39.4069 20.2317C39.1493 20.068 38.8505 19.9811 38.5453 19.981Z\" fill=\"white\"/><path d=\"M10.4115 17.5695H12.823H20.864H38.5453C38.8505 17.5694 39.1493 17.4825 39.4069 17.3188C39.6645 17.1552 39.8702 16.9216 40 16.6453C39.5449 16.2682 39.1786 15.7953 38.9271 15.2604C38.6757 14.7255 38.5453 14.1417 38.5453 13.5506C38.5453 12.9595 38.6757 12.3758 38.9271 11.8408C39.1786 11.3059 39.5449 10.833 40 10.4559C39.8702 10.1797 39.6645 9.94607 39.4069 9.78242C39.1493 9.61877 38.8505 9.53182 38.5453 9.53174H10.4115C10.0949 9.53165 9.78129 9.59395 9.48872 9.71507C9.19614 9.83619 8.9303 10.0138 8.70636 10.2376C8.48242 10.4615 8.30478 10.7273 8.18358 11.0199C8.06238 11.3124 8 11.626 8 11.9426V15.158C8 15.7976 8.25407 16.4109 8.70632 16.8632C9.15857 17.3154 9.77195 17.5695 10.4115 17.5695Z\" fill=\"white\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 23.8C25 23.3582 25.3582 23 25.8 23H33.8C34.2418 23 34.6 23.3582 34.6 23.8C34.6 24.2418 34.2418 24.6 33.8 24.6H25.8C25.3582 24.6 25 24.2418 25 23.8Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 34.8C25 34.3582 25.3582 34 25.8 34H33.8C34.2418 34 34.6 34.3582 34.6 34.8C34.6 35.2418 34.2418 35.6 33.8 35.6H25.8C25.3582 35.6 25 35.2418 25 34.8Z\" fill=\"#0964E3\"/><path id=\"Vector_2\" d=\"M18.75 11.2C18.75 11.0895 18.6605 11 18.55 11H12.2C12.0895 11 12 11.0895 12 11.2V22.555C12 22.7275 12.2039 22.8191 12.3329 22.7044L15.2421 20.1181C15.3179 20.0508 15.4321 20.0508 15.5079 20.1181L18.4171 22.7044C18.5461 22.8191 18.75 22.7275 18.75 22.555V11.2Z\" fill=\"url(#paint1_linear_106_505)\"/></g><defs><linearGradient id=\"paint0_linear_106_505\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_505\" x1=\"15.375\" y1=\"11\" x2=\"15.375\" y2=\"23.0004\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient></defs></svg>'\r\nconst IssueAnalysisIconRD =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#232;&#174;&#174;&#233;&#162;&#152;&#229;&#136;&#134;&#230;&#158;&#144;&#230;&#138;&#165;&#229;&#145;&#138;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_505)\"/><g id=\"Vector\"><path d=\"M38.5453 30.4309H20.864H12.823H10.4115C9.77195 30.4309 9.15857 30.685 8.70632 31.1373C8.25407 31.5895 8 32.2029 8 32.8425V36.0578C8 36.3745 8.06238 36.688 8.18358 36.9806C8.30478 37.2731 8.48242 37.5389 8.70636 37.7628C8.9303 37.9867 9.19614 38.1642 9.48872 38.2854C9.78129 38.4065 10.0949 38.4688 10.4115 38.4687H38.5453C38.8505 38.4686 39.1493 38.3817 39.4069 38.218C39.6645 38.0544 39.8702 37.8208 40 37.5445C39.5449 37.1674 39.1786 36.6945 38.9271 36.1596C38.6757 35.6247 38.5453 35.0409 38.5453 34.4498C38.5453 33.8588 38.6757 33.275 38.9271 32.74C39.1786 32.2051 39.5449 31.7322 40 31.3551C39.8702 31.0789 39.6645 30.8453 39.4069 30.6816C39.1493 30.518 38.8505 30.431 38.5453 30.4309Z\" fill=\"white\"/><path d=\"M38.5453 19.981H20.864H12.823H10.4115C9.77195 19.981 9.15857 20.2351 8.70632 20.6873C8.25407 21.1396 8 21.753 8 22.3925V25.6079C8 26.2475 8.25407 26.8609 8.70632 27.3131C9.15857 27.7653 9.77195 28.0194 10.4115 28.0194H12.823H20.864H38.5453C38.8505 28.0193 39.1493 27.9324 39.4069 27.7687C39.6645 27.6051 39.8702 27.3715 40 27.0953C39.5449 26.7181 39.1785 26.2451 38.9271 25.7102C38.6756 25.1752 38.5453 24.5913 38.5453 24.0002C38.5453 23.4091 38.6756 22.8253 38.9271 22.2903C39.1785 21.7553 39.5449 21.2824 40 20.9052C39.8702 20.6289 39.6645 20.3954 39.4069 20.2317C39.1493 20.068 38.8505 19.9811 38.5453 19.981Z\" fill=\"white\"/><path d=\"M10.4115 17.5695H12.823H20.864H38.5453C38.8505 17.5694 39.1493 17.4825 39.4069 17.3188C39.6645 17.1552 39.8702 16.9216 40 16.6453C39.5449 16.2682 39.1786 15.7953 38.9271 15.2604C38.6757 14.7255 38.5453 14.1417 38.5453 13.5506C38.5453 12.9595 38.6757 12.3758 38.9271 11.8408C39.1786 11.3059 39.5449 10.833 40 10.4559C39.8702 10.1797 39.6645 9.94607 39.4069 9.78242C39.1493 9.61877 38.8505 9.53182 38.5453 9.53174H10.4115C10.0949 9.53165 9.78129 9.59395 9.48872 9.71507C9.19614 9.83619 8.9303 10.0138 8.70636 10.2376C8.48242 10.4615 8.30478 10.7273 8.18358 11.0199C8.06238 11.3124 8 11.626 8 11.9426V15.158C8 15.7976 8.25407 16.4109 8.70632 16.8632C9.15857 17.3154 9.77195 17.5695 10.4115 17.5695Z\" fill=\"white\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 23.8C25 23.3582 25.3582 23 25.8 23H33.8C34.2418 23 34.6 23.3582 34.6 23.8C34.6 24.2418 34.2418 24.6 33.8 24.6H25.8C25.3582 24.6 25 24.2418 25 23.8Z\" fill=\"#F54747\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 34.8C25 34.3582 25.3582 34 25.8 34H33.8C34.2418 34 34.6 34.3582 34.6 34.8C34.6 35.2418 34.2418 35.6 33.8 35.6H25.8C25.3582 35.6 25 35.2418 25 34.8Z\" fill=\"#F54747\"/><path id=\"Vector_2\" d=\"M18.75 11.2C18.75 11.0895 18.6605 11 18.55 11H12.2C12.0895 11 12 11.0895 12 11.2V22.555C12 22.7275 12.2039 22.8191 12.3329 22.7044L15.2421 20.1181C15.3179 20.0508 15.4321 20.0508 15.5079 20.1181L18.4171 22.7044C18.5461 22.8191 18.75 22.7275 18.75 22.555V11.2Z\" fill=\"url(#paint1_linear_106_505)\"/></g><defs><linearGradient id=\"paint0_linear_106_505\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F54747\"/><stop offset=\"0.995\" stop-color=\"#F54747\"/></linearGradient><linearGradient id=\"paint1_linear_106_505\" x1=\"15.375\" y1=\"11\" x2=\"15.375\" y2=\"23.0004\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F54747\"/><stop offset=\"1\" stop-color=\"#F54747\"/></linearGradient></defs></svg>'\r\nconst MeetingRoomIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#229;&#176;&#143;&#231;&#187;&#147;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_745)\"/><g id=\"&#231;&#187;&#132; 2771\"><g id=\"&#231;&#187;&#132; 2769\"><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1808\" d=\"M31.4467 41.261C31.0716 41.636 30.563 41.8467 30.0326 41.8467L12.7926 41.8467C12.0103 41.8467 11.26 41.5359 10.7068 40.9827C10.1536 40.4295 9.84277 39.6792 9.84277 38.8968L9.84278 10.1667C9.84278 9.38431 10.1536 8.634 10.7068 8.08079C11.26 7.52758 12.0103 7.2168 12.7926 7.2168L35.2076 7.2168C35.9899 7.2168 36.7402 7.52759 37.2934 8.0808C37.8467 8.634 38.1574 9.38432 38.1574 10.1667L38.1574 33.7231C38.1574 34.2537 37.9467 34.7624 37.5715 35.1375L31.4467 41.261Z\" fill=\"white\"/><rect id=\"Rectangle 34625139\" x=\"15\" y=\"30\" width=\"11\" height=\"4\" rx=\"1\" fill=\"url(#paint1_linear_106_745)\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 362 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.0259 16.8C15.0259 16.3582 15.3841 16 15.8259 16H32.1747C32.6165 16 32.9747 16.3582 32.9747 16.8C32.9747 17.2418 32.6165 17.6 32.1747 17.6H15.8259C15.3841 17.6 15.0259 17.2418 15.0259 16.8Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 362 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.0259 21.8C15.0259 21.3582 15.3841 21 15.8259 21H32.1747C32.6165 21 32.9747 21.3582 32.9747 21.8C32.9747 22.2418 32.6165 22.6 32.1747 22.6H15.8259C15.3841 22.6 15.0259 22.2418 15.0259 21.8Z\" fill=\"#0964E3\"/></g></g><defs><linearGradient id=\"paint0_linear_106_745\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_745\" x1=\"20.5\" y1=\"30\" x2=\"20.5\" y2=\"34\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient></defs></svg>'\r\nconst HotPointIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#231;&#131;&#173;&#231;&#130;&#185;&#230;&#138;&#165;&#229;&#145;&#138;\" clip-path=\"url(#clip0_106_531)\"><g id=\"&#230;&#148;&#191;&#231;&#173;&#150;&#230;&#150;&#135;&#228;&#187;&#182;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 7578\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_531)\"/><g id=\"Group 1000006979\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6277\" d=\"M8.25 11.9333L8.25 34.8667C8.25 36.0449 9.20513 37 10.3833 37L37.5833 37C38.7615 37 39.7167 36.0449 39.7167 34.8667L39.7167 11.9333C39.7167 10.7551 38.7615 9.8 37.5833 9.8L10.3833 9.8C9.20512 9.8 8.25 10.7551 8.25 11.9333Z\" fill=\"white\"/><path id=\"Vector\" d=\"M30.5203 15.8245L29.4187 15.8317C29.2395 15.8229 29.0704 15.7458 28.9461 15.6164C28.8218 15.4869 28.7516 15.3148 28.7501 15.1353C28.7486 14.9559 28.8157 14.7826 28.9378 14.651C29.0599 14.5195 29.2277 14.4395 29.4067 14.4277L32.2159 14.4073C32.3561 14.4056 32.4936 14.4464 32.6101 14.5244C32.7267 14.6023 32.8168 14.7138 32.8687 14.8441C32.9035 14.9277 32.9214 15.0174 32.9215 15.1081V17.9317C32.9219 18.1176 32.8484 18.2961 32.7172 18.428C32.586 18.5598 32.4079 18.6342 32.2219 18.6349C32.036 18.6342 31.8578 18.5598 31.7267 18.428C31.5955 18.2961 31.522 18.1176 31.5223 17.9317V16.8061L25.2523 23.1001C25.1524 23.2006 25.0242 23.2683 24.8848 23.294C24.7455 23.3198 24.6015 23.3023 24.4723 23.2441L16.0747 19.5001C15.9049 19.4235 15.7723 19.2829 15.7057 19.1089C15.6392 18.9349 15.6442 18.7416 15.7195 18.5713C15.7566 18.487 15.81 18.4109 15.8765 18.3473C15.9431 18.2838 16.0215 18.2339 16.1073 18.2007C16.1932 18.1675 16.2847 18.1515 16.3767 18.1538C16.4687 18.156 16.5594 18.1763 16.6435 18.2137L24.6019 21.7657L30.5215 15.8245H30.5203Z\" fill=\"#0964E3\"/><path id=\"Vector_2\" d=\"M32.2219 21.2006C32.5938 21.2016 32.9502 21.3501 33.2128 21.6136C33.4753 21.877 33.6226 22.2339 33.6223 22.6059V31.9742C33.6229 32.3464 33.4758 32.7036 33.2132 32.9673C32.9506 33.231 32.594 33.3797 32.2219 33.3806H30.3559C29.9837 33.3797 29.6272 33.231 29.3646 32.9673C29.102 32.7036 28.9549 32.3464 28.9555 31.9742V22.6046C28.9549 22.2325 29.102 21.8753 29.3646 21.6116C29.6272 21.3479 29.9837 21.1992 30.3559 21.1982H32.2219V21.2006ZM19.1575 23.0726C19.3419 23.0731 19.5244 23.1099 19.6945 23.1809C19.8647 23.2519 20.0192 23.3557 20.1492 23.4864C20.2793 23.6171 20.3823 23.7722 20.4524 23.9427C20.5225 24.1132 20.5584 24.2959 20.5579 24.4802V31.973C20.5585 32.3452 20.4114 32.7024 20.1488 32.9661C19.8862 33.2298 19.5297 33.3785 19.1575 33.3794H17.2915C16.9194 33.3785 16.5628 33.2298 16.3002 32.9661C16.0376 32.7024 15.8905 32.3452 15.8911 31.973V24.479C15.8905 24.1069 16.0376 23.7497 16.3002 23.486C16.5628 23.2223 16.9193 23.0736 17.2915 23.0726H19.1575ZM25.6891 24.9459C26.0612 24.9468 26.4178 25.0955 26.6804 25.3592C26.943 25.6229 27.0901 25.9801 27.0895 26.3522V31.9719C27.0901 32.344 26.943 32.7012 26.6804 32.9649C26.4178 33.2286 26.0612 33.3773 25.6891 33.3782H23.8243C23.4519 33.3776 23.0951 33.2291 22.8322 32.9653C22.5694 32.7016 22.4221 32.3442 22.4227 31.9719V26.3522C22.4221 25.9799 22.5694 25.6225 22.8322 25.3588C23.0951 25.095 23.4519 24.9465 23.8243 24.9459H25.6891Z\" fill=\"url(#paint1_linear_106_531)\"/></g></g></g><defs><linearGradient id=\"paint0_linear_106_531\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_531\" x1=\"24.7567\" y1=\"21.1982\" x2=\"24.7567\" y2=\"33.3806\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_106_531\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>'\r\nconst reportData = ref([])\r\nconst toolId = ref('')\r\nconst toolInfo = ref({})\r\nconst toolData = ref([])\r\nconst toolIconData = {\r\n  issue_analysis: IssueAnalysisIcon,\r\n  'meeting-room': MeetingRoomIcon,\r\n  'hot-point': HotPointIcon,\r\n  letter_chat_sum: IssueAnalysisIconRD\r\n}\r\nconst editorRef = ref()\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst sendContent = ref('')\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleTips = (text) => {\r\n  const parts = text.split(/(\\{[^}]+\\})/)\r\n  const result = parts\r\n    .map((part) => {\r\n      if (part.startsWith('{') && part.endsWith('}')) {\r\n        return { value: part.slice(1, -1), type: true }\r\n      } else if (part.trim() !== '') {\r\n        return { value: part, type: false }\r\n      }\r\n    })\r\n    .filter((item) => item !== undefined)\r\n  return result\r\n}\r\nconst handleTool = (item) => {\r\n  toolInfo.value = item\r\n  toolId.value = item.chatToolCode\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n  nextTick(() => {\r\n    editorRef.value?.handleInsertPlaceholder(handleTips(item.userPromptTip))\r\n  })\r\n}\r\nconst handleGeneraList = () => {\r\n  store.commit('setOpenRoute', { name: '我生成的报告', path: '/AiReportGeneraList', query: {} })\r\n}\r\nconst handleView = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst aiScheduleExists = async (value) => {\r\n  const { data } = await api.aiScheduleExists({ question: value })\r\n  if (!data) return ElMessage({ type: 'warning', message: '当前日期没有委员会客，请更改日期！' })\r\n  store.commit('setOpenRoute', {\r\n    name: '会客室小结',\r\n    path: '/AiReportGeneraView',\r\n    query: { type: 'meeting-room', AiChatCode: 'ai-meeting-room-report' }\r\n  })\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!toolId.value) return ElMessage({ type: 'warning', message: '请先选择文档撰写类型！' })\r\n  const openAiParams = {\r\n    toolId: toolInfo.value.id,\r\n    toolCode: toolId.value,\r\n    toolContent: value,\r\n    fileData: fileData.value\r\n  }\r\n  sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams))\r\n  if (toolId.value === 'issue_analysis') {\r\n    store.commit('setOpenRoute', {\r\n      name: '议题分析报告',\r\n      path: '/AiReportGeneraView',\r\n      query: { type: 'issue_analysis', AiChatCode: 'ai-issue_analysis' }\r\n    })\r\n  }\r\n  if (toolId.value === 'hot-point') {\r\n    store.commit('setOpenRoute', {\r\n      name: '数据热点分析报告',\r\n      path: '/AiReportGeneraView',\r\n      query: { type: 'hot-point', AiChatCode: 'ai-hot-point-chat' }\r\n    })\r\n  }\r\n  if (toolId.value === 'letter_chat_sum') {\r\n    store.commit('setOpenRoute', {\r\n      name: '联络站留言分析报告',\r\n      path: '/AiReportGeneraView',\r\n      query: { type: 'letter_chat_sum', AiChatCode: 'letter_chat_sum' }\r\n    })\r\n  }\r\n  if (toolId.value === 'meeting-room') aiScheduleExists(value)\r\n  editorRef.value?.handleSetFile([])\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const chatSceneCode = openConfig.value?.systemPlatform === 'CPPCC' ? 'ai-general-report-main' : 'letter_chat_sum'\r\n  const { data } = await api.aigptChatSceneDetail({ query: { chatSceneCode } })\r\n  toolData.value = data?.tools?.filter((v) => v.isUsing) || []\r\n}\r\nconst aigptReportRecordList = async () => {\r\n  const { data } = await api.aigptReportRecordList({ pageNo: 1, pageSize: 4 })\r\n  reportData.value = data\r\n}\r\nonActivated(() => {\r\n  aigptReportRecordList()\r\n  aigptChatSceneDetail()\r\n  store.commit('setAiChatElShow', false)\r\n})\r\nonDeactivated(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\nonUnmounted(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.AiReportGenera {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  position: relative;\r\n\r\n  .AiReportGeneraBody {\r\n    padding: var(--zy-distance-two);\r\n  }\r\n\r\n  .AiReportGeneraTitle {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    font-weight: bold;\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-name-font-size);\r\n    padding: 12px 0;\r\n\r\n    span {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      font-weight: normal;\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-text-color-secondary);\r\n      cursor: pointer;\r\n\r\n      .zy-el-icon {\r\n        font-size: 16px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .AiReportGeneraReportBody {\r\n    width: 800px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .AiReportGeneraReportItem {\r\n      width: 388px;\r\n      height: 60px;\r\n      border-radius: 10px;\r\n      background: var(--zy-el-color-info-light-9);\r\n      margin-bottom: 16px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding-right: var(--zy-distance-two);\r\n      cursor: pointer;\r\n\r\n      .AiReportGeneraReportIcon {\r\n        width: 52px;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding-left: 6px;\r\n\r\n        .zy-el-icon {\r\n          font-size: 32px;\r\n          color: var(--zy-el-text-color-secondary);\r\n        }\r\n      }\r\n\r\n      .AiReportGeneraReportInfo {\r\n        width: calc(100% - 52px);\r\n\r\n        .AiReportGeneraReportName {\r\n          font-weight: bold;\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n\r\n        .AiReportGeneraReportTime {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          color: var(--zy-el-text-color-secondary);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .AiReportGeneraItemList {\r\n    width: 800px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .AiReportGeneraPlaceholder {\r\n      width: 185px;\r\n      height: 126px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .AiReportGeneraItem {\r\n      // width: 185px;\r\n      width: 190px;\r\n      height: 126px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      border-radius: 6px 6px 6px 6px;\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n      padding: 20px;\r\n      margin-bottom: 20px;\r\n      cursor: pointer;\r\n\r\n      &.is-active {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AiReportGeneraName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AiReportGeneraName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .AiReportGeneraIcon {\r\n        width: 48px;\r\n        height: 48px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n\r\n      .AiReportGeneraName {\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n    }\r\n  }\r\n\r\n  .AiReportGeneraEditorBody {\r\n    width: 800px;\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: 20px;\r\n    transform: translateX(-50%);\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalAiChatEditorContainer {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAqB;;EAS3BA,KAAK,EAAC;AAA0B;iBAZ3C;;EAceA,KAAK,EAAC;AAA0B;;EAKhCA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA0B;;EAChCA,KAAK,EAAC;AAA0B;;EAKtCA,KAAK,EAAC;AAAwB;kBA1BzC;kBAAA;;EAkCeA,KAAK,EAAC;AAAoB;;EAI9BA,KAAK,EAAC;AAA0B;;;;;;uBArCzCC,mBAAA,CAsDM,OAtDNC,UAsDM,GArDJC,mBAAA,CAiDM,OAjDNC,UAiDM,GAhDJD,mBAAA,CAQM,OARNE,UAQM,G,0BAXZC,gBAAA,CAGuC,UAE/B,IAAAH,mBAAA,CAKO;IALAI,OAAK,EAAEC,MAAA,CAAAC;EAAgB,I,0BALtCH,gBAAA,CAKwC,QAE9B,IAAAI,YAAA,CAEUC,kBAAA;IATpBC,OAAA,EAAAC,QAAA,CAQY;MAAA,OAAc,CAAdH,YAAA,CAAcI,qBAAA,E;;IAR1BC,CAAA;UAYMZ,mBAAA,CAYM,OAZNa,UAYM,I,kBAXJf,mBAAA,CAUMgB,SAAA,QAvBdC,WAAA,CAa6DV,MAAA,CAAAW,UAAU,EAbvE,UAaqDC,IAAI;yBAAjDnB,mBAAA,CAUM;MAVDD,KAAK,EAAC,0BAA0B;MAA6BqB,GAAG,EAAED,IAAI,CAACE,EAAE;MAAGf,OAAK,WAALA,OAAKA,CAAAgB,MAAA;QAAA,OAAEf,MAAA,CAAAgB,UAAU,CAACJ,IAAI;MAAA;QACrGjB,mBAAA,CAIM,OAJNsB,UAIM,GAHJf,YAAA,CAEUC,kBAAA;MAjBtBC,OAAA,EAAAC,QAAA,CAgBc;QAAA,OAAY,CAAZH,YAAA,CAAYgB,mBAAA,E;;MAhB1BX,CAAA;UAmBUZ,mBAAA,CAGM,OAHNwB,UAGM,GAFJxB,mBAAA,CAAqE,OAArEyB,UAAqE,EAAAC,gBAAA,CAA5BT,IAAI,CAACU,cAAc,kBAC5D3B,mBAAA,CAAyE,OAAzE4B,UAAyE,EAAAF,gBAAA,CAAhCrB,MAAA,CAAAwB,MAAM,CAACZ,IAAI,CAACa,UAAU,kB,mBArB3EC,UAAA;8DAyBM/B,mBAAA,CAA6C;IAAxCH,KAAK,EAAC;EAAqB,GAAC,QAAM,sBACvCG,mBAAA,CAWM,OAXNgC,WAWM,I,kBAVJlC,mBAAA,CAQMgB,SAAA,QAnCdC,WAAA,CA8ByBV,MAAA,CAAA4B,QAAQ,EA9BjC,UA8BiBhB,IAAI;yBAHbnB,mBAAA,CAQM;MAPJD,KAAK,EA5BfqC,eAAA,EA4BgB,oBAAoB;QAAA,aACH7B,MAAA,CAAA8B,MAAM,KAAKlB,IAAI,CAACmB;MAAY;MAElDlB,GAAG,EAAED,IAAI,CAACmB,YAAY;MACtBhC,OAAK,WAALA,OAAKA,CAAAgB,MAAA;QAAA,OAAEf,MAAA,CAAAgC,UAAU,CAACpB,IAAI;MAAA;QACvBjB,mBAAA,CAA+E;MAA1EH,KAAK,EAAC,oBAAoB;MAACyC,SAAwC,EAAhCjC,MAAA,CAAAkC,YAAY,CAACtB,IAAI,CAACmB,YAAY;4BAjChFI,WAAA,GAkCUxC,mBAAA,CAA6D,OAA7DyC,WAA6D,EAAAf,gBAAA,CAA1BT,IAAI,CAACyB,YAAY,iB,yBAlC9DC,WAAA;iDAoCQ7C,mBAAA,CAA4FgB,SAAA,QApCpGC,WAAA,CAoC8D,CAAC,EApC/D,UAoCsDE,IAAI;WAAlDjB,mBAAA,CAA4F;MAAvFH,KAAK,EAAC,2BAA2B;MAAoBqB,GAAG,EAAED,IAAI;;oCAErEjB,mBAAA,CAYM,OAZN4C,WAYM,G,gBAXJrC,YAAA,CAIgDF,MAAA;IAH7CwC,QAAQ,EAAExC,MAAA,CAAAwC,QAAQ;IAClBC,QAAQ,EAAEzC,MAAA,CAAAyC,QAAQ;IAClBC,OAAK,EAAE1C,MAAA,CAAA2C;+DACA3C,MAAA,CAAAwC,QAAQ,CAACI,MAAM,IAAI5C,MAAA,CAAAyC,QAAQ,CAACG,MAAM,E,GAC5C1C,YAAA,CAKuCF,MAAA;IAJrC6C,GAAG,EAAC,WAAW;IA7CzBC,UAAA,EA8CmB9C,MAAA,CAAA+C,WAAW;IA9C9B,uBAAAC,MAAA,QAAAA,MAAA,gBAAAjC,MAAA;MAAA,OA8CmBf,MAAA,CAAA+C,WAAW,GAAAhC,MAAA;IAAA;IACnBkC,MAAI,EAAEjD,MAAA,CAAAkD,iBAAiB;IACvBC,gBAAc,EAAEnD,MAAA,CAAAoD,gBAAgB;IAChCC,cAAY,EAAErD,MAAA,CAAAsD;+CAGrBpD,YAAA,CAEmBqD,2BAAA;IAtDvBT,UAAA,EAoD+B9C,MAAA,CAAAwD,IAAI;IApDnC,uBAAAR,MAAA,QAAAA,MAAA,gBAAAjC,MAAA;MAAA,OAoD+Bf,MAAA,CAAAwD,IAAI,GAAAzC,MAAA;IAAA;IAAE0C,IAAI,EAAC;;IApD1CrD,OAAA,EAAAC,QAAA,CAqDM;MAAA,OAAwD,CAAxDH,YAAA,CAAwDF,MAAA;QAAhCc,EAAE,EAAEd,MAAA,CAAAc;MAAE,gC;;IArDpCP,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}