{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { format } from 'common/js/time.js';\nimport { user } from 'common/js/system_var.js';\nimport DetailsTitle from './components/DetailsTitle.vue';\nimport LegalCaseDetails from './components/LegalCaseDetails.vue';\nvar __default__ = {\n  name: 'BackgroundCheckInfo'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var tipsObj = {\n      '0': '等待用户人脸验证',\n      '1': '用户已拒绝人脸识别',\n      '2': '认证未完成',\n      '200': '认证通过',\n      '201': '姓名和身份证不一致',\n      '202': '查询不到身份信息',\n      '203': '查询不到照片或照片不可用',\n      '204': '人脸对比不一致',\n      '205': '活体检测存在风险',\n      '20': '6业务策略限制，认证不通过'\n    };\n    var info = ref({});\n    var details = ref({});\n    var dataList = ref([{\n      id: 'criminal',\n      name: '刑事案件',\n      amount: 0\n    }, {\n      id: 'implement',\n      name: '执行案件',\n      amount: 0\n    }, {\n      id: 'administrative',\n      name: '行政案件',\n      amount: 0\n    }, {\n      id: 'preservation',\n      name: '非诉保全审查',\n      amount: 10\n    }, {\n      id: 'civil',\n      name: '民事案件',\n      amount: 0\n    }, {\n      id: 'bankrupt',\n      name: '强制清算与破产案件',\n      amount: 18\n    }]);\n    var activeName = ref('');\n    var tableData = ref([]);\n    var rowInfo = ref({});\n    var show = ref(false);\n    var faceResult = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$faceResult, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.faceResult({\n                uuid: route.query.uuid\n              });\n            case 2:\n              _yield$api$faceResult = _context.sent;\n              data = _yield$api$faceResult.data;\n              info.value = data;\n              if (data.status === '200') {\n                lawsuitResult();\n              }\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function faceResult() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var lawsuitResult = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _yield$api$lawsuitRes, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.lawsuitResult({\n                uuid: route.query.uuid\n              });\n            case 2:\n              _yield$api$lawsuitRes = _context2.sent;\n              data = _yield$api$lawsuitRes.data;\n              details.value = data;\n              activeName.value = 'criminal';\n              dataList.value.forEach(function (item) {\n                var _data$item$id;\n                item.amount = ((_data$item$id = data[item.id]) === null || _data$item$id === void 0 || (_data$item$id = _data$item$id.cases) === null || _data$item$id === void 0 ? void 0 : _data$item$id.length) || 0;\n              });\n              handleClick(activeName.value);\n            case 8:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function lawsuitResult() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var openVerify = function openVerify() {\n      window.open(route.query.url, '_blank');\n    };\n    var handleClick = function handleClick(tab) {\n      var _details$value$tab;\n      tableData.value = (_details$value$tab = details.value[tab]) === null || _details$value$tab === void 0 ? void 0 : _details$value$tab.cases;\n    };\n    var handleRow = function handleRow(row) {\n      rowInfo.value = row;\n      show.value = true;\n    };\n    onMounted(function () {\n      faceResult();\n    });\n    var __returned__ = {\n      route,\n      tipsObj,\n      info,\n      details,\n      dataList,\n      activeName,\n      tableData,\n      rowInfo,\n      show,\n      faceResult,\n      lawsuitResult,\n      openVerify,\n      handleClick,\n      handleRow,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      get useRoute() {\n        return useRoute;\n      },\n      get format() {\n        return format;\n      },\n      get user() {\n        return user;\n      },\n      DetailsTitle,\n      LegalCaseDetails\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onMounted", "useRoute", "format", "user", "DetailsTitle", "LegalCaseDetails", "__default__", "route", "tipsObj", "info", "details", "dataList", "id", "amount", "activeName", "tableData", "rowInfo", "show", "faceResult", "_ref2", "_callee", "_yield$api$faceResult", "data", "_callee$", "_context", "uuid", "query", "status", "lawsuitResult", "_ref3", "_callee2", "_yield$api$lawsuitRes", "_callee2$", "_context2", "item", "_data$item$id", "cases", "handleClick", "openVerify", "window", "open", "url", "tab", "_details$value$tab", "handleRow", "row"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/BackgroundCheck/BackgroundCheckInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"BackgroundCheckInfo\">\r\n    <anchor-location>\r\n      <div class=\"BackgroundCheckInfoBody\">\r\n        <div class=\"BackgroundCheckInfoTitle\">个人司法背景审查报告</div>\r\n        <div class=\"BackgroundCheckInfoTime\">数据截止至：{{ format(new Date(), 'YYYY-MM-DD') }}</div>\r\n        <div class=\"BackgroundCheckInfoTips\">免责声明：大数据风险排查，不能作为最终判定</div>\r\n        <div class=\"BackgroundCheckInfoInfo\">\r\n          <div class=\"BackgroundCheckInfoImg\">\r\n            <el-image :src=\"api.defaultImgURL('default_user_head.jpg')\" fit=\"cover\" />\r\n          </div>\r\n          <div class=\"BackgroundCheckInfoInfoItem\">\r\n            <div class=\"BackgroundCheckInfoUserName\">{{ info.name }}</div>\r\n            <div class=\"BackgroundCheckInfoUserIdcard\">{{ route.query.idcard }}</div>\r\n          </div>\r\n          <div class=\"BackgroundCheckInfoInfoItem\">\r\n            <div class=\"BackgroundCheckInfoText\">发起人：{{ user.userName }}</div>\r\n            <div class=\"BackgroundCheckInfoText\">发起时间：{{ format(new Date(), 'YYYY-MM-DD HH:mm') }}</div>\r\n          </div>\r\n          <!-- <div class=\"BackgroundCheckInfoInfoItem\">\r\n            <div class=\"BackgroundCheckInfoText\">授权时间：2024-03-13 12:00</div>\r\n            <div></div>\r\n          </div> -->\r\n        </div>\r\n        <DetailsTitle>\r\n          <div class=\"BackgroundCheckInfoName\">审查结果</div>\r\n        </DetailsTitle>\r\n        <div class=\"BackgroundCheckInfoResult\">\r\n          <div class=\"BackgroundCheckInfoResultVerify\" v-if=\"info.status !== '200'\">\r\n            <div class=\"BackgroundCheckInfoResultVerifyTisp\">{{ tipsObj[info.status] }} </div>\r\n            <div class=\"BackgroundCheckInfoResultButton\" v-if=\"['0', '2'].includes(info.status)\">\r\n              <el-button type=\"primary\" @click=\"faceResult\">刷新</el-button>\r\n              <el-button type=\"primary\" @click=\"openVerify\">去验证</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"BackgroundCheckInfoResultItem\" v-for=\"item in dataList\" :key=\"item.id\">\r\n            <div class=\"BackgroundCheckInfoResultItemIcon\" :class=\"{ anomaly: item.amount }\">\r\n              <el-icon>\r\n                <MessageBox />\r\n              </el-icon>\r\n            </div>\r\n            <div class=\"BackgroundCheckInfoResultItemName\" v-if=\"item.amount\">\r\n              {{ item.name }} <span>{{ item.amount }}</span>\r\n            </div>\r\n            <div class=\"BackgroundCheckInfoResultItemName\" v-if=\"!item.amount\">无{{ item.name }}记录</div>\r\n          </div>\r\n        </div>\r\n        <template v-if=\"info.status === '200'\">\r\n          <DetailsTitle>\r\n            <div class=\"BackgroundCheckInfoName\">审查纪录</div>\r\n          </DetailsTitle>\r\n          <el-tabs v-model=\"activeName\" @tab-change=\"handleClick\">\r\n            <el-tab-pane v-for=\"item in dataList\" :key=\"item.id\" :name=\"item.id\" :label=\"item.name\"></el-tab-pane>\r\n          </el-tabs>\r\n          <div class=\"globalTable\">\r\n            <el-table :data=\"tableData\">\r\n              <el-table-column label=\"案号\" min-width=\"160\" prop=\"c_ah\">\r\n                <template #default=\"scope\">\r\n                  <el-link type=\"primary\" @click=\"handleRow(scope.row)\">{{ scope.row.c_ah }} </el-link>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"案由\" min-width=\"160\" prop=\"n_laay\" />\r\n              <el-table-column label=\"诉讼地位\" min-width=\"160\" prop=\"n_ssdw\" />\r\n              <el-table-column label=\"立案时间\" width=\"130\" prop=\"d_larq\" />\r\n              <el-table-column label=\"结案时间\" width=\"130\" prop=\"d_jarq\" />\r\n            </el-table>\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </anchor-location>\r\n    <xyl-popup-window v-model=\"show\" name=\"案件详情\">\r\n      <LegalCaseDetails :data=\"rowInfo\"></LegalCaseDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'BackgroundCheckInfo' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\nimport { user } from 'common/js/system_var.js'\r\nimport DetailsTitle from './components/DetailsTitle.vue'\r\nimport LegalCaseDetails from './components/LegalCaseDetails.vue'\r\nconst route = useRoute()\r\nconst tipsObj = {\r\n  '0': '等待用户人脸验证',\r\n  '1': '用户已拒绝人脸识别',\r\n  '2': '认证未完成',\r\n  '200': '认证通过',\r\n  '201': '姓名和身份证不一致',\r\n  '202': '查询不到身份信息',\r\n  '203': '查询不到照片或照片不可用',\r\n  '204': '人脸对比不一致',\r\n  '205': '活体检测存在风险',\r\n  '20': '6业务策略限制，认证不通过'\r\n}\r\nconst info = ref({})\r\nconst details = ref({})\r\nconst dataList = ref([\r\n  { id: 'criminal', name: '刑事案件', amount: 0 },\r\n  { id: 'implement', name: '执行案件', amount: 0 },\r\n  { id: 'administrative', name: '行政案件', amount: 0 },\r\n  { id: 'preservation', name: '非诉保全审查', amount: 10 },\r\n  { id: 'civil', name: '民事案件', amount: 0 },\r\n  { id: 'bankrupt', name: '强制清算与破产案件', amount: 18 }\r\n])\r\nconst activeName = ref('')\r\nconst tableData = ref([])\r\nconst rowInfo = ref({})\r\nconst show = ref(false)\r\n\r\nconst faceResult = async () => {\r\n  const { data } = await api.faceResult({ uuid: route.query.uuid })\r\n  info.value = data\r\n  if (data.status === '200') {\r\n    lawsuitResult()\r\n  }\r\n}\r\nconst lawsuitResult = async () => {\r\n  const { data } = await api.lawsuitResult({ uuid: route.query.uuid })\r\n  details.value = data\r\n  activeName.value = 'criminal'\r\n  dataList.value.forEach(item => {\r\n    item.amount = data[item.id]?.cases?.length || 0\r\n  })\r\n  handleClick(activeName.value)\r\n}\r\nconst openVerify = () => {\r\n  window.open(route.query.url, '_blank')\r\n}\r\nconst handleClick = (tab) => {\r\n  tableData.value = details.value[tab]?.cases\r\n}\r\nconst handleRow = (row) => {\r\n  rowInfo.value = row\r\n  show.value = true\r\n}\r\nonMounted(() => { faceResult() })\r\n</script>\r\n<style lang=\"scss\">\r\n.BackgroundCheckInfo {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .BackgroundCheckInfoBody {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: var(--zy-distance-one);\r\n\r\n    .BackgroundCheckInfoTitle {\r\n      font-weight: bold;\r\n      text-align: center;\r\n      font-size: var(--zy-title-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-bottom: var(--zy-distance-two);\r\n    }\r\n\r\n    .BackgroundCheckInfoName {\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .BackgroundCheckInfoTime {\r\n      font-weight: bold;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-bottom: var(--zy-font-name-distance-five);\r\n    }\r\n\r\n    .BackgroundCheckInfoTips {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-color-danger);\r\n      padding-bottom: var(--zy-distance-two);\r\n    }\r\n\r\n    .BackgroundCheckInfoInfo {\r\n      display: flex;\r\n      padding: var(--zy-distance-two) var(--zy-distance-one);\r\n      background-color: var(--zy-el-color-info-light-9);\r\n      margin-bottom: var(--zy-distance-two);\r\n\r\n      .BackgroundCheckInfoImg {\r\n        width: 60px;\r\n        height: 60px;\r\n        margin-right: 9px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          height: 100%;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n\r\n      .BackgroundCheckInfoInfoItem {\r\n        width: calc(33.3% - 23px);\r\n        padding: 0 var(--zy-distance-five);\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n\r\n        .BackgroundCheckInfoUserName {\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n\r\n        .BackgroundCheckInfoUserIdcard {\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n\r\n        .BackgroundCheckInfoText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n      }\r\n    }\r\n\r\n    .BackgroundCheckInfoResult {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      position: relative;\r\n\r\n      .BackgroundCheckInfoResultVerify {\r\n        position: absolute;\r\n        left: 0;\r\n        bottom: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        background: #fff;\r\n        z-index: 9;\r\n\r\n        .BackgroundCheckInfoResultVerifyTisp {\r\n          text-align: center;\r\n          padding-top: 40px;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding-bottom: var(--zy-distance-three);\r\n        }\r\n\r\n        .BackgroundCheckInfoResultButton {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          .zy-el-button {\r\n            margin-left: 10px;\r\n            --zy-el-button-size: var(--zy-height-secondary);\r\n          }\r\n        }\r\n      }\r\n\r\n      .BackgroundCheckInfoResultItem {\r\n        width: calc(33.3% - 13px);\r\n        height: 52px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: var(--zy-distance-three) var(--zy-distance-two);\r\n        margin-bottom: var(--zy-distance-two);\r\n        background-color: var(--zy-el-color-info-light-9);\r\n\r\n        .BackgroundCheckInfoResultItemIcon {\r\n          width: 30px;\r\n          height: 30px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          background: var(--zy-el-color-success-light-3);\r\n          border-radius: 6px;\r\n\r\n          .zy-el-icon {\r\n            font-size: 20px;\r\n            color: #fff;\r\n          }\r\n        }\r\n\r\n        .BackgroundCheckInfoResultItemIcon.anomaly {\r\n          background: var(--zy-el-color-danger-light-3);\r\n        }\r\n\r\n        .BackgroundCheckInfoResultItemName {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 0 var(--zy-distance-four);\r\n          font-size: var(--zy-text-font-size);\r\n\r\n          span {\r\n            margin-left: 10px;\r\n            font-weight: bold;\r\n            font-size: var(--zy-name-font-size);\r\n            color: var(--zy-el-color-danger);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .globalTable {\r\n      width: 100%;\r\n      height: 380px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAgFA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,QAAQ,KAAK;AACpC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,gBAAgB,MAAM,mCAAmC;AAThE,IAAAC,WAAA,GAAe;EAAEnC,IAAI,EAAE;AAAsB,CAAC;;;;;IAU9C,IAAMoC,KAAK,GAAGN,QAAQ,CAAC,CAAC;IACxB,IAAMO,OAAO,GAAG;MACd,GAAG,EAAE,UAAU;MACf,GAAG,EAAE,WAAW;MAChB,GAAG,EAAE,OAAO;MACZ,KAAK,EAAE,MAAM;MACb,KAAK,EAAE,WAAW;MAClB,KAAK,EAAE,UAAU;MACjB,KAAK,EAAE,cAAc;MACrB,KAAK,EAAE,SAAS;MAChB,KAAK,EAAE,UAAU;MACjB,IAAI,EAAE;IACR,CAAC;IACD,IAAMC,IAAI,GAAGV,GAAG,CAAC,CAAC,CAAC,CAAC;IACpB,IAAMW,OAAO,GAAGX,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMY,QAAQ,GAAGZ,GAAG,CAAC,CACnB;MAAEa,EAAE,EAAE,UAAU;MAAEzC,IAAI,EAAE,MAAM;MAAE0C,MAAM,EAAE;IAAE,CAAC,EAC3C;MAAED,EAAE,EAAE,WAAW;MAAEzC,IAAI,EAAE,MAAM;MAAE0C,MAAM,EAAE;IAAE,CAAC,EAC5C;MAAED,EAAE,EAAE,gBAAgB;MAAEzC,IAAI,EAAE,MAAM;MAAE0C,MAAM,EAAE;IAAE,CAAC,EACjD;MAAED,EAAE,EAAE,cAAc;MAAEzC,IAAI,EAAE,QAAQ;MAAE0C,MAAM,EAAE;IAAG,CAAC,EAClD;MAAED,EAAE,EAAE,OAAO;MAAEzC,IAAI,EAAE,MAAM;MAAE0C,MAAM,EAAE;IAAE,CAAC,EACxC;MAAED,EAAE,EAAE,UAAU;MAAEzC,IAAI,EAAE,WAAW;MAAE0C,MAAM,EAAE;IAAG,CAAC,CAClD,CAAC;IACF,IAAMC,UAAU,GAAGf,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMgB,SAAS,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMiB,OAAO,GAAGjB,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMkB,IAAI,GAAGlB,GAAG,CAAC,KAAK,CAAC;IAEvB,IAAMmB,UAAU;MAAA,IAAAC,KAAA,GAAA1B,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgD,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAAtI,mBAAA,GAAAuB,IAAA,UAAAgH,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA3C,IAAA,GAAA2C,QAAA,CAAAtE,IAAA;YAAA;cAAAsE,QAAA,CAAAtE,IAAA;cAAA,OACM4C,GAAG,CAACoB,UAAU,CAAC;gBAAEO,IAAI,EAAElB,KAAK,CAACmB,KAAK,CAACD;cAAK,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAG,QAAA,CAAA7E,IAAA;cAAzD2E,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZb,IAAI,CAAC/G,KAAK,GAAG4H,IAAI;cACjB,IAAIA,IAAI,CAACK,MAAM,KAAK,KAAK,EAAE;gBACzBC,aAAa,CAAC,CAAC;cACjB;YAAC;YAAA;cAAA,OAAAJ,QAAA,CAAAxC,IAAA;UAAA;QAAA,GAAAoC,OAAA;MAAA,CACF;MAAA,gBANKF,UAAUA,CAAA;QAAA,OAAAC,KAAA,CAAAxB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMf;IACD,IAAMkC,aAAa;MAAA,IAAAC,KAAA,GAAApC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0D,SAAA;QAAA,IAAAC,qBAAA,EAAAT,IAAA;QAAA,OAAAtI,mBAAA,GAAAuB,IAAA,UAAAyH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApD,IAAA,GAAAoD,SAAA,CAAA/E,IAAA;YAAA;cAAA+E,SAAA,CAAA/E,IAAA;cAAA,OACG4C,GAAG,CAAC8B,aAAa,CAAC;gBAAEH,IAAI,EAAElB,KAAK,CAACmB,KAAK,CAACD;cAAK,CAAC,CAAC;YAAA;cAAAM,qBAAA,GAAAE,SAAA,CAAAtF,IAAA;cAA5D2E,IAAI,GAAAS,qBAAA,CAAJT,IAAI;cACZZ,OAAO,CAAChH,KAAK,GAAG4H,IAAI;cACpBR,UAAU,CAACpH,KAAK,GAAG,UAAU;cAC7BiH,QAAQ,CAACjH,KAAK,CAACoC,OAAO,CAAC,UAAAoG,IAAI,EAAI;gBAAA,IAAAC,aAAA;gBAC7BD,IAAI,CAACrB,MAAM,GAAG,EAAAsB,aAAA,GAAAb,IAAI,CAACY,IAAI,CAACtB,EAAE,CAAC,cAAAuB,aAAA,gBAAAA,aAAA,GAAbA,aAAA,CAAeC,KAAK,cAAAD,aAAA,uBAApBA,aAAA,CAAsBpE,MAAM,KAAI,CAAC;cACjD,CAAC,CAAC;cACFsE,WAAW,CAACvB,UAAU,CAACpH,KAAK,CAAC;YAAA;YAAA;cAAA,OAAAuI,SAAA,CAAAjD,IAAA;UAAA;QAAA,GAAA8C,QAAA;MAAA,CAC9B;MAAA,gBARKF,aAAaA,CAAA;QAAA,OAAAC,KAAA,CAAAlC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQlB;IACD,IAAM4C,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvBC,MAAM,CAACC,IAAI,CAACjC,KAAK,CAACmB,KAAK,CAACe,GAAG,EAAE,QAAQ,CAAC;IACxC,CAAC;IACD,IAAMJ,WAAW,GAAG,SAAdA,WAAWA,CAAIK,GAAG,EAAK;MAAA,IAAAC,kBAAA;MAC3B5B,SAAS,CAACrH,KAAK,IAAAiJ,kBAAA,GAAGjC,OAAO,CAAChH,KAAK,CAACgJ,GAAG,CAAC,cAAAC,kBAAA,uBAAlBA,kBAAA,CAAoBP,KAAK;IAC7C,CAAC;IACD,IAAMQ,SAAS,GAAG,SAAZA,SAASA,CAAIC,GAAG,EAAK;MACzB7B,OAAO,CAACtH,KAAK,GAAGmJ,GAAG;MACnB5B,IAAI,CAACvH,KAAK,GAAG,IAAI;IACnB,CAAC;IACDsG,SAAS,CAAC,YAAM;MAAEkB,UAAU,CAAC,CAAC;IAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}