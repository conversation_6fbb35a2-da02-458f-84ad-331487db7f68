{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createBlock as _createBlock, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalAiChat\",\n  \"data-no-text-select\": \"\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalAiChatLogo\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalAiChatHead forbidSelect\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalAiChatHeadName\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalAiChatDialogueBody\"\n};\nvar _hoisted_6 = {\n  class: \"GlobalAiChatDialogue ellipsis\"\n};\nvar _hoisted_7 = {\n  class: \"GlobalAiChatBody\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  class: \"GlobalAiChatBodyTipsBody\"\n};\nvar _hoisted_9 = [\"onClick\"];\nvar _hoisted_10 = [\"onClick\"];\nvar _hoisted_11 = {\n  class: \"GlobalChatMessagesFileName ellipsis\"\n};\nvar _hoisted_12 = {\n  class: \"GlobalChatMessagesFileSize\"\n};\nvar _hoisted_13 = {\n  class: \"GlobalAiChatSelfMessageInfo\"\n};\nvar _hoisted_14 = {\n  class: \"GlobalAiChatMessageInfo\"\n};\nvar _hoisted_15 = [\"onClick\"];\nvar _hoisted_16 = {\n  key: 0\n};\nvar _hoisted_17 = {\n  class: \"GlobalAiChatMessagePonderContent\"\n};\nvar _hoisted_18 = {\n  class: \"GlobalAiChatMessageContent\"\n};\nvar _hoisted_19 = {\n  class: \"GlobalAiChatMessageDataList\"\n};\nvar _hoisted_20 = [\"onClick\"];\nvar _hoisted_21 = {\n  class: \"GlobalAiChatMessageChart\"\n};\nvar _hoisted_22 = {\n  key: 0,\n  class: \"GlobalAiChatMessageLoading\"\n};\nvar _hoisted_23 = {\n  key: 1,\n  class: \"GlobalAiChatBodyTipsVice\"\n};\nvar _hoisted_24 = [\"onClick\"];\nvar _hoisted_25 = {\n  key: 1,\n  class: \"GlobalAiChatMessageControls\"\n};\nvar _hoisted_26 = [\"onClick\"];\nvar _hoisted_27 = [\"onClick\"];\nvar _hoisted_28 = [\"onClick\"];\nvar _hoisted_29 = {\n  class: \"GlobalAiChatDialogueEditor\"\n};\nvar _hoisted_30 = {\n  class: \"GlobalAiChatDialogueTools\"\n};\nvar _hoisted_31 = [\"onClick\"];\nvar _hoisted_32 = {\n  class: \"GlobalAiChatDialogueEditorBody\"\n};\nvar _hoisted_33 = {\n  class: \"GlobalAiChatToolsActive\"\n};\nvar _hoisted_34 = {\n  class: \"GlobalAiChatToolsActiveName\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_Close = _resolveComponent(\"Close\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_ArrowUpBold = _resolveComponent(\"ArrowUpBold\");\n  var _component_ArrowDownBold = _resolveComponent(\"ArrowDownBold\");\n  var _component_CopyDocument = _resolveComponent(\"CopyDocument\");\n  var _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  var _component_Refresh = _resolveComponent(\"Refresh\");\n  var _component_Star = _resolveComponent(\"Star\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n    class: \"GlobalAiChatClose\",\n    onClick: $setup.handleClick\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Close)];\n    }),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n    src: $setup.IntelligentAssistant,\n    loading: \"lazy\",\n    fit: \"cover\",\n    draggable: \"false\"\n  }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, \"您好 \" + _toDisplayString($setup.user.userName) + \"!\", 1 /* TEXT */), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"GlobalAiChatHeadText\"\n  }, \"我是西安政协深度融合DeepSeek的综合助手，内容由 AI 生成，请仔细甄别。\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_5, [_createVNode($setup[\"GlobalAiChatHistory\"], {\n    ref: \"chatHistoryRef\",\n    modelValue: $setup.chatId,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.chatId = $event;\n    }),\n    onSelect: $setup.handleSelect\n  }, null, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.chatContent || '新对话'), 1 /* TEXT */), _createElementVNode(\"div\", {\n    class: \"GlobalAiChatDialogueNew\",\n    onClick: $setup.handleNewDialogue\n  }, [_createElementVNode(\"span\", {\n    innerHTML: $setup.newIcon\n  })])]), _createVNode(_component_el_scrollbar, {\n    always: \"\",\n    ref: \"scrollRef\",\n    class: \"GlobalAiChatScroll\",\n    onScroll: $setup.handleScroll\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_7, [$setup.aiWords.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n        class: \"GlobalAiChatBodyTipsVice\"\n      }, \"您可以试着问我：\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.aiWords, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"GlobalAiChatBodyTipsItem\",\n          key: item.id\n        }, [_createElementVNode(\"div\", {\n          class: \"GlobalAiChatBodyTips\",\n          onClick: function onClick($event) {\n            return $setup.handleSendMessage(item.promptWord);\n          }\n        }, [_createElementVNode(\"span\", {\n          innerHTML: $setup.tipsIcon\n        }), _createTextVNode(\" \" + _toDisplayString(item.promptWord), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_9)]);\n      }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.chatMessage, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: _normalizeClass([item.type ? 'GlobalAiChatSelfMessage' : 'GlobalAiChatMessage']),\n          key: item.id\n        }, [item.type ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 0\n        }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.fileData, function (item) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            class: \"GlobalAiChatSelfMessageFile\",\n            key: item.id,\n            onClick: function onClick($event) {\n              return $setup.handlePreview(item);\n            }\n          }, [_createElementVNode(\"div\", {\n            class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon(item === null || item === void 0 ? void 0 : item.extName)])\n          }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_11, _toDisplayString((item === null || item === void 0 ? void 0 : item.originalFileName) || '未知文件'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_12, _toDisplayString(item !== null && item !== void 0 && item.fileSize ? $setup.size2Str(item === null || item === void 0 ? void 0 : item.fileSize) : '0KB'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_10);\n        }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"div\", _hoisted_13, _toDisplayString(item.content), 1 /* TEXT */)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), !item.type ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 1\n        }, [_createCommentVNode(\" <el-image :src=\\\"IntelligentAssistant\\\" loading=\\\"lazy\\\" fit=\\\"cover\\\" draggable=\\\"false\\\" /> \"), _createElementVNode(\"div\", _hoisted_14, [item.time ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"GlobalAiChatMessagePonder forbidSelect\",\n          onClick: function onClick($event) {\n            return item.ponderShow = !item.ponderShow;\n          }\n        }, [_createElementVNode(\"div\", {\n          innerHTML: $setup.ponderIcon\n        }), _cache[6] || (_cache[6] = _createTextVNode(\" 已深度思考 \")), item.time !== '1' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_16, \"（用时 \" + _toDisplayString(item.time) + \"）\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_icon, null, {\n          default: _withCtx(function () {\n            return [item.ponderShow ? (_openBlock(), _createBlock(_component_ArrowUpBold, {\n              key: 0\n            })) : _createCommentVNode(\"v-if\", true), !item.ponderShow ? (_openBlock(), _createBlock(_component_ArrowDownBold, {\n              key: 1\n            })) : _createCommentVNode(\"v-if\", true)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)], 8 /* PROPS */, _hoisted_15)) : _createCommentVNode(\"v-if\", true), _withDirectives(_createElementVNode(\"div\", _hoisted_17, [_createVNode($setup[\"GlobalMarkdown\"], {\n          ref_for: true,\n          ref: function ref(el) {\n            return $setup.getElPonderRef(el, index);\n          },\n          modelValue: item.ponderContent,\n          \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n            return item.ponderContent = $event;\n          },\n          content: item.ponderContentOld,\n          onUpdate: $setup.handleUpdate\n        }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\", \"content\"])], 512 /* NEED_PATCH */), [[_vShow, item.ponderShow]]), _createElementVNode(\"div\", _hoisted_18, [_createVNode($setup[\"GlobalMarkdown\"], {\n          ref_for: true,\n          ref: function ref(el) {\n            return $setup.getElRef(el, index);\n          },\n          modelValue: item.content,\n          \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n            return item.content = $event;\n          },\n          content: item.contentOld,\n          \"on-link-click\": $setup.handleLinkClick,\n          onUpdate: $setup.handleUpdate\n        }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\", \"content\"]), _createElementVNode(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.dataList, function (row) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            class: \"GlobalAiChatMessageDataItem\",\n            onClick: function onClick($event) {\n              return $setup.handleDataList(row);\n            }\n          }, _toDisplayString(row.sourceName), 9 /* TEXT, PROPS */, _hoisted_20);\n        }), 256 /* UNKEYED_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_21, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.chartData, function (row, i) {\n          return _openBlock(), _createBlock($setup[\"GlobalAiChart\"], {\n            key: i + 'chartData',\n            option: row\n          }, null, 8 /* PROPS */, [\"option\"]);\n        }), 128 /* KEYED_FRAGMENT */))]), index === $setup.chatMessage.length - 1 && $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createElementVNode(\"div\", {\n          class: \"answerLoading\",\n          innerHTML: $setup.loadingIcon\n        })])) : _createCommentVNode(\"v-if\", true), item.guideWord.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, \"您是不是想问：\")) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.guideWord, function (row, i) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            class: \"GlobalAiChatBodyTipsItem\",\n            key: i + 'guideWord'\n          }, [_createElementVNode(\"div\", {\n            class: \"GlobalAiChatBodyTips\",\n            onClick: function onClick($event) {\n              return $setup.handleGuideWord(row);\n            }\n          }, [_createElementVNode(\"span\", {\n            innerHTML: $setup.tipsIcon\n          }), _createTextVNode(\" \" + _toDisplayString(row.question), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_24)]);\n        }), 128 /* KEYED_FRAGMENT */))]), item.isControls ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [_createVNode(_component_el_tooltip, {\n          content: \"复制\",\n          placement: \"top\"\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"div\", {\n              class: \"GlobalAiChatMessageControlsItem\",\n              onClick: function onClick($event) {\n                return $setup.handleCopyMessage(item.content, index);\n              }\n            }, [_createVNode(_component_el_icon, null, {\n              default: _withCtx(function () {\n                return [_createVNode(_component_CopyDocument)];\n              }),\n              _: 1 /* STABLE */\n            })], 8 /* PROPS */, _hoisted_26)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_el_tooltip, {\n          content: \"重新生成\",\n          placement: \"top\"\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"div\", {\n              class: \"GlobalAiChatMessageControlsItem\",\n              onClick: function onClick($event) {\n                return $setup.handleRetryMessage(item, index);\n              }\n            }, [_createVNode(_component_el_icon, null, {\n              default: _withCtx(function () {\n                return [_createVNode(_component_Refresh)];\n              }),\n              _: 1 /* STABLE */\n            })], 8 /* PROPS */, _hoisted_27)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createVNode(_component_el_tooltip, {\n          content: \"满意度调查\",\n          placement: \"top\"\n        }, {\n          default: _withCtx(function () {\n            return [_createElementVNode(\"div\", {\n              class: \"GlobalAiChatMessageControlsItem\",\n              onClick: function onClick($event) {\n                return $setup.handleSatisfactionSurvey(item, index);\n              }\n            }, [_createVNode(_component_el_icon, null, {\n              default: _withCtx(function () {\n                return [_createVNode(_component_Star)];\n              }),\n              _: 1 /* STABLE */\n            })], 8 /* PROPS */, _hoisted_28)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)])) : _createCommentVNode(\"v-if\", true)])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_29, [_withDirectives(_createElementVNode(\"div\", _hoisted_30, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.aiTools, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"GlobalAiChatDialogueTool\",\n      key: item.id,\n      onClick: function onClick($event) {\n        return $setup.handleToolSendMessage(item);\n      }\n    }, [_createElementVNode(\"span\", {\n      innerHTML: $setup.toolIcon\n    }), _createTextVNode(\" \" + _toDisplayString(item.chatToolName), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_31);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vShow, !$setup.toolId]]), _createElementVNode(\"div\", _hoisted_32, [_withDirectives(_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, _toDisplayString($setup.toolName), 1 /* TEXT */), _createElementVNode(\"div\", {\n    class: \"GlobalAiChatToolsActiveIcon\",\n    onClick: $setup.handleToolClose\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Close)];\n    }),\n    _: 1 /* STABLE */\n  })])], 512 /* NEED_PATCH */), [[_vShow, $setup.toolId]]), _withDirectives(_createVNode($setup[\"GlobalAiChatFile\"], {\n    fileList: $setup.fileList,\n    fileData: $setup.fileData,\n    onClose: $setup.handleClose\n  }, null, 8 /* PROPS */, [\"fileList\", \"fileData\"]), [[_vShow, $setup.fileList.length || $setup.fileData.length]]), _createVNode($setup[\"GlobalAiChatEditor\"], {\n    ref: \"editorRef\",\n    modelValue: $setup.sendContent,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.sendContent = $event;\n    }),\n    disabled: $setup.disabled,\n    onSend: $setup.handleSendMessage,\n    onStop: $setup.handleStopMessage,\n    onUploadCallback: $setup.handleFileUpload,\n    onFileCallback: $setup.handleFileCallback\n  }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.dataShow,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.dataShow = $event;\n    }),\n    name: $setup.dataName\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalAiChatData\"], {\n        data: $setup.dataInfo\n      }, null, 8 /* PROPS */, [\"data\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"]), _createVNode($setup[\"CustomSatisfactionModal\"], {\n    modelValue: $setup.satisfactionSurveyShow,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.satisfactionSurveyShow = $event;\n    }),\n    data: $setup.dataInfo\n  }, null, 8 /* PROPS */, [\"modelValue\", \"data\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "onClick", "$setup", "handleClick", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Close", "_", "_hoisted_2", "_component_el_image", "src", "IntelligentAssistant", "loading", "fit", "draggable", "_hoisted_3", "_hoisted_4", "_toDisplayString", "user", "userName", "_hoisted_5", "ref", "modelValue", "chatId", "_cache", "$event", "onSelect", "handleSelect", "_hoisted_6", "chatContent", "handleNewDialogue", "innerHTML", "newIcon", "_component_el_scrollbar", "always", "onScroll", "handleScroll", "_hoisted_7", "aiWords", "length", "_hoisted_8", "_Fragment", "_renderList", "item", "id", "handleSendMessage", "promptWord", "tipsIcon", "_createTextVNode", "_hoisted_9", "_createCommentVNode", "chatMessage", "index", "_normalizeClass", "type", "fileData", "handlePreview", "fileIcon", "extName", "_hoisted_11", "originalFileName", "_hoisted_12", "fileSize", "size2Str", "_hoisted_10", "_hoisted_13", "content", "_hoisted_14", "time", "ponderShow", "ponderIcon", "_hoisted_16", "_createBlock", "_component_ArrowUpBold", "_component_ArrowDownBold", "_hoisted_15", "_hoisted_17", "ref_for", "el", "getElPonderRef", "ponder<PERSON><PERSON>nt", "onUpdateModelValue", "ponderContentOld", "onUpdate", "handleUpdate", "_hoisted_18", "getElRef", "contentOld", "handleLinkClick", "_hoisted_19", "dataList", "row", "handleDataList", "sourceName", "_hoisted_20", "_hoisted_21", "chartData", "i", "option", "_hoisted_22", "loadingIcon", "guideWord", "_hoisted_23", "handleGuideWord", "question", "_hoisted_24", "isControls", "_hoisted_25", "_component_el_tooltip", "placement", "handleCopyMessage", "_component_CopyDocument", "_hoisted_26", "handleRetryMessage", "_component_Refresh", "_hoisted_27", "handleSatisfactionSurvey", "_component_Star", "_hoisted_28", "_hoisted_29", "_hoisted_30", "aiTools", "handleToolSendMessage", "toolIcon", "chatToolName", "_hoisted_31", "toolId", "_hoisted_32", "_hoisted_33", "_hoisted_34", "toolName", "handleToolClose", "fileList", "onClose", "handleClose", "send<PERSON><PERSON><PERSON>", "disabled", "onSend", "onStop", "handleStopMessage", "onUploadCallback", "handleFileUpload", "onFileCallback", "handleFileCallback", "_component_xyl_popup_window", "dataShow", "name", "dataName", "data", "dataInfo", "satisfactionSurveyShow"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalAiChat\\GlobalAiChat.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChat\" data-no-text-select>\r\n    <div class=\"GlobalAiChatClose\" @click=\"handleClick\">\r\n      <el-icon>\r\n        <Close />\r\n      </el-icon>\r\n    </div>\r\n    <div class=\"GlobalAiChatLogo\">\r\n      <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" />\r\n    </div>\r\n    <div class=\"GlobalAiChatHead forbidSelect\">\r\n      <div class=\"GlobalAiChatHeadName\">您好 {{ user.userName }}!</div>\r\n      <div class=\"GlobalAiChatHeadText\">我是西安政协深度融合DeepSeek的综合助手，内容由 AI 生成，请仔细甄别。</div>\r\n    </div>\r\n    <div class=\"GlobalAiChatDialogueBody\">\r\n      <GlobalAiChatHistory ref=\"chatHistoryRef\" v-model=\"chatId\" @select=\"handleSelect\"></GlobalAiChatHistory>\r\n      <div class=\"GlobalAiChatDialogue ellipsis\">{{ chatContent || '新对话' }}</div>\r\n      <div class=\"GlobalAiChatDialogueNew\" @click=\"handleNewDialogue\"><span v-html=\"newIcon\"></span></div>\r\n    </div>\r\n    <el-scrollbar always ref=\"scrollRef\" class=\"GlobalAiChatScroll\" @scroll=\"handleScroll\">\r\n      <div class=\"GlobalAiChatBody\">\r\n        <div class=\"GlobalAiChatBodyTipsBody\" v-if=\"aiWords.length\">\r\n          <div class=\"GlobalAiChatBodyTipsVice\">您可以试着问我：</div>\r\n          <div class=\"GlobalAiChatBodyTipsItem\" v-for=\"item in aiWords\" :key=\"item.id\">\r\n            <div class=\"GlobalAiChatBodyTips\" @click=\"handleSendMessage(item.promptWord)\">\r\n              <span v-html=\"tipsIcon\"></span>\r\n              {{ item.promptWord }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div :class=\"[item.type ? 'GlobalAiChatSelfMessage' : 'GlobalAiChatMessage']\"\r\n          v-for=\"(item, index) in chatMessage\" :key=\"item.id\">\r\n          <template v-if=\"item.type\">\r\n            <div class=\"GlobalAiChatSelfMessageFile\" v-for=\"item in item.fileData\" :key=\"item.id\"\r\n              @click=\"handlePreview(item)\">\r\n              <div class=\"globalFileIcon\" :class=\"fileIcon(item?.extName)\"></div>\r\n              <div class=\"GlobalChatMessagesFileName ellipsis\">{{ item?.originalFileName || '未知文件' }}</div>\r\n              <div class=\"GlobalChatMessagesFileSize\">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>\r\n            </div>\r\n            <div class=\"GlobalAiChatSelfMessageInfo\">{{ item.content }}</div>\r\n          </template>\r\n          <template v-if=\"!item.type\">\r\n            <!-- <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" /> -->\r\n            <div class=\"GlobalAiChatMessageInfo\">\r\n              <div class=\"GlobalAiChatMessagePonder forbidSelect\" @click=\"item.ponderShow = !item.ponderShow\"\r\n                v-if=\"item.time\">\r\n                <div v-html=\"ponderIcon\"></div>\r\n                已深度思考\r\n                <span v-if=\"item.time !== '1'\">（用时 {{ item.time }}）</span>\r\n                <el-icon>\r\n                  <ArrowUpBold v-if=\"item.ponderShow\" />\r\n                  <ArrowDownBold v-if=\"!item.ponderShow\" />\r\n                </el-icon>\r\n              </div>\r\n              <div class=\"GlobalAiChatMessagePonderContent\" v-show=\"item.ponderShow\">\r\n                <GlobalMarkdown :ref=\"(el) => getElPonderRef(el, index)\" v-model=\"item.ponderContent\"\r\n                  :content=\"item.ponderContentOld\" @update=\"handleUpdate\" />\r\n              </div>\r\n              <div class=\"GlobalAiChatMessageContent\">\r\n                <GlobalMarkdown :ref=\"(el) => getElRef(el, index)\" v-model=\"item.content\" :content=\"item.contentOld\"\r\n                  :on-link-click=\"handleLinkClick\" @update=\"handleUpdate\" />\r\n                <div class=\"GlobalAiChatMessageDataList\">\r\n                  <div class=\"GlobalAiChatMessageDataItem\" v-for=\"row in item.dataList\" @click=\"handleDataList(row)\">\r\n                    {{ row.sourceName }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"GlobalAiChatMessageChart\">\r\n                  <GlobalAiChart v-for=\"(row, i) in item.chartData\" :key=\"i + 'chartData'\" :option=\"row\" />\r\n                </div>\r\n                <div class=\"GlobalAiChatMessageLoading\" v-if=\"index === chatMessage.length - 1 && loading\">\r\n                  <div class=\"answerLoading\" v-html=\"loadingIcon\"></div>\r\n                </div>\r\n                <div class=\"GlobalAiChatBodyTipsVice\" v-if=\"item.guideWord.length\">您是不是想问：</div>\r\n                <div class=\"GlobalAiChatBodyTipsItem\" v-for=\"(row, i) in item.guideWord\" :key=\"i + 'guideWord'\">\r\n                  <div class=\"GlobalAiChatBodyTips\" @click=\"handleGuideWord(row)\">\r\n                    <span v-html=\"tipsIcon\"></span>\r\n                    {{ row.question }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"GlobalAiChatMessageControls\" v-if=\"item.isControls\">\r\n                <el-tooltip content=\"复制\" placement=\"top\">\r\n                  <div class=\"GlobalAiChatMessageControlsItem\" @click=\"handleCopyMessage(item.content, index)\">\r\n                    <el-icon>\r\n                      <CopyDocument />\r\n                    </el-icon>\r\n                  </div>\r\n                </el-tooltip>\r\n                <el-tooltip content=\"重新生成\" placement=\"top\">\r\n                  <div class=\"GlobalAiChatMessageControlsItem\" @click=\"handleRetryMessage(item, index)\">\r\n                    <el-icon>\r\n                      <Refresh />\r\n                    </el-icon>\r\n                  </div>\r\n                </el-tooltip>\r\n                <el-tooltip content=\"满意度调查\" placement=\"top\">\r\n                  <div class=\"GlobalAiChatMessageControlsItem\" @click=\"handleSatisfactionSurvey(item, index)\">\r\n                    <el-icon>\r\n                      <Star />\r\n                    </el-icon>\r\n                  </div>\r\n                </el-tooltip>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"GlobalAiChatDialogueEditor\">\r\n      <div class=\"GlobalAiChatDialogueTools\" v-show=\"!toolId\">\r\n        <div class=\"GlobalAiChatDialogueTool\" v-for=\"item in aiTools\" :key=\"item.id\"\r\n          @click=\"handleToolSendMessage(item)\">\r\n          <span v-html=\"toolIcon\"></span>\r\n          {{ item.chatToolName }}\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalAiChatDialogueEditorBody\">\r\n        <div class=\"GlobalAiChatToolsActive\" v-show=\"toolId\">\r\n          <div class=\"GlobalAiChatToolsActiveName\">{{ toolName }}</div>\r\n          <div class=\"GlobalAiChatToolsActiveIcon\" @click=\"handleToolClose\">\r\n            <el-icon>\r\n              <Close />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <GlobalAiChatFile :fileList=\"fileList\" :fileData=\"fileData\" @close=\"handleClose\"\r\n          v-show=\"fileList.length || fileData.length\" />\r\n        <GlobalAiChatEditor ref=\"editorRef\" v-model=\"sendContent\" :disabled=\"disabled\" @send=\"handleSendMessage\"\r\n          @stop=\"handleStopMessage\" @uploadCallback=\"handleFileUpload\" @fileCallback=\"handleFileCallback\" />\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"dataShow\" :name=\"dataName\">\r\n      <GlobalAiChatData :data=\"dataInfo\"></GlobalAiChatData>\r\n    </xyl-popup-window>\r\n    <CustomSatisfactionModal v-model=\"satisfactionSurveyShow\" :data=\"dataInfo\"></CustomSatisfactionModal>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChat' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, onUnmounted, nextTick, watch, defineAsyncComponent } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { size2Str } from 'common/js/utils.js'\r\nimport http_stream from 'common/http/stream.js'\r\nimport { AiChatClass } from 'common/js/GlobalClass.js'\r\nimport { globalFileLocation } from 'common/config/location'\r\nimport config from 'common/config/index'\r\nimport { user, IntelligentAssistant } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst GlobalMarkdown = defineAsyncComponent(() => import('common/components/global-markdown/global-markdown.vue'))\r\nconst GlobalAiChatFile = defineAsyncComponent(() => import('./GlobalAiChatFile'))\r\nconst GlobalAiChatEditor = defineAsyncComponent(() => import('./GlobalAiChatEditor'))\r\nconst GlobalAiChatHistory = defineAsyncComponent(() => import('./GlobalAiChatHistory'))\r\nconst GlobalAiChatData = defineAsyncComponent(() => import('./GlobalAiChatData'))\r\nconst CustomSatisfactionModal = defineAsyncComponent(() => import('./CustomSatisfactionModal'))\r\nconst GlobalAiChart = defineAsyncComponent(() => import('./GlobalAiChart'))\r\nconst store = useStore()\r\nconst props = defineProps({ modelValue: { type: Boolean, default: false } })\r\nconst emit = defineEmits(['update:modelValue'])\r\nconst newIcon =\r\n  '<svg t=\"1741161744028\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"3254\" width=\"26\" height=\"26\"><path d=\"M566.464 150.336c22.144 0 39.744 17.216 40.576 39.296a40.448 40.448 0 0 1-38.08 41.792H183.488v432.128h171.2c21.312 0 38.912 16.384 40.576 37.696v52.416l66.368-76.16a39.872 39.872 0 0 1 27.456-13.952h319.04v-190.08c0-21.248 16.384-38.912 37.76-40.512h2.816c21.312 0 38.912 16.384 40.512 37.696v198.208c0 40.576-31.936 73.728-72.064 75.776H510.784l-125.76 143.808a40.832 40.832 0 0 1-71.296-23.808v-119.552H178.176c-40.576 0-73.728-32-75.776-72.128V226.112c0-40.576 32-73.728 72.064-75.776h392z m35.648 340.8c18.816 0 34.816 14.72 35.648 33.92a35.264 35.264 0 0 1-32.768 36.48h-285.44a35.328 35.328 0 0 1-2.944-70.4h285.504zM443.2 349.824c19.2 0 34.816 15.104 35.648 33.92a35.264 35.264 0 0 1-32.768 36.48H319.488a35.328 35.328 0 0 1-2.88-70.4h126.592z m335.424-229.376c18.432 0 34.048 14.336 35.2 32.768v72.896h70.528a35.264 35.264 0 0 1 2.88 70.4h-72.96v70.464c0 18.88-14.72 34.816-33.92 35.648a35.264 35.264 0 0 1-36.48-32.768V296.96h-70.464a35.264 35.264 0 0 1-2.88-70.4h72.96V155.968a34.816 34.816 0 0 1 35.2-35.584z\" p-id=\"3255\"></path></svg>'\r\nconst tipsIcon =\r\n  '<svg t=\"1741241762761\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"7848\" width=\"14\" height=\"14\"><path d=\"M115.152 356.453c-1.492-9.942-2.486-18.724-3.314-25.849-0.829-7.125-0.995-14.251-0.995-21.541 0-22.867 3.314-38.112 9.611-45.237 6.463-7.125 15.41-10.77 27.01-10.77h198.343L410.596 5.001c40.266 2.818 67.109 8.285 80.863 16.239 13.753 7.954 20.546 17.564 20.546 28.998v15.079L460.141 252.89h239.438L766.522 4.836c40.266 2.817 67.108 8.285 80.862 16.238 13.753 7.954 20.547 17.565 20.547 28.998 0 5.8-0.829 10.771-2.154 15.079l-49.71 187.739h170.34c2.817 8.617 4.309 16.902 4.309 24.855v22.701c0 21.541-3.314 36.289-9.776 44.242-6.463 7.954-15.41 11.765-27.01 11.765H788.063L710.35 643.281h200.498c1.326 10.108 2.485 19.056 3.314 27.01a217.169 217.169 0 0 1 1.159 22.701c0 37.448-12.262 56.007-36.619 56.007H682.181l-73.24 269.595c-40.265-2.816-66.942-8.285-79.867-16.072-12.925-7.954-19.387-17.564-19.387-29.164 0-5.634 0.662-10.107 2.154-12.925l56.006-211.269H326.421l-71.086 269.597c-40.265-2.817-67.606-8.286-82.022-16.074-14.416-7.953-21.541-17.564-21.541-29.163 0-2.816 0.331-4.971 0.994-6.462 0.663-1.326 0.994-3.646 0.994-6.463l58.327-211.269H39.592c-2.817-10.107-4.308-19.056-4.308-27.009V699.62c0-21.541 3.314-36.289 9.776-44.242 6.463-7.954 15.41-11.765 27.01-11.765h168.186l75.394-286.829H115.152v-0.331z m239.272 286.828H595.85l77.714-286.828H432.138l-77.714 286.828z\" p-id=\"7849\"></path></svg>'\r\nconst loadingIcon =\r\n  '<svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\"><path d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\" fill=\"#2c2c2c\" p-id=\"2363\"></path><path d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\" fill=\"#2c2c2c\" p-id=\"2364\"></path><path d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\" fill=\"#2c2c2c\" p-id=\"2365\"></path><path d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\" fill=\"#2c2c2c\" p-id=\"2366\"></path><path d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\" fill=\"#2c2c2c\" p-id=\"2367\"></path><path d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2368\"></path><path d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\" fill=\"#2c2c2c\" p-id=\"2369\"></path><path d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\" fill=\"#2c2c2c\" p-id=\"2370\"></path></svg>'\r\nconst toolIcon =\r\n  '<svg t=\"1741338779911\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"9993\" width=\"16\" height=\"16\"><path d=\"M707.2 350.976l-198.496 115.264-195.072-111.712c-37.088-21.12-68.736 34.528-31.648 55.616l198.72 113.792v256.736a32 32 0 0 0 64 0v-258.016c0-1.056-0.512-1.952-0.608-3.008l194.752-113.088c37.088-21.056 5.44-76.704-31.648-55.584z\" p-id=\"9994\"></path><path d=\"M880.288 232.48L560.192 45.12a95.648 95.648 0 0 0-96.64 0L143.68 232.48A96.64 96.64 0 0 0 96 315.904v397.664c0 34.784 18.624 66.88 48.736 84l320 181.92a95.52 95.52 0 0 0 94.496 0l320-181.92A96.576 96.576 0 0 0 928 713.568V315.904a96.64 96.64 0 0 0-47.712-83.424zM864 713.568c0 11.584-6.208 22.304-16.256 28l-320 181.92a31.776 31.776 0 0 1-31.488 0l-320-181.92A32.192 32.192 0 0 1 160 713.568V315.904c0-11.456 6.048-22.016 15.904-27.808l319.872-187.36a31.84 31.84 0 0 1 32.192 0l320.128 187.392c9.856 5.728 15.904 16.32 15.904 27.776v397.664z\" p-id=\"9995\"></path></svg>'\r\nconst ponderIcon =\r\n  '<svg t=\"1741658991857\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"11203\" width=\"16\" height=\"16\"><path d=\"M886.592 369.152c-42.24 89.28-116.48 190.848-211.456 285.76-94.976 94.976-196.48 169.216-285.824 211.52-44.352 20.992-88.96 35.712-130.24 39.168-40.832 3.456-87.68-3.712-122.432-38.4-34.752-34.816-41.92-81.664-38.464-122.496 3.456-41.216 18.176-85.888 39.168-130.24 42.304-89.28 116.544-190.912 211.456-285.824 94.976-94.912 196.544-169.152 285.824-211.456 44.416-21.056 88.96-35.712 130.24-39.232 40.832-3.456 87.68 3.712 122.496 38.464 34.752 34.752 41.92 81.664 38.4 122.496-3.456 41.216-18.112 85.824-39.168 130.24zM629.888 609.664c182.272-182.272 277.312-382.848 212.224-448-65.152-65.088-265.728 29.952-448 212.224-182.336 182.336-277.376 382.912-212.224 448 65.088 65.152 265.664-29.888 448-212.224z\" p-id=\"11204\"></path><path d=\"M137.344 369.152c42.304 89.28 116.544 190.848 211.52 285.76 94.912 94.976 196.48 169.216 285.76 211.52 44.416 20.992 88.96 35.712 130.24 39.168 40.832 3.456 87.68-3.712 122.496-38.4 34.752-34.816 41.92-81.664 38.4-122.496-3.456-41.216-18.112-85.888-39.168-130.24-42.24-89.28-116.48-190.912-211.456-285.824-94.912-94.912-196.48-169.152-285.824-211.456-44.352-21.056-88.96-35.712-130.24-39.232-40.832-3.456-87.68 3.712-122.432 38.464-34.752 34.752-41.92 81.664-38.464 122.496 3.456 41.216 18.176 85.824 39.168 130.24z m256.768 240.512c-182.336-182.272-277.376-382.848-212.224-448 65.088-65.088 265.664 29.952 448 212.224 182.272 182.336 277.312 382.912 212.224 448-65.152 65.152-265.728-29.888-448-212.224z\" p-id=\"11205\"></path><path d=\"M576 512a64 64 0 1 1-128 0 64 64 0 0 1 128 0z\" p-id=\"11206\"></path></svg>'\r\nconst elShow = computed({\r\n  get () {\r\n    return props.modelValue\r\n  },\r\n  set (value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst elRefs = ref([])\r\nconst getElRef = (el, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  if (el) elRefs.value[index] = el\r\n}\r\nconst elRef = computed(() => elRefs.value[elRefs.value.length - 1])\r\nconst elPonderRefs = ref([])\r\nconst getElPonderRef = (el, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  if (el) elPonderRefs.value[index] = el\r\n}\r\nconst elPonderRef = computed(() => elPonderRefs.value[elPonderRefs.value.length - 1])\r\nconst AiChatId = ref('')\r\nconst AiChatCode = ref('test_chat')\r\nconst AiChatModule = ref('')\r\nconst AiChatModuleId = ref('')\r\nconst elIndex = ref(0)\r\nconst AiChatParams = ref({})\r\nconst AiChatContent = ref('')\r\nconst aiTools = ref([])\r\nconst aiWords = ref([])\r\nconst chatId = ref('')\r\nconst chatObj = ref({})\r\nconst chatContent = ref('')\r\nconst chatMessage = ref([])\r\nconst chatMessageTotal = ref(0)\r\nconst chatMessageUpdate = ref(0)\r\nconst wrapScrollHeight = ref(0)\r\nconst chatHistoryRef = ref()\r\nconst scrollRef = ref()\r\nconst editorRef = ref()\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst sendContent = ref('')\r\nconst isScroll = ref(false)\r\nconst toolId = ref('')\r\nconst toolName = ref('')\r\nconst toolIsParam = ref(0)\r\nconst toolRequired = ref(0)\r\n\r\nlet currentRequest = null\r\nconst loading = ref(false)\r\nconst disabled = ref(false)\r\nconst isStreaming = ref(false)\r\nlet startTime = null\r\nlet endTime = null\r\n\r\nconst dataName = ref('')\r\nconst dataInfo = ref({})\r\nconst dataShow = ref(false)\r\nconst satisfactionSurveyShow = ref(false)\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nconst formatDuring = (mss) => {\r\n  const days = parseInt(mss / (1000 * 60 * 60 * 24))\r\n  const hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))\r\n  const minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60))\r\n  const seconds = (mss % (1000 * 60)) / 1000\r\n  var time = ''\r\n  if (days > 0) time += `${days} 天 `\r\n  if (hours > 0) time += `${hours} 小时 `\r\n  if (minutes > 0) time += `${minutes} 分钟 `\r\n  if (seconds > 0) time += `${seconds} 秒 `\r\n  return time\r\n}\r\nconst handlePreview = (row) => {\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nconst handleClick = () => {\r\n  elShow.value = !elShow.value\r\n}\r\nconst handleNewDialogue = () => {\r\n  elRefs.value = []\r\n  elPonderRefs.value = []\r\n  chatMessageTotal.value = 0\r\n  handleToolClose()\r\n  handleStopMessage()\r\n  chatId.value = guid()\r\n  chatContent.value = ''\r\n  chatMessage.value = []\r\n}\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleSelect = (data, type) => {\r\n  chatMessageTotal.value = 0\r\n  chatContent.value = data.userQuestion\r\n  if (!type) {\r\n    handleStopMessage()\r\n    chatMessage.value = []\r\n    handleChatMessage()\r\n  } else {\r\n    chatObj.value[AiChatCode.value] = { chatId: data.id, businessId: data.businessId || '' }\r\n  }\r\n}\r\nconst handleChatMessage = async (value) => {\r\n  const { data, total } = await api.aigptChatLogsList({\r\n    pageNo: value || 1,\r\n    pageSize: value ? 1 : 10,\r\n    isAsc: 1,\r\n    query: { chatId: chatId.value }\r\n  })\r\n  chatMessageTotal.value = total\r\n  if (value) {\r\n    let updateVal = 0\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      if (item.reasoning) updateVal += 1\r\n      if (item.answer) updateVal += 1\r\n      chatMessage.value.unshift({\r\n        id: guid(),\r\n        type: false,\r\n        ponderShow: true,\r\n        isControls: true,\r\n        content: '',\r\n        contentOld: item.answer,\r\n        ponderContent: '',\r\n        ponderContentOld: item.reasoning,\r\n        time: item.reasoning ? '1' : '',\r\n        dataList: [],\r\n        fileData: [],\r\n        chartData: [],\r\n        guideWord: []\r\n      })\r\n      chatMessage.value.unshift({\r\n        id: guid(),\r\n        type: true,\r\n        ponderShow: true,\r\n        isControls: true,\r\n        content: item.userQuestion,\r\n        contentOld: '',\r\n        ponderContent: '',\r\n        ponderContentOld: '',\r\n        time: '',\r\n        dataList: [],\r\n        fileData: item.attachments,\r\n        chartData: [],\r\n        guideWord: []\r\n      })\r\n    }\r\n    chatMessageUpdate.value = updateVal\r\n    nextTick(() => {\r\n      scrollElHeight()\r\n    })\r\n  } else {\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      chatMessage.value.push({\r\n        id: guid(),\r\n        type: true,\r\n        ponderShow: true,\r\n        isControls: true,\r\n        content: item.userQuestion,\r\n        contentOld: '',\r\n        ponderContent: '',\r\n        ponderContentOld: '',\r\n        time: '',\r\n        dataList: [],\r\n        fileData: item.attachments,\r\n        chartData: [],\r\n        guideWord: []\r\n      })\r\n      chatMessage.value.push({\r\n        id: guid(),\r\n        type: false,\r\n        ponderShow: true,\r\n        isControls: true,\r\n        content: '',\r\n        contentOld: item.answer,\r\n        ponderContent: '',\r\n        ponderContentOld: item.reasoning,\r\n        time: item.reasoning ? '1' : '',\r\n        dataList: [],\r\n        fileData: [],\r\n        chartData: [],\r\n        guideWord: []\r\n      })\r\n    }\r\n    isScroll.value = false\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n  }\r\n}\r\nconst handleUpdate = () => {\r\n  if (!isScroll.value && wrapScrollHeight.value) {\r\n    nextTick(() => {\r\n      scrollElHeight()\r\n    })\r\n    chatMessageUpdate.value = chatMessageUpdate.value - 1\r\n  } else {\r\n    chatMessageUpdate.value = 0\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n  }\r\n}\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (scrollTop === 0) {\r\n    wrapScrollHeight.value = scrollRef.value.wrapRef.scrollHeight\r\n    if (chatMessageTotal.value && chatMessageTotal.value > chatMessage.value.length / 2) {\r\n      handleChatMessage(chatMessage.value.length / 2 + 1)\r\n    }\r\n  }\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 52) {\r\n    isScroll.value = false\r\n  } else {\r\n    isScroll.value = true\r\n  }\r\n}\r\nconst scrollDown = () => {\r\n  if (isScroll.value) return\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight\r\n}\r\nconst scrollElHeight = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight - wrapScrollHeight.value\r\n}\r\nconst handleGuideWord = (data) => {\r\n  console.log('[]', data)\r\n  if (isStreaming.value) return ElMessage({ type: 'warning', message: '请先完成上一次对话再进行新对话！' })\r\n  handleCloseMessage()\r\n  loading.value = true\r\n  disabled.value = true\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: true,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: data.question,\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: fileData.value,\r\n    chartData: []\r\n  })\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: false,\r\n    ponderShow: true,\r\n    isControls: false,\r\n    content: '',\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: [],\r\n    chartData: [],\r\n    guideWord: []\r\n  })\r\n  nextTick(() => {\r\n    scrollDown()\r\n    handleHttpStream(data)\r\n    editorRef.value?.handleSetFile([])\r\n    handleToolClose()\r\n  })\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (isStreaming.value) return ElMessage({ type: 'warning', message: '请先完成上一次对话再进行新对话！' })\r\n  if (toolRequired.value && !fileData.value.length)\r\n    return ElMessage({ type: 'warning', message: `请先上传相关资料在进行${toolName.value}!` })\r\n  handleCloseMessage()\r\n  loading.value = true\r\n  disabled.value = true\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: true,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: value,\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: fileData.value,\r\n    chartData: []\r\n  })\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: false,\r\n    ponderShow: true,\r\n    isControls: false,\r\n    content: '',\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: [],\r\n    chartData: [],\r\n    guideWord: []\r\n  })\r\n  const fileId = fileData.value.map((v) => v.id).join(',')\r\n  const defaultParams = toolId.value\r\n    ? { question: value, tool: toolId.value, attachmentIds: fileId }\r\n    : { question: value, attachmentIds: fileId }\r\n  const params = toolIsParam.value ? { ...defaultParams, param: { pageContent: AiChatContent.value } } : defaultParams\r\n  nextTick(() => {\r\n    scrollDown()\r\n    handleHttpStream(params)\r\n    editorRef.value?.handleSetFile([])\r\n    handleToolClose()\r\n  })\r\n}\r\nconst handleTips = (text) => {\r\n  const parts = text.split(/(\\{[^}]+\\})/)\r\n  const result = parts\r\n    .map((part) => {\r\n      if (part.startsWith('{') && part.endsWith('}')) {\r\n        return { value: part.slice(1, -1), type: true }\r\n      } else if (part.trim() !== '') {\r\n        return { value: part, type: false }\r\n      }\r\n    })\r\n    .filter((item) => item !== undefined)\r\n  return result\r\n}\r\nconst handleToolClose = () => {\r\n  toolId.value = ''\r\n  toolName.value = ''\r\n  toolIsParam.value = 0\r\n  toolRequired.value = 0\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n}\r\nconst handleLinkClick = ({ href, text, event }) => {\r\n  console.log('链接被点击:', href, text)\r\n  if (text === '查看原文比对') {\r\n    // store.commit('setOpenRoute', { name: '查看原文比对', path: '/VersionComparisonAi', query: { chatId: chatId.value } })\r\n    const token = sessionStorage.getItem('token') || ''\r\n    window.open(`${config.mainPath}VersionComparisonAi?chatId=${chatId.value}&token=${token}`, '_blank')\r\n  } else {\r\n    window.open(href, '_blank')\r\n  }\r\n}\r\nconst handleToolSendMessage = (value) => {\r\n  const { id, chatToolName, isParam, userPromptTip, needTool } = value\r\n  if (userPromptTip) {\r\n    toolId.value = id\r\n    toolName.value = chatToolName\r\n    toolIsParam.value = isParam\r\n    toolRequired.value = needTool\r\n    editorRef.value?.handleInsertPlaceholder(handleTips(userPromptTip))\r\n    return\r\n  }\r\n  if (needTool && !fileData.value.length)\r\n    return ElMessage({ type: 'warning', message: `请先上传相关资料再进行${chatToolName}！` })\r\n  const finallySendContent = isParam ? chatToolName : AiChatContent.value || sendContent.value\r\n  if (!finallySendContent) return ElMessage({ type: 'warning', message: `请先输入内容再进行${chatToolName}！` })\r\n  handleStopMessage()\r\n  loading.value = true\r\n  disabled.value = true\r\n  // chatId.value = guid()\r\n  // chatContent.value = ''\r\n  // chatMessage.value = []\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: true,\r\n    ponderShow: true,\r\n    isControls: true,\r\n    content: chatToolName,\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: fileData.value,\r\n    chartData: [],\r\n    guideWord: []\r\n  })\r\n  chatMessage.value.push({\r\n    id: guid(),\r\n    type: false,\r\n    ponderShow: true,\r\n    isControls: false,\r\n    content: '',\r\n    contentOld: '',\r\n    ponderContent: '',\r\n    ponderContentOld: '',\r\n    time: '',\r\n    dataList: [],\r\n    fileData: [],\r\n    chartData: [],\r\n    guideWord: []\r\n  })\r\n  const fileId = fileData.value.map((v) => v.id).join(',')\r\n  const defaultParams = { question: finallySendContent, tool: id, attachmentIds: fileId }\r\n  const params = isParam ? { ...defaultParams, param: { pageContent: AiChatContent.value } } : defaultParams\r\n  nextTick(() => {\r\n    scrollDown()\r\n    handleHttpStream(params, fileData.value)\r\n    editorRef.value?.handleSetFile([])\r\n    editorRef.value?.handleSetContent('')\r\n  })\r\n}\r\nconst stringToJson = (str) => {\r\n  // 替换属性名\r\n  str = str.replace(/(\\w+):/g, '\"$1\":')\r\n  // 替换单引号为双引号\r\n  str = str.replace(/'/g, '\"')\r\n  const obj = JSON.parse(str)\r\n  return obj\r\n}\r\nconst handleHttpStream = async (params = {}) => {\r\n  isStreaming.value = true\r\n  try {\r\n    let AiChatParam = {}\r\n    if (params.param && AiChatParams.value.param) {\r\n      AiChatParam = { param: { ...params.param, ...AiChatParams.value.param } }\r\n    }\r\n    AiChatParam = { ...params, ...AiChatParams.value, ...AiChatParam }\r\n    startTime = new Date()\r\n    currentRequest = http_stream('/aigpt/chatStream', {\r\n      body: JSON.stringify({\r\n        chatBusinessScene: AiChatCode.value,\r\n        chatId: chatId.value,\r\n        ...AiChatParam\r\n      }),\r\n      onMessage (event) {\r\n        // if (event.data === '{\\\"status\\\":\\\"running\\\",\\\"name\\\":\\\"AI 对话\\\"}') loading.value = false\r\n        loading.value = false\r\n        if (event.data !== '[DONE]') {\r\n          const data = JSON.parse(event.data)\r\n          console.log('[]', data)\r\n          if (Array.isArray(data)) {\r\n            // console.log('[]', data)\r\n            let quoteList = []\r\n            let newChartData = []\r\n            let newGuideWord = []\r\n            for (let index = 0; index < data.length; index++) {\r\n              const item = data[index]\r\n              if (typeof item === 'string') {\r\n                newGuideWord.push({ ...AiChatParam, question: item })\r\n              } else {\r\n                if (item?.quoteList?.length) {\r\n                  for (let i = 0; i < item?.quoteList.length; i++) {\r\n                    const row = item?.quoteList[i]\r\n                    quoteList.push({ ...row, markdownContent: row.q })\r\n                  }\r\n                }\r\n                if (item?.echartItem) {\r\n                  newChartData.push(stringToJson(item?.echartItem))\r\n                }\r\n              }\r\n            }\r\n            chatMessage.value[chatMessage.value.length - 1].dataList = quoteList\r\n            chatMessage.value[chatMessage.value.length - 1].chartData = newChartData\r\n            chatMessage.value[chatMessage.value.length - 1].guideWord = newGuideWord\r\n          } else {\r\n            // console.log('{}', data)\r\n            const choice = data?.choices || [{}]\r\n            const details = choice[0]?.delta || {}\r\n            if (Object.prototype.hasOwnProperty.call(details, 'reasoning_content')) {\r\n              elPonderRef.value?.enqueueRender(details.reasoning_content || '')\r\n              if (chatMessage.value[chatMessage.value.length - 1].time) {\r\n                startTime = null\r\n                endTime = null\r\n              } else {\r\n                endTime = new Date()\r\n                const executionTime = endTime - startTime\r\n                chatMessage.value[chatMessage.value.length - 1].time = formatDuring(executionTime)\r\n              }\r\n            }\r\n            if (Object.prototype.hasOwnProperty.call(details, 'content'))\r\n              elRef.value?.enqueueRender(details.content || '')\r\n            nextTick(() => {\r\n              scrollDown()\r\n            })\r\n          }\r\n        } else {\r\n          // console.log(event.data)\r\n          elRef.value?.enqueueRender('')\r\n          nextTick(() => {\r\n            scrollDown()\r\n          })\r\n          disabled.value = false\r\n          isStreaming.value = false\r\n          chatMessage.value[chatMessage.value.length - 1].isControls = true\r\n          if (!chatContent.value) chatHistoryRef.value?.refresh()\r\n        }\r\n      },\r\n      onError (err) {\r\n        console.log('流式接口错误:', err)\r\n      },\r\n      onClose () {\r\n        loading.value = false\r\n        disabled.value = false\r\n        isStreaming.value = false\r\n        console.log('流式接口关闭')\r\n      }\r\n    })\r\n    await currentRequest.promise\r\n  } catch (error) {\r\n    loading.value = false\r\n    disabled.value = false\r\n    isStreaming.value = false\r\n    elRef.value?.enqueueRender('服务器繁忙，请稍后再试。')\r\n    nextTick(() => {\r\n      scrollDown()\r\n    })\r\n    console.error('启动流式接口失败:', error)\r\n  } finally {\r\n    // currentRequest = null\r\n  }\r\n}\r\nconst handleDataList = (row) => {\r\n  dataName.value = row.sourceName\r\n  dataInfo.value = row\r\n  dataShow.value = true\r\n}\r\nconst handleCopyMessage = (content, i) => {\r\n  const index = (i + 1) / 2 - 1\r\n  const copyContent = elRefs.value[index]?.elRef?.innerText || content\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = copyContent\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) {\r\n    ElMessage({ message: '复制成功', type: 'success' })\r\n  }\r\n  document.body.removeChild(textarea)\r\n}\r\nconst handleRetryMessage = (item, index) => {\r\n  const length = chatMessage.value.length - 1\r\n  if (index === length && currentRequest) {\r\n    loading.value = true\r\n    disabled.value = true\r\n    isStreaming.value = true\r\n    chatMessage.value[length] = {\r\n      id: guid(),\r\n      type: false,\r\n      ponderShow: true,\r\n      isControls: false,\r\n      content: '',\r\n      contentOld: '',\r\n      ponderContent: '',\r\n      ponderContentOld: '',\r\n      time: '',\r\n      dataList: [],\r\n      fileData: [],\r\n      chartData: [],\r\n      guideWord: []\r\n    }\r\n    nextTick(async () => {\r\n      await currentRequest.retry()\r\n    })\r\n  } else {\r\n    isScroll.value = false\r\n    fileData.value = chatMessage.value[index - 1].fileData\r\n    handleSendMessage(chatMessage.value[index - 1].content)\r\n  }\r\n}\r\nconst handleSatisfactionSurvey = (item, index) => {\r\n  // 获取当前回答对应的用户问题\r\n  const questionIndex = index - 1\r\n  const question = chatMessage.value[questionIndex]?.content || ''\r\n  const answerIndex = ((index + 1) / 2) - 1\r\n  const answer = elRefs.value[answerIndex]?.elRef?.innerText || item.content || item.contentOld || ''\r\n  // 构建传递给满意度调查的数据\r\n  dataInfo.value = {\r\n    ...item,\r\n    question: question,\r\n    answer: answer,\r\n    chatId: chatId.value,\r\n    chatContent: chatContent.value\r\n  }\r\n  console.log('dataInfo.value==>', dataInfo.value)\r\n  satisfactionSurveyShow.value = true\r\n}\r\nconst handleCloseMessage = () => {\r\n  currentRequest = null\r\n  loading.value = false\r\n  disabled.value = false\r\n  isStreaming.value = false\r\n}\r\nconst handleStopMessage = () => {\r\n  if (currentRequest) {\r\n    currentRequest.abort()\r\n    loading.value = false\r\n    disabled.value = false\r\n    isStreaming.value = false\r\n    console.log('启动流式接口停止')\r\n    if (!chatContent.value) chatHistoryRef.value?.refresh()\r\n  }\r\n  handleCloseMessage()\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const { data } = await api.aigptChatSceneDetail({ key: guid(), query: { chatSceneCode: AiChatCode.value } })\r\n  aiTools.value = data?.tools || []\r\n  aiWords.value = data?.promptWords || []\r\n  chatObj.value[AiChatCode.value] = { chatId: chatId.value, businessId: AiChatModuleId.value }\r\n}\r\nconst handleHistoryMessage = async (code, businessId) => {\r\n  elIndex.value = 1\r\n  chatHistoryRef.value?.handleCurrentChat(code, businessId)\r\n}\r\nconst isEmptyObject = (obj) => {\r\n  if (typeof obj !== 'object' || obj === null) return false\r\n  return Object.keys(obj).length !== 0\r\n}\r\n/**\r\n * AiChatCode 场景code String\r\n * --主工程调用 store.commit('setAiChatCode', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatCode: '' })\r\n *\r\n * AiChatConfig 批量设置参数 Object\r\n * AiChatConfig可以设置 AiChatWindow AiChatParams AiChatContent AiChatSetContent AiChatAddContent AiChatSendMessage AiChatToolSendMessage\r\n * --主工程调用 store.commit('setAiChatConfig', { AiChatWindow, AiChatParams, AiChatContent, AiChatSetContent, AiChatAddContent, AiChatSendMessage, AiChatToolSendMessage })\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatConfig: { AiChatWindow, AiChatParams, AiChatContent, AiChatSetContent, AiChatAddContent, AiChatSendMessage, AiChatToolSendMessage } })\r\n *\r\n * AiChatWindow 窗口是否显示 Boolean\r\n * --主工程调用 store.commit('setAiChatWindow', true / false)\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatWindow: true / false })\r\n *\r\n * AiChatParams 其他传参 Object\r\n * --主工程调用 store.commit('setAiChatParams', {})\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatParams: {} })\r\n *\r\n * AiChatContent 内容参数 String\r\n * --主工程调用 store.commit('setAiChatContent', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatContent: '' })\r\n *\r\n * AiChatSetContent 替换内容参数 String\r\n * --主工程调用 store.commit('setAiChatSetContent', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatSetContent: '' })\r\n *\r\n * AiChatAddContent 追加内容参数 String\r\n * --主工程调用 store.commit('setAiChatAddContent', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatAddContent: '' })\r\n *\r\n * AiChatSendMessage 发送消息 String\r\n * --主工程调用 store.commit('setAiChatSendMessage', '')\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatSendMessage: '' })\r\n *\r\n * AiChatToolSendMessage 点击工具 Object\r\n * --主工程调用 store.commit('setAiChatToolSendMessage', {})\r\n * --子工程调用 qiankunMicro.setGlobalState({ AiChatToolSendMessage: {} })\r\n */\r\nwatch(\r\n  () => store.state.AiChatCode,\r\n  (val) => {\r\n    if (val) {\r\n      AiChatCode.value = val\r\n      handleToolClose()\r\n      handleNewDialogue()\r\n      aigptChatSceneDetail()\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatModuleId,\r\n  (val) => {\r\n    AiChatModuleId.value = val\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatConfig,\r\n  (val) => {\r\n    if (isEmptyObject(val)) {\r\n      if (val.hasOwnProperty('AiChatWindow')) elShow.value = !!val.AiChatWindow\r\n      if (val.hasOwnProperty('AiChatFile')) editorRef.value?.handleSetFile(val.AiChatFile)\r\n      if (val.hasOwnProperty('AiChatParams')) AiChatParams.value = val.AiChatParams\r\n      if (val.hasOwnProperty('AiChatContent')) AiChatContent.value = val.AiChatContent\r\n      if (val.hasOwnProperty('AiChatSetContent')) editorRef.value?.handleSetContent(val.AiChatSetContent)\r\n      if (val.hasOwnProperty('AiChatAddContent')) editorRef.value?.handleAddContent(val.AiChatAddContent)\r\n      if (val.hasOwnProperty('AiChatSendMessage')) if (val.AiChatSendMessage) handleSendMessage(val.AiChatSendMessage)\r\n      if (val.hasOwnProperty('AiChatToolSendMessage'))\r\n        if (isEmptyObject(val.AiChatToolSendMessage)) handleToolSendMessage(val.AiChatToolSendMessage)\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatWindow,\r\n  (val) => {\r\n    elShow.value = !!val\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatFile,\r\n  (val) => {\r\n    editorRef.value?.handleSetFile(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatParams,\r\n  (val) => {\r\n    AiChatParams.value = val\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatContent,\r\n  (val) => {\r\n    AiChatContent.value = val\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatSetContent,\r\n  (val) => {\r\n    editorRef.value?.handleSetContent(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatAddContent,\r\n  (val) => {\r\n    editorRef.value?.handleAddContent(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatSendMessage,\r\n  (val) => {\r\n    if (val) handleSendMessage(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => store.state.AiChatToolSendMessage,\r\n  (val) => {\r\n    if (isEmptyObject(val)) handleToolSendMessage(val)\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => elShow.value,\r\n  (val) => {\r\n    if (val && elIndex.value) {\r\n      elIndex.value = 0\r\n      nextTick(() => {\r\n        scrollDown()\r\n      })\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\nconst handleAiChatConfig = (data) => {\r\n  const {\r\n    AiChatId: newAiChatId = '',\r\n    AiChatCode: newAiChatCode = '',\r\n    AiChatModule: newAiChatModule = '',\r\n    AiChatModuleId: newAiChatModuleId = '',\r\n    AiChatWindow: newAiChatWindow = false,\r\n    AiChatFile: newAiChatFile = [],\r\n    AiChatClearFile: newAiChatClearFile = false,\r\n    AiChatParams: newAiChatParams = {},\r\n    AiChatContent: newAiChatContent = ''\r\n  } = data\r\n  AiChatId.value = newAiChatId\r\n  AiChatCode.value = newAiChatCode\r\n  AiChatModule.value = newAiChatModule\r\n  AiChatModuleId.value = newAiChatModuleId\r\n  elShow.value = typeof newAiChatWindow === 'boolean' ? newAiChatWindow : false\r\n  if ((typeof newAiChatClearFile === 'boolean' ? newAiChatClearFile : false) || newAiChatFile.length) {\r\n    editorRef.value?.handleSetFile(newAiChatFile)\r\n  }\r\n  AiChatParams.value = newAiChatParams\r\n  AiChatContent.value = newAiChatContent\r\n}\r\nconst handleAiChatEditor = (type = true, content = '') => {\r\n  const newType = typeof type === 'boolean' ? type : true\r\n  if (newType) editorRef.value?.handleSetContent(content)\r\n  if (!newType) editorRef.value?.handleAddContent(content)\r\n}\r\nconst handleAiChatHistory = (newAiChatCode = '', newAiChatModuleId = '') => {\r\n  handleToolClose()\r\n  handleNewDialogue()\r\n  aigptChatSceneDetail()\r\n  if (newAiChatCode) handleHistoryMessage(newAiChatCode, newAiChatModuleId)\r\n}\r\nconst handleAiChatSend = (content) => {\r\n  if (content) {\r\n    elShow.value = true\r\n    handleSendMessage(content)\r\n  }\r\n}\r\nconst handleAiChatToolSend = (data) => {\r\n  if (isEmptyObject(data)) {\r\n    elShow.value = true\r\n    handleToolSendMessage(data)\r\n  }\r\n}\r\nAiChatClass.prototype.AiChatConfig = handleAiChatConfig\r\nAiChatClass.prototype.AiChatEditor = handleAiChatEditor\r\nAiChatClass.prototype.AiChatHistory = handleAiChatHistory\r\nAiChatClass.prototype.AiChatSend = handleAiChatSend\r\nAiChatClass.prototype.AiChatToolSend = handleAiChatToolSend\r\nonMounted(() => {\r\n  handleNewDialogue()\r\n  aigptChatSceneDetail()\r\n})\r\nonUnmounted(() => { })\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalAiChat {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 12px 0;\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .GlobalAiChatClose {\r\n    width: 38px;\r\n    height: 32px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 20px;\r\n    border-radius: 2px;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: #fff;\r\n      background-color: rgba($color: red, $alpha: 0.6);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatLogo {\r\n    width: 62px;\r\n    height: 62px;\r\n    position: absolute;\r\n    top: 16px;\r\n    left: 12px;\r\n\r\n    .zy-el-image {\r\n      width: 62px;\r\n      height: 62px;\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatHead {\r\n    width: 100%;\r\n    height: 68px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-end;\r\n    padding: 0 12px 0 82px;\r\n\r\n    .GlobalAiChatHeadName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n\r\n    .GlobalAiChatHeadText {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      min-height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 8px);\r\n      max-height: calc((var(--zy-text-font-size) * var(--zy-line-height)) * 2);\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-text-color-secondary);\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatDialogueBody {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    padding: 0 6px;\r\n\r\n    .GlobalAiChatDialogue {\r\n      padding: 0 6px;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n\r\n    .GlobalAiChatDialogueNew,\r\n    .GlobalAiChatDialogueHistory {\r\n      width: 38px;\r\n      height: 38px;\r\n      min-width: 38px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border-radius: 2px;\r\n      cursor: pointer;\r\n\r\n      span {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        path {\r\n          fill: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatDialogueNew {\r\n      &:hover {\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        span {\r\n          path {\r\n            fill: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatDialogueHistory {\r\n      &:hover {\r\n        background: var(--zy-el-color-info-light-9);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatScroll {\r\n    width: 100%;\r\n    flex: 1;\r\n\r\n    .GlobalAiChatBody {\r\n      width: 100%;\r\n      padding: 0 12px;\r\n\r\n      .GlobalAiChatBodyTipsBody {\r\n        width: 100%;\r\n        padding-top: 12px;\r\n      }\r\n\r\n      .GlobalAiChatBodyTipsVice {\r\n        width: 100%;\r\n        padding: 6px 0;\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-text-font-size);\r\n        color: var(--zy-el-text-color-secondary);\r\n      }\r\n\r\n      .GlobalAiChatBodyTipsItem {\r\n        width: 100%;\r\n        padding: 6px 0;\r\n\r\n        .GlobalAiChatBodyTips {\r\n          display: inline-block;\r\n          padding: 6px 12px;\r\n          border-radius: 6px;\r\n          line-height: var(--zy-line-height);\r\n          color: var(--zy-el-text-color-regular);\r\n          font-size: var(--zy-text-font-size);\r\n          background: var(--zy-el-color-info-light-9);\r\n          cursor: pointer;\r\n\r\n          span {\r\n            width: 14px;\r\n            display: inline-block;\r\n            line-height: 1.2;\r\n            margin-right: 6px;\r\n            vertical-align: middle;\r\n\r\n            svg {\r\n              vertical-align: top;\r\n\r\n              path {\r\n                fill: var(--zy-el-color-primary);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .GlobalAiChatMessage,\r\n      .GlobalAiChatSelfMessage {\r\n        padding: 12px 0;\r\n      }\r\n\r\n      .GlobalAiChatMessage {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding-right: 12px;\r\n\r\n        .zy-el-image {\r\n          width: 32px;\r\n          height: 32px;\r\n        }\r\n\r\n        .GlobalAiChatMessageInfo {\r\n          width: 100%;\r\n          // width: calc(100% - 46px);\r\n          display: inline-block;\r\n          background: #fff;\r\n          position: relative;\r\n\r\n          .GlobalAiChatMessageLoading {\r\n            width: 100%;\r\n            height: 32px;\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n\r\n            @keyframes circleRoate {\r\n              from {\r\n                transform: translateY(-50%) rotate(0deg);\r\n              }\r\n\r\n              to {\r\n                transform: translateY(-50%) rotate(360deg);\r\n              }\r\n            }\r\n\r\n            .answerLoading {\r\n              width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n              height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n              position: absolute;\r\n              top: 50%;\r\n              left: 0;\r\n              z-index: 3;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              animation: circleRoate 1s infinite linear;\r\n\r\n              path {\r\n                fill: var(--zy-el-color-primary);\r\n              }\r\n            }\r\n\r\n            .answerLoading+.QuestionsAndAnswersChatText {\r\n              color: var(--zy-el-color-primary);\r\n              padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            }\r\n          }\r\n\r\n          .GlobalAiChatMessagePonder {\r\n            height: 32px;\r\n            display: inline-flex;\r\n            align-items: center;\r\n            padding: 0 12px;\r\n            border-radius: 6px;\r\n            line-height: var(--zy-line-height);\r\n            font-size: var(--zy-text-font-size);\r\n            color: var(--zy-el-text-color-primary);\r\n            background: var(--zy-el-color-info-light-9);\r\n            margin-bottom: 12px;\r\n            cursor: pointer;\r\n\r\n            div {\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              margin-right: 6px;\r\n            }\r\n\r\n            span {\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              color: var(--zy-el-text-color-primary);\r\n            }\r\n          }\r\n\r\n          .GlobalAiChatMessagePonderContent {\r\n            width: 100%;\r\n            padding-left: 12px;\r\n            border-left: 2px solid var(--zy-el-border-color);\r\n\r\n            * {\r\n              color: var(--zy-el-text-color-secondary);\r\n            }\r\n          }\r\n\r\n          .GlobalAiChatMessageContent {\r\n            width: 100%;\r\n            padding-top: calc((32px - (var(--zy-line-height) * var(--zy-text-font-size))) / 2);\r\n\r\n            .GlobalAiChatMessageDataList {\r\n              padding-top: 12px;\r\n\r\n              .GlobalAiChatMessageDataItem {\r\n                color: var(--zy-el-color-primary);\r\n                line-height: var(--zy-line-height);\r\n                font-size: var(--zy-text-font-size);\r\n                cursor: pointer;\r\n              }\r\n            }\r\n          }\r\n\r\n          .GlobalAiChatMessageControls {\r\n            width: 100%;\r\n            display: flex;\r\n            align-items: center;\r\n            padding-top: 12px;\r\n\r\n            .GlobalAiChatMessageControlsItem {\r\n              width: 32px;\r\n              height: 32px;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              border-radius: 6px;\r\n              cursor: pointer;\r\n              margin-right: 2px;\r\n\r\n              &:hover {\r\n                background: var(--zy-el-color-info-light-8);\r\n              }\r\n\r\n              .zy-el-icon {\r\n                font-size: 20px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .GlobalAiChatSelfMessage {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        align-items: flex-end;\r\n        flex-direction: column;\r\n        padding-left: 46px;\r\n        padding-right: 12px;\r\n\r\n        .GlobalAiChatSelfMessageFile {\r\n          width: 280px;\r\n          height: 52px;\r\n          display: inline-flex;\r\n          flex-direction: column;\r\n          justify-content: center;\r\n          background: #fff;\r\n          position: relative;\r\n          padding: 0 40px 0 12px;\r\n          border-radius: var(--el-border-radius-base);\r\n          border: 1px solid var(--zy-el-border-color-light);\r\n          background: var(--zy-el-color-info-light-9);\r\n          word-wrap: break-word;\r\n          white-space: pre-wrap;\r\n          cursor: pointer;\r\n          margin-bottom: 12px;\r\n\r\n          .GlobalChatMessagesFileName {\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n            padding-bottom: 2px;\r\n          }\r\n\r\n          .GlobalChatMessagesFileSize {\r\n            color: var(--zy-el-text-color-secondary);\r\n            font-size: calc(var(--zy-text-font-size) - 2px);\r\n          }\r\n\r\n          .globalFileIcon {\r\n            width: 28px;\r\n            height: 28px;\r\n            vertical-align: middle;\r\n            position: absolute;\r\n            top: 50%;\r\n            right: 6px;\r\n            transform: translateY(-50%);\r\n          }\r\n\r\n          .globalFileUnknown {\r\n            background: url('./img/unknown.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFilePDF {\r\n            background: url('./img/PDF.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileWord {\r\n            background: url('./img/Word.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileExcel {\r\n            background: url('./img/Excel.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFilePicture {\r\n            background: url('./img/picture.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileVideo {\r\n            background: url('./img/video.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileTXT {\r\n            background: url('./img/TXT.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileCompress {\r\n            background: url('./img/compress.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFileWPS {\r\n            background: url('./img/WPS.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n\r\n          .globalFilePPT {\r\n            background: url('./img/PPT.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n          }\r\n        }\r\n\r\n        .GlobalAiChatSelfMessageInfo {\r\n          display: inline-block;\r\n          padding: 12px;\r\n          border-radius: 6px;\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          background: var(--zy-el-bg-color-page);\r\n          white-space: pre-wrap;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalAiChatDialogueEditor {\r\n    width: 100%;\r\n    padding: 0 12px;\r\n\r\n    .GlobalAiChatDialogueTools {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      align-items: center;\r\n      padding: 6px;\r\n\r\n      .GlobalAiChatDialogueTool {\r\n        height: 28px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 14px;\r\n        padding: 0 16px;\r\n        font-size: var(--zy-text-font-size);\r\n        border: 1px solid var(--zy-el-border-color);\r\n        margin: 3px 6px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          color: var(--zy-el-color-primary);\r\n          border: 1px solid var(--zy-el-color-primary);\r\n          background: var(--zy-el-color-primary-light-9);\r\n\r\n          span {\r\n            path {\r\n              fill: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n\r\n        span {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatDialogueEditorBody {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 8px;\r\n      box-shadow: var(--zy-el-box-shadow);\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n      .GlobalAiChatToolsActive {\r\n        width: 100%;\r\n        height: var(--zy-height);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 0 12px;\r\n        background: var(--zy-el-color-info-light-9);\r\n\r\n        div {\r\n          font-weight: bold;\r\n        }\r\n\r\n        .GlobalAiChatToolsActiveIcon {\r\n          width: 26px;\r\n          height: 26px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 2px;\r\n          cursor: pointer;\r\n\r\n          &:hover {\r\n            background: var(--zy-el-color-info-light-8);\r\n          }\r\n\r\n          .zy-el-icon {\r\n            font-size: 18px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,cAAc;EAAC,qBAAmB,EAAnB;;;EAMnBA,KAAK,EAAC;AAAkB;;EAGxBA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAsB;;EAG9BA,KAAK,EAAC;AAA0B;;EAE9BA,KAAK,EAAC;AAA+B;;EAIrCA,KAAK,EAAC;AAAkB;;EApBnCC,GAAA;EAqBaD,KAAK,EAAC;;iBArBnB;kBAAA;;EAoCmBA,KAAK,EAAC;AAAqC;;EAC3CA,KAAK,EAAC;AAA4B;;EAEpCA,KAAK,EAAC;AAA6B;;EAInCA,KAAK,EAAC;AAAyB;kBA3ChD;;EAAAC,GAAA;AAAA;;EAsDmBD,KAAK,EAAC;AAAkC;;EAIxCA,KAAK,EAAC;AAA4B;;EAGhCA,KAAK,EAAC;AAA6B;kBA7DxD;;EAkEqBA,KAAK,EAAC;AAA0B;;EAlErDC,GAAA;EAqEqBD,KAAK,EAAC;;;EArE3BC,GAAA;EAwEqBD,KAAK,EAAC;;kBAxE3B;;EAAAC,GAAA;EAgFmBD,KAAK,EAAC;;kBAhFzB;kBAAA;kBAAA;;EA4GSA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAA2B;kBA7G5C;;EAoHWA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAA6B;;;;;;;;;;;;;uBArHhDE,mBAAA,CAsIM,OAtINC,UAsIM,GArIJC,mBAAA,CAIM;IAJDJ,KAAK,EAAC,mBAAmB;IAAEK,OAAK,EAAEC,MAAA,CAAAC;MACrCC,YAAA,CAEUC,kBAAA;IALhBC,OAAA,EAAAC,QAAA,CAIQ;MAAA,OAAS,CAATH,YAAA,CAASI,gBAAA,E;;IAJjBC,CAAA;QAOIT,mBAAA,CAEM,OAFNU,UAEM,GADJN,YAAA,CAAqFO,mBAAA;IAA1EC,GAAG,EAAEV,MAAA,CAAAW,oBAAoB;IAAEC,OAAO,EAAC,MAAM;IAACC,GAAG,EAAC,OAAO;IAACC,SAAS,EAAC;sCAE7EhB,mBAAA,CAGM,OAHNiB,UAGM,GAFJjB,mBAAA,CAA+D,OAA/DkB,UAA+D,EAA7B,KAAG,GAAAC,gBAAA,CAAGjB,MAAA,CAAAkB,IAAI,CAACC,QAAQ,IAAG,GAAC,iB,0BACzDrB,mBAAA,CAAgF;IAA3EJ,KAAK,EAAC;EAAsB,GAAC,0CAAwC,qB,GAE5EI,mBAAA,CAIM,OAJNsB,UAIM,GAHJlB,YAAA,CAAwGF,MAAA;IAAnFqB,GAAG,EAAC,gBAAgB;IAf/CC,UAAA,EAeyDtB,MAAA,CAAAuB,MAAM;IAf/D,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAeyDzB,MAAA,CAAAuB,MAAM,GAAAE,MAAA;IAAA;IAAGC,QAAM,EAAE1B,MAAA,CAAA2B;2CACpE7B,mBAAA,CAA2E,OAA3E8B,UAA2E,EAAAX,gBAAA,CAA7BjB,MAAA,CAAA6B,WAAW,2BACzD/B,mBAAA,CAAoG;IAA/FJ,KAAK,EAAC,yBAAyB;IAAEK,OAAK,EAAEC,MAAA,CAAA8B;MAAmBhC,mBAAA,CAA8B;IAAxBiC,SAAgB,EAAR/B,MAAA,CAAAgC;EAAO,G,KAEvF9B,YAAA,CAwFe+B,uBAAA;IAxFDC,MAAM,EAAN,EAAM;IAACb,GAAG,EAAC,WAAW;IAAC3B,KAAK,EAAC,oBAAoB;IAAEyC,QAAM,EAAEnC,MAAA,CAAAoC;;IAnB7EhC,OAAA,EAAAC,QAAA,CAoBM;MAAA,OAsFM,CAtFNP,mBAAA,CAsFM,OAtFNuC,UAsFM,GArFwCrC,MAAA,CAAAsC,OAAO,CAACC,MAAM,I,cAA1D3C,mBAAA,CAQM,OARN4C,UAQM,G,0BAPJ1C,mBAAA,CAAoD;QAA/CJ,KAAK,EAAC;MAA0B,GAAC,UAAQ,uB,kBAC9CE,mBAAA,CAKM6C,SAAA,QA5BhBC,WAAA,CAuB+D1C,MAAA,CAAAsC,OAAO,EAvBtE,UAuBuDK,IAAI;6BAAjD/C,mBAAA,CAKM;UALDF,KAAK,EAAC,0BAA0B;UAA0BC,GAAG,EAAEgD,IAAI,CAACC;YACvE9C,mBAAA,CAGM;UAHDJ,KAAK,EAAC,sBAAsB;UAAEK,OAAK,WAALA,OAAKA,CAAA0B,MAAA;YAAA,OAAEzB,MAAA,CAAA6C,iBAAiB,CAACF,IAAI,CAACG,UAAU;UAAA;YACzEhD,mBAAA,CAA+B;UAAzBiC,SAAiB,EAAT/B,MAAA,CAAA+C;QAAQ,IAzBpCC,gBAAA,CAyB6C,GAC/B,GAAA/B,gBAAA,CAAG0B,IAAI,CAACG,UAAU,iB,iBA1BhCG,UAAA,E;0CAAAC,mBAAA,iB,kBA8BQtD,mBAAA,CA2EM6C,SAAA,QAzGdC,WAAA,CA+BkC1C,MAAA,CAAAmD,WAAW,EA/B7C,UA+BkBR,IAAI,EAAES,KAAK;6BADrBxD,mBAAA,CA2EM;UA3EAF,KAAK,EA9BnB2D,eAAA,EA8BsBV,IAAI,CAACW,IAAI;UACiB3D,GAAG,EAAEgD,IAAI,CAACC;YAChCD,IAAI,CAACW,IAAI,I,cAAzB1D,mBAAA,CAQW6C,SAAA;UAxCrB9C,GAAA;QAAA,K,kBAiCYC,mBAAA,CAKM6C,SAAA,QAtClBC,WAAA,CAiCoEC,IAAI,CAACY,QAAQ,EAjCjF,UAiC4DZ,IAAI;+BAApD/C,mBAAA,CAKM;YALDF,KAAK,EAAC,6BAA6B;YAAgCC,GAAG,EAAEgD,IAAI,CAACC,EAAE;YACjF7C,OAAK,WAALA,OAAKA,CAAA0B,MAAA;cAAA,OAAEzB,MAAA,CAAAwD,aAAa,CAACb,IAAI;YAAA;cAC1B7C,mBAAA,CAAmE;YAA9DJ,KAAK,EAnCxB2D,eAAA,EAmCyB,gBAAgB,EAASrD,MAAA,CAAAyD,QAAQ,CAACd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,OAAO;mCAC1D5D,mBAAA,CAA6F,OAA7F6D,WAA6F,EAAA1C,gBAAA,CAAzC,CAAA0B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,gBAAgB,6BAC1E9D,mBAAA,CAAqG,OAArG+D,WAAqG,EAAA5C,gBAAA,CAA1D0B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEmB,QAAQ,GAAG9D,MAAA,CAAA+D,QAAQ,CAACpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,QAAQ,0B,iBArCjGE,WAAA;wCAuCYlE,mBAAA,CAAiE,OAAjEmE,WAAiE,EAAAhD,gBAAA,CAArB0B,IAAI,CAACuB,OAAO,iB,+BAvCpEhB,mBAAA,gB,CAyC2BP,IAAI,CAACW,IAAI,I,cAA1B1D,mBAAA,CA+DW6C,SAAA;UAxGrB9C,GAAA;QAAA,IA0CYuD,mBAAA,mGAA8F,EAC9FpD,mBAAA,CA4DM,OA5DNqE,WA4DM,GA1DIxB,IAAI,CAACyB,IAAI,I,cADjBxE,mBAAA,CASM;UArDpBD,GAAA;UA4CmBD,KAAK,EAAC,wCAAwC;UAAEK,OAAK,WAALA,OAAKA,CAAA0B,MAAA;YAAA,OAAEkB,IAAI,CAAC0B,UAAU,IAAI1B,IAAI,CAAC0B,UAAU;UAAA;YAE5FvE,mBAAA,CAA+B;UAA1BiC,SAAmB,EAAX/B,MAAA,CAAAsE;QAAU,I,0BA9CvCtB,gBAAA,CA8C+C,SAE/B,IAAYL,IAAI,CAACyB,IAAI,Y,cAArBxE,mBAAA,CAA0D,QAhD1E2E,WAAA,EAgD+C,MAAI,GAAAtD,gBAAA,CAAG0B,IAAI,CAACyB,IAAI,IAAG,GAAC,mBAhDnElB,mBAAA,gBAiDgBhD,YAAA,CAGUC,kBAAA;UApD1BC,OAAA,EAAAC,QAAA,CA8FS;YAAA,OAAsC,CA5CVsC,IAAI,CAAC0B,UAAU,I,cAAlCG,YAAA,CAAsCC,sBAAA;cAlDxD9E,GAAA;YAAA,MAAAuD,mBAAA,gB,CAmDwCP,IAAI,CAAC0B,UAAU,I,cAArCG,YAAA,CAAyCE,wBAAA;cAnD3D/E,GAAA;YAAA,MAAAuD,mBAAA,e;;UAAA3C,CAAA;sDAAAoE,WAAA,KAAAzB,mBAAA,gB,gBAsDcpD,mBAAA,CAGM,OAHN8E,WAGM,GAFJ1E,YAAA,CAC4DF,MAAA;UAxD5E6E,OAAA;UAuDiCxD,GAAG,WAAHA,GAAGA,CAAGyD,EAAE;YAAA,OAAK9E,MAAA,CAAA+E,cAAc,CAACD,EAAE,EAAE1B,KAAK;UAAA;UAvDtE9B,UAAA,EAuDkFqB,IAAI,CAACqC,aAAa;UAvDpG,gCAAAC,mBAAAxD,MAAA;YAAA,OAuDkFkB,IAAI,CAACqC,aAAa,GAAAvD,MAAA;UAAA;UACjFyC,OAAO,EAAEvB,IAAI,CAACuC,gBAAgB;UAAGC,QAAM,EAAEnF,MAAA,CAAAoF;qHAFQzC,IAAI,CAAC0B,UAAU,E,GAIrEvE,mBAAA,CAqBM,OArBNuF,WAqBM,GApBJnF,YAAA,CAC4DF,MAAA;UA5D5E6E,OAAA;UA2DiCxD,GAAG,WAAHA,GAAGA,CAAGyD,EAAE;YAAA,OAAK9E,MAAA,CAAAsF,QAAQ,CAACR,EAAE,EAAE1B,KAAK;UAAA;UA3DhE9B,UAAA,EA2D4EqB,IAAI,CAACuB,OAAO;UA3DxF,gCAAAe,mBAAAxD,MAAA;YAAA,OA2D4EkB,IAAI,CAACuB,OAAO,GAAAzC,MAAA;UAAA;UAAGyC,OAAO,EAAEvB,IAAI,CAAC4C,UAAU;UAChG,eAAa,EAAEvF,MAAA,CAAAwF,eAAe;UAAGL,QAAM,EAAEnF,MAAA,CAAAoF;mFAC5CtF,mBAAA,CAIM,OAJN2F,WAIM,I,kBAHJ7F,mBAAA,CAEM6C,SAAA,QAhExBC,WAAA,CA8DyEC,IAAI,CAAC+C,QAAQ,EA9DtF,UA8DkEC,GAAG;+BAAnD/F,mBAAA,CAEM;YAFDF,KAAK,EAAC,6BAA6B;YAA+BK,OAAK,WAALA,OAAKA,CAAA0B,MAAA;cAAA,OAAEzB,MAAA,CAAA4F,cAAc,CAACD,GAAG;YAAA;8BAC3FA,GAAG,CAACE,UAAU,wBA/DrCC,WAAA;4CAkEgBhG,mBAAA,CAEM,OAFNiG,WAEM,I,kBADJnG,mBAAA,CAAyF6C,SAAA,QAnE3GC,WAAA,CAmEoDC,IAAI,CAACqD,SAAS,EAnElE,UAmEyCL,GAAG,EAAEM,CAAC;+BAA7BzB,YAAA,CAAyFxE,MAAA;YAAtCL,GAAG,EAAEsG,CAAC;YAAiBC,MAAM,EAAEP;;0CAEtCvC,KAAK,KAAKpD,MAAA,CAAAmD,WAAW,CAACZ,MAAM,QAAQvC,MAAA,CAAAY,OAAO,I,cAAzFhB,mBAAA,CAEM,OAFNuG,WAEM,GADJrG,mBAAA,CAAsD;UAAjDJ,KAAK,EAAC,eAAe;UAACqC,SAAoB,EAAZ/B,MAAA,CAAAoG;gBAtErDlD,mBAAA,gBAwE4DP,IAAI,CAAC0D,SAAS,CAAC9D,MAAM,I,cAAjE3C,mBAAA,CAAgF,OAAhF0G,WAAgF,EAAb,SAAO,KAxE1FpD,mBAAA,iB,kBAyEgBtD,mBAAA,CAKM6C,SAAA,QA9EtBC,WAAA,CAyEyEC,IAAI,CAAC0D,SAAS,EAzEvF,UAyE8DV,GAAG,EAAEM,CAAC;+BAApDrG,mBAAA,CAKM;YALDF,KAAK,EAAC,0BAA0B;YAAqCC,GAAG,EAAEsG,CAAC;cAC9EnG,mBAAA,CAGM;YAHDJ,KAAK,EAAC,sBAAsB;YAAEK,OAAK,WAALA,OAAKA,CAAA0B,MAAA;cAAA,OAAEzB,MAAA,CAAAuG,eAAe,CAACZ,GAAG;YAAA;cAC3D7F,mBAAA,CAA+B;YAAzBiC,SAAiB,EAAT/B,MAAA,CAAA+C;UAAQ,IA3E1CC,gBAAA,CA2EmD,GAC/B,GAAA/B,gBAAA,CAAG0E,GAAG,CAACa,QAAQ,iB,iBA5EnCC,WAAA,E;0CAgF6D9D,IAAI,CAAC+D,UAAU,I,cAA9D9G,mBAAA,CAsBM,OAtBN+G,WAsBM,GArBJzG,YAAA,CAMa0G,qBAAA;UAND1C,OAAO,EAAC,IAAI;UAAC2C,SAAS,EAAC;;UAjFnDzG,OAAA,EAAAC,QAAA,CAkFkB;YAAA,OAIM,CAJNP,mBAAA,CAIM;cAJDJ,KAAK,EAAC,iCAAiC;cAAEK,OAAK,WAALA,OAAKA,CAAA0B,MAAA;gBAAA,OAAEzB,MAAA,CAAA8G,iBAAiB,CAACnE,IAAI,CAACuB,OAAO,EAAEd,KAAK;cAAA;gBACxFlD,YAAA,CAEUC,kBAAA;cArF9BC,OAAA,EAAAC,QAAA,CAoFsB;gBAAA,OAAgB,CAAhBH,YAAA,CAAgB6G,uBAAA,E;;cApFtCxG,CAAA;gCAAAyG,WAAA,E;;UAAAzG,CAAA;sCAwFgBL,YAAA,CAMa0G,qBAAA;UAND1C,OAAO,EAAC,MAAM;UAAC2C,SAAS,EAAC;;UAxFrDzG,OAAA,EAAAC,QAAA,CAyFkB;YAAA,OAIM,CAJNP,mBAAA,CAIM;cAJDJ,KAAK,EAAC,iCAAiC;cAAEK,OAAK,WAALA,OAAKA,CAAA0B,MAAA;gBAAA,OAAEzB,MAAA,CAAAiH,kBAAkB,CAACtE,IAAI,EAAES,KAAK;cAAA;gBACjFlD,YAAA,CAEUC,kBAAA;cA5F9BC,OAAA,EAAAC,QAAA,CA2FsB;gBAAA,OAAW,CAAXH,YAAA,CAAWgH,kBAAA,E;;cA3FjC3G,CAAA;gCAAA4G,WAAA,E;;UAAA5G,CAAA;sCA+FgBL,YAAA,CAMa0G,qBAAA;UAND1C,OAAO,EAAC,OAAO;UAAC2C,SAAS,EAAC;;UA/FtDzG,OAAA,EAAAC,QAAA,CAgGkB;YAAA,OAIM,CAJNP,mBAAA,CAIM;cAJDJ,KAAK,EAAC,iCAAiC;cAAEK,OAAK,WAALA,OAAKA,CAAA0B,MAAA;gBAAA,OAAEzB,MAAA,CAAAoH,wBAAwB,CAACzE,IAAI,EAAES,KAAK;cAAA;gBACvFlD,YAAA,CAEUC,kBAAA;cAnG9BC,OAAA,EAAAC,QAAA,CAkGsB;gBAAA,OAAQ,CAARH,YAAA,CAAQmH,eAAA,E;;cAlG9B9G,CAAA;gCAAA+G,WAAA,E;;UAAA/G,CAAA;0CAAA2C,mBAAA,e,iCAAAA,mBAAA,e;;;IAAA3C,CAAA;4BA4GIT,mBAAA,CAsBM,OAtBNyH,WAsBM,G,gBArBJzH,mBAAA,CAMM,OANN0H,WAMM,I,kBALJ5H,mBAAA,CAIM6C,SAAA,QAlHdC,WAAA,CA8G6D1C,MAAA,CAAAyH,OAAO,EA9GpE,UA8GqD9E,IAAI;yBAAjD/C,mBAAA,CAIM;MAJDF,KAAK,EAAC,0BAA0B;MAA0BC,GAAG,EAAEgD,IAAI,CAACC,EAAE;MACxE7C,OAAK,WAALA,OAAKA,CAAA0B,MAAA;QAAA,OAAEzB,MAAA,CAAA0H,qBAAqB,CAAC/E,IAAI;MAAA;QAClC7C,mBAAA,CAA+B;MAAzBiC,SAAiB,EAAT/B,MAAA,CAAA2H;IAAQ,IAhHhC3E,gBAAA,CAgHyC,GAC/B,GAAA/B,gBAAA,CAAG0B,IAAI,CAACiF,YAAY,iB,iBAjH9BC,WAAA;qEA6GsD7H,MAAA,CAAA8H,MAAM,E,GAOtDhI,mBAAA,CAaM,OAbNiI,WAaM,G,gBAZJjI,mBAAA,CAOM,OAPNkI,WAOM,GANJlI,mBAAA,CAA6D,OAA7DmI,WAA6D,EAAAhH,gBAAA,CAAjBjB,MAAA,CAAAkI,QAAQ,kBACpDpI,mBAAA,CAIM;IAJDJ,KAAK,EAAC,6BAA6B;IAAEK,OAAK,EAAEC,MAAA,CAAAmI;MAC/CjI,YAAA,CAEUC,kBAAA;IA1HtBC,OAAA,EAAAC,QAAA,CAyHc;MAAA,OAAS,CAATH,YAAA,CAASI,gBAAA,E;;IAzHvBC,CAAA;0CAqHqDP,MAAA,CAAA8H,MAAM,E,mBAQnD5H,YAAA,CACgDF,MAAA;IAD7BoI,QAAQ,EAAEpI,MAAA,CAAAoI,QAAQ;IAAG7E,QAAQ,EAAEvD,MAAA,CAAAuD,QAAQ;IAAG8E,OAAK,EAAErI,MAAA,CAAAsI;+DAC1DtI,MAAA,CAAAoI,QAAQ,CAAC7F,MAAM,IAAIvC,MAAA,CAAAuD,QAAQ,CAAChB,MAAM,E,GAC5CrC,YAAA,CACoGF,MAAA;IADhFqB,GAAG,EAAC,WAAW;IA/H3CC,UAAA,EA+HqDtB,MAAA,CAAAuI,WAAW;IA/HhE,uBAAA/G,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA+HqDzB,MAAA,CAAAuI,WAAW,GAAA9G,MAAA;IAAA;IAAG+G,QAAQ,EAAExI,MAAA,CAAAwI,QAAQ;IAAGC,MAAI,EAAEzI,MAAA,CAAA6C,iBAAiB;IACpG6F,MAAI,EAAE1I,MAAA,CAAA2I,iBAAiB;IAAGC,gBAAc,EAAE5I,MAAA,CAAA6I,gBAAgB;IAAGC,cAAY,EAAE9I,MAAA,CAAA+I;2DAGlF7I,YAAA,CAEmB8I,2BAAA;IArIvB1H,UAAA,EAmI+BtB,MAAA,CAAAiJ,QAAQ;IAnIvC,uBAAAzH,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAmI+BzB,MAAA,CAAAiJ,QAAQ,GAAAxH,MAAA;IAAA;IAAGyH,IAAI,EAAElJ,MAAA,CAAAmJ;;IAnIhD/I,OAAA,EAAAC,QAAA,CAoIM;MAAA,OAAsD,CAAtDH,YAAA,CAAsDF,MAAA;QAAnCoJ,IAAI,EAAEpJ,MAAA,CAAAqJ;MAAQ,kC;;IApIvC9I,CAAA;6CAsIIL,YAAA,CAAqGF,MAAA;IAtIzGsB,UAAA,EAsIsCtB,MAAA,CAAAsJ,sBAAsB;IAtI5D,uBAAA9H,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAsIsCzB,MAAA,CAAAsJ,sBAAsB,GAAA7H,MAAA;IAAA;IAAG2H,IAAI,EAAEpJ,MAAA,CAAAqJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}