{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalVoteDetails\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalVoteDetailsUser\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalVoteDetailsUserName\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalVoteDetailsTitle\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalVoteDetailsTimeBody\"\n};\nvar _hoisted_6 = {\n  class: \"GlobalVoteDetailsTime\"\n};\nvar _hoisted_7 = {\n  key: 0,\n  class: \"is-primary\"\n};\nvar _hoisted_8 = {\n  key: 1,\n  class: \"is-warning\"\n};\nvar _hoisted_9 = {\n  key: 2,\n  class: \"is-info\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  class: \"GlobalVoteDetailsImg\"\n};\nvar _hoisted_11 = {\n  class: \"GlobalVoteDetailsOptions\"\n};\nvar _hoisted_12 = {\n  key: 0\n};\nvar _hoisted_13 = [\"innerHTML\"];\nvar _hoisted_14 = {\n  key: 1\n};\nvar _hoisted_15 = {\n  key: 2\n};\nvar _hoisted_16 = {\n  key: 2,\n  class: \"GlobalVoteDetailsButton\"\n};\nvar _hoisted_17 = {\n  key: 3,\n  class: \"VoteStatisticsList\"\n};\nvar _hoisted_18 = [\"onClick\"];\nvar _hoisted_19 = {\n  class: \"VoteStatisticsItemName\"\n};\nvar _hoisted_20 = {\n  class: \"GlobalVoteDetailsOptionContent\"\n};\nvar _hoisted_21 = {\n  class: \"GlobalVoteDetailsUserName ellipsis\"\n};\nvar _hoisted_22 = {\n  class: \"GlobalVoteDetailsUserTime\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_Close = _resolveComponent(\"Close\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_ArrowRightBold = _resolveComponent(\"ArrowRightBold\");\n  var _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  var _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n  var _component_el_progress = _resolveComponent(\"el-progress\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n    class: \"GlobalAiChatClose\",\n    onClick: $setup.handleClick\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Close)];\n    }),\n    _: 1 /* STABLE */\n  })]), $setup.isDelete ? (_openBlock(), _createBlock(_component_el_empty, {\n    key: 0,\n    description: \"投票已不存在\"\n  })) : _createCommentVNode(\"v-if\", true), $setup.details.id ? _withDirectives((_openBlock(), _createBlock(_component_el_scrollbar, {\n    key: 1,\n    class: \"GlobalVoteDetailsScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n        src: $setup.imgUrl($setup.details.headImg),\n        fit: \"cover\",\n        draggable: \"false\"\n      }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.details.crateUserName), 1 /* TEXT */), $setup.details.createBy === $setup.user.id ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"GlobalVoteDetailsDel\",\n        onClick: $setup.handleDelVote\n      }, [_createVNode(_component_el_button, {\n        type: \"danger\",\n        icon: $setup.Delete,\n        plain: \"\"\n      }, null, 8 /* PROPS */, [\"icon\"])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.details.topic), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.format($setup.details.startTime)) + \" 至 \" + _toDisplayString($setup.format($setup.details.endTime)), 1 /* TEXT */), $setup.detailsStatus.id === '1' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_7, _toDisplayString($setup.detailsStatus.name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.detailsStatus.id === '2' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_8, _toDisplayString($setup.detailsStatus.name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.detailsStatus.id === '3' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_9, _toDisplayString($setup.detailsStatus.name), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), $setup.details.topicImg ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_el_image, {\n        src: $setup.imgUrl($setup.details.topicImg),\n        fit: \"cover\",\n        draggable: \"false\"\n      }, null, 8 /* PROPS */, [\"src\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_11, [$setup.hasVote ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", {\n        class: \"GlobalVoteDetailsHasIcon\",\n        innerHTML: $setup.hasVoteIcon\n      }, null, 8 /* PROPS */, _hoisted_13)])) : _createCommentVNode(\"v-if\", true), !$setup.hasVote && $setup.detailsStatus.id !== '3' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, \"可以选择 \" + _toDisplayString($setup.details.maxVote) + \" 项\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), !$setup.hasVote && $setup.detailsStatus.id === '3' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", {\n        onClick: $setup.handleVoteUser\n      }, [_createTextVNode(_toDisplayString($setup.totals) + \"人已参与 \", 1 /* TEXT */), !$setup.details.isAnonymous ? (_openBlock(), _createBlock(_component_el_icon, {\n        key: 0\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowRightBold)];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)])]), !$setup.hasVote && $setup.detailsStatus.id !== '3' ? (_openBlock(), _createBlock(_component_el_checkbox_group, {\n        key: 1,\n        modelValue: $setup.checked,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.checked = $event;\n        }),\n        max: $setup.details.maxVote,\n        disabled: $setup.detailsStatus.id !== '2'\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.details.options, function (item) {\n            return _openBlock(), _createBlock(_component_el_checkbox, {\n              key: item.id,\n              value: item.id,\n              label: item.optionContent\n            }, null, 8 /* PROPS */, [\"value\", \"label\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\", \"max\", \"disabled\"])) : _createCommentVNode(\"v-if\", true), !$setup.hasVote && $setup.detailsStatus.id === '2' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.globalJson\n      }, {\n        default: _withCtx(function () {\n          return _cache[1] || (_cache[1] = [_createTextVNode(\"立即投票\")]);\n        }),\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true), $setup.hasVote || $setup.detailsStatus.id === '3' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.details.options, function (item, index) {\n        var _$setup$voteOptionsUs, _$setup$voteOptionsUs2;\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"VoteStatisticsItem\",\n          key: item.id,\n          onClick: function onClick($event) {\n            return $setup.handleVoteStatistics(item);\n          }\n        }, [_createElementVNode(\"div\", _hoisted_19, _toDisplayString(index + 1) + \"、\" + _toDisplayString(item.optionContent), 1 /* TEXT */), _createVNode(_component_el_progress, {\n          percentage: $setup.percent((_$setup$voteOptionsUs = $setup.voteOptionsUser[item.id]) === null || _$setup$voteOptionsUs === void 0 ? void 0 : _$setup$voteOptionsUs.length, $setup.voteTotal),\n          status: (_$setup$voteOptionsUs2 = $setup.voteOptionsUser[item.id]) !== null && _$setup$voteOptionsUs2 !== void 0 && _$setup$voteOptionsUs2.includes($setup.user.id) ? 'success' : ''\n        }, {\n          default: _withCtx(function (_ref) {\n            var _$setup$voteOptionsUs3;\n            var percentage = _ref.percentage;\n            return [_createElementVNode(\"span\", null, _toDisplayString(percentage) + \"%（\" + _toDisplayString((_$setup$voteOptionsUs3 = $setup.voteOptionsUser[item.id]) === null || _$setup$voteOptionsUs3 === void 0 ? void 0 : _$setup$voteOptionsUs3.length) + \"票）\", 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"percentage\", \"status\"])], 8 /* PROPS */, _hoisted_18);\n      }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */)), [[_vShow, !$setup.voteOptionsInfo.id]]) : _createCommentVNode(\"v-if\", true), _withDirectives(_createVNode(_component_el_scrollbar, {\n    class: \"GlobalVoteDetailsScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_20, _toDisplayString($setup.voteOptionsInfo.optionContent || '所有人员'), 1 /* TEXT */), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.voteOptionsData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"GlobalVoteDetailsUserInfo\",\n          key: item.id\n        }, [_createVNode(_component_el_image, {\n          src: $setup.imgUrl(item.headImg),\n          fit: \"cover\",\n          draggable: \"false\"\n        }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_21, _toDisplayString(item.userName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_22, _toDisplayString($setup.format(item.voteTime)), 1 /* TEXT */)]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */), [[_vShow, $setup.voteOptionsInfo.id]])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "onClick", "$setup", "handleClick", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Close", "_", "isDelete", "_createBlock", "_component_el_empty", "description", "_createCommentVNode", "details", "id", "_component_el_scrollbar", "_hoisted_2", "_component_el_image", "src", "imgUrl", "headImg", "fit", "draggable", "_hoisted_3", "_toDisplayString", "crateUserName", "createBy", "user", "handleDelVote", "_component_el_button", "type", "icon", "Delete", "plain", "_hoisted_4", "topic", "_hoisted_5", "_hoisted_6", "format", "startTime", "endTime", "detailsStatus", "_hoisted_7", "name", "_hoisted_8", "_hoisted_9", "topicImg", "_hoisted_10", "_hoisted_11", "hasVote", "_hoisted_12", "innerHTML", "hasVoteIcon", "_hoisted_13", "_hoisted_14", "maxVote", "_hoisted_15", "handleVoteUser", "_createTextVNode", "totals", "isAnonymous", "_component_ArrowRightBold", "_component_el_checkbox_group", "modelValue", "checked", "_cache", "$event", "max", "disabled", "_Fragment", "_renderList", "options", "item", "_component_el_checkbox", "value", "label", "optionContent", "_hoisted_16", "globalJson", "_hoisted_17", "index", "_$setup$voteOptionsUs", "_$setup$voteOptionsUs2", "handleVoteStatistics", "_hoisted_19", "_component_el_progress", "percentage", "percent", "voteOptionsUser", "length", "voteTotal", "status", "includes", "_ref", "_$setup$voteOptionsUs3", "_hoisted_18", "voteOptionsInfo", "_hoisted_20", "voteOptionsData", "_hoisted_21", "userName", "_hoisted_22", "voteTime"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalGroupVote\\GlobalVoteDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalVoteDetails\">\r\n    <div class=\"GlobalAiChatClose\" @click=\"handleClick\">\r\n      <el-icon>\r\n        <Close />\r\n      </el-icon>\r\n    </div>\r\n    <el-empty description=\"投票已不存在\" v-if=\"isDelete\" />\r\n    <el-scrollbar class=\"GlobalVoteDetailsScrollbar\" v-if=\"details.id\" v-show=\"!voteOptionsInfo.id\">\r\n      <div class=\"GlobalVoteDetailsUser\">\r\n        <el-image :src=\"imgUrl(details.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n        <div class=\"GlobalVoteDetailsUserName\">{{ details.crateUserName }}</div>\r\n        <div class=\"GlobalVoteDetailsDel\" @click=\"handleDelVote\" v-if=\"details.createBy === user.id\">\r\n          <el-button type=\"danger\" :icon=\"Delete\" plain />\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalVoteDetailsTitle\">{{ details.topic }}</div>\r\n      <div class=\"GlobalVoteDetailsTimeBody\">\r\n        <div class=\"GlobalVoteDetailsTime\">{{ format(details.startTime) }} 至 {{ format(details.endTime) }}</div>\r\n        <span class=\"is-primary\" v-if=\"detailsStatus.id === '1'\">{{ detailsStatus.name }}</span>\r\n        <span class=\"is-warning\" v-if=\"detailsStatus.id === '2'\">{{ detailsStatus.name }}</span>\r\n        <span class=\"is-info\" v-if=\"detailsStatus.id === '3'\">{{ detailsStatus.name }}</span>\r\n      </div>\r\n      <div class=\"GlobalVoteDetailsImg\" v-if=\"details.topicImg\">\r\n        <el-image :src=\"imgUrl(details.topicImg)\" fit=\"cover\" draggable=\"false\" />\r\n      </div>\r\n      <div class=\"GlobalVoteDetailsOptions\">\r\n        <div v-if=\"hasVote\">\r\n          <div class=\"GlobalVoteDetailsHasIcon\" v-html=\"hasVoteIcon\"></div>\r\n        </div>\r\n        <div v-if=\"!hasVote && detailsStatus.id !== '3'\">可以选择 {{ details.maxVote }} 项</div>\r\n        <div v-if=\"!hasVote && detailsStatus.id === '3'\"></div>\r\n        <span @click=\"handleVoteUser\">\r\n          {{ totals }}人已参与\r\n          <el-icon v-if=\"!details.isAnonymous\">\r\n            <ArrowRightBold />\r\n          </el-icon>\r\n        </span>\r\n      </div>\r\n      <el-checkbox-group v-model=\"checked\" :max=\"details.maxVote\" :disabled=\"detailsStatus.id !== '2'\"\r\n        v-if=\"!hasVote && detailsStatus.id !== '3'\">\r\n        <el-checkbox v-for=\"item in details.options\" :key=\"item.id\" :value=\"item.id\"\r\n          :label=\"item.optionContent\"></el-checkbox>\r\n      </el-checkbox-group>\r\n      <div class=\"GlobalVoteDetailsButton\" v-if=\"!hasVote && detailsStatus.id === '2'\">\r\n        <el-button type=\"primary\" @click=\"globalJson\">立即投票</el-button>\r\n      </div>\r\n      <div class=\"VoteStatisticsList\" v-if=\"hasVote || detailsStatus.id === '3'\">\r\n        <div class=\"VoteStatisticsItem\" v-for=\"(item, index) in details.options\" :key=\"item.id\"\r\n          @click=\"handleVoteStatistics(item)\">\r\n          <div class=\"VoteStatisticsItemName\">{{ index + 1 }}、{{ item.optionContent }}</div>\r\n          <el-progress :percentage=\"percent(voteOptionsUser[item.id]?.length, voteTotal)\"\r\n            :status=\"voteOptionsUser[item.id]?.includes(user.id) ? 'success' : ''\">\r\n            <template #default=\"{ percentage }\">\r\n              <span>{{ percentage }}%（{{ voteOptionsUser[item.id]?.length }}票）</span>\r\n            </template>\r\n          </el-progress>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <el-scrollbar class=\"GlobalVoteDetailsScrollbar\" v-show=\"voteOptionsInfo.id\">\r\n      <div class=\"GlobalVoteDetailsOptionContent\">{{ voteOptionsInfo.optionContent || '所有人员' }}</div>\r\n      <div class=\"GlobalVoteDetailsUserInfo\" v-for=\"item in voteOptionsData\" :key=\"item.id\">\r\n        <el-image :src=\"imgUrl(item.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n        <div class=\"GlobalVoteDetailsUserName ellipsis\">{{ item.userName }}</div>\r\n        <div class=\"GlobalVoteDetailsUserTime\">{{ format(item.voteTime) }}</div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalVoteDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, watch } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { hasVoteIcon } from '../../js/icon.js'\r\nimport { Delete } from '@element-plus/icons-vue'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback', 'sendMessage'])\r\n\r\nconst details = ref({})\r\nconst detailsStatus = ref({})\r\nconst checked = ref([])\r\nconst totals = ref(0)\r\nconst voteTotal = ref(0)\r\nconst hasVote = ref(false)\r\nconst isDelete = ref(false)\r\nconst voteOptionsUser = ref({})\r\nconst voteOptionsInfo = ref({})\r\nconst voteOptionsData = ref([])\r\nconst voteUserData = ref([])\r\nconst tableData = ref([])\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\n\r\nconst checkAuditTime = (startTime, endTime) => {\r\n  if (!startTime && !endTime) return { id: '0', name: '' }\r\n  // 获取当前时间\r\n  const date = new Date()\r\n  // 获取开始时间、结束时间、现在时间的时间戳\r\n  const startDate = new Date(startTime).getTime()\r\n  const endDate = new Date(endTime).getTime()\r\n  const nowDate = date.getTime()\r\n  // 判断现在的时间是否在开始时间和结束时间之间\r\n  if (nowDate < startDate) return { id: '1', name: '未开始' }\r\n  if (nowDate > startDate && nowDate < endDate) return { id: '2', name: '进行中' }\r\n  if (nowDate > endDate) return { id: '3', name: '已结束' }\r\n}\r\nconst handleVoteUser = () => {\r\n  if (details.value.isAnonymous) return ElMessage({ type: 'warning', message: '当前是匿名投票，不可查看详情！' })\r\n  voteOptionsData.value = voteUserData.value\r\n  if (voteOptionsData.value.length) voteOptionsInfo.value = { id: '1' }\r\n}\r\nconst handleVoteStatistics = (item) => {\r\n  if (details.value.isAnonymous) return ElMessage({ type: 'warning', message: '当前是匿名投票，不可查看详情！' })\r\n  voteOptionsData.value = tableData.value.filter(v => v.optionId === item.id)\r\n  if (voteOptionsData.value.length) voteOptionsInfo.value = item\r\n}\r\nconst VoteInfo = async () => {\r\n  const { data } = await api.VoteInfo({ detailId: props.id })\r\n  if (data.id) {\r\n    details.value = data\r\n    detailsStatus.value = checkAuditTime(data.startTime, data.endTime)\r\n    VoteUserList()\r\n  } else {\r\n    isDelete.value = true\r\n  }\r\n}\r\nconst handleDelVote = () => {\r\n  ElMessageBox.confirm('此操作将删除当前投票, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { VoteDel() }).catch(() => { ElMessage({ type: 'info', message: '已取消删除' }) })\r\n}\r\nconst VoteDel = async () => {\r\n  const { code } = await api.VoteDel({ ids: [props.id] })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '删除成功' })\r\n    emit('callback', 'del')\r\n  }\r\n}\r\nconst VoteUserList = async () => {\r\n  const { data, total } = await api.VoteUserList({ pageNo: 1, pageSize: 999999, query: { topicId: props.id } })\r\n  const userId = []\r\n  const userData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.userId === user.value.id) hasVote.value = true\r\n    if (!userId?.includes(item.userId)) {\r\n      userId.push(item.userId)\r\n      userData.push(item)\r\n    }\r\n    if (voteOptionsUser.value[item.optionId]) {\r\n      voteOptionsUser.value[item.optionId].push(item.userId)\r\n    } else {\r\n      voteOptionsUser.value[item.optionId] = [item.userId]\r\n    }\r\n  }\r\n  tableData.value = data\r\n  voteUserData.value = userData\r\n  totals.value = userId.length\r\n  voteTotal.value = total\r\n}\r\nconst percent = (num = 0, total = 0) => {\r\n  if (num == 0 || total == 0) return 0\r\n  return (Math.round(num / total * 10000) / 100.00)\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/voteTopicOptionUser/add', { topicId: props.id, optionIds: checked.value })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '投票成功' })\r\n    details.value = {}\r\n    detailsStatus.value = {}\r\n    checked.value = []\r\n    totals.value = 0\r\n    voteTotal.value = 0\r\n    hasVote.value = false\r\n    isDelete.value = false\r\n    voteOptionsUser.value = {}\r\n    VoteInfo()\r\n    const sendMessageData = {\r\n      name: `${user.value?.userName} 参与了投票 ${details.value.topic}`,\r\n      data: `${user.value?.userName}|OUI|${user.value?.accountId}|| 参与了投票 ||${details.value.topic}|details30|${details.value.id}`,\r\n    }\r\n    emit('sendMessage', sendMessageData)\r\n  }\r\n}\r\nconst handleClick = () => {\r\n  if (voteOptionsInfo.value.id) {\r\n    voteOptionsInfo.value = {}\r\n  } else {\r\n    emit('callback')\r\n  }\r\n}\r\nwatch(() => props.id, (val) => {\r\n  if (val) {\r\n    details.value = {}\r\n    detailsStatus.value = {}\r\n    checked.value = []\r\n    totals.value = 0\r\n    voteTotal.value = 0\r\n    hasVote.value = false\r\n    isDelete.value = false\r\n    voteOptionsUser.value = {}\r\n    VoteInfo()\r\n  }\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalVoteDetails {\r\n  width: 420px;\r\n  height: 100%;\r\n  padding-top: 32px;\r\n  background: #fff;\r\n  position: relative;\r\n\r\n  .GlobalAiChatClose {\r\n    width: 32px;\r\n    height: 28px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 20px;\r\n    border-radius: 2px;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: #fff;\r\n      background-color: rgba($color: red, $alpha: 0.6);\r\n    }\r\n\r\n    .zy-el-icon {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsScrollbar {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .GlobalVoteDetailsUser {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    padding: 0 20px 12px 20px;\r\n    position: relative;\r\n\r\n    .zy-el-image {\r\n      width: 38px;\r\n      height: 38px;\r\n      border-radius: 50%;\r\n    }\r\n\r\n    .GlobalVoteDetailsUserName {\r\n      padding-left: 12px;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .GlobalVoteDetailsDel {\r\n      position: absolute;\r\n      right: 20px;\r\n      bottom: 12px;\r\n\r\n      .zy-el-button {\r\n        width: var(--zy-height-secondary);\r\n        height: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsTitle {\r\n    padding: 0 20px;\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .GlobalVoteDetailsTimeBody {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n\r\n    .GlobalVoteDetailsTime {\r\n      padding: 6px 0;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-secondary);\r\n    }\r\n\r\n    .is-primary,\r\n    .is-warning,\r\n    .is-info {\r\n      font-size: 12px;\r\n      padding: 2px 6px;\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .is-primary {\r\n      color: var(--zy-el-color-primary);\r\n      background: var(--zy-el-color-primary-light-9);\r\n    }\r\n\r\n    .is-warning {\r\n      color: var(--zy-el-color-warning);\r\n      background: var(--zy-el-color-warning-light-9);\r\n    }\r\n\r\n    .is-info {\r\n      color: var(--zy-el-color-info);\r\n      background: var(--zy-el-color-info-light-9);\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsImg {\r\n    width: 100%;\r\n    padding: 0 20px;\r\n\r\n    .zy-el-image {\r\n      width: 100%;\r\n      height: 168px;\r\n      border-radius: 6px;\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsOptions {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 40px 20px 0 20px;\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: 100%;\r\n      height: 10px;\r\n      position: absolute;\r\n      top: 10px;\r\n      left: 0;\r\n      background: var(--zy-el-color-info-light-9);\r\n    }\r\n\r\n    &>div {\r\n      font-weight: bold;\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-color-primary);\r\n      position: relative;\r\n    }\r\n\r\n    .GlobalVoteDetailsHasIcon {\r\n      width: 42px;\r\n      height: 42px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      transform: translateY(-50%);\r\n      z-index: 9;\r\n    }\r\n\r\n    span {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-text-color-secondary);\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .zy-el-checkbox-group {\r\n    padding: 6px 20px 20px 20px;\r\n\r\n    .zy-el-checkbox {\r\n      width: 100%;\r\n      height: auto;\r\n      padding: 6px 0;\r\n      margin: 0;\r\n\r\n      .zy-el-checkbox__label {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        overflow-wrap: break-word;\r\n        white-space: pre-wrap;\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsButton {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding-bottom: 20px;\r\n\r\n    .zy-el-button {\r\n      width: 120px;\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n\r\n  .VoteStatisticsList {\r\n    width: 100%;\r\n    padding: 12px 20px;\r\n\r\n    .VoteStatisticsItem {\r\n      cursor: pointer;\r\n      padding: var(--zy-distance-five) 0;\r\n\r\n      .VoteStatisticsItemName {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-bottom: var(--zy-font-text-distance-five);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsOptionContent {\r\n    width: 100%;\r\n    padding: 0 20px 32px 20px;\r\n    font-weight: bold;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: 100%;\r\n      height: 10px;\r\n      position: absolute;\r\n      left: 0;\r\n      bottom: 12px;\r\n      background: var(--zy-el-color-info-light-9);\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsUserInfo {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    padding: 0 20px 12px 20px;\r\n    position: relative;\r\n\r\n    .zy-el-image {\r\n      width: 32px;\r\n      height: 32px;\r\n      border-radius: 50%;\r\n    }\r\n\r\n    .GlobalVoteDetailsUserName {\r\n      flex: 1;\r\n      padding: 0 12px;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .GlobalVoteDetailsUserTime {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAQrBA,KAAK,EAAC;AAAuB;;EAE3BA,KAAK,EAAC;AAA2B;;EAKnCA,KAAK,EAAC;AAAwB;;EAC9BA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAuB;;EAlB1CC,GAAA;EAmBcD,KAAK,EAAC;;;EAnBpBC,GAAA;EAoBcD,KAAK,EAAC;;;EApBpBC,GAAA;EAqBcD,KAAK,EAAC;;;EArBpBC,GAAA;EAuBWD,KAAK,EAAC;;;EAGNA,KAAK,EAAC;AAA0B;;EA1B3CC,GAAA;AAAA;kBAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;EA4CWD,KAAK,EAAC;;;EA5CjBC,GAAA;EA+CWD,KAAK,EAAC;;kBA/CjB;;EAkDeA,KAAK,EAAC;AAAwB;;EAWlCA,KAAK,EAAC;AAAgC;;EAGpCA,KAAK,EAAC;AAAoC;;EAC1CA,KAAK,EAAC;AAA2B;;;;;;;;;;;;uBAhE5CE,mBAAA,CAmEM,OAnENC,UAmEM,GAlEJC,mBAAA,CAIM;IAJDJ,KAAK,EAAC,mBAAmB;IAAEK,OAAK,EAAEC,MAAA,CAAAC;MACrCC,YAAA,CAEUC,kBAAA;IALhBC,OAAA,EAAAC,QAAA,CAIQ;MAAA,OAAS,CAATH,YAAA,CAASI,gBAAA,E;;IAJjBC,CAAA;QAOyCP,MAAA,CAAAQ,QAAQ,I,cAA7CC,YAAA,CAAiDC,mBAAA;IAPrDf,GAAA;IAOcgB,WAAW,EAAC;QAP1BC,mBAAA,gBAQ2DZ,MAAA,CAAAa,OAAO,CAACC,EAAE,G,+BAAjEL,YAAA,CAmDeM,uBAAA;IA3DnBpB,GAAA;IAQkBD,KAAK,EAAC;;IARxBU,OAAA,EAAAC,QAAA,CASM;MAAA,OAMM,CANNP,mBAAA,CAMM,OANNkB,UAMM,GALJd,YAAA,CAAyEe,mBAAA;QAA9DC,GAAG,EAAElB,MAAA,CAAAmB,MAAM,CAACnB,MAAA,CAAAa,OAAO,CAACO,OAAO;QAAGC,GAAG,EAAC,OAAO;QAACC,SAAS,EAAC;wCAC/DxB,mBAAA,CAAwE,OAAxEyB,UAAwE,EAAAC,gBAAA,CAA9BxB,MAAA,CAAAa,OAAO,CAACY,aAAa,kBACAzB,MAAA,CAAAa,OAAO,CAACa,QAAQ,KAAK1B,MAAA,CAAA2B,IAAI,CAACb,EAAE,I,cAA3FlB,mBAAA,CAEM;QAddD,GAAA;QAYaD,KAAK,EAAC,sBAAsB;QAAEK,OAAK,EAAEC,MAAA,CAAA4B;UACxC1B,YAAA,CAAgD2B,oBAAA;QAArCC,IAAI,EAAC,QAAQ;QAAEC,IAAI,EAAE/B,MAAA,CAAAgC,MAAM;QAAEC,KAAK,EAAL;6CAblDrB,mBAAA,e,GAgBMd,mBAAA,CAA6D,OAA7DoC,UAA6D,EAAAV,gBAAA,CAAtBxB,MAAA,CAAAa,OAAO,CAACsB,KAAK,kBACpDrC,mBAAA,CAKM,OALNsC,UAKM,GAJJtC,mBAAA,CAAwG,OAAxGuC,UAAwG,EAAAb,gBAAA,CAAlExB,MAAA,CAAAsC,MAAM,CAACtC,MAAA,CAAAa,OAAO,CAAC0B,SAAS,KAAI,KAAG,GAAAf,gBAAA,CAAGxB,MAAA,CAAAsC,MAAM,CAACtC,MAAA,CAAAa,OAAO,CAAC2B,OAAO,mBAC/DxC,MAAA,CAAAyC,aAAa,CAAC3B,EAAE,Y,cAA/ClB,mBAAA,CAAwF,QAAxF8C,UAAwF,EAAAlB,gBAAA,CAA5BxB,MAAA,CAAAyC,aAAa,CAACE,IAAI,oBAnBtF/B,mBAAA,gBAoBuCZ,MAAA,CAAAyC,aAAa,CAAC3B,EAAE,Y,cAA/ClB,mBAAA,CAAwF,QAAxFgD,UAAwF,EAAApB,gBAAA,CAA5BxB,MAAA,CAAAyC,aAAa,CAACE,IAAI,oBApBtF/B,mBAAA,gBAqBoCZ,MAAA,CAAAyC,aAAa,CAAC3B,EAAE,Y,cAA5ClB,mBAAA,CAAqF,QAArFiD,UAAqF,EAAArB,gBAAA,CAA5BxB,MAAA,CAAAyC,aAAa,CAACE,IAAI,oBArBnF/B,mBAAA,e,GAuB8CZ,MAAA,CAAAa,OAAO,CAACiC,QAAQ,I,cAAxDlD,mBAAA,CAEM,OAFNmD,WAEM,GADJ7C,YAAA,CAA0Ee,mBAAA;QAA/DC,GAAG,EAAElB,MAAA,CAAAmB,MAAM,CAACnB,MAAA,CAAAa,OAAO,CAACiC,QAAQ;QAAGzB,GAAG,EAAC,OAAO;QAACC,SAAS,EAAC;4CAxBxEV,mBAAA,gBA0BMd,mBAAA,CAYM,OAZNkD,WAYM,GAXOhD,MAAA,CAAAiD,OAAO,I,cAAlBrD,mBAAA,CAEM,OA7BdsD,WAAA,GA4BUpD,mBAAA,CAAiE;QAA5DJ,KAAK,EAAC,0BAA0B;QAACyD,SAAoB,EAAZnD,MAAA,CAAAoD;8BA5BxDC,WAAA,E,KAAAzC,mBAAA,gB,CA8BoBZ,MAAA,CAAAiD,OAAO,IAAIjD,MAAA,CAAAyC,aAAa,CAAC3B,EAAE,Y,cAAvClB,mBAAA,CAAmF,OA9B3F0D,WAAA,EA8ByD,OAAK,GAAA9B,gBAAA,CAAGxB,MAAA,CAAAa,OAAO,CAAC0C,OAAO,IAAG,IAAE,mBA9BrF3C,mBAAA,gB,CA+BoBZ,MAAA,CAAAiD,OAAO,IAAIjD,MAAA,CAAAyC,aAAa,CAAC3B,EAAE,Y,cAAvClB,mBAAA,CAAuD,OA/B/D4D,WAAA,KAAA5C,mBAAA,gBAgCQd,mBAAA,CAKO;QALAC,OAAK,EAAEC,MAAA,CAAAyD;MAAc,IAhCpCC,gBAAA,CAAAlC,gBAAA,CAiCaxB,MAAA,CAAA2D,MAAM,IAAG,OACZ,iB,CAAgB3D,MAAA,CAAAa,OAAO,CAAC+C,WAAW,I,cAAnCnD,YAAA,CAEUN,kBAAA;QApCpBR,GAAA;MAAA;QAAAS,OAAA,EAAAC,QAAA,CAmCY;UAAA,OAAkB,CAAlBH,YAAA,CAAkB2D,yBAAA,E;;QAnC9BtD,CAAA;YAAAK,mBAAA,e,MAwCeZ,MAAA,CAAAiD,OAAO,IAAIjD,MAAA,CAAAyC,aAAa,CAAC3B,EAAE,Y,cADpCL,YAAA,CAIoBqD,4BAAA;QA3C1BnE,GAAA;QAAAoE,UAAA,EAuCkC/D,MAAA,CAAAgE,OAAO;QAvCzC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAuCkClE,MAAA,CAAAgE,OAAO,GAAAE,MAAA;QAAA;QAAGC,GAAG,EAAEnE,MAAA,CAAAa,OAAO,CAAC0C,OAAO;QAAGa,QAAQ,EAAEpE,MAAA,CAAAyC,aAAa,CAAC3B,EAAE;;QAvC7FV,OAAA,EAAAC,QAAA,CAyCqB;UAAA,OAA+B,E,kBAA5CT,mBAAA,CAC4CyE,SAAA,QA1CpDC,WAAA,CAyCoCtE,MAAA,CAAAa,OAAO,CAAC0D,OAAO,EAzCnD,UAyC4BC,IAAI;iCAAxB/D,YAAA,CAC4CgE,sBAAA;cADE9E,GAAG,EAAE6E,IAAI,CAAC1D,EAAE;cAAG4D,KAAK,EAAEF,IAAI,CAAC1D,EAAE;cACxE6D,KAAK,EAAEH,IAAI,CAACI;;;;QA1CvBrE,CAAA;8DAAAK,mBAAA,gB,CA4CkDZ,MAAA,CAAAiD,OAAO,IAAIjD,MAAA,CAAAyC,aAAa,CAAC3B,EAAE,Y,cAAvElB,mBAAA,CAEM,OAFNiF,WAEM,GADJ3E,YAAA,CAA8D2B,oBAAA;QAAnDC,IAAI,EAAC,SAAS;QAAE/B,OAAK,EAAEC,MAAA,CAAA8E;;QA7C1C1E,OAAA,EAAAC,QAAA,CA6CsD;UAAA,OAAI4D,MAAA,QAAAA,MAAA,OA7C1DP,gBAAA,CA6CsD,MAAI,E;;QA7C1DnD,CAAA;cAAAK,mBAAA,gBA+C4CZ,MAAA,CAAAiD,OAAO,IAAIjD,MAAA,CAAAyC,aAAa,CAAC3B,EAAE,Y,cAAjElB,mBAAA,CAWM,OAXNmF,WAWM,I,kBAVJnF,mBAAA,CASMyE,SAAA,QAzDdC,WAAA,CAgDgEtE,MAAA,CAAAa,OAAO,CAAC0D,OAAO,EAhD/E,UAgDgDC,IAAI,EAAEQ,KAAK;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;6BAAnDtF,mBAAA,CASM;UATDF,KAAK,EAAC,oBAAoB;UAA2CC,GAAG,EAAE6E,IAAI,CAAC1D,EAAE;UACnFf,OAAK,WAALA,OAAKA,CAAAmE,MAAA;YAAA,OAAElE,MAAA,CAAAmF,oBAAoB,CAACX,IAAI;UAAA;YACjC1E,mBAAA,CAAkF,OAAlFsF,WAAkF,EAAA5D,gBAAA,CAA3CwD,KAAK,QAAO,GAAC,GAAAxD,gBAAA,CAAGgD,IAAI,CAACI,aAAa,kBACzE1E,YAAA,CAKcmF,sBAAA;UALAC,UAAU,EAAEtF,MAAA,CAAAuF,OAAO,EAAAN,qBAAA,GAACjF,MAAA,CAAAwF,eAAe,CAAChB,IAAI,CAAC1D,EAAE,eAAAmE,qBAAA,uBAAvBA,qBAAA,CAA0BQ,MAAM,EAAEzF,MAAA,CAAA0F,SAAS;UAC1EC,MAAM,EAAE,CAAAT,sBAAA,GAAAlF,MAAA,CAAAwF,eAAe,CAAChB,IAAI,CAAC1D,EAAE,eAAAoE,sBAAA,eAAvBA,sBAAA,CAA0BU,QAAQ,CAAC5F,MAAA,CAAA2B,IAAI,CAACb,EAAE;;UACxCV,OAAO,EAAAC,QAAA,CAChB,UAAAwF,IAAA;YAAA,IAAAC,sBAAA;YAAA,IADoBR,UAAU,GAAAO,IAAA,CAAVP,UAAU;YAAA,QAC9BxF,mBAAA,CAAuE,cAAA0B,gBAAA,CAA9D8D,UAAU,IAAG,IAAE,GAAA9D,gBAAA,EAAAsE,sBAAA,GAAG9F,MAAA,CAAAwF,eAAe,CAAChB,IAAI,CAAC1D,EAAE,eAAAgF,sBAAA,uBAAvBA,sBAAA,CAA0BL,MAAM,IAAG,IAAE,gB;;UAtD9ElF,CAAA;uFAAAwF,WAAA;0CAAAnF,mBAAA,e;;IAAAL,CAAA;wCAQgFP,MAAA,CAAAgG,eAAe,CAAClF,EAAE,E,IARlGF,mBAAA,gB,gBA4DIV,YAAA,CAOea,uBAAA;IAPDrB,KAAK,EAAC;EAA4B;IA5DpDU,OAAA,EAAAC,QAAA,CA6DM;MAAA,OAA+F,CAA/FP,mBAAA,CAA+F,OAA/FmG,WAA+F,EAAAzE,gBAAA,CAAhDxB,MAAA,CAAAgG,eAAe,CAACpB,aAAa,6B,kBAC5EhF,mBAAA,CAIMyE,SAAA,QAlEZC,WAAA,CA8D4DtE,MAAA,CAAAkG,eAAe,EA9D3E,UA8DoD1B,IAAI;6BAAlD5E,mBAAA,CAIM;UAJDF,KAAK,EAAC,2BAA2B;UAAkCC,GAAG,EAAE6E,IAAI,CAAC1D;YAChFZ,YAAA,CAAsEe,mBAAA;UAA3DC,GAAG,EAAElB,MAAA,CAAAmB,MAAM,CAACqD,IAAI,CAACpD,OAAO;UAAGC,GAAG,EAAC,OAAO;UAACC,SAAS,EAAC;0CAC5DxB,mBAAA,CAAyE,OAAzEqG,WAAyE,EAAA3E,gBAAA,CAAtBgD,IAAI,CAAC4B,QAAQ,kBAChEtG,mBAAA,CAAwE,OAAxEuG,WAAwE,EAAA7E,gBAAA,CAA9BxB,MAAA,CAAAsC,MAAM,CAACkC,IAAI,CAAC8B,QAAQ,kB;;;IAjEtE/F,CAAA;sCA4D6DP,MAAA,CAAAgG,eAAe,CAAClF,EAAE,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}