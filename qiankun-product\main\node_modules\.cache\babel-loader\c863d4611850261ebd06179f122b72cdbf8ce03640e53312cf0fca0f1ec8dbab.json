{"ast": null, "code": "import { createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, resolveDirective as _resolveDirective, openBlock as _openBlock, createBlock as _createBlock, withDirectives as _withDirectives, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"VersionComparison\"\n};\nvar _hoisted_2 = {\n  class: \"VersionComparisonBody\"\n};\nvar _hoisted_3 = {\n  class: \"VersionComparisonDataBody\"\n};\nvar _hoisted_4 = {\n  class: \"VersionComparisonDataFile\"\n};\nvar _hoisted_5 = {\n  class: \"VersionComparisonWordBody\"\n};\nvar _hoisted_6 = {\n  class: \"VersionComparisonWordButton\"\n};\nvar _hoisted_7 = {\n  class: \"VersionComparisonWordButtonItem\"\n};\nvar _hoisted_8 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_withDirectives((_openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"VersionComparisonScrollbar\",\n    \"lement-loading-text\": $setup.loadingText,\n    always: \"\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode($setup[\"VersionComparisonFileInfo\"], {\n        name: \"旧版本\",\n        id: $setup.oldId,\n        onLoadCallback: $setup.oldCallback\n      }, null, 8 /* PROPS */, [\"id\"]), _createVNode($setup[\"VersionComparisonFileInfo\"], {\n        name: \"新版本\",\n        id: $setup.newId,\n        onLoadCallback: $setup.newCallback\n      }, null, 8 /* PROPS */, [\"id\"])])]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"VersionComparisonWordButtonItem\"\n      }, \" 对照表 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_button, {\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.handleButton(false);\n        }),\n        type: \"primary\",\n        round: \"\"\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"生成对照表\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.handleFileDownload,\n        type: \"primary\",\n        round: \"\"\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"下载\")]);\n        }),\n        _: 1 /* STABLE */\n      })])]), _createVNode(_component_el_scrollbar, {\n        class: \"VersionComparisonWordScrollbar\",\n        always: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", {\n            class: \"VersionComparisonWord\",\n            innerHTML: $setup.html\n          }, null, 8 /* PROPS */, _hoisted_8)];\n        }),\n        _: 1 /* STABLE */\n      })])])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"lement-loading-text\"])), [[_directive_loading, $setup.loading]]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"下载\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"DownloadFileName\"], {\n        onCallback: $setup.callback\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createBlock", "_component_el_scrollbar", "$setup", "loadingText", "always", "default", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "name", "id", "oldId", "onLoadCallback", "oldCallback", "newId", "newCallback", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_component_el_button", "onClick", "_cache", "$event", "handleButton", "type", "round", "_createTextVNode", "_", "handleFileDownload", "innerHTML", "html", "_hoisted_8", "loading", "_component_xyl_popup_window", "modelValue", "show", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\Intelligentize\\VersionComparison\\VersionComparison.vue"], "sourcesContent": ["<template>\r\n  <div class=\"VersionComparison\">\r\n    <el-scrollbar class=\"VersionComparisonScrollbar\"\r\n                  v-loading=\"loading\"\r\n                  :lement-loading-text=\"loadingText\"\r\n                  always>\r\n      <div class=\"VersionComparisonBody\">\r\n        <div class=\"VersionComparisonDataBody\">\r\n          <div class=\"VersionComparisonDataFile\">\r\n            <VersionComparisonFileInfo name=\"旧版本\"\r\n                                       :id=\"oldId\"\r\n                                       @loadCallback=\"oldCallback\"></VersionComparisonFileInfo>\r\n            <VersionComparisonFileInfo name=\"新版本\"\r\n                                       :id=\"newId\"\r\n                                       @loadCallback=\"newCallback\"></VersionComparisonFileInfo>\r\n          </div>\r\n        </div>\r\n        <div class=\"VersionComparisonWordBody\">\r\n          <div class=\"VersionComparisonWordButton\">\r\n            <div class=\"VersionComparisonWordButtonItem\">\r\n              对照表\r\n            </div>\r\n            <div class=\"VersionComparisonWordButtonItem\">\r\n              <el-button @click=\"handleButton(false)\"\r\n                         type=\"primary\"\r\n                         round>生成对照表</el-button>\r\n              <el-button @click=\"handleFileDownload\"\r\n                         type=\"primary\"\r\n                         round>下载</el-button>\r\n            </div>\r\n          </div>\r\n          <el-scrollbar class=\"VersionComparisonWordScrollbar\"\r\n                        always>\r\n            <div class=\"VersionComparisonWord\"\r\n                 v-html=\"html\"></div>\r\n          </el-scrollbar>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <xyl-popup-window v-model=\"show\"\r\n                      name=\"下载\">\r\n      <DownloadFileName @callback=\"callback\"></DownloadFileName>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template >\r\n<script>\r\nexport default { name: 'VersionComparison' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport store from '@/store'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport DownloadFileName from './DownloadFileName/DownloadFileName'\r\nimport VersionComparisonFileInfo from './VersionComparisonFileInfo'\r\nconst route = useRoute()\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n// const tabId = ref('4')\r\nconst oldId = ref('')\r\nconst newId = ref('')\r\nconst oldFile = ref()\r\nconst newFile = ref()\r\nconst url = ref('')\r\nconst fileName = ref('')\r\nconst html = ref('')\r\nconst show = ref(false)\r\n\r\nonMounted(() => {\r\n  if (route.query.ids) {\r\n    const idList = route.query.ids.split(',')\r\n    oldId.value = idList[0]\r\n    newId.value = idList[1]\r\n  }\r\n})\r\n\r\n// const tabClick = (row) => { tabId.value = row }\r\nconst oldCallback = (file) => {\r\n  oldFile.value = file\r\n}\r\nconst newCallback = (file) => {\r\n  newFile.value = file\r\n}\r\n// const loadCallback = (type) => {\r\n//   loading.value = type\r\n// }\r\nconst handleButton = (type) => {\r\n  if ((oldId.value && newId.value) || (oldFile.value && newFile.value)) {\r\n    if (type) { wordApiPaintedWord() } else { wordApiContrastWord() }\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请先上传需要对比的两个原始法规！' })\r\n  }\r\n}\r\n/**\r\n   * 版本对比-花脸稿接口\r\n  */\r\nconst wordApiPaintedWord = async () => {\r\n  try {\r\n    loading.value = true\r\n    const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区\r\n    const param = new FormData()\r\n    if (oldId.value && newId.value) {\r\n      param.append('urlPath1', api.filePreview(oldId.value))\r\n      param.append('urlPath2', api.filePreview(newId.value))\r\n    } else if (oldFile.value && newFile.value) {\r\n      param.append('file1', oldFile.value)\r\n      param.append('file2', newFile.value)\r\n    }\r\n    param.append('areaCode', AreaId)\r\n    const { data } = await api.wordApiPaintedWord(param, () => { }, '')\r\n    loading.value = false\r\n    fileName.value = data.fileName\r\n    html.value = data.html\r\n    url.value = URL.createObjectURL(new Blob([dataURLtoBlob(data.pdfBase64)]))\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\n/**\r\n   * 版本对比-对照表接口\r\n  */\r\nconst wordApiContrastWord = async () => {\r\n  try {\r\n    loading.value = true\r\n    const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区\r\n    const param = new FormData()\r\n    if (oldId.value && newId.value) {\r\n      param.append('urlPath1', api.filePreview(oldId.value))\r\n      param.append('urlPath2', api.filePreview(newId.value))\r\n    } else if (oldFile.value && newFile.value) {\r\n      param.append('file1', oldFile.value)\r\n      param.append('file2', newFile.value)\r\n    }\r\n    param.append('areaCode', AreaId)\r\n    const { data } = await api.wordApiContrastWord(param, () => { }, '')\r\n    loading.value = false\r\n    fileName.value = data.fileName\r\n    html.value = data.html\r\n    url.value = URL.createObjectURL(new Blob([dataURLtoBlob(data.pdfBase64)]))\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nfunction dataURLtoBlob (str) {\r\n  const bstr = window.atob(str)\r\n  var n = bstr.length\r\n  const u8arr = new Uint8Array(n)\r\n  while (n--) {\r\n    u8arr[n] = bstr.charCodeAt(n)\r\n  }\r\n  return new Blob([u8arr], { type: 'application/pdf' })\r\n}\r\nconst handleFileDownload = () => {\r\n  if (!fileName.value) return ElMessage({ type: 'warning', message: '当前没有内容可下载！' })\r\n  show.value = true\r\n}\r\nconst callback = (name) => {\r\n  if (name) {\r\n    store.commit('setExtendDownloadFile', { url: '/wordApi/downloadWord', params: { fileName: fileName.value }, fileSize: 0, fileName: `${name}.docx`, fileType: 'docx' })\r\n    // extendDownloadFile({ url: '/wordApi/downloadWord', params: { fileName: fileName.value }, fileSize: 0, fileName: `${name}.docx`, fileType: 'docx' })\r\n  }\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.VersionComparison {\r\n  width: 100%;\r\n  height: 100%;\r\n  // height: 100vh;\r\n  background: var(--zy-el-color-info-light-9);\r\n\r\n  .VersionComparisonScrollbar {\r\n    width: 100%;\r\n    height: calc(100vh - 62px);\r\n  }\r\n\r\n  .VersionComparisonBody {\r\n    width: 100%;\r\n    max-width: 1660px;\r\n    height: calc(100vh - 62px);\r\n    margin: auto;\r\n    padding: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .VersionComparisonWordBody {\r\n      width: 880px;\r\n      height: 100%;\r\n      border-radius: 8px;\r\n      box-shadow: var(--zy-el-box-shadow);\r\n\r\n\r\n      .VersionComparisonWordButton {\r\n        width: 100%;\r\n        height: 50px;\r\n        color: #fff;\r\n        background-image: linear-gradient(72deg, var(--zy-el-color-primary), var(--zy-el-color-primary-light-9));\r\n        // background-color: var(--zy-el-color-primary-light-5);\r\n        // margin-bottom: 10px;\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 0 20px;\r\n        border-top-left-radius: 8px;\r\n        border-top-right-radius: 8px;\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .VersionComparisonWordButtonItem {\r\n          display: flex;\r\n\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n        }\r\n      }\r\n\r\n      .VersionComparisonWordScrollbar {\r\n        width: 100%;\r\n        height: calc(100% - 50px);\r\n        background-color: #fff;\r\n        border-bottom-left-radius: 8px;\r\n        border-bottom-right-radius: 8px;\r\n\r\n        .VersionComparisonWord {\r\n          width: 595.3pt;\r\n          min-height: 842pt;\r\n          box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n          margin: 20px auto;\r\n          padding: 44.9pt 73pt;\r\n        }\r\n      }\r\n    }\r\n\r\n    .VersionComparisonDataBody {\r\n      width: calc(100% - 890px);\r\n      // margin-left: 10px;\r\n\r\n      .VersionComparisonDataTab {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        height: 60px;\r\n        padding: 0 20px;\r\n        padding-bottom: 10px;\r\n\r\n        .VersionComparisonDataTabItem {\r\n          width: 25%;\r\n          height: var(--zy-height);\r\n          box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.16);\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-weight: bold;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .VersionComparisonDataTabItem.is-active {\r\n          color: #fff;\r\n          background-color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .VersionComparisonDataFile {\r\n        width: 100%;\r\n        // height: calc(100% - 60px);\r\n        height: calc(100%);\r\n        padding-right: 10px;\r\n\r\n        .VersionComparisonFileInfo {\r\n          background-color: #fff;\r\n        }\r\n\r\n        .LawRetrieval {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAKrBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAA2B;;EASnCA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAA6B;;EAIjCA,KAAK,EAAC;AAAiC;iBAtBxD;;;;;;uBACEC,mBAAA,CA0CM,OA1CNC,UA0CM,G,+BAzCJC,YAAA,CAoCeC,uBAAA;IApCDJ,KAAK,EAAC,4BAA4B;IAEjC,qBAAmB,EAAEK,MAAA,CAAAC,WAAW;IACjCC,MAAM,EAAN;;IALlBC,OAAA,EAAAC,QAAA,CAMM;MAAA,OA+BM,CA/BNC,mBAAA,CA+BM,OA/BNC,UA+BM,GA9BJD,mBAAA,CASM,OATNE,UASM,GARJF,mBAAA,CAOM,OAPNG,UAOM,GANJC,YAAA,CAEmFT,MAAA;QAFxDU,IAAI,EAAC,KAAK;QACTC,EAAE,EAAEX,MAAA,CAAAY,KAAK;QACTC,cAAY,EAAEb,MAAA,CAAAc;uCAC1CL,YAAA,CAEmFT,MAAA;QAFxDU,IAAI,EAAC,KAAK;QACTC,EAAE,EAAEX,MAAA,CAAAe,KAAK;QACTF,cAAY,EAAEb,MAAA,CAAAgB;2CAG9CX,mBAAA,CAmBM,OAnBNY,UAmBM,GAlBJZ,mBAAA,CAYM,OAZNa,UAYM,G,0BAXJb,mBAAA,CAEM;QAFDV,KAAK,EAAC;MAAiC,GAAC,OAE7C,sBACAU,mBAAA,CAOM,OAPNc,UAOM,GANJV,YAAA,CAEkCW,oBAAA;QAFtBC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEvB,MAAA,CAAAwB,YAAY;QAAA;QACpBC,IAAI,EAAC,SAAS;QACdC,KAAK,EAAL;;QAzBzBvB,OAAA,EAAAC,QAAA,CAyB+B;UAAA,OAAKkB,MAAA,QAAAA,MAAA,OAzBpCK,gBAAA,CAyB+B,OAAK,E;;QAzBpCC,CAAA;UA0BcnB,YAAA,CAE+BW,oBAAA;QAFnBC,OAAK,EAAErB,MAAA,CAAA6B,kBAAkB;QAC1BJ,IAAI,EAAC,SAAS;QACdC,KAAK,EAAL;;QA5BzBvB,OAAA,EAAAC,QAAA,CA4B+B;UAAA,OAAEkB,MAAA,QAAAA,MAAA,OA5BjCK,gBAAA,CA4B+B,IAAE,E;;QA5BjCC,CAAA;cA+BUnB,YAAA,CAIeV,uBAAA;QAJDJ,KAAK,EAAC,gCAAgC;QACtCO,MAAM,EAAN;;QAhCxBC,OAAA,EAAAC,QAAA,CAiCY;UAAA,OACyB,CADzBC,mBAAA,CACyB;YADpBV,KAAK,EAAC,uBAAuB;YAC7BmC,SAAa,EAAL9B,MAAA,CAAA+B;kCAlCzBC,UAAA,E;;QAAAJ,CAAA;;;IAAAA,CAAA;qEAG6B5B,MAAA,CAAAiC,OAAO,E,GAoChCxB,YAAA,CAGmByB,2BAAA;IA1CvBC,UAAA,EAuC+BnC,MAAA,CAAAoC,IAAI;IAvCnC,uBAAAd,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAuC+BvB,MAAA,CAAAoC,IAAI,GAAAb,MAAA;IAAA;IACbb,IAAI,EAAC;;IAxC3BP,OAAA,EAAAC,QAAA,CAyCM;MAAA,OAA0D,CAA1DK,YAAA,CAA0DT,MAAA;QAAvCqC,UAAQ,EAAErC,MAAA,CAAAsC;MAAQ,G;;IAzC3CV,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}