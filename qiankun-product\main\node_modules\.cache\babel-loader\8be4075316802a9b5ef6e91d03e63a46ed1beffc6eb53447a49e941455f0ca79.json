{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createBlock as _createBlock, vShow as _vShow, withDirectives as _withDirectives, resolveDirective as _resolveDirective, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalGroupAddUser\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalGroupAddUserList\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalGroupAddUserInput\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  class: \"GlobalGroupAddUserLabel\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalGroupAddUserItem\"\n};\nvar _hoisted_6 = {\n  class: \"GlobalGroupAddUserName ellipsis\"\n};\nvar _hoisted_7 = {\n  key: 0,\n  class: \"GlobalGroupAddUserLabel\"\n};\nvar _hoisted_8 = {\n  class: \"GlobalGroupAddUserItem\"\n};\nvar _hoisted_9 = {\n  class: \"GlobalGroupAddUserName ellipsis\"\n};\nvar _hoisted_10 = {\n  class: \"GlobalGroupAddUserBody\"\n};\nvar _hoisted_11 = {\n  class: \"GlobalGroupAddUserInfo\"\n};\nvar _hoisted_12 = {\n  class: \"GlobalGroupAddUserInfoName\"\n};\nvar _hoisted_13 = {\n  class: \"GlobalGroupAddUserUserBody\"\n};\nvar _hoisted_14 = [\"onClick\"];\nvar _hoisted_15 = {\n  class: \"GlobalGroupAddUserUserName ellipsis\"\n};\nvar _hoisted_16 = {\n  class: \"GlobalGroupAddUserButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  var _component_el_tree = _resolveComponent(\"el-tree\");\n  var _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_CircleCloseFilled = _resolveComponent(\"CircleCloseFilled\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_input, {\n    modelValue: $setup.keyword,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.keyword = $event;\n    }),\n    \"prefix-icon\": $setup.Search,\n    placeholder: \"搜索\",\n    onInput: $setup.handleQuery,\n    clearable: \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\", \"onInput\"])]), _withDirectives((_openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"GlobalGroupAddUserScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_withDirectives(_createVNode(_component_el_checkbox_group, {\n        modelValue: $setup.checkedUser,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.checkedUser = $event;\n        })\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree, {\n            \"node-key\": \"id\",\n            data: $setup.searchData\n          }, {\n            default: _withCtx(function (_ref) {\n              var _$setup$disabledId;\n              var data = _ref.data;\n              return [data.type !== 'user' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _toDisplayString(data.label), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), data.type === 'user' ? (_openBlock(), _createBlock(_component_el_checkbox, {\n                key: 1,\n                value: data.id,\n                disabled: (_$setup$disabledId = $setup.disabledId) === null || _$setup$disabledId === void 0 ? void 0 : _$setup$disabledId.includes(data.id),\n                onChange: function onChange($event) {\n                  return $setup.handleChange(data);\n                }\n              }, {\n                default: _withCtx(function () {\n                  return [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_image, {\n                    src: $setup.imgUrl(data.user.photo || data.user.headImg),\n                    fit: \"cover\",\n                    draggable: \"false\"\n                  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(data.user.userName), 1 /* TEXT */)])];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\", \"disabled\", \"onChange\"])) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"data\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), [[_vShow, !!$setup.keyword]]), _withDirectives(_createVNode(_component_el_checkbox_group, {\n        modelValue: $setup.checkedUser,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.checkedUser = $event;\n        })\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree, {\n            lazy: \"\",\n            load: $setup.loadNode,\n            \"node-key\": \"id\",\n            props: {\n              isLeaf: 'isLeaf'\n            }\n          }, {\n            default: _withCtx(function (_ref2) {\n              var _$setup$disabledId2;\n              var data = _ref2.data;\n              return [data.type !== 'user' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, _toDisplayString(data.label), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), data.type === 'user' ? (_openBlock(), _createBlock(_component_el_checkbox, {\n                key: 1,\n                value: data.id,\n                disabled: (_$setup$disabledId2 = $setup.disabledId) === null || _$setup$disabledId2 === void 0 ? void 0 : _$setup$disabledId2.includes(data.id),\n                onChange: function onChange($event) {\n                  return $setup.handleChange(data);\n                }\n              }, {\n                default: _withCtx(function () {\n                  return [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_image, {\n                    src: $setup.imgUrl(data.user.photo || data.user.headImg),\n                    fit: \"cover\",\n                    draggable: \"false\"\n                  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_9, _toDisplayString(data.user.userName), 1 /* TEXT */)])];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\", \"disabled\", \"onChange\"])) : _createCommentVNode(\"v-if\", true)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), [[_vShow, !$setup.keyword]])];\n    }),\n    _: 1 /* STABLE */\n  })), [[_directive_loading, $setup.loading]])]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[3] || (_cache[3] = _createTextVNode(\"选择联系人 \")), _createElementVNode(\"span\", null, \"已选择\" + _toDisplayString($setup.checkedUserData.length) + \"位联系人\", 1 /* TEXT */)])]), _createVNode(_component_el_scrollbar, {\n    class: \"GlobalGroupAddUserUserScroll\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_13, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.checkedUserData, function (item) {\n        var _$setup$disabledId3;\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"GlobalGroupAddUserUser\",\n          key: item.id\n        }, [!((_$setup$disabledId3 = $setup.disabledId) !== null && _$setup$disabledId3 !== void 0 && _$setup$disabledId3.includes(item.id)) ? (_openBlock(), _createElementBlock(\"div\", {\n          key: 0,\n          class: \"GlobalGroupAddUserUserDel\",\n          onClick: function onClick($event) {\n            return $setup.handleDelClick(item);\n          }\n        }, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_CircleCloseFilled)];\n          }),\n          _: 1 /* STABLE */\n        })], 8 /* PROPS */, _hoisted_14)) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_image, {\n          src: $setup.imgUrl(item.user.photo || item.user.headImg),\n          fit: \"cover\",\n          draggable: \"false\"\n        }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_15, _toDisplayString(item.user.userName), 1 /* TEXT */)]);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_button, {\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return _cache[4] || (_cache[4] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSubmit,\n    disabled: !$setup.checkedUserData.length\n  }, {\n    default: _withCtx(function () {\n      return _cache[5] || (_cache[5] = [_createTextVNode(\"完成\")]);\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\"])])])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_input", "modelValue", "$setup", "keyword", "_cache", "$event", "Search", "placeholder", "onInput", "handleQuery", "clearable", "_createBlock", "_component_el_scrollbar", "default", "_withCtx", "_component_el_checkbox_group", "checkedUser", "_component_el_tree", "data", "searchData", "_ref", "_$setup$disabledId", "type", "_hoisted_4", "_toDisplayString", "label", "_createCommentVNode", "_component_el_checkbox", "value", "id", "disabled", "disabledId", "includes", "onChange", "handleChange", "_hoisted_5", "_component_el_image", "src", "imgUrl", "user", "photo", "headImg", "fit", "draggable", "_hoisted_6", "userName", "_", "lazy", "load", "loadNode", "props", "<PERSON><PERSON><PERSON><PERSON>", "_ref2", "_$setup$disabledId2", "_hoisted_7", "_hoisted_8", "_hoisted_9", "loading", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_createTextVNode", "checkedUserData", "length", "_hoisted_13", "_Fragment", "_renderList", "item", "_$setup$disabledId3", "onClick", "handleDelClick", "_component_el_icon", "_component_CircleCloseFilled", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_component_el_button", "handleReset", "handleSubmit"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalGroupAddUser\\GlobalGroupAddUser.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalGroupAddUser\">\r\n    <div class=\"GlobalGroupAddUserList\">\r\n      <div class=\"GlobalGroupAddUserInput\">\r\n        <el-input v-model=\"keyword\" :prefix-icon=\"Search\" placeholder=\"搜索\" @input=\"handleQuery\" clearable />\r\n      </div>\r\n      <el-scrollbar class=\"GlobalGroupAddUserScrollbar\" v-loading=\"loading\">\r\n        <el-checkbox-group v-model=\"checkedUser\" v-show=\"!(!keyword)\">\r\n          <el-tree node-key=\"id\" :data=\"searchData\">\r\n            <template #default=\"{ data }\">\r\n              <div class=\"GlobalGroupAddUserLabel\" v-if=\"data.type !== 'user'\">{{ data.label }}</div>\r\n              <el-checkbox :value=\"data.id\" :disabled=\"disabledId?.includes(data.id)\" @change=\"handleChange(data)\"\r\n                v-if=\"data.type === 'user'\">\r\n                <div class=\"GlobalGroupAddUserItem\">\r\n                  <el-image :src=\"imgUrl(data.user.photo || data.user.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n                  <div class=\"GlobalGroupAddUserName ellipsis\">{{ data.user.userName }}</div>\r\n                </div>\r\n              </el-checkbox>\r\n            </template>\r\n          </el-tree>\r\n        </el-checkbox-group>\r\n        <el-checkbox-group v-model=\"checkedUser\" v-show=\"!keyword\">\r\n          <el-tree lazy :load=\"loadNode\" node-key=\"id\" :props=\"{ isLeaf: 'isLeaf' }\">\r\n            <template #default=\"{ data }\">\r\n              <div class=\"GlobalGroupAddUserLabel\" v-if=\"data.type !== 'user'\">{{ data.label }}</div>\r\n              <el-checkbox :value=\"data.id\" :disabled=\"disabledId?.includes(data.id)\" @change=\"handleChange(data)\"\r\n                v-if=\"data.type === 'user'\">\r\n                <div class=\"GlobalGroupAddUserItem\">\r\n                  <el-image :src=\"imgUrl(data.user.photo || data.user.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n                  <div class=\"GlobalGroupAddUserName ellipsis\">{{ data.user.userName }}</div>\r\n                </div>\r\n              </el-checkbox>\r\n            </template>\r\n          </el-tree>\r\n        </el-checkbox-group>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"GlobalGroupAddUserBody\">\r\n      <div class=\"GlobalGroupAddUserInfo\">\r\n        <div class=\"GlobalGroupAddUserInfoName\">选择联系人 <span>已选择{{ checkedUserData.length }}位联系人</span></div>\r\n      </div>\r\n      <el-scrollbar class=\"GlobalGroupAddUserUserScroll\">\r\n        <div class=\"GlobalGroupAddUserUserBody\">\r\n          <div class=\"GlobalGroupAddUserUser\" v-for=\"item in checkedUserData\" :key=\"item.id\">\r\n            <div class=\"GlobalGroupAddUserUserDel\" @click=\"handleDelClick(item)\" v-if=\"!disabledId?.includes(item.id)\">\r\n              <el-icon>\r\n                <CircleCloseFilled />\r\n              </el-icon>\r\n            </div>\r\n            <el-image :src=\"imgUrl(item.user.photo || item.user.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n            <div class=\"GlobalGroupAddUserUserName ellipsis\">{{ item.user.userName }}</div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <div class=\"GlobalGroupAddUserButton\">\r\n        <el-button @click=\"handleReset\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleSubmit\" :disabled=\"(!checkedUserData.length)\">完成</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalGroupAddUser' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { user, appOnlyHeader } from 'common/js/system_var.js'\r\nimport { Search } from '@element-plus/icons-vue'\r\nconst store = useStore()\r\nconst props = defineProps({ infoId: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\nconst rongCloudUrl = computed(() => store.getters.getRongCloudUrl)\r\nconst isPrivatization = computed(() => store.getters.getIsPrivatization)\r\nconst groupInfo = ref({})\r\nconst keyword = ref('')\r\nconst labelAll = ref([])\r\nconst loading = ref(false)\r\nconst searchData = ref([])\r\nconst disabledId = ref([])\r\nconst checkedUser = ref([])\r\nconst checkedUserData = ref([])\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\n\r\nconst throttle = (fn, delay) => {\r\n  let lastTime = 0\r\n  return function (...args) {\r\n    const now = Date.now()\r\n    if (now - lastTime >= delay) {\r\n      fn.apply(this, args)\r\n      lastTime = now\r\n    }\r\n  }\r\n}\r\nconst handleQuery = throttle(async () => {\r\n  loading.value = !(!keyword.value)\r\n  let newUserDataAll = []\r\n  if (keyword.value) {\r\n    for (let index = 0; index < labelAll.value.length; index++) {\r\n      const item = labelAll.value[index]\r\n      const newUserData = await handleUserData(item.id)\r\n      newUserDataAll = [...newUserDataAll, ...newUserData]\r\n    }\r\n  }\r\n  loading.value = false\r\n  searchData.value = [...new Map(newUserDataAll.map(item => [item.id, item])).values()]\r\n}, 300)\r\nconst handleUserData = async (id) => {\r\n  try {\r\n    const { data } = await api.SelectPersonBookUser({ isOpen: 1, keyword: keyword.value, labelCode: id, nodeId: '', relationBookId: '', tabCode: 'relationBooksTemp' })\r\n    const newUserData = []\r\n    for (let index = 0; index < data.length; index++) {\r\n      const item = data[index]\r\n      if (item.accountId) newUserData.push({ id: item.accountId, label: item.userName, children: [], type: 'user', user: item, isLeaf: true })\r\n    }\r\n    return newUserData\r\n  } catch (error) {\r\n    return []\r\n  }\r\n}\r\nconst handleChange = (item) => {\r\n  if (checkedUser.value?.includes(item.id)) {\r\n    checkedUserData.value.push(item)\r\n  } else {\r\n    checkedUserData.value = checkedUserData.value.filter(v => v.id !== item.id)\r\n  }\r\n}\r\nconst handleDelClick = (item) => {\r\n  checkedUser.value = checkedUser.value.filter(v => v !== item.id)\r\n  checkedUserData.value = checkedUserData.value.filter(v => v.id !== item.id)\r\n}\r\nconst handleSubmit = async () => {\r\n  const { code } = await api.chatGroupEdit({\r\n    form: { id: props.infoId, groupName: groupInfo.value.groupName },\r\n    ownerUserId: groupInfo.value.ownerUserId, memberUserIds: checkedUser.value\r\n  })\r\n  if (code === 200) handleCreateGroup()\r\n}\r\nconst handleCreateGroup = async () => {\r\n  const { code } = await api.rongCloud(rongCloudUrl.value, {\r\n    type: 'joinGroup',\r\n    userIds: checkedUserData.value.map(v => `${appOnlyHeader.value}${v.id}`).join(','),\r\n    groupId: `${appOnlyHeader.value}${props.infoId}`,\r\n    groupName: groupInfo.value.groupName,\r\n    environment: 1\r\n  }, isPrivatization.value)\r\n  if (code === 200) {\r\n    const joinName = checkedUserData.value.map(v => v.label).join('、')\r\n    const dataJoinName = checkedUserData.value.map(v => `||${v.label}|OUI|${v.id}||`).join('、')\r\n    const sendMessageData = {\r\n      name: `${user.value?.userName} 将 ${joinName} 加入群聊`,\r\n      data: `${user.value?.userName}|OUI|${user.value?.accountId}|| 将 ${dataJoinName} 加入群聊`,\r\n    }\r\n    emit('callback', true, sendMessageData)\r\n  }\r\n}\r\nconst handleReset = () => { emit('callback', false) }\r\nconst SelectPersonTab = async (resolve) => {\r\n  const { data } = await api.SelectPersonTab({ tabCodes: ['relationBooksTemp'] })\r\n  const newLabelData = []\r\n  for (let index = 0; index < data[0]?.chooseLabels.length; index++) {\r\n    const item = data[0]?.chooseLabels[index]\r\n    newLabelData.push({ id: item.labelCode, label: item.name, children: [], type: 'label', isLeaf: false })\r\n  }\r\n  labelAll.value = newLabelData\r\n  resolve(newLabelData)\r\n}\r\nconst handleTreeData = (id, data) => {\r\n  const newLabelData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.code !== id) {\r\n      const children = handleTreeData(id, item.children)\r\n      newLabelData.push({ id: item.code, label: item.name, children: children, type: 'tree', isLeaf: false })\r\n    }\r\n  }\r\n  return newLabelData\r\n}\r\nconst SelectPersonGroup = async (id) => {\r\n  const { data } = await api.SelectPersonGroup({ labelCode: id, tabCode: 'relationBooksTemp' })\r\n  return handleTreeData(id, data)\r\n}\r\nconst SelectPersonBookUser = async (parentId, id) => {\r\n  const { data } = await api.SelectPersonBookUser({ isOpen: 1, keyword: '', labelCode: parentId, nodeId: id, relationBookId: id, tabCode: 'relationBooksTemp' })\r\n  const newUserData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.accountId) newUserData.push({ id: item.accountId, label: item.userName, children: [], type: 'user', user: item, isLeaf: true })\r\n  }\r\n  return newUserData\r\n}\r\nconst loadNode = async (node, resolve) => {\r\n  if (node.level === 0) {\r\n    SelectPersonTab(resolve)\r\n  } else {\r\n    if (node.data?.children?.length) {\r\n      const newTreeData = node.data?.children\r\n      const newUserData = await SelectPersonBookUser(node.parent.key, node.key)\r\n      const newData = [...newTreeData, ...newUserData]\r\n      resolve(newData)\r\n    } else {\r\n      if (node.parent.level) {\r\n        const newUserData = await SelectPersonBookUser(node.parent.key, node.key)\r\n        resolve(newUserData)\r\n      } else {\r\n        const newTreeData = await SelectPersonGroup(node.key)\r\n        resolve(newTreeData)\r\n      }\r\n    }\r\n  }\r\n}\r\nconst chatGroupInfo = async () => {\r\n  const { data } = await api.chatGroupInfo({ detailId: props.infoId })\r\n  groupInfo.value = data\r\n  disabledId.value = data.memberUserIds\r\n  checkedUser.value = data.memberUserIds\r\n}\r\nonMounted(() => { chatGroupInfo() })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalGroupAddUser {\r\n  width: 690px;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  .GlobalGroupAddUserList {\r\n    width: 280px;\r\n    height: 100%;\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalGroupAddUserInput {\r\n      width: 100%;\r\n      height: 56px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n\r\n      .zy-el-input {\r\n        width: 240px;\r\n        height: var(--zy-height-routine);\r\n      }\r\n    }\r\n\r\n    .GlobalGroupAddUserScrollbar {\r\n      width: 100%;\r\n      height: calc(100% - 56px);\r\n\r\n      .zy-el-tree {\r\n        padding: 0 20px 20px 20px;\r\n\r\n        .zy-el-tree-node {\r\n          .zy-el-tree-node__content {\r\n            height: auto;\r\n            padding: 10px 0;\r\n            position: relative;\r\n            background: transparent;\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-checkbox {\r\n        width: 100%;\r\n        height: auto;\r\n        margin: 0;\r\n        position: relative;\r\n\r\n        .zy-el-checkbox__input {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: -5px;\r\n          transform: translate(-100%, -50%);\r\n        }\r\n\r\n        .zy-el-checkbox__label {\r\n          width: 100%;\r\n          padding: 0;\r\n        }\r\n      }\r\n\r\n      .GlobalGroupAddUserLabel {\r\n        &::after {\r\n          content: \"\";\r\n          width: 100%;\r\n          border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n          position: absolute;\r\n          right: 0;\r\n          bottom: 0;\r\n        }\r\n      }\r\n\r\n      .GlobalGroupAddUserItem {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        position: relative;\r\n\r\n        &.is-active {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 38px;\r\n          height: 38px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .GlobalGroupAddUserName {\r\n          width: calc(100% - 54px);\r\n          font-size: 14px;\r\n\r\n          &::after {\r\n            content: \"\";\r\n            width: calc(100% - 54px);\r\n            border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n            position: absolute;\r\n            right: 0;\r\n            bottom: -10px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalGroupAddUserBody {\r\n    width: calc(100% - 280px);\r\n    height: 100%;\r\n    padding-bottom: 20px;\r\n\r\n    .GlobalGroupAddUserInfo {\r\n      width: 100%;\r\n      height: 56px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n\r\n      .GlobalGroupAddUserInfoName {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        font-size: 14px;\r\n\r\n        span {\r\n          font-size: 12px;\r\n          color: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalGroupAddUserUserScroll {\r\n      width: 100%;\r\n      height: calc(100% - 102px);\r\n    }\r\n\r\n    .GlobalGroupAddUserUserBody {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      align-items: center;\r\n      padding: 10px 20px;\r\n\r\n      .GlobalGroupAddUserUser {\r\n        width: 25%;\r\n        display: flex;\r\n        align-items: center;\r\n        flex-direction: column;\r\n        padding: 10px 0;\r\n        position: relative;\r\n\r\n        .GlobalGroupAddUserUserDel {\r\n          width: 20px;\r\n          height: 20px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          color: var(--zy-el-text-color-secondary);\r\n          cursor: pointer;\r\n          position: absolute;\r\n          top: 2px;\r\n          right: 16px;\r\n          font-size: 16px;\r\n          z-index: 2;\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 46px;\r\n          height: 46px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .GlobalGroupAddUserUserName {\r\n          width: 100%;\r\n          font-size: 14px;\r\n          text-align: center;\r\n          padding: 0 6px;\r\n          padding-top: 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalGroupAddUserButton {\r\n      width: 100%;\r\n      height: 46px;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      justify-content: center;\r\n\r\n      .zy-el-button {\r\n        width: 120px;\r\n        height: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAyB;;EAH1CC,GAAA;EAUmBD,KAAK,EAAC;;;EAGJA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAiC;;EAf9DC,GAAA;EAwBmBD,KAAK,EAAC;;;EAGJA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAiC;;EAQrDA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAGlCA,KAAK,EAAC;AAA4B;kBA1C/C;;EAkDiBA,KAAK,EAAC;AAAqC;;EAIjDA,KAAK,EAAC;AAA0B;;;;;;;;;;;;uBArDzCE,mBAAA,CA0DM,OA1DNC,UA0DM,GAzDJC,mBAAA,CAkCM,OAlCNC,UAkCM,GAjCJD,mBAAA,CAEM,OAFNE,UAEM,GADJC,YAAA,CAAoGC,mBAAA;IAJ5GC,UAAA,EAI2BC,MAAA,CAAAC,OAAO;IAJlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAI2BH,MAAA,CAAAC,OAAO,GAAAE,MAAA;IAAA;IAAG,aAAW,EAAEH,MAAA,CAAAI,MAAM;IAAEC,WAAW,EAAC,IAAI;IAAEC,OAAK,EAAEN,MAAA,CAAAO,WAAW;IAAEC,SAAS,EAAT;sGAE1FC,YAAA,CA6BeC,uBAAA;IA7BDpB,KAAK,EAAC;EAA6B;IANvDqB,OAAA,EAAAC,QAAA,CAOQ;MAAA,OAaoB,C,gBAbpBf,YAAA,CAaoBgB,4BAAA;QApB5Bd,UAAA,EAOoCC,MAAA,CAAAc,WAAW;QAP/C,uBAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAOoCH,MAAA,CAAAc,WAAW,GAAAX,MAAA;QAAA;;QAP/CQ,OAAA,EAAAC,QAAA,CAQU;UAAA,OAWU,CAXVf,YAAA,CAWUkB,kBAAA;YAXD,UAAQ,EAAC,IAAI;YAAEC,IAAI,EAAEhB,MAAA,CAAAiB;;YACjBN,OAAO,EAAAC,QAAA,CAEoB,UAAAM,IAAA;cAAA,IAAAC,kBAAA;cAAA,IAFhBH,IAAI,GAAAE,IAAA,CAAJF,IAAI;cAAA,QACmBA,IAAI,CAACI,IAAI,e,cAApD5B,mBAAA,CAAuF,OAAvF6B,UAAuF,EAAAC,gBAAA,CAAnBN,IAAI,CAACO,KAAK,oBAV5FC,mBAAA,gBAYsBR,IAAI,CAACI,IAAI,e,cADjBX,YAAA,CAMcgB,sBAAA;gBAjB5BlC,GAAA;gBAW4BmC,KAAK,EAAEV,IAAI,CAACW,EAAE;gBAAGC,QAAQ,GAAAT,kBAAA,GAAEnB,MAAA,CAAA6B,UAAU,cAAAV,kBAAA,uBAAVA,kBAAA,CAAYW,QAAQ,CAACd,IAAI,CAACW,EAAE;gBAAII,QAAM,WAANA,QAAMA,CAAA5B,MAAA;kBAAA,OAAEH,MAAA,CAAAgC,YAAY,CAAChB,IAAI;gBAAA;;gBAXhHL,OAAA,EAAAC,QAAA,CAagB;kBAAA,OAGM,CAHNlB,mBAAA,CAGM,OAHNuC,UAGM,GAFJpC,YAAA,CAA8FqC,mBAAA;oBAAnFC,GAAG,EAAEnC,MAAA,CAAAoC,MAAM,CAACpB,IAAI,CAACqB,IAAI,CAACC,KAAK,IAAItB,IAAI,CAACqB,IAAI,CAACE,OAAO;oBAAGC,GAAG,EAAC,OAAO;oBAACC,SAAS,EAAC;oDACpF/C,mBAAA,CAA2E,OAA3EgD,UAA2E,EAAApB,gBAAA,CAA3BN,IAAI,CAACqB,IAAI,CAACM,QAAQ,iB;;gBAfpFC,CAAA;wFAAApB,mBAAA,e;;YAAAoB,CAAA;;;QAAAA,CAAA;qDAO4D5C,MAAA,CAAAC,OAAO,E,mBAc3DJ,YAAA,CAaoBgB,4BAAA;QAlC5Bd,UAAA,EAqBoCC,MAAA,CAAAc,WAAW;QArB/C,uBAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAqBoCH,MAAA,CAAAc,WAAW,GAAAX,MAAA;QAAA;;QArB/CQ,OAAA,EAAAC,QAAA,CAsBU;UAAA,OAWU,CAXVf,YAAA,CAWUkB,kBAAA;YAXD8B,IAAI,EAAJ,EAAI;YAAEC,IAAI,EAAE9C,MAAA,CAAA+C,QAAQ;YAAE,UAAQ,EAAC,IAAI;YAAEC,KAAK,EAAE;cAAAC,MAAA;YAAA;;YACxCtC,OAAO,EAAAC,QAAA,CAmB7B,UAAAsC,KAAA;cAAA,IAAAC,mBAAA;cAAA,IAnBiCnC,IAAI,GAAAkC,KAAA,CAAJlC,IAAI;cAAA,QACmBA,IAAI,CAACI,IAAI,e,cAApD5B,mBAAA,CAAuF,OAAvF4D,UAAuF,EAAA9B,gBAAA,CAAnBN,IAAI,CAACO,KAAK,oBAxB5FC,mBAAA,gBA0BsBR,IAAI,CAACI,IAAI,e,cADjBX,YAAA,CAMcgB,sBAAA;gBA/B5BlC,GAAA;gBAyB4BmC,KAAK,EAAEV,IAAI,CAACW,EAAE;gBAAGC,QAAQ,GAAAuB,mBAAA,GAAEnD,MAAA,CAAA6B,UAAU,cAAAsB,mBAAA,uBAAVA,mBAAA,CAAYrB,QAAQ,CAACd,IAAI,CAACW,EAAE;gBAAII,QAAM,WAANA,QAAMA,CAAA5B,MAAA;kBAAA,OAAEH,MAAA,CAAAgC,YAAY,CAAChB,IAAI;gBAAA;;gBAzBhHL,OAAA,EAAAC,QAAA,CA2BgB;kBAAA,OAGM,CAHNlB,mBAAA,CAGM,OAHN2D,UAGM,GAFJxD,YAAA,CAA8FqC,mBAAA;oBAAnFC,GAAG,EAAEnC,MAAA,CAAAoC,MAAM,CAACpB,IAAI,CAACqB,IAAI,CAACC,KAAK,IAAItB,IAAI,CAACqB,IAAI,CAACE,OAAO;oBAAGC,GAAG,EAAC,OAAO;oBAACC,SAAS,EAAC;oDACpF/C,mBAAA,CAA2E,OAA3E4D,UAA2E,EAAAhC,gBAAA,CAA3BN,IAAI,CAACqB,IAAI,CAACM,QAAQ,iB;;gBA7BpFC,CAAA;wFAAApB,mBAAA,e;;YAAAoB,CAAA;;;QAAAA,CAAA;oDAqB0D5C,MAAA,CAAAC,OAAO,E;;IArBjE2C,CAAA;6BAMmE5C,MAAA,CAAAuD,OAAO,E,KA+BtE7D,mBAAA,CAqBM,OArBN8D,WAqBM,GApBJ9D,mBAAA,CAEM,OAFN+D,WAEM,GADJ/D,mBAAA,CAAoG,OAApGgE,WAAoG,G,0BAvC5GC,gBAAA,CAuCgD,QAAM,IAAAjE,mBAAA,CAAgD,cAA1C,KAAG,GAAA4B,gBAAA,CAAGtB,MAAA,CAAA4D,eAAe,CAACC,MAAM,IAAG,MAAI,gB,KAEzFhE,YAAA,CAYea,uBAAA;IAZDpB,KAAK,EAAC;EAA8B;IAzCxDqB,OAAA,EAAAC,QAAA,CA0CQ;MAAA,OAUM,CAVNlB,mBAAA,CAUM,OAVNoE,WAUM,I,kBATJtE,mBAAA,CAQMuE,SAAA,QAnDhBC,WAAA,CA2C6DhE,MAAA,CAAA4D,eAAe,EA3C5E,UA2CqDK,IAAI;QAAA,IAAAC,mBAAA;6BAA/C1E,mBAAA,CAQM;UARDF,KAAK,EAAC,wBAAwB;UAAkCC,GAAG,EAAE0E,IAAI,CAACtC;qCACD3B,MAAA,CAAA6B,UAAU,cAAAqC,mBAAA,eAAVA,mBAAA,CAAYpC,QAAQ,CAACmC,IAAI,CAACtC,EAAE,M,cAAxGnC,mBAAA,CAIM;UAhDlBD,GAAA;UA4CiBD,KAAK,EAAC,2BAA2B;UAAE6E,OAAK,WAALA,OAAKA,CAAAhE,MAAA;YAAA,OAAEH,MAAA,CAAAoE,cAAc,CAACH,IAAI;UAAA;YAChEpE,YAAA,CAEUwE,kBAAA;UA/CxB1D,OAAA,EAAAC,QAAA,CA8CgB;YAAA,OAAqB,CAArBf,YAAA,CAAqByE,4BAAA,E;;UA9CrC1B,CAAA;4BAAA2B,WAAA,KAAA/C,mBAAA,gBAiDY3B,YAAA,CAA8FqC,mBAAA;UAAnFC,GAAG,EAAEnC,MAAA,CAAAoC,MAAM,CAAC6B,IAAI,CAAC5B,IAAI,CAACC,KAAK,IAAI2B,IAAI,CAAC5B,IAAI,CAACE,OAAO;UAAGC,GAAG,EAAC,OAAO;UAACC,SAAS,EAAC;0CACpF/C,mBAAA,CAA+E,OAA/E8E,WAA+E,EAAAlD,gBAAA,CAA3B2C,IAAI,CAAC5B,IAAI,CAACM,QAAQ,iB;;;IAlDlFC,CAAA;MAsDMlD,mBAAA,CAGM,OAHN+E,WAGM,GAFJ5E,YAAA,CAA8C6E,oBAAA;IAAlCP,OAAK,EAAEnE,MAAA,CAAA2E;EAAW;IAvDtChE,OAAA,EAAAC,QAAA,CAuDwC;MAAA,OAAEV,MAAA,QAAAA,MAAA,OAvD1CyD,gBAAA,CAuDwC,IAAE,E;;IAvD1Cf,CAAA;MAwDQ/C,YAAA,CAAoG6E,oBAAA;IAAzFtD,IAAI,EAAC,SAAS;IAAE+C,OAAK,EAAEnE,MAAA,CAAA4E,YAAY;IAAGhD,QAAQ,GAAI5B,MAAA,CAAA4D,eAAe,CAACC;;IAxDrFlD,OAAA,EAAAC,QAAA,CAwD8F;MAAA,OAAEV,MAAA,QAAAA,MAAA,OAxDhGyD,gBAAA,CAwD8F,IAAE,E;;IAxDhGf,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}