{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"DataRecommendationOpen\"\n};\nvar _hoisted_2 = {\n  class: \"DataRecommendationOpenHead\"\n};\nvar _hoisted_3 = [\"title\"];\nvar _hoisted_4 = {\n  class: \"DataRecommendationOpenBody\"\n};\nvar _hoisted_5 = {\n  class: \"DataRecommendationOpenBodyContent\"\n};\nvar _hoisted_6 = {\n  class: \"DataRecommendationOpenBodyContentViews\"\n};\nvar _hoisted_7 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$setup$details;\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", {\n    class: \"DataRecommendationOpenName ellipsis\",\n    title: $setup.title\n  }, _toDisplayString($setup.title), 9 /* TEXT, PROPS */, _hoisted_3)]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", {\n    class: \"DataRecommendationOpenBodyContentViewsWrapper\",\n    innerHTML: ((_$setup$details = $setup.details) === null || _$setup$details === void 0 || (_$setup$details = _$setup$details.article) === null || _$setup$details === void 0 ? void 0 : _$setup$details.articleContent) || ''\n  }, null, 8 /* PROPS */, _hoisted_7)])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "title", "$setup", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "innerHTML", "_$setup$details", "details", "article", "articleContent", "_hoisted_7"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\Intelligentize\\DataRecommendation\\DataRecommendationOpen.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DataRecommendationOpen\">\r\n    <div class=\"DataRecommendationOpenHead\">\r\n      <div class=\"DataRecommendationOpenName ellipsis\"\r\n           :title=\"title\">{{ title }}</div>\r\n    </div>\r\n\r\n    <div class=\"DataRecommendationOpenBody\">\r\n      <div class=\"DataRecommendationOpenBodyContent\">\r\n        <div class=\"DataRecommendationOpenBodyContentViews\">\r\n          <div class=\"DataRecommendationOpenBodyContentViewsWrapper\"\r\n               v-html=\"details?.article?.articleContent || ''\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DataRecommendationOpen' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\n// import { useStore } from 'vuex'\r\nimport { useRoute } from 'vue-router'\r\nconst route = useRoute()\r\nconst title = ref(route.query.title)\r\nconst details = ref({})\r\nonMounted(() => {\r\n  if (route.query.id) { hadoopLawseesInfo(route.query.id) }\r\n})\r\nconst hadoopLawseesInfo = async (id) => {\r\n  var params = {\r\n    articleId: id,\r\n  }\r\n  const { data } = await api.hadoopLawseesInfo(params)\r\n  details.value = data\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.DataRecommendationOpen {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .DataRecommendationOpenHead {\r\n    width: 100%;\r\n    height: 52px;\r\n    box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.05);\r\n    padding: 0 32px 0 22px;\r\n    z-index: 999;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid #eeeeee;\r\n\r\n    .DataRecommendationOpenName {\r\n      cursor: pointer;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n  }\r\n\r\n  .DataRecommendationOpenBody {\r\n    width: 100%;\r\n    height: calc(100% - 52px);\r\n    background: #eeeeee;\r\n\r\n    .DataRecommendationOpenBodyContent {\r\n      width: 100%;\r\n      height: 100%;\r\n      overflow: auto;\r\n      position: relative;\r\n\r\n      .DataRecommendationOpenBodyContentViews {\r\n        direction: ltr;\r\n        width: 816px;\r\n        min-height: 1056px;\r\n        margin: 1px auto -8px;\r\n        position: relative;\r\n        overflow: visible;\r\n        border: 9px solid transparent;\r\n        border-image: url('../../img/Develop/Blur.png') 9 9 repeat;\r\n        background-clip: content-box;\r\n        background-color: #fff;\r\n\r\n        .DataRecommendationOpenBodyContentViewsWrapper {\r\n          padding: 20px 40px;\r\n          line-height: var(--zy-line-height);\r\n\r\n          table {\r\n            border: 1px solid #ddd;\r\n\r\n            // tr,\r\n            td {\r\n              border: 1px solid #ddd;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;iBAF3C;;EAOSA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAwC;iBAT3D;;;uBACEC,mBAAA,CAcM,OAdNC,UAcM,GAbJC,mBAAA,CAGM,OAHNC,UAGM,GAFJD,mBAAA,CACqC;IADhCH,KAAK,EAAC,qCAAqC;IAC1CK,KAAK,EAAEC,MAAA,CAAAD;sBAAUC,MAAA,CAAAD,KAAK,wBAJlCE,UAAA,E,GAOIJ,mBAAA,CAOM,OAPNK,UAOM,GANJL,mBAAA,CAKM,OALNM,UAKM,GAJJN,mBAAA,CAGM,OAHNO,UAGM,GAFJP,mBAAA,CAC2D;IADtDH,KAAK,EAAC,+CAA+C;IACrDW,SAA+C,EAAvC,EAAAC,eAAA,GAAAN,MAAA,CAAAO,OAAO,cAAAD,eAAA,gBAAAA,eAAA,GAAPA,eAAA,CAASE,OAAO,cAAAF,eAAA,uBAAhBA,eAAA,CAAkBG,cAAc;0BAXvDC,UAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}