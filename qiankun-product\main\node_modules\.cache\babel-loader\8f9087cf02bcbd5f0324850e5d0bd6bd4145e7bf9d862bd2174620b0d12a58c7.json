{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from '../img/complianceReviewTopics/empty.png';\nimport _imports_1 from '../img/complianceReviewTopics/uploadIng.png';\nvar _hoisted_1 = {\n  class: \"complianceReviewTopics\"\n};\nvar _hoisted_2 = {\n  class: \"complianceReviewTopicsLeft\"\n};\nvar _hoisted_3 = {\n  class: \"buttonBox\"\n};\nvar _hoisted_4 = {\n  class: \"complianceReviewTopicsLeftTitle\"\n};\nvar _hoisted_5 = [\"innerHTML\"];\nvar _hoisted_6 = {\n  key: 0,\n  class: \"complianceReviewTopicsRight\"\n};\nvar _hoisted_7 = {\n  class: \"buttonBox\"\n};\nvar _hoisted_8 = {\n  class: \"rightContent\"\n};\nvar _hoisted_9 = {\n  key: 0,\n  class: \"emptyBox\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  src: _imports_0,\n  alt: \"\"\n};\nvar _hoisted_11 = {\n  key: 1,\n  src: _imports_1,\n  alt: \"\"\n};\nvar _hoisted_12 = {\n  key: 2\n};\nvar _hoisted_13 = {\n  key: 3,\n  class: \"progressingText\"\n};\nvar _hoisted_14 = [\"innerHTML\"];\nvar _hoisted_15 = {\n  key: 0,\n  class: \"loader3\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_Download = _resolveComponent(\"Download\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_upload = _resolveComponent(\"el-upload\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, _toDisplayString(!$setup.isRewrite ? '内容编辑' : '原文内容'), 1 /* TEXT */), !$setup.isRewrite ? (_openBlock(), _createBlock(_component_el_upload, {\n    key: 0,\n    action: \"/\",\n    \"before-upload\": $setup.handleFile,\n    \"http-request\": $setup.fileWordUpload,\n    \"show-file-list\": false\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_button, {\n        type: \"primary\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_icon, {\n            class: \"el-icon--right\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_Download)];\n            }),\n            _: 1 /* STABLE */\n          }), _cache[1] || (_cache[1] = _createTextVNode(\" 文档导入 \"))];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)]), !$setup.isRewrite ? (_openBlock(), _createBlock(_component_TinyMceEditor, {\n    key: 0,\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.content = $event;\n    })\n  }, null, 8 /* PROPS */, [\"modelValue\"])) : (_openBlock(), _createBlock(_component_el_scrollbar, {\n    key: 1,\n    class: \"rewriteBox\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        innerHTML: $setup.content\n      }, null, 8 /* PROPS */, _hoisted_5)];\n    }),\n    _: 1 /* STABLE */\n  }))]), false ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"complianceReviewTopicsLeftTitle\"\n  }, \"符合性审查\", -1 /* HOISTED */)), _createElementVNode(\"div\", null, [$setup.startIndex == 0 ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 0,\n    type: \"primary\",\n    disabled: $setup.content == '',\n    onClick: $setup.startReview\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\" 开始审查 \")]);\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\"])) : _createCommentVNode(\"v-if\", true), $setup.reviewStaus == 'progressing' ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 1,\n    type: \"info\",\n    link: \"\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"正在检查中...\")]);\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" <el-button v-if=\\\"reviewStaus == 'end'\\\">导出结果</el-button> \"), $setup.reviewStaus == 'end' ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 2,\n    type: \"primary\",\n    onClick: $setup.startReview\n  }, {\n    default: _withCtx(function () {\n      return _cache[4] || (_cache[4] = [_createTextVNode(\"重新生成\")]);\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), $setup.reviewStaus == 'end' ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 3,\n    type: \"primary\",\n    onClick: $setup.rewriteHanld\n  }, {\n    default: _withCtx(function () {\n      return _cache[5] || (_cache[5] = [_createTextVNode(\"智能改写\")]);\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_8, [$setup.startIndex != 100 && !$setup.isFluid || $setup.startIndex == 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [$setup.reviewStaus == '' ? (_openBlock(), _createElementBlock(\"img\", _hoisted_10)) : _createCommentVNode(\"v-if\", true), $setup.reviewStaus == 'progressing' && !$setup.isFluid ? (_openBlock(), _createElementBlock(\"img\", _hoisted_11)) : _createCommentVNode(\"v-if\", true), $setup.reviewStaus == '' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, _cache[7] || (_cache[7] = [_createTextVNode(\" 请先在左侧输入内容或导入word文档 \"), _createElementVNode(\"br\", null, null, -1 /* HOISTED */), _createTextVNode(\" 才可进行审查 \")]))) : _createCommentVNode(\"v-if\", true), $setup.reviewStaus == 'progressing' && !$setup.isFluid ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_cache[8] || (_cache[8] = _createTextVNode(\" 正在进行检查，请耐心等待！ \")), _cache[9] || (_cache[9] = _createElementVNode(\"br\", null, null, -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.startIndex) + \".0%\", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), $setup.reviewStaus == 'progressing' && $setup.isFluid ? (_openBlock(), _createBlock(_component_el_scrollbar, {\n    key: 1,\n    class: \"resultBox\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"span\", {\n        innerHTML: $setup.resultContent\n      }, null, 8 /* PROPS */, _hoisted_14), $setup.reviewStaus == 'progressing' && $setup.isFluid ? (_openBlock(), _createElementBlock(\"span\", _hoisted_15, _cache[10] || (_cache[10] = [_createElementVNode(\"div\", {\n        class: \"circle1\"\n      }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n        class: \"circle1\"\n      }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n        class: \"circle1\"\n      }, null, -1 /* HOISTED */)]))) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "class", "key", "src", "alt", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$setup", "isRewrite", "_createBlock", "_component_el_upload", "action", "handleFile", "fileWordUpload", "default", "_withCtx", "_createVNode", "_component_el_button", "type", "_component_el_icon", "_component_Download", "_", "_createTextVNode", "_createCommentVNode", "_component_TinyMceEditor", "modelValue", "content", "_cache", "$event", "_component_el_scrollbar", "innerHTML", "_hoisted_5", "_hoisted_6", "_hoisted_7", "startIndex", "disabled", "onClick", "startReview", "reviewStaus", "link", "rewriteHanld", "_hoisted_8", "isFluid", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "resultContent", "_hoisted_14", "_hoisted_15"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\complianceReviewTopics\\complianceReviewTopics.vue"], "sourcesContent": ["<template>\r\n  <div class=\"complianceReviewTopics\">\r\n    <div class=\"complianceReviewTopicsLeft\">\r\n      <div class=\"buttonBox\">\r\n        <div class=\"complianceReviewTopicsLeftTitle\">{{ !isRewrite ? '内容编辑' : '原文内容' }}</div>\r\n        <el-upload\r\n          action=\"/\"\r\n          v-if=\"!isRewrite\"\r\n          :before-upload=\"handleFile\"\r\n          :http-request=\"fileWordUpload\"\r\n          :show-file-list=\"false\">\r\n          <el-button type=\"primary\">\r\n            <el-icon class=\"el-icon--right\">\r\n              <Download />\r\n            </el-icon>\r\n            文档导入\r\n          </el-button>\r\n        </el-upload>\r\n      </div>\r\n      <TinyMceEditor v-if=\"!isRewrite\" v-model=\"content\" />\r\n      <el-scrollbar class=\"rewriteBox\" v-else>\r\n        <div v-html=\"content\"></div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"complianceReviewTopicsRight\" v-if=\"false\">\r\n      <div class=\"buttonBox\">\r\n        <div class=\"complianceReviewTopicsLeftTitle\">符合性审查</div>\r\n        <div>\r\n          <el-button type=\"primary\" v-if=\"startIndex == 0\" :disabled=\"content == ''\" @click=\"startReview\">\r\n            开始审查\r\n          </el-button>\r\n          <el-button type=\"info\" link v-if=\"reviewStaus == 'progressing'\">正在检查中...</el-button>\r\n          <!-- <el-button v-if=\"reviewStaus == 'end'\">导出结果</el-button> -->\r\n          <el-button type=\"primary\" @click=\"startReview\" v-if=\"reviewStaus == 'end'\">重新生成</el-button>\r\n          <el-button type=\"primary\" v-if=\"reviewStaus == 'end'\" @click=\"rewriteHanld\">智能改写</el-button>\r\n        </div>\r\n      </div>\r\n      <div class=\"rightContent\">\r\n        <div class=\"emptyBox\" v-if=\"(startIndex != 100 && !isFluid) || startIndex == 0\">\r\n          <img v-if=\"reviewStaus == ''\" src=\"../img/complianceReviewTopics/empty.png\" alt=\"\" />\r\n          <img\r\n            v-if=\"reviewStaus == 'progressing' && !isFluid\"\r\n            src=\"../img/complianceReviewTopics/uploadIng.png\"\r\n            alt=\"\" />\r\n          <div v-if=\"reviewStaus == ''\">\r\n            请先在左侧输入内容或导入word文档\r\n            <br />\r\n            才可进行审查\r\n          </div>\r\n          <div v-if=\"reviewStaus == 'progressing' && !isFluid\" class=\"progressingText\">\r\n            正在进行检查，请耐心等待！\r\n            <br />\r\n            <span>{{ startIndex }}.0%</span>\r\n          </div>\r\n        </div>\r\n        <el-scrollbar class=\"resultBox\" v-if=\"reviewStaus == 'progressing' && isFluid\">\r\n          <span v-html=\"resultContent\"></span>\r\n          <span class=\"loader3\" v-if=\"reviewStaus == 'progressing' && isFluid\">\r\n            <div class=\"circle1\"></div>\r\n            <div class=\"circle1\"></div>\r\n            <div class=\"circle1\"></div>\r\n          </span>\r\n        </el-scrollbar>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'complianceReviewTopics' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport config from 'common/config/index'\r\nimport { ref, onActivated, onDeactivated, onUnmounted, watch } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { fetchEventSource } from '@microsoft/fetch-event-source'\r\nconst content = ref('')\r\nconst loading = ref(false)\r\nconst isRewrite = ref(false) // 是否改写\r\nconst startIndex = ref(0) // 进度条\r\nconst timer = ref(null) // 定时器\r\nconst reviewStaus = ref('') // 审查状态\r\nconst resultContent = ref('') //  生成的结果\r\nconst isFluid = ref(true) // 是否流式输出\r\nconst store = useStore()\r\n\r\n// 监听content变化，无论是手动输入还是文件导入都会触发\r\nwatch(content, (newValue) => {\r\n  store.commit('setAiChatContent', newValue)\r\n})\r\n\r\nconst handleFile = (file) => {\r\n  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()\r\n  const isShow = ['doc', 'docx'].includes(fileType)\r\n  if (!isShow) {\r\n    ElMessage({ type: 'warning', message: `仅支持word格式!` })\r\n  }\r\n  return isShow\r\n}\r\nonActivated(() => {\r\n  store.commit('setAiChatWidth', window.innerWidth - 1280)\r\n  store.commit('setAiChatWindow', true)\r\n  store.commit('setAiChatCode', 'compliance_chat')\r\n  store.commit('setAiChatParams', { filterHtml: '1' })\r\n})\r\nonDeactivated(() => {\r\n  const width = window.innerWidth - 1280 > 520 ? 520 : 400\r\n  store.commit('setAiChatWidth', width)\r\n  store.commit('setAiChatWindow', false)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatParams', {})\r\n  store.commit('setAiChatContent', '')\r\n})\r\nonUnmounted(() => {\r\n  const width = window.innerWidth - 1280 > 520 ? 520 : 400\r\n  store.commit('setAiChatWidth', width)\r\n  store.commit('setAiChatWindow', false)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatParams', {})\r\n  store.commit('setAiChatContent', '')\r\n})\r\nconst fileWordUpload = async (file) => {\r\n  reviewStaus.value = ''\r\n  content.value = ''\r\n  startIndex.value = 0\r\n  try {\r\n    const param = new FormData()\r\n    param.append('file', file.file)\r\n    const { data } = await api.fileword2html(param)\r\n    content.value = data\r\n      .replace(/<\\/?html[^>]*>/g, '')\r\n      .replace(/<head\\b[^<]*(?:(?!<\\/head>)<[^<]*)*<\\/head>/gi, '')\r\n      .replace(/<\\/?body[^>]*>/g, '')\r\n      .replace(/<\\/?div[^>]*>/g, '')\r\n    loading.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\nvar ctrl = ref(new AbortController())\r\nconst startReview = () => {\r\n  startIndex.value = 0\r\n  startIndex.value++\r\n  resultContent.value = ''\r\n  reviewStaus.value = 'progressing'\r\n  if (isFluid) getFetchEventSource()\r\n  if (!isFluid) chatStream()\r\n  timer.value = setInterval(() => {\r\n    startIndex.value++\r\n    if (startIndex.value >= 98) {\r\n      clearInterval(timer.value)\r\n    }\r\n  }, 30)\r\n}\r\n\r\nconst chatStream = async () => {\r\n  try {\r\n    const { data } = await api.globalJson('/aigpt/chat', {\r\n      chatBusinessScene: 'compliance_chat',\r\n      chatId: 'compliance_id' + guid(),\r\n      question: content.value,\r\n      dataId: guid(),\r\n      tool: '',\r\n      attachmentIds: ''\r\n    })\r\n    reviewStaus.value = 'end'\r\n    startIndex.value = 100\r\n    resultContent.value = (data.choices || []).length ? data.choices[0].message.content : ''\r\n  } catch (err) {\r\n    clearInterval(timer.value)\r\n    reviewStaus.value = ''\r\n    startIndex.value = 0\r\n  }\r\n}\r\n\r\nconst getFetchEventSource = async () => {\r\n  ctrl.value.abort()\r\n  ctrl.value = new AbortController() // 创建新的控制器\r\n  const { signal } = ctrl.value\r\n  const token = sessionStorage.getItem('token') || ''\r\n  await fetchEventSource(`${config.API_URL}/aigpt/chatStream`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      authorization: token\r\n    },\r\n    body: JSON.stringify({\r\n      chatBusinessScene: 'compliance_chat',\r\n      chatId: 'compliance_id' + guid(),\r\n      question: content.value,\r\n      dataId: guid(),\r\n      tool: '',\r\n      attachmentIds: ''\r\n    }),\r\n    openWhenHidden: true, // 取消visibilityChange事件\r\n    signal,\r\n    onmessage: (res) => {\r\n      console.log('🚀 ~ getFetchEventSource ~ res:', res)\r\n      if (res.data === '[DONE]') {\r\n        reviewStaus.value = 'end'\r\n        startIndex.value = 100\r\n      } else {\r\n        const data = JSON.parse(res.data)\r\n        resultContent.value += data.choices[0].delta.content\r\n      }\r\n    },\r\n    onclose: (data) => {},\r\n    onerror: (err) => {\r\n      console.log(err)\r\n      throw err\r\n    }\r\n  })\r\n}\r\nconst rewriteHanld = () => {\r\n  isRewrite.value = true\r\n}\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.complianceReviewTopics {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  background: #f4f4f4;\r\n\r\n  .buttonBox {\r\n    height: 56px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .complianceReviewTopicsLeftTitle {\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .el-icon--right {\r\n      margin-right: 6px;\r\n    }\r\n  }\r\n\r\n  .complianceReviewTopicsLeft {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 10px;\r\n    background: #fff;\r\n\r\n    .rewriteBox {\r\n      height: calc(100% - 56px);\r\n      padding: 16px;\r\n    }\r\n\r\n    .TinyMceEditor {\r\n      height: calc(100% - 56px);\r\n\r\n      .tox-tinymce {\r\n        height: 100% !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .complianceReviewTopicsRight {\r\n    width: 49%;\r\n    height: 100%;\r\n    background: #fff;\r\n    padding: 10px;\r\n\r\n    .rightContent {\r\n      height: calc(100% - 56px);\r\n      border-top: 1px solid transparent;\r\n\r\n      .emptyBox {\r\n        margin-top: 300px;\r\n        text-align: center;\r\n        line-height: 26px;\r\n        user-select: none;\r\n\r\n        .progressingText {\r\n          color: #262626;\r\n          font-weight: 600;\r\n\r\n          span {\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n\r\n        img {\r\n          width: 60px;\r\n          margin-bottom: 20px;\r\n        }\r\n      }\r\n\r\n      .resultBox {\r\n        height: 100%;\r\n        padding: 16px;\r\n\r\n        .loader3 {\r\n          display: inline-flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n        }\r\n\r\n        .circle1 {\r\n          width: 4px;\r\n          height: 4px;\r\n          border-radius: 50%;\r\n          margin: 0 5px;\r\n          background-color: #333;\r\n          animation: circle1 1s ease-in-out infinite;\r\n        }\r\n\r\n        .circle1:nth-child(2) {\r\n          animation-delay: 0.33s;\r\n        }\r\n\r\n        .circle1:nth-child(3) {\r\n          animation-delay: 0.67s;\r\n        }\r\n\r\n        .circle1:nth-child(4) {\r\n          animation-delay: 0.6s;\r\n        }\r\n\r\n        .circle1:nth-child(5) {\r\n          animation-delay: 0.8s;\r\n        }\r\n\r\n        @keyframes circle1 {\r\n          0% {\r\n            transform: scale(1);\r\n            opacity: 1;\r\n          }\r\n\r\n          50% {\r\n            transform: scale(1.5);\r\n            opacity: 0.5;\r\n          }\r\n\r\n          100% {\r\n            transform: scale(1);\r\n            opacity: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAuCwCA,UAA6C;OAGzEC,UAAiD;;EAzCtDC,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAiC;iBAJpD;;EAAAC,GAAA;EAwBSD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAW;;EAYjBA,KAAK,EAAC;AAAc;;EArC/BC,GAAA;EAsCaD,KAAK,EAAC;;;EAtCnBC,GAAA;EAuCwCC,GAA6C,EAA7CJ,UAA6C;EAACK,GAAG,EAAC;;;EAvC1FF,GAAA;EA0CYC,GAAiD,EAAjDH,UAAiD;EACjDI,GAAG,EAAC;;;EA3ChBF,GAAA;AAAA;;EAAAA,GAAA;EAiD+DD,KAAK,EAAC;;kBAjDrE;;EAAAC,GAAA;EAyDgBD,KAAK,EAAC;;;;;;;;;uBAxDpBI,mBAAA,CAgEM,OAhENC,UAgEM,GA/DJC,mBAAA,CAqBM,OArBNC,UAqBM,GApBJD,mBAAA,CAeM,OAfNE,UAeM,GAdJF,mBAAA,CAAqF,OAArFG,UAAqF,EAAAC,gBAAA,EAApCC,MAAA,CAAAC,SAAS,oC,CAGjDD,MAAA,CAAAC,SAAS,I,cAFlBC,YAAA,CAYYC,oBAAA;IAjBpBb,GAAA;IAMUc,MAAM,EAAC,GAAG;IAET,eAAa,EAAEJ,MAAA,CAAAK,UAAU;IACzB,cAAY,EAAEL,MAAA,CAAAM,cAAc;IAC5B,gBAAc,EAAE;;IAV3BC,OAAA,EAAAC,QAAA,CAWU;MAAA,OAKY,CALZC,YAAA,CAKYC,oBAAA;QALDC,IAAI,EAAC;MAAS;QAXnCJ,OAAA,EAAAC,QAAA,CAYY;UAAA,OAEU,CAFVC,YAAA,CAEUG,kBAAA;YAFDvB,KAAK,EAAC;UAAgB;YAZ3CkB,OAAA,EAAAC,QAAA,CAac;cAAA,OAAY,CAAZC,YAAA,CAAYI,mBAAA,E;;YAb1BC,CAAA;wCAAAC,gBAAA,CAcsB,QAEZ,G;;QAhBVD,CAAA;;;IAAAA,CAAA;QAAAE,mBAAA,e,IAmB4BhB,MAAA,CAAAC,SAAS,I,cAA/BC,YAAA,CAAqDe,wBAAA;IAnB3D3B,GAAA;IAAA4B,UAAA,EAmBgDlB,MAAA,CAAAmB,OAAO;IAnBvD,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAmBgDrB,MAAA,CAAAmB,OAAO,GAAAE,MAAA;IAAA;4DACjDnB,YAAA,CAEeoB,uBAAA;IAtBrBhC,GAAA;IAoBoBD,KAAK,EAAC;;IApB1BkB,OAAA,EAAAC,QAAA,CAqBQ;MAAA,OAA4B,CAA5Bb,mBAAA,CAA4B;QAAvB4B,SAAgB,EAARvB,MAAA,CAAAmB;MAAO,wBArB5BK,UAAA,E;;IAAAV,CAAA;SAwBmD,KAAK,I,cAApDrB,mBAAA,CAwCM,OAxCNgC,UAwCM,GAvCJ9B,mBAAA,CAWM,OAXN+B,UAWM,G,0BAVJ/B,mBAAA,CAAwD;IAAnDN,KAAK,EAAC;EAAiC,GAAC,OAAK,sBAClDM,mBAAA,CAQM,cAP4BK,MAAA,CAAA2B,UAAU,S,cAA1CzB,YAAA,CAEYQ,oBAAA;IA9BtBpB,GAAA;IA4BqBqB,IAAI,EAAC,SAAS;IAAyBiB,QAAQ,EAAE5B,MAAA,CAAAmB,OAAO;IAASU,OAAK,EAAE7B,MAAA,CAAA8B;;IA5B7FvB,OAAA,EAAAC,QAAA,CA4B0G;MAAA,OAEhGY,MAAA,QAAAA,MAAA,OA9BVL,gBAAA,CA4B0G,QAEhG,E;;IA9BVD,CAAA;qCAAAE,mBAAA,gBA+B4ChB,MAAA,CAAA+B,WAAW,qB,cAA7C7B,YAAA,CAAoFQ,oBAAA;IA/B9FpB,GAAA;IA+BqBqB,IAAI,EAAC,MAAM;IAACqB,IAAI,EAAJ;;IA/BjCzB,OAAA,EAAAC,QAAA,CA+B0E;MAAA,OAAQY,MAAA,QAAAA,MAAA,OA/BlFL,gBAAA,CA+B0E,UAAQ,E;;IA/BlFD,CAAA;QAAAE,mBAAA,gBAgCUA,mBAAA,+DAAgE,EACXhB,MAAA,CAAA+B,WAAW,a,cAAhE7B,YAAA,CAA2FQ,oBAAA;IAjCrGpB,GAAA;IAiCqBqB,IAAI,EAAC,SAAS;IAAEkB,OAAK,EAAE7B,MAAA,CAAA8B;;IAjC5CvB,OAAA,EAAAC,QAAA,CAiCqF;MAAA,OAAIY,MAAA,QAAAA,MAAA,OAjCzFL,gBAAA,CAiCqF,MAAI,E;;IAjCzFD,CAAA;QAAAE,mBAAA,gBAkC0ChB,MAAA,CAAA+B,WAAW,a,cAA3C7B,YAAA,CAA4FQ,oBAAA;IAlCtGpB,GAAA;IAkCqBqB,IAAI,EAAC,SAAS;IAA8BkB,OAAK,EAAE7B,MAAA,CAAAiC;;IAlCxE1B,OAAA,EAAAC,QAAA,CAkCsF;MAAA,OAAIY,MAAA,QAAAA,MAAA,OAlC1FL,gBAAA,CAkCsF,MAAI,E;;IAlC1FD,CAAA;QAAAE,mBAAA,e,KAqCMrB,mBAAA,CA0BM,OA1BNuC,UA0BM,GAzByBlC,MAAA,CAAA2B,UAAU,YAAY3B,MAAA,CAAAmC,OAAO,IAAKnC,MAAA,CAAA2B,UAAU,S,cAAzElC,mBAAA,CAgBM,OAhBN2C,UAgBM,GAfOpC,MAAA,CAAA+B,WAAW,U,cAAtBtC,mBAAA,CAAqF,OAArF4C,WAAqF,KAvC/FrB,mBAAA,gBAyCkBhB,MAAA,CAAA+B,WAAW,sBAAsB/B,MAAA,CAAAmC,OAAO,I,cADhD1C,mBAAA,CAGW,OAHX6C,WAGW,KA3CrBtB,mBAAA,gBA4CqBhB,MAAA,CAAA+B,WAAW,U,cAAtBtC,mBAAA,CAIM,OAhDhB8C,WAAA,EAAAnB,MAAA,QAAAA,MAAA,OAAAL,gBAAA,CA4CwC,sBAE5B,GAAApB,mBAAA,CAAM,qCA9ClBoB,gBAAA,CA8CkB,UAER,E,MAhDVC,mBAAA,gBAiDqBhB,MAAA,CAAA+B,WAAW,sBAAsB/B,MAAA,CAAAmC,OAAO,I,cAAnD1C,mBAAA,CAIM,OAJN+C,WAIM,G,0BArDhBzB,gBAAA,CAiDuF,iBAE3E,I,0BAAApB,mBAAA,CAAM,sCACNA,mBAAA,CAAgC,cAAAI,gBAAA,CAAvBC,MAAA,CAAA2B,UAAU,IAAG,KAAG,gB,KApDrCX,mBAAA,e,KAAAA,mBAAA,gBAuD8ChB,MAAA,CAAA+B,WAAW,qBAAqB/B,MAAA,CAAAmC,OAAO,I,cAA7EjC,YAAA,CAOeoB,uBAAA;IA9DvBhC,GAAA;IAuDsBD,KAAK,EAAC;;IAvD5BkB,OAAA,EAAAC,QAAA,CAwDU;MAAA,OAAoC,CAApCb,mBAAA,CAAoC;QAA9B4B,SAAsB,EAAdvB,MAAA,CAAAyC;MAAa,wBAxDrCC,WAAA,GAyDsC1C,MAAA,CAAA+B,WAAW,qBAAqB/B,MAAA,CAAAmC,OAAO,I,cAAnE1C,mBAAA,CAIO,QAJPkD,WAIO,EAAAvB,MAAA,SAAAA,MAAA,QAHLzB,mBAAA,CAA2B;QAAtBN,KAAK,EAAC;MAAS,4BACpBM,mBAAA,CAA2B;QAAtBN,KAAK,EAAC;MAAS,4BACpBM,mBAAA,CAA2B;QAAtBN,KAAK,EAAC;MAAS,2B,MA5DhC2B,mBAAA,e;;IAAAF,CAAA;QAAAE,mBAAA,e,OAAAA,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}