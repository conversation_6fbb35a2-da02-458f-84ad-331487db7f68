{"ast": null, "code": "import { computed } from 'vue';\nimport { useStore } from 'vuex';\nimport { LayoutElement } from './LayoutContainer';\nvar __default__ = {\n  name: 'LayoutContainer'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var elCode = computed(function () {\n      var getElCode = store.getters.getLayoutElement || 'LayoutView';\n      return LayoutElement[getElCode] ? getElCode : 'LayoutView';\n    });\n    var __returned__ = {\n      store,\n      elCode,\n      computed,\n      get useStore() {\n        return useStore;\n      },\n      get LayoutElement() {\n        return LayoutElement;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["computed", "useStore", "LayoutElement", "__default__", "name", "store", "elCode", "getElCode", "getters", "getLayoutElement"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutContainer/LayoutContainer.vue"], "sourcesContent": ["<template>\r\n  <component :is=\"LayoutElement[elCode]\"></component>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutContainer' }\r\n</script>\r\n<script setup>\r\nimport { computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { LayoutElement } from './LayoutContainer'\r\nconst store = useStore()\r\nconst elCode = computed(() => {\r\n  const getElCode = store.getters.getLayoutElement || 'LayoutView'\r\n  return LayoutElement[getElCode] ? getElCode : 'LayoutView'\r\n})\r\n</script>\r\n"], "mappings": "AAOA,SAASA,QAAQ,QAAQ,KAAK;AAC9B,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,aAAa,QAAQ,mBAAmB;AALjD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAkB,CAAC;;;;;IAM1C,IAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC;IACxB,IAAMK,MAAM,GAAGN,QAAQ,CAAC,YAAM;MAC5B,IAAMO,SAAS,GAAGF,KAAK,CAACG,OAAO,CAACC,gBAAgB,IAAI,YAAY;MAChE,OAAOP,aAAa,CAACK,SAAS,CAAC,GAAGA,SAAS,GAAG,YAAY;IAC5D,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}