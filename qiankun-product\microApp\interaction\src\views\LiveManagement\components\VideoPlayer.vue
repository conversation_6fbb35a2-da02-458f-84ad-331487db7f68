<template>
  <div class="video-player-container">
    <div class="player-container">
      <video ref="videoPlayer" id="video-player" controls></video>
    </div>
  </div>
</template>

<script>
export default { name: 'VideoPlayer' }
</script>

<script setup>
import { ref, onBeforeUnmount, onMounted, nextTick, watch } from 'vue'
import Hls from 'hls.js'

const props = defineProps({
  liveUrl: {
    type: String,
    default: ''
  },
  replayUrl: {
    type: String,
    default: ''
  },
  isReplay: {
    type: Boolean,
    default: false
  },
  autoInit: {
    type: Boolean,
    default: true
  }
})

// 视频播放器相关
const videoPlayer = ref(null)
const player = ref(null)
const hls = ref(null)
const isPlayerInitialized = ref(false)

// 初始化视频播放器
const initVideoPlayer = async () => {
  console.log('initVideoPlayer: 开始初始化直播播放器')
  console.log('initVideoPlayer: isPlayerInitialized =', isPlayerInitialized.value)
  console.log('initVideoPlayer: videoPlayer.value =', !!videoPlayer.value)

  if (!videoPlayer.value) {
    console.log('initVideoPlayer: video元素不存在，跳过初始化')
    return
  }

  // 销毁现有播放器
  destroyVideoPlayer()

  const video = videoPlayer.value
  player.value = video
  isPlayerInitialized.value = true

  console.log('initVideoPlayer: 原始直播URL:', props.liveUrl)

  // HLS视频流地址 - 使用动态的liveUrl
  const hlsUrl = getHlsUrl(props.liveUrl)

  console.log('initVideoPlayer: 解析后的直播URL:', hlsUrl)

  if (!hlsUrl) {
    console.warn('initVideoPlayer: 没有推流地址')
    return
  }

  // 检查浏览器是否原生支持HLS
  if (video.canPlayType('application/vnd.apple.mpegurl')) {
    // 原生支持HLS
    video.src = hlsUrl
    setupVideoEvents()
  } else if (Hls.isSupported()) {
    // 使用HLS.js库
    hls.value = new Hls({
      maxBufferLength: 30,
      maxMaxBufferLength: 60,
      startLevel: -1, // 自动选择适合的初始清晰度
      maxBufferHole: 0.5,
      highLatencyMode: false
    })

    // 加载视频流
    hls.value.loadSource(hlsUrl)
    hls.value.attachMedia(video)

    // HLS事件监听
    hls.value.on(Hls.Events.MANIFEST_PARSED, function () {
      console.log('视频流准备就绪，点击播放按钮开始')
    })

    // 错误处理
    hls.value.on(Hls.Events.ERROR, function (_, data) {
      console.error('HLS错误:', data)
      switch (data.type) {
        case Hls.ErrorTypes.NETWORK_ERROR:
          hls.value.startLoad() // 尝试重新加载
          break
        case Hls.ErrorTypes.MEDIA_ERROR:
          hls.value.recoverMediaError() // 尝试恢复媒体错误
          break
        default:
          // 无法恢复的错误，尝试重新初始化
          setTimeout(initVideoPlayer, 3000)
          break
      }
    })

    setupVideoEvents()
  }
}

// 初始化回放播放器
const initReplayPlayer = async () => {
  console.log('initReplayPlayer: 开始初始化回放播放器')
  console.log('initReplayPlayer: isPlayerInitialized =', isPlayerInitialized.value)
  console.log('initReplayPlayer: videoPlayer.value =', !!videoPlayer.value)

  if (!videoPlayer.value) {
    console.log('initReplayPlayer: video元素不存在，跳过初始化')
    return
  }

  // 销毁现有播放器
  destroyVideoPlayer()

  const video = videoPlayer.value
  player.value = video
  isPlayerInitialized.value = true

  console.log('initReplayPlayer: 原始回放URL:', props.replayUrl)

  // 使用回放地址，也需要解析JSON格式
  const replayUrl = getHlsUrl(props.replayUrl)

  console.log('initReplayPlayer: 解析后的回放URL:', replayUrl)

  if (!replayUrl) {
    console.warn('initReplayPlayer: 没有回放地址')
    return
  }

  console.log('initReplayPlayer: 开始播放回放:', replayUrl)

  // 检查是否是HLS格式
  if (replayUrl.includes('.m3u8') || replayUrl.includes('hls')) {
    // HLS回放
    if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // 原生支持HLS
      video.src = replayUrl
      setupVideoEvents()
    } else if (Hls.isSupported()) {
      // 使用HLS.js库
      hls.value = new Hls({
        maxBufferLength: 30,
        maxMaxBufferLength: 60,
        startLevel: -1,
        maxBufferHole: 0.5,
        highLatencyMode: false
      })

      hls.value.loadSource(replayUrl)
      hls.value.attachMedia(video)

      hls.value.on(Hls.Events.MANIFEST_PARSED, function () {
        console.log('回放视频准备就绪')
      })

      hls.value.on(Hls.Events.ERROR, function (_, data) {
        console.error('HLS回放错误:', data)
      })

      setupVideoEvents()
    }
  } else {
    // 普通视频格式
    video.src = replayUrl
    setupVideoEvents()
  }
}

// 设置视频事件监听
const setupVideoEvents = () => {
  console.log('setupVideoEvents: 开始设置视频事件监听')
  const video = player.value
  if (!video) {
    console.log('setupVideoEvents: player.value不存在')
    return
  }

  console.log('setupVideoEvents: 添加事件监听器')

  // 视频可以播放时
  video.addEventListener('canplay', function () {
    console.log('视频准备就绪，点击播放按钮开始')
  })

  // 播放事件
  video.addEventListener('play', function () {
    console.log('正在播放HLS视频流')
  })

  // 暂停事件
  video.addEventListener('pause', function () {
    console.log('HLS视频流已暂停')
  })

  // 视频结束事件
  video.addEventListener('ended', function () {
    console.log('视频播放已结束')
  })

  // 音量变化事件
  video.addEventListener('volumechange', function () {
    console.log('音量变化')
  })

  // 添加错误事件监听
  video.addEventListener('error', function (e) {
    console.error('视频播放错误:', e)
  })

  // 添加加载开始事件
  video.addEventListener('loadstart', function () {
    console.log('开始加载视频')
  })

  // 添加元数据加载完成事件
  video.addEventListener('loadedmetadata', function () {
    console.log('视频元数据加载完成')
  })
}

// 销毁视频播放器（包括直播和回放播放器）
const destroyVideoPlayer = () => {
  console.log('destroyVideoPlayer: 开始销毁播放器')

  // 销毁 hls.js 实例（直播和回放都可能使用）
  if (hls.value) {
    try {
      console.log('destroyVideoPlayer: 销毁HLS实例')
      hls.value.destroy()
    } catch (error) {
      console.error('销毁HLS实例错误:', error)
    }
    hls.value = null
  }

  // 销毁video元素播放器（直播和回放共用同一个video元素）
  if (player.value) {
    try {
      console.log('destroyVideoPlayer: 清理player引用')
      player.value.pause() // 停止播放
      player.value.currentTime = 0 // 重置播放时间
      player.value.src = '' // 清空视频源
      player.value.removeAttribute('src') // 移除src属性
      player.value.load() // 重新加载空的video元素
      player.value.muted = false // 静音
    } catch (error) {
      console.error('销毁播放器错误:', error)
    }
    player.value = null
  }

  // 确保video元素也被清理
  if (videoPlayer.value) {
    try {
      console.log('destroyVideoPlayer: 清理video元素')
      videoPlayer.value.pause()
      videoPlayer.value.currentTime = 0
      videoPlayer.value.src = ''
      videoPlayer.value.removeAttribute('src')
      videoPlayer.value.load()
      videoPlayer.value.muted = false
    } catch (error) {
      console.error('清理video元素错误:', error)
    }
  }

  // 重置状态
  isPlayerInitialized.value = false

  console.log('视频播放器已完全销毁')
}

// 从推流地址中获取HLS地址
const getHlsUrl = (liveUrl) => {
  if (!liveUrl) return null

  console.log('原始推流地址:', liveUrl)

  // 如果liveUrl是JSON格式，解析出HLS地址
  try {
    const urlData = JSON.parse(liveUrl)
    console.log('解析的JSON数据:', urlData)
    const hlsUrl = urlData.hls || urlData.m3u8 || liveUrl
    console.log('提取的HLS地址:', hlsUrl)
    return hlsUrl
  } catch (error) {
    console.log('不是JSON格式，直接使用原地址')
    // 如果不是JSON格式，直接返回原地址
    return liveUrl
  }
}

// 监听回放状态变化
watch(() => props.isReplay, (isReplay) => {
  console.log('VideoPlayer: isReplay状态变化为:', isReplay)
  nextTick(() => {
    initPlayer()
  })
})

// 监听URL变化
watch(() => [props.liveUrl, props.replayUrl], ([newLiveUrl, newReplayUrl]) => {
  console.log('VideoPlayer: URL变化检测')
  console.log('VideoPlayer: 新的直播URL:', newLiveUrl)
  console.log('VideoPlayer: 新的回放URL:', newReplayUrl)

  if (props.autoInit) {
    console.log('VideoPlayer: URL变化，重新初始化播放器')
    nextTick(() => {
      initPlayer()
    })
  }
})

// 统一的初始化方法
const initPlayer = () => {
  console.log('VideoPlayer: 开始初始化播放器')
  console.log('VideoPlayer: isReplay =', props.isReplay)
  console.log('VideoPlayer: liveUrl =', props.liveUrl)
  console.log('VideoPlayer: replayUrl =', props.replayUrl)

  // 先销毁现有播放器，确保每次初始化都是干净的
  console.log('VideoPlayer: 先销毁现有播放器')
  destroyVideoPlayer()

  // 强制重置状态
  console.log('VideoPlayer: 重置播放器状态')
  isPlayerInitialized.value = false

  // 等待一小段时间确保销毁完成
  setTimeout(() => {
    if (props.isReplay) {
      console.log('VideoPlayer: 初始化回放播放器')
      initReplayPlayer()
    } else {
      console.log('VideoPlayer: 初始化直播播放器')
      initVideoPlayer()
    }
  }, 50)
}

onMounted(() => {
  console.log('VideoPlayer: 组件已挂载')
  // 组件挂载后自动初始化播放器
  if (props.autoInit) {
    nextTick(() => {
      initPlayer()
    })
  }
})

onBeforeUnmount(() => {
  destroyVideoPlayer()
})

// 暴露方法给父组件
defineExpose({
  initVideoPlayer,
  initReplayPlayer,
  destroyVideoPlayer,
  initPlayer
})
</script>

<style lang="scss" scoped>
.video-player-container {
  width: 100%;
  height: 100%;
  z-index: 2;

  .player-container {
    width: 100%;
    height: 100%;

    #video-player {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
