{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"BackgroundCheckInfo\"\n};\nvar _hoisted_2 = {\n  class: \"BackgroundCheckInfoBody\"\n};\nvar _hoisted_3 = {\n  class: \"BackgroundCheckInfoTime\"\n};\nvar _hoisted_4 = {\n  class: \"BackgroundCheckInfoInfo\"\n};\nvar _hoisted_5 = {\n  class: \"BackgroundCheckInfoImg\"\n};\nvar _hoisted_6 = {\n  class: \"BackgroundCheckInfoInfoItem\"\n};\nvar _hoisted_7 = {\n  class: \"BackgroundCheckInfoUserName\"\n};\nvar _hoisted_8 = {\n  class: \"BackgroundCheckInfoUserIdcard\"\n};\nvar _hoisted_9 = {\n  class: \"BackgroundCheckInfoInfoItem\"\n};\nvar _hoisted_10 = {\n  class: \"BackgroundCheckInfoText\"\n};\nvar _hoisted_11 = {\n  class: \"BackgroundCheckInfoText\"\n};\nvar _hoisted_12 = {\n  class: \"BackgroundCheckInfoResult\"\n};\nvar _hoisted_13 = {\n  key: 0,\n  class: \"BackgroundCheckInfoResultVerify\"\n};\nvar _hoisted_14 = {\n  class: \"BackgroundCheckInfoResultVerifyTisp\"\n};\nvar _hoisted_15 = {\n  key: 0,\n  class: \"BackgroundCheckInfoResultButton\"\n};\nvar _hoisted_16 = {\n  key: 0,\n  class: \"BackgroundCheckInfoResultItemName\"\n};\nvar _hoisted_17 = {\n  key: 1,\n  class: \"BackgroundCheckInfoResultItemName\"\n};\nvar _hoisted_18 = {\n  class: \"globalTable\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_MessageBox = _resolveComponent(\"MessageBox\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  var _component_el_tabs = _resolveComponent(\"el-tabs\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_anchor_location = _resolveComponent(\"anchor-location\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_anchor_location, null, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_2, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"BackgroundCheckInfoTitle\"\n      }, \"个人司法背景审查报告\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, \"数据截止至：\" + _toDisplayString($setup.format(new Date(), 'YYYY-MM-DD')), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n        class: \"BackgroundCheckInfoTips\"\n      }, \"免责声明：大数据风险排查，不能作为最终判定\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_image, {\n        src: $setup.api.defaultImgURL('default_user_head.jpg'),\n        fit: \"cover\"\n      }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.info.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.route.query.idcard), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, \"发起人：\" + _toDisplayString($setup.user.userName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, \"发起时间：\" + _toDisplayString($setup.format(new Date(), 'YYYY-MM-DD HH:mm')), 1 /* TEXT */)]), _createCommentVNode(\" <div class=\\\"BackgroundCheckInfoInfoItem\\\">\\r\\n            <div class=\\\"BackgroundCheckInfoText\\\">授权时间：2024-03-13 12:00</div>\\r\\n            <div></div>\\r\\n          </div> \")]), _createVNode($setup[\"DetailsTitle\"], null, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n            class: \"BackgroundCheckInfoName\"\n          }, \"审查结果\", -1 /* HOISTED */)]);\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_12, [$setup.info.status !== '200' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, _toDisplayString($setup.tipsObj[$setup.info.status]), 1 /* TEXT */), ['0', '2'].includes($setup.info.status) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.faceResult\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"刷新\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.openVerify\n      }, {\n        default: _withCtx(function () {\n          return _cache[4] || (_cache[4] = [_createTextVNode(\"去验证\")]);\n        }),\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dataList, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"BackgroundCheckInfoResultItem\",\n          key: item.id\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"BackgroundCheckInfoResultItemIcon\", {\n            anomaly: item.amount\n          }])\n        }, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_MessageBox)];\n          }),\n          _: 1 /* STABLE */\n        })], 2 /* CLASS */), item.amount ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createTextVNode(_toDisplayString(item.name) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString(item.amount), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), !item.amount ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, \"无\" + _toDisplayString(item.name) + \"记录\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]);\n      }), 128 /* KEYED_FRAGMENT */))]), $setup.info.status === '200' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 0\n      }, [_createVNode($setup[\"DetailsTitle\"], null, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n            class: \"BackgroundCheckInfoName\"\n          }, \"审查纪录\", -1 /* HOISTED */)]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_tabs, {\n        modelValue: $setup.activeName,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.activeName = $event;\n        }),\n        onTabChange: $setup.handleClick\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dataList, function (item) {\n            return _openBlock(), _createBlock(_component_el_tab_pane, {\n              key: item.id,\n              name: item.id,\n              label: item.name\n            }, null, 8 /* PROPS */, [\"name\", \"label\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_table, {\n        data: $setup.tableData\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_table_column, {\n            label: \"案号\",\n            \"min-width\": \"160\",\n            prop: \"c_ah\"\n          }, {\n            default: _withCtx(function (scope) {\n              return [_createVNode(_component_el_link, {\n                type: \"primary\",\n                onClick: function onClick($event) {\n                  return $setup.handleRow(scope.row);\n                }\n              }, {\n                default: _withCtx(function () {\n                  return [_createTextVNode(_toDisplayString(scope.row.c_ah), 1 /* TEXT */)];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_table_column, {\n            label: \"案由\",\n            \"min-width\": \"160\",\n            prop: \"n_laay\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"诉讼地位\",\n            \"min-width\": \"160\",\n            prop: \"n_ssdw\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"立案时间\",\n            width: \"130\",\n            prop: \"d_larq\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"结案时间\",\n            width: \"130\",\n            prop: \"d_jarq\"\n          })];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"data\"])])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"案件详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"LegalCaseDetails\"], {\n        data: $setup.rowInfo\n      }, null, 8 /* PROPS */, [\"data\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_anchor_location", "default", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$setup", "format", "Date", "_hoisted_4", "_hoisted_5", "_component_el_image", "src", "api", "defaultImgURL", "fit", "_hoisted_6", "_hoisted_7", "info", "name", "_hoisted_8", "route", "query", "idcard", "_hoisted_9", "_hoisted_10", "user", "userName", "_hoisted_11", "_createCommentVNode", "_cache", "_", "_hoisted_12", "status", "_hoisted_13", "_hoisted_14", "tipsObj", "includes", "_hoisted_15", "_component_el_button", "type", "onClick", "faceResult", "_createTextVNode", "openVerify", "_Fragment", "_renderList", "dataList", "item", "id", "_normalizeClass", "anomaly", "amount", "_component_el_icon", "_component_MessageBox", "_hoisted_16", "_hoisted_17", "_component_el_tabs", "modelValue", "activeName", "$event", "onTabChange", "handleClick", "_createBlock", "_component_el_tab_pane", "label", "_hoisted_18", "_component_el_table", "data", "tableData", "_component_el_table_column", "prop", "scope", "_component_el_link", "handleRow", "row", "c_ah", "width", "_component_xyl_popup_window", "show", "rowInfo"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\BackgroundCheck\\BackgroundCheckInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"BackgroundCheckInfo\">\r\n    <anchor-location>\r\n      <div class=\"BackgroundCheckInfoBody\">\r\n        <div class=\"BackgroundCheckInfoTitle\">个人司法背景审查报告</div>\r\n        <div class=\"BackgroundCheckInfoTime\">数据截止至：{{ format(new Date(), 'YYYY-MM-DD') }}</div>\r\n        <div class=\"BackgroundCheckInfoTips\">免责声明：大数据风险排查，不能作为最终判定</div>\r\n        <div class=\"BackgroundCheckInfoInfo\">\r\n          <div class=\"BackgroundCheckInfoImg\">\r\n            <el-image :src=\"api.defaultImgURL('default_user_head.jpg')\" fit=\"cover\" />\r\n          </div>\r\n          <div class=\"BackgroundCheckInfoInfoItem\">\r\n            <div class=\"BackgroundCheckInfoUserName\">{{ info.name }}</div>\r\n            <div class=\"BackgroundCheckInfoUserIdcard\">{{ route.query.idcard }}</div>\r\n          </div>\r\n          <div class=\"BackgroundCheckInfoInfoItem\">\r\n            <div class=\"BackgroundCheckInfoText\">发起人：{{ user.userName }}</div>\r\n            <div class=\"BackgroundCheckInfoText\">发起时间：{{ format(new Date(), 'YYYY-MM-DD HH:mm') }}</div>\r\n          </div>\r\n          <!-- <div class=\"BackgroundCheckInfoInfoItem\">\r\n            <div class=\"BackgroundCheckInfoText\">授权时间：2024-03-13 12:00</div>\r\n            <div></div>\r\n          </div> -->\r\n        </div>\r\n        <DetailsTitle>\r\n          <div class=\"BackgroundCheckInfoName\">审查结果</div>\r\n        </DetailsTitle>\r\n        <div class=\"BackgroundCheckInfoResult\">\r\n          <div class=\"BackgroundCheckInfoResultVerify\" v-if=\"info.status !== '200'\">\r\n            <div class=\"BackgroundCheckInfoResultVerifyTisp\">{{ tipsObj[info.status] }} </div>\r\n            <div class=\"BackgroundCheckInfoResultButton\" v-if=\"['0', '2'].includes(info.status)\">\r\n              <el-button type=\"primary\" @click=\"faceResult\">刷新</el-button>\r\n              <el-button type=\"primary\" @click=\"openVerify\">去验证</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"BackgroundCheckInfoResultItem\" v-for=\"item in dataList\" :key=\"item.id\">\r\n            <div class=\"BackgroundCheckInfoResultItemIcon\" :class=\"{ anomaly: item.amount }\">\r\n              <el-icon>\r\n                <MessageBox />\r\n              </el-icon>\r\n            </div>\r\n            <div class=\"BackgroundCheckInfoResultItemName\" v-if=\"item.amount\">\r\n              {{ item.name }} <span>{{ item.amount }}</span>\r\n            </div>\r\n            <div class=\"BackgroundCheckInfoResultItemName\" v-if=\"!item.amount\">无{{ item.name }}记录</div>\r\n          </div>\r\n        </div>\r\n        <template v-if=\"info.status === '200'\">\r\n          <DetailsTitle>\r\n            <div class=\"BackgroundCheckInfoName\">审查纪录</div>\r\n          </DetailsTitle>\r\n          <el-tabs v-model=\"activeName\" @tab-change=\"handleClick\">\r\n            <el-tab-pane v-for=\"item in dataList\" :key=\"item.id\" :name=\"item.id\" :label=\"item.name\"></el-tab-pane>\r\n          </el-tabs>\r\n          <div class=\"globalTable\">\r\n            <el-table :data=\"tableData\">\r\n              <el-table-column label=\"案号\" min-width=\"160\" prop=\"c_ah\">\r\n                <template #default=\"scope\">\r\n                  <el-link type=\"primary\" @click=\"handleRow(scope.row)\">{{ scope.row.c_ah }} </el-link>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"案由\" min-width=\"160\" prop=\"n_laay\" />\r\n              <el-table-column label=\"诉讼地位\" min-width=\"160\" prop=\"n_ssdw\" />\r\n              <el-table-column label=\"立案时间\" width=\"130\" prop=\"d_larq\" />\r\n              <el-table-column label=\"结案时间\" width=\"130\" prop=\"d_jarq\" />\r\n            </el-table>\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </anchor-location>\r\n    <xyl-popup-window v-model=\"show\" name=\"案件详情\">\r\n      <LegalCaseDetails :data=\"rowInfo\"></LegalCaseDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'BackgroundCheckInfo' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\nimport { user } from 'common/js/system_var.js'\r\nimport DetailsTitle from './components/DetailsTitle.vue'\r\nimport LegalCaseDetails from './components/LegalCaseDetails.vue'\r\nconst route = useRoute()\r\nconst tipsObj = {\r\n  '0': '等待用户人脸验证',\r\n  '1': '用户已拒绝人脸识别',\r\n  '2': '认证未完成',\r\n  '200': '认证通过',\r\n  '201': '姓名和身份证不一致',\r\n  '202': '查询不到身份信息',\r\n  '203': '查询不到照片或照片不可用',\r\n  '204': '人脸对比不一致',\r\n  '205': '活体检测存在风险',\r\n  '20': '6业务策略限制，认证不通过'\r\n}\r\nconst info = ref({})\r\nconst details = ref({})\r\nconst dataList = ref([\r\n  { id: 'criminal', name: '刑事案件', amount: 0 },\r\n  { id: 'implement', name: '执行案件', amount: 0 },\r\n  { id: 'administrative', name: '行政案件', amount: 0 },\r\n  { id: 'preservation', name: '非诉保全审查', amount: 10 },\r\n  { id: 'civil', name: '民事案件', amount: 0 },\r\n  { id: 'bankrupt', name: '强制清算与破产案件', amount: 18 }\r\n])\r\nconst activeName = ref('')\r\nconst tableData = ref([])\r\nconst rowInfo = ref({})\r\nconst show = ref(false)\r\n\r\nconst faceResult = async () => {\r\n  const { data } = await api.faceResult({ uuid: route.query.uuid })\r\n  info.value = data\r\n  if (data.status === '200') {\r\n    lawsuitResult()\r\n  }\r\n}\r\nconst lawsuitResult = async () => {\r\n  const { data } = await api.lawsuitResult({ uuid: route.query.uuid })\r\n  details.value = data\r\n  activeName.value = 'criminal'\r\n  dataList.value.forEach(item => {\r\n    item.amount = data[item.id]?.cases?.length || 0\r\n  })\r\n  handleClick(activeName.value)\r\n}\r\nconst openVerify = () => {\r\n  window.open(route.query.url, '_blank')\r\n}\r\nconst handleClick = (tab) => {\r\n  tableData.value = details.value[tab]?.cases\r\n}\r\nconst handleRow = (row) => {\r\n  rowInfo.value = row\r\n  show.value = true\r\n}\r\nonMounted(() => { faceResult() })\r\n</script>\r\n<style lang=\"scss\">\r\n.BackgroundCheckInfo {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .BackgroundCheckInfoBody {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: var(--zy-distance-one);\r\n\r\n    .BackgroundCheckInfoTitle {\r\n      font-weight: bold;\r\n      text-align: center;\r\n      font-size: var(--zy-title-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-bottom: var(--zy-distance-two);\r\n    }\r\n\r\n    .BackgroundCheckInfoName {\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .BackgroundCheckInfoTime {\r\n      font-weight: bold;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-bottom: var(--zy-font-name-distance-five);\r\n    }\r\n\r\n    .BackgroundCheckInfoTips {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-color-danger);\r\n      padding-bottom: var(--zy-distance-two);\r\n    }\r\n\r\n    .BackgroundCheckInfoInfo {\r\n      display: flex;\r\n      padding: var(--zy-distance-two) var(--zy-distance-one);\r\n      background-color: var(--zy-el-color-info-light-9);\r\n      margin-bottom: var(--zy-distance-two);\r\n\r\n      .BackgroundCheckInfoImg {\r\n        width: 60px;\r\n        height: 60px;\r\n        margin-right: 9px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          height: 100%;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n\r\n      .BackgroundCheckInfoInfoItem {\r\n        width: calc(33.3% - 23px);\r\n        padding: 0 var(--zy-distance-five);\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n\r\n        .BackgroundCheckInfoUserName {\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n\r\n        .BackgroundCheckInfoUserIdcard {\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n\r\n        .BackgroundCheckInfoText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n      }\r\n    }\r\n\r\n    .BackgroundCheckInfoResult {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      position: relative;\r\n\r\n      .BackgroundCheckInfoResultVerify {\r\n        position: absolute;\r\n        left: 0;\r\n        bottom: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        background: #fff;\r\n        z-index: 9;\r\n\r\n        .BackgroundCheckInfoResultVerifyTisp {\r\n          text-align: center;\r\n          padding-top: 40px;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding-bottom: var(--zy-distance-three);\r\n        }\r\n\r\n        .BackgroundCheckInfoResultButton {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          .zy-el-button {\r\n            margin-left: 10px;\r\n            --zy-el-button-size: var(--zy-height-secondary);\r\n          }\r\n        }\r\n      }\r\n\r\n      .BackgroundCheckInfoResultItem {\r\n        width: calc(33.3% - 13px);\r\n        height: 52px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: var(--zy-distance-three) var(--zy-distance-two);\r\n        margin-bottom: var(--zy-distance-two);\r\n        background-color: var(--zy-el-color-info-light-9);\r\n\r\n        .BackgroundCheckInfoResultItemIcon {\r\n          width: 30px;\r\n          height: 30px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          background: var(--zy-el-color-success-light-3);\r\n          border-radius: 6px;\r\n\r\n          .zy-el-icon {\r\n            font-size: 20px;\r\n            color: #fff;\r\n          }\r\n        }\r\n\r\n        .BackgroundCheckInfoResultItemIcon.anomaly {\r\n          background: var(--zy-el-color-danger-light-3);\r\n        }\r\n\r\n        .BackgroundCheckInfoResultItemName {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 0 var(--zy-distance-four);\r\n          font-size: var(--zy-text-font-size);\r\n\r\n          span {\r\n            margin-left: 10px;\r\n            font-weight: bold;\r\n            font-size: var(--zy-name-font-size);\r\n            color: var(--zy-el-color-danger);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .globalTable {\r\n      width: 100%;\r\n      height: 380px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAyB;;EAE7BA,KAAK,EAAC;AAAyB;;EAE/BA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAwB;;EAG9BA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAA6B;;EACnCA,KAAK,EAAC;AAA+B;;EAEvCA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAyB;;EAC/BA,KAAK,EAAC;AAAyB;;EAUnCA,KAAK,EAAC;AAA2B;;EA3B9CC,GAAA;EA4BeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAqC;;EA7B5DC,GAAA;EA8BiBD,KAAK,EAAC;;;EA9BvBC,GAAA;EAyCiBD,KAAK,EAAC;;;EAzCvBC,GAAA;EA4CiBD,KAAK,EAAC;;;EAURA,KAAK,EAAC;AAAa;;;;;;;;;;;;;uBArDhCE,mBAAA,CAwEM,OAxENC,UAwEM,GAvEJC,YAAA,CAmEkBC,0BAAA;IArEtBC,OAAA,EAAAC,QAAA,CAGM;MAAA,OAiEM,CAjENC,mBAAA,CAiEM,OAjENC,UAiEM,G,0BAhEJD,mBAAA,CAAsD;QAAjDR,KAAK,EAAC;MAA0B,GAAC,YAAU,sBAChDQ,mBAAA,CAAuF,OAAvFE,UAAuF,EAAlD,QAAM,GAAAC,gBAAA,CAAGC,MAAA,CAAAC,MAAM,KAAKC,IAAI,mC,0BAC7DN,mBAAA,CAAgE;QAA3DR,KAAK,EAAC;MAAyB,GAAC,uBAAqB,sBAC1DQ,mBAAA,CAgBM,OAhBNO,UAgBM,GAfJP,mBAAA,CAEM,OAFNQ,UAEM,GADJZ,YAAA,CAA0Ea,mBAAA;QAA/DC,GAAG,EAAEN,MAAA,CAAAO,GAAG,CAACC,aAAa;QAA2BC,GAAG,EAAC;0CAElEb,mBAAA,CAGM,OAHNc,UAGM,GAFJd,mBAAA,CAA8D,OAA9De,UAA8D,EAAAZ,gBAAA,CAAlBC,MAAA,CAAAY,IAAI,CAACC,IAAI,kBACrDjB,mBAAA,CAAyE,OAAzEkB,UAAyE,EAAAf,gBAAA,CAA3BC,MAAA,CAAAe,KAAK,CAACC,KAAK,CAACC,MAAM,iB,GAElErB,mBAAA,CAGM,OAHNsB,UAGM,GAFJtB,mBAAA,CAAkE,OAAlEuB,WAAkE,EAA7B,MAAI,GAAApB,gBAAA,CAAGC,MAAA,CAAAoB,IAAI,CAACC,QAAQ,kBACzDzB,mBAAA,CAA4F,OAA5F0B,WAA4F,EAAvD,OAAK,GAAAvB,gBAAA,CAAGC,MAAA,CAAAC,MAAM,KAAKC,IAAI,wC,GAE9DqB,mBAAA,kLAGU,C,GAEZ/B,YAAA,CAEeQ,MAAA;QA1BvBN,OAAA,EAAAC,QAAA,CAyBU;UAAA,OAA+C6B,MAAA,QAAAA,MAAA,OAA/C5B,mBAAA,CAA+C;YAA1CR,KAAK,EAAC;UAAyB,GAAC,MAAI,oB;;QAzBnDqC,CAAA;UA2BQ7B,mBAAA,CAmBM,OAnBN8B,WAmBM,GAlB+C1B,MAAA,CAAAY,IAAI,CAACe,MAAM,c,cAA9DrC,mBAAA,CAMM,OANNsC,WAMM,GALJhC,mBAAA,CAAkF,OAAlFiC,WAAkF,EAAA9B,gBAAA,CAA9BC,MAAA,CAAA8B,OAAO,CAAC9B,MAAA,CAAAY,IAAI,CAACe,MAAM,mB,WACTI,QAAQ,CAAC/B,MAAA,CAAAY,IAAI,CAACe,MAAM,K,cAAlFrC,mBAAA,CAGM,OAHN0C,WAGM,GAFJxC,YAAA,CAA4DyC,oBAAA;QAAjDC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAEnC,MAAA,CAAAoC;;QA/BhD1C,OAAA,EAAAC,QAAA,CA+B4D;UAAA,OAAE6B,MAAA,QAAAA,MAAA,OA/B9Da,gBAAA,CA+B4D,IAAE,E;;QA/B9DZ,CAAA;UAgCcjC,YAAA,CAA6DyC,oBAAA;QAAlDC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAEnC,MAAA,CAAAsC;;QAhChD5C,OAAA,EAAAC,QAAA,CAgC4D;UAAA,OAAG6B,MAAA,QAAAA,MAAA,OAhC/Da,gBAAA,CAgC4D,KAAG,E;;QAhC/DZ,CAAA;cAAAF,mBAAA,e,KAAAA,mBAAA,iB,kBAmCUjC,mBAAA,CAUMiD,SAAA,QA7ChBC,WAAA,CAmCoExC,MAAA,CAAAyC,QAAQ,EAnC5E,UAmC4DC,IAAI;6BAAtDpD,mBAAA,CAUM;UAVDF,KAAK,EAAC,+BAA+B;UAA2BC,GAAG,EAAEqD,IAAI,CAACC;YAC7E/C,mBAAA,CAIM;UAJDR,KAAK,EApCtBwD,eAAA,EAoCuB,mCAAmC;YAAAC,OAAA,EAAoBH,IAAI,CAACI;UAAM;YAC3EtD,YAAA,CAEUuD,kBAAA;UAvCxBrD,OAAA,EAAAC,QAAA,CAsCgB;YAAA,OAAc,CAAdH,YAAA,CAAcwD,qBAAA,E;;UAtC9BvB,CAAA;6BAyCiEiB,IAAI,CAACI,MAAM,I,cAAhExD,mBAAA,CAEM,OAFN2D,WAEM,GA3ClBZ,gBAAA,CAAAtC,gBAAA,CA0CiB2C,IAAI,CAAC7B,IAAI,IAAG,GAAC,iBAAAjB,mBAAA,CAA8B,cAAAG,gBAAA,CAArB2C,IAAI,CAACI,MAAM,iB,KA1ClDvB,mBAAA,gB,CA4CkEmB,IAAI,CAACI,MAAM,I,cAAjExD,mBAAA,CAA2F,OAA3F4D,WAA2F,EAAxB,GAAC,GAAAnD,gBAAA,CAAG2C,IAAI,CAAC7B,IAAI,IAAG,IAAE,mBA5CjGU,mBAAA,e;wCA+CwBvB,MAAA,CAAAY,IAAI,CAACe,MAAM,c,cAA3BrC,mBAAA,CAoBWiD,SAAA;QAnEnBlD,GAAA;MAAA,IAgDUG,YAAA,CAEeQ,MAAA;QAlDzBN,OAAA,EAAAC,QAAA,CAiDY;UAAA,OAA+C6B,MAAA,QAAAA,MAAA,OAA/C5B,mBAAA,CAA+C;YAA1CR,KAAK,EAAC;UAAyB,GAAC,MAAI,oB;;QAjDrDqC,CAAA;UAmDUjC,YAAA,CAEU2D,kBAAA;QArDpBC,UAAA,EAmD4BpD,MAAA,CAAAqD,UAAU;QAnDtC,uBAAA7B,MAAA,QAAAA,MAAA,gBAAA8B,MAAA;UAAA,OAmD4BtD,MAAA,CAAAqD,UAAU,GAAAC,MAAA;QAAA;QAAGC,WAAU,EAAEvD,MAAA,CAAAwD;;QAnDrD9D,OAAA,EAAAC,QAAA,CAoDyB;UAAA,OAAwB,E,kBAArCL,mBAAA,CAAsGiD,SAAA,QApDlHC,WAAA,CAoDwCxC,MAAA,CAAAyC,QAAQ,EApDhD,UAoDgCC,IAAI;iCAAxBe,YAAA,CAAsGC,sBAAA;cAA/DrE,GAAG,EAAEqD,IAAI,CAACC,EAAE;cAAG9B,IAAI,EAAE6B,IAAI,CAACC,EAAE;cAAGgB,KAAK,EAAEjB,IAAI,CAAC7B;;;;QApD9FY,CAAA;yCAsDU7B,mBAAA,CAYM,OAZNgE,WAYM,GAXJpE,YAAA,CAUWqE,mBAAA;QAVAC,IAAI,EAAE9D,MAAA,CAAA+D;MAAS;QAvDtCrE,OAAA,EAAAC,QAAA,CAwDc;UAAA,OAIkB,CAJlBH,YAAA,CAIkBwE,0BAAA;YAJDL,KAAK,EAAC,IAAI;YAAC,WAAS,EAAC,KAAK;YAACM,IAAI,EAAC;;YACpCvE,OAAO,EAAAC,QAAA,CAChB,UAAqFuE,KAD9D;cAAA,QACvB1E,YAAA,CAAqF2E,kBAAA;gBAA5EjC,IAAI,EAAC,SAAS;gBAAEC,OAAK,WAALA,OAAKA,CAAAmB,MAAA;kBAAA,OAAEtD,MAAA,CAAAoE,SAAS,CAACF,KAAK,CAACG,GAAG;gBAAA;;gBA1DrE3E,OAAA,EAAAC,QAAA,CA0DwE;kBAAA,OAAoB,CA1D5F0C,gBAAA,CAAAtC,gBAAA,CA0D2EmE,KAAK,CAACG,GAAG,CAACC,IAAI,iB;;gBA1DzF7C,CAAA;;;YAAAA,CAAA;cA6DcjC,YAAA,CAA4DwE,0BAAA;YAA3CL,KAAK,EAAC,IAAI;YAAC,WAAS,EAAC,KAAK;YAACM,IAAI,EAAC;cACjDzE,YAAA,CAA8DwE,0BAAA;YAA7CL,KAAK,EAAC,MAAM;YAAC,WAAS,EAAC,KAAK;YAACM,IAAI,EAAC;cACnDzE,YAAA,CAA0DwE,0BAAA;YAAzCL,KAAK,EAAC,MAAM;YAACY,KAAK,EAAC,KAAK;YAACN,IAAI,EAAC;cAC/CzE,YAAA,CAA0DwE,0BAAA;YAAzCL,KAAK,EAAC,MAAM;YAACY,KAAK,EAAC,KAAK;YAACN,IAAI,EAAC;;;QAhE7DxC,CAAA;mEAAAF,mBAAA,e;;IAAAE,CAAA;MAsEIjC,YAAA,CAEmBgF,2BAAA;IAxEvBpB,UAAA,EAsE+BpD,MAAA,CAAAyE,IAAI;IAtEnC,uBAAAjD,MAAA,QAAAA,MAAA,gBAAA8B,MAAA;MAAA,OAsE+BtD,MAAA,CAAAyE,IAAI,GAAAnB,MAAA;IAAA;IAAEzC,IAAI,EAAC;;IAtE1CnB,OAAA,EAAAC,QAAA,CAuEM;MAAA,OAAqD,CAArDH,YAAA,CAAqDQ,MAAA;QAAlC8D,IAAI,EAAE9D,MAAA,CAAA0E;MAAO,kC;;IAvEtCjD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}