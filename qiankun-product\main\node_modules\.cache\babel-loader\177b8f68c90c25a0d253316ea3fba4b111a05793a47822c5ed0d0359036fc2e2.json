{"ast": null, "code": "import { ref, onMounted } from 'vue';\nvar __default__ = {\n  name: 'ActivityLeaveDetails'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var details = ref({});\n    onMounted(function () {\n      details.value = props.data;\n    });\n    var __returned__ = {\n      props,\n      details,\n      ref,\n      onMounted\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "__default__", "name", "props", "__props", "details", "value", "data"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/BackgroundCheck/components/LegalCaseDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ActivityLeaveDetails\">\r\n    <div class=\"ActivityLeaveName\">{{ details.c_ah }}</div>\r\n    <global-info>\r\n      <!-- <global-info-item label=\"诉讼地位\">{{ details.n_ssdw }}</global-info-item>\r\n      <global-info-item label=\"立案案由\">{{ details.n_laay }}</global-info-item>\r\n      <global-info-line>\r\n        <global-info-item label=\"案件类型\">{{ details.n_ajlx }}</global-info-item>\r\n        <global-info-item label=\"立案时间\">{{ details.d_larq }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-item label=\"经办法院\">{{ details.n_jbfy }}</global-info-item>\r\n      <global-info-item label=\"判处结果\">{{ details.n_pcjg }}</global-info-item>\r\n      <global-info-line>\r\n        <global-info-item label=\"立案时间\">{{ details.d_larq }}</global-info-item>\r\n        <global-info-item label=\"结案时间\">{{ details.d_jarq }}</global-info-item>\r\n      </global-info-line>\r\n      <global-info-item label=\"结案案由\">{{ details.n_jaay }}</global-info-item> -->\r\n\r\n      <global-info-item label=\"案件类型\">{{ details.n_ajlx }}</global-info-item>\r\n      <global-info-item label=\"原审案号\">{{ details.c_ah_ys }}</global-info-item>\r\n      <global-info-item label=\"后续案号\">{{ details.c_ah_hx }}</global-info-item>\r\n      <global-info-item label=\"案件标识\">{{ details.n_ajbs }}</global-info-item>\r\n      <global-info-item label=\"经办法院\">{{ details.n_jbfy }}</global-info-item>\r\n      <global-info-item label=\"法院所属层级\">{{ details.n_jbfy_cj }}</global-info-item>\r\n      <global-info-item label=\"案件进展阶段\">{{ details.n_ajjzjd }}</global-info-item>\r\n      <global-info-item label=\"审理程序\">{{ details.n_slcx }}</global-info-item>\r\n      <global-info-item label=\"所属地域\">{{ details.c_ssdy }}</global-info-item>\r\n      <global-info-item label=\"立案时间\">{{ details.d_larq }}</global-info-item>\r\n      <global-info-item label=\"立案案由\">{{ details.n_laay }}</global-info-item>\r\n      <global-info-item label=\"立案案由标签\">{{ details.n_laay_tag }}</global-info-item>\r\n      <global-info-item label=\"立案案由详细\">{{ details.n_laay_tree }}</global-info-item>\r\n      <global-info-item label=\"起诉标的金额等级\">{{ details.n_qsbdje_level }}</global-info-item>\r\n      <global-info-item label=\"起诉标的金额\">{{ details.n_qsbdje }}</global-info-item>\r\n      <global-info-item label=\"起诉标的金额估计等级\">{{ details.n_qsbdje_gj_level }}</global-info-item>\r\n      <global-info-item label=\"起诉标的金额估计\">{{ details.n_qsbdje_gj }}</global-info-item>\r\n      <global-info-item label=\"审理方式信息\">{{ details.c_slfsxx }}</global-info-item>\r\n      <global-info-item label=\"结案时间\">{{ details.d_jarq }}</global-info-item>\r\n      <global-info-item label=\"结案案由\">{{ details.n_jaay }}</global-info-item>\r\n      <global-info-item label=\"结案案由标签\">{{ details.n_jaay_tag }}</global-info-item>\r\n      <global-info-item label=\"结案案由详细\">{{ details.n_jaay_tree }}</global-info-item>\r\n      <global-info-item label=\"结案标的金额等级\">{{ details.n_jabdje_level }}</global-info-item>\r\n      <global-info-item label=\"结案标的金额\">{{ details.n_jabdje }}</global-info-item>\r\n      <global-info-item label=\"结案标的金额估计等级\">{{ details.n_jabdje_gj_level }}</global-info-item>\r\n      <global-info-item label=\"结案标的金额估计\">{{ details.n_jabdje_gj }}</global-info-item>\r\n      <global-info-item label=\"结案方式\">{{ details.n_jafs }}</global-info-item>\r\n      <global-info-item label=\"胜诉估计\">{{ details.n_pj_victory }}</global-info-item>\r\n      <global-info-item label=\"诉讼地位\">{{ details.n_ssdw }}</global-info-item>\r\n      <global-info-item label=\"一审诉讼地位\">{{ details.n_ssdw_ys }}</global-info-item>\r\n      <global-info-item label=\"公开文书ID\">{{ details.c_gkws_id }}</global-info-item>\r\n      <global-info-item label=\"相关案件号\">{{ details.c_gkws_glah }}</global-info-item>\r\n      <global-info-item label=\"当事人\">{{ details.c_gkws_dsr }}</global-info-item>\r\n      <global-info-item label=\"判决结果\">{{ details.c_gkws_pjjg }}</global-info-item>\r\n      <global-info-item label=\"犯罪金额等级\">{{ details.n_fzje_level }}</global-info-item>\r\n      <global-info-item label=\"犯罪金额\">{{ details.n_fzje }}</global-info-item>\r\n      <global-info-item label=\"被请求赔偿金额等级\">{{ details.n_bqqpcje_level }}</global-info-item>\r\n      <global-info-item label=\"被请求赔偿金额\">{{ details.n_bqqpcje }}</global-info-item>\r\n      <global-info-item label=\"财产刑执行金额等级\">{{ details.n_ccxzxje_level }}</global-info-item>\r\n      <global-info-item label=\"财产刑执行金额\">{{ details.n_ccxzxje }}</global-info-item>\r\n      <global-info-item label=\"财产刑执行金额估计等级\">{{ details.n_ccxzxje_gj_level }}</global-info-item>\r\n      <global-info-item label=\"财产刑执行金额估计\">{{ details.n_ccxzxje_gj }}</global-info-item>\r\n      <global-info-item label=\"判处赔偿金额等级\">{{ details.n_pcpcje_level }}</global-info-item>\r\n      <global-info-item label=\"判处赔偿金额\">{{ details.n_pcpcje }}</global-info-item>\r\n      <global-info-item label=\"判处赔偿金额估计等级\">{{ details.n_pcpcje_gj_level }}</global-info-item>\r\n      <global-info-item label=\"判处赔偿金额估计\">{{ details.n_pcpcje_gj }}</global-info-item>\r\n      <global-info-item label=\"判处结果\">{{ details.n_pcjg }}</global-info-item>\r\n      <global-info-item label=\"定罪罪名\">{{ details.n_dzzm }}</global-info-item>\r\n      <global-info-item label=\"定罪罪名详细\">{{ details.n_dzzm_tree }}</global-info-item>\r\n      <global-info-item label=\"申请执行标的金额\">{{ details.n_sqzxbdje }}</global-info-item>\r\n      <global-info-item label=\"实际到位金额\">{{ details.n_sjdwje }}</global-info-item>\r\n      <global-info-item label=\"未执行金额\">{{ details.n_wzxje }}</global-info-item>\r\n      <global-info-item label=\"申请保全数额等级\">{{ details.n_sqbqse_level }}</global-info-item>\r\n      <global-info-item label=\"申请保全数额\">{{ details.n_sqbqse }}</global-info-item>\r\n      <global-info-item label=\"申请保全标的物\">{{ details.c_sqbqbdw }}</global-info-item>\r\n      <global-info-item label=\"当事人\">{{ details.c_dsrxx }}</global-info-item>\r\n      <global-info-item label=\"名称\">{{ details.c_mc }}</global-info-item>\r\n      <global-info-item label=\"当事人类型\">{{ details.n_dsrlx }}</global-info-item>\r\n      <global-info-item label=\"诉讼地位\">{{ details.n_ssdw }}</global-info-item>\r\n    </global-info>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ActivityLeaveDetails' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nconst props = defineProps({ data: { type: Object, default: () => ({}) } })\r\n\r\nconst details = ref({})\r\n\r\nonMounted(() => { details.value = props.data }) \r\n</script>\r\n<style lang=\"scss\">\r\n.ActivityLeaveDetails {\r\n  width: 880px;\r\n  padding: 40px;\r\n\r\n  .ActivityLeaveName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    color: var(--zy-el-color-primary);\r\n    border-bottom: 1px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding-bottom: 20px;\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .global-info {\r\n\r\n    .global-info-item {\r\n      .global-info-label {\r\n        width: 160px;\r\n      }\r\n\r\n      .global-info-content {\r\n        width: calc(100% - 160px);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAoFA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAK;AAHpC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAuB,CAAC;;;;;;;;;;;;;IAI/C,IAAMC,KAAK,GAAGC,OAA4D;IAE1E,IAAMC,OAAO,GAAGN,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvBC,SAAS,CAAC,YAAM;MAAEK,OAAO,CAACC,KAAK,GAAGH,KAAK,CAACI,IAAI;IAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}