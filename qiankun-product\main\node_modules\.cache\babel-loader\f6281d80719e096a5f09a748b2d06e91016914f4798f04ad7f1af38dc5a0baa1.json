{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AssistedWriting\"\n};\nvar _hoisted_2 = {\n  class: \"AssistedWritingBody\"\n};\nvar _hoisted_3 = {\n  class: \"AssistedWritingUserBody\"\n};\nvar _hoisted_4 = {\n  class: \"AssistedWritingUser\"\n};\nvar _hoisted_5 = {\n  class: \"AssistedWritingUserInfo\"\n};\nvar _hoisted_6 = {\n  class: \"AssistedWritingUserName\"\n};\nvar _hoisted_7 = {\n  class: \"AssistedWritingUserText\"\n};\nvar _hoisted_8 = {\n  class: \"AssistedWritingList\"\n};\nvar _hoisted_9 = [\"onClick\"];\nvar _hoisted_10 = [\"innerHTML\"];\nvar _hoisted_11 = {\n  class: \"AssistedWritingName\"\n};\nvar _hoisted_12 = {\n  class: \"AssistedWritingEditorBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_image, {\n    src: $setup.user.image,\n    fit: \"cover\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.user.userName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.user.position), 1 /* TEXT */)])])]), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"AssistedWritingTitle\"\n  }, \"文档撰写类型\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.toolData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"AssistedWritingItem\", {\n        'is-active': $setup.toolId === item.chatToolCode\n      }]),\n      key: item.chatToolCode,\n      onClick: function onClick($event) {\n        return $setup.handleTool(item);\n      }\n    }, [_createElementVNode(\"div\", {\n      class: \"AssistedWritingIcon\",\n      innerHTML: $setup.toolIconData[item.chatToolCode]\n    }, null, 8 /* PROPS */, _hoisted_10), _createElementVNode(\"div\", _hoisted_11, _toDisplayString(item.chatToolName), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_9);\n  }), 128 /* KEYED_FRAGMENT */)), (_openBlock(), _createElementBlock(_Fragment, null, _renderList(2, function (item) {\n    return _createElementVNode(\"div\", {\n      class: \"AssistedWritingPlaceholder\",\n      key: item + '_placeholder'\n    });\n  }), 64 /* STABLE_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_12, [_withDirectives(_createVNode($setup[\"GlobalAiChatFile\"], {\n    fileList: $setup.fileList,\n    fileData: $setup.fileData,\n    onClose: $setup.handleClose\n  }, null, 8 /* PROPS */, [\"fileList\", \"fileData\"]), [[_vShow, $setup.fileList.length || $setup.fileData.length]]), _createVNode($setup[\"GlobalAiChatEditor\"], {\n    ref: \"editorRef\",\n    modelValue: $setup.sendContent,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.sendContent = $event;\n    }),\n    onSend: $setup.handleSendMessage,\n    onUploadCallback: $setup.handleFileUpload,\n    onFileCallback: $setup.handleFileCallback\n  }, null, 8 /* PROPS */, [\"modelValue\"])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_image", "src", "$setup", "user", "image", "fit", "_hoisted_5", "_hoisted_6", "_toDisplayString", "userName", "_hoisted_7", "position", "_hoisted_8", "_Fragment", "_renderList", "toolData", "item", "_normalizeClass", "toolId", "chatToolCode", "key", "onClick", "$event", "handleTool", "innerHTML", "toolIconData", "_hoisted_10", "_hoisted_11", "chatToolName", "_hoisted_9", "_hoisted_12", "fileList", "fileData", "onClose", "handleClose", "length", "ref", "modelValue", "send<PERSON><PERSON><PERSON>", "_cache", "onSend", "handleSendMessage", "onUploadCallback", "handleFileUpload", "onFileCallback", "handleFileCallback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AssistedWriting\\AssistedWriting.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AssistedWriting\">\r\n    <div class=\"AssistedWritingBody\">\r\n      <div class=\"AssistedWritingUserBody\">\r\n        <div class=\"AssistedWritingUser\">\r\n          <el-image :src=\"user.image\" fit=\"cover\" />\r\n          <div class=\"AssistedWritingUserInfo\">\r\n            <div class=\"AssistedWritingUserName\">{{ user.userName }}</div>\r\n            <div class=\"AssistedWritingUserText\">{{ user.position }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AssistedWritingTitle\">文档撰写类型</div>\r\n      <div class=\"AssistedWritingList\">\r\n        <div\r\n          class=\"AssistedWritingItem\"\r\n          :class=\"{ 'is-active': toolId === item.chatToolCode }\"\r\n          v-for=\"item in toolData\"\r\n          :key=\"item.chatToolCode\"\r\n          @click=\"handleTool(item)\">\r\n          <div class=\"AssistedWritingIcon\" v-html=\"toolIconData[item.chatToolCode]\"></div>\r\n          <div class=\"AssistedWritingName\">{{ item.chatToolName }}</div>\r\n        </div>\r\n        <div class=\"AssistedWritingPlaceholder\" v-for=\"item in 2\" :key=\"item + '_placeholder'\"></div>\r\n      </div>\r\n      <div class=\"AssistedWritingEditorBody\">\r\n        <GlobalAiChatFile\r\n          :fileList=\"fileList\"\r\n          :fileData=\"fileData\"\r\n          @close=\"handleClose\"\r\n          v-show=\"fileList.length || fileData.length\" />\r\n        <GlobalAiChatEditor\r\n          ref=\"editorRef\"\r\n          v-model=\"sendContent\"\r\n          @send=\"handleSendMessage\"\r\n          @uploadCallback=\"handleFileUpload\"\r\n          @fileCallback=\"handleFileCallback\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AssistedWriting' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, inject, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue'\r\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue'\r\nconst store = useStore()\r\nconst proposalIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#230;&#148;&#191;&#229;&#141;&#143;&#230;&#143;&#144;&#230;&#161;&#136;\" clip-path=\"url(#clip0_40_267)\"><g id=\"&#230;&#142;&#140;&#228;&#184;&#138;&#230;&#143;&#144;&#230;&#161;&#136;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6276\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_40_267)\"/><g id=\"&#231;&#187;&#132; 2742\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6277\" d=\"M35.4666 8.2666H12.5332C11.355 8.2666 10.3999 9.22173 10.3999 10.3999V37.5999C10.3999 38.7781 11.355 39.7333 12.5332 39.7333H35.4666C36.6448 39.7333 37.5999 38.7781 37.5999 37.5999V10.3999C37.5999 9.22173 36.6448 8.2666 35.4666 8.2666Z\" fill=\"white\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 342 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 13.8664C14.1333 13.4246 14.4915 13.0664 14.9333 13.0664H29.8666C30.3085 13.0664 30.6666 13.4246 30.6666 13.8664C30.6666 14.3082 30.3085 14.6664 29.8666 14.6664H14.9333C14.4915 14.6664 14.1333 14.3082 14.1333 13.8664Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 19.8664C14.1333 19.4246 14.4915 19.0664 14.9333 19.0664H22.9333C23.3751 19.0664 23.7333 19.4246 23.7333 19.8664C23.7333 20.3082 23.3751 20.6664 22.9333 20.6664H14.9333C14.4915 20.6664 14.1333 20.3082 14.1333 19.8664Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 344 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14.1333 25.8664C14.1333 25.4246 14.4915 25.0664 14.9333 25.0664H25.6C26.0418 25.0664 26.4 25.4246 26.4 25.8664C26.4 26.3082 26.0418 26.6664 25.6 26.6664H14.9333C14.4915 26.6664 14.1333 26.3082 14.1333 25.8664Z\" fill=\"#0964E3\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 797\" d=\"M28.9067 36.2952L36.2805 25.764C36.3438 25.674 36.3887 25.5724 36.4127 25.4649C36.4367 25.3575 36.4392 25.2464 36.4203 25.138C36.4013 25.0296 36.3612 24.9259 36.3021 24.833C36.2431 24.7401 36.1664 24.6598 36.0763 24.5966L33.9179 23.0851C33.8277 23.022 33.726 22.9773 33.6186 22.9535C33.5112 22.9297 33.4001 22.9272 33.2918 22.9463C33.1834 22.9654 33.0799 23.0057 32.9871 23.0649C32.8943 23.124 32.814 23.2008 32.7509 23.291L25.376 33.8222C25.3014 33.9287 25.2528 34.0513 25.2341 34.18L28.6213 36.5523C28.7352 36.4901 28.8329 36.402 28.9067 36.2952Z\" fill=\"url(#paint1_linear_40_267)\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 798\" d=\"M28.6218 36.5514L25.5604 37.7685C25.5189 37.7849 25.4746 37.793 25.43 37.7923C25.3854 37.7916 25.3413 37.7821 25.3003 37.7644C25.2594 37.7467 25.2223 37.7211 25.1913 37.689C25.1602 37.6569 25.1358 37.619 25.1194 37.5775C25.1023 37.5334 25.0943 37.4862 25.0959 37.4389L25.2378 34.1855L28.6218 36.5514Z\" fill=\"url(#paint2_linear_40_267)\"/></g></g></g><defs><linearGradient id=\"paint0_linear_40_267\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_40_267\" x1=\"30.8335\" y1=\"22.9336\" x2=\"30.8335\" y2=\"36.5523\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><linearGradient id=\"paint2_linear_40_267\" x1=\"26.8587\" y1=\"34.1855\" x2=\"26.8587\" y2=\"37.7924\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F1F7FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_40_267\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>'\r\nconst socialIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#231;&#164;&#190;&#230;&#131;&#133;&#230;&#176;&#145;&#230;&#132;&#143;\" clip-path=\"url(#clip0_40_278)\"><g id=\"&#231;&#164;&#190;&#230;&#131;&#133;&#230;&#176;&#145;&#230;&#132;&#143;_2\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6278\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_40_278)\"/><g id=\"&#231;&#187;&#132; 2744\"><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1794\" d=\"M36.9018 40.0001H11.0981C10.3701 40.0001 9.67185 39.7109 9.15705 39.1961C8.64225 38.6813 8.35303 37.9831 8.35303 37.2551V27.3351L24 20.7041L39.6469 27.3324V37.2524C39.6473 37.6131 39.5765 37.9704 39.4387 38.3037C39.3009 38.6371 39.0988 38.94 38.8438 39.1952C38.5889 39.4504 38.2862 39.6528 37.9529 39.7909C37.6197 39.9291 37.2625 40.0001 36.9018 40.0001Z\" fill=\"#BBD7FE\"/><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6279\" d=\"M34.241 8H13.7588C12.5806 8 11.6255 8.95513 11.6255 10.1333V34.4976C11.6255 35.6758 12.5806 36.6309 13.7588 36.6309H34.241C35.4192 36.6309 36.3743 35.6758 36.3743 34.4976V10.1333C36.3743 8.95513 35.4192 8 34.241 8Z\" fill=\"white\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 345 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 19.2219C15.5129 18.78 15.8711 18.4219 16.3129 18.4219H29.8121C30.254 18.4219 30.6121 18.78 30.6121 19.2219C30.6121 19.6637 30.254 20.0219 29.8121 20.0219H16.3129C15.8711 20.0219 15.5129 19.6637 15.5129 19.2219Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 346 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 24.5651C15.5129 24.1233 15.8711 23.7651 16.3129 23.7651H25.3124C25.7542 23.7651 26.1124 24.1233 26.1124 24.5651C26.1124 25.007 25.7542 25.3651 25.3124 25.3651H16.3129C15.8711 25.3651 15.5129 25.007 15.5129 24.5651Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 347 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.5129 13.8786C15.5129 13.4368 15.8711 13.0786 16.3129 13.0786H29.2495C29.6913 13.0786 30.0495 13.4368 30.0495 13.8786C30.0495 14.3204 29.6913 14.6786 29.2495 14.6786H16.3129C15.8711 14.6786 15.5129 14.3204 15.5129 13.8786Z\" fill=\"#0964E3\"/><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1795\" d=\"M36.9018 40.0002H11.0981C10.3701 40.0002 9.67185 39.711 9.15705 39.1962C8.64225 38.6814 8.35303 37.9832 8.35303 37.2552V27.3352L24 30.764L39.6469 27.3325V37.2525C39.6473 37.6132 39.5765 37.9705 39.4387 38.3038C39.3009 38.6372 39.0988 38.9401 38.8438 39.1953C38.5889 39.4505 38.2862 39.6529 37.9529 39.791C37.6197 39.9292 37.2625 40.0002 36.9018 40.0002Z\" fill=\"url(#paint1_linear_40_278)\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 348 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M20.3877 35.5334C20.3877 35.0916 20.7459 34.7334 21.1877 34.7334H26.8122C27.2541 34.7334 27.6122 35.0916 27.6122 35.5334C27.6122 35.9752 27.2541 36.3334 26.8122 36.3334H21.1877C20.7459 36.3334 20.3877 35.9752 20.3877 35.5334Z\" fill=\"white\"/></g></g></g><defs><linearGradient id=\"paint0_linear_40_278\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_40_278\" x1=\"24\" y1=\"27.3325\" x2=\"24\" y2=\"40.0002\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_40_278\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>'\r\n\r\nconst openPage = inject('openPage')\r\nconst toolId = ref('')\r\nconst toolInfo = ref({})\r\nconst toolData = ref([])\r\nconst toolIconData = { proposal: proposalIcon, social: socialIcon }\r\n\r\nconst editorRef = ref()\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst sendContent = ref('')\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleTips = (text) => {\r\n  const parts = text.split(/(\\{[^}]+\\})/)\r\n  const result = parts\r\n    .map((part) => {\r\n      if (part.startsWith('{') && part.endsWith('}')) {\r\n        return { value: part.slice(1, -1), type: true }\r\n      } else if (part.trim() !== '') {\r\n        return { value: part, type: false }\r\n      }\r\n    })\r\n    .filter((item) => item !== undefined)\r\n  return result\r\n}\r\nconst handleTool = (item) => {\r\n  toolInfo.value = item\r\n  toolId.value = item.chatToolCode\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n  nextTick(() => {\r\n    editorRef.value?.handleInsertPlaceholder(handleTips(item.userPromptTip))\r\n  })\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!toolId.value) return ElMessage({ type: 'warning', message: '请先选择文档撰写类型！' })\r\n  const openAiParams = {\r\n    toolId: toolInfo.value.id,\r\n    toolCode: toolId.value,\r\n    toolContent: value,\r\n    fileData: fileData.value\r\n  }\r\n  sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams))\r\n  if (toolId.value === 'proposal') openPage({ key: 'routePath', value: '/proposal/SubmitSuggest?utype=1' })\r\n  if (toolId.value === 'social') openPage({ key: 'routePath', value: '/publicOpinion/PublicOpinionNew' })\r\n  editorRef.value?.handleSetFile([])\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const { data } = await api.aigptChatSceneDetail({ query: { chatSceneCode: 'ai-assisted-writing-chat' } })\r\n  toolData.value = data?.tools?.filter((v) => v.isUsing) || []\r\n}\r\nonActivated(() => {\r\n  aigptChatSceneDetail()\r\n  store.commit('setAiChatElShow', false)\r\n})\r\nonDeactivated(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\nonUnmounted(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.AssistedWriting {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  position: relative;\r\n\r\n  .AssistedWritingBody {\r\n    padding: var(--zy-distance-two);\r\n  }\r\n\r\n  .AssistedWritingUserBody {\r\n    width: 800px;\r\n    border-radius: 6px 6px 6px 6px;\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .AssistedWritingUser {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: var(--zy-distance-two);\r\n\r\n      .zy-el-image {\r\n        width: 62px;\r\n        height: 62px;\r\n        border-radius: 50%;\r\n      }\r\n\r\n      .AssistedWritingUserInfo {\r\n        width: calc(100% - 62px);\r\n        height: 58px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        padding-left: var(--zy-distance-two);\r\n\r\n        .AssistedWritingUserName {\r\n          font-weight: bold;\r\n          line-height: var(--zy-line-height);\r\n          font-size: calc(var(--zy-name-font-size) + 2px);\r\n        }\r\n\r\n        .AssistedWritingUserText {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-name-font-size);\r\n          color: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .AssistedWritingTitle {\r\n    width: 100%;\r\n    font-weight: bold;\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-name-font-size);\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .AssistedWritingList {\r\n    width: 800px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .AssistedWritingPlaceholder {\r\n      width: 185px;\r\n      height: 126px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .AssistedWritingItem {\r\n      width: 185px;\r\n      height: 126px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      border-radius: 6px 6px 6px 6px;\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n      padding: 20px;\r\n      margin-bottom: 20px;\r\n      cursor: pointer;\r\n\r\n      &.is-active {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AssistedWritingName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AssistedWritingName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .AssistedWritingIcon {\r\n        width: 48px;\r\n        height: 48px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n\r\n      .AssistedWritingName {\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n    }\r\n  }\r\n\r\n  .AssistedWritingEditorBody {\r\n    width: 800px;\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: 20px;\r\n    transform: translateX(-50%);\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalAiChatEditorContainer {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAyB;;EAC/BA,KAAK,EAAC;AAAyB;;EAKrCA,KAAK,EAAC;AAAqB;iBAbtC;kBAAA;;EAqBeA,KAAK,EAAC;AAAqB;;EAI/BA,KAAK,EAAC;AAA2B;;;uBAxB1CC,mBAAA,CAsCM,OAtCNC,UAsCM,GArCJC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJD,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAMM,OANNG,UAMM,GALJC,YAAA,CAA0CC,mBAAA;IAA/BC,GAAG,EAAEC,MAAA,CAAAC,IAAI,CAACC,KAAK;IAAEC,GAAG,EAAC;oCAChCV,mBAAA,CAGM,OAHNW,UAGM,GAFJX,mBAAA,CAA8D,OAA9DY,UAA8D,EAAAC,gBAAA,CAAtBN,MAAA,CAAAC,IAAI,CAACM,QAAQ,kBACrDd,mBAAA,CAA8D,OAA9De,UAA8D,EAAAF,gBAAA,CAAtBN,MAAA,CAAAC,IAAI,CAACQ,QAAQ,iB,iCAI3DhB,mBAAA,CAA8C;IAAzCH,KAAK,EAAC;EAAsB,GAAC,QAAM,sBACxCG,mBAAA,CAWM,OAXNiB,UAWM,I,kBAVJnB,mBAAA,CAQMoB,SAAA,QAtBdC,WAAA,CAiByBZ,MAAA,CAAAa,QAAQ,EAjBjC,UAiBiBC,IAAI;yBAHbvB,mBAAA,CAQM;MAPJD,KAAK,EAffyB,eAAA,EAegB,qBAAqB;QAAA,aACJf,MAAA,CAAAgB,MAAM,KAAKF,IAAI,CAACG;MAAY;MAElDC,GAAG,EAAEJ,IAAI,CAACG,YAAY;MACtBE,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAEpB,MAAA,CAAAqB,UAAU,CAACP,IAAI;MAAA;QACvBrB,mBAAA,CAAgF;MAA3EH,KAAK,EAAC,qBAAqB;MAACgC,SAAwC,EAAhCtB,MAAA,CAAAuB,YAAY,CAACT,IAAI,CAACG,YAAY;4BApBjFO,WAAA,GAqBU/B,mBAAA,CAA8D,OAA9DgC,WAA8D,EAAAnB,gBAAA,CAA1BQ,IAAI,CAACY,YAAY,iB,yBArB/DC,UAAA;iDAuBQpC,mBAAA,CAA6FoB,SAAA,QAvBrGC,WAAA,CAuB+D,CAAC,EAvBhE,UAuBuDE,IAAI;WAAnDrB,mBAAA,CAA6F;MAAxFH,KAAK,EAAC,4BAA4B;MAAoB4B,GAAG,EAAEJ,IAAI;;oCAEtErB,mBAAA,CAYM,OAZNmC,WAYM,G,gBAXJ/B,YAAA,CAIgDG,MAAA;IAH7C6B,QAAQ,EAAE7B,MAAA,CAAA6B,QAAQ;IAClBC,QAAQ,EAAE9B,MAAA,CAAA8B,QAAQ;IAClBC,OAAK,EAAE/B,MAAA,CAAAgC;+DACAhC,MAAA,CAAA6B,QAAQ,CAACI,MAAM,IAAIjC,MAAA,CAAA8B,QAAQ,CAACG,MAAM,E,GAC5CpC,YAAA,CAKuCG,MAAA;IAJrCkC,GAAG,EAAC,WAAW;IAhCzBC,UAAA,EAiCmBnC,MAAA,CAAAoC,WAAW;IAjC9B,uBAAAC,MAAA,QAAAA,MAAA,gBAAAjB,MAAA;MAAA,OAiCmBpB,MAAA,CAAAoC,WAAW,GAAAhB,MAAA;IAAA;IACnBkB,MAAI,EAAEtC,MAAA,CAAAuC,iBAAiB;IACvBC,gBAAc,EAAExC,MAAA,CAAAyC,gBAAgB;IAChCC,cAAY,EAAE1C,MAAA,CAAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}