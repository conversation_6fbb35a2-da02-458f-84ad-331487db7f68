"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[6358],{36358:function(e,t,n){n.r(t),n.d(t,{default:function(){return u}});var a=n(74061),o={name:"SettingPopupWindow"},i=Object.assign(o,{props:{modelValue:{type:Boolean,default:!1},beforeClose:Function},emits:["update:modelValue"],setup(e,t){var n,o=t.emit,i=e,l=o,u=null===(n=window.electron)||void 0===n?void 0:n.isMac,c=(0,a.ref)(i.modelValue),r=(0,a.ref)(!1),s=(0,a.ref)(!1);(0,a.watch)((function(){return i.modelValue}),(function(){c.value=i.modelValue,i.modelValue?(r.value=!0,(0,a.nextTick)((function(){s.value=!0}))):(s.value=!1,setTimeout((function(){r.value=!1}),99))}));var d=function(){"function"===typeof i.beforeClose?i.beforeClose((function(){l("update:modelValue",!1)})):l("update:modelValue",!1)};return function(e,t){return r.value?((0,a.openBlock)(),(0,a.createElementBlock)("div",{key:0,class:(0,a.normalizeClass)(["setting-popup-window",{"setting-popup-window-mac":(0,a.unref)(u)}]),onClick:(0,a.withModifiers)(d,["stop"])},[(0,a.createVNode)(a.Transition,{"enter-active-class":"animate__animated animate__fadeInRight animate__faster","leave-active-class":"animate__animated animate__fadeOutRight animate__faster"},{default:(0,a.withCtx)((function(){return[s.value?((0,a.openBlock)(),(0,a.createElementBlock)("div",{key:0,class:"setting-popup-window-body forbidSelect",onClick:t[0]||(t[0]=(0,a.withModifiers)((function(){}),["stop"]))},[(0,a.renderSlot)(e.$slots,"default")])):(0,a.createCommentVNode)("",!0)]})),_:3})],2)):(0,a.createCommentVNode)("",!0)}}});const l=i;var u=l}}]);