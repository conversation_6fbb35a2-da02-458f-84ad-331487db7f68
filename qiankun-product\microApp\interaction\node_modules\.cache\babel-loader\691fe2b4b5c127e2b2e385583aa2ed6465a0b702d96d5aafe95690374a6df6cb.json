{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, Fragment as _Fragment, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, renderList as _renderList, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, vShow as _vShow, Transition as _Transition } from \"vue\";\nimport _imports_0 from '../../assets/img/icon_live_time.png';\nimport _imports_1 from '../../assets/img/icon_live_send.png';\nvar _hoisted_1 = {\n  class: \"LiveBroadcastDetails\"\n};\nvar _hoisted_2 = {\n  class: \"LiveBroadcastDetailsHeader\"\n};\nvar _hoisted_3 = [\"src\"];\nvar _hoisted_4 = {\n  class: \"LiveBroadcastDetailsHeadInfo\"\n};\nvar _hoisted_5 = {\n  class: \"LiveBroadcastDetailsTitle\"\n};\nvar _hoisted_6 = {\n  class: \"LiveBroadcastDetailsTime\"\n};\nvar _hoisted_7 = {\n  class: \"LiveBroadcastDetailsBody\"\n};\nvar _hoisted_8 = {\n  class: \"LiveBroadcastDetailsCanvas\"\n};\nvar _hoisted_9 = {\n  key: 0,\n  class: \"LiveBroadcastDetailsPoster\"\n};\nvar _hoisted_10 = [\"src\"];\nvar _hoisted_11 = {\n  class: \"LiveBroadcastDetailsLiveOverlay\"\n};\nvar _hoisted_12 = {\n  class: \"LiveBroadcastDetailsEnded\"\n};\nvar _hoisted_13 = [\"src\"];\nvar _hoisted_14 = {\n  class: \"LiveBroadcastDetailsEnded\"\n};\nvar _hoisted_15 = [\"src\"];\nvar _hoisted_16 = {\n  class: \"LiveBroadcastDetailsEndedWrap\"\n};\nvar _hoisted_17 = {\n  class: \"LiveBroadcastDetailsSidebar\"\n};\nvar _hoisted_18 = {\n  class: \"LiveBroadcastDetailsTabs\"\n};\nvar _hoisted_19 = {\n  key: 0,\n  class: \"LiveBroadcastDetailsTabPane\"\n};\nvar _hoisted_20 = {\n  class: \"detailsTitle\"\n};\nvar _hoisted_21 = {\n  class: \"detailsTimeBox\"\n};\nvar _hoisted_22 = {\n  class: \"detailsTime\"\n};\nvar _hoisted_23 = {\n  class: \"detailsDesc\"\n};\nvar _hoisted_24 = {\n  key: 1,\n  class: \"LiveBroadcastDetailsTabPane interact-pane\"\n};\nvar _hoisted_25 = {\n  class: \"comments-container\",\n  ref: \"commentsContainer\"\n};\nvar _hoisted_26 = {\n  class: \"comment-main\"\n};\nvar _hoisted_27 = {\n  class: \"comment-content\"\n};\nvar _hoisted_28 = {\n  class: \"comment-header\"\n};\nvar _hoisted_29 = {\n  class: \"comment-nickname\"\n};\nvar _hoisted_30 = {\n  class: \"comment-time\"\n};\nvar _hoisted_31 = {\n  class: \"comment-text\"\n};\nvar _hoisted_32 = {\n  class: \"comment-actions\"\n};\nvar _hoisted_33 = [\"onClick\"];\nvar _hoisted_34 = [\"onClick\"];\nvar _hoisted_35 = [\"src\"];\nvar _hoisted_36 = {\n  class: \"like-count\"\n};\nvar _hoisted_37 = {\n  key: 0,\n  class: \"replies-container\"\n};\nvar _hoisted_38 = {\n  class: \"comment-content\"\n};\nvar _hoisted_39 = {\n  class: \"comment-header\"\n};\nvar _hoisted_40 = {\n  class: \"comment-nickname\"\n};\nvar _hoisted_41 = {\n  key: 0,\n  class: \"reply-to\"\n};\nvar _hoisted_42 = {\n  class: \"comment-time\"\n};\nvar _hoisted_43 = {\n  class: \"comment-text\"\n};\nvar _hoisted_44 = {\n  class: \"comment-actions\"\n};\nvar _hoisted_45 = [\"onClick\"];\nvar _hoisted_46 = [\"src\"];\nvar _hoisted_47 = {\n  class: \"like-count\"\n};\nvar _hoisted_48 = {\n  key: 1,\n  class: \"reply-input-container\"\n};\nvar _hoisted_49 = {\n  class: \"reply-input-wrapper\"\n};\nvar _hoisted_50 = [\"placeholder\", \"onKeyup\"];\nvar _hoisted_51 = {\n  class: \"reply-actions\"\n};\nvar _hoisted_52 = [\"onClick\", \"disabled\"];\nvar _hoisted_53 = {\n  key: 0,\n  class: \"comments-empty\"\n};\nvar _hoisted_54 = {\n  class: \"comment-input-area\"\n};\nvar _hoisted_55 = {\n  class: \"input-wrapper\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createBlock(_Transition, {\n    name: \"details-fade\",\n    persisted: \"\"\n  }, {\n    default: _withCtx(function () {\n      var _$setup$details, _$setup$details2, _$setup$details3;\n      return [_withDirectives(_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [$setup.logoSrc ? (_openBlock(), _createElementBlock(\"img\", {\n        key: 0,\n        class: \"LiveBroadcastDetailsEmblem\",\n        src: $setup.logoSrc,\n        alt: \"emblem\"\n      }, null, 8 /* PROPS */, _hoisted_3)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.details.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, \"直播时间：\" + _toDisplayString($setup.format($setup.details.startTime)) + \" 到 \" + _toDisplayString($setup.format($setup.details.endTime)), 1 /* TEXT */)]), _createVNode(_component_el_icon, {\n        class: \"LiveBroadcastDetailsClose\",\n        onClick: $setup.handleClose\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"Close\"])];\n        }),\n        _: 1 /* STABLE */\n      })]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" 未开始：显示封面图 + 倒计时 \"), $setup.details.meetingStatus === '未开始' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createElementVNode(\"img\", {\n        src: $setup.imgUrl,\n        alt: \"海报/播放画面区域\",\n        style: {\n          \"width\": \"100%\",\n          \"height\": \"100%\",\n          \"object-fit\": \"contain\"\n        }\n      }, null, 8 /* PROPS */, _hoisted_10), _createVNode($setup[\"CountdownTimer\"], {\n        \"start-time\": $setup.details.startTime,\n        onCountdownEnd: $setup.handleCountdownEnd,\n        ref: \"countdownTimerRef\"\n      }, null, 8 /* PROPS */, [\"start-time\"])])) : $setup.details.meetingStatus === '进行中' && $setup.details.liveUrl ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 进行中且有推流地址：直播播放器 \"), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(), _createBlock($setup[\"VideoPlayer\"], {\n        key: `video-player-live-${$setup.props.id}-${$setup.details.meetingStatus}`,\n        \"live-url\": $setup.details.liveUrl,\n        \"replay-url\": $setup.details.liveReplayUrl,\n        \"is-replay\": false,\n        ref: \"videoPlayerRef\"\n      }, null, 8 /* PROPS */, [\"live-url\", \"replay-url\"]))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.details.meetingStatus === '无推流' || $setup.details.meetingStatus === '进行中' && !$setup.details.liveUrl ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 2\n      }, [_createCommentVNode(\" 无推流：倒计时结束但没有推流地址 或 进行中但没有推流地址 \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"img\", {\n        src: $setup.imgUrl,\n        alt: \"直播背景\",\n        class: \"LiveBroadcastDetailsEndedBg\"\n      }, null, 8 /* PROPS */, _hoisted_13), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"LiveBroadcastDetailsEndedWrap\"\n      }, [_createElementVNode(\"div\", {\n        class: \"endedTitle\"\n      }, \"暂无配置直播地址\")], -1 /* HOISTED */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.details.meetingStatus === '已结束' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 3\n      }, [_createCommentVNode(\" 已结束：回放播放器或背景图 + 悬浮内容 \"), _createElementVNode(\"div\", _hoisted_14, [_createCommentVNode(\" 回放模式：显示回放播放器 \"), $setup.isReplayMode && $setup.details.liveReplayUrl ? (_openBlock(), _createBlock($setup[\"VideoPlayer\"], {\n        key: `video-player-replay-${$setup.props.id}-${$setup.details.meetingStatus}`,\n        \"live-url\": $setup.details.liveUrl,\n        \"replay-url\": $setup.details.liveReplayUrl,\n        \"is-replay\": true,\n        ref: \"videoPlayerRef\"\n      }, null, 8 /* PROPS */, [\"live-url\", \"replay-url\"])) : (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 非回放模式：显示背景图和按钮 \"), _createCommentVNode(\" 背景图 \"), _createElementVNode(\"img\", {\n        src: $setup.imgUrl,\n        alt: \"直播背景\",\n        class: \"LiveBroadcastDetailsEndedBg\"\n      }, null, 8 /* PROPS */, _hoisted_15), _createCommentVNode(\" 悬浮的结束状态内容 \"), _createElementVNode(\"div\", _hoisted_16, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"endedTitle\"\n      }, \"直播已结束\", -1 /* HOISTED */)), _createCommentVNode(\" 有回放地址：显示观看回放按钮 \"), $setup.details.isReplay == 1 ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 0,\n        type: \"primary\",\n        class: \"replayBtn\",\n        onClick: $setup.handleReplay\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\" 观看回放 \")]);\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)])], 64 /* STABLE_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", {\n        class: _normalizeClass(['LiveBroadcastDetailsTab', {\n          active: $setup.activeTab === 'details'\n        }]),\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.activeTab = 'details';\n        })\n      }, \" 直播详情\", 2 /* CLASS */), _createElementVNode(\"div\", {\n        class: _normalizeClass(['LiveBroadcastDetailsTab', {\n          active: $setup.activeTab === 'interact'\n        }]),\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.activeTab = 'interact';\n        })\n      }, \" 互动\", 2 /* CLASS */)]), $setup.activeTab === 'details' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, _toDisplayString(((_$setup$details = $setup.details) === null || _$setup$details === void 0 ? void 0 : _$setup$details.theme) || '-'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_21, [_cache[7] || (_cache[7] = _createElementVNode(\"img\", {\n        src: _imports_0,\n        alt: \"\",\n        class: \"detailsTimeIcon\"\n      }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_22, \"时间：\" + _toDisplayString($setup.format((_$setup$details2 = $setup.details) === null || _$setup$details2 === void 0 ? void 0 : _$setup$details2.startTime)) + \" - \" + _toDisplayString($setup.format((_$setup$details3 = $setup.details) === null || _$setup$details3 === void 0 ? void 0 : _$setup$details3.endTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_23, _toDisplayString($setup.details.liveDescribes || '暂无简介'), 1 /* TEXT */)])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_createCommentVNode(\" 评论列表 \"), _createElementVNode(\"div\", _hoisted_25, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.comments, function (comment) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: comment.id,\n          class: \"comment-item\"\n        }, [_createCommentVNode(\" 主评论 \"), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"span\", _hoisted_29, _toDisplayString(comment.commentUserName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_30, _toDisplayString($setup.format(comment.createDate)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_31, _toDisplayString(comment.commentContent), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"span\", {\n          class: \"action-item\",\n          onClick: function onClick($event) {\n            return $setup.showReplyInput(comment.id);\n          }\n        }, \"跟帖互动\", 8 /* PROPS */, _hoisted_33), _createElementVNode(\"span\", {\n          class: _normalizeClass([\"action-item like-btn\", {\n            liked: comment.hasClickPraises\n          }]),\n          onClick: function onClick($event) {\n            return $setup.toggleLike(comment);\n          }\n        }, [_createElementVNode(\"img\", {\n          src: comment.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png'),\n          alt: \"点赞\",\n          class: \"like-icon\"\n        }, null, 8 /* PROPS */, _hoisted_35), _createElementVNode(\"span\", _hoisted_36, \"(\" + _toDisplayString(comment.praisesCount || 0) + \")\", 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_34)])])]), _createCommentVNode(\" 回复列表 \"), comment.replies && comment.replies.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_37, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(comment.replies, function (reply) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: reply.id,\n            class: \"reply-item\"\n          }, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"span\", _hoisted_40, _toDisplayString(reply.nickname), 1 /* TEXT */), reply.replyTo ? (_openBlock(), _createElementBlock(\"span\", _hoisted_41, \"回复 @\" + _toDisplayString(reply.replyTo), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", _hoisted_42, _toDisplayString($setup.format(reply.time)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_43, _toDisplayString(reply.content), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"span\", {\n            class: _normalizeClass([\"action-item like-btn\", {\n              liked: reply.hasClickPraises\n            }]),\n            onClick: function onClick($event) {\n              return $setup.toggleLike(reply);\n            }\n          }, [_createElementVNode(\"img\", {\n            src: reply.hasClickPraises ? require('../../assets/img/fabulous.png') : require('../../assets/img/fabulous_o.png'),\n            alt: \"点赞\",\n            class: \"like-icon\"\n          }, null, 8 /* PROPS */, _hoisted_46), _createElementVNode(\"span\", _hoisted_47, \"(\" + _toDisplayString(reply.praisesCount || 0) + \")\", 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_45)])])]);\n        }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 回复输入框 \"), $setup.replyingTo === comment.id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_48, [_createElementVNode(\"div\", _hoisted_49, [_withDirectives(_createElementVNode(\"input\", {\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n            return $setup.replyContent = $event;\n          }),\n          type: \"text\",\n          placeholder: $setup.replyToUser ? `回复 @${$setup.replyToUser}:` : '写下你的回复...',\n          class: \"reply-input\",\n          onKeyup: _withKeys(function ($event) {\n            return $setup.submitReply(comment.id);\n          }, [\"enter\"]),\n          maxlength: \"200\",\n          ref_for: true,\n          ref: \"replyInputRef\"\n        }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_50), [[_vModelText, $setup.replyContent]]), _createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"button\", {\n          class: \"cancel-btn\",\n          onClick: $setup.cancelReply\n        }, \"取消\"), _createElementVNode(\"button\", {\n          class: \"submit-btn\",\n          onClick: function onClick($event) {\n            return $setup.submitReply(comment.id);\n          },\n          disabled: !$setup.replyContent.trim()\n        }, \" 回复 \", 8 /* PROPS */, _hoisted_52)])])])) : _createCommentVNode(\"v-if\", true)]);\n      }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 空状态 \"), $setup.comments.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_53, _cache[8] || (_cache[8] = [_createElementVNode(\"div\", {\n        class: \"empty-text\"\n      }, \"暂无评论\", -1 /* HOISTED */)]))) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), _createCommentVNode(\" 发表评论输入框 \"), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"div\", _hoisted_55, [_withDirectives(_createElementVNode(\"textarea\", {\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.newComment = $event;\n        }),\n        placeholder: \"说点什么...\",\n        class: \"comment-input\",\n        onKeyup: _withKeys($setup.submitComment, [\"enter\"]),\n        maxlength: \"200\",\n        rows: \"3\",\n        style: {\n          \"width\": \"100%\",\n          \"height\": \"100%\"\n        }\n      }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $setup.newComment]]), _createElementVNode(\"div\", {\n        class: \"submit-comment-btn\",\n        onClick: $setup.submitComment\n      }, _cache[9] || (_cache[9] = [_createElementVNode(\"div\", {\n        class: \"comment-btn\"\n      }, \"发送\", -1 /* HOISTED */), _createElementVNode(\"img\", {\n        src: _imports_1,\n        alt: \"发送\",\n        class: \"send-icon\"\n      }, null, -1 /* HOISTED */)]))])])]))])])], 512 /* NEED_PATCH */), [[_vShow, $props.modelValue]])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "class", "key", "ref", "_createBlock", "_Transition", "name", "persisted", "default", "_withCtx", "_$setup$details", "_$setup$details2", "_$setup$details3", "_createElementVNode", "_hoisted_1", "_hoisted_2", "$setup", "logoSrc", "_createElementBlock", "src", "alt", "_hoisted_3", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "_toDisplayString", "details", "theme", "_hoisted_6", "format", "startTime", "endTime", "_createVNode", "_component_el_icon", "onClick", "handleClose", "_", "_hoisted_7", "_hoisted_8", "meetingStatus", "_hoisted_9", "imgUrl", "style", "_hoisted_10", "onCountdownEnd", "handleCountdownEnd", "liveUrl", "_Fragment", "_hoisted_11", "props", "id", "liveReplayUrl", "_hoisted_12", "_hoisted_13", "_hoisted_14", "isReplayMode", "_hoisted_15", "_hoisted_16", "isReplay", "_component_el_button", "type", "handleReplay", "_cache", "_createTextVNode", "_hoisted_17", "_hoisted_18", "_normalizeClass", "active", "activeTab", "$event", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "liveDescribes", "_hoisted_24", "_hoisted_25", "_renderList", "comments", "comment", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "commentUserName", "_hoisted_30", "createDate", "_hoisted_31", "commentContent", "_hoisted_32", "showReplyInput", "_hoisted_33", "liked", "hasClickPraises", "toggleLike", "require", "_hoisted_35", "_hoisted_36", "praisesCount", "_hoisted_34", "replies", "length", "_hoisted_37", "reply", "_hoisted_38", "_hoisted_39", "_hoisted_40", "nickname", "replyTo", "_hoisted_41", "_hoisted_42", "time", "_hoisted_43", "content", "_hoisted_44", "_hoisted_46", "_hoisted_47", "_hoisted_45", "replyingTo", "_hoisted_48", "_hoisted_49", "replyContent", "placeholder", "replyToUser", "onKeyup", "_with<PERSON><PERSON><PERSON>", "submitReply", "maxlength", "ref_for", "_hoisted_50", "_hoisted_51", "cancelReply", "disabled", "trim", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "newComment", "submitComment", "rows", "$props", "modelValue"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveBroadcastDetails.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"details-fade\">\r\n    <div v-show=\"modelValue\" class=\"LiveBroadcastDetails\">\r\n      <div class=\"LiveBroadcastDetailsHeader\">\r\n        <img v-if=\"logoSrc\" class=\"LiveBroadcastDetailsEmblem\" :src=\"logoSrc\" alt=\"emblem\" />\r\n        <div class=\"LiveBroadcastDetailsHeadInfo\">\r\n          <div class=\"LiveBroadcastDetailsTitle\">{{ details.theme }}</div>\r\n          <div class=\"LiveBroadcastDetailsTime\">直播时间：{{ format(details.startTime) }} 到 {{\r\n            format(details.endTime) }}</div>\r\n        </div>\r\n        <el-icon class=\"LiveBroadcastDetailsClose\" @click=\"handleClose\">\r\n          <Close />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"LiveBroadcastDetailsBody\">\r\n        <div class=\"LiveBroadcastDetailsCanvas\">\r\n          <!-- 未开始：显示封面图 + 倒计时 -->\r\n          <div v-if=\"details.meetingStatus === '未开始'\" class=\"LiveBroadcastDetailsPoster\">\r\n            <img :src=\"imgUrl\" alt=\"海报/播放画面区域\" style=\"width: 100%;height: 100%;object-fit: contain;\">\r\n            <CountdownTimer :start-time=\"details.startTime\" @countdown-end=\"handleCountdownEnd\"\r\n              ref=\"countdownTimerRef\" />\r\n          </div>\r\n          <!-- 进行中且有推流地址：直播播放器 -->\r\n          <div v-else-if=\"details.meetingStatus === '进行中' && details.liveUrl\" class=\"LiveBroadcastDetailsLiveOverlay\">\r\n            <VideoPlayer :key=\"`video-player-live-${props.id}-${details.meetingStatus}`\" :live-url=\"details.liveUrl\"\r\n              :replay-url=\"details.liveReplayUrl\" :is-replay=\"false\" ref=\"videoPlayerRef\" />\r\n          </div>\r\n          <!-- 无推流：倒计时结束但没有推流地址 或 进行中但没有推流地址 -->\r\n          <div v-else-if=\"details.meetingStatus === '无推流' || (details.meetingStatus === '进行中' && !details.liveUrl)\"\r\n            class=\"LiveBroadcastDetailsEnded\">\r\n            <img :src=\"imgUrl\" alt=\"直播背景\" class=\"LiveBroadcastDetailsEndedBg\">\r\n            <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n              <div class=\"endedTitle\">暂无配置直播地址</div>\r\n            </div>\r\n          </div>\r\n          <!-- 已结束：回放播放器或背景图 + 悬浮内容 -->\r\n          <div v-else-if=\"details.meetingStatus === '已结束'\" class=\"LiveBroadcastDetailsEnded\">\r\n            <!-- 回放模式：显示回放播放器 -->\r\n            <VideoPlayer v-if=\"isReplayMode && details.liveReplayUrl\"\r\n              :key=\"`video-player-replay-${props.id}-${details.meetingStatus}`\" :live-url=\"details.liveUrl\"\r\n              :replay-url=\"details.liveReplayUrl\" :is-replay=\"true\" ref=\"videoPlayerRef\" />\r\n            <!-- 非回放模式：显示背景图和按钮 -->\r\n            <template v-else>\r\n              <!-- 背景图 -->\r\n              <img :src=\"imgUrl\" alt=\"直播背景\" class=\"LiveBroadcastDetailsEndedBg\">\r\n              <!-- 悬浮的结束状态内容 -->\r\n              <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n                <div class=\"endedTitle\">直播已结束</div>\r\n                <!-- 有回放地址：显示观看回放按钮 -->\r\n                <el-button type=\"primary\" class=\"replayBtn\" v-if=\"details.isReplay == 1\" @click=\"handleReplay\">\r\n                  观看回放\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </div>\r\n        <div class=\"LiveBroadcastDetailsSidebar\">\r\n          <div class=\"LiveBroadcastDetailsTabs\">\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]\"\r\n              @click=\"activeTab = 'details'\">\r\n              直播详情</div>\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]\"\r\n              @click=\"activeTab = 'interact'\">\r\n              互动</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-if=\"activeTab === 'details'\">\r\n            <div class=\"detailsTitle\">{{ details?.theme || '-' }}</div>\r\n            <div class=\"detailsTimeBox\">\r\n              <img src=\"../../assets/img/icon_live_time.png\" alt=\"\" class=\"detailsTimeIcon\">\r\n              <span class=\"detailsTime\">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</span>\r\n            </div>\r\n            <div class=\"detailsDesc\">{{ details.liveDescribes || '暂无简介' }}</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane interact-pane\" v-else>\r\n            <!-- 评论列表 -->\r\n            <div class=\"comments-container\" ref=\"commentsContainer\">\r\n              <div v-for=\"comment in comments\" :key=\"comment.id\" class=\"comment-item\">\r\n                <!-- 主评论 -->\r\n                <div class=\"comment-main\">\r\n                  <div class=\"comment-content\">\r\n                    <div class=\"comment-header\">\r\n                      <span class=\"comment-nickname\">{{ comment.commentUserName }}</span>\r\n                      <span class=\"comment-time\">{{ format(comment.createDate) }}</span>\r\n                    </div>\r\n                    <div class=\"comment-text\">{{ comment.commentContent }}</div>\r\n                    <div class=\"comment-actions\">\r\n                      <span class=\"action-item\" @click=\"showReplyInput(comment.id)\">跟帖互动</span>\r\n                      <span class=\"action-item like-btn\" @click=\"toggleLike(comment)\"\r\n                        :class=\"{ liked: comment.hasClickPraises }\">\r\n                        <img\r\n                          :src=\"comment.hasClickPraises ? require('../../assets/img/fabulous_o.png') : require('../../assets/img/fabulous.png')\"\r\n                          alt=\"点赞\" class=\"like-icon\" />\r\n                        <span class=\"like-count\">({{ comment.praisesCount || 0 }})</span>\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 回复列表 -->\r\n                <div v-if=\"comment.replies && comment.replies.length > 0\" class=\"replies-container\">\r\n                  <div v-for=\"reply in comment.replies\" :key=\"reply.id\" class=\"reply-item\">\r\n                    <div class=\"comment-content\">\r\n                      <div class=\"comment-header\">\r\n                        <span class=\"comment-nickname\">{{ reply.nickname }}</span>\r\n                        <span v-if=\"reply.replyTo\" class=\"reply-to\">回复 @{{ reply.replyTo }}</span>\r\n                        <span class=\"comment-time\">{{ format(reply.time) }}</span>\r\n                      </div>\r\n                      <div class=\"comment-text\">{{ reply.content }}</div>\r\n                      <div class=\"comment-actions\">\r\n                        <span class=\"action-item like-btn\" @click=\"toggleLike(reply)\"\r\n                          :class=\"{ liked: reply.hasClickPraises }\">\r\n                          <img\r\n                            :src=\"reply.hasClickPraises ? require('../../assets/img/fabulous.png') : require('../../assets/img/fabulous_o.png')\"\r\n                            alt=\"点赞\" class=\"like-icon\" />\r\n                          <span class=\"like-count\">({{ reply.praisesCount || 0 }})</span>\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 回复输入框 -->\r\n                <div v-if=\"replyingTo === comment.id\" class=\"reply-input-container\">\r\n                  <div class=\"reply-input-wrapper\">\r\n                    <input v-model=\"replyContent\" type=\"text\"\r\n                      :placeholder=\"replyToUser ? `回复 @${replyToUser}:` : '写下你的回复...'\" class=\"reply-input\"\r\n                      @keyup.enter=\"submitReply(comment.id)\" maxlength=\"200\" ref=\"replyInputRef\" />\r\n                    <div class=\"reply-actions\">\r\n                      <button class=\"cancel-btn\" @click=\"cancelReply\">取消</button>\r\n                      <button class=\"submit-btn\" @click=\"submitReply(comment.id)\" :disabled=\"!replyContent.trim()\">\r\n                        回复\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 空状态 -->\r\n              <div v-if=\"comments.length === 0\" class=\"comments-empty\">\r\n                <div class=\"empty-text\">暂无评论</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 发表评论输入框 -->\r\n            <div class=\"comment-input-area\">\r\n              <div class=\"input-wrapper\">\r\n                <textarea v-model=\"newComment\" placeholder=\"说点什么...\" class=\"comment-input\" @keyup.enter=\"submitComment\"\r\n                  maxlength=\"200\" rows=\"3\" style=\"width: 100%;height: 100%;\"></textarea>\r\n                <div class=\"submit-comment-btn\" @click=\"submitComment\">\r\n                  <div class=\"comment-btn\">发送</div>\r\n                  <img src=\"../../assets/img/icon_live_send.png\" alt=\"发送\" class=\"send-icon\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n\r\n</template>\r\n<script>\r\nexport default { name: 'LiveBroadcastDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onBeforeUnmount, onMounted, computed, watch, nextTick } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport { ElMessage } from 'element-plus'\r\n\r\nimport CountdownTimer from './components/CountdownTimer.vue'\r\nimport VideoPlayer from './components/VideoPlayer.vue'\r\n\r\n// 通用消息提示函数\r\nconst showMessage = (message, type = 'success') => {\r\n  ElMessage({\r\n    message,\r\n    type\r\n  })\r\n}\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  id: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback', 'update:modelValue'])\r\nconst details = ref({})\r\nconst logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')\r\n\r\nconst activeTab = ref('details')\r\nconst imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')\r\n\r\n// 组件引用\r\nconst countdownTimerRef = ref(null)\r\nconst videoPlayerRef = ref(null)\r\n\r\n// 回放模式标识\r\nconst isReplayMode = ref(false)\r\n\r\n// 评论相关数据\r\nconst comments = ref([])\r\nconst newComment = ref('')\r\nconst replyingTo = ref(null)\r\nconst replyContent = ref('')\r\nconst replyToUser = ref('')\r\nconst replyInputRef = ref(null)\r\n\r\n// 评论定时刷新\r\nlet commentTimer = null\r\n\r\n// 监听状态变化\r\nwatch(() => details.value.meetingStatus, (newStatus, oldStatus) => {\r\n  if (newStatus === '进行中' && oldStatus !== '进行中') {\r\n    // 只有在有推流地址时才初始化播放器\r\n    if (details.value.liveUrl) {\r\n      nextTick(() => {\r\n        if (videoPlayerRef.value) {\r\n          videoPlayerRef.value.initPlayer()\r\n        }\r\n      })\r\n    }\r\n  } else if (newStatus === '无推流' && oldStatus !== '无推流') {\r\n    console.log('切换到无推流状态（倒计时结束但无推流地址）')\r\n    // 无推流状态不需要初始化播放器，只显示提示\r\n  } else if (newStatus === '已结束' && oldStatus !== '已结束') {\r\n    // 如果是回放模式且有回放地址，初始化回放播放器\r\n    if (isReplayMode.value && details.value.liveReplayUrl) {\r\n      nextTick(() => {\r\n        if (videoPlayerRef.value) {\r\n          videoPlayerRef.value.initPlayer()\r\n        }\r\n      })\r\n    }\r\n  } else if ((newStatus !== '进行中' && oldStatus === '进行中') ||\r\n    (newStatus !== '已结束' && oldStatus === '已结束' && isReplayMode.value)) {\r\n    // 从进行中或回放状态变为其他状态，销毁播放器\r\n    if (videoPlayerRef.value) {\r\n      videoPlayerRef.value.destroyVideoPlayer()\r\n    }\r\n  }\r\n})\r\n\r\n// 倒计时结束处理\r\nconst handleCountdownEnd = () => {\r\n  // 倒计时结束后检查是否有推流地址\r\n  if (details.value.liveUrl && details.value.meetingStatus === '未开始') {\r\n    isReplayMode.value = false\r\n    details.value.meetingStatus = '进行中'\r\n  } else if (!details.value.liveUrl && details.value.meetingStatus === '未开始') {\r\n    isReplayMode.value = false\r\n    details.value.meetingStatus = '无推流'  // 新增状态：倒计时结束但无推流地址\r\n  }\r\n}\r\n\r\n// 根据数据判断实际状态\r\nconst determineActualStatus = () => {\r\n  if (!details.value) return\r\n  // 如果是未开始状态，不要修改，保持倒计时\r\n  if (details.value.meetingStatus === '未开始') return\r\n  // 如果是进行中状态，不要修改，保持进行中（让模板根据是否有推流地址决定显示内容）\r\n  if (details.value.meetingStatus === '进行中') {\r\n    return isReplayMode.value = false  // 确保是直播模式\r\n  }\r\n  // 如果是已结束状态，但没有回放地址，需要重新判断状态\r\n  if (details.value.meetingStatus === '已结束' && details.value.liveReplayUrl) {\r\n    isReplayMode.value = false\r\n    return\r\n  }\r\n  // 根据回放地址和推流地址判断状态（只对其他状态生效）\r\n  if (details.value.liveReplayUrl) {\r\n    // 1. 如果有回放地址 → 已结束状态\r\n    details.value.meetingStatus = '已结束'\r\n    isReplayMode.value = false  // 重置回放模式\r\n    return\r\n  }\r\n  if (details.value.liveUrl) {\r\n    // 2. 如果没有回放地址但有推流地址 → 进行中状态（还在直播）\r\n    details.value.meetingStatus = '进行中'\r\n    isReplayMode.value = false  // 确保是直播模式\r\n    return\r\n  }\r\n  // 3. 如果既没有回放地址也没有推流地址 → 无推流状态（不能是已结束）\r\n  details.value.meetingStatus = '无推流'\r\n  isReplayMode.value = false\r\n}\r\n\r\nconst applyStatusFromProps = () => {\r\n  // 先根据数据判断实际状态\r\n  determineActualStatus()\r\n  if (details.value.meetingStatus === '进行中') {\r\n    if (details.value.liveUrl) {\r\n      // 延迟一点时间确保VideoPlayer组件已经渲染\r\n      setTimeout(() => {\r\n        if (videoPlayerRef.value) {\r\n          if (isReplayMode.value && details.value.liveReplayUrl) {\r\n            videoPlayerRef.value.initPlayer()\r\n          } else if (!isReplayMode.value && details.value.liveUrl) {\r\n            videoPlayerRef.value.initPlayer()\r\n          }\r\n        }\r\n      }, 100)\r\n    } else {\r\n      console.log('进行中状态但没有推流地址，不初始化VideoPlayer')\r\n    }\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\r\n  if (props.modelValue && props.id) {\r\n    getInfo()\r\n    getCommentData()\r\n    // 开始评论定时刷新\r\n    startCommentRefresh()\r\n  }\r\n  applyStatusFromProps()\r\n})\r\n\r\n// 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\r\nwatch(() => props.modelValue, (newVal, oldVal) => {\r\n  if (newVal && props.id && !oldVal) {\r\n    // 从 false 变为 true 且有 id 时加载数据\r\n    console.log('弹窗打开，开始加载数据')\r\n    // 重置状态，确保每次打开都是干净的状态\r\n    isReplayMode.value = false\r\n    activeTab.value = 'details'\r\n    getInfo()\r\n    getCommentData()\r\n    // 开始评论定时刷新\r\n    startCommentRefresh()\r\n  } else if (!newVal && oldVal) {\r\n    // 从显示变为隐藏时，停止评论定时刷新\r\n    console.log('弹窗关闭，停止定时刷新')\r\n    stopCommentRefresh()\r\n  }\r\n})\r\n\r\n// 监听 id 变化 - 当 id 改变且组件显示时加载数据\r\nwatch(() => props.id, (newVal, oldVal) => {\r\n  if (newVal && props.modelValue && newVal !== oldVal) {\r\n    getInfo()\r\n    getCommentData()\r\n    // 重新开始评论定时刷新\r\n    startCommentRefresh()\r\n  }\r\n})\r\n\r\nconst getInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n  // 应用状态\r\n  applyStatusFromProps()\r\n  // 额外的保障：如果是进行中状态且有推流地址，确保VideoPlayer能够初始化\r\n  if (data.meetingStatus === '进行中' && data.liveUrl) {\r\n    setTimeout(() => {\r\n      if (videoPlayerRef.value) {\r\n        if (isReplayMode.value && data.liveReplayUrl) {\r\n          videoPlayerRef.value.initPlayer()\r\n        } else if (!isReplayMode.value && data.liveUrl) {\r\n          videoPlayerRef.value.initPlayer()\r\n        }\r\n      }\r\n    }, 200)\r\n  }\r\n}\r\n\r\n// 获取评论数据\r\nconst getCommentData = async () => {\r\n  const res = await api.twoLevelTree({\r\n    businessCode: 'liveBroadcast',\r\n    businessId: props.id,\r\n    pageNo: 1,\r\n    pageSize: 99,\r\n  })\r\n  console.log('评论数据:', res)\r\n  comments.value = res.data || []\r\n}\r\n\r\n// 开始评论定时刷新\r\nconst startCommentRefresh = () => {\r\n  if (commentTimer) clearInterval(commentTimer)\r\n  commentTimer = setInterval(() => {\r\n    getCommentData()\r\n  }, 3000) // 每3秒刷新一次\r\n}\r\n\r\n// 停止评论定时刷新\r\nconst stopCommentRefresh = () => {\r\n  if (commentTimer) {\r\n    clearInterval(commentTimer)\r\n    commentTimer = null\r\n  }\r\n}\r\n\r\n// 打开回放\r\nconst handleReplay = () => {\r\n  if (!details.value.liveReplayUrl) {\r\n    showMessage('没有回放地址', 'error')\r\n    return\r\n  }\r\n  // 设置回放模式\r\n  isReplayMode.value = true\r\n  // 确保VideoPlayer组件能够正确初始化\r\n  nextTick(() => {\r\n    if (videoPlayerRef.value) {\r\n      videoPlayerRef.value.initPlayer()\r\n    }\r\n  })\r\n}\r\n\r\n// 提交新评论\r\nconst submitComment = async () => {\r\n  if (!newComment.value.trim()) return\r\n  const res = await api.commentNew({\r\n    form: {\r\n      businessCode: 'liveBroadcast',\r\n      businessId: props.id,\r\n      checkedStatus: 1,\r\n      commentContent: newComment.value,\r\n      parentId: \"\",\r\n      terminalName: 'PC'\r\n    }\r\n  })\r\n\r\n  if (res.code == 200) {\r\n    showMessage('评论发表成功')\r\n    getCommentData()\r\n  } else {\r\n    showMessage(res.message || '评论发表失败', 'error')\r\n  }\r\n  newComment.value = ''\r\n}\r\n\r\n// 点赞功能\r\nconst toggleLike = async (item) => {\r\n  if (item.hasClickPraises) {\r\n    // 取消点赞\r\n    const res = await api.praisesDels({\r\n      businessCode: \"comment\",\r\n      businessId: item.id\r\n    })\r\n\r\n    if (res.code == 200) {\r\n      item.hasClickPraises = false\r\n      item.praisesCount = Math.max(0, (item.praisesCount || 0) - 1)\r\n      ElMessage({\r\n        message: '取消点赞成功',\r\n        type: 'success'\r\n      })\r\n    } else {\r\n      ElMessage({\r\n        message: res.message || '取消点赞失败',\r\n        type: 'error'\r\n      })\r\n    }\r\n  } else {\r\n    // 点赞\r\n    const res = await api.praisesAdd({\r\n      form: {\r\n        businessCode: \"comment\",\r\n        businessId: item.id\r\n      }\r\n    })\r\n\r\n    if (res.code == 200) {\r\n      item.hasClickPraises = true\r\n      item.praisesCount = (item.praisesCount || 0) + 1\r\n      ElMessage({\r\n        message: '点赞成功',\r\n        type: 'success'\r\n      })\r\n    } else {\r\n      ElMessage({\r\n        message: res.message || '点赞失败',\r\n        type: 'error'\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n// 显示回复输入框\r\nconst showReplyInput = (commentId, replyToUsername = '') => {\r\n  replyingTo.value = commentId\r\n  replyToUser.value = replyToUsername\r\n  replyContent.value = ''\r\n\r\n  nextTick(() => {\r\n    if (replyInputRef.value) {\r\n      replyInputRef.value.focus()\r\n    }\r\n  })\r\n}\r\n\r\n// 取消回复\r\nconst cancelReply = () => {\r\n  replyingTo.value = null\r\n  replyToUser.value = ''\r\n  replyContent.value = ''\r\n}\r\n\r\n// 提交回复\r\nconst submitReply = async (commentId) => {\r\n  if (!replyContent.value.trim()) return\r\n  const res = await api.commentNew({\r\n    form: {\r\n      businessCode: \"liveBroadcast\",\r\n      businessId: props.id,\r\n      checkedStatus: 1,\r\n      commentContent: replyContent.value.trim(),\r\n      parentId: commentId,\r\n      terminalName: \"PC\"\r\n    }\r\n  })\r\n  if (res.code == 200) {\r\n    ElMessage({\r\n      message: '回复发表成功',\r\n      type: 'success'\r\n    })\r\n    getCommentData()\r\n    cancelReply()\r\n  } else {\r\n    ElMessage({\r\n      message: res.message || '回复发表失败',\r\n      type: 'error'\r\n    })\r\n  }\r\n}\r\n\r\nonBeforeUnmount(() => {\r\n  // 清理评论定时器\r\n  stopCommentRefresh()\r\n  // 销毁视频播放器\r\n  if (videoPlayerRef.value) {\r\n    videoPlayerRef.value.destroyVideoPlayer()\r\n  }\r\n})\r\n\r\nconst handleClose = () => {\r\n  console.log('handleClose: 开始关闭弹窗')\r\n  // 停止评论定时刷新\r\n  stopCommentRefresh()\r\n  // 销毁视频播放器\r\n  if (videoPlayerRef.value) {\r\n    videoPlayerRef.value.destroyVideoPlayer()\r\n  }\r\n  // 重置状态\r\n  isReplayMode.value = false\r\n  activeTab.value = 'details'\r\n  // 清空数据引用，确保下次打开时重新加载\r\n  details.value = {}\r\n  emit('update:modelValue', false)\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveBroadcastDetails {\r\n  position: fixed;\r\n  inset: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: #0F0F0F;\r\n  display: flex;\r\n  flex-direction: column;\r\n  z-index: 1000;\r\n\r\n  .LiveBroadcastDetailsHeader {\r\n    position: relative;\r\n    height: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 16px;\r\n    color: #fff;\r\n    background: #2B2B2B;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.08);\r\n\r\n    .LiveBroadcastDetailsEmblem {\r\n      width: 58px;\r\n      height: 58px;\r\n      margin-right: 14px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsHeadInfo {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTitle {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #D9D9D9;\r\n    }\r\n\r\n    .LiveBroadcastDetailsClose {\r\n      position: absolute;\r\n      right: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      color: #fff;\r\n      cursor: pointer;\r\n      font-size: 18px;\r\n      opacity: .85;\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsBody {\r\n    flex: 1;\r\n    display: grid;\r\n    grid-template-columns: 1fr 360px;\r\n    gap: 16px;\r\n    padding: 16px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .LiveBroadcastDetailsCanvas {\r\n    position: relative;\r\n    height: 100%;\r\n    background: #111;\r\n    overflow: hidden;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LiveBroadcastDetailsPoster {\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #000;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: #d23a2e;\r\n      font-weight: bold;\r\n      font-size: 22px;\r\n    }\r\n\r\n    // 倒计时样式已移至CountdownTimer组件\r\n\r\n    .LiveBroadcastDetailsLiveOverlay {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: 1;\r\n      pointer-events: none;\r\n\r\n      // 确保VideoPlayer组件可以接收点击事件\r\n      :deep(.video-player-container) {\r\n        pointer-events: auto;\r\n        z-index: 2;\r\n      }\r\n\r\n      // 播放器样式已移至VideoPlayer组件\r\n    }\r\n\r\n    .LiveBroadcastDetailsNoStream {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .no-stream-bg {\r\n        position: absolute;\r\n        inset: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: contain;\r\n        z-index: 1;\r\n      }\r\n\r\n      // 遮罩层覆盖整个图片\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        background: rgba(0, 0, 0, 0.45);\r\n        z-index: 2;\r\n      }\r\n\r\n      .no-stream-wrap {\r\n        position: relative;\r\n        z-index: 3;\r\n        text-align: center;\r\n\r\n        .no-stream-title {\r\n          font-weight: 400;\r\n          font-size: 16px;\r\n          color: #FFFFFF;\r\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsEnded {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .LiveBroadcastDetailsEndedBg {\r\n        position: absolute;\r\n        inset: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: contain;\r\n        z-index: 1;\r\n      }\r\n\r\n      // 遮罩层覆盖整个图片\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        inset: 0;\r\n        background: rgba(0, 0, 0, 0.45);\r\n        z-index: 2;\r\n      }\r\n\r\n      .LiveBroadcastDetailsEndedWrap {\r\n        position: relative;\r\n        z-index: 3;\r\n        text-align: center;\r\n\r\n        .endedTitle {\r\n          font-weight: 400;\r\n          font-size: 16px;\r\n          color: #FFFFFF;\r\n          margin-bottom: 20px;\r\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\r\n        }\r\n\r\n        .replayBtn {\r\n          background: linear-gradient(90deg, #2F04FF 0%, #00F0FE 100%);\r\n          border-radius: 6px;\r\n          border: none;\r\n          padding: 12px 0;\r\n          font-size: 18px;\r\n          color: #FFFFFF;\r\n          width: 175px;\r\n          height: 44px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsSidebar {\r\n    height: 100%;\r\n    background: #191919;\r\n    border-left: 1px solid rgba(255, 255, 255, 0.05);\r\n    color: #e8e8e8;\r\n    // padding: 14px 16px 16px 16px;\r\n    overflow: auto;\r\n\r\n    .LiveBroadcastDetailsTabs {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      gap: 40px;\r\n      margin-bottom: 10px;\r\n      background: #2B2B2B;\r\n      padding: 14px 16px;\r\n\r\n      .LiveBroadcastDetailsTab {\r\n        cursor: pointer;\r\n        color: #999999;\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        transition: color .2s ease;\r\n        font-weight: 500;\r\n\r\n        &.active {\r\n          color: #ffffff;\r\n          font-weight: 700;\r\n        }\r\n\r\n        &.active::after {\r\n          content: '';\r\n          position: absolute;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: -8px;\r\n          height: 3px;\r\n          background: #54BDFF;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsTabPane {\r\n      padding: 12px 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelTitle {\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      font-size: 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelText {\r\n      font-size: 13px;\r\n      line-height: 1.8;\r\n      color: #cfcfcf;\r\n    }\r\n\r\n    /* 详情tab内样式 */\r\n    .detailsTitle {\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #FFFFFF;\r\n    }\r\n\r\n    .detailsTimeBox {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 14px;\r\n\r\n      .detailsTime {\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #666666;\r\n      }\r\n\r\n      .detailsTimeIcon {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n\r\n\r\n    .detailsDesc {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #FFFFFF;\r\n      line-height: 24px;\r\n      margin-top: 24px;\r\n    }\r\n\r\n    /* 互动评论样式 */\r\n    .interact-pane {\r\n      display: flex;\r\n      flex-direction: column;\r\n      height: calc(100vh - 80px - 60px - 32px);\r\n      padding: 0 !important;\r\n    }\r\n\r\n    .comments-container {\r\n      flex: 1;\r\n      overflow-y: auto;\r\n      padding: 12px 15px;\r\n\r\n      &::-webkit-scrollbar {\r\n        width: 4px;\r\n      }\r\n\r\n      &::-webkit-scrollbar-track {\r\n        background: #2B2B2B;\r\n      }\r\n\r\n      &::-webkit-scrollbar-thumb {\r\n        background: #666;\r\n        border-radius: 2px;\r\n      }\r\n    }\r\n\r\n    .comment-item {\r\n      margin-bottom: 20px;\r\n\r\n      .comment-main {\r\n        display: flex;\r\n        margin-bottom: 8px;\r\n      }\r\n\r\n      .comment-content {\r\n        flex: 1;\r\n        min-width: 0;\r\n\r\n        .comment-header {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 6px;\r\n\r\n          .comment-nickname {\r\n            font-size: 13px;\r\n            color: #54BDFF;\r\n            margin-right: 8px;\r\n            font-weight: 500;\r\n          }\r\n\r\n          .reply-to {\r\n            font-size: 12px;\r\n            color: #999999;\r\n            margin-right: 8px;\r\n          }\r\n\r\n          .comment-time {\r\n            font-size: 11px;\r\n            color: #666666;\r\n          }\r\n        }\r\n\r\n        .comment-text {\r\n          font-size: 14px;\r\n          color: #FFFFFF;\r\n          line-height: 20px;\r\n          word-wrap: break-word;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .comment-actions {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 16px;\r\n          margin-bottom: 8px;\r\n\r\n          .action-item {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 4px;\r\n            font-size: 12px;\r\n            color: #999999;\r\n            cursor: pointer;\r\n            transition: color 0.2s;\r\n\r\n            &:hover {\r\n              color: #54BDFF;\r\n            }\r\n\r\n            &.like-btn {\r\n              &.liked {\r\n                color: #54BDFF;\r\n              }\r\n            }\r\n\r\n            .like-icon {\r\n              width: 14px;\r\n              height: 14px;\r\n            }\r\n\r\n            .like-count {\r\n              font-size: 11px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 回复列表样式\r\n    .replies-container {\r\n      margin-left: 42px;\r\n      border-left: 2px solid rgba(255, 255, 255, 0.05);\r\n      padding-left: 12px;\r\n\r\n      .reply-item {\r\n        margin-bottom: 12px;\r\n\r\n        .comment-content {\r\n          .comment-header {\r\n            .comment-nickname {\r\n              font-size: 12px;\r\n            }\r\n          }\r\n\r\n          .comment-text {\r\n            font-size: 13px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 回复输入框样式\r\n    .reply-input-container {\r\n      margin-left: 42px;\r\n      margin-top: 8px;\r\n\r\n      .reply-input-wrapper {\r\n        background: #2B2B2B;\r\n        border-radius: 6px;\r\n        padding: 8px;\r\n\r\n        .reply-input {\r\n          width: 100%;\r\n          height: 32px;\r\n          padding: 0 8px;\r\n          background: #191919;\r\n          border: 1px solid rgba(255, 255, 255, 0.1);\r\n          border-radius: 4px;\r\n          color: #FFFFFF;\r\n          font-size: 13px;\r\n          outline: none;\r\n          margin-bottom: 8px;\r\n\r\n          &::placeholder {\r\n            color: #666666;\r\n          }\r\n\r\n          &:focus {\r\n            border-color: #54BDFF;\r\n          }\r\n        }\r\n\r\n        .reply-actions {\r\n          display: flex;\r\n          justify-content: flex-end;\r\n          gap: 8px;\r\n\r\n          .cancel-btn,\r\n          .submit-btn {\r\n            height: 28px;\r\n            padding: 0 12px;\r\n            border: none;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            cursor: pointer;\r\n            transition: background-color 0.2s;\r\n          }\r\n\r\n          .cancel-btn {\r\n            background: transparent;\r\n            color: #999999;\r\n\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.05);\r\n            }\r\n          }\r\n\r\n          .submit-btn {\r\n            background: #54BDFF;\r\n            color: #FFFFFF;\r\n\r\n            &:hover:not(:disabled) {\r\n              background: #4AA8E8;\r\n            }\r\n\r\n            &:disabled {\r\n              background: #333333;\r\n              color: #666666;\r\n              cursor: not-allowed;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .comments-empty {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 200px;\r\n\r\n      .empty-text {\r\n        font-size: 14px;\r\n        color: #666666;\r\n      }\r\n    }\r\n\r\n    .comment-input-area {\r\n      border-top: 1px solid rgba(255, 255, 255, 0.08);\r\n      padding: 12px;\r\n\r\n      .input-wrapper {\r\n        background: #4A4A4A;\r\n        border-radius: 8px;\r\n        padding: 12px;\r\n        height: 90px;\r\n        position: relative;\r\n\r\n        .comment-input {\r\n          width: 100%;\r\n          height: calc(100% - 40px);\r\n          padding: 0;\r\n          padding-right: 80px;\r\n          background: transparent;\r\n          border: none;\r\n          color: #FFFFFF;\r\n          font-size: 14px;\r\n          outline: none;\r\n          resize: none;\r\n          font-family: inherit;\r\n          line-height: 1.4;\r\n\r\n          &::placeholder {\r\n            color: #999999;\r\n          }\r\n        }\r\n\r\n        .submit-comment-btn {\r\n          position: absolute;\r\n          bottom: 8px;\r\n          right: 8px;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 4px;\r\n\r\n          .comment-btn {\r\n            color: #54BDFF;\r\n            font-size: 17px;\r\n            font-weight: 500;\r\n          }\r\n\r\n          .send-icon {\r\n            width: 18px;\r\n            height: 18px;\r\n          }\r\n\r\n          &:disabled {\r\n            background: #666666;\r\n            color: #999999;\r\n            cursor: not-allowed;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.details-fade-enter-active,\r\n.details-fade-leave-active {\r\n  transition: opacity .2s ease;\r\n}\r\n\r\n.details-fade-enter-from,\r\n.details-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": ";OAoEmBA,UAAyC;OAkFrCC,UAAyC;;EApJnCC,KAAK,EAAC;AAAsB;;EAC9CA,KAAK,EAAC;AAA4B;iBAH7C;;EAKaA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAA2B;;EACjCA,KAAK,EAAC;AAA0B;;EAOpCA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA4B;;EAf/CC,GAAA;EAiBsDD,KAAK,EAAC;;kBAjB5D;;EAuB8EA,KAAK,EAAC;AAAiC;;EAMzGA,KAAK,EAAC;AAA2B;kBA7B7C;;EAoC2DA,KAAK,EAAC;AAA2B;kBApC5F;;EA8CmBA,KAAK,EAAC;AAA+B;;EAU3CA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAA0B;;EAzD/CC,GAAA;EAiEeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAgB;;EAEnBA,KAAK,EAAC;AAAa;;EAEtBA,KAAK,EAAC;AAAa;;EAvEpCC,GAAA;EAyEeD,KAAK,EAAC;;;EAEJA,KAAK,EAAC,oBAAoB;EAACE,GAAG,EAAC;;;EAG3BF,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAiB;kBArFhD;kBAAA;kBAAA;;EA4F8BA,KAAK,EAAC;AAAY;;EA5FhDC,GAAA;EAmG0ED,KAAK,EAAC;;;EAEvDA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAkB;;EAvGtDC,GAAA;EAwGmDD,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAc;;EAEvBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAiB;kBA5GlD;kBAAA;;EAkHgCA,KAAK,EAAC;AAAY;;EAlHlDC,GAAA;EA0HsDD,KAAK,EAAC;;;EACrCA,KAAK,EAAC;AAAqB;kBA3HlD;;EA+HyBA,KAAK,EAAC;AAAe;kBA/H9C;;EAAAC,GAAA;EA0IgDD,KAAK,EAAC;;;EAMrCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAe;;;;uBAhJtCG,YAAA,CA6JaC,WAAA;IA7JDC,IAAI,EAAC,cAAc;IAA/BC,SA6Ja,EA7Jb;;IADFC,OAAA,EAAAC,QAAA,CAEI;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MAAA,OA2JM,C,gBA3JNC,mBAAA,CA2JM,OA3JNC,UA2JM,GA1JJD,mBAAA,CAUM,OAVNE,UAUM,GATOC,MAAA,CAAAC,OAAO,I,cAAlBC,mBAAA,CAAqF;QAJ7FhB,GAAA;QAI4BD,KAAK,EAAC,4BAA4B;QAAEkB,GAAG,EAAEH,MAAA,CAAAC,OAAO;QAAEG,GAAG,EAAC;8BAJlFC,UAAA,KAAAC,mBAAA,gBAKQT,mBAAA,CAIM,OAJNU,UAIM,GAHJV,mBAAA,CAAgE,OAAhEW,UAAgE,EAAAC,gBAAA,CAAtBT,MAAA,CAAAU,OAAO,CAACC,KAAK,kBACvDd,mBAAA,CACkC,OADlCe,UACkC,EADI,OAAK,GAAAH,gBAAA,CAAGT,MAAA,CAAAa,MAAM,CAACb,MAAA,CAAAU,OAAO,CAACI,SAAS,KAAI,KAAG,GAAAL,gBAAA,CAC3ET,MAAA,CAAAa,MAAM,CAACb,MAAA,CAAAU,OAAO,CAACK,OAAO,kB,GAE1BC,YAAA,CAEUC,kBAAA;QAFDhC,KAAK,EAAC,2BAA2B;QAAEiC,OAAK,EAAElB,MAAA,CAAAmB;;QAV3D3B,OAAA,EAAAC,QAAA,CAWU;UAAA,OAAS,CAATuB,YAAA,CAAShB,MAAA,W;;QAXnBoB,CAAA;YAcMvB,mBAAA,CA8IM,OA9INwB,UA8IM,GA7IJxB,mBAAA,CAwCM,OAxCNyB,UAwCM,GAvCJhB,mBAAA,qBAAwB,EACbN,MAAA,CAAAU,OAAO,CAACa,aAAa,c,cAAhCrB,mBAAA,CAIM,OAJNsB,UAIM,GAHJ3B,mBAAA,CAAyF;QAAnFM,GAAG,EAAEH,MAAA,CAAAyB,MAAM;QAAErB,GAAG,EAAC,WAAW;QAACsB,KAAqD,EAArD;UAAA;UAAA;UAAA;QAAA;8BAlB/CC,WAAA,GAmBYX,YAAA,CAC4BhB,MAAA;QADX,YAAU,EAAEA,MAAA,CAAAU,OAAO,CAACI,SAAS;QAAGc,cAAa,EAAE5B,MAAA,CAAA6B,kBAAkB;QAChF1C,GAAG,EAAC;mDAGQa,MAAA,CAAAU,OAAO,CAACa,aAAa,cAAcvB,MAAA,CAAAU,OAAO,CAACoB,OAAO,I,cAAlE5B,mBAAA,CAGM6B,SAAA;QA1BhB7C,GAAA;MAAA,IAsBUoB,mBAAA,qBAAwB,EACxBT,mBAAA,CAGM,OAHNmC,WAGM,I,cAFJ5C,YAAA,CACgFY,MAAA;QADlEd,GAAG,uBAAuBc,MAAA,CAAAiC,KAAK,CAACC,EAAE,IAAIlC,MAAA,CAAAU,OAAO,CAACa,aAAa;QAAK,UAAQ,EAAEvB,MAAA,CAAAU,OAAO,CAACoB,OAAO;QACpG,YAAU,EAAE9B,MAAA,CAAAU,OAAO,CAACyB,aAAa;QAAG,WAAS,EAAE,KAAK;QAAEhD,GAAG,EAAC;iHAG/Ca,MAAA,CAAAU,OAAO,CAACa,aAAa,cAAevB,MAAA,CAAAU,OAAO,CAACa,aAAa,eAAevB,MAAA,CAAAU,OAAO,CAACoB,OAAO,I,cAAvG5B,mBAAA,CAMM6B,SAAA;QAlChB7C,GAAA;MAAA,IA2BUoB,mBAAA,mCAAsC,EACtCT,mBAAA,CAMM,OANNuC,WAMM,GAJJvC,mBAAA,CAAkE;QAA5DM,GAAG,EAAEH,MAAA,CAAAyB,MAAM;QAAErB,GAAG,EAAC,MAAM;QAACnB,KAAK,EAAC;8BA9BhDoD,WAAA,G,0BA+BYxC,mBAAA,CAEM;QAFDZ,KAAK,EAAC;MAA+B,IACxCY,mBAAA,CAAsC;QAAjCZ,KAAK,EAAC;MAAY,GAAC,UAAQ,E,2EAIpBe,MAAA,CAAAU,OAAO,CAACa,aAAa,c,cAArCrB,mBAAA,CAkBM6B,SAAA;QAtDhB7C,GAAA;MAAA,IAmCUoB,mBAAA,0BAA6B,EAC7BT,mBAAA,CAkBM,OAlBNyC,WAkBM,GAjBJhC,mBAAA,kBAAqB,EACFN,MAAA,CAAAuC,YAAY,IAAIvC,MAAA,CAAAU,OAAO,CAACyB,aAAa,I,cAAxD/C,YAAA,CAE+EY,MAAA;QAD5Ed,GAAG,yBAAyBc,MAAA,CAAAiC,KAAK,CAACC,EAAE,IAAIlC,MAAA,CAAAU,OAAO,CAACa,aAAa;QAAK,UAAQ,EAAEvB,MAAA,CAAAU,OAAO,CAACoB,OAAO;QAC3F,YAAU,EAAE9B,MAAA,CAAAU,OAAO,CAACyB,aAAa;QAAG,WAAS,EAAE,IAAI;QAAEhD,GAAG,EAAC;4EAE5De,mBAAA,CAWW6B,SAAA;QArDvB7C,GAAA;MAAA,IAyCYoB,mBAAA,oBAAuB,EAErBA,mBAAA,SAAY,EACZT,mBAAA,CAAkE;QAA5DM,GAAG,EAAEH,MAAA,CAAAyB,MAAM;QAAErB,GAAG,EAAC,MAAM;QAACnB,KAAK,EAAC;8BA5ClDuD,WAAA,GA6CclC,mBAAA,eAAkB,EAClBT,mBAAA,CAMM,OANN4C,WAMM,G,0BALJ5C,mBAAA,CAAmC;QAA9BZ,KAAK,EAAC;MAAY,GAAC,OAAK,sBAC7BqB,mBAAA,oBAAuB,EAC2BN,MAAA,CAAAU,OAAO,CAACgC,QAAQ,S,cAAlEtD,YAAA,CAEYuD,oBAAA;QAnD5BzD,GAAA;QAiD2B0D,IAAI,EAAC,SAAS;QAAC3D,KAAK,EAAC,WAAW;QAA+BiC,OAAK,EAAElB,MAAA,CAAA6C;;QAjDjGrD,OAAA,EAAAC,QAAA,CAiD+G;UAAA,OAE/FqD,MAAA,QAAAA,MAAA,OAnDhBC,gBAAA,CAiD+G,QAE/F,E;;QAnDhB3B,CAAA;YAAAd,mBAAA,e,qFAAAA,mBAAA,e,GAwDQT,mBAAA,CAmGM,OAnGNmD,WAmGM,GAlGJnD,mBAAA,CAOM,OAPNoD,WAOM,GANJpD,mBAAA,CAEY;QAFNZ,KAAK,EA1DvBiE,eAAA;UAAAC,MAAA,EA0D+DnD,MAAA,CAAAoD,SAAS;QAAA;QACzDlC,OAAK,EAAA4B,MAAA,QAAAA,MAAA,gBAAAO,MAAA;UAAA,OAAErD,MAAA,CAAAoD,SAAS;QAAA;SAAc,OAC3B,kBACNvD,mBAAA,CAEU;QAFJZ,KAAK,EA7DvBiE,eAAA;UAAAC,MAAA,EA6D+DnD,MAAA,CAAAoD,SAAS;QAAA;QACzDlC,OAAK,EAAA4B,MAAA,QAAAA,MAAA,gBAAAO,MAAA;UAAA,OAAErD,MAAA,CAAAoD,SAAS;QAAA;SAAe,KAC9B,iB,GAEyCpD,MAAA,CAAAoD,SAAS,kB,cAAxDlD,mBAAA,CAOM,OAPNoD,WAOM,GANJzD,mBAAA,CAA2D,OAA3D0D,WAA2D,EAAA9C,gBAAA,CAA9B,EAAAf,eAAA,GAAAM,MAAA,CAAAU,OAAO,cAAAhB,eAAA,uBAAPA,eAAA,CAASiB,KAAK,0BAC3Cd,mBAAA,CAGM,OAHN2D,WAGM,G,0BAFJ3D,mBAAA,CAA8E;QAAzEM,GAAyC,EAAzCpB,UAAyC;QAACqB,GAAG,EAAC,EAAE;QAACnB,KAAK,EAAC;mCAC5DY,mBAAA,CAAqG,QAArG4D,WAAqG,EAA3E,KAAG,GAAAhD,gBAAA,CAAGT,MAAA,CAAAa,MAAM,EAAAlB,gBAAA,GAACK,MAAA,CAAAU,OAAO,cAAAf,gBAAA,uBAAPA,gBAAA,CAASmB,SAAS,KAAI,KAAG,GAAAL,gBAAA,CAAGT,MAAA,CAAAa,MAAM,EAAAjB,gBAAA,GAACI,MAAA,CAAAU,OAAO,cAAAd,gBAAA,uBAAPA,gBAAA,CAASmB,OAAO,kB,GAE5FlB,mBAAA,CAAoE,OAApE6D,WAAoE,EAAAjD,gBAAA,CAAxCT,MAAA,CAAAU,OAAO,CAACiD,aAAa,2B,oBAEnDzD,mBAAA,CAiFM,OAjFN0D,WAiFM,GAhFJtD,mBAAA,UAAa,EACbT,mBAAA,CAkEM,OAlENgE,WAkEM,I,kBAjEJ3D,mBAAA,CA2DM6B,SAAA,QAvIpB+B,WAAA,CA4EqC9D,MAAA,CAAA+D,QAAQ,EA5E7C,UA4E0BC,OAAO;6BAAnB9D,mBAAA,CA2DM;UA3D4BhB,GAAG,EAAE8E,OAAO,CAAC9B,EAAE;UAAEjD,KAAK,EAAC;YACvDqB,mBAAA,SAAY,EACZT,mBAAA,CAkBM,OAlBNoE,WAkBM,GAjBJpE,mBAAA,CAgBM,OAhBNqE,WAgBM,GAfJrE,mBAAA,CAGM,OAHNsE,WAGM,GAFJtE,mBAAA,CAAmE,QAAnEuE,WAAmE,EAAA3D,gBAAA,CAAjCuD,OAAO,CAACK,eAAe,kBACzDxE,mBAAA,CAAkE,QAAlEyE,WAAkE,EAAA7D,gBAAA,CAApCT,MAAA,CAAAa,MAAM,CAACmD,OAAO,CAACO,UAAU,kB,GAEzD1E,mBAAA,CAA4D,OAA5D2E,WAA4D,EAAA/D,gBAAA,CAA/BuD,OAAO,CAACS,cAAc,kBACnD5E,mBAAA,CASM,OATN6E,WASM,GARJ7E,mBAAA,CAAyE;UAAnEZ,KAAK,EAAC,aAAa;UAAEiC,OAAK,WAALA,OAAKA,CAAAmC,MAAA;YAAA,OAAErD,MAAA,CAAA2E,cAAc,CAACX,OAAO,CAAC9B,EAAE;UAAA;WAAG,MAAI,iBAtFxF0C,WAAA,GAuFsB/E,mBAAA,CAMO;UANDZ,KAAK,EAvFjCiE,eAAA,EAuFkC,sBAAsB;YAAA2B,KAAA,EACfb,OAAO,CAACc;UAAe;UADN5D,OAAK,WAALA,OAAKA,CAAAmC,MAAA;YAAA,OAAErD,MAAA,CAAA+E,UAAU,CAACf,OAAO;UAAA;YAE3DnE,mBAAA,CAE+B;UAD5BM,GAAG,EAAE6D,OAAO,CAACc,eAAe,GAAGE,OAAO,sCAAsCA,OAAO;UACpF5E,GAAG,EAAC,IAAI;UAACnB,KAAK,EAAC;gCA3FzCgG,WAAA,GA4FwBpF,mBAAA,CAAiE,QAAjEqF,WAAiE,EAAxC,GAAC,GAAAzE,gBAAA,CAAGuD,OAAO,CAACmB,YAAY,SAAQ,GAAC,gB,yBA5FlFC,WAAA,E,OAkGgB9E,mBAAA,UAAa,EACF0D,OAAO,CAACqB,OAAO,IAAIrB,OAAO,CAACqB,OAAO,CAACC,MAAM,Q,cAApDpF,mBAAA,CAoBM,OApBNqF,WAoBM,I,kBAnBJrF,mBAAA,CAkBM6B,SAAA,QAtHxB+B,WAAA,CAoGuCE,OAAO,CAACqB,OAAO,EApGtD,UAoG8BG,KAAK;+BAAjBtF,mBAAA,CAkBM;YAlBiChB,GAAG,EAAEsG,KAAK,CAACtD,EAAE;YAAEjD,KAAK,EAAC;cAC1DY,mBAAA,CAgBM,OAhBN4F,WAgBM,GAfJ5F,mBAAA,CAIM,OAJN6F,WAIM,GAHJ7F,mBAAA,CAA0D,QAA1D8F,WAA0D,EAAAlF,gBAAA,CAAxB+E,KAAK,CAACI,QAAQ,kBACpCJ,KAAK,CAACK,OAAO,I,cAAzB3F,mBAAA,CAA0E,QAA1E4F,WAA0E,EAA9B,MAAI,GAAArF,gBAAA,CAAG+E,KAAK,CAACK,OAAO,oBAxGxFvF,mBAAA,gBAyGwBT,mBAAA,CAA0D,QAA1DkG,WAA0D,EAAAtF,gBAAA,CAA5BT,MAAA,CAAAa,MAAM,CAAC2E,KAAK,CAACQ,IAAI,kB,GAEjDnG,mBAAA,CAAmD,OAAnDoG,WAAmD,EAAAxF,gBAAA,CAAtB+E,KAAK,CAACU,OAAO,kBAC1CrG,mBAAA,CAQM,OARNsG,WAQM,GAPJtG,mBAAA,CAMO;YANDZ,KAAK,EA7GnCiE,eAAA,EA6GoC,sBAAsB;cAAA2B,KAAA,EACfW,KAAK,CAACV;YAAe;YADJ5D,OAAK,WAALA,OAAKA,CAAAmC,MAAA;cAAA,OAAErD,MAAA,CAAA+E,UAAU,CAACS,KAAK;YAAA;cAEzD3F,mBAAA,CAE+B;YAD5BM,GAAG,EAAEqF,KAAK,CAACV,eAAe,GAAGE,OAAO,oCAAoCA,OAAO;YAChF5E,GAAG,EAAC,IAAI;YAACnB,KAAK,EAAC;kCAjH3CmH,WAAA,GAkH0BvG,mBAAA,CAA+D,QAA/DwG,WAA+D,EAAtC,GAAC,GAAA5F,gBAAA,CAAG+E,KAAK,CAACL,YAAY,SAAQ,GAAC,gB,yBAlHlFmB,WAAA,E;4CAAAhG,mBAAA,gBAyHgBA,mBAAA,WAAc,EACHN,MAAA,CAAAuG,UAAU,KAAKvC,OAAO,CAAC9B,EAAE,I,cAApChC,mBAAA,CAYM,OAZNsG,WAYM,GAXJ3G,mBAAA,CAUM,OAVN4G,WAUM,G,gBATJ5G,mBAAA,CAE+E;UA9HnG,uBAAAiD,MAAA,QAAAA,MAAA,gBAAAO,MAAA;YAAA,OA4HoCrD,MAAA,CAAA0G,YAAY,GAAArD,MAAA;UAAA;UAAET,IAAI,EAAC,MAAM;UACtC+D,WAAW,EAAE3G,MAAA,CAAA4G,WAAW,UAAU5G,MAAA,CAAA4G,WAAW;UAAmB3H,KAAK,EAAC,aAAa;UACnF4H,OAAK,EA9H5BC,SAAA,WAAAzD,MAAA;YAAA,OA8HoCrD,MAAA,CAAA+G,WAAW,CAAC/C,OAAO,CAAC9B,EAAE;UAAA;UAAG8E,SAAS,EAAC,KAAK;UA9H5EC,OAAA;UA8H6E9H,GAAG,EAAC;iDA9HjF+H,WAAA,I,cA4HoClH,MAAA,CAAA0G,YAAY,E,GAG5B7G,mBAAA,CAKM,OALNsH,WAKM,GAJJtH,mBAAA,CAA2D;UAAnDZ,KAAK,EAAC,YAAY;UAAEiC,OAAK,EAAElB,MAAA,CAAAoH;WAAa,IAAE,GAClDvH,mBAAA,CAES;UAFDZ,KAAK,EAAC,YAAY;UAAEiC,OAAK,WAALA,OAAKA,CAAAmC,MAAA;YAAA,OAAErD,MAAA,CAAA+G,WAAW,CAAC/C,OAAO,CAAC9B,EAAE;UAAA;UAAImF,QAAQ,GAAGrH,MAAA,CAAA0G,YAAY,CAACY,IAAI;WAAI,MAE7F,iBAnItBC,WAAA,E,SAAAjH,mBAAA,e;sCAyIcA,mBAAA,SAAY,EACDN,MAAA,CAAA+D,QAAQ,CAACuB,MAAM,U,cAA1BpF,mBAAA,CAEM,OAFNsH,WAEM,EAAA1E,MAAA,QAAAA,MAAA,OADJjD,mBAAA,CAAkC;QAA7BZ,KAAK,EAAC;MAAY,GAAC,MAAI,oB,MA3I5CqB,mBAAA,e,yBA+IYA,mBAAA,aAAgB,EAChBT,mBAAA,CASM,OATN4H,WASM,GARJ5H,mBAAA,CAOM,OAPN6H,WAOM,G,gBANJ7H,mBAAA,CACwE;QAnJxF,uBAAAiD,MAAA,QAAAA,MAAA,gBAAAO,MAAA;UAAA,OAkJmCrD,MAAA,CAAA2H,UAAU,GAAAtE,MAAA;QAAA;QAAEsD,WAAW,EAAC,SAAS;QAAC1H,KAAK,EAAC,eAAe;QAAE4H,OAAK,EAlJjGC,SAAA,CAkJyG9G,MAAA,CAAA4H,aAAa;QACpGZ,SAAS,EAAC,KAAK;QAACa,IAAI,EAAC,GAAG;QAACnG,KAAiC,EAAjC;UAAA;UAAA;QAAA;qEADR1B,MAAA,CAAA2H,UAAU,E,GAE7B9H,mBAAA,CAGM;QAHDZ,KAAK,EAAC,oBAAoB;QAAEiC,OAAK,EAAElB,MAAA,CAAA4H;oCACtC/H,mBAAA,CAAiC;QAA5BZ,KAAK,EAAC;MAAa,GAAC,IAAE,qBAC3BY,mBAAA,CAA4E;QAAvEM,GAAyC,EAAzCnB,UAAyC;QAACoB,GAAG,EAAC,IAAI;QAACnB,KAAK,EAAC;kFApJ/D6I,MAAA,CAAAC,UAAU,E;;IAF3B3G,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}