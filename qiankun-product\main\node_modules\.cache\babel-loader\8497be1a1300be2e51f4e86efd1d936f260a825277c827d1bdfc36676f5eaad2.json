{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport api from '@/api';\nimport { useStore } from 'vuex';\nimport { useRouter } from 'vue-router';\nimport { ref, inject, onMounted, computed, nextTick } from 'vue';\nimport { user, systemLogo, systemName } from 'common/js/system_var.js';\nimport config from 'common/config';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod';\nvar __default__ = {\n  name: 'WorkBenchCopy'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var router = useRouter();\n    var openPage = inject('openPage');\n    var leftMenuData = inject('leftMenuData');\n    var menuListData = inject('WorkBenchList');\n    var regionId = inject('regionId');\n    var regionSelect = inject('regionSelect');\n    var area = inject('area');\n    // const menuListData = computed(() => filterMenu(store.getters.getMenuFn || []))\n    var store = useStore();\n    var menuIcon = `${config.API_URL}/pageImg/open/menuIcon`;\n    var backgroundImage = ref(`${config.API_URL}/pageImg/open/homePage?areaId=${user.value.areaId}`);\n    var originMenuItems = [{\n      title: '',\n      bg: require('../img/menu_wdgz.png'),\n      bgActive: require('../img/menu_wdgz_s.png')\n    }, {\n      title: '',\n      bg: require('../img/menu_zhyy.png'),\n      bgActive: require('../img/menu_zhyy_s.png')\n    }, {\n      title: '',\n      bg: require('../img/menu_wddb.png'),\n      bgActive: require('../img/menu_wddb_s.png')\n    }, {\n      title: '',\n      bg: require('../img/menu_qtyy.png'),\n      bgActive: require('../img/menu_qtyy_s.png')\n    }];\n    var activeMenuIndex = ref(null);\n    var isSys = ref(false);\n    onMounted(function () {\n      nextTick(function () {\n        setTimeout(function () {\n          var roleList = JSON.parse(sessionStorage.getItem('role'));\n          console.log('当前角色===>', roleList);\n          if (roleList) {\n            isSys.value = roleList === null || roleList === void 0 ? void 0 : roleList.includes('管理员');\n          }\n        }, 1000);\n      });\n    });\n    var menuItems = computed(function () {\n      var filtered = (menuListData.value || []).filter(function (item) {\n        return item.routePath !== '/homePage';\n      });\n      return filtered.map(function (item, idx) {\n        return _objectSpread(_objectSpread({}, originMenuItems[idx]), {}, {\n          title: item.name,\n          id: item.id,\n          has: item.permissions\n        });\n      });\n    });\n    var subMenus = computed(function () {\n      var filtered = (menuListData.value || []).filter(function (item) {\n        return item.routePath !== '/homePage';\n      });\n      var arr = filtered.map(function (item) {\n        return (item.children || []).map(function (child) {\n          return {\n            id: child.id,\n            name: child.name,\n            icon: child.icon,\n            children: child.children,\n            routePath: child.routePath,\n            menuFunction: child.menuFunction,\n            menuRouteType: child.menuRouteType,\n            has: child.has || child.permissions\n          };\n        });\n      });\n      var result = arr.map(function (subArray) {\n        return subArray.filter(function (item) {\n          return item.name !== '系统运维' && item.name !== '我的';\n        });\n      }).filter(function (subArray) {\n        return subArray.length > 0;\n      });\n      return result;\n    });\n    var handleWorkBench = function handleWorkBench(item) {\n      leftMenuData(item);\n    };\n    var _filterMenu = function filterMenu(menuList) {\n      var newMenuList = [];\n      for (var i = 0, len = menuList.length; i < len; i++) {\n        newMenuList.push({\n          id: menuList[i].menuId,\n          name: menuList[i].name,\n          routePath: menuList[i].routePath,\n          menuFunction: menuList[i].menuFunction,\n          menuRouteType: menuList[i].menuRouteType,\n          icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,\n          has: menuList[i].permissions,\n          children: _filterMenu(menuList[i].children || [])\n        });\n      }\n      return newMenuList;\n    };\n    // 切换旧版本\n    var oldVersion = function oldVersion() {\n      console.log('切换到旧版本');\n      openPage({\n        key: 'routePath',\n        value: '/homePage'\n      });\n    };\n    // 退出\n    var handleExit = function handleExit() {\n      ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        loginOut('已安全退出！');\n      }).catch(function () {\n        ElMessage({\n          type: 'info',\n          message: '已取消退出'\n        });\n      });\n    };\n    var loginOut = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(text) {\n        var _yield$api$loginOut, code, goal_login_router_path, goal_login_router_query;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.loginOut();\n            case 2:\n              _yield$api$loginOut = _context.sent;\n              code = _yield$api$loginOut.code;\n              if (code === 200) {\n                sessionStorage.clear();\n                goal_login_router_path = localStorage.getItem('goal_login_router_path');\n                if (goal_login_router_path) {\n                  goal_login_router_query = localStorage.getItem('goal_login_router_query') || '';\n                  router.push({\n                    path: goal_login_router_path,\n                    query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\n                  });\n                } else {\n                  router.push({\n                    path: '/LoginView'\n                  });\n                }\n                store.commit('setState');\n                globalReadOpenConfig();\n                ElMessage({\n                  message: text,\n                  showClose: true,\n                  type: 'success'\n                });\n              }\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function loginOut(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    // 系统管理\n    var sysManagement = function sysManagement() {\n      var filtered = (menuListData.value || []).filter(function (item) {\n        return item.routePath !== '/homePage';\n      });\n      var systemOperation = filtered.flatMap(function (item) {\n        return item.children;\n      }).find(function (child) {\n        return child.name === '系统运维';\n      });\n      leftMenuData(systemOperation);\n    };\n    // 跳转到我的\n    var sysUser = function sysUser() {\n      var filtered = (menuListData.value || []).filter(function (item) {\n        return item.routePath !== '/homePage';\n      });\n      var myOperation = filtered.flatMap(function (item) {\n        return item.children;\n      }).find(function (child) {\n        return child.name === '我的';\n      });\n      leftMenuData(myOperation);\n    };\n    // 点击主菜单展开子级\n    var handleMenuClick = function handleMenuClick(index) {\n      var _subMenus$value$index;\n      // if (index === 3) {\n      //   const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\n      //   const data = filtered.find(item => item.name === '数据中心')\n      //   leftMenuData(data)\n      //   return\n      // }\n      if (!((_subMenus$value$index = subMenus.value[index]) !== null && _subMenus$value$index !== void 0 && _subMenus$value$index.length)) {\n        ElMessage.info('该菜单下暂无子菜单');\n        return;\n      }\n      activeMenuIndex.value = activeMenuIndex.value === index ? null : index;\n    };\n    var handleContentClick = function handleContentClick(event) {\n      // 如果点击的是菜单项或其子元素，不处理\n      if (event.target.closest('.menu-item') || event.target.closest('.module-area')) {\n        return;\n      }\n      // 点击空白区域时收起菜单\n      activeMenuIndex.value = null;\n    };\n    var __returned__ = {\n      router,\n      openPage,\n      leftMenuData,\n      menuListData,\n      regionId,\n      regionSelect,\n      area,\n      store,\n      menuIcon,\n      backgroundImage,\n      originMenuItems,\n      activeMenuIndex,\n      isSys,\n      menuItems,\n      subMenus,\n      handleWorkBench,\n      filterMenu: _filterMenu,\n      oldVersion,\n      handleExit,\n      loginOut,\n      sysManagement,\n      sysUser,\n      handleMenuClick,\n      handleContentClick,\n      get api() {\n        return api;\n      },\n      get useStore() {\n        return useStore;\n      },\n      get useRouter() {\n        return useRouter;\n      },\n      ref,\n      inject,\n      onMounted,\n      computed,\n      nextTick,\n      get user() {\n        return user;\n      },\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get config() {\n        return config;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get globalReadOpenConfig() {\n        return globalReadOpenConfig;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "api", "useStore", "useRouter", "ref", "inject", "onMounted", "computed", "nextTick", "user", "systemLogo", "systemName", "config", "ElMessage", "ElMessageBox", "globalReadOpenConfig", "__default__", "router", "openPage", "leftMenuData", "menuListData", "regionId", "regionSelect", "area", "store", "menuIcon", "API_URL", "backgroundImage", "areaId", "originMenuItems", "title", "bg", "require", "bgActive", "activeMenuIndex", "isSys", "setTimeout", "roleList", "JSON", "parse", "sessionStorage", "getItem", "console", "log", "includes", "menuItems", "filtered", "item", "routePath", "map", "idx", "id", "has", "permissions", "subMenus", "arr", "children", "child", "icon", "menuFunction", "menuRouteType", "result", "subArray", "handleWorkBench", "filterMenu", "menuList", "newMenuList", "len", "menuId", "iconUrl", "fileURL", "oldVersion", "key", "handleExit", "confirm", "confirmButtonText", "cancelButtonText", "loginOut", "message", "_ref2", "_callee", "text", "_yield$api$loginOut", "code", "goal_login_router_path", "goal_login_router_query", "_callee$", "_context", "clear", "localStorage", "path", "query", "commit", "showClose", "_x", "sysManagement", "systemOperation", "flatMap", "find", "sysUser", "myOperation", "handleMenuClick", "index", "_subMenus$value$index", "info", "handleContentClick", "event", "target", "closest"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/WorkBench/WorkBenchCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"WorkBenchCopy\">\r\n    <div class=\"background-layer\" :class=\"{ 'blur-bg': activeMenuIndex !== null }\"\r\n      :style=\"{ backgroundImage: `url(${backgroundImage})` }\"></div>\r\n    <div class=\"content-layer\" @click=\"handleContentClick\">\r\n      <div class=\"WorkBenchHeader\">\r\n        <div class=\"WorkBenchHeaderLogoName\">\r\n          <el-image class=\"WorkBenchHeaderLogo\" :src=\"systemLogo\" fit=\"contain\" />\r\n          <div class=\"WorkBenchHeaderName\" v-html=\"systemName\"></div>\r\n        </div>\r\n        <!-- <img class=\"WorkBenchHeaderLogo\" src=\"../img/home_toptext_bg.png\" alt=\"logo\" /> -->\r\n        <div class=\"WorkBenchHeaderRight\">\r\n          <!-- <span class=\"WorkBenchHeaderLink\">操作指南</span> -->\r\n          <xyl-region v-model=\"regionId\" :data=\"area\" @select=\"regionSelect\"\r\n            :props=\"{ label: 'name', children: 'children' }\"></xyl-region>\r\n          <button class=\"old_version\" @click=\"oldVersion\">\r\n            <img src=\"../img/login_btn_bg.png\" alt=\"\" />\r\n            <span>旧版本</span>\r\n          </button>\r\n          <span class=\"WorkBenchHeaderLink\" @click=\"sysManagement\" v-if=\"isSys\">系统管理</span>\r\n          <div class=\"WorkBenchHeaderUser\" @click=\"sysUser\">\r\n            <img class=\"WorkBenchHeaderAvatar\" :src=\"user.image\" alt=\"头像\" />\r\n            <span class=\"WorkBenchHeaderName\">{{ user.userName }}</span>\r\n          </div>\r\n          <span class=\"WorkBenchHeaderLogout WorkBenchHeaderLink\" @click=\"handleExit\">退出</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"WorkBenchContent\">\r\n        <div class=\"menu-container\">\r\n          <div class=\"menu-list\" :class=\"{ 'has-active': activeMenuIndex !== null }\">\r\n            <div v-for=\"(item, index) in menuItems\" :key=\"index\" class=\"menu-item\" :class=\"[\r\n              { active: activeMenuIndex === index },\r\n              index === 0 ? 'menu-item-first' : '',\r\n              index === 1 ? 'menu-item-second' : '',\r\n              index === 2 ? 'menu-item-third' : '',\r\n              index === 3 ? 'menu-item-fourth' : '',\r\n              index === 0 || index === 3 ? 'u-top' : 'u-bottom'\r\n            ]\" :style=\"{\r\n              backgroundImage: `url(${activeMenuIndex === index ? item.bgActive : item.bg})`\r\n            }\" @click.stop=\"handleMenuClick(index)\">\r\n              <div class=\"menu-title\">{{ item.title }}</div>\r\n            </div>\r\n          </div>\r\n          <transition name=\"fade-slide\">\r\n            <div v-if=\"activeMenuIndex !== null\" class=\"module-area\">\r\n              <div class=\"module-bg\"></div>\r\n              <div class=\"module-list\">\r\n                <div v-for=\"(mod, idx) in subMenus[activeMenuIndex] || []\" :key=\"mod.id\" class=\"module-item\"\r\n                  @click=\"handleWorkBench(mod)\">\r\n                  <img class=\"module-icon\" :src=\"mod.icon\" />\r\n                  <div class=\"module-title\">{{ mod.name }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </transition>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'WorkBenchCopy' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { useStore } from 'vuex'\r\nimport { useRouter } from 'vue-router'\r\nimport { ref, inject, onMounted, computed, nextTick } from 'vue'\r\nimport { user, systemLogo, systemName } from 'common/js/system_var.js'\r\nimport config from 'common/config'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod'\r\nconst router = useRouter()\r\nconst openPage = inject('openPage')\r\nconst leftMenuData = inject('leftMenuData')\r\nconst menuListData = inject('WorkBenchList')\r\nconst regionId = inject('regionId')\r\nconst regionSelect = inject('regionSelect')\r\nconst area = inject('area')\r\n// const menuListData = computed(() => filterMenu(store.getters.getMenuFn || []))\r\nconst store = useStore()\r\nconst menuIcon = `${config.API_URL}/pageImg/open/menuIcon`\r\nconst backgroundImage = ref(`${config.API_URL}/pageImg/open/homePage?areaId=${user.value.areaId}`)\r\nconst originMenuItems = [\r\n  {\r\n    title: '',\r\n    bg: require('../img/menu_wdgz.png'),\r\n    bgActive: require('../img/menu_wdgz_s.png')\r\n  },\r\n  {\r\n    title: '',\r\n    bg: require('../img/menu_zhyy.png'),\r\n    bgActive: require('../img/menu_zhyy_s.png')\r\n  },\r\n  {\r\n    title: '',\r\n    bg: require('../img/menu_wddb.png'),\r\n    bgActive: require('../img/menu_wddb_s.png')\r\n  },\r\n  {\r\n    title: '',\r\n    bg: require('../img/menu_qtyy.png'),\r\n    bgActive: require('../img/menu_qtyy_s.png')\r\n  }\r\n]\r\nconst activeMenuIndex = ref(null)\r\nconst isSys = ref(false)\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    setTimeout(() => {\r\n      const roleList = JSON.parse(sessionStorage.getItem('role'))\r\n      console.log('当前角色===>', roleList)\r\n      if (roleList) { isSys.value = roleList?.includes('管理员') }\r\n    }, 1000);\r\n  })\r\n})\r\nconst menuItems = computed(() => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  return filtered.map((item, idx) => ({\r\n    ...originMenuItems[idx],\r\n    title: item.name,\r\n    id: item.id,\r\n    has: item.permissions\r\n  }))\r\n})\r\nconst subMenus = computed(() => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  var arr = filtered.map(item => (item.children || []).map(child => ({\r\n    id: child.id,\r\n    name: child.name,\r\n    icon: child.icon,\r\n    children: child.children,\r\n    routePath: child.routePath,\r\n    menuFunction: child.menuFunction,\r\n    menuRouteType: child.menuRouteType,\r\n    has: child.has || child.permissions\r\n  }))\r\n  )\r\n  const result = arr.map(subArray =>\r\n    subArray.filter(item =>\r\n      item.name !== '系统运维' && item.name !== '我的'\r\n    )\r\n  ).filter(subArray => subArray.length > 0);\r\n  return result\r\n})\r\nconst handleWorkBench = (item) => { leftMenuData(item) }\r\nconst filterMenu = menuList => {\r\n  let newMenuList = []\r\n  for (let i = 0, len = menuList.length; i < len; i++) {\r\n    newMenuList.push({\r\n      id: menuList[i].menuId, name: menuList[i].name,\r\n      routePath: menuList[i].routePath,\r\n      menuFunction: menuList[i].menuFunction,\r\n      menuRouteType: menuList[i].menuRouteType,\r\n      icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,\r\n      has: menuList[i].permissions,\r\n      children: filterMenu(menuList[i].children || [])\r\n    })\r\n  }\r\n  return newMenuList\r\n}\r\n// 切换旧版本\r\nconst oldVersion = () => {\r\n  console.log('切换到旧版本')\r\n  openPage({ key: 'routePath', value: '/homePage' })\r\n}\r\n// 退出\r\nconst handleExit = () => {\r\n  ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { loginOut('已安全退出！') }).catch(() => { ElMessage({ type: 'info', message: '已取消退出' }) })\r\n}\r\nconst loginOut = async (text) => {\r\n  const { code } = await api.loginOut()\r\n  if (code === 200) {\r\n    sessionStorage.clear()\r\n    const goal_login_router_path = localStorage.getItem('goal_login_router_path')\r\n    if (goal_login_router_path) {\r\n      const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''\r\n      router.push({ path: goal_login_router_path, query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {} })\r\n    } else {\r\n      router.push({ path: '/LoginView' })\r\n    }\r\n    store.commit('setState')\r\n    globalReadOpenConfig()\r\n    ElMessage({ message: text, showClose: true, type: 'success' })\r\n  }\r\n}\r\n// 系统管理\r\nconst sysManagement = () => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '系统运维')\r\n  leftMenuData(systemOperation)\r\n}\r\n// 跳转到我的\r\nconst sysUser = () => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  const myOperation = filtered.flatMap(item => item.children).find(child => child.name === '我的')\r\n  leftMenuData(myOperation)\r\n}\r\n// 点击主菜单展开子级\r\nconst handleMenuClick = (index) => {\r\n  // if (index === 3) {\r\n  //   const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  //   const data = filtered.find(item => item.name === '数据中心')\r\n  //   leftMenuData(data)\r\n  //   return\r\n  // }\r\n  if (!subMenus.value[index]?.length) {\r\n    ElMessage.info('该菜单下暂无子菜单');\r\n    return;\r\n  }\r\n  activeMenuIndex.value = activeMenuIndex.value === index ? null : index\r\n}\r\nconst handleContentClick = (event) => {\r\n  // 如果点击的是菜单项或其子元素，不处理\r\n  if (event.target.closest('.menu-item') || event.target.closest('.module-area')) {\r\n    return;\r\n  }\r\n  // 点击空白区域时收起菜单\r\n  activeMenuIndex.value = null;\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.WorkBenchCopy {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  .background-layer {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: no-repeat;\r\n    background-size: 100% 100%;\r\n    transition: filter 0.3s ease;\r\n    z-index: 1;\r\n\r\n    &.blur-bg {\r\n      filter: blur(5px);\r\n      -webkit-filter: blur(5px);\r\n    }\r\n  }\r\n\r\n  .content-layer {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 2;\r\n  }\r\n\r\n  .WorkBenchHeader {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24px 40px 0 40px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    z-index: 2;\r\n    flex-shrink: 0;\r\n\r\n    .WorkBenchHeaderLogoName {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .WorkBenchHeaderLogo {\r\n        height: 74px;\r\n        width: 74px;\r\n      }\r\n\r\n      .WorkBenchHeaderName {\r\n        font-size: 37px;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        margin-left: 15px;\r\n        letter-spacing: 5px;\r\n      }\r\n    }\r\n\r\n    .WorkBenchHeaderLogo {\r\n      height: 74px;\r\n    }\r\n\r\n    .WorkBenchHeaderRight {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 20px;\r\n\r\n      .old_version {\r\n        background: none;\r\n        border: none;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          height: 39px;\r\n        }\r\n\r\n        span {\r\n          position: absolute;\r\n          left: 0;\r\n          width: 100%;\r\n          text-align: center;\r\n          color: rgb(0, 51, 152);\r\n          font-size: 14px;\r\n          line-height: 39px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n\r\n      .WorkBenchHeaderLink {\r\n        color: #fff;\r\n        font-size: 16px;\r\n        cursor: pointer;\r\n        margin-right: 8px;\r\n\r\n        &:hover {\r\n          text-decoration: underline;\r\n        }\r\n      }\r\n\r\n      .WorkBenchHeaderUser {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .WorkBenchHeaderAvatar {\r\n          width: 32px;\r\n          height: 32px;\r\n          border-radius: 50%;\r\n          margin: 0 8px;\r\n          object-fit: cover;\r\n          border: 2px solid #fff;\r\n          background: #eee;\r\n        }\r\n\r\n        .WorkBenchHeaderName {\r\n          color: #fff;\r\n          font-size: 16px;\r\n          margin-right: 8px;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n\r\n\r\n      .WorkBenchHeaderLogout {\r\n        color: #fff;\r\n        font-size: 16px;\r\n        cursor: pointer;\r\n        margin-left: 8px;\r\n\r\n        &:hover {\r\n          color: #ff4d4f;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .WorkBenchContent {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: flex-start;\r\n    padding-top: 8vh;\r\n    position: relative;\r\n    z-index: 2;\r\n    height: calc(100% - 98px);\r\n    overflow: hidden;\r\n\r\n    .menu-container {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      overflow: hidden;\r\n      max-height: 100%;\r\n    }\r\n\r\n    .menu-list {\r\n      width: calc(100% - 240px);\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: flex-start;\r\n      position: relative;\r\n      margin-top: 30vh;\r\n      transition: margin-top 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n      &.has-active {\r\n        margin-top: 0;\r\n\r\n        .menu-item {\r\n          max-width: 310px;\r\n          position: relative;\r\n\r\n          &::before {\r\n            content: '';\r\n            display: block;\r\n            padding-top: 90.3%;\r\n            /* 282/310 ≈ 0.903 */\r\n          }\r\n        }\r\n\r\n        .u-top {\r\n          margin-top: 0;\r\n        }\r\n\r\n        .u-bottom {\r\n          margin-top: 25px;\r\n        }\r\n\r\n        .menu-item-first {\r\n          margin-right: -40px;\r\n\r\n          .menu-title {\r\n            margin-top: 8%;\r\n            margin-left: 18px;\r\n          }\r\n        }\r\n\r\n        .menu-item-second {\r\n          margin-right: -55px;\r\n          position: relative;\r\n\r\n          &::before {\r\n            content: '';\r\n            display: block;\r\n            padding-top: 84.5%;\r\n            /* 262/310 ≈ 0.845 */\r\n          }\r\n\r\n          .menu-title {\r\n            margin-top: 5%;\r\n          }\r\n        }\r\n\r\n        .menu-item-third {\r\n          margin-right: -40px;\r\n          position: relative;\r\n\r\n          &::before {\r\n            content: '';\r\n            display: block;\r\n            padding-top: 84.5%;\r\n            /* 262/310 ≈ 0.845 */\r\n          }\r\n\r\n          .menu-title {\r\n            margin-top: 5%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .menu-item {\r\n      flex: 1 1 0;\r\n      max-width: 475px;\r\n      min-width: 220px;\r\n      width: 100%;\r\n      position: relative;\r\n      border-radius: 24px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background-size: 100% 100%;\r\n      background-repeat: no-repeat;\r\n      cursor: pointer;\r\n      transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s, max-width 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n      margin: 0 8px;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .menu-item::before {\r\n      content: '';\r\n      display: block;\r\n      padding-top: 94.7%;\r\n    }\r\n\r\n    .menu-item>.menu-title,\r\n    .menu-item>.menu-icon {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      z-index: 1;\r\n\r\n    }\r\n\r\n    .u-top {\r\n      margin-top: 0;\r\n    }\r\n\r\n    .u-bottom {\r\n      margin-top: 44px;\r\n    }\r\n\r\n    .menu-item-first {\r\n      margin-right: -40px;\r\n\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 20px;\r\n        font-weight: bold;\r\n        margin-top: 8%;\r\n        margin-left: 18px;\r\n      }\r\n    }\r\n\r\n    .menu-item-second {\r\n      margin-right: -55px;\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        display: block;\r\n        padding-top: 84.5%;\r\n        /* 262/310 ≈ 0.845 */\r\n      }\r\n\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 20px;\r\n        font-weight: bold;\r\n        margin-top: 4%;\r\n      }\r\n    }\r\n\r\n    .menu-item-third {\r\n      margin-right: -40px;\r\n      position: relative;\r\n\r\n      &::before {\r\n        content: '';\r\n        display: block;\r\n        padding-top: 84.5%;\r\n      }\r\n\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 20px;\r\n        font-weight: bold;\r\n        margin-top: 4%;\r\n      }\r\n    }\r\n\r\n    .menu-item-fourth {\r\n      .menu-title {\r\n        color: #fff;\r\n        font-size: 20px;\r\n        font-weight: bold;\r\n        margin-top: 8%;\r\n        left: 42%;\r\n      }\r\n    }\r\n\r\n    .module-area {\r\n      width: 90%;\r\n      max-width: 1248px;\r\n      margin: 0 auto 0;\r\n      position: relative;\r\n      top: -70px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      overflow: hidden;\r\n      min-height: 500px;\r\n      opacity: 0;\r\n      animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;\r\n      height: auto;\r\n\r\n      @keyframes fadeIn {\r\n        from {\r\n          opacity: 0;\r\n        }\r\n\r\n        to {\r\n          opacity: 1;\r\n        }\r\n      }\r\n\r\n      .module-bg {\r\n        position: absolute;\r\n        width: 100%;\r\n        height: 100%;\r\n        z-index: -1;\r\n        left: 0;\r\n        // top: -20px;\r\n        top: -30px;\r\n        background: url('../img/module_bg.png') no-repeat center top;\r\n        background-size: 100% auto;\r\n        border-radius: 24px;\r\n        pointer-events: none;\r\n      }\r\n\r\n      .module-list {\r\n        display: grid;\r\n        grid-template-columns: repeat(5, 1fr);\r\n        gap: 20px;\r\n        width: 100%;\r\n        // padding: 70px 40px 40px 40px;\r\n        padding: 45px 40px 40px 40px;\r\n        position: relative;\r\n        z-index: 1;\r\n\r\n        @media (max-width: 1400px) {\r\n          grid-template-columns: repeat(4, 1fr);\r\n        }\r\n\r\n        @media (max-width: 1100px) {\r\n          grid-template-columns: repeat(3, 1fr);\r\n        }\r\n      }\r\n\r\n      .module-item {\r\n        min-width: 180px;\r\n        background: url('../img/module_item_bg.png') no-repeat center/100% 100%;\r\n        border-radius: 16px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 15px 20px 0;\r\n        height: 82px;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n        cursor: pointer;\r\n\r\n        .module-icon {\r\n          width: 32px;\r\n          height: 32px;\r\n        }\r\n\r\n        .module-title {\r\n          color: #fff;\r\n          font-size: 16px;\r\n          font-weight: bold;\r\n          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);\r\n          margin-left: 15px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .fade-slide-enter-active,\r\n  .fade-slide-leave-active {\r\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  }\r\n\r\n  .fade-slide-enter-from,\r\n  .fade-slide-leave-to {\r\n    opacity: 0;\r\n    transform: translateY(40px);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAiEA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AAAA,SAAAE,QAAA7G,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAA2G,qBAAA,QAAAvG,CAAA,GAAAJ,MAAA,CAAA2G,qBAAA,CAAA9G,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAwG,MAAA,WAAA7G,CAAA,WAAAC,MAAA,CAAA6G,wBAAA,CAAAhH,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAiC,KAAA,CAAAzG,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAAgH,cAAAjH,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAAuG,SAAA,CAAA3B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAAwG,SAAA,CAAAvG,CAAA,IAAAuG,SAAA,CAAAvG,CAAA,QAAAA,CAAA,OAAA2G,OAAA,CAAA1G,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAAgH,eAAA,CAAAlH,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAAgH,yBAAA,GAAAhH,MAAA,CAAAiH,gBAAA,CAAApH,CAAA,EAAAG,MAAA,CAAAgH,yBAAA,CAAAlH,CAAA,KAAA4G,OAAA,CAAA1G,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAA6G,wBAAA,CAAA/G,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAAkH,gBAAAlH,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAAmH,cAAA,CAAAnH,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAqH,eAAApH,CAAA,QAAAS,CAAA,GAAA4G,YAAA,CAAArH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAA4G,aAAArH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAA4G,WAAA,kBAAAvH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAsH,MAAA,GAAAC,MAAA,EAAAxH,CAAA;AADA,OAAOyH,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,KAAK;AAChE,SAASC,IAAI,EAAEC,UAAU,EAAEC,UAAU,QAAQ,yBAAyB;AACtE,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,oBAAoB,QAAQ,wBAAwB;AAV7D,IAAAC,WAAA,GAAe;EAAEvD,IAAI,EAAE;AAAgB,CAAC;;;;;IAWxC,IAAMwD,MAAM,GAAGd,SAAS,CAAC,CAAC;IAC1B,IAAMe,QAAQ,GAAGb,MAAM,CAAC,UAAU,CAAC;IACnC,IAAMc,YAAY,GAAGd,MAAM,CAAC,cAAc,CAAC;IAC3C,IAAMe,YAAY,GAAGf,MAAM,CAAC,eAAe,CAAC;IAC5C,IAAMgB,QAAQ,GAAGhB,MAAM,CAAC,UAAU,CAAC;IACnC,IAAMiB,YAAY,GAAGjB,MAAM,CAAC,cAAc,CAAC;IAC3C,IAAMkB,IAAI,GAAGlB,MAAM,CAAC,MAAM,CAAC;IAC3B;IACA,IAAMmB,KAAK,GAAGtB,QAAQ,CAAC,CAAC;IACxB,IAAMuB,QAAQ,GAAG,GAAGb,MAAM,CAACc,OAAO,wBAAwB;IAC1D,IAAMC,eAAe,GAAGvB,GAAG,CAAC,GAAGQ,MAAM,CAACc,OAAO,iCAAiCjB,IAAI,CAACzH,KAAK,CAAC4I,MAAM,EAAE,CAAC;IAClG,IAAMC,eAAe,GAAG,CACtB;MACEC,KAAK,EAAE,EAAE;MACTC,EAAE,EAAEC,OAAO,CAAC,sBAAsB,CAAC;MACnCC,QAAQ,EAAED,OAAO,CAAC,wBAAwB;IAC5C,CAAC,EACD;MACEF,KAAK,EAAE,EAAE;MACTC,EAAE,EAAEC,OAAO,CAAC,sBAAsB,CAAC;MACnCC,QAAQ,EAAED,OAAO,CAAC,wBAAwB;IAC5C,CAAC,EACD;MACEF,KAAK,EAAE,EAAE;MACTC,EAAE,EAAEC,OAAO,CAAC,sBAAsB,CAAC;MACnCC,QAAQ,EAAED,OAAO,CAAC,wBAAwB;IAC5C,CAAC,EACD;MACEF,KAAK,EAAE,EAAE;MACTC,EAAE,EAAEC,OAAO,CAAC,sBAAsB,CAAC;MACnCC,QAAQ,EAAED,OAAO,CAAC,wBAAwB;IAC5C,CAAC,CACF;IACD,IAAME,eAAe,GAAG9B,GAAG,CAAC,IAAI,CAAC;IACjC,IAAM+B,KAAK,GAAG/B,GAAG,CAAC,KAAK,CAAC;IAExBE,SAAS,CAAC,YAAM;MACdE,QAAQ,CAAC,YAAM;QACb4B,UAAU,CAAC,YAAM;UACf,IAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;UAC3DC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEN,QAAQ,CAAC;UACjC,IAAIA,QAAQ,EAAE;YAAEF,KAAK,CAACnJ,KAAK,GAAGqJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,QAAQ,CAAC,KAAK,CAAC;UAAC;QAC1D,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAMC,SAAS,GAAGtC,QAAQ,CAAC,YAAM;MAC/B,IAAMuC,QAAQ,GAAG,CAAC1B,YAAY,CAACpI,KAAK,IAAI,EAAE,EAAEsG,MAAM,CAAC,UAAAyD,IAAI;QAAA,OAAIA,IAAI,CAACC,SAAS,KAAK,WAAW;MAAA,EAAC;MAC1F,OAAOF,QAAQ,CAACG,GAAG,CAAC,UAACF,IAAI,EAAEG,GAAG;QAAA,OAAA1D,aAAA,CAAAA,aAAA,KACzBqC,eAAe,CAACqB,GAAG,CAAC;UACvBpB,KAAK,EAAEiB,IAAI,CAACtF,IAAI;UAChB0F,EAAE,EAAEJ,IAAI,CAACI,EAAE;UACXC,GAAG,EAAEL,IAAI,CAACM;QAAW;MAAA,CACrB,CAAC;IACL,CAAC,CAAC;IACF,IAAMC,QAAQ,GAAG/C,QAAQ,CAAC,YAAM;MAC9B,IAAMuC,QAAQ,GAAG,CAAC1B,YAAY,CAACpI,KAAK,IAAI,EAAE,EAAEsG,MAAM,CAAC,UAAAyD,IAAI;QAAA,OAAIA,IAAI,CAACC,SAAS,KAAK,WAAW;MAAA,EAAC;MAC1F,IAAIO,GAAG,GAAGT,QAAQ,CAACG,GAAG,CAAC,UAAAF,IAAI;QAAA,OAAI,CAACA,IAAI,CAACS,QAAQ,IAAI,EAAE,EAAEP,GAAG,CAAC,UAAAQ,KAAK;UAAA,OAAK;YACjEN,EAAE,EAAEM,KAAK,CAACN,EAAE;YACZ1F,IAAI,EAAEgG,KAAK,CAAChG,IAAI;YAChBiG,IAAI,EAAED,KAAK,CAACC,IAAI;YAChBF,QAAQ,EAAEC,KAAK,CAACD,QAAQ;YACxBR,SAAS,EAAES,KAAK,CAACT,SAAS;YAC1BW,YAAY,EAAEF,KAAK,CAACE,YAAY;YAChCC,aAAa,EAAEH,KAAK,CAACG,aAAa;YAClCR,GAAG,EAAEK,KAAK,CAACL,GAAG,IAAIK,KAAK,CAACJ;UAC1B,CAAC;QAAA,CAAC,CAAC;MAAA,CACH,CAAC;MACD,IAAMQ,MAAM,GAAGN,GAAG,CAACN,GAAG,CAAC,UAAAa,QAAQ;QAAA,OAC7BA,QAAQ,CAACxE,MAAM,CAAC,UAAAyD,IAAI;UAAA,OAClBA,IAAI,CAACtF,IAAI,KAAK,MAAM,IAAIsF,IAAI,CAACtF,IAAI,KAAK,IAAI;QAAA,CAC5C,CAAC;MAAA,CACH,CAAC,CAAC6B,MAAM,CAAC,UAAAwE,QAAQ;QAAA,OAAIA,QAAQ,CAACzG,MAAM,GAAG,CAAC;MAAA,EAAC;MACzC,OAAOwG,MAAM;IACf,CAAC,CAAC;IACF,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAIhB,IAAI,EAAK;MAAE5B,YAAY,CAAC4B,IAAI,CAAC;IAAC,CAAC;IACxD,IAAMiB,WAAU,GAAG,SAAbA,UAAUA,CAAGC,QAAQ,EAAI;MAC7B,IAAIC,WAAW,GAAG,EAAE;MACpB,KAAK,IAAIjL,CAAC,GAAG,CAAC,EAAEkL,GAAG,GAAGF,QAAQ,CAAC5G,MAAM,EAAEpE,CAAC,GAAGkL,GAAG,EAAElL,CAAC,EAAE,EAAE;QACnDiL,WAAW,CAAClH,IAAI,CAAC;UACfmG,EAAE,EAAEc,QAAQ,CAAChL,CAAC,CAAC,CAACmL,MAAM;UAAE3G,IAAI,EAAEwG,QAAQ,CAAChL,CAAC,CAAC,CAACwE,IAAI;UAC9CuF,SAAS,EAAEiB,QAAQ,CAAChL,CAAC,CAAC,CAAC+J,SAAS;UAChCW,YAAY,EAAEM,QAAQ,CAAChL,CAAC,CAAC,CAAC0K,YAAY;UACtCC,aAAa,EAAEK,QAAQ,CAAChL,CAAC,CAAC,CAAC2K,aAAa;UACxCF,IAAI,EAAEO,QAAQ,CAAChL,CAAC,CAAC,CAACoL,OAAO,GAAG,GAAGpE,GAAG,CAACqE,OAAO,CAACL,QAAQ,CAAChL,CAAC,CAAC,CAACoL,OAAO,CAAC,EAAE,GAAG5C,QAAQ;UAC5E2B,GAAG,EAAEa,QAAQ,CAAChL,CAAC,CAAC,CAACoK,WAAW;UAC5BG,QAAQ,EAAEQ,WAAU,CAACC,QAAQ,CAAChL,CAAC,CAAC,CAACuK,QAAQ,IAAI,EAAE;QACjD,CAAC,CAAC;MACJ;MACA,OAAOU,WAAW;IACpB,CAAC;IACD;IACA,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB7B,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrBzB,QAAQ,CAAC;QAAEsD,GAAG,EAAE,WAAW;QAAExL,KAAK,EAAE;MAAY,CAAC,CAAC;IACpD,CAAC;IACD;IACA,IAAMyL,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB3D,YAAY,CAAC4D,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QAC9CC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBzK,IAAI,EAAE;MACR,CAAC,CAAC,CAACuB,IAAI,CAAC,YAAM;QAAEmJ,QAAQ,CAAC,QAAQ,CAAC;MAAC,CAAC,CAAC,CAAClG,KAAK,CAAC,YAAM;QAAEkC,SAAS,CAAC;UAAE1G,IAAI,EAAE,MAAM;UAAE2K,OAAO,EAAE;QAAQ,CAAC,CAAC;MAAC,CAAC,CAAC;IACtG,CAAC;IACD,IAAMD,QAAQ;MAAA,IAAAE,KAAA,GAAAhG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsH,QAAOC,IAAI;QAAA,IAAAC,mBAAA,EAAAC,IAAA,EAAAC,sBAAA,EAAAC,uBAAA;QAAA,OAAA/M,mBAAA,GAAAuB,IAAA,UAAAyL,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAApH,IAAA,GAAAoH,QAAA,CAAA/I,IAAA;YAAA;cAAA+I,QAAA,CAAA/I,IAAA;cAAA,OACHyD,GAAG,CAAC4E,QAAQ,CAAC,CAAC;YAAA;cAAAK,mBAAA,GAAAK,QAAA,CAAAtJ,IAAA;cAA7BkJ,IAAI,GAAAD,mBAAA,CAAJC,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChB3C,cAAc,CAACgD,KAAK,CAAC,CAAC;gBAChBJ,sBAAsB,GAAGK,YAAY,CAAChD,OAAO,CAAC,wBAAwB,CAAC;gBAC7E,IAAI2C,sBAAsB,EAAE;kBACpBC,uBAAuB,GAAGI,YAAY,CAAChD,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE;kBACrFxB,MAAM,CAACjE,IAAI,CAAC;oBAAE0I,IAAI,EAAEN,sBAAsB;oBAAEO,KAAK,EAAEN,uBAAuB,GAAG/C,IAAI,CAACC,KAAK,CAAC8C,uBAAuB,CAAC,GAAG,CAAC;kBAAE,CAAC,CAAC;gBAC1H,CAAC,MAAM;kBACLpE,MAAM,CAACjE,IAAI,CAAC;oBAAE0I,IAAI,EAAE;kBAAa,CAAC,CAAC;gBACrC;gBACAlE,KAAK,CAACoE,MAAM,CAAC,UAAU,CAAC;gBACxB7E,oBAAoB,CAAC,CAAC;gBACtBF,SAAS,CAAC;kBAAEiE,OAAO,EAAEG,IAAI;kBAAEY,SAAS,EAAE,IAAI;kBAAE1L,IAAI,EAAE;gBAAU,CAAC,CAAC;cAChE;YAAC;YAAA;cAAA,OAAAoL,QAAA,CAAAjH,IAAA;UAAA;QAAA,GAAA0G,OAAA;MAAA,CACF;MAAA,gBAfKH,QAAQA,CAAAiB,EAAA;QAAA,OAAAf,KAAA,CAAA9F,KAAA,OAAAD,SAAA;MAAA;IAAA,GAeb;IACD;IACA,IAAM+G,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAMjD,QAAQ,GAAG,CAAC1B,YAAY,CAACpI,KAAK,IAAI,EAAE,EAAEsG,MAAM,CAAC,UAAAyD,IAAI;QAAA,OAAIA,IAAI,CAACC,SAAS,KAAK,WAAW;MAAA,EAAC;MAC1F,IAAMgD,eAAe,GAAGlD,QAAQ,CAACmD,OAAO,CAAC,UAAAlD,IAAI;QAAA,OAAIA,IAAI,CAACS,QAAQ;MAAA,EAAC,CAAC0C,IAAI,CAAC,UAAAzC,KAAK;QAAA,OAAIA,KAAK,CAAChG,IAAI,KAAK,MAAM;MAAA,EAAC;MACpG0D,YAAY,CAAC6E,eAAe,CAAC;IAC/B,CAAC;IACD;IACA,IAAMG,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpB,IAAMrD,QAAQ,GAAG,CAAC1B,YAAY,CAACpI,KAAK,IAAI,EAAE,EAAEsG,MAAM,CAAC,UAAAyD,IAAI;QAAA,OAAIA,IAAI,CAACC,SAAS,KAAK,WAAW;MAAA,EAAC;MAC1F,IAAMoD,WAAW,GAAGtD,QAAQ,CAACmD,OAAO,CAAC,UAAAlD,IAAI;QAAA,OAAIA,IAAI,CAACS,QAAQ;MAAA,EAAC,CAAC0C,IAAI,CAAC,UAAAzC,KAAK;QAAA,OAAIA,KAAK,CAAChG,IAAI,KAAK,IAAI;MAAA,EAAC;MAC9F0D,YAAY,CAACiF,WAAW,CAAC;IAC3B,CAAC;IACD;IACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;MAAA,IAAAC,qBAAA;MACjC;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,GAAAA,qBAAA,GAACjD,QAAQ,CAACtK,KAAK,CAACsN,KAAK,CAAC,cAAAC,qBAAA,eAArBA,qBAAA,CAAuBlJ,MAAM,GAAE;QAClCwD,SAAS,CAAC2F,IAAI,CAAC,WAAW,CAAC;QAC3B;MACF;MACAtE,eAAe,CAAClJ,KAAK,GAAGkJ,eAAe,CAAClJ,KAAK,KAAKsN,KAAK,GAAG,IAAI,GAAGA,KAAK;IACxE,CAAC;IACD,IAAMG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIC,KAAK,EAAK;MACpC;MACA,IAAIA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,YAAY,CAAC,IAAIF,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,EAAE;QAC9E;MACF;MACA;MACA1E,eAAe,CAAClJ,KAAK,GAAG,IAAI;IAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}