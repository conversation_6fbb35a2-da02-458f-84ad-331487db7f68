{"ast": null, "code": "import { renderSlot as _renderSlot, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, Transition as _Transition, withCtx as _withCtx, createVNode as _createVNode, normalizeClass as _normalizeClass } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return $setup.isShow ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: _normalizeClass([\"setting-popup-window\", {\n      'setting-popup-window-mac': $setup.isMac\n    }]),\n    onClick: _withModifiers($setup.closeClick, [\"stop\"])\n  }, [_createVNode(_Transition, {\n    \"enter-active-class\": \"animate__animated animate__fadeInRight animate__faster\",\n    \"leave-active-class\": \"animate__animated animate__fadeOutRight animate__faster\"\n  }, {\n    default: _withCtx(function () {\n      return [$setup.bodyShow ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"setting-popup-window-body forbidSelect\",\n        onClick: _cache[0] || (_cache[0] = _withModifiers(function () {}, [\"stop\"]))\n      }, [_renderSlot(_ctx.$slots, \"default\")])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 3 /* FORWARDED */\n  })], 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true);\n}", "map": {"version": 3, "names": ["$setup", "isShow", "_createElementBlock", "key", "class", "_normalizeClass", "isMac", "onClick", "_withModifiers", "closeClick", "_createVNode", "_Transition", "default", "_withCtx", "bodyShow", "_cache", "_renderSlot", "_ctx", "$slots", "_createCommentVNode", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\setting-popup-window\\setting-popup-window.vue"], "sourcesContent": ["<template>\r\n  <div class=\"setting-popup-window\" :class=\"{ 'setting-popup-window-mac': isMac }\" @click.stop=\"closeClick\"\r\n    v-if=\"isShow\">\r\n    <transition enter-active-class=\"animate__animated animate__fadeInRight animate__faster\"\r\n      leave-active-class=\"animate__animated animate__fadeOutRight animate__faster\">\r\n      <div class=\"setting-popup-window-body forbidSelect\" @click.stop v-if=\"bodyShow\">\r\n        <slot></slot>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SettingPopupWindow' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch, nextTick } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  beforeClose: Function\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\nconst isMac = window.electron?.isMac\r\n\r\nconst show = ref(props.modelValue)\r\nconst isShow = ref(false)\r\nconst bodyShow = ref(false)\r\n\r\nwatch(() => props.modelValue, () => {\r\n  show.value = props.modelValue\r\n  if (props.modelValue) {\r\n    isShow.value = true\r\n    nextTick(() => { bodyShow.value = true })\r\n  } else {\r\n    bodyShow.value = false\r\n    setTimeout(() => { isShow.value = false }, 99)\r\n  }\r\n})\r\nconst closeClick = () => {\r\n  if (typeof props.beforeClose === 'function') {\r\n    props.beforeClose(() => { emit('update:modelValue', false) })\r\n  } else {\r\n    emit('update:modelValue', false)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.setting-popup-window {\r\n  width: 100%;\r\n  height: calc(100% - 66px);\r\n  position: absolute;\r\n  top: 66px;\r\n  left: 0;\r\n  z-index: 998;\r\n  overflow: hidden;\r\n\r\n  &.setting-popup-window-mac {\r\n    height: calc(100% - 56px);\r\n    top: 56px;\r\n  }\r\n\r\n  .setting-popup-window-body {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    min-width: 320px;\r\n    height: 100%;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;SAEUA,MAAA,CAAAC,MAAM,I,cADdC,mBAAA,CAQM;IATRC,GAAA;IACOC,KAAK,EADZC,eAAA,EACa,sBAAsB;MAAA,4BAAuCL,MAAA,CAAAM;IAAK;IAAKC,OAAK,EADzFC,cAAA,CACgGR,MAAA,CAAAS,UAAU;MAEtGC,YAAA,CAKaC,WAAA;IALD,oBAAkB,EAAC,wDAAwD;IACrF,oBAAkB,EAAC;;IAJzBC,OAAA,EAAAC,QAAA,CAOO;MAAA,OAGC,CALoEb,MAAA,CAAAc,QAAQ,I,cAA9EZ,mBAAA,CAEM;QAPZC,GAAA;QAKWC,KAAK,EAAC,wCAAwC;QAAEG,OAAK,EAAAQ,MAAA,QAAAA,MAAA,MALhEP,cAAA,CAK0D,cAAW;UAC7DQ,WAAA,CAAaC,IAAA,CAAAC,MAAA,a,KANrBC,mBAAA,e;;IAAAC,CAAA;yBAAAD,mBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}