"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[7761],{67761:function(t,e,r){r.d(e,{J0:function(){return w},_S:function(){return y},iO:function(){return m}});var n=r(4955),o=r(88609);function a(t){return s(t)||c(t)||u(t)||i()}function i(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}function c(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function s(t){if(Array.isArray(t))return l(t)}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */h=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function l(t,e,r,n){var a=e&&e.prototype instanceof m?e:m,i=Object.create(a.prototype),u=new _(n||[]);return o(i,"_invoke",{value:N(t,r,u)}),i}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=l;var p="suspendedStart",d="suspendedYield",v="executing",g="completed",y={};function m(){}function w(){}function b(){}var x={};s(x,i,(function(){return this}));var I=Object.getPrototypeOf,T=I&&I(I(A([])));T&&T!==r&&n.call(T,i)&&(x=T);var L=b.prototype=m.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(o,a,i,u){var c=f(t[o],t,a);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==typeof l&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,i,u)}),(function(t){r("throw",t,i,u)})):e.resolve(l).then((function(t){s.value=t,i(s)}),(function(t){return r("throw",t,i,u)}))}u(c.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function N(e,r,n){var o=p;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===g){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var c=O(u,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var s=f(e,r,n);if("normal"===s.type){if(o=n.done?g:d,s.arg===y)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=g,n.method="throw",n.arg=s.arg)}}}function O(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,O(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=f(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function M(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function _(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function A(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(typeof e+" is not iterable")}return w.prototype=b,o(L,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=s(b,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,c,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},k(E.prototype),s(E.prototype,u,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new E(l(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(L),s(L,c,"Generator"),s(L,i,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=A,_.prototype={constructor:_,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(M),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),M(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;M(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:A(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function f(t,e,r,n,o,a,i){try{var u=t[a](i),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function p(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){f(a,n,o,i,u,"next",t)}function u(t){f(a,n,o,i,u,"throw",t)}i(void 0)}))}}var d=function(){var t=p(h().mark((function t(e){var r,a;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,n.A.userInfoByAccount({accountId:e.slice(o.TC.value.length)});case 3:return r=t.sent,a=r.data,t.abrupt("return",{uid:e,id:a.accountId,name:a.userName,img:a.photo||a.headImg,userInfo:{userId:a.id,userName:a.userName,photo:a.photo,headImg:a.headImg}});case 8:return t.prev=8,t.t0=t["catch"](0),t.abrupt("return","");case 11:case"end":return t.stop()}}),t,null,[[0,8]])})));return function(e){return t.apply(this,arguments)}}(),v=function(){var t=p(h().mark((function t(e){var r,a,i,u,c;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,n.A.userInfoByAccount({accountIds:e});case 3:for(r=t.sent,a=r.data,i=[],u=0;u<a.length;u++)c=a[u],i.push({uid:o.TC.value+c.accountId,id:c.accountId,name:c.userName,img:c.photo||c.headImg,userInfo:{userId:a.id,userName:a.userName,photo:a.photo,headImg:a.headImg}});return t.abrupt("return",i);case 10:return t.prev=10,t.t0=t["catch"](0),t.abrupt("return",[]);case 13:case"end":return t.stop()}}),t,null,[[0,10]])})));return function(e){return t.apply(this,arguments)}}(),g=function(){var t=p(h().mark((function t(e){var r,a,i,u,c,s,l;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,n.A.chatGroupList({ids:e});case 3:for(r=t.sent,a=r.data,i=[],u=0;u<a.length;u++)l=a[u],i.push({uid:o.TC.value+l.id,id:l.id,name:l.groupName,img:l.groupImg,chatGroupType:"0"!==(null===l||void 0===l||null===(c=l.chatGroupType)||void 0===c?void 0:c.value)?null===l||void 0===l||null===(s=l.chatGroupType)||void 0===s||null===(s=s.name)||void 0===s?void 0:s.slice(0,2):""});return t.abrupt("return",i);case 10:return t.prev=10,t.t0=t["catch"](0),t.abrupt("return",[]);case 13:case"end":return t.stop()}}),t,null,[[0,10]])})));return function(e){return t.apply(this,arguments)}}(),y=function(){var t=p(h().mark((function t(e){var r,o;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,n.A.chatGroupMemberList({pageNo:1,pageSize:9999,query:{chatGroupId:e}});case 2:return r=t.sent,o=r.data,t.abrupt("return",o);case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),m=function(){var t=p(h().mark((function t(e,r,n){var i,u,c,s,l,f,p,d;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(i=!0,u=[],c=[],s=n.map((function(t){return t.uid})),l=0;l<e.length;l++)f=e[l],null!==s&&void 0!==s&&s.includes(f.targetId)||(i=!1),1===f.conversationType&&u.push(f.targetId.slice(o.TC.value.length)),3===f.conversationType&&c.push(f.targetId.slice(o.TC.value.length));if(!i||!r){t.next=9;break}return t.abrupt("return",n);case 9:return t.next=11,v(u);case 11:return p=t.sent,t.next=14,g(c);case 14:return d=t.sent,t.abrupt("return",[].concat(a(p),a(d)));case 16:case"end":return t.stop()}}),t)})));return function(e,r,n){return t.apply(this,arguments)}}(),w=function(){var t=p(h().mark((function t(e,r){var n,i,u,c,s,l,f,p,v,g,y,m,w,b,x,I,T,L,k,E,N,O,j,M,_,A,C,S,G=arguments;return h().wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(n=G.length>2&&void 0!==G[2]?G[2]:[],i=G.length>3?G[3]:void 0,u={},c=0;c<i.length;c++)s=i[c],u[s.uid]=s;l=[],f=[],p=[],v=0;case 8:if(!(v<e.length)){t.next=28;break}if(g=e[v],y=u[g.targetId]||"",!y){t.next=25;break}if(l.push(g.targetId),w="","RC:RcCmd"!==(null===(m=g.latestMessage)||void 0===m?void 0:m.messageType)){t.next=24;break}if(x=(null===(b=g.latestMessage)||void 0===b?void 0:b.senderUserId)===o.TC.value+o.kQ.value.accountId,!x){t.next=20;break}w="你撤回了一条消息",t.next=24;break;case 20:return t.next=22,d(null===(I=g.latestMessage)||void 0===I?void 0:I.senderUserId);case 22:T=t.sent,w=T.name+"撤回了一条消息";case 24:g.isTop?f.push({isTop:g.isTop,isNotInform:g.notificationStatus,id:g.targetId,targetId:g.targetId,type:g.conversationType,chatObjectInfo:y,sentTime:null===(L=g.latestMessage)||void 0===L?void 0:L.sentTime,messageType:(null===(k=g.latestMessage)||void 0===k?void 0:k.messageType)||"RC:TxtMsg",content:null===(E=g.latestMessage)||void 0===E?void 0:E.content,revocationMessage:w,count:g.unreadMessageCount}):p.push({isTop:g.isTop,isNotInform:g.notificationStatus,id:g.targetId,targetId:g.targetId,type:g.conversationType,chatObjectInfo:y,sentTime:null===(N=g.latestMessage)||void 0===N?void 0:N.sentTime,messageType:(null===(O=g.latestMessage)||void 0===O?void 0:O.messageType)||"RC:TxtMsg",content:null===(j=g.latestMessage)||void 0===j?void 0:j.content,revocationMessage:w,count:g.unreadMessageCount});case 25:v++,t.next=8;break;case 28:for(M=0;M<r.length;M++)_=r[M],null!==l&&void 0!==l&&l.includes(_.id)||(_.isTop?f.push(_):p.push(_));for(A=0;A<n.length;A++)C=n[A],null!==l&&void 0!==l&&l.includes(C.id)||(C.isTop?f.push(C):p.push(C));return S=[].concat(a(f.sort((function(t,e){return e.sentTime-t.sentTime}))),a(p.sort((function(t,e){return e.sentTime-t.sentTime})))),console.log(S),t.abrupt("return",S);case 33:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}()}}]);