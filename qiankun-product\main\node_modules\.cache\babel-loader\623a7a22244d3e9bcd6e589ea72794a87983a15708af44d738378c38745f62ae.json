{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue';\nimport { useStore } from 'vuex';\nimport { format } from 'common/js/time.js';\nimport { openConfig } from 'common/js/system_var.js';\nimport { ElMessage } from 'element-plus';\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue';\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue';\nimport AiReportGeneraDetails from './component/AiReportGeneraDetails';\nvar __default__ = {\n  name: 'AiReportGenera'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var IssueAnalysisIcon = '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#232;&#174;&#174;&#233;&#162;&#152;&#229;&#136;&#134;&#230;&#158;&#144;&#230;&#138;&#165;&#229;&#145;&#138;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_505)\"/><g id=\"Vector\"><path d=\"M38.5453 30.4309H20.864H12.823H10.4115C9.77195 30.4309 9.15857 30.685 8.70632 31.1373C8.25407 31.5895 8 32.2029 8 32.8425V36.0578C8 36.3745 8.06238 36.688 8.18358 36.9806C8.30478 37.2731 8.48242 37.5389 8.70636 37.7628C8.9303 37.9867 9.19614 38.1642 9.48872 38.2854C9.78129 38.4065 10.0949 38.4688 10.4115 38.4687H38.5453C38.8505 38.4686 39.1493 38.3817 39.4069 38.218C39.6645 38.0544 39.8702 37.8208 40 37.5445C39.5449 37.1674 39.1786 36.6945 38.9271 36.1596C38.6757 35.6247 38.5453 35.0409 38.5453 34.4498C38.5453 33.8588 38.6757 33.275 38.9271 32.74C39.1786 32.2051 39.5449 31.7322 40 31.3551C39.8702 31.0789 39.6645 30.8453 39.4069 30.6816C39.1493 30.518 38.8505 30.431 38.5453 30.4309Z\" fill=\"white\"/><path d=\"M38.5453 19.981H20.864H12.823H10.4115C9.77195 19.981 9.15857 20.2351 8.70632 20.6873C8.25407 21.1396 8 21.753 8 22.3925V25.6079C8 26.2475 8.25407 26.8609 8.70632 27.3131C9.15857 27.7653 9.77195 28.0194 10.4115 28.0194H12.823H20.864H38.5453C38.8505 28.0193 39.1493 27.9324 39.4069 27.7687C39.6645 27.6051 39.8702 27.3715 40 27.0953C39.5449 26.7181 39.1785 26.2451 38.9271 25.7102C38.6756 25.1752 38.5453 24.5913 38.5453 24.0002C38.5453 23.4091 38.6756 22.8253 38.9271 22.2903C39.1785 21.7553 39.5449 21.2824 40 20.9052C39.8702 20.6289 39.6645 20.3954 39.4069 20.2317C39.1493 20.068 38.8505 19.9811 38.5453 19.981Z\" fill=\"white\"/><path d=\"M10.4115 17.5695H12.823H20.864H38.5453C38.8505 17.5694 39.1493 17.4825 39.4069 17.3188C39.6645 17.1552 39.8702 16.9216 40 16.6453C39.5449 16.2682 39.1786 15.7953 38.9271 15.2604C38.6757 14.7255 38.5453 14.1417 38.5453 13.5506C38.5453 12.9595 38.6757 12.3758 38.9271 11.8408C39.1786 11.3059 39.5449 10.833 40 10.4559C39.8702 10.1797 39.6645 9.94607 39.4069 9.78242C39.1493 9.61877 38.8505 9.53182 38.5453 9.53174H10.4115C10.0949 9.53165 9.78129 9.59395 9.48872 9.71507C9.19614 9.83619 8.9303 10.0138 8.70636 10.2376C8.48242 10.4615 8.30478 10.7273 8.18358 11.0199C8.06238 11.3124 8 11.626 8 11.9426V15.158C8 15.7976 8.25407 16.4109 8.70632 16.8632C9.15857 17.3154 9.77195 17.5695 10.4115 17.5695Z\" fill=\"white\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 23.8C25 23.3582 25.3582 23 25.8 23H33.8C34.2418 23 34.6 23.3582 34.6 23.8C34.6 24.2418 34.2418 24.6 33.8 24.6H25.8C25.3582 24.6 25 24.2418 25 23.8Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 34.8C25 34.3582 25.3582 34 25.8 34H33.8C34.2418 34 34.6 34.3582 34.6 34.8C34.6 35.2418 34.2418 35.6 33.8 35.6H25.8C25.3582 35.6 25 35.2418 25 34.8Z\" fill=\"#0964E3\"/><path id=\"Vector_2\" d=\"M18.75 11.2C18.75 11.0895 18.6605 11 18.55 11H12.2C12.0895 11 12 11.0895 12 11.2V22.555C12 22.7275 12.2039 22.8191 12.3329 22.7044L15.2421 20.1181C15.3179 20.0508 15.4321 20.0508 15.5079 20.1181L18.4171 22.7044C18.5461 22.8191 18.75 22.7275 18.75 22.555V11.2Z\" fill=\"url(#paint1_linear_106_505)\"/></g><defs><linearGradient id=\"paint0_linear_106_505\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_505\" x1=\"15.375\" y1=\"11\" x2=\"15.375\" y2=\"23.0004\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient></defs></svg>';\n    var IssueAnalysisIconRD = '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#232;&#174;&#174;&#233;&#162;&#152;&#229;&#136;&#134;&#230;&#158;&#144;&#230;&#138;&#165;&#229;&#145;&#138;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_505)\"/><g id=\"Vector\"><path d=\"M38.5453 30.4309H20.864H12.823H10.4115C9.77195 30.4309 9.15857 30.685 8.70632 31.1373C8.25407 31.5895 8 32.2029 8 32.8425V36.0578C8 36.3745 8.06238 36.688 8.18358 36.9806C8.30478 37.2731 8.48242 37.5389 8.70636 37.7628C8.9303 37.9867 9.19614 38.1642 9.48872 38.2854C9.78129 38.4065 10.0949 38.4688 10.4115 38.4687H38.5453C38.8505 38.4686 39.1493 38.3817 39.4069 38.218C39.6645 38.0544 39.8702 37.8208 40 37.5445C39.5449 37.1674 39.1786 36.6945 38.9271 36.1596C38.6757 35.6247 38.5453 35.0409 38.5453 34.4498C38.5453 33.8588 38.6757 33.275 38.9271 32.74C39.1786 32.2051 39.5449 31.7322 40 31.3551C39.8702 31.0789 39.6645 30.8453 39.4069 30.6816C39.1493 30.518 38.8505 30.431 38.5453 30.4309Z\" fill=\"white\"/><path d=\"M38.5453 19.981H20.864H12.823H10.4115C9.77195 19.981 9.15857 20.2351 8.70632 20.6873C8.25407 21.1396 8 21.753 8 22.3925V25.6079C8 26.2475 8.25407 26.8609 8.70632 27.3131C9.15857 27.7653 9.77195 28.0194 10.4115 28.0194H12.823H20.864H38.5453C38.8505 28.0193 39.1493 27.9324 39.4069 27.7687C39.6645 27.6051 39.8702 27.3715 40 27.0953C39.5449 26.7181 39.1785 26.2451 38.9271 25.7102C38.6756 25.1752 38.5453 24.5913 38.5453 24.0002C38.5453 23.4091 38.6756 22.8253 38.9271 22.2903C39.1785 21.7553 39.5449 21.2824 40 20.9052C39.8702 20.6289 39.6645 20.3954 39.4069 20.2317C39.1493 20.068 38.8505 19.9811 38.5453 19.981Z\" fill=\"white\"/><path d=\"M10.4115 17.5695H12.823H20.864H38.5453C38.8505 17.5694 39.1493 17.4825 39.4069 17.3188C39.6645 17.1552 39.8702 16.9216 40 16.6453C39.5449 16.2682 39.1786 15.7953 38.9271 15.2604C38.6757 14.7255 38.5453 14.1417 38.5453 13.5506C38.5453 12.9595 38.6757 12.3758 38.9271 11.8408C39.1786 11.3059 39.5449 10.833 40 10.4559C39.8702 10.1797 39.6645 9.94607 39.4069 9.78242C39.1493 9.61877 38.8505 9.53182 38.5453 9.53174H10.4115C10.0949 9.53165 9.78129 9.59395 9.48872 9.71507C9.19614 9.83619 8.9303 10.0138 8.70636 10.2376C8.48242 10.4615 8.30478 10.7273 8.18358 11.0199C8.06238 11.3124 8 11.626 8 11.9426V15.158C8 15.7976 8.25407 16.4109 8.70632 16.8632C9.15857 17.3154 9.77195 17.5695 10.4115 17.5695Z\" fill=\"white\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 23.8C25 23.3582 25.3582 23 25.8 23H33.8C34.2418 23 34.6 23.3582 34.6 23.8C34.6 24.2418 34.2418 24.6 33.8 24.6H25.8C25.3582 24.6 25 24.2418 25 23.8Z\" fill=\"#F54747\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 34.8C25 34.3582 25.3582 34 25.8 34H33.8C34.2418 34 34.6 34.3582 34.6 34.8C34.6 35.2418 34.2418 35.6 33.8 35.6H25.8C25.3582 35.6 25 35.2418 25 34.8Z\" fill=\"#F54747\"/><path id=\"Vector_2\" d=\"M18.75 11.2C18.75 11.0895 18.6605 11 18.55 11H12.2C12.0895 11 12 11.0895 12 11.2V22.555C12 22.7275 12.2039 22.8191 12.3329 22.7044L15.2421 20.1181C15.3179 20.0508 15.4321 20.0508 15.5079 20.1181L18.4171 22.7044C18.5461 22.8191 18.75 22.7275 18.75 22.555V11.2Z\" fill=\"url(#paint1_linear_106_505)\"/></g><defs><linearGradient id=\"paint0_linear_106_505\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F54747\"/><stop offset=\"0.995\" stop-color=\"#F54747\"/></linearGradient><linearGradient id=\"paint1_linear_106_505\" x1=\"15.375\" y1=\"11\" x2=\"15.375\" y2=\"23.0004\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F54747\"/><stop offset=\"1\" stop-color=\"#F54747\"/></linearGradient></defs></svg>';\n    var MeetingRoomIcon = '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#229;&#176;&#143;&#231;&#187;&#147;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_745)\"/><g id=\"&#231;&#187;&#132; 2771\"><g id=\"&#231;&#187;&#132; 2769\"><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1808\" d=\"M31.4467 41.261C31.0716 41.636 30.563 41.8467 30.0326 41.8467L12.7926 41.8467C12.0103 41.8467 11.26 41.5359 10.7068 40.9827C10.1536 40.4295 9.84277 39.6792 9.84277 38.8968L9.84278 10.1667C9.84278 9.38431 10.1536 8.634 10.7068 8.08079C11.26 7.52758 12.0103 7.2168 12.7926 7.2168L35.2076 7.2168C35.9899 7.2168 36.7402 7.52759 37.2934 8.0808C37.8467 8.634 38.1574 9.38432 38.1574 10.1667L38.1574 33.7231C38.1574 34.2537 37.9467 34.7624 37.5715 35.1375L31.4467 41.261Z\" fill=\"white\"/><rect id=\"Rectangle 34625139\" x=\"15\" y=\"30\" width=\"11\" height=\"4\" rx=\"1\" fill=\"url(#paint1_linear_106_745)\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 362 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.0259 16.8C15.0259 16.3582 15.3841 16 15.8259 16H32.1747C32.6165 16 32.9747 16.3582 32.9747 16.8C32.9747 17.2418 32.6165 17.6 32.1747 17.6H15.8259C15.3841 17.6 15.0259 17.2418 15.0259 16.8Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 362 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.0259 21.8C15.0259 21.3582 15.3841 21 15.8259 21H32.1747C32.6165 21 32.9747 21.3582 32.9747 21.8C32.9747 22.2418 32.6165 22.6 32.1747 22.6H15.8259C15.3841 22.6 15.0259 22.2418 15.0259 21.8Z\" fill=\"#0964E3\"/></g></g><defs><linearGradient id=\"paint0_linear_106_745\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_745\" x1=\"20.5\" y1=\"30\" x2=\"20.5\" y2=\"34\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient></defs></svg>';\n    var HotPointIcon = '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#231;&#131;&#173;&#231;&#130;&#185;&#230;&#138;&#165;&#229;&#145;&#138;\" clip-path=\"url(#clip0_106_531)\"><g id=\"&#230;&#148;&#191;&#231;&#173;&#150;&#230;&#150;&#135;&#228;&#187;&#182;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 7578\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_531)\"/><g id=\"Group 1000006979\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6277\" d=\"M8.25 11.9333L8.25 34.8667C8.25 36.0449 9.20513 37 10.3833 37L37.5833 37C38.7615 37 39.7167 36.0449 39.7167 34.8667L39.7167 11.9333C39.7167 10.7551 38.7615 9.8 37.5833 9.8L10.3833 9.8C9.20512 9.8 8.25 10.7551 8.25 11.9333Z\" fill=\"white\"/><path id=\"Vector\" d=\"M30.5203 15.8245L29.4187 15.8317C29.2395 15.8229 29.0704 15.7458 28.9461 15.6164C28.8218 15.4869 28.7516 15.3148 28.7501 15.1353C28.7486 14.9559 28.8157 14.7826 28.9378 14.651C29.0599 14.5195 29.2277 14.4395 29.4067 14.4277L32.2159 14.4073C32.3561 14.4056 32.4936 14.4464 32.6101 14.5244C32.7267 14.6023 32.8168 14.7138 32.8687 14.8441C32.9035 14.9277 32.9214 15.0174 32.9215 15.1081V17.9317C32.9219 18.1176 32.8484 18.2961 32.7172 18.428C32.586 18.5598 32.4079 18.6342 32.2219 18.6349C32.036 18.6342 31.8578 18.5598 31.7267 18.428C31.5955 18.2961 31.522 18.1176 31.5223 17.9317V16.8061L25.2523 23.1001C25.1524 23.2006 25.0242 23.2683 24.8848 23.294C24.7455 23.3198 24.6015 23.3023 24.4723 23.2441L16.0747 19.5001C15.9049 19.4235 15.7723 19.2829 15.7057 19.1089C15.6392 18.9349 15.6442 18.7416 15.7195 18.5713C15.7566 18.487 15.81 18.4109 15.8765 18.3473C15.9431 18.2838 16.0215 18.2339 16.1073 18.2007C16.1932 18.1675 16.2847 18.1515 16.3767 18.1538C16.4687 18.156 16.5594 18.1763 16.6435 18.2137L24.6019 21.7657L30.5215 15.8245H30.5203Z\" fill=\"#0964E3\"/><path id=\"Vector_2\" d=\"M32.2219 21.2006C32.5938 21.2016 32.9502 21.3501 33.2128 21.6136C33.4753 21.877 33.6226 22.2339 33.6223 22.6059V31.9742C33.6229 32.3464 33.4758 32.7036 33.2132 32.9673C32.9506 33.231 32.594 33.3797 32.2219 33.3806H30.3559C29.9837 33.3797 29.6272 33.231 29.3646 32.9673C29.102 32.7036 28.9549 32.3464 28.9555 31.9742V22.6046C28.9549 22.2325 29.102 21.8753 29.3646 21.6116C29.6272 21.3479 29.9837 21.1992 30.3559 21.1982H32.2219V21.2006ZM19.1575 23.0726C19.3419 23.0731 19.5244 23.1099 19.6945 23.1809C19.8647 23.2519 20.0192 23.3557 20.1492 23.4864C20.2793 23.6171 20.3823 23.7722 20.4524 23.9427C20.5225 24.1132 20.5584 24.2959 20.5579 24.4802V31.973C20.5585 32.3452 20.4114 32.7024 20.1488 32.9661C19.8862 33.2298 19.5297 33.3785 19.1575 33.3794H17.2915C16.9194 33.3785 16.5628 33.2298 16.3002 32.9661C16.0376 32.7024 15.8905 32.3452 15.8911 31.973V24.479C15.8905 24.1069 16.0376 23.7497 16.3002 23.486C16.5628 23.2223 16.9193 23.0736 17.2915 23.0726H19.1575ZM25.6891 24.9459C26.0612 24.9468 26.4178 25.0955 26.6804 25.3592C26.943 25.6229 27.0901 25.9801 27.0895 26.3522V31.9719C27.0901 32.344 26.943 32.7012 26.6804 32.9649C26.4178 33.2286 26.0612 33.3773 25.6891 33.3782H23.8243C23.4519 33.3776 23.0951 33.2291 22.8322 32.9653C22.5694 32.7016 22.4221 32.3442 22.4227 31.9719V26.3522C22.4221 25.9799 22.5694 25.6225 22.8322 25.3588C23.0951 25.095 23.4519 24.9465 23.8243 24.9459H25.6891Z\" fill=\"url(#paint1_linear_106_531)\"/></g></g></g><defs><linearGradient id=\"paint0_linear_106_531\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_531\" x1=\"24.7567\" y1=\"21.1982\" x2=\"24.7567\" y2=\"33.3806\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_106_531\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>';\n    var reportData = ref([]);\n    var toolId = ref('');\n    var toolInfo = ref({});\n    var toolData = ref([]);\n    var toolIconData = {\n      issue_analysis: IssueAnalysisIcon,\n      'meeting-room': MeetingRoomIcon,\n      'hot-point': HotPointIcon,\n      letter_chat_sum: IssueAnalysisIconRD\n    };\n    var editorRef = ref();\n    var fileList = ref([]);\n    var fileData = ref([]);\n    var sendContent = ref('');\n    var id = ref('');\n    var show = ref(false);\n    var handleFileUpload = function handleFileUpload(data) {\n      fileList.value = data;\n    };\n    var handleFileCallback = function handleFileCallback(data) {\n      fileData.value = data;\n    };\n    var handleClose = function handleClose(item) {\n      var _editorRef$value;\n      (_editorRef$value = editorRef.value) === null || _editorRef$value === void 0 || _editorRef$value.handleSetFile(fileData.value.filter(function (v) {\n        return v.id !== item.id;\n      }));\n    };\n    var handleTips = function handleTips(text) {\n      var parts = text.split(/(\\{[^}]+\\})/);\n      var result = parts.map(function (part) {\n        if (part.startsWith('{') && part.endsWith('}')) {\n          return {\n            value: part.slice(1, -1),\n            type: true\n          };\n        } else if (part.trim() !== '') {\n          return {\n            value: part,\n            type: false\n          };\n        }\n      }).filter(function (item) {\n        return item !== undefined;\n      });\n      return result;\n    };\n    var handleTool = function handleTool(item) {\n      var _editorRef$value2, _editorRef$value3;\n      toolInfo.value = item;\n      toolId.value = item.chatToolCode;\n      (_editorRef$value2 = editorRef.value) === null || _editorRef$value2 === void 0 || _editorRef$value2.handleSetFile([]);\n      (_editorRef$value3 = editorRef.value) === null || _editorRef$value3 === void 0 || _editorRef$value3.handleSetContent('');\n      nextTick(function () {\n        var _editorRef$value4;\n        (_editorRef$value4 = editorRef.value) === null || _editorRef$value4 === void 0 || _editorRef$value4.handleInsertPlaceholder(handleTips(item.userPromptTip));\n      });\n    };\n    var handleGeneraList = function handleGeneraList() {\n      store.commit('setOpenRoute', {\n        name: '我生成的报告',\n        path: '/AiReportGeneraList',\n        query: {}\n      });\n    };\n    var handleView = function handleView(item) {\n      id.value = item.id;\n      show.value = true;\n    };\n    var aiScheduleExists = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(value) {\n        var _yield$api$aiSchedule, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.aiScheduleExists({\n                question: value\n              });\n            case 2:\n              _yield$api$aiSchedule = _context.sent;\n              data = _yield$api$aiSchedule.data;\n              if (data) {\n                _context.next = 6;\n                break;\n              }\n              return _context.abrupt(\"return\", ElMessage({\n                type: 'warning',\n                message: '当前日期没有委员会客，请更改日期！'\n              }));\n            case 6:\n              store.commit('setOpenRoute', {\n                name: '会客室小结',\n                path: '/AiReportGeneraView',\n                query: {\n                  type: 'meeting-room',\n                  AiChatCode: 'ai-meeting-room-report'\n                }\n              });\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function aiScheduleExists(_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleSendMessage = function handleSendMessage(value) {\n      var _editorRef$value5;\n      if (!toolId.value) return ElMessage({\n        type: 'warning',\n        message: '请先选择文档撰写类型！'\n      });\n      var openAiParams = {\n        toolId: toolInfo.value.id,\n        toolCode: toolId.value,\n        toolContent: value,\n        fileData: fileData.value\n      };\n      sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams));\n      if (toolId.value === 'issue_analysis') {\n        store.commit('setOpenRoute', {\n          name: '议题分析报告',\n          path: '/AiReportGeneraView',\n          query: {\n            type: 'issue_analysis',\n            AiChatCode: 'ai-issue_analysis'\n          }\n        });\n      }\n      if (toolId.value === 'hot-point') {\n        store.commit('setOpenRoute', {\n          name: '数据热点分析报告',\n          path: '/AiReportGeneraView',\n          query: {\n            type: 'hot-point',\n            AiChatCode: 'ai-hot-point-chat'\n          }\n        });\n      }\n      if (toolId.value === 'letter_chat_sum') {\n        store.commit('setOpenRoute', {\n          name: '联络站留言分析报告',\n          path: '/AiReportGeneraView',\n          query: {\n            type: 'letter_chat_sum',\n            AiChatCode: 'letter_chat_sum'\n          }\n        });\n      }\n      if (toolId.value === 'meeting-room') aiScheduleExists(value);\n      (_editorRef$value5 = editorRef.value) === null || _editorRef$value5 === void 0 || _editorRef$value5.handleSetFile([]);\n    };\n    var aigptChatSceneDetail = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _openConfig$value, _data$tools;\n        var chatSceneCode, _yield$api$aigptChatS, data;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              chatSceneCode = ((_openConfig$value = openConfig.value) === null || _openConfig$value === void 0 ? void 0 : _openConfig$value.systemPlatform) === 'CPPCC' ? 'ai-general-report-main' : 'letter_chat_sum';\n              _context2.next = 3;\n              return api.aigptChatSceneDetail({\n                query: {\n                  chatSceneCode\n                }\n              });\n            case 3:\n              _yield$api$aigptChatS = _context2.sent;\n              data = _yield$api$aigptChatS.data;\n              toolData.value = (data === null || data === void 0 || (_data$tools = data.tools) === null || _data$tools === void 0 ? void 0 : _data$tools.filter(function (v) {\n                return v.isUsing;\n              })) || [];\n            case 6:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function aigptChatSceneDetail() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var aigptReportRecordList = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _yield$api$aigptRepor, data;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.aigptReportRecordList({\n                pageNo: 1,\n                pageSize: 4\n              });\n            case 2:\n              _yield$api$aigptRepor = _context3.sent;\n              data = _yield$api$aigptRepor.data;\n              reportData.value = data;\n            case 5:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function aigptReportRecordList() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    onActivated(function () {\n      aigptReportRecordList();\n      aigptChatSceneDetail();\n      store.commit('setAiChatElShow', false);\n    });\n    onDeactivated(function () {\n      store.commit('setAiChatElShow', true);\n    });\n    onUnmounted(function () {\n      store.commit('setAiChatElShow', true);\n    });\n    var __returned__ = {\n      store,\n      IssueAnalysisIcon,\n      IssueAnalysisIconRD,\n      MeetingRoomIcon,\n      HotPointIcon,\n      reportData,\n      toolId,\n      toolInfo,\n      toolData,\n      toolIconData,\n      editorRef,\n      fileList,\n      fileData,\n      sendContent,\n      id,\n      show,\n      handleFileUpload,\n      handleFileCallback,\n      handleClose,\n      handleTips,\n      handleTool,\n      handleGeneraList,\n      handleView,\n      aiScheduleExists,\n      handleSendMessage,\n      aigptChatSceneDetail,\n      aigptReportRecordList,\n      get api() {\n        return api;\n      },\n      ref,\n      nextTick,\n      onActivated,\n      onDeactivated,\n      onUnmounted,\n      get useStore() {\n        return useStore;\n      },\n      get format() {\n        return format;\n      },\n      get openConfig() {\n        return openConfig;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      GlobalAiChatFile,\n      GlobalAiChatEditor,\n      get AiReportGeneraDetails() {\n        return AiReportGeneraDetails;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "nextTick", "onActivated", "onDeactivated", "onUnmounted", "useStore", "format", "openConfig", "ElMessage", "GlobalAiChatFile", "GlobalAiChatEditor", "AiReportGeneraDetails", "__default__", "store", "IssueAnalysisIcon", "IssueAnalysisIconRD", "MeetingRoomIcon", "HotPointIcon", "reportData", "toolId", "toolInfo", "toolData", "toolIconData", "issue_analysis", "letter_chat_sum", "editor<PERSON><PERSON>", "fileList", "fileData", "send<PERSON><PERSON><PERSON>", "id", "show", "handleFileUpload", "data", "handleFileCallback", "handleClose", "item", "_editorRef$value", "handleSetFile", "filter", "handleTips", "text", "parts", "split", "result", "map", "part", "startsWith", "endsWith", "trim", "undefined", "handleTool", "_editorRef$value2", "_editorRef$value3", "chatToolCode", "handleSetContent", "_editorRef$value4", "handleInsertPlaceholder", "userPromptTip", "handleGeneraList", "commit", "path", "query", "handleView", "aiScheduleExists", "_ref2", "_callee", "_yield$api$aiSchedule", "_callee$", "_context", "question", "message", "AiChatCode", "_x", "handleSendMessage", "_editorRef$value5", "openAiParams", "toolCode", "toolContent", "sessionStorage", "setItem", "JSON", "stringify", "aigptChatSceneDetail", "_ref3", "_callee2", "_openConfig$value", "_data$tools", "chatSceneCode", "_yield$api$aigptChatS", "_callee2$", "_context2", "systemPlatform", "tools", "isUsing", "aigptReportRecordList", "_ref4", "_callee3", "_yield$api$aigptRepor", "_callee3$", "_context3", "pageNo", "pageSize"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiReportGenera/AiReportGenera.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AiReportGenera\">\r\n    <div class=\"AiReportGeneraBody\">\r\n      <div class=\"AiReportGeneraTitle\">\r\n        我的生成报告\r\n        <span @click=\"handleGeneraList\">\r\n          查看全部\r\n          <el-icon>\r\n            <ArrowRight />\r\n          </el-icon>\r\n        </span>\r\n      </div>\r\n      <div class=\"AiReportGeneraReportBody\">\r\n        <div class=\"AiReportGeneraReportItem\" v-for=\"item in reportData\" :key=\"item.id\" @click=\"handleView(item)\">\r\n          <div class=\"AiReportGeneraReportIcon\">\r\n            <el-icon>\r\n              <Document />\r\n            </el-icon>\r\n          </div>\r\n          <div class=\"AiReportGeneraReportInfo\">\r\n            <div class=\"AiReportGeneraReportName\">{{ item.reportTypeName }}</div>\r\n            <div class=\"AiReportGeneraReportTime\">{{ format(item.createDate) }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AiReportGeneraTitle\">文档撰写类型</div>\r\n      <div class=\"AiReportGeneraItemList\">\r\n        <div\r\n          class=\"AiReportGeneraItem\"\r\n          :class=\"{ 'is-active': toolId === item.chatToolCode }\"\r\n          v-for=\"item in toolData\"\r\n          :key=\"item.chatToolCode\"\r\n          @click=\"handleTool(item)\">\r\n          <div class=\"AiReportGeneraIcon\" v-html=\"toolIconData[item.chatToolCode]\"></div>\r\n          <div class=\"AiReportGeneraName\">{{ item.chatToolName }}</div>\r\n        </div>\r\n        <div class=\"AiReportGeneraPlaceholder\" v-for=\"item in 2\" :key=\"item + '_placeholder'\"></div>\r\n      </div>\r\n      <div class=\"AiReportGeneraEditorBody\">\r\n        <GlobalAiChatFile\r\n          :fileList=\"fileList\"\r\n          :fileData=\"fileData\"\r\n          @close=\"handleClose\"\r\n          v-show=\"fileList.length || fileData.length\" />\r\n        <GlobalAiChatEditor\r\n          ref=\"editorRef\"\r\n          v-model=\"sendContent\"\r\n          @send=\"handleSendMessage\"\r\n          @uploadCallback=\"handleFileUpload\"\r\n          @fileCallback=\"handleFileCallback\" />\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"报告详情\">\r\n      <AiReportGeneraDetails :id=\"id\"></AiReportGeneraDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AiReportGenera' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nimport { openConfig } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue'\r\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue'\r\nimport AiReportGeneraDetails from './component/AiReportGeneraDetails'\r\nconst store = useStore()\r\nconst IssueAnalysisIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#232;&#174;&#174;&#233;&#162;&#152;&#229;&#136;&#134;&#230;&#158;&#144;&#230;&#138;&#165;&#229;&#145;&#138;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_505)\"/><g id=\"Vector\"><path d=\"M38.5453 30.4309H20.864H12.823H10.4115C9.77195 30.4309 9.15857 30.685 8.70632 31.1373C8.25407 31.5895 8 32.2029 8 32.8425V36.0578C8 36.3745 8.06238 36.688 8.18358 36.9806C8.30478 37.2731 8.48242 37.5389 8.70636 37.7628C8.9303 37.9867 9.19614 38.1642 9.48872 38.2854C9.78129 38.4065 10.0949 38.4688 10.4115 38.4687H38.5453C38.8505 38.4686 39.1493 38.3817 39.4069 38.218C39.6645 38.0544 39.8702 37.8208 40 37.5445C39.5449 37.1674 39.1786 36.6945 38.9271 36.1596C38.6757 35.6247 38.5453 35.0409 38.5453 34.4498C38.5453 33.8588 38.6757 33.275 38.9271 32.74C39.1786 32.2051 39.5449 31.7322 40 31.3551C39.8702 31.0789 39.6645 30.8453 39.4069 30.6816C39.1493 30.518 38.8505 30.431 38.5453 30.4309Z\" fill=\"white\"/><path d=\"M38.5453 19.981H20.864H12.823H10.4115C9.77195 19.981 9.15857 20.2351 8.70632 20.6873C8.25407 21.1396 8 21.753 8 22.3925V25.6079C8 26.2475 8.25407 26.8609 8.70632 27.3131C9.15857 27.7653 9.77195 28.0194 10.4115 28.0194H12.823H20.864H38.5453C38.8505 28.0193 39.1493 27.9324 39.4069 27.7687C39.6645 27.6051 39.8702 27.3715 40 27.0953C39.5449 26.7181 39.1785 26.2451 38.9271 25.7102C38.6756 25.1752 38.5453 24.5913 38.5453 24.0002C38.5453 23.4091 38.6756 22.8253 38.9271 22.2903C39.1785 21.7553 39.5449 21.2824 40 20.9052C39.8702 20.6289 39.6645 20.3954 39.4069 20.2317C39.1493 20.068 38.8505 19.9811 38.5453 19.981Z\" fill=\"white\"/><path d=\"M10.4115 17.5695H12.823H20.864H38.5453C38.8505 17.5694 39.1493 17.4825 39.4069 17.3188C39.6645 17.1552 39.8702 16.9216 40 16.6453C39.5449 16.2682 39.1786 15.7953 38.9271 15.2604C38.6757 14.7255 38.5453 14.1417 38.5453 13.5506C38.5453 12.9595 38.6757 12.3758 38.9271 11.8408C39.1786 11.3059 39.5449 10.833 40 10.4559C39.8702 10.1797 39.6645 9.94607 39.4069 9.78242C39.1493 9.61877 38.8505 9.53182 38.5453 9.53174H10.4115C10.0949 9.53165 9.78129 9.59395 9.48872 9.71507C9.19614 9.83619 8.9303 10.0138 8.70636 10.2376C8.48242 10.4615 8.30478 10.7273 8.18358 11.0199C8.06238 11.3124 8 11.626 8 11.9426V15.158C8 15.7976 8.25407 16.4109 8.70632 16.8632C9.15857 17.3154 9.77195 17.5695 10.4115 17.5695Z\" fill=\"white\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 23.8C25 23.3582 25.3582 23 25.8 23H33.8C34.2418 23 34.6 23.3582 34.6 23.8C34.6 24.2418 34.2418 24.6 33.8 24.6H25.8C25.3582 24.6 25 24.2418 25 23.8Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 34.8C25 34.3582 25.3582 34 25.8 34H33.8C34.2418 34 34.6 34.3582 34.6 34.8C34.6 35.2418 34.2418 35.6 33.8 35.6H25.8C25.3582 35.6 25 35.2418 25 34.8Z\" fill=\"#0964E3\"/><path id=\"Vector_2\" d=\"M18.75 11.2C18.75 11.0895 18.6605 11 18.55 11H12.2C12.0895 11 12 11.0895 12 11.2V22.555C12 22.7275 12.2039 22.8191 12.3329 22.7044L15.2421 20.1181C15.3179 20.0508 15.4321 20.0508 15.5079 20.1181L18.4171 22.7044C18.5461 22.8191 18.75 22.7275 18.75 22.555V11.2Z\" fill=\"url(#paint1_linear_106_505)\"/></g><defs><linearGradient id=\"paint0_linear_106_505\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_505\" x1=\"15.375\" y1=\"11\" x2=\"15.375\" y2=\"23.0004\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient></defs></svg>'\r\nconst IssueAnalysisIconRD =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#232;&#174;&#174;&#233;&#162;&#152;&#229;&#136;&#134;&#230;&#158;&#144;&#230;&#138;&#165;&#229;&#145;&#138;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_505)\"/><g id=\"Vector\"><path d=\"M38.5453 30.4309H20.864H12.823H10.4115C9.77195 30.4309 9.15857 30.685 8.70632 31.1373C8.25407 31.5895 8 32.2029 8 32.8425V36.0578C8 36.3745 8.06238 36.688 8.18358 36.9806C8.30478 37.2731 8.48242 37.5389 8.70636 37.7628C8.9303 37.9867 9.19614 38.1642 9.48872 38.2854C9.78129 38.4065 10.0949 38.4688 10.4115 38.4687H38.5453C38.8505 38.4686 39.1493 38.3817 39.4069 38.218C39.6645 38.0544 39.8702 37.8208 40 37.5445C39.5449 37.1674 39.1786 36.6945 38.9271 36.1596C38.6757 35.6247 38.5453 35.0409 38.5453 34.4498C38.5453 33.8588 38.6757 33.275 38.9271 32.74C39.1786 32.2051 39.5449 31.7322 40 31.3551C39.8702 31.0789 39.6645 30.8453 39.4069 30.6816C39.1493 30.518 38.8505 30.431 38.5453 30.4309Z\" fill=\"white\"/><path d=\"M38.5453 19.981H20.864H12.823H10.4115C9.77195 19.981 9.15857 20.2351 8.70632 20.6873C8.25407 21.1396 8 21.753 8 22.3925V25.6079C8 26.2475 8.25407 26.8609 8.70632 27.3131C9.15857 27.7653 9.77195 28.0194 10.4115 28.0194H12.823H20.864H38.5453C38.8505 28.0193 39.1493 27.9324 39.4069 27.7687C39.6645 27.6051 39.8702 27.3715 40 27.0953C39.5449 26.7181 39.1785 26.2451 38.9271 25.7102C38.6756 25.1752 38.5453 24.5913 38.5453 24.0002C38.5453 23.4091 38.6756 22.8253 38.9271 22.2903C39.1785 21.7553 39.5449 21.2824 40 20.9052C39.8702 20.6289 39.6645 20.3954 39.4069 20.2317C39.1493 20.068 38.8505 19.9811 38.5453 19.981Z\" fill=\"white\"/><path d=\"M10.4115 17.5695H12.823H20.864H38.5453C38.8505 17.5694 39.1493 17.4825 39.4069 17.3188C39.6645 17.1552 39.8702 16.9216 40 16.6453C39.5449 16.2682 39.1786 15.7953 38.9271 15.2604C38.6757 14.7255 38.5453 14.1417 38.5453 13.5506C38.5453 12.9595 38.6757 12.3758 38.9271 11.8408C39.1786 11.3059 39.5449 10.833 40 10.4559C39.8702 10.1797 39.6645 9.94607 39.4069 9.78242C39.1493 9.61877 38.8505 9.53182 38.5453 9.53174H10.4115C10.0949 9.53165 9.78129 9.59395 9.48872 9.71507C9.19614 9.83619 8.9303 10.0138 8.70636 10.2376C8.48242 10.4615 8.30478 10.7273 8.18358 11.0199C8.06238 11.3124 8 11.626 8 11.9426V15.158C8 15.7976 8.25407 16.4109 8.70632 16.8632C9.15857 17.3154 9.77195 17.5695 10.4115 17.5695Z\" fill=\"white\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 23.8C25 23.3582 25.3582 23 25.8 23H33.8C34.2418 23 34.6 23.3582 34.6 23.8C34.6 24.2418 34.2418 24.6 33.8 24.6H25.8C25.3582 24.6 25 24.2418 25 23.8Z\" fill=\"#F54747\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 343 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M25 34.8C25 34.3582 25.3582 34 25.8 34H33.8C34.2418 34 34.6 34.3582 34.6 34.8C34.6 35.2418 34.2418 35.6 33.8 35.6H25.8C25.3582 35.6 25 35.2418 25 34.8Z\" fill=\"#F54747\"/><path id=\"Vector_2\" d=\"M18.75 11.2C18.75 11.0895 18.6605 11 18.55 11H12.2C12.0895 11 12 11.0895 12 11.2V22.555C12 22.7275 12.2039 22.8191 12.3329 22.7044L15.2421 20.1181C15.3179 20.0508 15.4321 20.0508 15.5079 20.1181L18.4171 22.7044C18.5461 22.8191 18.75 22.7275 18.75 22.555V11.2Z\" fill=\"url(#paint1_linear_106_505)\"/></g><defs><linearGradient id=\"paint0_linear_106_505\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F54747\"/><stop offset=\"0.995\" stop-color=\"#F54747\"/></linearGradient><linearGradient id=\"paint1_linear_106_505\" x1=\"15.375\" y1=\"11\" x2=\"15.375\" y2=\"23.0004\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#F54747\"/><stop offset=\"1\" stop-color=\"#F54747\"/></linearGradient></defs></svg>'\r\nconst MeetingRoomIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#229;&#176;&#143;&#231;&#187;&#147;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6304\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_745)\"/><g id=\"&#231;&#187;&#132; 2771\"><g id=\"&#231;&#187;&#132; 2769\"><path id=\"&#232;&#183;&#175;&#229;&#190;&#132; 1808\" d=\"M31.4467 41.261C31.0716 41.636 30.563 41.8467 30.0326 41.8467L12.7926 41.8467C12.0103 41.8467 11.26 41.5359 10.7068 40.9827C10.1536 40.4295 9.84277 39.6792 9.84277 38.8968L9.84278 10.1667C9.84278 9.38431 10.1536 8.634 10.7068 8.08079C11.26 7.52758 12.0103 7.2168 12.7926 7.2168L35.2076 7.2168C35.9899 7.2168 36.7402 7.52759 37.2934 8.0808C37.8467 8.634 38.1574 9.38432 38.1574 10.1667L38.1574 33.7231C38.1574 34.2537 37.9467 34.7624 37.5715 35.1375L31.4467 41.261Z\" fill=\"white\"/><rect id=\"Rectangle 34625139\" x=\"15\" y=\"30\" width=\"11\" height=\"4\" rx=\"1\" fill=\"url(#paint1_linear_106_745)\"/></g><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 362 (Stroke)\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.0259 16.8C15.0259 16.3582 15.3841 16 15.8259 16H32.1747C32.6165 16 32.9747 16.3582 32.9747 16.8C32.9747 17.2418 32.6165 17.6 32.1747 17.6H15.8259C15.3841 17.6 15.0259 17.2418 15.0259 16.8Z\" fill=\"#0964E3\"/><path id=\"&#231;&#155;&#180;&#231;&#186;&#191; 362 (Stroke)_2\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M15.0259 21.8C15.0259 21.3582 15.3841 21 15.8259 21H32.1747C32.6165 21 32.9747 21.3582 32.9747 21.8C32.9747 22.2418 32.6165 22.6 32.1747 22.6H15.8259C15.3841 22.6 15.0259 22.2418 15.0259 21.8Z\" fill=\"#0964E3\"/></g></g><defs><linearGradient id=\"paint0_linear_106_745\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_745\" x1=\"20.5\" y1=\"30\" x2=\"20.5\" y2=\"34\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient></defs></svg>'\r\nconst HotPointIcon =\r\n  '<svg width=\"48\" height=\"48\" viewBox=\"0 0 48 48\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><g id=\"&#231;&#131;&#173;&#231;&#130;&#185;&#230;&#138;&#165;&#229;&#145;&#138;\" clip-path=\"url(#clip0_106_531)\"><g id=\"&#230;&#148;&#191;&#231;&#173;&#150;&#230;&#150;&#135;&#228;&#187;&#182;\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 7578\" d=\"M37.3333 0H10.6667C4.77563 0 0 4.77563 0 10.6667V37.3333C0 43.2244 4.77563 48 10.6667 48H37.3333C43.2244 48 48 43.2244 48 37.3333V10.6667C48 4.77563 43.2244 0 37.3333 0Z\" fill=\"url(#paint0_linear_106_531)\"/><g id=\"Group 1000006979\"><path id=\"&#231;&#159;&#169;&#229;&#189;&#162; 6277\" d=\"M8.25 11.9333L8.25 34.8667C8.25 36.0449 9.20513 37 10.3833 37L37.5833 37C38.7615 37 39.7167 36.0449 39.7167 34.8667L39.7167 11.9333C39.7167 10.7551 38.7615 9.8 37.5833 9.8L10.3833 9.8C9.20512 9.8 8.25 10.7551 8.25 11.9333Z\" fill=\"white\"/><path id=\"Vector\" d=\"M30.5203 15.8245L29.4187 15.8317C29.2395 15.8229 29.0704 15.7458 28.9461 15.6164C28.8218 15.4869 28.7516 15.3148 28.7501 15.1353C28.7486 14.9559 28.8157 14.7826 28.9378 14.651C29.0599 14.5195 29.2277 14.4395 29.4067 14.4277L32.2159 14.4073C32.3561 14.4056 32.4936 14.4464 32.6101 14.5244C32.7267 14.6023 32.8168 14.7138 32.8687 14.8441C32.9035 14.9277 32.9214 15.0174 32.9215 15.1081V17.9317C32.9219 18.1176 32.8484 18.2961 32.7172 18.428C32.586 18.5598 32.4079 18.6342 32.2219 18.6349C32.036 18.6342 31.8578 18.5598 31.7267 18.428C31.5955 18.2961 31.522 18.1176 31.5223 17.9317V16.8061L25.2523 23.1001C25.1524 23.2006 25.0242 23.2683 24.8848 23.294C24.7455 23.3198 24.6015 23.3023 24.4723 23.2441L16.0747 19.5001C15.9049 19.4235 15.7723 19.2829 15.7057 19.1089C15.6392 18.9349 15.6442 18.7416 15.7195 18.5713C15.7566 18.487 15.81 18.4109 15.8765 18.3473C15.9431 18.2838 16.0215 18.2339 16.1073 18.2007C16.1932 18.1675 16.2847 18.1515 16.3767 18.1538C16.4687 18.156 16.5594 18.1763 16.6435 18.2137L24.6019 21.7657L30.5215 15.8245H30.5203Z\" fill=\"#0964E3\"/><path id=\"Vector_2\" d=\"M32.2219 21.2006C32.5938 21.2016 32.9502 21.3501 33.2128 21.6136C33.4753 21.877 33.6226 22.2339 33.6223 22.6059V31.9742C33.6229 32.3464 33.4758 32.7036 33.2132 32.9673C32.9506 33.231 32.594 33.3797 32.2219 33.3806H30.3559C29.9837 33.3797 29.6272 33.231 29.3646 32.9673C29.102 32.7036 28.9549 32.3464 28.9555 31.9742V22.6046C28.9549 22.2325 29.102 21.8753 29.3646 21.6116C29.6272 21.3479 29.9837 21.1992 30.3559 21.1982H32.2219V21.2006ZM19.1575 23.0726C19.3419 23.0731 19.5244 23.1099 19.6945 23.1809C19.8647 23.2519 20.0192 23.3557 20.1492 23.4864C20.2793 23.6171 20.3823 23.7722 20.4524 23.9427C20.5225 24.1132 20.5584 24.2959 20.5579 24.4802V31.973C20.5585 32.3452 20.4114 32.7024 20.1488 32.9661C19.8862 33.2298 19.5297 33.3785 19.1575 33.3794H17.2915C16.9194 33.3785 16.5628 33.2298 16.3002 32.9661C16.0376 32.7024 15.8905 32.3452 15.8911 31.973V24.479C15.8905 24.1069 16.0376 23.7497 16.3002 23.486C16.5628 23.2223 16.9193 23.0736 17.2915 23.0726H19.1575ZM25.6891 24.9459C26.0612 24.9468 26.4178 25.0955 26.6804 25.3592C26.943 25.6229 27.0901 25.9801 27.0895 26.3522V31.9719C27.0901 32.344 26.943 32.7012 26.6804 32.9649C26.4178 33.2286 26.0612 33.3773 25.6891 33.3782H23.8243C23.4519 33.3776 23.0951 33.2291 22.8322 32.9653C22.5694 32.7016 22.4221 32.3442 22.4227 31.9719V26.3522C22.4221 25.9799 22.5694 25.6225 22.8322 25.3588C23.0951 25.095 23.4519 24.9465 23.8243 24.9459H25.6891Z\" fill=\"url(#paint1_linear_106_531)\"/></g></g></g><defs><linearGradient id=\"paint0_linear_106_531\" x1=\"37.296\" y1=\"48.624\" x2=\"11.52\" y2=\"0.863998\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#599FFA\"/><stop offset=\"0.995\" stop-color=\"#0964E3\"/></linearGradient><linearGradient id=\"paint1_linear_106_531\" x1=\"24.7567\" y1=\"21.1982\" x2=\"24.7567\" y2=\"33.3806\" gradientUnits=\"userSpaceOnUse\"><stop stop-color=\"#EAF2FE\"/><stop offset=\"1\" stop-color=\"#A3C9FE\"/></linearGradient><clipPath id=\"clip0_106_531\"><rect width=\"48\" height=\"48\" fill=\"white\"/></clipPath></defs></svg>'\r\nconst reportData = ref([])\r\nconst toolId = ref('')\r\nconst toolInfo = ref({})\r\nconst toolData = ref([])\r\nconst toolIconData = {\r\n  issue_analysis: IssueAnalysisIcon,\r\n  'meeting-room': MeetingRoomIcon,\r\n  'hot-point': HotPointIcon,\r\n  letter_chat_sum: IssueAnalysisIconRD\r\n}\r\nconst editorRef = ref()\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst sendContent = ref('')\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleTips = (text) => {\r\n  const parts = text.split(/(\\{[^}]+\\})/)\r\n  const result = parts\r\n    .map((part) => {\r\n      if (part.startsWith('{') && part.endsWith('}')) {\r\n        return { value: part.slice(1, -1), type: true }\r\n      } else if (part.trim() !== '') {\r\n        return { value: part, type: false }\r\n      }\r\n    })\r\n    .filter((item) => item !== undefined)\r\n  return result\r\n}\r\nconst handleTool = (item) => {\r\n  toolInfo.value = item\r\n  toolId.value = item.chatToolCode\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n  nextTick(() => {\r\n    editorRef.value?.handleInsertPlaceholder(handleTips(item.userPromptTip))\r\n  })\r\n}\r\nconst handleGeneraList = () => {\r\n  store.commit('setOpenRoute', { name: '我生成的报告', path: '/AiReportGeneraList', query: {} })\r\n}\r\nconst handleView = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst aiScheduleExists = async (value) => {\r\n  const { data } = await api.aiScheduleExists({ question: value })\r\n  if (!data) return ElMessage({ type: 'warning', message: '当前日期没有委员会客，请更改日期！' })\r\n  store.commit('setOpenRoute', {\r\n    name: '会客室小结',\r\n    path: '/AiReportGeneraView',\r\n    query: { type: 'meeting-room', AiChatCode: 'ai-meeting-room-report' }\r\n  })\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!toolId.value) return ElMessage({ type: 'warning', message: '请先选择文档撰写类型！' })\r\n  const openAiParams = {\r\n    toolId: toolInfo.value.id,\r\n    toolCode: toolId.value,\r\n    toolContent: value,\r\n    fileData: fileData.value\r\n  }\r\n  sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams))\r\n  if (toolId.value === 'issue_analysis') {\r\n    store.commit('setOpenRoute', {\r\n      name: '议题分析报告',\r\n      path: '/AiReportGeneraView',\r\n      query: { type: 'issue_analysis', AiChatCode: 'ai-issue_analysis' }\r\n    })\r\n  }\r\n  if (toolId.value === 'hot-point') {\r\n    store.commit('setOpenRoute', {\r\n      name: '数据热点分析报告',\r\n      path: '/AiReportGeneraView',\r\n      query: { type: 'hot-point', AiChatCode: 'ai-hot-point-chat' }\r\n    })\r\n  }\r\n  if (toolId.value === 'letter_chat_sum') {\r\n    store.commit('setOpenRoute', {\r\n      name: '联络站留言分析报告',\r\n      path: '/AiReportGeneraView',\r\n      query: { type: 'letter_chat_sum', AiChatCode: 'letter_chat_sum' }\r\n    })\r\n  }\r\n  if (toolId.value === 'meeting-room') aiScheduleExists(value)\r\n  editorRef.value?.handleSetFile([])\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const chatSceneCode = openConfig.value?.systemPlatform === 'CPPCC' ? 'ai-general-report-main' : 'letter_chat_sum'\r\n  const { data } = await api.aigptChatSceneDetail({ query: { chatSceneCode } })\r\n  toolData.value = data?.tools?.filter((v) => v.isUsing) || []\r\n}\r\nconst aigptReportRecordList = async () => {\r\n  const { data } = await api.aigptReportRecordList({ pageNo: 1, pageSize: 4 })\r\n  reportData.value = data\r\n}\r\nonActivated(() => {\r\n  aigptReportRecordList()\r\n  aigptChatSceneDetail()\r\n  store.commit('setAiChatElShow', false)\r\n})\r\nonDeactivated(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\nonUnmounted(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.AiReportGenera {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  position: relative;\r\n\r\n  .AiReportGeneraBody {\r\n    padding: var(--zy-distance-two);\r\n  }\r\n\r\n  .AiReportGeneraTitle {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    font-weight: bold;\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-name-font-size);\r\n    padding: 12px 0;\r\n\r\n    span {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      font-weight: normal;\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-text-color-secondary);\r\n      cursor: pointer;\r\n\r\n      .zy-el-icon {\r\n        font-size: 16px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .AiReportGeneraReportBody {\r\n    width: 800px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .AiReportGeneraReportItem {\r\n      width: 388px;\r\n      height: 60px;\r\n      border-radius: 10px;\r\n      background: var(--zy-el-color-info-light-9);\r\n      margin-bottom: 16px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding-right: var(--zy-distance-two);\r\n      cursor: pointer;\r\n\r\n      .AiReportGeneraReportIcon {\r\n        width: 52px;\r\n        height: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding-left: 6px;\r\n\r\n        .zy-el-icon {\r\n          font-size: 32px;\r\n          color: var(--zy-el-text-color-secondary);\r\n        }\r\n      }\r\n\r\n      .AiReportGeneraReportInfo {\r\n        width: calc(100% - 52px);\r\n\r\n        .AiReportGeneraReportName {\r\n          font-weight: bold;\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n\r\n        .AiReportGeneraReportTime {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          color: var(--zy-el-text-color-secondary);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .AiReportGeneraItemList {\r\n    width: 800px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .AiReportGeneraPlaceholder {\r\n      width: 185px;\r\n      height: 126px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .AiReportGeneraItem {\r\n      // width: 185px;\r\n      width: 190px;\r\n      height: 126px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      border-radius: 6px 6px 6px 6px;\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n      padding: 20px;\r\n      margin-bottom: 20px;\r\n      cursor: pointer;\r\n\r\n      &.is-active {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AiReportGeneraName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AiReportGeneraName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .AiReportGeneraIcon {\r\n        width: 48px;\r\n        height: 48px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n\r\n      .AiReportGeneraName {\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n    }\r\n  }\r\n\r\n  .AiReportGeneraEditorBody {\r\n    width: 800px;\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: 20px;\r\n    transform: translateX(-50%);\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalAiChatEditorContainer {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA8DA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,QAAQ,KAAK;AAC5E,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,kBAAkB,MAAM,wCAAwC;AACvE,OAAOC,qBAAqB,MAAM,mCAAmC;AAXrE,IAAAC,WAAA,GAAe;EAAExC,IAAI,EAAE;AAAiB,CAAC;;;;;IAYzC,IAAMyC,KAAK,GAAGR,QAAQ,CAAC,CAAC;IACxB,IAAMS,iBAAiB,GACrB,iyHAAiyH;IACnyH,IAAMC,mBAAmB,GACvB,iyHAAiyH;IACnyH,IAAMC,eAAe,GACnB,ooEAAooE;IACtoE,IAAMC,YAAY,GAChB,+1HAA+1H;IACj2H,IAAMC,UAAU,GAAGlB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMmB,MAAM,GAAGnB,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMoB,QAAQ,GAAGpB,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAMqB,QAAQ,GAAGrB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMsB,YAAY,GAAG;MACnBC,cAAc,EAAET,iBAAiB;MACjC,cAAc,EAAEE,eAAe;MAC/B,WAAW,EAAEC,YAAY;MACzBO,eAAe,EAAET;IACnB,CAAC;IACD,IAAMU,SAAS,GAAGzB,GAAG,CAAC,CAAC;IACvB,IAAM0B,QAAQ,GAAG1B,GAAG,CAAC,EAAE,CAAC;IACxB,IAAM2B,QAAQ,GAAG3B,GAAG,CAAC,EAAE,CAAC;IACxB,IAAM4B,WAAW,GAAG5B,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAM6B,EAAE,GAAG7B,GAAG,CAAC,EAAE,CAAC;IAClB,IAAM8B,IAAI,GAAG9B,GAAG,CAAC,KAAK,CAAC;IACvB,IAAM+B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;MACjCN,QAAQ,CAAC/H,KAAK,GAAGqI,IAAI;IACvB,CAAC;IACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAID,IAAI,EAAK;MACnCL,QAAQ,CAAChI,KAAK,GAAGqI,IAAI;IACvB,CAAC;IACD,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAAA,IAAAC,gBAAA;MAC5B,CAAAA,gBAAA,GAAAX,SAAS,CAAC9H,KAAK,cAAAyI,gBAAA,eAAfA,gBAAA,CAAiBC,aAAa,CAACV,QAAQ,CAAChI,KAAK,CAAC2I,MAAM,CAAC,UAAC3G,CAAC;QAAA,OAAKA,CAAC,CAACkG,EAAE,KAAKM,IAAI,CAACN,EAAE;MAAA,EAAC,CAAC;IAChF,CAAC;IACD,IAAMU,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B,IAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,aAAa,CAAC;MACvC,IAAMC,MAAM,GAAGF,KAAK,CACjBG,GAAG,CAAC,UAACC,IAAI,EAAK;QACb,IAAIA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,IAAID,IAAI,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC9C,OAAO;YAAEpJ,KAAK,EAAEkJ,IAAI,CAAC7D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAAElE,IAAI,EAAE;UAAK,CAAC;QACjD,CAAC,MAAM,IAAI+H,IAAI,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAC7B,OAAO;YAAErJ,KAAK,EAAEkJ,IAAI;YAAE/H,IAAI,EAAE;UAAM,CAAC;QACrC;MACF,CAAC,CAAC,CACDwH,MAAM,CAAC,UAACH,IAAI;QAAA,OAAKA,IAAI,KAAKc,SAAS;MAAA,EAAC;MACvC,OAAON,MAAM;IACf,CAAC;IACD,IAAMO,UAAU,GAAG,SAAbA,UAAUA,CAAIf,IAAI,EAAK;MAAA,IAAAgB,iBAAA,EAAAC,iBAAA;MAC3BhC,QAAQ,CAACzH,KAAK,GAAGwI,IAAI;MACrBhB,MAAM,CAACxH,KAAK,GAAGwI,IAAI,CAACkB,YAAY;MAChC,CAAAF,iBAAA,GAAA1B,SAAS,CAAC9H,KAAK,cAAAwJ,iBAAA,eAAfA,iBAAA,CAAiBd,aAAa,CAAC,EAAE,CAAC;MAClC,CAAAe,iBAAA,GAAA3B,SAAS,CAAC9H,KAAK,cAAAyJ,iBAAA,eAAfA,iBAAA,CAAiBE,gBAAgB,CAAC,EAAE,CAAC;MACrCrD,QAAQ,CAAC,YAAM;QAAA,IAAAsD,iBAAA;QACb,CAAAA,iBAAA,GAAA9B,SAAS,CAAC9H,KAAK,cAAA4J,iBAAA,eAAfA,iBAAA,CAAiBC,uBAAuB,CAACjB,UAAU,CAACJ,IAAI,CAACsB,aAAa,CAAC,CAAC;MAC1E,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B7C,KAAK,CAAC8C,MAAM,CAAC,cAAc,EAAE;QAAEvF,IAAI,EAAE,QAAQ;QAAEwF,IAAI,EAAE,qBAAqB;QAAEC,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAC1F,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAI3B,IAAI,EAAK;MAC3BN,EAAE,CAAClI,KAAK,GAAGwI,IAAI,CAACN,EAAE;MAClBC,IAAI,CAACnI,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMoK,gBAAgB;MAAA,IAAAC,KAAA,GAAAtE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4F,QAAOtK,KAAK;QAAA,IAAAuK,qBAAA,EAAAlC,IAAA;QAAA,OAAA/I,mBAAA,GAAAuB,IAAA,UAAA2J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAtF,IAAA,GAAAsF,QAAA,CAAAjH,IAAA;YAAA;cAAAiH,QAAA,CAAAjH,IAAA;cAAA,OACZ4C,GAAG,CAACgE,gBAAgB,CAAC;gBAAEM,QAAQ,EAAE1K;cAAM,CAAC,CAAC;YAAA;cAAAuK,qBAAA,GAAAE,QAAA,CAAAxH,IAAA;cAAxDoF,IAAI,GAAAkC,qBAAA,CAAJlC,IAAI;cAAA,IACPA,IAAI;gBAAAoC,QAAA,CAAAjH,IAAA;gBAAA;cAAA;cAAA,OAAAiH,QAAA,CAAArH,MAAA,WAASyD,SAAS,CAAC;gBAAE1F,IAAI,EAAE,SAAS;gBAAEwJ,OAAO,EAAE;cAAoB,CAAC,CAAC;YAAA;cAC9EzD,KAAK,CAAC8C,MAAM,CAAC,cAAc,EAAE;gBAC3BvF,IAAI,EAAE,OAAO;gBACbwF,IAAI,EAAE,qBAAqB;gBAC3BC,KAAK,EAAE;kBAAE/I,IAAI,EAAE,cAAc;kBAAEyJ,UAAU,EAAE;gBAAyB;cACtE,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAH,QAAA,CAAAnF,IAAA;UAAA;QAAA,GAAAgF,OAAA;MAAA,CACH;MAAA,gBARKF,gBAAgBA,CAAAS,EAAA;QAAA,OAAAR,KAAA,CAAApE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAQrB;IACD,IAAM8E,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAI9K,KAAK,EAAK;MAAA,IAAA+K,iBAAA;MACnC,IAAI,CAACvD,MAAM,CAACxH,KAAK,EAAE,OAAO6G,SAAS,CAAC;QAAE1F,IAAI,EAAE,SAAS;QAAEwJ,OAAO,EAAE;MAAc,CAAC,CAAC;MAChF,IAAMK,YAAY,GAAG;QACnBxD,MAAM,EAAEC,QAAQ,CAACzH,KAAK,CAACkI,EAAE;QACzB+C,QAAQ,EAAEzD,MAAM,CAACxH,KAAK;QACtBkL,WAAW,EAAElL,KAAK;QAClBgI,QAAQ,EAAEA,QAAQ,CAAChI;MACrB,CAAC;MACDmL,cAAc,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACN,YAAY,CAAC,CAAC;MACpE,IAAIxD,MAAM,CAACxH,KAAK,KAAK,gBAAgB,EAAE;QACrCkH,KAAK,CAAC8C,MAAM,CAAC,cAAc,EAAE;UAC3BvF,IAAI,EAAE,QAAQ;UACdwF,IAAI,EAAE,qBAAqB;UAC3BC,KAAK,EAAE;YAAE/I,IAAI,EAAE,gBAAgB;YAAEyJ,UAAU,EAAE;UAAoB;QACnE,CAAC,CAAC;MACJ;MACA,IAAIpD,MAAM,CAACxH,KAAK,KAAK,WAAW,EAAE;QAChCkH,KAAK,CAAC8C,MAAM,CAAC,cAAc,EAAE;UAC3BvF,IAAI,EAAE,UAAU;UAChBwF,IAAI,EAAE,qBAAqB;UAC3BC,KAAK,EAAE;YAAE/I,IAAI,EAAE,WAAW;YAAEyJ,UAAU,EAAE;UAAoB;QAC9D,CAAC,CAAC;MACJ;MACA,IAAIpD,MAAM,CAACxH,KAAK,KAAK,iBAAiB,EAAE;QACtCkH,KAAK,CAAC8C,MAAM,CAAC,cAAc,EAAE;UAC3BvF,IAAI,EAAE,WAAW;UACjBwF,IAAI,EAAE,qBAAqB;UAC3BC,KAAK,EAAE;YAAE/I,IAAI,EAAE,iBAAiB;YAAEyJ,UAAU,EAAE;UAAkB;QAClE,CAAC,CAAC;MACJ;MACA,IAAIpD,MAAM,CAACxH,KAAK,KAAK,cAAc,EAAEoK,gBAAgB,CAACpK,KAAK,CAAC;MAC5D,CAAA+K,iBAAA,GAAAjD,SAAS,CAAC9H,KAAK,cAAA+K,iBAAA,eAAfA,iBAAA,CAAiBrC,aAAa,CAAC,EAAE,CAAC;IACpC,CAAC;IACD,IAAM6C,oBAAoB;MAAA,IAAAC,KAAA,GAAAzF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+G,SAAA;QAAA,IAAAC,iBAAA,EAAAC,WAAA;QAAA,IAAAC,aAAA,EAAAC,qBAAA,EAAAxD,IAAA;QAAA,OAAA/I,mBAAA,GAAAuB,IAAA,UAAAiL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5G,IAAA,GAAA4G,SAAA,CAAAvI,IAAA;YAAA;cACrBoI,aAAa,GAAG,EAAAF,iBAAA,GAAA9E,UAAU,CAAC5G,KAAK,cAAA0L,iBAAA,uBAAhBA,iBAAA,CAAkBM,cAAc,MAAK,OAAO,GAAG,wBAAwB,GAAG,iBAAiB;cAAAD,SAAA,CAAAvI,IAAA;cAAA,OAC1F4C,GAAG,CAACmF,oBAAoB,CAAC;gBAAErB,KAAK,EAAE;kBAAE0B;gBAAc;cAAE,CAAC,CAAC;YAAA;cAAAC,qBAAA,GAAAE,SAAA,CAAA9I,IAAA;cAArEoF,IAAI,GAAAwD,qBAAA,CAAJxD,IAAI;cACZX,QAAQ,CAAC1H,KAAK,GAAG,CAAAqI,IAAI,aAAJA,IAAI,gBAAAsD,WAAA,GAAJtD,IAAI,CAAE4D,KAAK,cAAAN,WAAA,uBAAXA,WAAA,CAAahD,MAAM,CAAC,UAAC3G,CAAC;gBAAA,OAAKA,CAAC,CAACkK,OAAO;cAAA,EAAC,KAAI,EAAE;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAzG,IAAA;UAAA;QAAA,GAAAmG,QAAA;MAAA,CAC7D;MAAA,gBAJKF,oBAAoBA,CAAA;QAAA,OAAAC,KAAA,CAAAvF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAIzB;IACD,IAAMmG,qBAAqB;MAAA,IAAAC,KAAA,GAAArG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2H,SAAA;QAAA,IAAAC,qBAAA,EAAAjE,IAAA;QAAA,OAAA/I,mBAAA,GAAAuB,IAAA,UAAA0L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArH,IAAA,GAAAqH,SAAA,CAAAhJ,IAAA;YAAA;cAAAgJ,SAAA,CAAAhJ,IAAA;cAAA,OACL4C,GAAG,CAAC+F,qBAAqB,CAAC;gBAAEM,MAAM,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAE,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAE,SAAA,CAAAvJ,IAAA;cAApEoF,IAAI,GAAAiE,qBAAA,CAAJjE,IAAI;cACZd,UAAU,CAACvH,KAAK,GAAGqI,IAAI;YAAA;YAAA;cAAA,OAAAmE,SAAA,CAAAlH,IAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA,CACxB;MAAA,gBAHKF,qBAAqBA,CAAA;QAAA,OAAAC,KAAA,CAAAnG,KAAA,OAAAD,SAAA;MAAA;IAAA,GAG1B;IACDO,WAAW,CAAC,YAAM;MAChB4F,qBAAqB,CAAC,CAAC;MACvBZ,oBAAoB,CAAC,CAAC;MACtBrE,KAAK,CAAC8C,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;IACxC,CAAC,CAAC;IACFxD,aAAa,CAAC,YAAM;MAClBU,KAAK,CAAC8C,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC;IACFvD,WAAW,CAAC,YAAM;MAChBS,KAAK,CAAC8C,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}