{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AiReportGeneraView\"\n};\nvar _hoisted_2 = {\n  class: \"AiReportGeneraViewButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSave\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"保存\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleCopy\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"复制\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleDownload\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"下载\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_TinyMceEditor, {\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.content = $event;\n    }),\n    setting: {\n      height: '100%'\n    }\n  }, null, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_button", "type", "onClick", "$setup", "handleSave", "default", "_withCtx", "_cache", "_createTextVNode", "_", "handleCopy", "handleDownload", "_component_TinyMceEditor", "modelValue", "content", "$event", "setting", "height"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiReportGenera\\AiReportGeneraView.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AiReportGeneraView\">\r\n    <div class=\"AiReportGeneraViewButton\">\r\n      <el-button type=\"primary\" @click=\"handleSave\">保存</el-button>\r\n      <el-button type=\"primary\" @click=\"handleCopy\">复制</el-button>\r\n      <el-button type=\"primary\" @click=\"handleDownload\">下载</el-button>\r\n    </div>\r\n    <TinyMceEditor v-model=\"content\" :setting=\"{ height: '100%' }\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AiReportGeneraView' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { ElMessage } from 'element-plus'\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst content = ref('')\r\nconst reportId = ref('')\r\nconst AiParams = ref({})\r\nlet timer = null\r\nconst globalJson = async () => {\r\n  const { code, data } = await api.globalJson(reportId.value ? '/aigptReportRecord/edit' : '/aigptReportRecord/add', {\r\n    form: { id: reportId.value, reportType: route.query.type, content: content.value }\r\n  })\r\n  if (code === 200) {\r\n    if (reportId.value) reportId.value = data\r\n    ElMessage({ type: 'success', message: '保存成功' })\r\n  }\r\n}\r\nconst handleSave = () => {\r\n  if (!content.value.replace(/<[^>]*>/g, '')) return ElMessage({ message: '请先输入内容！', type: 'warning' })\r\n  globalJson()\r\n}\r\nconst handleCopy = () => {\r\n  if (!content.value.replace(/<[^>]*>/g, '')) return ElMessage({ message: '无复制内容', type: 'warning' })\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = content.value.replace(/<[^>]*>/g, '')\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) ElMessage({ message: '复制成功', type: 'success' })\r\n  document.body.removeChild(textarea)\r\n}\r\nconst handleDownload = () => {\r\n  store.commit('setExportWordHtmlObj', { code: 'exportWord', name: '导出内容', key: 'content', data: { content: content.value } })\r\n}\r\nonActivated(() => {\r\n  store.commit('setAiChatWidth', window.innerWidth - 1280)\r\n  store.commit('setAiChatCode', route.query.AiChatCode)\r\n  if (JSON.stringify(AiParams.value) !== '{}') {\r\n    store.commit('setAiChatConfig', {\r\n      AiChatWindow: true,\r\n      AiChatFile: AiParams.value.fileData,\r\n      AiChatParams: {},\r\n      AiChatContent: AiParams.value.toolContent\r\n    })\r\n  }\r\n  const openAiParams = JSON.parse(sessionStorage.getItem('openAiParams')) || ''\r\n  if (openAiParams) {\r\n    AiParams.value = openAiParams\r\n    store.commit('setAiChatConfig', {\r\n      AiChatWindow: true,\r\n      AiChatFile: openAiParams.fileData,\r\n      AiChatParams: { tool: openAiParams.toolId, param: { isPage: '1' } },\r\n      AiChatSendMessage: openAiParams.toolContent\r\n    })\r\n    sessionStorage.setItem('openAiParams', JSON.stringify(''))\r\n    timer = setTimeout(() => {\r\n      store.commit('setAiChatConfig', {\r\n        AiChatWindow: true,\r\n        AiChatFile: openAiParams.fileData,\r\n        AiChatParams: {},\r\n        AiChatContent: openAiParams.toolContent\r\n      })\r\n    }, 2000)\r\n  }\r\n})\r\nonDeactivated(() => {\r\n  if (timer) {\r\n    clearTimeout(timer)\r\n    timer = null\r\n  }\r\n  const width = window.innerWidth - 1280 > 520 ? 520 : 400\r\n  store.commit('setAiChatWidth', width)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatConfig', {\r\n    AiChatWindow: false,\r\n    AiChatFile: [],\r\n    AiChatParams: {},\r\n    AiChatContent: ''\r\n  })\r\n})\r\nonUnmounted(() => {\r\n  if (timer) {\r\n    clearTimeout(timer)\r\n    timer = null\r\n  }\r\n  const width = window.innerWidth - 1280 > 520 ? 520 : 400\r\n  store.commit('setAiChatWidth', width)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatConfig', {\r\n    AiChatWindow: false,\r\n    AiChatFile: [],\r\n    AiChatParams: {},\r\n    AiChatContent: ''\r\n  })\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.AiReportGeneraView {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n\r\n  .AiReportGeneraViewButton {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    padding-bottom: var(--zy-distance-three);\r\n  }\r\n\r\n  .TinyMceEditor {\r\n    height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAA0B;;;;uBADvCC,mBAAA,CAOM,OAPNC,UAOM,GANJC,mBAAA,CAIM,OAJNC,UAIM,GAHJC,YAAA,CAA4DC,oBAAA;IAAjDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,MAAA,CAAAC;;IAHxCC,OAAA,EAAAC,QAAA,CAGoD;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAHtDC,gBAAA,CAGoD,IAAE,E;;IAHtDC,CAAA;MAIMV,YAAA,CAA4DC,oBAAA;IAAjDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,MAAA,CAAAO;;IAJxCL,OAAA,EAAAC,QAAA,CAIoD;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAJtDC,gBAAA,CAIoD,IAAE,E;;IAJtDC,CAAA;MAKMV,YAAA,CAAgEC,oBAAA;IAArDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,MAAA,CAAAQ;;IALxCN,OAAA,EAAAC,QAAA,CAKwD;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAL1DC,gBAAA,CAKwD,IAAE,E;;IAL1DC,CAAA;QAOIV,YAAA,CAAiEa,wBAAA;IAPrEC,UAAA,EAO4BV,MAAA,CAAAW,OAAO;IAPnC,uBAAAP,MAAA,QAAAA,MAAA,gBAAAQ,MAAA;MAAA,OAO4BZ,MAAA,CAAAW,OAAO,GAAAC,MAAA;IAAA;IAAGC,OAAO,EAAE;MAAAC,MAAA;IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}