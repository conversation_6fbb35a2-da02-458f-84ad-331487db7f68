"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[6160],{56160:function(e,t,a){a.r(t),a.d(t,{default:function(){return h}});var n=a(81474),c=(a(76945),a(64352),a(44917)),r=(a(40065),a(74061)),l=a(4955),u={class:"ChatSendImg"},o={class:"ChatSendImgUser"},i={class:"ChatSendImgUserName ellipsis"},d={class:"ChatSendImgBody"},s={class:"ChatSendImgButton"},f={name:"ChatSendImg"},m=Object.assign(f,{props:{chatInfo:{type:Object,default:function(){return{}}},fileImg:{type:Object,default:function(){return{}}}},emits:["callback"],setup(e,t){var a=t.emit,f=e,m=a,g=(0,r.computed)((function(){return f.chatInfo})),h=(0,r.computed)((function(){return f.fileImg})),p=function(e){return e?l.A.fileURL(e):l.A.defaultImgURL("default_user_head.jpg")},v=function(){m("callback",!0)},I=function(){m("callback",!1)};return function(e,t){var a=c.Zq,l=n.S2;return(0,r.openBlock)(),(0,r.createElementBlock)("div",u,[t[2]||(t[2]=(0,r.createElementVNode)("div",{class:"ChatSendImgObject"},"发送给：",-1)),(0,r.createElementVNode)("div",o,[(0,r.createVNode)(a,{src:p(g.value.chatObjectInfo.img),fit:"cover",draggable:"false"},null,8,["src"]),(0,r.createElementVNode)("div",i,(0,r.toDisplayString)(g.value.chatObjectInfo.name),1)]),(0,r.createElementVNode)("div",d,[(0,r.createVNode)(a,{src:h.value.url,fit:"cover",draggable:"false"},null,8,["src"])]),(0,r.createElementVNode)("div",s,[(0,r.createVNode)(l,{onClick:I},{default:(0,r.withCtx)((function(){return t[0]||(t[0]=[(0,r.createTextVNode)("取消")])})),_:1}),(0,r.createVNode)(l,{type:"primary",onClick:v},{default:(0,r.withCtx)((function(){return t[1]||(t[1]=[(0,r.createTextVNode)("发送")])})),_:1})])])}}});const g=m;var h=g}}]);