"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[7637],{7637:function(e,t,r){r.r(t),r.d(t,{default:function(){return Z}});var n=r(79590),a=(r(76945),r(88810),r(81474)),o=(r(64352),r(62427)),u=(r(98773),r(44863)),i=(r(4711),r(71928)),c=(r(31584),r(49744)),l=(r(98326),r(15934),r(44917)),s=(r(40065),r(84098)),d=(r(63584),r(74061)),f=r(4955),p=r(59335),v=r(44500),h=r(88609),y=r(24652);function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function g(e,t,r){return(t=w(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function w(e){var t=k(e,"string");return"symbol"==typeof t?t:t+""}function k(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function x(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */x=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},u=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var o=t&&t.prototype instanceof m?t:m,u=Object.create(o.prototype),i=new j(n||[]);return a(u,"_invoke",{value:V(e,r,i)}),u}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",y={};function m(){}function b(){}function g(){}var w={};l(w,u,(function(){return this}));var k=Object.getPrototypeOf,C=k&&k(k(S([])));C&&C!==r&&n.call(C,u)&&(w=C);var N=g.prototype=m.prototype=Object.create(w);function G(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function r(a,o,u,i){var c=d(e[a],e,o);if("throw"!==c.type){var l=c.arg,s=l.value;return s&&"object"==typeof s&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,u,i)}),(function(e){r("throw",e,u,i)})):t.resolve(s).then((function(e){l.value=e,u(l)}),(function(e){return r("throw",e,u,i)}))}i(c.arg)}var o;a(this,"_invoke",{value:function(e,n){function a(){return new t((function(t,a){r(e,n,t,a)}))}return o=o?o.then(a,a):a()}})}function V(t,r,n){var a=f;return function(o,u){if(a===v)throw Error("Generator is already running");if(a===h){if("throw"===o)throw u;return{value:e,done:!0}}for(n.method=o,n.arg=u;;){var i=n.delegate;if(i){var c=E(i,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===f)throw a=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=v;var l=d(t,r,n);if("normal"===l.type){if(a=n.done?h:p,l.arg===y)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=h,n.method="throw",n.arg=l.arg)}}}function E(t,r){var n=r.method,a=t.iterator[n];if(a===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var o=d(a,t.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,y;var u=o.arg;return u?u.done?(r[t.resultName]=u.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):u:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function S(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function r(){for(;++a<t.length;)if(n.call(t,a))return r.value=t[a],r.done=!1,r;return r.value=e,r.done=!0,r};return o.next=o}}throw new TypeError(typeof t+" is not iterable")}return b.prototype=g,a(N,"constructor",{value:g,configurable:!0}),a(g,"constructor",{value:b,configurable:!0}),b.displayName=l(g,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,l(e,c,"GeneratorFunction")),e.prototype=Object.create(N),e},t.awrap=function(e){return{__await:e}},G(I.prototype),l(I.prototype,i,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,n,a,o){void 0===o&&(o=Promise);var u=new I(s(e,r,n,a),o);return t.isGeneratorFunction(r)?u:u.next().then((function(e){return e.done?e.value:u.next()}))},G(N),l(N,c,"Generator"),l(N,u,(function(){return this})),l(N,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=S,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function a(n,a){return i.type="throw",i.arg=t,r.next=n,a&&(r.method="next",r.arg=e),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var u=this.tryEntries[o],i=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var c=n.call(u,"catchLoc"),l=n.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var u=o?o.completion:{};return u.type=e,u.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(u)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;O(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:S(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function C(e){return V(e)||I(e)||G(e)||N()}function N(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function G(e,t){if(e){if("string"==typeof e)return E(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?E(e,t):void 0}}function I(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function V(e){if(Array.isArray(e))return E(e)}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function L(e,t,r,n,a,o,u){try{var i=e[o](u),c=i.value}catch(e){return void r(e)}i.done?t(c):Promise.resolve(c).then(n,a)}function O(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function u(e){L(o,n,a,u,i,"next",e)}function i(e){L(o,n,a,u,i,"throw",e)}u(void 0)}))}}var j={class:"GlobalCreateGroup"},S={class:"GlobalCreateGroupList"},T={class:"GlobalCreateGroupInput"},B={key:0,class:"GlobalCreateGroupLabel"},_={class:"GlobalCreateGroupItem"},P={class:"GlobalCreateGroupName ellipsis"},A={key:0,class:"GlobalCreateGroupLabel"},D={class:"GlobalCreateGroupItem"},U={class:"GlobalCreateGroupName ellipsis"},M={class:"GlobalCreateGroupBody"},F={class:"GlobalCreateGroupInfo"},$={class:"GlobalCreateGroupInfoName"},R={class:"GlobalCreateGroupUserBody"},q=["onClick"],z={class:"GlobalCreateGroupUserName ellipsis"},Y={class:"GlobalCreateGroupButton"},K={name:"GlobalCreateGroup"},Q=Object.assign(K,{props:{userId:{type:Array,default:function(){return[]}}},emits:["callback"],setup(e,t){var r=t.emit,m=(0,p.useStore)(),g=e,w=r,k=(0,d.computed)((function(){return m.getters.getRongCloudUrl})),N=(0,d.computed)((function(){return m.getters.getIsPrivatization})),G=(0,d.ref)(""),I=(0,d.ref)([]),V=(0,d.ref)(!1),E=(0,d.ref)([]),L=(0,d.ref)([]),K=(0,d.ref)(""),Q=(0,d.ref)([]),W=(0,d.ref)([]),Z=function(e){return e?f.A.fileURL(e):f.A.defaultImgURL("default_user_head.jpg")},H=function(e,t){var r=0;return function(){var n=Date.now();if(n-r>=t){for(var a=arguments.length,o=new Array(a),u=0;u<a;u++)o[u]=arguments[u];e.apply(this,o),r=n}}},J=H(O(x().mark((function e(){var t,r,n,a;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(V.value=!!G.value,t=[],!G.value){e.next=13;break}r=0;case 4:if(!(r<I.value.length)){e.next=13;break}return n=I.value[r],e.next=8,X(n.id);case 8:a=e.sent,t=[].concat(C(t),C(a));case 10:r++,e.next=4;break;case 13:V.value=!1,E.value=C(new Map(t.map((function(e){return[e.id,e]}))).values());case 15:case"end":return e.stop()}}),e)}))),300),X=function(){var e=O(x().mark((function e(t){var r,n,a,o,u;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,f.A.SelectPersonBookUser({isOpen:1,keyword:G.value,labelCode:t,nodeId:"",relationBookId:"",tabCode:"relationBooksTemp"});case 3:for(r=e.sent,n=r.data,a=[],o=0;o<n.length;o++)u=n[o],u.accountId&&a.push({id:u.accountId,label:u.userName,children:[],type:"user",user:u,isLeaf:!0});return e.abrupt("return",a);case 10:return e.prev=10,e.t0=e["catch"](0),e.abrupt("return",[]);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(t){return e.apply(this,arguments)}}(),ee=function(e){var t;null!==(t=Q.value)&&void 0!==t&&t.includes(e.id)?W.value.push(e):W.value=W.value.filter((function(t){return t.id!==e.id}))},te=function(e){Q.value=Q.value.filter((function(t){return t!==e.id})),W.value=W.value.filter((function(t){return t.id!==e.id}))},re=function(){var e=O(x().mark((function e(){var t,r,n,a;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.chatGroupAdd({form:{groupName:K.value,chatGroupType:"1"},ownerUserId:null===(t=h.kQ.value)||void 0===t?void 0:t.accountId,memberUserIds:Q.value});case 2:r=e.sent,n=r.code,a=r.data,200===n&&ne(a);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ne=function(){var e=O(x().mark((function e(t){var r,n;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.chatGroupInfo({detailId:t});case 2:r=e.sent,n=r.data,ae(n);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ae=function(){var e=O(x().mark((function e(t){var r,n;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.rongCloud(k.value,{type:"createGroup",userIds:Q.value.map((function(e){return`${h.TC.value}${e}`})).join(","),groupId:`${h.TC.value}${t.id}`,groupName:K.value,environment:1},N.value);case 2:r=e.sent,n=r.code,200===n&&ue(t);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),oe=function(){var e=O(x().mark((function e(t,r){var n;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,v.getConversation({conversationType:t,targetId:r});case 2:return n=e.sent,e.abrupt("return",n);case 4:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),ue=function(){var e=O(x().mark((function e(t){var r,n,a,o,u,i,c,l,s,d;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=h.TC.value+t.id,e.next=3,oe(3,r);case 3:n=e.sent,a=n.code,o=n.data,a||(d={isTemporary:!0,isTop:o.isTop,isNotInform:o.notificationStatus,id:o.targetId,targetId:o.targetId,type:o.conversationType,chatObjectInfo:{uid:o.targetId,id:t.id,name:t.groupName,img:t.groupImg,userIdData:t.memberUserIds,chatGroupType:"0"!==(null===t||void 0===t||null===(u=t.chatGroupType)||void 0===u?void 0:u.value)?null===t||void 0===t||null===(i=t.chatGroupType)||void 0===i||null===(i=i.name)||void 0===i?void 0:i.slice(0,2):""},sentTime:(null===(c=o.latestMessage)||void 0===c?void 0:c.sentTime)||Date.parse(new Date),messageType:(null===(l=o.latestMessage)||void 0===l?void 0:l.messageType)||"RC:TxtMsg",content:(null===(s=o.latestMessage)||void 0===s?void 0:s.content)||{content:""},count:o.unreadMessageCount},w("callback",d));case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ie=function(){w("callback",!1)},ce=function(){var e=O(x().mark((function e(t){var r,n,a,o,u,i,c;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.SelectPersonTab({tabCodes:["relationBooksTemp"]});case 2:for(r=e.sent,n=r.data,a=[],o=0;o<(null===(u=n[0])||void 0===u?void 0:u.chooseLabels.length);o++)c=null===(i=n[0])||void 0===i?void 0:i.chooseLabels[o],a.push({id:c.labelCode,label:c.name,children:[],type:"label",isLeaf:!1});I.value=a,t(a);case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),le=function(e,t){for(var r=[],n=0;n<t.length;n++){var a=t[n];if(a.code!==e){var o=le(e,a.children);r.push({id:a.code,label:a.name,children:o,type:"tree",isLeaf:!1})}}return r},se=function(){var e=O(x().mark((function e(t){var r,n;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.SelectPersonGroup({labelCode:t,tabCode:"relationBooksTemp"});case 2:return r=e.sent,n=r.data,e.abrupt("return",le(t,n));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),de=function(){var e=O(x().mark((function e(t,r){var n,a,o,u,i;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.SelectPersonBookUser({isOpen:1,keyword:"",labelCode:t,nodeId:r,relationBookId:r,tabCode:"relationBooksTemp"});case 2:for(n=e.sent,a=n.data,o=[],u=0;u<a.length;u++)i=a[u],i.accountId&&o.push({id:i.accountId,label:i.userName,children:[],type:"user",user:i,isLeaf:!0});return e.abrupt("return",o);case 7:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),fe=function(){var e=O(x().mark((function e(t,r){var n,a,o,u,i,c,l;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!==t.level){e.next=4;break}ce(r),e.next=24;break;case 4:if(null===(n=t.data)||void 0===n||null===(n=n.children)||void 0===n||!n.length){e.next=13;break}return o=null===(a=t.data)||void 0===a?void 0:a.children,e.next=8,de(t.parent.key,t.key);case 8:u=e.sent,i=[].concat(C(o),C(u)),r(i),e.next=24;break;case 13:if(!t.parent.level){e.next=20;break}return e.next=16,de(t.parent.key,t.key);case 16:c=e.sent,r(c),e.next=24;break;case 20:return e.next=22,se(t.key);case 22:l=e.sent,r(l);case 24:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),pe=function(){var e=O(x().mark((function e(t){var r,n;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,f.A.userInfoByAccount({accountId:t});case 3:return r=e.sent,n=r.data,e.abrupt("return",n);case 8:return e.prev=8,e.t0=e["catch"](0),e.abrupt("return","");case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(t){return e.apply(this,arguments)}}(),ve=function(){var e=O(x().mark((function e(){var t,r,n,a,o;return x().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=[],r=[],n=[],a=0;case 4:if(!(a<g.userId.length)){e.next=14;break}return e.next=7,pe(g.userId[a]);case 7:o=e.sent,t.push(o.accountId),r.push(o.accountId),n.push({id:o.accountId,label:o.userName,children:[],type:"user",user:b(b({},o),{},{userId:o.id}),del:!0,isLeaf:!0});case 11:a++,e.next=4;break;case 14:L.value=t,Q.value=r,W.value=n;case 17:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,d.onMounted)((function(){g.userId.length&&ve()})),function(e,t){var r=s.WK,f=l.Zq,p=i.dI,v=c.q,h=i.o5,m=u.kA,b=(0,d.resolveComponent)("CircleCloseFilled"),g=o.tk,w=a.S2,k=n.L;return(0,d.openBlock)(),(0,d.createElementBlock)("div",j,[(0,d.createElementVNode)("div",S,[(0,d.createElementVNode)("div",T,[(0,d.createVNode)(r,{modelValue:G.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return G.value=e}),"prefix-icon":(0,d.unref)(y.Search),placeholder:"搜索",onInput:(0,d.unref)(J),clearable:""},null,8,["modelValue","prefix-icon","onInput"])]),(0,d.withDirectives)(((0,d.openBlock)(),(0,d.createBlock)(m,{class:"GlobalCreateGroupScrollbar"},{default:(0,d.withCtx)((function(){return[(0,d.withDirectives)((0,d.createVNode)(h,{modelValue:Q.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return Q.value=e})},{default:(0,d.withCtx)((function(){return[(0,d.createVNode)(v,{"node-key":"id",data:E.value},{default:(0,d.withCtx)((function(e){var t,r=e.data;return["user"!==r.type?((0,d.openBlock)(),(0,d.createElementBlock)("div",B,(0,d.toDisplayString)(r.label),1)):(0,d.createCommentVNode)("",!0),"user"===r.type?((0,d.openBlock)(),(0,d.createBlock)(p,{key:1,value:r.id,disabled:null===(t=L.value)||void 0===t?void 0:t.includes(r.id),onChange:function(e){return ee(r)}},{default:(0,d.withCtx)((function(){return[(0,d.createElementVNode)("div",_,[(0,d.createVNode)(f,{src:Z(r.user.photo||r.user.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,d.createElementVNode)("div",P,(0,d.toDisplayString)(r.user.userName),1)])]})),_:2},1032,["value","disabled","onChange"])):(0,d.createCommentVNode)("",!0)]})),_:1},8,["data"])]})),_:1},8,["modelValue"]),[[d.vShow,!!G.value]]),(0,d.withDirectives)((0,d.createVNode)(h,{modelValue:Q.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return Q.value=e})},{default:(0,d.withCtx)((function(){return[(0,d.createVNode)(v,{lazy:"",load:fe,"node-key":"id",props:{isLeaf:"isLeaf"}},{default:(0,d.withCtx)((function(e){var t,r=e.data;return["user"!==r.type?((0,d.openBlock)(),(0,d.createElementBlock)("div",A,(0,d.toDisplayString)(r.label),1)):(0,d.createCommentVNode)("",!0),"user"===r.type?((0,d.openBlock)(),(0,d.createBlock)(p,{key:1,value:r.id,disabled:null===(t=L.value)||void 0===t?void 0:t.includes(r.id),onChange:function(e){return ee(r)}},{default:(0,d.withCtx)((function(){return[(0,d.createElementVNode)("div",D,[(0,d.createVNode)(f,{src:Z(r.user.photo||r.user.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,d.createElementVNode)("div",U,(0,d.toDisplayString)(r.user.userName),1)])]})),_:2},1032,["value","disabled","onChange"])):(0,d.createCommentVNode)("",!0)]})),_:1})]})),_:1},8,["modelValue"]),[[d.vShow,!G.value]])]})),_:1})),[[k,V.value]])]),(0,d.createElementVNode)("div",M,[(0,d.createElementVNode)("div",F,[(0,d.createElementVNode)("div",$,[t[4]||(t[4]=(0,d.createTextVNode)("发起群聊 ")),(0,d.createElementVNode)("span",null,"已选择"+(0,d.toDisplayString)(W.value.length)+"位联系人",1)]),(0,d.createVNode)(r,{modelValue:K.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return K.value=e}),placeholder:"群聊名称",clearable:""},null,8,["modelValue"])]),(0,d.createVNode)(m,{class:"GlobalCreateGroupUserScroll"},{default:(0,d.withCtx)((function(){return[(0,d.createElementVNode)("div",R,[((0,d.openBlock)(!0),(0,d.createElementBlock)(d.Fragment,null,(0,d.renderList)(W.value,(function(e){var t;return(0,d.openBlock)(),(0,d.createElementBlock)("div",{class:"GlobalCreateGroupUser",key:e.id},[null!==(t=L.value)&&void 0!==t&&t.includes(e.id)?(0,d.createCommentVNode)("",!0):((0,d.openBlock)(),(0,d.createElementBlock)("div",{key:0,class:"GlobalCreateGroupUserDel",onClick:function(t){return te(e)}},[(0,d.createVNode)(g,null,{default:(0,d.withCtx)((function(){return[(0,d.createVNode)(b)]})),_:1})],8,q)),(0,d.createVNode)(f,{src:Z(e.user.photo||e.user.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,d.createElementVNode)("div",z,(0,d.toDisplayString)(e.user.userName),1)])})),128))])]})),_:1}),(0,d.createElementVNode)("div",Y,[(0,d.createVNode)(w,{onClick:ie},{default:(0,d.withCtx)((function(){return t[5]||(t[5]=[(0,d.createTextVNode)("取消")])})),_:1}),(0,d.createVNode)(w,{type:"primary",onClick:re,disabled:!K.value||W.value.length<L.value.length+1},{default:(0,d.withCtx)((function(){return t[6]||(t[6]=[(0,d.createTextVNode)("创建")])})),_:1},8,["disabled"])])])])}}});const W=Q;var Z=W}}]);