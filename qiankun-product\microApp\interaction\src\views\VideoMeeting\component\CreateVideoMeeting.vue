<template>
  <div class="CreateVideoMeeting">
    <el-form ref="formRef" :model="form" :rules="rules" inline label-position="top" class="globalForm">
      <el-form-item label="会议主题" prop="theme" class="globalFormTitle">
        <el-input v-model="form.theme" placeholder="请输入会议主题" clearable />
      </el-form-item>
      <el-form-item label="会议开始时间" prop="startTime" class="InitVideoMeetingTime">
        <xyl-date-picker v-model="form.startDate" type="date" :disabled-date="disabledDate" placeholder="请选择"
          value-format="YYYY-MM-DD" format="YYYY-MM-DD" :teleported="false" :clearable="false" />
        <el-time-select v-model="form.startTime" start="00:00" step="00:15" end="23:45" placeholder="请选择"
          :teleported="false" :clearable="false" />
      </el-form-item>
      <el-form-item label="会议时长" prop="duration">
        <el-select v-model="form.duration" placeholder="请选择会议时长" :teleported="false" clearable>
          <el-option v-for="item in VideoMeetingTime" :key="item.key" :label="item.name" :value="item.key" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否开启直播">
        <el-radio-group v-model="form.isLive">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="参会人员" prop="joinUserIds" class="globalFormTitle">
        <business-select-person v-model="form.joinUserIds" @callback="userCallback"></business-select-person>
      </el-form-item>
      <template v-if="form.isLive === 1">
        <div class="globalFormName">直播相关配置</div>
        <el-form-item label="直播简介" prop="liveDescribes" class="globalFormTitle">
          <el-input v-model="form.liveDescribes" type="textarea" placeholder="请输入直播简介" clearable rows="4"
            :disabled="props.id != ''" />
        </el-form-item>
        <el-form-item label="直播推流地址" class="globalFormTitle">
          <el-input v-model="form.liveUrl" placeholder="请输入直播推流地址" clearable :disabled="props.id != ''" />
        </el-form-item>
        <el-form-item label="直播回放地址" class="globalFormTitle">
          <el-input v-model="form.liveReplayUrl" placeholder="请输入直播回放地址" clearable :disabled="props.id != ''" />
        </el-form-item>
        <el-form-item label="是否开启直播回放">
          <el-radio-group v-model="form.isReplay" :disabled="props.id != ''">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否推送群众端查看">
          <el-radio-group v-model="form.isPublic" :disabled="props.id != ''">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否开启直播互动">
          <el-radio-group v-model="form.isInteraction" :disabled="props.id != ''">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="coverImg">
          <template #label>
            上传封面
            <span class="label-gary-tips"> (建议宽高尺寸比为16:9) </span>
          </template>
          <div>
            <div class="informationDetailsImgBox">
              <xyl-upload-img :max="1" ref="imgFileRef" :fileId="imgParams.imgFileName"
                :fileData="imgParams.imgFileData" :beforeUpload="handleBeforeUpload" @fileUpload="imgGlobalUpload"
                height="90px" :disabled="props.id != ''" />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="直播设备号">
          <el-input v-model="form.liveNumber" placeholder="请输入直播设备号" clearable :disabled="props.id != ''" />
        </el-form-item>
      </template>
      <div class="globalFormButton">
        <el-button type="primary" @click="submitForm(formRef)">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
  <xyl-popup-window v-model="cropperShow" name="封面">
    <global-cropper :enlarge="3" :width="375" :height="210" :cropperStyle="{ width: '520px', height: '360px' }"
      :file="cropperFile" @callback="handleCropper"></global-cropper>
  </xyl-popup-window>
</template>
<script>
export default { name: 'CreateVideoMeeting' }
</script>
<script setup>
import api from '@/api'
import { reactive, ref, onMounted } from 'vue'
import { format } from 'common/js/time.js'
import { ElMessage } from 'element-plus'
import GlobalCropper from 'common/components/global-cropper/global-cropper.vue'
const props = defineProps({ id: { type: String, default: '' } })
const emit = defineEmits(['callback'])

const formRef = ref()
const form = reactive({
  theme: '',
  startDate: '',
  startTime: '',
  duration: '',
  isLive: 0, // 是否开启直播
  joinUserIds: [],
  liveDescribes: '', // 直播简介
  liveUrl: '', // 直播推流地址
  liveReplayUrl: '', // 直播回放地址
  isReplay: 0, // 是否开启直播回放
  isPublic: 0, // 是否推送群众端查看
  isInteraction: 1, // 是否开启直播互动
  coverImg: '', // 封面必填校验
  liveNumber: '', // 直播设备号
})
const imgFileRef = ref()
const imgParams = ref({
  imgFileName: '',
  imgFileData: []
})
const cropperFile = ref({})
const cropperShow = ref(false)
const cropperCallback = ref()
const rules = reactive({
  theme: [{ required: true, message: '请输入会议主题', trigger: ['blur', 'change'] }],
  startTime: [{ required: true, message: '请选择会议开始时间', trigger: ['blur', 'change'] }],
  duration: [{ required: true, message: '请选择会议时长', trigger: ['blur', 'change'] }],
  joinUserIds: [{ required: true, message: '请选择参会人员', trigger: ['blur', 'change'] }],
  liveDescribes: [{ required: true, message: '请输入直播简介', trigger: ['blur', 'change'] }],
  coverImg: [{ required: true, message: '请上传封面', trigger: ['blur', 'change'] }],
})
const disabledDate = (time) => time.getTime() < Date.now() - 8.64e7
const VideoMeetingTime = ref([])
/**
 * 处理自动生成的推荐日期
 */
const getStartTime = () => {
  var newDate = new Date().getTime()
  var [hours, minutes] = format(newDate, 'HH:mm').split(':') // 处理时分
  if (parseInt(minutes) >= 0 && parseInt(minutes) < 15) {
    minutes = '15'
  } else if (parseInt(minutes) >= 15 && parseInt(minutes) < 30) {
    minutes = '30'
  } else if (parseInt(minutes) >= 30 && parseInt(minutes) < 45) {
    minutes = '45'
  } else if (parseInt(minutes) >= 45 && parseInt(minutes) < 60) {
    minutes = '00'
    hours = (parseInt(hours) + 1) > 23 ? '00' : (parseInt(hours) + 1).toString()
    if ((parseInt(hours) + 1) > 23) {
      newDate = newDate + 24 * 60 * 60 * 1000
    }
  }
  form.startDate = format(newDate, 'YYYY-MM-DD') // 处理日期
  form.startTime = hours + ':' + minutes // 处理时间
}
onMounted(() => {
  getStartTime()
  dictionaryData()
  if (props.id) { videoConnectionInfo() }
})
const dictionaryData = async () => {
  const { data } = await api.dictionaryData({ dictCodes: ['video_meeting_time'] })
  VideoMeetingTime.value = data.video_meeting_time || []
}
const videoConnectionInfo = async () => {
  const res = await api.videoConnectionInfo({ detailId: props.id })
  var { data } = res
  form.theme = data.theme
  form.duration = String(data.during)
  form.isLive = data.isLive
  form.startDate = format(data.startTime, 'YYYY-MM-DD')
  form.startTime = format(data.startTime, 'HH:mm')
  form.joinUserIds = data.joinUsers?.map(v => v.userId)
  form.liveDescribes = data.liveDescribes
  form.liveUrl = data.liveUrl
  form.liveReplayUrl = data.liveReplayUrl
  form.isReplay = data.isReplay
  form.isPublic = data.isPublic
  form.isInteraction = data.isInteraction
  imgParams.value.imgFileName = data.coverImg
  form.coverImg = data.coverImg  // 编辑模式下同步设置form.coverImg
  form.liveNumber = data.liveNumber
}
const userCallback = () => {
  if (formRef.value) {
    formRef.value.validateField('memberUserIds')
  }
}
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) { globalJson() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }
  })
}
const globalJson = async () => {
  const { code } = await api.globalJson(props.id ? '/videoConnection/edit' : '/videoConnection/add', {
    form: {
      id: props.id,
      theme: form.theme,
      during: form.duration,
      isLive: form.isLive,
      startTime: new Date(form.startDate + ' ' + form.startTime).getTime(),
      endTime: new Date(form.startDate + ' ' + form.startTime).getTime() + (60000 * Number(form.duration)),
      liveDescribes: form.liveDescribes,
      liveUrl: form.liveUrl,
      liveReplayUrl: form.liveReplayUrl,
      isReplay: form.isReplay,
      isPublic: form.isPublic,
      isInteraction: form.isInteraction,
      coverImg: imgParams.value.imgFileName,
      liveNumber: form.liveNumber
    },
    joinUserIds: form.joinUserIds
  })
  if (code === 200) {
    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })
    emit('callback')
  }
}
const resetForm = () => { emit('callback') }
const handleBeforeUpload = (file, callbackFn) => {
  cropperFile.value = file
  cropperShow.value = true
  cropperCallback.value = callbackFn
}
const handleCropper = (file) => {
  cropperShow.value = false
  if (file) cropperCallback.value(file)
}
const imgGlobalUpload = (file) => {
  form.imgFileFlag = ''
  if (file.newFileName) {
    imgParams.value.imgFileName = file.newFileName
    form.coverImg = file.newFileName  // 同步更新表单的coverImg字段
    form.imgFileFlag = '1'
  } else {
    imgParams.value.imgFileName = ''
    form.coverImg = ''  // 清空表单的coverImg字段
  }
  formRef.value.validateField('coverImg')  // 验证coverImg字段而不是imgFileFlag
}
</script>
<style lang="scss">
.CreateVideoMeeting {
  width: 990px;

  .InitVideoMeetingTime {
    width: calc(580px + var(--zy-distance-two));

    .zy-el-form-item__content {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
