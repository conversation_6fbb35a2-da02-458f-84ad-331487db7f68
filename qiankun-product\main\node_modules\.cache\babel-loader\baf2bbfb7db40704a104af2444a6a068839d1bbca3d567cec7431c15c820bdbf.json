{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"BackgroundCheckDetails\"\n};\nvar _hoisted_2 = {\n  class: \"BackgroundCheckDetailsBody\"\n};\nvar _hoisted_3 = {\n  class: \"BackgroundCheckDetailsTime\"\n};\nvar _hoisted_4 = {\n  class: \"BackgroundCheckDetailsInfo\"\n};\nvar _hoisted_5 = {\n  class: \"BackgroundCheckDetailsImg\"\n};\nvar _hoisted_6 = {\n  class: \"BackgroundCheckDetailsInfoItem\"\n};\nvar _hoisted_7 = {\n  class: \"BackgroundCheckDetailsUserName\"\n};\nvar _hoisted_8 = {\n  class: \"BackgroundCheckDetailsUserIdcard\"\n};\nvar _hoisted_9 = {\n  class: \"BackgroundCheckDetailsInfoItem\"\n};\nvar _hoisted_10 = {\n  class: \"BackgroundCheckDetailsText\"\n};\nvar _hoisted_11 = {\n  class: \"BackgroundCheckDetailsText\"\n};\nvar _hoisted_12 = {\n  class: \"BackgroundCheckDetailsResult\"\n};\nvar _hoisted_13 = {\n  class: \"BackgroundCheckDetailsResultRisk\"\n};\nvar _hoisted_14 = {\n  class: \"BackgroundCheckDetailsResultRiskName\"\n};\nvar _hoisted_15 = {\n  class: \"BackgroundCheckDetailsResultItem\"\n};\nvar _hoisted_16 = {\n  class: \"BackgroundCheckDetailsResultItemName\"\n};\nvar _hoisted_17 = {\n  class: \"BackgroundCheckDetailsResultItem\"\n};\nvar _hoisted_18 = {\n  class: \"BackgroundCheckDetailsResultItemName\"\n};\nvar _hoisted_19 = {\n  class: \"globalTable\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_WarnTriangleFilled = _resolveComponent(\"WarnTriangleFilled\");\n  var _component_CircleCloseFilled = _resolveComponent(\"CircleCloseFilled\");\n  var _component_WarningFilled = _resolveComponent(\"WarningFilled\");\n  var _component_CircleCheckFilled = _resolveComponent(\"CircleCheckFilled\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_MessageBox = _resolveComponent(\"MessageBox\");\n  var _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  var _component_el_tabs = _resolveComponent(\"el-tabs\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_anchor_location = _resolveComponent(\"anchor-location\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [$setup.route.query.name && $setup.route.query.idcard ? (_openBlock(), _createBlock(_component_anchor_location, {\n    key: 0\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_2, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n        class: \"BackgroundCheckDetailsTitle\"\n      }, \"个人司法背景审查报告\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, \"数据截止至：\" + _toDisplayString($setup.format(new Date(), 'YYYY-MM-DD')), 1 /* TEXT */), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n        class: \"BackgroundCheckDetailsTips\"\n      }, \"免责声明：大数据风险排查，不能作为最终判定\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_image, {\n        src: $setup.api.defaultImgURL('default_user_head.jpg'),\n        fit: \"cover\"\n      }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.route.query.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.route.query.idcard), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, \"发起人：\" + _toDisplayString($setup.user.userName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, \"发起时间：\" + _toDisplayString($setup.format(new Date(), 'YYYY-MM-DD HH:mm')), 1 /* TEXT */)])]), _createVNode($setup[\"DetailsTitle\"], null, {\n        default: _withCtx(function () {\n          return _cache[1] || (_cache[1] = [_createElementVNode(\"div\", {\n            class: \"BackgroundCheckDetailsName\"\n          }, \"审查结果\", -1 /* HOISTED */)]);\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"BackgroundCheckDetailsResultRiskIcon\", $setup.classObj[$setup.details.totalLevel]])\n      }, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [['A', 'B'].includes($setup.details.totalLevel) ? (_openBlock(), _createBlock(_component_WarnTriangleFilled, {\n            key: 0\n          })) : _createCommentVNode(\"v-if\", true), ['C'].includes($setup.details.totalLevel) ? (_openBlock(), _createBlock(_component_CircleCloseFilled, {\n            key: 1\n          })) : _createCommentVNode(\"v-if\", true), ['D'].includes($setup.details.totalLevel) ? (_openBlock(), _createBlock(_component_WarningFilled, {\n            key: 2\n          })) : _createCommentVNode(\"v-if\", true), ['E'].includes($setup.details.totalLevel) ? (_openBlock(), _createBlock(_component_CircleCheckFilled, {\n            key: 3\n          })) : _createCommentVNode(\"v-if\", true)];\n        }),\n        _: 1 /* STABLE */\n      })], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_14, _toDisplayString($setup.tipsObj[$setup.details.totalLevel]), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"BackgroundCheckDetailsResultItemIcon\", {\n          anomaly: $setup.info.case === '是'\n        }])\n      }, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_MessageBox)];\n        }),\n        _: 1 /* STABLE */\n      })], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_16, _toDisplayString($setup.info.case === '是' ? '有涉诉案件记录' : '无涉诉案件记录'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"BackgroundCheckDetailsResultItemIcon\", {\n          anomaly: $setup.info.risk === '是'\n        }])\n      }, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_MessageBox)];\n        }),\n        _: 1 /* STABLE */\n      })], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_18, _toDisplayString($setup.info.risk === '是' ? '有不良风险记录' : '无不良风险记录'), 1 /* TEXT */)])]), _createVNode($setup[\"DetailsTitle\"], null, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n            class: \"BackgroundCheckDetailsName\"\n          }, \"审查纪录\", -1 /* HOISTED */)]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_tabs, {\n        modelValue: $setup.activeName,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.activeName = $event;\n        }),\n        onTabChange: $setup.handleClick\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.activeList, function (item) {\n            return _openBlock(), _createBlock(_component_el_tab_pane, {\n              key: item.id,\n              name: item.id,\n              label: item.name\n            }, null, 8 /* PROPS */, [\"name\", \"label\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_table, {\n        data: $setup.tableData,\n        border: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_table_column, {\n            label: \"\",\n            \"min-width\": \"220\",\n            prop: \"name\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"\",\n            \"min-width\": \"120\"\n          }, {\n            default: _withCtx(function (scope) {\n              return [_createTextVNode(_toDisplayString($setup.levelObj[scope.row.level] || scope.row.level), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"data\"])])])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "$setup", "route", "query", "name", "idcard", "_createBlock", "_component_anchor_location", "key", "default", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "format", "Date", "_hoisted_4", "_hoisted_5", "_createVNode", "_component_el_image", "src", "api", "defaultImgURL", "fit", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "user", "userName", "_hoisted_11", "_cache", "_", "_hoisted_12", "_hoisted_13", "_normalizeClass", "classObj", "details", "totalLevel", "_component_el_icon", "includes", "_component_WarnTriangleFilled", "_createCommentVNode", "_component_CircleCloseFilled", "_component_WarningFilled", "_component_CircleCheckFilled", "_hoisted_14", "tipsObj", "_hoisted_15", "anomaly", "info", "case", "_component_MessageBox", "_hoisted_16", "_hoisted_17", "risk", "_hoisted_18", "_component_el_tabs", "modelValue", "activeName", "$event", "onTabChange", "handleClick", "_Fragment", "_renderList", "activeList", "item", "_component_el_tab_pane", "id", "label", "_hoisted_19", "_component_el_table", "data", "tableData", "border", "_component_el_table_column", "prop", "scope", "_createTextVNode", "levelObj", "row", "level"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\BackgroundCheck\\BackgroundCheckDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"BackgroundCheckDetails\">\r\n    <anchor-location v-if=\"route.query.name && route.query.idcard\">\r\n      <div class=\"BackgroundCheckDetailsBody\">\r\n        <div class=\"BackgroundCheckDetailsTitle\">个人司法背景审查报告</div>\r\n        <div class=\"BackgroundCheckDetailsTime\">数据截止至：{{ format(new Date(), 'YYYY-MM-DD') }}</div>\r\n        <div class=\"BackgroundCheckDetailsTips\">免责声明：大数据风险排查，不能作为最终判定</div>\r\n        <div class=\"BackgroundCheckDetailsInfo\">\r\n          <div class=\"BackgroundCheckDetailsImg\">\r\n            <el-image :src=\"api.defaultImgURL('default_user_head.jpg')\" fit=\"cover\" />\r\n          </div>\r\n          <div class=\"BackgroundCheckDetailsInfoItem\">\r\n            <div class=\"BackgroundCheckDetailsUserName\">{{ route.query.name }}</div>\r\n            <div class=\"BackgroundCheckDetailsUserIdcard\">{{ route.query.idcard }}</div>\r\n          </div>\r\n          <div class=\"BackgroundCheckDetailsInfoItem\">\r\n            <div class=\"BackgroundCheckDetailsText\">发起人：{{ user.userName }}</div>\r\n            <div class=\"BackgroundCheckDetailsText\">发起时间：{{ format(new Date(), 'YYYY-MM-DD HH:mm') }}</div>\r\n          </div>\r\n        </div>\r\n        <DetailsTitle>\r\n          <div class=\"BackgroundCheckDetailsName\">审查结果</div>\r\n        </DetailsTitle>\r\n        <div class=\"BackgroundCheckDetailsResult\">\r\n          <div class=\"BackgroundCheckDetailsResultRisk\">\r\n            <div class=\"BackgroundCheckDetailsResultRiskIcon\" :class=\"classObj[details.totalLevel]\">\r\n              <el-icon>\r\n                <WarnTriangleFilled v-if=\"['A', 'B'].includes(details.totalLevel)\" />\r\n                <CircleCloseFilled v-if=\"['C'].includes(details.totalLevel)\" />\r\n                <WarningFilled v-if=\"['D'].includes(details.totalLevel)\" />\r\n                <CircleCheckFilled v-if=\"['E'].includes(details.totalLevel)\" />\r\n              </el-icon>\r\n            </div>\r\n            <div class=\"BackgroundCheckDetailsResultRiskName\">{{ tipsObj[details.totalLevel] }}</div>\r\n          </div>\r\n          <div class=\"BackgroundCheckDetailsResultItem\">\r\n            <div class=\"BackgroundCheckDetailsResultItemIcon\" :class=\"{ anomaly: info.case === '是' }\">\r\n              <el-icon>\r\n                <MessageBox />\r\n              </el-icon>\r\n            </div>\r\n            <div class=\"BackgroundCheckDetailsResultItemName\">{{ info.case === '是' ? '有涉诉案件记录' : '无涉诉案件记录' }}</div>\r\n          </div>\r\n          <div class=\"BackgroundCheckDetailsResultItem\">\r\n            <div class=\"BackgroundCheckDetailsResultItemIcon\" :class=\"{ anomaly: info.risk === '是' }\">\r\n              <el-icon>\r\n                <MessageBox />\r\n              </el-icon>\r\n            </div>\r\n            <div class=\"BackgroundCheckDetailsResultItemName\">{{ info.risk === '是' ? '有不良风险记录' : '无不良风险记录' }}</div>\r\n          </div>\r\n        </div>\r\n        <DetailsTitle>\r\n          <div class=\"BackgroundCheckDetailsName\">审查纪录</div>\r\n        </DetailsTitle>\r\n        <el-tabs v-model=\"activeName\" @tab-change=\"handleClick\">\r\n          <el-tab-pane v-for=\"item in activeList\" :key=\"item.id\" :name=\"item.id\" :label=\"item.name\"></el-tab-pane>\r\n        </el-tabs>\r\n        <div class=\"globalTable\">\r\n          <el-table :data=\"tableData\" border>\r\n            <el-table-column label=\"\" min-width=\"220\" prop=\"name\" />\r\n            <el-table-column label=\"\" min-width=\"120\">\r\n              <template #default=\"scope\">\r\n                {{ levelObj[scope.row.level] || scope.row.level }}\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </anchor-location>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'BackgroundCheckDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { format } from 'common/js/time.js'\r\nimport { user } from 'common/js/system_var.js'\r\nimport DetailsTitle from './components/DetailsTitle.vue'\r\nconst route = useRoute()\r\nconst tipsObj = {\r\n  'A': '严重风险',\r\n  'B': '高风险',\r\n  'C': '中风险',\r\n  'D': '低风险',\r\n  'E': '无风险'\r\n}\r\nconst classObj = {\r\n  'A': 'WarnTriangleFilled',\r\n  'B': 'WarnTriangleFilled',\r\n  'C': 'CircleCloseFilled',\r\n  'D': 'WarningFilled',\r\n  'E': 'CircleCheckFilled'\r\n}\r\nconst levelObj = {\r\n  '0级': '案件数量 0 件',\r\n  '1级': '案件数量 1 ～ 5 件',\r\n  '2级': '案件数量 6 ～ 15 件',\r\n  '3级': '案件数量 16 ～ 30 件',\r\n  '4级': '案件数量 31 ～ 50 件',\r\n  '5级': '案件数量大于 50 件'\r\n}\r\nconst info = ref({})\r\nconst details = ref({})\r\nconst activeName = ref('')\r\nconst activeList = ref([])\r\nconst tableData = ref([])\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)\r\n    return v.toString(16)\r\n  })\r\n}\r\n\r\nconst backReview = async () => {\r\n  const { data } = await api.backReview({ name: route.query.name, idcard: route.query.idcard, createDate: route.query.createDate, authorize: '1' })\r\n  info.value = data\r\n}\r\nconst employRisks = async () => {\r\n  const { data } = await api.employRisks({ name: route.query.name, idcard: route.query.idcard, createDate: route.query.createDate, authorize: '1' })\r\n  details.value = data\r\n  activeList.value = data?.infoList?.map(v => ({ ...v, id: guid(), name: v.itemName.slice(0, 4) === '是否存在' ? v.itemName.slice(4) : v.itemName }))\r\n  activeName.value = activeList.value[0]?.id\r\n  handleClick(activeName.value)\r\n}\r\nconst handleClick = (tab) => {\r\n  for (let index = 0; index < activeList.value.length; index++) {\r\n    const item = activeList.value[index]\r\n    if (item.id === tab) {\r\n      tableData.value = item?.itemList\r\n    }\r\n  }\r\n}\r\nonMounted(() => {\r\n  if (route.query.name && route.query.idcard) {\r\n    backReview()\r\n    employRisks()\r\n  }\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.BackgroundCheckDetails {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .BackgroundCheckDetailsBody {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: var(--zy-distance-one);\r\n\r\n    .BackgroundCheckDetailsTitle {\r\n      font-weight: bold;\r\n      text-align: center;\r\n      font-size: var(--zy-title-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-bottom: var(--zy-distance-two);\r\n    }\r\n\r\n    .BackgroundCheckDetailsName {\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .BackgroundCheckDetailsTime {\r\n      font-weight: bold;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-bottom: var(--zy-font-name-distance-five);\r\n    }\r\n\r\n    .BackgroundCheckDetailsTips {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-color-danger);\r\n      padding-bottom: var(--zy-distance-two);\r\n    }\r\n\r\n    .BackgroundCheckDetailsInfo {\r\n      display: flex;\r\n      padding: var(--zy-distance-two) var(--zy-distance-one);\r\n      background-color: var(--zy-el-color-info-light-9);\r\n      margin-bottom: var(--zy-distance-two);\r\n\r\n      .BackgroundCheckDetailsImg {\r\n        width: 60px;\r\n        height: 60px;\r\n        margin-right: 9px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          height: 100%;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n\r\n      .BackgroundCheckDetailsInfoItem {\r\n        width: calc(33.3% - 23px);\r\n        padding: 0 var(--zy-distance-five);\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n\r\n        .BackgroundCheckDetailsUserName {\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n\r\n        .BackgroundCheckDetailsUserIdcard {\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n\r\n        .BackgroundCheckDetailsText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: calc(var(--zy-name-font-size) * var(--zy-line-height));\r\n        }\r\n      }\r\n    }\r\n\r\n    .BackgroundCheckDetailsResult {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      justify-content: space-between;\r\n      position: relative;\r\n\r\n      .BackgroundCheckDetailsResultRisk {\r\n        width: calc(33.3% - 13px);\r\n        height: 52px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: var(--zy-distance-three) var(--zy-distance-two);\r\n        margin-bottom: var(--zy-distance-two);\r\n        background-color: var(--zy-el-color-info-light-9);\r\n\r\n        .BackgroundCheckDetailsResultRiskIcon {\r\n          width: 30px;\r\n          height: 30px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 6px;\r\n\r\n          .zy-el-icon {\r\n            font-size: 20px;\r\n            color: #fff;\r\n          }\r\n        }\r\n\r\n        .WarnTriangleFilled {\r\n          background: var(--zy-el-color-danger);\r\n        }\r\n\r\n        .CircleCloseFilled {\r\n          background: var(--zy-el-color-danger-light-3);\r\n        }\r\n\r\n        .WarningFilled {\r\n          background: var(--zy-el-color-warning-light-3);\r\n        }\r\n\r\n        .CircleCheckFilled {\r\n          background: var(--zy-el-color-success-light-3);\r\n        }\r\n\r\n        .BackgroundCheckDetailsResultRiskIcon.anomaly {\r\n          background: var(--zy-el-color-danger-light-3);\r\n        }\r\n\r\n        .BackgroundCheckDetailsResultRiskName {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 0 var(--zy-distance-four);\r\n          font-size: var(--zy-text-font-size);\r\n\r\n          span {\r\n            margin-left: 10px;\r\n            font-weight: bold;\r\n            font-size: var(--zy-name-font-size);\r\n            color: var(--zy-el-color-danger);\r\n          }\r\n        }\r\n      }\r\n\r\n      .BackgroundCheckDetailsResultItem {\r\n        width: calc(33.3% - 13px);\r\n        height: 52px;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: var(--zy-distance-three) var(--zy-distance-two);\r\n        margin-bottom: var(--zy-distance-two);\r\n        background-color: var(--zy-el-color-info-light-9);\r\n\r\n        .BackgroundCheckDetailsResultItemIcon {\r\n          width: 30px;\r\n          height: 30px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          background: var(--zy-el-color-success-light-3);\r\n          border-radius: 6px;\r\n\r\n          .zy-el-icon {\r\n            font-size: 20px;\r\n            color: #fff;\r\n          }\r\n        }\r\n\r\n\r\n        .BackgroundCheckDetailsResultItemIcon.anomaly {\r\n          background: var(--zy-el-color-danger-light-3);\r\n        }\r\n\r\n        .BackgroundCheckDetailsResultItemName {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 0 var(--zy-distance-four);\r\n          font-size: var(--zy-text-font-size);\r\n\r\n          span {\r\n            margin-left: 10px;\r\n            font-weight: bold;\r\n            font-size: var(--zy-name-font-size);\r\n            color: var(--zy-el-color-danger);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .globalTable {\r\n      width: 100%;\r\n      height: 280px;\r\n\r\n      .zy-el-table thead .zy-el-table__cell {\r\n        padding: 0;\r\n      }\r\n\r\n      .zy-el-table .zy-el-table__cell {\r\n        border-right: var(--zy-el-table-border) !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAA4B;;EAEhCA,KAAK,EAAC;AAA4B;;EAElCA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAA2B;;EAGjCA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAAgC;;EACtCA,KAAK,EAAC;AAAkC;;EAE1CA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAA4B;;EAClCA,KAAK,EAAC;AAA4B;;EAMtCA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAAkC;;EAStCA,KAAK,EAAC;AAAsC;;EAE9CA,KAAK,EAAC;AAAkC;;EAMtCA,KAAK,EAAC;AAAsC;;EAE9CA,KAAK,EAAC;AAAkC;;EAMtCA,KAAK,EAAC;AAAsC;;EAShDA,KAAK,EAAC;AAAa;;;;;;;;;;;;;;uBAzD9BC,mBAAA,CAqEM,OArENC,UAqEM,GApEmBC,MAAA,CAAAC,KAAK,CAACC,KAAK,CAACC,IAAI,IAAIH,MAAA,CAAAC,KAAK,CAACC,KAAK,CAACE,MAAM,I,cAA7DC,YAAA,CAmEkBC,0BAAA;IArEtBC,GAAA;EAAA;IAAAC,OAAA,EAAAC,QAAA,CAGM;MAAA,OAiEM,CAjENC,mBAAA,CAiEM,OAjENC,UAiEM,G,0BAhEJD,mBAAA,CAAyD;QAApDb,KAAK,EAAC;MAA6B,GAAC,YAAU,sBACnDa,mBAAA,CAA0F,OAA1FE,UAA0F,EAAlD,QAAM,GAAAC,gBAAA,CAAGb,MAAA,CAAAc,MAAM,KAAKC,IAAI,mC,0BAChEL,mBAAA,CAAmE;QAA9Db,KAAK,EAAC;MAA4B,GAAC,uBAAqB,sBAC7Da,mBAAA,CAYM,OAZNM,UAYM,GAXJN,mBAAA,CAEM,OAFNO,UAEM,GADJC,YAAA,CAA0EC,mBAAA;QAA/DC,GAAG,EAAEpB,MAAA,CAAAqB,GAAG,CAACC,aAAa;QAA2BC,GAAG,EAAC;0CAElEb,mBAAA,CAGM,OAHNc,UAGM,GAFJd,mBAAA,CAAwE,OAAxEe,UAAwE,EAAAZ,gBAAA,CAAzBb,MAAA,CAAAC,KAAK,CAACC,KAAK,CAACC,IAAI,kBAC/DO,mBAAA,CAA4E,OAA5EgB,UAA4E,EAAAb,gBAAA,CAA3Bb,MAAA,CAAAC,KAAK,CAACC,KAAK,CAACE,MAAM,iB,GAErEM,mBAAA,CAGM,OAHNiB,UAGM,GAFJjB,mBAAA,CAAqE,OAArEkB,WAAqE,EAA7B,MAAI,GAAAf,gBAAA,CAAGb,MAAA,CAAA6B,IAAI,CAACC,QAAQ,kBAC5DpB,mBAAA,CAA+F,OAA/FqB,WAA+F,EAAvD,OAAK,GAAAlB,gBAAA,CAAGb,MAAA,CAAAc,MAAM,KAAKC,IAAI,wC,KAGnEG,YAAA,CAEelB,MAAA;QAtBvBQ,OAAA,EAAAC,QAAA,CAqBU;UAAA,OAAkDuB,MAAA,QAAAA,MAAA,OAAlDtB,mBAAA,CAAkD;YAA7Cb,KAAK,EAAC;UAA4B,GAAC,MAAI,oB;;QArBtDoC,CAAA;UAuBQvB,mBAAA,CA4BM,OA5BNwB,WA4BM,GA3BJxB,mBAAA,CAUM,OAVNyB,WAUM,GATJzB,mBAAA,CAOM;QAPDb,KAAK,EAzBtBuC,eAAA,EAyBuB,sCAAsC,EAASpC,MAAA,CAAAqC,QAAQ,CAACrC,MAAA,CAAAsC,OAAO,CAACC,UAAU;UACnFrB,YAAA,CAKUsB,kBAAA;QA/BxBhC,OAAA,EAAAC,QAAA,CAyCQ;UAAA,OACc,C,WAf+BgC,QAAQ,CAACzC,MAAA,CAAAsC,OAAO,CAACC,UAAU,K,cAAhElC,YAAA,CAAqEqC,6BAAA;YA3BrFnC,GAAA;UAAA,MAAAoC,mBAAA,gB,MA4B+CF,QAAQ,CAACzC,MAAA,CAAAsC,OAAO,CAACC,UAAU,K,cAA1DlC,YAAA,CAA+DuC,4BAAA;YA5B/ErC,GAAA;UAAA,MAAAoC,mBAAA,gB,MA6B2CF,QAAQ,CAACzC,MAAA,CAAAsC,OAAO,CAACC,UAAU,K,cAAtDlC,YAAA,CAA2DwC,wBAAA;YA7B3EtC,GAAA;UAAA,MAAAoC,mBAAA,gB,MA8B+CF,QAAQ,CAACzC,MAAA,CAAAsC,OAAO,CAACC,UAAU,K,cAA1DlC,YAAA,CAA+DyC,4BAAA;YA9B/EvC,GAAA;UAAA,MAAAoC,mBAAA,e;;QAAAV,CAAA;2BAiCYvB,mBAAA,CAAyF,OAAzFqC,WAAyF,EAAAlC,gBAAA,CAApCb,MAAA,CAAAgD,OAAO,CAAChD,MAAA,CAAAsC,OAAO,CAACC,UAAU,kB,GAEjF7B,mBAAA,CAOM,OAPNuC,WAOM,GANJvC,mBAAA,CAIM;QAJDb,KAAK,EApCtBuC,eAAA,EAoCuB,sCAAsC;UAAAc,OAAA,EAAoBlD,MAAA,CAAAmD,IAAI,CAACC,IAAI;QAAA;UAC5ElC,YAAA,CAEUsB,kBAAA;QAvCxBhC,OAAA,EAAAC,QAAA,CAsCgB;UAAA,OAAc,CAAdS,YAAA,CAAcmC,qBAAA,E;;QAtC9BpB,CAAA;2BAyCYvB,mBAAA,CAAuG,OAAvG4C,WAAuG,EAAAzC,gBAAA,CAAlDb,MAAA,CAAAmD,IAAI,CAACC,IAAI,iD,GAEhE1C,mBAAA,CAOM,OAPN6C,WAOM,GANJ7C,mBAAA,CAIM;QAJDb,KAAK,EA5CtBuC,eAAA,EA4CuB,sCAAsC;UAAAc,OAAA,EAAoBlD,MAAA,CAAAmD,IAAI,CAACK,IAAI;QAAA;UAC5EtC,YAAA,CAEUsB,kBAAA;QA/CxBhC,OAAA,EAAAC,QAAA,CA8CgB;UAAA,OAAc,CAAdS,YAAA,CAAcmC,qBAAA,E;;QA9C9BpB,CAAA;2BAiDYvB,mBAAA,CAAuG,OAAvG+C,WAAuG,EAAA5C,gBAAA,CAAlDb,MAAA,CAAAmD,IAAI,CAACK,IAAI,iD,KAGlEtC,YAAA,CAEelB,MAAA;QAtDvBQ,OAAA,EAAAC,QAAA,CAqDU;UAAA,OAAkDuB,MAAA,QAAAA,MAAA,OAAlDtB,mBAAA,CAAkD;YAA7Cb,KAAK,EAAC;UAA4B,GAAC,MAAI,oB;;QArDtDoC,CAAA;UAuDQf,YAAA,CAEUwC,kBAAA;QAzDlBC,UAAA,EAuD0B3D,MAAA,CAAA4D,UAAU;QAvDpC,uBAAA5B,MAAA,QAAAA,MAAA,gBAAA6B,MAAA;UAAA,OAuD0B7D,MAAA,CAAA4D,UAAU,GAAAC,MAAA;QAAA;QAAGC,WAAU,EAAE9D,MAAA,CAAA+D;;QAvDnDvD,OAAA,EAAAC,QAAA,CAwDuB;UAAA,OAA0B,E,kBAAvCX,mBAAA,CAAwGkE,SAAA,QAxDlHC,WAAA,CAwDsCjE,MAAA,CAAAkE,UAAU,EAxDhD,UAwD8BC,IAAI;iCAAxB9D,YAAA,CAAwG+D,sBAAA;cAA/D7D,GAAG,EAAE4D,IAAI,CAACE,EAAE;cAAGlE,IAAI,EAAEgE,IAAI,CAACE,EAAE;cAAGC,KAAK,EAAEH,IAAI,CAAChE;;;;QAxD9F8B,CAAA;yCA0DQvB,mBAAA,CASM,OATN6D,WASM,GARJrD,YAAA,CAOWsD,mBAAA;QAPAC,IAAI,EAAEzE,MAAA,CAAA0E,SAAS;QAAEC,MAAM,EAAN;;QA3DtCnE,OAAA,EAAAC,QAAA,CA4DY;UAAA,OAAwD,CAAxDS,YAAA,CAAwD0D,0BAAA;YAAvCN,KAAK,EAAC,EAAE;YAAC,WAAS,EAAC,KAAK;YAACO,IAAI,EAAC;cAC/C3D,YAAA,CAIkB0D,0BAAA;YAJDN,KAAK,EAAC,EAAE;YAAC,WAAS,EAAC;;YACvB9D,OAAO,EAAAC,QAAA,CAChB,UAAkDqE,KAD3B;cAAA,QA9DvCC,gBAAA,CAAAlE,gBAAA,CA+DmBb,MAAA,CAAAgF,QAAQ,CAACF,KAAK,CAACG,GAAG,CAACC,KAAK,KAAKJ,KAAK,CAACG,GAAG,CAACC,KAAK,iB;;YA/D/DjD,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA;QAAAU,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}