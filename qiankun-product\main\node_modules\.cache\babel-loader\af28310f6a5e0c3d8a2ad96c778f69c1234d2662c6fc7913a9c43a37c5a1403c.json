{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport api from '@/api';\nimport { ref, inject, computed, watch, onMounted } from 'vue';\nvar __default__ = {\n  name: 'WorkBench'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var ApplyIcon = `<svg t=\"1744701234646\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2521\" width=\"16\" height=\"16\"><path d=\"M409.290323 475.354839H175.483871c-36.645161 0-66.064516-29.419355-66.064516-66.064516V175.483871c0-36.129032 29.935484-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.645161-29.419355 66.064516-66.064516 66.064516zM409.290323 914.580645H175.483871c-36.645161 0-66.064516-29.419355-66.064516-66.064516v-233.806452c0-36.645161 29.419355-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.129032-29.419355 66.064516-66.064516 66.064516zM848.516129 914.580645h-233.806452c-36.645161 0-66.064516-29.419355-66.064516-66.064516v-233.806452c0-36.645161 29.419355-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.129032-29.935484 66.064516-66.064516 66.064516zM930.580645 246.193548l-152.774193-152.774193c-25.806452-25.806452-67.612903-25.806452-93.419355 0L531.612903 246.193548c-25.806452 25.806452-25.806452 67.612903 0 93.419355l152.774194 152.774194c25.806452 25.806452 67.612903 25.806452 93.419355 0l152.774193-152.774194c25.806452-26.322581 25.806452-68.129032 0-93.419355z m-185.806451 175.483871c-7.225806 7.225806-19.612903 7.225806-26.83871 0l-115.612903-115.612903c-7.225806-7.225806-7.225806-19.612903 0-26.83871l115.612903-115.612903c7.225806-7.225806 19.612903-7.225806 26.83871 0l115.612903 115.612903c7.225806 7.225806 7.225806 19.612903 0 26.83871l-115.612903 115.612903z\" p-id=\"2522\"></path></svg>`;\n    var menuFunction = ref([]);\n    var WorkBenchData = ref([]);\n    var WorkBenchList = inject('WorkBenchList');\n    var leftMenuData = inject('leftMenuData');\n    var setOpenPageId = inject('setOpenPageId');\n    var keyword = ref('');\n    var WorkBench = computed(function () {\n      if (!keyword.value) return WorkBenchData.value;\n      var newWorkBench = [];\n      for (var index = 0; index < WorkBenchData.value.length; index++) {\n        var item = WorkBenchData.value[index];\n        var newApply = [];\n        var applyChildrenAll = [];\n        for (var i = 0; i < item.data.length; i++) {\n          var apply = item.data[i];\n          var applyChildren = _filterWorkBenchMenu(apply.children);\n          // if (apply.name.includes(keyword.value) || applyChildren.length) {\n          //   newApply.push(apply)\n          // }\n          if (apply.name.includes(keyword.value)) newApply.push(apply);\n          if (applyChildren.length) {\n            for (var _index = 0; _index < applyChildren.length; _index++) {\n              var applyItem = applyChildren[_index];\n              applyChildrenAll.push(_objectSpread(_objectSpread({}, applyItem), {}, {\n                applyId: apply.id,\n                applyName: apply.name\n              }));\n            }\n          }\n        }\n        if (newApply.length || applyChildrenAll.length) newWorkBench.push(_objectSpread(_objectSpread({}, item), {}, {\n          data: newApply,\n          dataAll: item.dataAll,\n          menuData: applyChildrenAll\n        }));\n      }\n      return newWorkBench;\n    });\n    onMounted(function () {\n      dictionaryData();\n    });\n    var _filterWorkBenchMenu = function filterWorkBenchMenu(data) {\n      var newData = [];\n      for (var index = 0; index < data.length; index++) {\n        var _item$children;\n        var item = data[index];\n        if ((_item$children = item.children) !== null && _item$children !== void 0 && _item$children.length) {\n          var children = _filterWorkBenchMenu(item.children);\n          newData = [].concat(_toConsumableArray(newData), _toConsumableArray(children));\n        } else {\n          if (item.name.includes(keyword.value)) newData.push(item);\n        }\n      }\n      return newData;\n    };\n\n    // 添加高亮关键词的函数\n    var highlightKeyword = function highlightKeyword(text) {\n      if (!keyword.value || !text) return text;\n      var regex = new RegExp(`(${keyword.value})`, 'gi');\n      return text.replace(regex, '<span style=\"color: red; font-weight: bold;\">$1</span>');\n    };\n    var dictionaryData = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.dictionaryData({\n                dictCodes: ['menu_function']\n              });\n            case 2:\n              res = _context.sent;\n              data = res.data;\n              menuFunction.value = data.menu_function || [];\n              if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {\n                handleWorkBenchData(menuFunction.value.map(function (v) {\n                  return {\n                    id: v.key,\n                    name: v.name,\n                    data: [],\n                    dataAll: [],\n                    menuData: []\n                  };\n                }));\n              }\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function dictionaryData() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleWorkBenchData = function handleWorkBenchData(arr) {\n      if (!WorkBenchList || !WorkBenchList.value) return;\n      var other = '';\n      var arrData = arr;\n      var arrIndex = arr.map(function (v) {\n        return v.id;\n      });\n      var _loop = function _loop() {\n        var _item$menuFunction;\n        var item = WorkBenchList.value[i];\n        if ((_item$menuFunction = item.menuFunction) !== null && _item$menuFunction !== void 0 && _item$menuFunction.value) {\n          if (arrIndex.includes(item.menuFunction.value)) {\n            arrData.forEach(function (row) {\n              if (item.menuFunction.value === row.id) {\n                console.log(row);\n                row.data.push(item);\n                row.dataAll.push(item);\n              }\n            });\n          } else {\n            arrIndex.push(item.menuFunction.value);\n            arrData.push({\n              id: item.menuFunction.value,\n              name: item.menuFunction.label,\n              data: [item],\n              dataAll: [item],\n              menuData: []\n            });\n          }\n        } else {\n          if (other) {\n            other.data.push(item);\n            other.dataAll.push(item);\n          } else {\n            other = {\n              id: 'other',\n              data: [item],\n              dataAll: [item],\n              menuData: []\n            };\n          }\n        }\n      };\n      for (var i = 0, len = WorkBenchList.value.length; i < len; i++) {\n        _loop();\n      }\n      WorkBenchData.value = arrData.filter(function (v) {\n        return v.data.length;\n      });\n      if (other) {\n        WorkBenchData.value.push(_objectSpread(_objectSpread({}, other), {}, {\n          name: WorkBenchData.value.length ? '其他' : ''\n        }));\n      }\n    };\n    var handleWorkBench = function handleWorkBench(item) {\n      leftMenuData(item);\n    };\n    var handleWorkBenchMenu = function handleWorkBenchMenu(data, item) {\n      setOpenPageId(item.id);\n      if (item.applyId) {\n        var applyItem = data.find(function (v) {\n          return v.id === item.applyId;\n        });\n        if (applyItem) leftMenuData(applyItem);\n      }\n    };\n    watch(function () {\n      return WorkBenchList.value;\n    }, function () {\n      if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {\n        handleWorkBenchData(menuFunction.value.map(function (v) {\n          return {\n            id: v.key,\n            name: v.name,\n            data: []\n          };\n        }));\n      }\n    }, {\n      deep: true\n    });\n    var __returned__ = {\n      ApplyIcon,\n      menuFunction,\n      WorkBenchData,\n      WorkBenchList,\n      leftMenuData,\n      setOpenPageId,\n      keyword,\n      WorkBench,\n      filterWorkBenchMenu: _filterWorkBenchMenu,\n      highlightKeyword,\n      dictionaryData,\n      handleWorkBenchData,\n      handleWorkBench,\n      handleWorkBenchMenu,\n      get api() {\n        return api;\n      },\n      ref,\n      inject,\n      computed,\n      watch,\n      onMounted\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "api", "ref", "inject", "computed", "watch", "onMounted", "__default__", "ApplyIcon", "menuFunction", "WorkBenchData", "WorkBenchList", "leftMenuData", "setOpenPageId", "keyword", "WorkBench", "newWorkBench", "index", "item", "newApply", "applyChildrenAll", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filterWorkBenchMenu", "children", "includes", "applyItem", "applyId", "id", "applyName", "dataAll", "menuData", "dictionaryData", "newData", "_item$children", "concat", "highlightKeyword", "text", "regex", "RegExp", "replace", "_ref2", "_callee", "res", "_callee$", "_context", "dictCodes", "menu_function", "handleWorkBenchData", "map", "key", "arr", "other", "arrData", "arrIndex", "_loop", "_item$menuFunction", "row", "console", "log", "label", "len", "handleWorkBench", "handleWorkBenchMenu", "find", "deep"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/WorkBench/components/WorkBenchTwo.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"WorkBenchTwo\">\r\n    <div class=\"WorkBenchTwoSearch\">\r\n      <div class=\"WorkBenchTwoInput\">\r\n        <div class=\"WorkBenchTwoApplySearch\">\r\n          <div v-html=\"ApplyIcon\"></div>\r\n          应用搜索\r\n        </div>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入应用名称\" />\r\n        <el-icon>\r\n          <Search />\r\n        </el-icon>\r\n      </div>\r\n    </div>\r\n    <div class=\"WorkBenchTwoBox\" v-for=\"item in WorkBench\" :key=\"item.id\">\r\n      <div class=\"WorkBenchTwoColumn\" :class=\"{ WorkBenchTwoColumnOther: item.id === 'other' && !item.name }\">\r\n        {{ item.name }}\r\n      </div>\r\n      <div class=\"WorkBenchTwoList\">\r\n        <div class=\"WorkBenchTwoItem\" v-for=\"row in item.data\" :key=\"row.id\" @click=\"handleWorkBench(row)\">\r\n          <el-image :src=\"row.icon\" fit=\"cover\" />\r\n          <div class=\"WorkBenchTwoItemBox\">\r\n            <div class=\"WorkBenchTwoItemName\" v-html=\"highlightKeyword(row.name)\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"WorkBenchTwoMenu\">\r\n        <div class=\"WorkBenchTwoMenuItem\" v-for=\"row in item.menuData\" :key=\"row.id\"\r\n          @click=\"handleWorkBenchMenu(item.dataAll, row)\">\r\n          <div class=\"WorkBenchTwoMenuItemName\" v-html=\"highlightKeyword(row.name)\"></div>\r\n          <div class=\"WorkBenchTwoMenuItemApplyName\" v-html=\"row.applyName\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'WorkBench' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, inject, computed, watch, onMounted } from 'vue'\r\nconst ApplyIcon = `<svg t=\"1744701234646\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"2521\" width=\"16\" height=\"16\"><path d=\"M409.290323 475.354839H175.483871c-36.645161 0-66.064516-29.419355-66.064516-66.064516V175.483871c0-36.129032 29.935484-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.645161-29.419355 66.064516-66.064516 66.064516zM409.290323 914.580645H175.483871c-36.645161 0-66.064516-29.419355-66.064516-66.064516v-233.806452c0-36.645161 29.419355-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.129032-29.419355 66.064516-66.064516 66.064516zM848.516129 914.580645h-233.806452c-36.645161 0-66.064516-29.419355-66.064516-66.064516v-233.806452c0-36.645161 29.419355-66.064516 66.064516-66.064516h233.806452c36.645161 0 66.064516 29.419355 66.064516 66.064516v233.806452c0 36.129032-29.935484 66.064516-66.064516 66.064516zM930.580645 246.193548l-152.774193-152.774193c-25.806452-25.806452-67.612903-25.806452-93.419355 0L531.612903 246.193548c-25.806452 25.806452-25.806452 67.612903 0 93.419355l152.774194 152.774194c25.806452 25.806452 67.612903 25.806452 93.419355 0l152.774193-152.774194c25.806452-26.322581 25.806452-68.129032 0-93.419355z m-185.806451 175.483871c-7.225806 7.225806-19.612903 7.225806-26.83871 0l-115.612903-115.612903c-7.225806-7.225806-7.225806-19.612903 0-26.83871l115.612903-115.612903c7.225806-7.225806 19.612903-7.225806 26.83871 0l115.612903 115.612903c7.225806 7.225806 7.225806 19.612903 0 26.83871l-115.612903 115.612903z\" p-id=\"2522\"></path></svg>`\r\nconst menuFunction = ref([])\r\nconst WorkBenchData = ref([])\r\nconst WorkBenchList = inject('WorkBenchList')\r\nconst leftMenuData = inject('leftMenuData')\r\nconst setOpenPageId = inject('setOpenPageId')\r\nconst keyword = ref('')\r\nconst WorkBench = computed(() => {\r\n  if (!keyword.value) return WorkBenchData.value\r\n  const newWorkBench = []\r\n  for (let index = 0; index < WorkBenchData.value.length; index++) {\r\n    const item = WorkBenchData.value[index]\r\n    const newApply = []\r\n    const applyChildrenAll = []\r\n    for (let i = 0; i < item.data.length; i++) {\r\n      const apply = item.data[i]\r\n      const applyChildren = filterWorkBenchMenu(apply.children)\r\n      // if (apply.name.includes(keyword.value) || applyChildren.length) {\r\n      //   newApply.push(apply)\r\n      // }\r\n      if (apply.name.includes(keyword.value)) newApply.push(apply)\r\n      if (applyChildren.length) {\r\n        for (let index = 0; index < applyChildren.length; index++) {\r\n          const applyItem = applyChildren[index]\r\n          applyChildrenAll.push({ ...applyItem, applyId: apply.id, applyName: apply.name })\r\n        }\r\n      }\r\n    }\r\n    if (newApply.length || applyChildrenAll.length)\r\n      newWorkBench.push({ ...item, data: newApply, dataAll: item.dataAll, menuData: applyChildrenAll })\r\n  }\r\n  return newWorkBench\r\n})\r\n\r\nonMounted(() => {\r\n  dictionaryData()\r\n})\r\n\r\nconst filterWorkBenchMenu = (data) => {\r\n  let newData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.children?.length) {\r\n      const children = filterWorkBenchMenu(item.children)\r\n      newData = [...newData, ...children]\r\n    } else {\r\n      if (item.name.includes(keyword.value)) newData.push(item)\r\n    }\r\n  }\r\n  return newData\r\n}\r\n\r\n// 添加高亮关键词的函数\r\nconst highlightKeyword = (text) => {\r\n  if (!keyword.value || !text) return text\r\n  const regex = new RegExp(`(${keyword.value})`, 'gi')\r\n  return text.replace(regex, '<span style=\"color: red; font-weight: bold;\">$1</span>')\r\n}\r\n\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['menu_function'] })\r\n  var { data } = res\r\n  menuFunction.value = data.menu_function || []\r\n  if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {\r\n    handleWorkBenchData(\r\n      menuFunction.value.map((v) => ({ id: v.key, name: v.name, data: [], dataAll: [], menuData: [] }))\r\n    )\r\n  }\r\n}\r\nconst handleWorkBenchData = (arr) => {\r\n  if (!WorkBenchList || !WorkBenchList.value) return\r\n\r\n  var other = ''\r\n  var arrData = arr\r\n  var arrIndex = arr.map((v) => v.id)\r\n  for (let i = 0, len = WorkBenchList.value.length; i < len; i++) {\r\n    const item = WorkBenchList.value[i]\r\n    if (item.menuFunction?.value) {\r\n      if (arrIndex.includes(item.menuFunction.value)) {\r\n        arrData.forEach((row) => {\r\n          if (item.menuFunction.value === row.id) {\r\n            console.log(row)\r\n            row.data.push(item)\r\n            row.dataAll.push(item)\r\n          }\r\n        })\r\n      } else {\r\n        arrIndex.push(item.menuFunction.value)\r\n        arrData.push({\r\n          id: item.menuFunction.value,\r\n          name: item.menuFunction.label,\r\n          data: [item],\r\n          dataAll: [item],\r\n          menuData: []\r\n        })\r\n      }\r\n    } else {\r\n      if (other) {\r\n        other.data.push(item)\r\n        other.dataAll.push(item)\r\n      } else {\r\n        other = { id: 'other', data: [item], dataAll: [item], menuData: [] }\r\n      }\r\n    }\r\n  }\r\n  WorkBenchData.value = arrData.filter((v) => v.data.length)\r\n  if (other) {\r\n    WorkBenchData.value.push({ ...other, name: WorkBenchData.value.length ? '其他' : '' })\r\n  }\r\n}\r\nconst handleWorkBench = (item) => {\r\n  leftMenuData(item)\r\n}\r\nconst handleWorkBenchMenu = (data, item) => {\r\n  setOpenPageId(item.id)\r\n  if (item.applyId) {\r\n    const applyItem = data.find((v) => v.id === item.applyId)\r\n    if (applyItem) leftMenuData(applyItem)\r\n  }\r\n}\r\n\r\nwatch(\r\n  () => WorkBenchList.value,\r\n  () => {\r\n    if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {\r\n      handleWorkBenchData(menuFunction.value.map((v) => ({ id: v.key, name: v.name, data: [] })))\r\n    }\r\n  },\r\n  { deep: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.WorkBenchTwo {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .WorkBenchTwoSearch {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 40px 0 20px 0;\r\n\r\n    .WorkBenchTwoInput {\r\n      width: 600px;\r\n      display: flex;\r\n      align-items: center;\r\n      background: #fff;\r\n      padding: 6px 20px;\r\n\r\n      .WorkBenchTwoApplySearch {\r\n        display: flex;\r\n        align-items: center;\r\n        font-weight: bold;\r\n        font-size: var(--zy-navigation-font-size);\r\n        line-height: var(--zy-line-height);\r\n        border-radius: var(--el-border-radius-base);\r\n\r\n        &>div {\r\n          width: calc(var(--zy-navigation-font-size) + 2px);\r\n          height: calc(var(--zy-navigation-font-size) + 2px);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 2px;\r\n\r\n          svg {\r\n            width: 100%;\r\n            height: 100%;\r\n\r\n            path {\r\n              fill: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-input {\r\n        flex: 1;\r\n\r\n        .zy-el-input__wrapper {\r\n          box-shadow: none;\r\n        }\r\n      }\r\n\r\n      &>.zy-el-icon {\r\n        color: var(--zy-el-color-primary);\r\n        font-size: var(--zy-navigation-font-size);\r\n      }\r\n    }\r\n  }\r\n\r\n  .WorkBenchTwoBox {\r\n    width: 1140px;\r\n    margin: auto;\r\n    padding-left: 40px;\r\n\r\n    &:last-child {\r\n      padding-bottom: 40px;\r\n    }\r\n\r\n    .WorkBenchTwoColumn {\r\n      padding: 30px 0;\r\n      padding-left: 24px;\r\n      font-size: calc(var(--zy-navigation-font-size) + 2px);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      color: #333333;\r\n      position: relative;\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        width: 0;\r\n        height: 0;\r\n        border-top: 6px solid transparent;\r\n        border-right: 6px solid transparent;\r\n        border-bottom: 6px solid transparent;\r\n        border-left: 6px solid var(--zy-el-color-primary);\r\n        transform: translateY(-50%);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 10px;\r\n        width: 0;\r\n        height: 0;\r\n        border-top: 6px solid transparent;\r\n        border-right: 6px solid transparent;\r\n        border-bottom: 6px solid transparent;\r\n        border-left: 6px solid var(--zy-el-color-primary);\r\n        transform: translateY(-50%);\r\n      }\r\n    }\r\n\r\n    .WorkBenchTwoColumnOther {\r\n      &::after {\r\n        border-left: 6px solid transparent;\r\n      }\r\n\r\n      &::before {\r\n        border-left: 6px solid transparent;\r\n      }\r\n    }\r\n\r\n    .WorkBenchTwoList {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n\r\n      .WorkBenchTwoItem {\r\n        width: 248px;\r\n        background: #ffffff;\r\n        box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.1);\r\n        border-radius: 4px;\r\n        margin: 0 26px 26px 0;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 20px 22px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.2);\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n\r\n        .WorkBenchTwoItemBox {\r\n          width: calc(100% - 48px);\r\n          height: calc((var(--zy-name-font-size) * var(--zy-line-height)) * 2);\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          align-items: center;\r\n        }\r\n\r\n        .WorkBenchTwoItemName {\r\n          width: 100%;\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-name-font-size);\r\n          padding-left: 20px;\r\n          font-weight: bold;\r\n          overflow: hidden;\r\n        }\r\n      }\r\n    }\r\n\r\n    .WorkBenchTwoMenu {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n\r\n      .WorkBenchTwoMenuItem {\r\n        width: 522px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        background: #ffffff;\r\n        box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.1);\r\n        border-radius: var(--el-border-radius-base);\r\n        padding: var(--zy-distance-five);\r\n        padding-left: 30px;\r\n        margin-right: 26px;\r\n        margin-bottom: 16px;\r\n        cursor: pointer;\r\n        position: relative;\r\n\r\n        &:hover {\r\n          box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.2);\r\n        }\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 12px;\r\n          width: 6px;\r\n          height: 6px;\r\n          border-radius: 50%;\r\n          background: var(--zy-el-color-primary);\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          width: 80%;\r\n          height: 0;\r\n          border-top: 1px dashed var(--zy-el-text-color-secondary);\r\n          transform: translate(-50%, -50%);\r\n        }\r\n\r\n        .WorkBenchTwoMenuItemName {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          color: var(--zy-el-text-color-regular);\r\n          background: #fff;\r\n          font-weight: bold;\r\n          padding-right: 6px;\r\n          position: relative;\r\n          z-index: 2;\r\n        }\r\n\r\n        .WorkBenchTwoMenuItemApplyName {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          color: var(--zy-el-text-color-secondary);\r\n          background: #fff;\r\n          padding-left: 6px;\r\n          position: relative;\r\n          z-index: 2;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAyCA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AAAA,SAAAE,mBAAA3G,CAAA,WAAA4G,kBAAA,CAAA5G,CAAA,KAAA6G,gBAAA,CAAA7G,CAAA,KAAA8G,2BAAA,CAAA9G,CAAA,KAAA+G,kBAAA;AAAA,SAAAA,mBAAA,cAAAlD,SAAA;AAAA,SAAAiD,4BAAA9G,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAAgH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAAkH,QAAA,CAAArF,IAAA,CAAA5B,CAAA,EAAA4F,KAAA,6BAAA7F,CAAA,IAAAC,CAAA,CAAA+E,WAAA,KAAAhF,CAAA,GAAAC,CAAA,CAAA+E,WAAA,CAAAC,IAAA,aAAAjF,CAAA,cAAAA,CAAA,GAAAmH,KAAA,CAAAC,IAAA,CAAAnH,CAAA,oBAAAD,CAAA,+CAAAqH,IAAA,CAAArH,CAAA,IAAAiH,iBAAA,CAAAhH,CAAA,EAAAU,CAAA;AAAA,SAAAmG,iBAAA7G,CAAA,8BAAAS,MAAA,YAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,aAAAX,CAAA,uBAAAkH,KAAA,CAAAC,IAAA,CAAAnH,CAAA;AAAA,SAAA4G,mBAAA5G,CAAA,QAAAkH,KAAA,CAAAG,OAAA,CAAArH,CAAA,UAAAgH,iBAAA,CAAAhH,CAAA;AAAA,SAAAgH,kBAAAhH,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA4E,MAAA,MAAAlE,CAAA,GAAAV,CAAA,CAAA4E,MAAA,YAAA9E,CAAA,MAAAK,CAAA,GAAA+G,KAAA,CAAAxG,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AAAA,SAAAmH,QAAAxH,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAsH,qBAAA,QAAAlH,CAAA,GAAAJ,MAAA,CAAAsH,qBAAA,CAAAzH,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAmH,MAAA,WAAAxH,CAAA,WAAAC,MAAA,CAAAwH,wBAAA,CAAA3H,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAiC,KAAA,CAAAzG,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2H,cAAA5H,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAAuG,SAAA,CAAA3B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAAwG,SAAA,CAAAvG,CAAA,IAAAuG,SAAA,CAAAvG,CAAA,QAAAA,CAAA,OAAAsH,OAAA,CAAArH,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA2H,eAAA,CAAA7H,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA2H,yBAAA,GAAA3H,MAAA,CAAA4H,gBAAA,CAAA/H,CAAA,EAAAG,MAAA,CAAA2H,yBAAA,CAAA7H,CAAA,KAAAuH,OAAA,CAAArH,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAwH,wBAAA,CAAA1H,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA6H,gBAAA7H,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA8H,cAAA,CAAA9H,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAgI,eAAA/H,CAAA,QAAAS,CAAA,GAAAuH,YAAA,CAAAhI,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAuH,aAAAhI,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAuH,WAAA,kBAAAlI,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAiI,MAAA,GAAAC,MAAA,EAAAnI,CAAA;AADA,OAAOoI,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,QAAQ,KAAK;AAJ7D,IAAAC,WAAA,GAAe;EAAEzD,IAAI,EAAE;AAAY,CAAC;;;;;IAKpC,IAAM0D,SAAS,GAAG,4mDAA4mD;IAC9nD,IAAMC,YAAY,GAAGP,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMQ,aAAa,GAAGR,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMS,aAAa,GAAGR,MAAM,CAAC,eAAe,CAAC;IAC7C,IAAMS,YAAY,GAAGT,MAAM,CAAC,cAAc,CAAC;IAC3C,IAAMU,aAAa,GAAGV,MAAM,CAAC,eAAe,CAAC;IAC7C,IAAMW,OAAO,GAAGZ,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMa,SAAS,GAAGX,QAAQ,CAAC,YAAM;MAC/B,IAAI,CAACU,OAAO,CAACzI,KAAK,EAAE,OAAOqI,aAAa,CAACrI,KAAK;MAC9C,IAAM2I,YAAY,GAAG,EAAE;MACvB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,aAAa,CAACrI,KAAK,CAACqE,MAAM,EAAEuE,KAAK,EAAE,EAAE;QAC/D,IAAMC,IAAI,GAAGR,aAAa,CAACrI,KAAK,CAAC4I,KAAK,CAAC;QACvC,IAAME,QAAQ,GAAG,EAAE;QACnB,IAAMC,gBAAgB,GAAG,EAAE;QAC3B,KAAK,IAAI9I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4I,IAAI,CAACG,IAAI,CAAC3E,MAAM,EAAEpE,CAAC,EAAE,EAAE;UACzC,IAAMgG,KAAK,GAAG4C,IAAI,CAACG,IAAI,CAAC/I,CAAC,CAAC;UAC1B,IAAMgJ,aAAa,GAAGC,oBAAmB,CAACjD,KAAK,CAACkD,QAAQ,CAAC;UACzD;UACA;UACA;UACA,IAAIlD,KAAK,CAACxB,IAAI,CAAC2E,QAAQ,CAACX,OAAO,CAACzI,KAAK,CAAC,EAAE8I,QAAQ,CAAC9E,IAAI,CAACiC,KAAK,CAAC;UAC5D,IAAIgD,aAAa,CAAC5E,MAAM,EAAE;YACxB,KAAK,IAAIuE,MAAK,GAAG,CAAC,EAAEA,MAAK,GAAGK,aAAa,CAAC5E,MAAM,EAAEuE,MAAK,EAAE,EAAE;cACzD,IAAMS,SAAS,GAAGJ,aAAa,CAACL,MAAK,CAAC;cACtCG,gBAAgB,CAAC/E,IAAI,CAAAmD,aAAA,CAAAA,aAAA,KAAMkC,SAAS;gBAAEC,OAAO,EAAErD,KAAK,CAACsD,EAAE;gBAAEC,SAAS,EAAEvD,KAAK,CAACxB;cAAI,EAAE,CAAC;YACnF;UACF;QACF;QACA,IAAIqE,QAAQ,CAACzE,MAAM,IAAI0E,gBAAgB,CAAC1E,MAAM,EAC5CsE,YAAY,CAAC3E,IAAI,CAAAmD,aAAA,CAAAA,aAAA,KAAM0B,IAAI;UAAEG,IAAI,EAAEF,QAAQ;UAAEW,OAAO,EAAEZ,IAAI,CAACY,OAAO;UAAEC,QAAQ,EAAEX;QAAgB,EAAE,CAAC;MACrG;MACA,OAAOJ,YAAY;IACrB,CAAC,CAAC;IAEFV,SAAS,CAAC,YAAM;MACd0B,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,IAAMT,oBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIF,IAAI,EAAK;MACpC,IAAIY,OAAO,GAAG,EAAE;MAChB,KAAK,IAAIhB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGI,IAAI,CAAC3E,MAAM,EAAEuE,KAAK,EAAE,EAAE;QAAA,IAAAiB,cAAA;QAChD,IAAMhB,IAAI,GAAGG,IAAI,CAACJ,KAAK,CAAC;QACxB,KAAAiB,cAAA,GAAIhB,IAAI,CAACM,QAAQ,cAAAU,cAAA,eAAbA,cAAA,CAAexF,MAAM,EAAE;UACzB,IAAM8E,QAAQ,GAAGD,oBAAmB,CAACL,IAAI,CAACM,QAAQ,CAAC;UACnDS,OAAO,MAAAE,MAAA,CAAA1D,kBAAA,CAAOwD,OAAO,GAAAxD,kBAAA,CAAK+C,QAAQ,EAAC;QACrC,CAAC,MAAM;UACL,IAAIN,IAAI,CAACpE,IAAI,CAAC2E,QAAQ,CAACX,OAAO,CAACzI,KAAK,CAAC,EAAE4J,OAAO,CAAC5F,IAAI,CAAC6E,IAAI,CAAC;QAC3D;MACF;MACA,OAAOe,OAAO;IAChB,CAAC;;IAED;IACA,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;MACjC,IAAI,CAACvB,OAAO,CAACzI,KAAK,IAAI,CAACgK,IAAI,EAAE,OAAOA,IAAI;MACxC,IAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIzB,OAAO,CAACzI,KAAK,GAAG,EAAE,IAAI,CAAC;MACpD,OAAOgK,IAAI,CAACG,OAAO,CAACF,KAAK,EAAE,wDAAwD,CAAC;IACtF,CAAC;IAED,IAAMN,cAAc;MAAA,IAAAS,KAAA,GAAArE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2F,QAAA;QAAA,IAAAC,GAAA,EAAAtB,IAAA;QAAA,OAAA1J,mBAAA,GAAAuB,IAAA,UAAA0J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAArF,IAAA,GAAAqF,QAAA,CAAAhH,IAAA;YAAA;cAAAgH,QAAA,CAAAhH,IAAA;cAAA,OACHoE,GAAG,CAAC+B,cAAc,CAAC;gBAAEc,SAAS,EAAE,CAAC,eAAe;cAAE,CAAC,CAAC;YAAA;cAAhEH,GAAG,GAAAE,QAAA,CAAAvH,IAAA;cACH+F,IAAI,GAAKsB,GAAG,CAAZtB,IAAI;cACVZ,YAAY,CAACpI,KAAK,GAAGgJ,IAAI,CAAC0B,aAAa,IAAI,EAAE;cAC7C,IAAIpC,aAAa,IAAIA,aAAa,CAACtI,KAAK,IAAIsI,aAAa,CAACtI,KAAK,CAACqE,MAAM,EAAE;gBACtEsG,mBAAmB,CACjBvC,YAAY,CAACpI,KAAK,CAAC4K,GAAG,CAAC,UAAC5I,CAAC;kBAAA,OAAM;oBAAEuH,EAAE,EAAEvH,CAAC,CAAC6I,GAAG;oBAAEpG,IAAI,EAAEzC,CAAC,CAACyC,IAAI;oBAAEuE,IAAI,EAAE,EAAE;oBAAES,OAAO,EAAE,EAAE;oBAAEC,QAAQ,EAAE;kBAAG,CAAC;gBAAA,CAAC,CAClG,CAAC;cACH;YAAC;YAAA;cAAA,OAAAc,QAAA,CAAAlF,IAAA;UAAA;QAAA,GAAA+E,OAAA;MAAA,CACF;MAAA,gBATKV,cAAcA,CAAA;QAAA,OAAAS,KAAA,CAAAnE,KAAA,OAAAD,SAAA;MAAA;IAAA,GASnB;IACD,IAAM2E,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIG,GAAG,EAAK;MACnC,IAAI,CAACxC,aAAa,IAAI,CAACA,aAAa,CAACtI,KAAK,EAAE;MAE5C,IAAI+K,KAAK,GAAG,EAAE;MACd,IAAIC,OAAO,GAAGF,GAAG;MACjB,IAAIG,QAAQ,GAAGH,GAAG,CAACF,GAAG,CAAC,UAAC5I,CAAC;QAAA,OAAKA,CAAC,CAACuH,EAAE;MAAA,EAAC;MAAA,IAAA2B,KAAA,YAAAA,MAAA,EAC6B;QAAA,IAAAC,kBAAA;QAC9D,IAAMtC,IAAI,GAAGP,aAAa,CAACtI,KAAK,CAACC,CAAC,CAAC;QACnC,KAAAkL,kBAAA,GAAItC,IAAI,CAACT,YAAY,cAAA+C,kBAAA,eAAjBA,kBAAA,CAAmBnL,KAAK,EAAE;UAC5B,IAAIiL,QAAQ,CAAC7B,QAAQ,CAACP,IAAI,CAACT,YAAY,CAACpI,KAAK,CAAC,EAAE;YAC9CgL,OAAO,CAAC5I,OAAO,CAAC,UAACgJ,GAAG,EAAK;cACvB,IAAIvC,IAAI,CAACT,YAAY,CAACpI,KAAK,KAAKoL,GAAG,CAAC7B,EAAE,EAAE;gBACtC8B,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;gBAChBA,GAAG,CAACpC,IAAI,CAAChF,IAAI,CAAC6E,IAAI,CAAC;gBACnBuC,GAAG,CAAC3B,OAAO,CAACzF,IAAI,CAAC6E,IAAI,CAAC;cACxB;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLoC,QAAQ,CAACjH,IAAI,CAAC6E,IAAI,CAACT,YAAY,CAACpI,KAAK,CAAC;YACtCgL,OAAO,CAAChH,IAAI,CAAC;cACXuF,EAAE,EAAEV,IAAI,CAACT,YAAY,CAACpI,KAAK;cAC3ByE,IAAI,EAAEoE,IAAI,CAACT,YAAY,CAACmD,KAAK;cAC7BvC,IAAI,EAAE,CAACH,IAAI,CAAC;cACZY,OAAO,EAAE,CAACZ,IAAI,CAAC;cACfa,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL,IAAIqB,KAAK,EAAE;YACTA,KAAK,CAAC/B,IAAI,CAAChF,IAAI,CAAC6E,IAAI,CAAC;YACrBkC,KAAK,CAACtB,OAAO,CAACzF,IAAI,CAAC6E,IAAI,CAAC;UAC1B,CAAC,MAAM;YACLkC,KAAK,GAAG;cAAExB,EAAE,EAAE,OAAO;cAAEP,IAAI,EAAE,CAACH,IAAI,CAAC;cAAEY,OAAO,EAAE,CAACZ,IAAI,CAAC;cAAEa,QAAQ,EAAE;YAAG,CAAC;UACtE;QACF;MACF,CAAC;MA7BD,KAAK,IAAIzJ,CAAC,GAAG,CAAC,EAAEuL,GAAG,GAAGlD,aAAa,CAACtI,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAGuL,GAAG,EAAEvL,CAAC,EAAE;QAAAiL,KAAA;MAAA;MA8B9D7C,aAAa,CAACrI,KAAK,GAAGgL,OAAO,CAAC/D,MAAM,CAAC,UAACjF,CAAC;QAAA,OAAKA,CAAC,CAACgH,IAAI,CAAC3E,MAAM;MAAA,EAAC;MAC1D,IAAI0G,KAAK,EAAE;QACT1C,aAAa,CAACrI,KAAK,CAACgE,IAAI,CAAAmD,aAAA,CAAAA,aAAA,KAAM4D,KAAK;UAAEtG,IAAI,EAAE4D,aAAa,CAACrI,KAAK,CAACqE,MAAM,GAAG,IAAI,GAAG;QAAE,EAAE,CAAC;MACtF;IACF,CAAC;IACD,IAAMoH,eAAe,GAAG,SAAlBA,eAAeA,CAAI5C,IAAI,EAAK;MAChCN,YAAY,CAACM,IAAI,CAAC;IACpB,CAAC;IACD,IAAM6C,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAI1C,IAAI,EAAEH,IAAI,EAAK;MAC1CL,aAAa,CAACK,IAAI,CAACU,EAAE,CAAC;MACtB,IAAIV,IAAI,CAACS,OAAO,EAAE;QAChB,IAAMD,SAAS,GAAGL,IAAI,CAAC2C,IAAI,CAAC,UAAC3J,CAAC;UAAA,OAAKA,CAAC,CAACuH,EAAE,KAAKV,IAAI,CAACS,OAAO;QAAA,EAAC;QACzD,IAAID,SAAS,EAAEd,YAAY,CAACc,SAAS,CAAC;MACxC;IACF,CAAC;IAEDrB,KAAK,CACH;MAAA,OAAMM,aAAa,CAACtI,KAAK;IAAA,GACzB,YAAM;MACJ,IAAIsI,aAAa,IAAIA,aAAa,CAACtI,KAAK,IAAIsI,aAAa,CAACtI,KAAK,CAACqE,MAAM,EAAE;QACtEsG,mBAAmB,CAACvC,YAAY,CAACpI,KAAK,CAAC4K,GAAG,CAAC,UAAC5I,CAAC;UAAA,OAAM;YAAEuH,EAAE,EAAEvH,CAAC,CAAC6I,GAAG;YAAEpG,IAAI,EAAEzC,CAAC,CAACyC,IAAI;YAAEuE,IAAI,EAAE;UAAG,CAAC;QAAA,CAAC,CAAC,CAAC;MAC7F;IACF,CAAC,EACD;MAAE4C,IAAI,EAAE;IAAK,CACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}