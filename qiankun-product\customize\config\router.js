const HomeLayout = () => import('customize/HomeLayout/HomeLayout.vue') // 新默认打开页面
const LoginView = () => import('customize/LoginView/LoginViewCopy.vue')
// const LoginView = () => import('customize/LoginView/LoginView.vue')
const LayoutContainer = () => import('customize/LayoutContainer/LayoutContainer.vue')
const UnifyLogin = () => import('customize/UnifyLogin/UnifyLogin.vue')
const LoginViewOne = () => import('customize/LoginViewOne/LoginViewOne.vue')
const LoginViewRegion = () => import('customize/LoginViewRegion/LoginViewRegion.vue')
const PostMam = () => import('customize/PostMam/PostMam.vue')
const WorkBench = () => import('customize/WorkBench/WorkBench.vue')
const WorkBenchCopy = () => import('customize/WorkBench/WorkBenchCopy.vue')
const OtherRouter = () => import('customize/OtherRouter/OtherRouter.vue')
const NotFoundPage = () => import('customize/NotFoundPage/NotFoundPage.vue')
const NoneAccessAuthority = () => import('customize/NoneAccessAuthority/NoneAccessAuthority.vue')
const GlobalHome = () => import('customize/GlobalHome/GlobalHome.vue')
const HotSpot = () => import('customize/HotSpot/HotSpot.vue')
const BackgroundCheck = () => import('customize/BackgroundCheck/BackgroundCheck.vue')
const BackgroundCheckInfo = () => import('customize/BackgroundCheck/BackgroundCheckInfo.vue')
const BackgroundCheckDetails = () => import('customize/BackgroundCheck/BackgroundCheckDetails.vue')
const PublicSentimentInfo = () => import('customize/PublicSentimentInfo/PublicSentimentInfo.vue')
const AnswerManage = () => import('customize/QuestionsAndAnswers/AnswerManage.vue')
const QuestionsAndAnswers = () => import('customize/QuestionsAndAnswers/QuestionsAndAnswers.vue')
// const homePage = () => import('customize/homePage/homePage.vue')
const homePage = () => import('customize/homePage/homePageNew.vue')

// const GlobalChat = () => import('customize/GlobalChat/GlobalChat.vue')
const GlobalLayoutChat = () => import('customize/GlobalChat/GlobalLayoutChat.vue')
const DevelopContent = () => import('customize/Intelligentize/DevelopContent/DevelopContent.vue')
const VersionComparison = () => import('customize/Intelligentize/VersionComparison/VersionComparison.vue')
const DataRecommendation = () => import('customize/Intelligentize/DataRecommendation/DataRecommendation.vue')
const DataRecommendationOpen = () => import('customize/Intelligentize/DataRecommendation/DataRecommendationOpen.vue')
const AssistedWriting = () => import('customize/AssistedWriting/AssistedWriting.vue')
const AssistedWritingRD = () => import('customize/AssistedWriting/AssistedWritingRD.vue')
const AiReportGenera = () => import('customize/AiReportGenera/AiReportGenera.vue')
const AiReportGeneraList = () => import('customize/AiReportGenera/AiReportGeneraList.vue')
const AiReportGeneraView = () => import('customize/AiReportGenera/AiReportGeneraView.vue')
const HotspotPush = () => import('customize/HotspotPush/HotspotPush.vue')
const WarningDetail = () => import('customize/HotspotPush/WarningDetail.vue')
const VersionComparisonAi = () => import('customize/Intelligentize/VersionComparison/VersionComparisonAi.vue')
const AiToolBox = () => import('customize/AiToolBox/AiToolBox.vue')
const AiUseStatistics = () => import('customize/AiUseStatistics/AiUseStatistics.vue')
const AiHotWordList = () => import('customize/AiUseStatistics/AiHotWord/AiHotWordList.vue')
// 议题符合性审查
const complianceReviewTopics = () => import('customize/complianceReviewTopics/complianceReviewTopics.vue')
const page = [
  { path: '/PostMam', name: 'PostMam', component: PostMam, meta: { moduleName: 'main' } },
  { path: '/WorkBench', name: 'WorkBench', component: WorkBench, meta: { moduleName: 'main' } },
  { path: '/OtherRouter', name: 'OtherRouter', component: OtherRouter, meta: { moduleName: 'main' } },
  { path: '/NotFoundPage', name: 'NotFoundPage', component: NotFoundPage, meta: { moduleName: 'main' } },
  {
    path: '/NoneAccessAuthority',
    name: 'NoneAccessAuthority',
    component: NoneAccessAuthority,
    meta: { moduleName: 'main' }
  },
  { path: '/GlobalHome', name: 'GlobalHome', component: GlobalHome, meta: { moduleName: 'main' } },
  // { path: '/GlobalChat', name: 'GlobalChat', component: GlobalChat, meta: { moduleName: 'main' } },
  { path: '/HotSpot', name: 'HotSpot', component: HotSpot, meta: { moduleName: 'main' } },
  { path: '/BackgroundCheck', name: 'BackgroundCheck', component: BackgroundCheck, meta: { moduleName: 'main' } },
  {
    path: '/BackgroundCheckInfo',
    name: 'BackgroundCheckInfo',
    component: BackgroundCheckInfo,
    meta: { moduleName: 'main' }
  },
  {
    path: '/BackgroundCheckDetails',
    name: 'BackgroundCheckDetails',
    component: BackgroundCheckDetails,
    meta: { moduleName: 'main' }
  },
  {
    path: '/PublicSentimentInfo',
    name: 'PublicSentimentInfo',
    component: PublicSentimentInfo,
    meta: { moduleName: 'main' }
  },
  { path: '/AnswerManage', name: 'AnswerManage', component: AnswerManage, meta: { moduleName: 'main' } },
  {
    path: '/QuestionsAndAnswers',
    name: 'QuestionsAndAnswers',
    component: QuestionsAndAnswers,
    meta: { moduleName: 'main' }
  },
  { path: '/homePage', name: 'homePage', component: homePage, meta: { moduleName: 'main' } },
  { path: '/WorkBenchCopy', name: 'WorkBenchCopy', component: WorkBenchCopy, meta: { moduleName: 'main' } },
  { path: '/DevelopContent', name: 'DevelopContent', component: DevelopContent, meta: { moduleName: 'main' } },
  { path: '/VersionComparison', name: 'VersionComparison', component: VersionComparison, meta: { moduleName: 'main' } },
  {
    path: '/DataRecommendation',
    name: 'DataRecommendation',
    component: DataRecommendation,
    meta: { moduleName: 'main' }
  },
  {
    path: '/complianceReviewTopics',
    name: 'complianceReviewTopics',
    component: complianceReviewTopics,
    meta: { moduleName: 'main' }
  },
  { path: '/AssistedWriting', name: 'AssistedWriting', component: AssistedWriting, meta: { moduleName: 'main' } },
  { path: '/AssistedWritingRD', name: 'AssistedWritingRD', component: AssistedWritingRD, meta: { moduleName: 'main' } },
  { path: '/AiReportGenera', name: 'AiReportGenera', component: AiReportGenera, meta: { moduleName: 'main' } },
  {
    path: '/AiReportGeneraList',
    name: 'AiReportGeneraList',
    component: AiReportGeneraList,
    meta: { moduleName: 'main' }
  },
  {
    path: '/AiReportGeneraView',
    name: 'AiReportGeneraView',
    component: AiReportGeneraView,
    meta: { moduleName: 'main' }
  },
  { path: '/HotspotPush', name: 'HotspotPush', component: HotspotPush, meta: { moduleName: 'main' } },
  { path: '/WarningDetail', name: 'WarningDetail', component: WarningDetail, meta: { moduleName: 'main' } },
  {
    path: '/AiToolBox',
    name: 'AiToolBox',
    component: AiToolBox,
    meta: { moduleName: 'main' }
  },
  {
    path: '/AiUseStatistics',
    name: 'AiUseStatistics',
    component: AiUseStatistics,
    meta: { moduleName: 'main' }
  },
  {
    path: '/AiHotWordList',
    name: 'AiHotWordList',
    component: AiHotWordList,
    meta: { moduleName: 'main' }
  }
]
const customizeRouter = [
  { path: '/HomeLayout', name: 'HomeLayout', component: HomeLayout, meta: { moduleName: 'verify' } },
  { path: '/LoginView', name: 'LoginView', component: LoginView },
  { path: '/:pathMatch(.*)*', name: 'LayoutContainer', component: LayoutContainer, children: page },
  { path: '/GlobalLayoutChat', name: 'GlobalLayoutChat', component: GlobalLayoutChat, meta: { moduleName: 'main' } },
  { path: '/UnifyLogin', name: 'UnifyLogin', component: UnifyLogin, meta: { moduleName: 'verify' } },
  { path: '/LoginViewOne', name: 'LoginViewOne', component: LoginViewOne, meta: { moduleName: 'verify' } },
  { path: '/LoginViewRegion', name: 'LoginViewRegion', component: LoginViewRegion, meta: { moduleName: 'verify' } },
  { path: '/DataRecommendationOpen', name: 'DataRecommendationOpen', component: DataRecommendationOpen },
  {
    path: '/DevelopContentPublic',
    name: 'DevelopContentPublic',
    component: DevelopContent,
    meta: { moduleName: 'main' }
  },
  {
    path: '/VersionComparisonPublic',
    name: 'VersionComparisonPublic',
    component: VersionComparison,
    meta: { moduleName: 'main' }
  },
  {
    path: '/DataRecommendationPublic',
    name: 'DataRecommendationPublic',
    component: DataRecommendation,
    meta: { moduleName: 'main' }
  },
  {
    path: '/VersionComparisonAi',
    name: 'VersionComparisonAi',
    component: VersionComparisonAi,
    meta: { moduleName: 'main' }
  }
]
export default customizeRouter
