{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ChartFive\",\n  ref: \"elChartRef\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, null, 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\PublicSentimentInfo\\components\\ChartFive.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ChartFive\" ref=\"elChartRef\">\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChartFive' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport * as echarts from 'echarts'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst erd = elementResizeDetectorMaker()\r\nconst ticketType = ref('')\r\nlet elChart = null\r\nconst endValue = ref(6)\r\nconst elChartRef = ref()\r\nconst tableData = ref([])\r\nconst stateData = computed(() => store.getters.getPublicSentimentInfoFn)\r\nconst initChart = () => {\r\n  if (!elChart) { elChart = echarts.init(elChartRef.value) }\r\n  const setOption = {\r\n    // color: [props.color],\r\n    tooltip: {\r\n      trigger: 'axis',\r\n      formatter (params) {\r\n        const item = tableData.value[params[0].dataIndex]\r\n        return `<div class=\"ColumnChartTooltip\">\r\n          <div class=\"ColumnChartName\">${params[0].marker}${item.name}</div>\r\n          <div class=\"ColumnChartText\"><span>数量：${item.value}</span></div>\r\n        </div>`\r\n      }\r\n    },\r\n    xAxis: {\r\n      axisLabel: { interval: 0, fontSize: 12, formatter: (value) => value.length > 6 ? `${value.slice(0, 6)}...` : value },\r\n      data: tableData.value.map(v => v.name)\r\n    },\r\n    yAxis: { splitLine: { show: true } },\r\n    grid: {\r\n      top: '18%',\r\n      left: '2%',\r\n      right: '2%',\r\n      bottom: '8%',\r\n      containLabel: true\r\n    },\r\n    dataZoom: [\r\n      {\r\n        id: 'dataZoomY',\r\n        xAxisIndex: [0],\r\n        show: endValue.value + 1 < tableData.value.length, // 是否显示滑动条，不影响使用\r\n        type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件\r\n        endValue: endValue.value,\r\n        height: 12,\r\n        bottom: '2%',\r\n        zoomLock: true,\r\n        showDataShadow: false, // 是否显示数据阴影 默认auto\r\n        backgroundColor: '#fff',\r\n        showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true\r\n        realtime: true, // 是否实时更新\r\n        filterMode: 'filter',\r\n        handleIcon: 'circle',\r\n        moveHandleSize: 0,\r\n        brushSelect: false\r\n      },\r\n      {\r\n        type: 'inside',\r\n        xAxisIndex: 0,\r\n        zoomOnMouseWheel: false,  // 滚轮是否触发缩放\r\n        moveOnMouseMove: true,  // 鼠标滚轮触发滚动\r\n        moveOnMouseWheel: true\r\n      }\r\n    ],\r\n    series: {\r\n      type: \"bar\",\r\n      barWidth: 20,\r\n      label: { show: true, fontSize: 12, position: 'top' },\r\n      data: tableData.value.map(v => v.value)\r\n    }\r\n  }\r\n  elChart.setOption(setOption)\r\n}\r\nconst publicChartAnalysis = async () => {\r\n  if (stateData.value[`ChartFive-${ticketType.value}`]) {\r\n    tableData.value = stateData.value[`ChartFive-${ticketType.value}`]\r\n  } else {\r\n    const { data } = await api.publicChartAnalysis({ ticketType: ticketType.value, chartType: '5' })\r\n    tableData.value = data?.charts?.map(v => ({ key: v.key, name: v.name, value: v.num, percent: v.percent })) || []\r\n    store.commit('setPublicSentimentInfo', { key: `ChartFive-${ticketType.value}`, params: tableData.value })\r\n  }\r\n  initChart()\r\n  nextTick(() => {\r\n    erd.listenTo(elChartRef.value, (element) => {\r\n      endValue.value = Math.trunc((element.offsetWidth / 99) - 2)\r\n      elChart.setOption({ dataZoom: [{ show: endValue.value + 1 < tableData.value.length, endValue: endValue.value }] })\r\n      elChart.resize()\r\n    })\r\n  })\r\n}\r\nonMounted(() => {\r\n  ticketType.value = route.query.ticketType || '2'\r\n  publicChartAnalysis()\r\n})\r\nonUnmounted(() => { erd.uninstall(elChartRef.value) }) \r\n</script>\r\n<style lang=\"scss\">\r\n.ChartFive {\r\n  width: 100%;\r\n  height: 320px;\r\n  margin: auto;\r\n\r\n  .ColumnChartTooltip {\r\n    min-width: 180px;\r\n\r\n    .ColumnChartName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-primary);\r\n      padding-left: 16px;\r\n      position: relative;\r\n\r\n      span {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #FFFFFF;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ColumnChartText {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: var(--zy-font-name-distance-five);\r\n\r\n      span+span {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,WAAW;EAACC,GAAG,EAAC;;;uBAA3BC,mBAAA,CACM,OADNC,UACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}