"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[3201],{63201:function(e,t,n){n.r(t),n.d(t,{default:function(){return q}});var r=n(44863),o=(n(76945),n(4711),n(81086)),a=(n(2222),n(71928)),i=(n(31584),n(15934),n(81474)),l=(n(64352),n(44917)),c=(n(40065),n(98267)),u=(n(15475),n(62427)),s=(n(98773),n(74061)),d=n(4955),v=n(3671),f=n(88609),p=n(43955),h=n(24652),m=n(98885),y=n(42714);n(35894),n(50389);function g(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */g=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var a=t&&t.prototype instanceof y?t:y,i=Object.create(a.prototype),l=new G(r||[]);return o(i,"_invoke",{value:D(e,n,l)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var v="suspendedStart",f="suspendedYield",p="executing",h="completed",m={};function y(){}function k(){}function w(){}var V={};u(V,i,(function(){return this}));var b=Object.getPrototypeOf,N=b&&b(b(I([])));N&&N!==n&&r.call(N,i)&&(V=N);var E=w.prototype=y.prototype=Object.create(V);function x(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function B(e,t){function n(o,a,i,l){var c=d(e[o],e,a);if("throw"!==c.type){var u=c.arg,s=u.value;return s&&"object"==typeof s&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,i,l)}),(function(e){n("throw",e,i,l)})):t.resolve(s).then((function(e){u.value=e,i(u)}),(function(e){return n("throw",e,i,l)}))}l(c.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function D(t,n,r){var o=v;return function(a,i){if(o===p)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var l=r.delegate;if(l){var c=C(l,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===v)throw o=h,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=p;var u=d(t,n,r);if("normal"===u.type){if(o=r.done?h:f,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=h,r.method="throw",r.arg=u.arg)}}}function C(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,C(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var a=d(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,m;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,m):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function G(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function I(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return k.prototype=w,o(E,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:k,configurable:!0}),k.displayName=u(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,c,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},x(B.prototype),u(B.prototype,l,(function(){return this})),t.AsyncIterator=B,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new B(s(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},x(E),u(E,c,"Generator"),u(E,i,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=I,G.prototype={constructor:G,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:I(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),m}},t}function k(e,t,n,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function w(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){k(a,r,o,i,l,"next",e)}function l(e){k(a,r,o,i,l,"throw",e)}i(void 0)}))}}var V={class:"GlobalVoteDetails"},b={class:"GlobalVoteDetailsUser"},N={class:"GlobalVoteDetailsUserName"},E={class:"GlobalVoteDetailsTitle"},x={class:"GlobalVoteDetailsTimeBody"},B={class:"GlobalVoteDetailsTime"},D={key:0,class:"is-primary"},C={key:1,class:"is-warning"},L={key:2,class:"is-info"},S={key:0,class:"GlobalVoteDetailsImg"},G={class:"GlobalVoteDetailsOptions"},I={key:0},T=["innerHTML"],_={key:1},O={key:2},j={key:2,class:"GlobalVoteDetailsButton"},A={key:3,class:"VoteStatisticsList"},U=["onClick"],P={class:"VoteStatisticsItemName"},F={class:"GlobalVoteDetailsOptionContent"},Q={class:"GlobalVoteDetailsUserName ellipsis"},$={class:"GlobalVoteDetailsUserTime"},M={name:"GlobalVoteDetails"},H=Object.assign(M,{props:{id:{type:String,default:""}},emits:["callback","sendMessage"],setup(e,t){var n=t.emit,k=e,M=n,H=(0,s.ref)({}),R=(0,s.ref)({}),q=(0,s.ref)([]),Y=(0,s.ref)(0),z=(0,s.ref)(0),J=(0,s.ref)(!1),K=(0,s.ref)(!1),Z=(0,s.ref)({}),W=(0,s.ref)({}),X=(0,s.ref)([]),ee=(0,s.ref)([]),te=(0,s.ref)([]),ne=function(e){return e?d.A.fileURL(e):d.A.defaultImgURL("default_user_head.jpg")},re=function(e,t){if(!e&&!t)return{id:"0",name:""};var n=new Date,r=new Date(e).getTime(),o=new Date(t).getTime(),a=n.getTime();return a<r?{id:"1",name:"未开始"}:a>r&&a<o?{id:"2",name:"进行中"}:a>o?{id:"3",name:"已结束"}:void 0},oe=function(){if(H.value.isAnonymous)return(0,m.nk)({type:"warning",message:"当前是匿名投票，不可查看详情！"});X.value=ee.value,X.value.length&&(W.value={id:"1"})},ae=function(e){if(H.value.isAnonymous)return(0,m.nk)({type:"warning",message:"当前是匿名投票，不可查看详情！"});X.value=te.value.filter((function(t){return t.optionId===e.id})),X.value.length&&(W.value=e)},ie=function(){var e=w(g().mark((function e(){var t,n;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,d.A.VoteInfo({detailId:k.id});case 2:t=e.sent,n=t.data,n.id?(H.value=n,R.value=re(n.startTime,n.endTime),ue()):K.value=!0;case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),le=function(){y.s.confirm("此操作将删除当前投票, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){ce()})).catch((function(){(0,m.nk)({type:"info",message:"已取消删除"})}))},ce=function(){var e=w(g().mark((function e(){var t,n;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,d.A.VoteDel({ids:[k.id]});case 2:t=e.sent,n=t.code,200===n&&((0,m.nk)({type:"success",message:"删除成功"}),M("callback","del"));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ue=function(){var e=w(g().mark((function e(){var t,n,r,o,a,i,l;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,d.A.VoteUserList({pageNo:1,pageSize:999999,query:{topicId:k.id}});case 2:for(t=e.sent,n=t.data,r=t.total,o=[],a=[],i=0;i<n.length;i++)l=n[i],l.userId===f.kQ.value.id&&(J.value=!0),null!==o&&void 0!==o&&o.includes(l.userId)||(o.push(l.userId),a.push(l)),Z.value[l.optionId]?Z.value[l.optionId].push(l.userId):Z.value[l.optionId]=[l.userId];te.value=n,ee.value=a,Y.value=o.length,z.value=r;case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),se=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return 0==e||0==t?0:Math.round(e/t*1e4)/100},de=function(){var e=w(g().mark((function e(){var t,n,r,o,a,i;return g().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,d.A.globalJson("/voteTopicOptionUser/add",{topicId:k.id,optionIds:q.value});case 2:t=e.sent,n=t.code,200===n&&((0,m.nk)({type:"success",message:"投票成功"}),H.value={},R.value={},q.value=[],Y.value=0,z.value=0,J.value=!1,K.value=!1,Z.value={},ie(),i={name:`${null===(r=f.kQ.value)||void 0===r?void 0:r.userName} 参与了投票 ${H.value.topic}`,data:`${null===(o=f.kQ.value)||void 0===o?void 0:o.userName}|OUI|${null===(a=f.kQ.value)||void 0===a?void 0:a.accountId}|| 参与了投票 ||${H.value.topic}|details30|${H.value.id}`},M("sendMessage",i));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ve=function(){W.value.id?W.value={}:M("callback")};return(0,s.watch)((function(){return k.id}),(function(e){e&&(H.value={},R.value={},q.value=[],Y.value=0,z.value=0,J.value=!1,K.value=!1,Z.value={},ie())}),{immediate:!0}),function(e,t){var n=(0,s.resolveComponent)("Close"),d=u.tk,m=c.x0,y=l.Zq,g=i.S2,k=(0,s.resolveComponent)("ArrowRightBold"),w=a.dI,M=a.o5,ee=o.ve,te=r.kA;return(0,s.openBlock)(),(0,s.createElementBlock)("div",V,[(0,s.createElementVNode)("div",{class:"GlobalAiChatClose",onClick:ve},[(0,s.createVNode)(d,null,{default:(0,s.withCtx)((function(){return[(0,s.createVNode)(n)]})),_:1})]),K.value?((0,s.openBlock)(),(0,s.createBlock)(m,{key:0,description:"投票已不存在"})):(0,s.createCommentVNode)("",!0),H.value.id?(0,s.withDirectives)(((0,s.openBlock)(),(0,s.createBlock)(te,{key:1,class:"GlobalVoteDetailsScrollbar"},{default:(0,s.withCtx)((function(){return[(0,s.createElementVNode)("div",b,[(0,s.createVNode)(y,{src:ne(H.value.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,s.createElementVNode)("div",N,(0,s.toDisplayString)(H.value.crateUserName),1),H.value.createBy===(0,s.unref)(f.kQ).id?((0,s.openBlock)(),(0,s.createElementBlock)("div",{key:0,class:"GlobalVoteDetailsDel",onClick:le},[(0,s.createVNode)(g,{type:"danger",icon:(0,s.unref)(h.Delete),plain:""},null,8,["icon"])])):(0,s.createCommentVNode)("",!0)]),(0,s.createElementVNode)("div",E,(0,s.toDisplayString)(H.value.topic),1),(0,s.createElementVNode)("div",x,[(0,s.createElementVNode)("div",B,(0,s.toDisplayString)((0,s.unref)(v.G)(H.value.startTime))+" 至 "+(0,s.toDisplayString)((0,s.unref)(v.G)(H.value.endTime)),1),"1"===R.value.id?((0,s.openBlock)(),(0,s.createElementBlock)("span",D,(0,s.toDisplayString)(R.value.name),1)):(0,s.createCommentVNode)("",!0),"2"===R.value.id?((0,s.openBlock)(),(0,s.createElementBlock)("span",C,(0,s.toDisplayString)(R.value.name),1)):(0,s.createCommentVNode)("",!0),"3"===R.value.id?((0,s.openBlock)(),(0,s.createElementBlock)("span",L,(0,s.toDisplayString)(R.value.name),1)):(0,s.createCommentVNode)("",!0)]),H.value.topicImg?((0,s.openBlock)(),(0,s.createElementBlock)("div",S,[(0,s.createVNode)(y,{src:ne(H.value.topicImg),fit:"cover",draggable:"false"},null,8,["src"])])):(0,s.createCommentVNode)("",!0),(0,s.createElementVNode)("div",G,[J.value?((0,s.openBlock)(),(0,s.createElementBlock)("div",I,[(0,s.createElementVNode)("div",{class:"GlobalVoteDetailsHasIcon",innerHTML:(0,s.unref)(p.Kp)},null,8,T)])):(0,s.createCommentVNode)("",!0),J.value||"3"===R.value.id?(0,s.createCommentVNode)("",!0):((0,s.openBlock)(),(0,s.createElementBlock)("div",_,"可以选择 "+(0,s.toDisplayString)(H.value.maxVote)+" 项",1)),J.value||"3"!==R.value.id?(0,s.createCommentVNode)("",!0):((0,s.openBlock)(),(0,s.createElementBlock)("div",O)),(0,s.createElementVNode)("span",{onClick:oe},[(0,s.createTextVNode)((0,s.toDisplayString)(Y.value)+"人已参与 ",1),H.value.isAnonymous?(0,s.createCommentVNode)("",!0):((0,s.openBlock)(),(0,s.createBlock)(d,{key:0},{default:(0,s.withCtx)((function(){return[(0,s.createVNode)(k)]})),_:1}))])]),J.value||"3"===R.value.id?(0,s.createCommentVNode)("",!0):((0,s.openBlock)(),(0,s.createBlock)(M,{key:1,modelValue:q.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return q.value=e}),max:H.value.maxVote,disabled:"2"!==R.value.id},{default:(0,s.withCtx)((function(){return[((0,s.openBlock)(!0),(0,s.createElementBlock)(s.Fragment,null,(0,s.renderList)(H.value.options,(function(e){return(0,s.openBlock)(),(0,s.createBlock)(w,{key:e.id,value:e.id,label:e.optionContent},null,8,["value","label"])})),128))]})),_:1},8,["modelValue","max","disabled"])),J.value||"2"!==R.value.id?(0,s.createCommentVNode)("",!0):((0,s.openBlock)(),(0,s.createElementBlock)("div",j,[(0,s.createVNode)(g,{type:"primary",onClick:de},{default:(0,s.withCtx)((function(){return t[1]||(t[1]=[(0,s.createTextVNode)("立即投票")])})),_:1})])),J.value||"3"===R.value.id?((0,s.openBlock)(),(0,s.createElementBlock)("div",A,[((0,s.openBlock)(!0),(0,s.createElementBlock)(s.Fragment,null,(0,s.renderList)(H.value.options,(function(e,t){var n,r;return(0,s.openBlock)(),(0,s.createElementBlock)("div",{class:"VoteStatisticsItem",key:e.id,onClick:function(t){return ae(e)}},[(0,s.createElementVNode)("div",P,(0,s.toDisplayString)(t+1)+"、"+(0,s.toDisplayString)(e.optionContent),1),(0,s.createVNode)(ee,{percentage:se(null===(n=Z.value[e.id])||void 0===n?void 0:n.length,z.value),status:null!==(r=Z.value[e.id])&&void 0!==r&&r.includes((0,s.unref)(f.kQ).id)?"success":""},{default:(0,s.withCtx)((function(t){var n,r=t.percentage;return[(0,s.createElementVNode)("span",null,(0,s.toDisplayString)(r)+"%（"+(0,s.toDisplayString)(null===(n=Z.value[e.id])||void 0===n?void 0:n.length)+"票）",1)]})),_:2},1032,["percentage","status"])],8,U)})),128))])):(0,s.createCommentVNode)("",!0)]})),_:1},512)),[[s.vShow,!W.value.id]]):(0,s.createCommentVNode)("",!0),(0,s.withDirectives)((0,s.createVNode)(te,{class:"GlobalVoteDetailsScrollbar"},{default:(0,s.withCtx)((function(){return[(0,s.createElementVNode)("div",F,(0,s.toDisplayString)(W.value.optionContent||"所有人员"),1),((0,s.openBlock)(!0),(0,s.createElementBlock)(s.Fragment,null,(0,s.renderList)(X.value,(function(e){return(0,s.openBlock)(),(0,s.createElementBlock)("div",{class:"GlobalVoteDetailsUserInfo",key:e.id},[(0,s.createVNode)(y,{src:ne(e.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,s.createElementVNode)("div",Q,(0,s.toDisplayString)(e.userName),1),(0,s.createElementVNode)("div",$,(0,s.toDisplayString)((0,s.unref)(v.G)(e.voteTime)),1)])})),128))]})),_:1},512),[[s.vShow,W.value.id]])])}}});const R=H;var q=R}}]);