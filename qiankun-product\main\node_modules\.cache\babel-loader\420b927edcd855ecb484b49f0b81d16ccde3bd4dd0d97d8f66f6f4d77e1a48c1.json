{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, watch } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { user } from 'common/js/system_var.js';\nimport { hasVoteIcon } from '../../js/icon.js';\nimport { Delete } from '@element-plus/icons-vue';\nvar __default__ = {\n  name: 'GlobalVoteDetails'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['callback', 'sendMessage'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var details = ref({});\n    var detailsStatus = ref({});\n    var checked = ref([]);\n    var totals = ref(0);\n    var voteTotal = ref(0);\n    var hasVote = ref(false);\n    var isDelete = ref(false);\n    var voteOptionsUser = ref({});\n    var voteOptionsInfo = ref({});\n    var voteOptionsData = ref([]);\n    var voteUserData = ref([]);\n    var tableData = ref([]);\n    var imgUrl = function imgUrl(url) {\n      return url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg');\n    };\n    var checkAuditTime = function checkAuditTime(startTime, endTime) {\n      if (!startTime && !endTime) return {\n        id: '0',\n        name: ''\n      };\n      // 获取当前时间\n      var date = new Date();\n      // 获取开始时间、结束时间、现在时间的时间戳\n      var startDate = new Date(startTime).getTime();\n      var endDate = new Date(endTime).getTime();\n      var nowDate = date.getTime();\n      // 判断现在的时间是否在开始时间和结束时间之间\n      if (nowDate < startDate) return {\n        id: '1',\n        name: '未开始'\n      };\n      if (nowDate > startDate && nowDate < endDate) return {\n        id: '2',\n        name: '进行中'\n      };\n      if (nowDate > endDate) return {\n        id: '3',\n        name: '已结束'\n      };\n    };\n    var handleVoteUser = function handleVoteUser() {\n      if (details.value.isAnonymous) return ElMessage({\n        type: 'warning',\n        message: '当前是匿名投票，不可查看详情！'\n      });\n      voteOptionsData.value = voteUserData.value;\n      if (voteOptionsData.value.length) voteOptionsInfo.value = {\n        id: '1'\n      };\n    };\n    var handleVoteStatistics = function handleVoteStatistics(item) {\n      if (details.value.isAnonymous) return ElMessage({\n        type: 'warning',\n        message: '当前是匿名投票，不可查看详情！'\n      });\n      voteOptionsData.value = tableData.value.filter(function (v) {\n        return v.optionId === item.id;\n      });\n      if (voteOptionsData.value.length) voteOptionsInfo.value = item;\n    };\n    var VoteInfo = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$VoteInfo, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.VoteInfo({\n                detailId: props.id\n              });\n            case 2:\n              _yield$api$VoteInfo = _context.sent;\n              data = _yield$api$VoteInfo.data;\n              if (data.id) {\n                details.value = data;\n                detailsStatus.value = checkAuditTime(data.startTime, data.endTime);\n                VoteUserList();\n              } else {\n                isDelete.value = true;\n              }\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function VoteInfo() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleDelVote = function handleDelVote() {\n      ElMessageBox.confirm('此操作将删除当前投票, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        VoteDel();\n      }).catch(function () {\n        ElMessage({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n    };\n    var VoteDel = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var _yield$api$VoteDel, code;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.VoteDel({\n                ids: [props.id]\n              });\n            case 2:\n              _yield$api$VoteDel = _context2.sent;\n              code = _yield$api$VoteDel.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '删除成功'\n                });\n                emit('callback', 'del');\n              }\n            case 5:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function VoteDel() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var VoteUserList = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var _yield$api$VoteUserLi, data, total, userId, userData, index, item;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.VoteUserList({\n                pageNo: 1,\n                pageSize: 999999,\n                query: {\n                  topicId: props.id\n                }\n              });\n            case 2:\n              _yield$api$VoteUserLi = _context3.sent;\n              data = _yield$api$VoteUserLi.data;\n              total = _yield$api$VoteUserLi.total;\n              userId = [];\n              userData = [];\n              for (index = 0; index < data.length; index++) {\n                item = data[index];\n                if (item.userId === user.value.id) hasVote.value = true;\n                if (!(userId !== null && userId !== void 0 && userId.includes(item.userId))) {\n                  userId.push(item.userId);\n                  userData.push(item);\n                }\n                if (voteOptionsUser.value[item.optionId]) {\n                  voteOptionsUser.value[item.optionId].push(item.userId);\n                } else {\n                  voteOptionsUser.value[item.optionId] = [item.userId];\n                }\n              }\n              tableData.value = data;\n              voteUserData.value = userData;\n              totals.value = userId.length;\n              voteTotal.value = total;\n            case 12:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function VoteUserList() {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var percent = function percent() {\n      var num = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      var total = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      if (num == 0 || total == 0) return 0;\n      return Math.round(num / total * 10000) / 100.00;\n    };\n    var globalJson = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var _yield$api$globalJson, code, _user$value, _user$value2, _user$value3, sendMessageData;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.next = 2;\n              return api.globalJson('/voteTopicOptionUser/add', {\n                topicId: props.id,\n                optionIds: checked.value\n              });\n            case 2:\n              _yield$api$globalJson = _context4.sent;\n              code = _yield$api$globalJson.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: '投票成功'\n                });\n                details.value = {};\n                detailsStatus.value = {};\n                checked.value = [];\n                totals.value = 0;\n                voteTotal.value = 0;\n                hasVote.value = false;\n                isDelete.value = false;\n                voteOptionsUser.value = {};\n                VoteInfo();\n                sendMessageData = {\n                  name: `${(_user$value = user.value) === null || _user$value === void 0 ? void 0 : _user$value.userName} 参与了投票 ${details.value.topic}`,\n                  data: `${(_user$value2 = user.value) === null || _user$value2 === void 0 ? void 0 : _user$value2.userName}|OUI|${(_user$value3 = user.value) === null || _user$value3 === void 0 ? void 0 : _user$value3.accountId}|| 参与了投票 ||${details.value.topic}|details30|${details.value.id}`\n                };\n                emit('sendMessage', sendMessageData);\n              }\n            case 5:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function globalJson() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handleClick = function handleClick() {\n      if (voteOptionsInfo.value.id) {\n        voteOptionsInfo.value = {};\n      } else {\n        emit('callback');\n      }\n    };\n    watch(function () {\n      return props.id;\n    }, function (val) {\n      if (val) {\n        details.value = {};\n        detailsStatus.value = {};\n        checked.value = [];\n        totals.value = 0;\n        voteTotal.value = 0;\n        hasVote.value = false;\n        isDelete.value = false;\n        voteOptionsUser.value = {};\n        VoteInfo();\n      }\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      props,\n      emit,\n      details,\n      detailsStatus,\n      checked,\n      totals,\n      voteTotal,\n      hasVote,\n      isDelete,\n      voteOptionsUser,\n      voteOptionsInfo,\n      voteOptionsData,\n      voteUserData,\n      tableData,\n      imgUrl,\n      checkAuditTime,\n      handleVoteUser,\n      handleVoteStatistics,\n      VoteInfo,\n      handleDelVote,\n      VoteDel,\n      VoteUserList,\n      percent,\n      globalJson,\n      handleClick,\n      get api() {\n        return api;\n      },\n      ref,\n      watch,\n      get format() {\n        return format;\n      },\n      get user() {\n        return user;\n      },\n      get hasVoteIcon() {\n        return hasVoteIcon;\n      },\n      get Delete() {\n        return Delete;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "watch", "format", "user", "hasVoteIcon", "Delete", "__default__", "props", "__props", "emit", "__emit", "details", "detailsStatus", "checked", "totals", "voteTotal", "hasVote", "isDelete", "voteOptionsUser", "voteOptionsInfo", "voteOptionsData", "voteUserData", "tableData", "imgUrl", "url", "fileURL", "defaultImgURL", "checkAuditTime", "startTime", "endTime", "id", "date", "Date", "startDate", "getTime", "endDate", "nowDate", "handleVoteUser", "isAnonymous", "ElMessage", "message", "handleVoteStatistics", "item", "filter", "optionId", "VoteInfo", "_ref2", "_callee", "_yield$api$VoteInfo", "data", "_callee$", "_context", "detailId", "VoteUserList", "handleDelVote", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "VoteDel", "_ref3", "_callee2", "_yield$api$VoteDel", "code", "_callee2$", "_context2", "ids", "_ref4", "_callee3", "_yield$api$VoteUserLi", "total", "userId", "userData", "index", "_callee3$", "_context3", "pageNo", "pageSize", "query", "topicId", "includes", "percent", "num", "undefined", "Math", "round", "globalJson", "_ref5", "_callee4", "_yield$api$globalJson", "_user$value", "_user$value2", "_user$value3", "sendMessageData", "_callee4$", "_context4", "optionIds", "userName", "topic", "accountId", "handleClick", "val", "immediate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/components/GlobalGroupVote/GlobalVoteDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalVoteDetails\">\r\n    <div class=\"GlobalAiChatClose\" @click=\"handleClick\">\r\n      <el-icon>\r\n        <Close />\r\n      </el-icon>\r\n    </div>\r\n    <el-empty description=\"投票已不存在\" v-if=\"isDelete\" />\r\n    <el-scrollbar class=\"GlobalVoteDetailsScrollbar\" v-if=\"details.id\" v-show=\"!voteOptionsInfo.id\">\r\n      <div class=\"GlobalVoteDetailsUser\">\r\n        <el-image :src=\"imgUrl(details.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n        <div class=\"GlobalVoteDetailsUserName\">{{ details.crateUserName }}</div>\r\n        <div class=\"GlobalVoteDetailsDel\" @click=\"handleDelVote\" v-if=\"details.createBy === user.id\">\r\n          <el-button type=\"danger\" :icon=\"Delete\" plain />\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalVoteDetailsTitle\">{{ details.topic }}</div>\r\n      <div class=\"GlobalVoteDetailsTimeBody\">\r\n        <div class=\"GlobalVoteDetailsTime\">{{ format(details.startTime) }} 至 {{ format(details.endTime) }}</div>\r\n        <span class=\"is-primary\" v-if=\"detailsStatus.id === '1'\">{{ detailsStatus.name }}</span>\r\n        <span class=\"is-warning\" v-if=\"detailsStatus.id === '2'\">{{ detailsStatus.name }}</span>\r\n        <span class=\"is-info\" v-if=\"detailsStatus.id === '3'\">{{ detailsStatus.name }}</span>\r\n      </div>\r\n      <div class=\"GlobalVoteDetailsImg\" v-if=\"details.topicImg\">\r\n        <el-image :src=\"imgUrl(details.topicImg)\" fit=\"cover\" draggable=\"false\" />\r\n      </div>\r\n      <div class=\"GlobalVoteDetailsOptions\">\r\n        <div v-if=\"hasVote\">\r\n          <div class=\"GlobalVoteDetailsHasIcon\" v-html=\"hasVoteIcon\"></div>\r\n        </div>\r\n        <div v-if=\"!hasVote && detailsStatus.id !== '3'\">可以选择 {{ details.maxVote }} 项</div>\r\n        <div v-if=\"!hasVote && detailsStatus.id === '3'\"></div>\r\n        <span @click=\"handleVoteUser\">\r\n          {{ totals }}人已参与\r\n          <el-icon v-if=\"!details.isAnonymous\">\r\n            <ArrowRightBold />\r\n          </el-icon>\r\n        </span>\r\n      </div>\r\n      <el-checkbox-group v-model=\"checked\" :max=\"details.maxVote\" :disabled=\"detailsStatus.id !== '2'\"\r\n        v-if=\"!hasVote && detailsStatus.id !== '3'\">\r\n        <el-checkbox v-for=\"item in details.options\" :key=\"item.id\" :value=\"item.id\"\r\n          :label=\"item.optionContent\"></el-checkbox>\r\n      </el-checkbox-group>\r\n      <div class=\"GlobalVoteDetailsButton\" v-if=\"!hasVote && detailsStatus.id === '2'\">\r\n        <el-button type=\"primary\" @click=\"globalJson\">立即投票</el-button>\r\n      </div>\r\n      <div class=\"VoteStatisticsList\" v-if=\"hasVote || detailsStatus.id === '3'\">\r\n        <div class=\"VoteStatisticsItem\" v-for=\"(item, index) in details.options\" :key=\"item.id\"\r\n          @click=\"handleVoteStatistics(item)\">\r\n          <div class=\"VoteStatisticsItemName\">{{ index + 1 }}、{{ item.optionContent }}</div>\r\n          <el-progress :percentage=\"percent(voteOptionsUser[item.id]?.length, voteTotal)\"\r\n            :status=\"voteOptionsUser[item.id]?.includes(user.id) ? 'success' : ''\">\r\n            <template #default=\"{ percentage }\">\r\n              <span>{{ percentage }}%（{{ voteOptionsUser[item.id]?.length }}票）</span>\r\n            </template>\r\n          </el-progress>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <el-scrollbar class=\"GlobalVoteDetailsScrollbar\" v-show=\"voteOptionsInfo.id\">\r\n      <div class=\"GlobalVoteDetailsOptionContent\">{{ voteOptionsInfo.optionContent || '所有人员' }}</div>\r\n      <div class=\"GlobalVoteDetailsUserInfo\" v-for=\"item in voteOptionsData\" :key=\"item.id\">\r\n        <el-image :src=\"imgUrl(item.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n        <div class=\"GlobalVoteDetailsUserName ellipsis\">{{ item.userName }}</div>\r\n        <div class=\"GlobalVoteDetailsUserTime\">{{ format(item.voteTime) }}</div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalVoteDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, watch } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { hasVoteIcon } from '../../js/icon.js'\r\nimport { Delete } from '@element-plus/icons-vue'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback', 'sendMessage'])\r\n\r\nconst details = ref({})\r\nconst detailsStatus = ref({})\r\nconst checked = ref([])\r\nconst totals = ref(0)\r\nconst voteTotal = ref(0)\r\nconst hasVote = ref(false)\r\nconst isDelete = ref(false)\r\nconst voteOptionsUser = ref({})\r\nconst voteOptionsInfo = ref({})\r\nconst voteOptionsData = ref([])\r\nconst voteUserData = ref([])\r\nconst tableData = ref([])\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\n\r\nconst checkAuditTime = (startTime, endTime) => {\r\n  if (!startTime && !endTime) return { id: '0', name: '' }\r\n  // 获取当前时间\r\n  const date = new Date()\r\n  // 获取开始时间、结束时间、现在时间的时间戳\r\n  const startDate = new Date(startTime).getTime()\r\n  const endDate = new Date(endTime).getTime()\r\n  const nowDate = date.getTime()\r\n  // 判断现在的时间是否在开始时间和结束时间之间\r\n  if (nowDate < startDate) return { id: '1', name: '未开始' }\r\n  if (nowDate > startDate && nowDate < endDate) return { id: '2', name: '进行中' }\r\n  if (nowDate > endDate) return { id: '3', name: '已结束' }\r\n}\r\nconst handleVoteUser = () => {\r\n  if (details.value.isAnonymous) return ElMessage({ type: 'warning', message: '当前是匿名投票，不可查看详情！' })\r\n  voteOptionsData.value = voteUserData.value\r\n  if (voteOptionsData.value.length) voteOptionsInfo.value = { id: '1' }\r\n}\r\nconst handleVoteStatistics = (item) => {\r\n  if (details.value.isAnonymous) return ElMessage({ type: 'warning', message: '当前是匿名投票，不可查看详情！' })\r\n  voteOptionsData.value = tableData.value.filter(v => v.optionId === item.id)\r\n  if (voteOptionsData.value.length) voteOptionsInfo.value = item\r\n}\r\nconst VoteInfo = async () => {\r\n  const { data } = await api.VoteInfo({ detailId: props.id })\r\n  if (data.id) {\r\n    details.value = data\r\n    detailsStatus.value = checkAuditTime(data.startTime, data.endTime)\r\n    VoteUserList()\r\n  } else {\r\n    isDelete.value = true\r\n  }\r\n}\r\nconst handleDelVote = () => {\r\n  ElMessageBox.confirm('此操作将删除当前投票, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { VoteDel() }).catch(() => { ElMessage({ type: 'info', message: '已取消删除' }) })\r\n}\r\nconst VoteDel = async () => {\r\n  const { code } = await api.VoteDel({ ids: [props.id] })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '删除成功' })\r\n    emit('callback', 'del')\r\n  }\r\n}\r\nconst VoteUserList = async () => {\r\n  const { data, total } = await api.VoteUserList({ pageNo: 1, pageSize: 999999, query: { topicId: props.id } })\r\n  const userId = []\r\n  const userData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.userId === user.value.id) hasVote.value = true\r\n    if (!userId?.includes(item.userId)) {\r\n      userId.push(item.userId)\r\n      userData.push(item)\r\n    }\r\n    if (voteOptionsUser.value[item.optionId]) {\r\n      voteOptionsUser.value[item.optionId].push(item.userId)\r\n    } else {\r\n      voteOptionsUser.value[item.optionId] = [item.userId]\r\n    }\r\n  }\r\n  tableData.value = data\r\n  voteUserData.value = userData\r\n  totals.value = userId.length\r\n  voteTotal.value = total\r\n}\r\nconst percent = (num = 0, total = 0) => {\r\n  if (num == 0 || total == 0) return 0\r\n  return (Math.round(num / total * 10000) / 100.00)\r\n}\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/voteTopicOptionUser/add', { topicId: props.id, optionIds: checked.value })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '投票成功' })\r\n    details.value = {}\r\n    detailsStatus.value = {}\r\n    checked.value = []\r\n    totals.value = 0\r\n    voteTotal.value = 0\r\n    hasVote.value = false\r\n    isDelete.value = false\r\n    voteOptionsUser.value = {}\r\n    VoteInfo()\r\n    const sendMessageData = {\r\n      name: `${user.value?.userName} 参与了投票 ${details.value.topic}`,\r\n      data: `${user.value?.userName}|OUI|${user.value?.accountId}|| 参与了投票 ||${details.value.topic}|details30|${details.value.id}`,\r\n    }\r\n    emit('sendMessage', sendMessageData)\r\n  }\r\n}\r\nconst handleClick = () => {\r\n  if (voteOptionsInfo.value.id) {\r\n    voteOptionsInfo.value = {}\r\n  } else {\r\n    emit('callback')\r\n  }\r\n}\r\nwatch(() => props.id, (val) => {\r\n  if (val) {\r\n    details.value = {}\r\n    detailsStatus.value = {}\r\n    checked.value = []\r\n    totals.value = 0\r\n    voteTotal.value = 0\r\n    hasVote.value = false\r\n    isDelete.value = false\r\n    voteOptionsUser.value = {}\r\n    VoteInfo()\r\n  }\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalVoteDetails {\r\n  width: 420px;\r\n  height: 100%;\r\n  padding-top: 32px;\r\n  background: #fff;\r\n  position: relative;\r\n\r\n  .GlobalAiChatClose {\r\n    width: 32px;\r\n    height: 28px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: 20px;\r\n    border-radius: 2px;\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: #fff;\r\n      background-color: rgba($color: red, $alpha: 0.6);\r\n    }\r\n\r\n    .zy-el-icon {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsScrollbar {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .GlobalVoteDetailsUser {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    padding: 0 20px 12px 20px;\r\n    position: relative;\r\n\r\n    .zy-el-image {\r\n      width: 38px;\r\n      height: 38px;\r\n      border-radius: 50%;\r\n    }\r\n\r\n    .GlobalVoteDetailsUserName {\r\n      padding-left: 12px;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .GlobalVoteDetailsDel {\r\n      position: absolute;\r\n      right: 20px;\r\n      bottom: 12px;\r\n\r\n      .zy-el-button {\r\n        width: var(--zy-height-secondary);\r\n        height: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsTitle {\r\n    padding: 0 20px;\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-line-height);\r\n  }\r\n\r\n  .GlobalVoteDetailsTimeBody {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n\r\n    .GlobalVoteDetailsTime {\r\n      padding: 6px 0;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-secondary);\r\n    }\r\n\r\n    .is-primary,\r\n    .is-warning,\r\n    .is-info {\r\n      font-size: 12px;\r\n      padding: 2px 6px;\r\n      border-radius: 2px;\r\n    }\r\n\r\n    .is-primary {\r\n      color: var(--zy-el-color-primary);\r\n      background: var(--zy-el-color-primary-light-9);\r\n    }\r\n\r\n    .is-warning {\r\n      color: var(--zy-el-color-warning);\r\n      background: var(--zy-el-color-warning-light-9);\r\n    }\r\n\r\n    .is-info {\r\n      color: var(--zy-el-color-info);\r\n      background: var(--zy-el-color-info-light-9);\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsImg {\r\n    width: 100%;\r\n    padding: 0 20px;\r\n\r\n    .zy-el-image {\r\n      width: 100%;\r\n      height: 168px;\r\n      border-radius: 6px;\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsOptions {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 40px 20px 0 20px;\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: 100%;\r\n      height: 10px;\r\n      position: absolute;\r\n      top: 10px;\r\n      left: 0;\r\n      background: var(--zy-el-color-info-light-9);\r\n    }\r\n\r\n    &>div {\r\n      font-weight: bold;\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-color-primary);\r\n      position: relative;\r\n    }\r\n\r\n    .GlobalVoteDetailsHasIcon {\r\n      width: 42px;\r\n      height: 42px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 0;\r\n      transform: translateY(-50%);\r\n      z-index: 9;\r\n    }\r\n\r\n    span {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: var(--zy-text-font-size);\r\n      color: var(--zy-el-text-color-secondary);\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .zy-el-checkbox-group {\r\n    padding: 6px 20px 20px 20px;\r\n\r\n    .zy-el-checkbox {\r\n      width: 100%;\r\n      height: auto;\r\n      padding: 6px 0;\r\n      margin: 0;\r\n\r\n      .zy-el-checkbox__label {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        overflow-wrap: break-word;\r\n        white-space: pre-wrap;\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsButton {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding-bottom: 20px;\r\n\r\n    .zy-el-button {\r\n      width: 120px;\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n\r\n  .VoteStatisticsList {\r\n    width: 100%;\r\n    padding: 12px 20px;\r\n\r\n    .VoteStatisticsItem {\r\n      cursor: pointer;\r\n      padding: var(--zy-distance-five) 0;\r\n\r\n      .VoteStatisticsItemName {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-bottom: var(--zy-font-text-distance-five);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsOptionContent {\r\n    width: 100%;\r\n    padding: 0 20px 32px 20px;\r\n    font-weight: bold;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: 100%;\r\n      height: 10px;\r\n      position: absolute;\r\n      left: 0;\r\n      bottom: 12px;\r\n      background: var(--zy-el-color-info-light-9);\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetailsUserInfo {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    padding: 0 20px 12px 20px;\r\n    position: relative;\r\n\r\n    .zy-el-image {\r\n      width: 32px;\r\n      height: 32px;\r\n      border-radius: 50%;\r\n    }\r\n\r\n    .GlobalVoteDetailsUserName {\r\n      flex: 1;\r\n      padding: 0 12px;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .GlobalVoteDetailsUserTime {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA2EA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAChC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,yBAAyB;AARhD,IAAAC,WAAA,GAAe;EAAElC,IAAI,EAAE;AAAoB,CAAC;;;;;;;;;;;;;IAS5C,IAAMmC,KAAK,GAAGC,OAAkD;IAChE,IAAMC,IAAI,GAAGC,MAAwC;IAErD,IAAMC,OAAO,GAAGX,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMY,aAAa,GAAGZ,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAMa,OAAO,GAAGb,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMc,MAAM,GAAGd,GAAG,CAAC,CAAC,CAAC;IACrB,IAAMe,SAAS,GAAGf,GAAG,CAAC,CAAC,CAAC;IACxB,IAAMgB,OAAO,GAAGhB,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMiB,QAAQ,GAAGjB,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMkB,eAAe,GAAGlB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAMmB,eAAe,GAAGnB,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAMoB,eAAe,GAAGpB,GAAG,CAAC,EAAE,CAAC;IAC/B,IAAMqB,YAAY,GAAGrB,GAAG,CAAC,EAAE,CAAC;IAC5B,IAAMsB,SAAS,GAAGtB,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMuB,MAAM,GAAG,SAATA,MAAMA,CAAGC,GAAG;MAAA,OAAIA,GAAG,GAAGzB,GAAG,CAAC0B,OAAO,CAACD,GAAG,CAAC,GAAGzB,GAAG,CAAC2B,aAAa,CAAC,uBAAuB,CAAC;IAAA;IAEzF,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,SAAS,EAAEC,OAAO,EAAK;MAC7C,IAAI,CAACD,SAAS,IAAI,CAACC,OAAO,EAAE,OAAO;QAAEC,EAAE,EAAE,GAAG;QAAE1D,IAAI,EAAE;MAAG,CAAC;MACxD;MACA,IAAM2D,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC;MACvB;MACA,IAAMC,SAAS,GAAG,IAAID,IAAI,CAACJ,SAAS,CAAC,CAACM,OAAO,CAAC,CAAC;MAC/C,IAAMC,OAAO,GAAG,IAAIH,IAAI,CAACH,OAAO,CAAC,CAACK,OAAO,CAAC,CAAC;MAC3C,IAAME,OAAO,GAAGL,IAAI,CAACG,OAAO,CAAC,CAAC;MAC9B;MACA,IAAIE,OAAO,GAAGH,SAAS,EAAE,OAAO;QAAEH,EAAE,EAAE,GAAG;QAAE1D,IAAI,EAAE;MAAM,CAAC;MACxD,IAAIgE,OAAO,GAAGH,SAAS,IAAIG,OAAO,GAAGD,OAAO,EAAE,OAAO;QAAEL,EAAE,EAAE,GAAG;QAAE1D,IAAI,EAAE;MAAM,CAAC;MAC7E,IAAIgE,OAAO,GAAGD,OAAO,EAAE,OAAO;QAAEL,EAAE,EAAE,GAAG;QAAE1D,IAAI,EAAE;MAAM,CAAC;IACxD,CAAC;IACD,IAAMiE,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAI1B,OAAO,CAAChH,KAAK,CAAC2I,WAAW,EAAE,OAAOC,SAAS,CAAC;QAAEzH,IAAI,EAAE,SAAS;QAAE0H,OAAO,EAAE;MAAkB,CAAC,CAAC;MAChGpB,eAAe,CAACzH,KAAK,GAAG0H,YAAY,CAAC1H,KAAK;MAC1C,IAAIyH,eAAe,CAACzH,KAAK,CAACqE,MAAM,EAAEmD,eAAe,CAACxH,KAAK,GAAG;QAAEmI,EAAE,EAAE;MAAI,CAAC;IACvE,CAAC;IACD,IAAMW,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,IAAI,EAAK;MACrC,IAAI/B,OAAO,CAAChH,KAAK,CAAC2I,WAAW,EAAE,OAAOC,SAAS,CAAC;QAAEzH,IAAI,EAAE,SAAS;QAAE0H,OAAO,EAAE;MAAkB,CAAC,CAAC;MAChGpB,eAAe,CAACzH,KAAK,GAAG2H,SAAS,CAAC3H,KAAK,CAACgJ,MAAM,CAAC,UAAAhH,CAAC;QAAA,OAAIA,CAAC,CAACiH,QAAQ,KAAKF,IAAI,CAACZ,EAAE;MAAA,EAAC;MAC3E,IAAIV,eAAe,CAACzH,KAAK,CAACqE,MAAM,EAAEmD,eAAe,CAACxH,KAAK,GAAG+I,IAAI;IAChE,CAAC;IACD,IAAMG,QAAQ;MAAA,IAAAC,KAAA,GAAApD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0E,QAAA;QAAA,IAAAC,mBAAA,EAAAC,IAAA;QAAA,OAAAhK,mBAAA,GAAAuB,IAAA,UAAA0I,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAArE,IAAA,GAAAqE,QAAA,CAAAhG,IAAA;YAAA;cAAAgG,QAAA,CAAAhG,IAAA;cAAA,OACQ4C,GAAG,CAAC8C,QAAQ,CAAC;gBAAEO,QAAQ,EAAE7C,KAAK,CAACuB;cAAG,CAAC,CAAC;YAAA;cAAAkB,mBAAA,GAAAG,QAAA,CAAAvG,IAAA;cAAnDqG,IAAI,GAAAD,mBAAA,CAAJC,IAAI;cACZ,IAAIA,IAAI,CAACnB,EAAE,EAAE;gBACXnB,OAAO,CAAChH,KAAK,GAAGsJ,IAAI;gBACpBrC,aAAa,CAACjH,KAAK,GAAGgI,cAAc,CAACsB,IAAI,CAACrB,SAAS,EAAEqB,IAAI,CAACpB,OAAO,CAAC;gBAClEwB,YAAY,CAAC,CAAC;cAChB,CAAC,MAAM;gBACLpC,QAAQ,CAACtH,KAAK,GAAG,IAAI;cACvB;YAAC;YAAA;cAAA,OAAAwJ,QAAA,CAAAlE,IAAA;UAAA;QAAA,GAAA8D,OAAA;MAAA,CACF;MAAA,gBATKF,QAAQA,CAAA;QAAA,OAAAC,KAAA,CAAAlD,KAAA,OAAAD,SAAA;MAAA;IAAA,GASb;IACD,IAAM2D,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1BC,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;QAC9CC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtB5I,IAAI,EAAE;MACR,CAAC,CAAC,CAACuB,IAAI,CAAC,YAAM;QAAEsH,OAAO,CAAC,CAAC;MAAC,CAAC,CAAC,CAACrE,KAAK,CAAC,YAAM;QAAEiD,SAAS,CAAC;UAAEzH,IAAI,EAAE,MAAM;UAAE0H,OAAO,EAAE;QAAQ,CAAC,CAAC;MAAC,CAAC,CAAC;IAC7F,CAAC;IACD,IAAMmB,OAAO;MAAA,IAAAC,KAAA,GAAAlE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAwF,SAAA;QAAA,IAAAC,kBAAA,EAAAC,IAAA;QAAA,OAAA9K,mBAAA,GAAAuB,IAAA,UAAAwJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnF,IAAA,GAAAmF,SAAA,CAAA9G,IAAA;YAAA;cAAA8G,SAAA,CAAA9G,IAAA;cAAA,OACS4C,GAAG,CAAC4D,OAAO,CAAC;gBAAEO,GAAG,EAAE,CAAC3D,KAAK,CAACuB,EAAE;cAAE,CAAC,CAAC;YAAA;cAAAgC,kBAAA,GAAAG,SAAA,CAAArH,IAAA;cAA/CmH,IAAI,GAAAD,kBAAA,CAAJC,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBxB,SAAS,CAAC;kBAAEzH,IAAI,EAAE,SAAS;kBAAE0H,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/C/B,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;cACzB;YAAC;YAAA;cAAA,OAAAwD,SAAA,CAAAhF,IAAA;UAAA;QAAA,GAAA4E,QAAA;MAAA,CACF;MAAA,gBANKF,OAAOA,CAAA;QAAA,OAAAC,KAAA,CAAAhE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMZ;IACD,IAAM0D,YAAY;MAAA,IAAAc,KAAA,GAAAzE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+F,SAAA;QAAA,IAAAC,qBAAA,EAAApB,IAAA,EAAAqB,KAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,KAAA,EAAA/B,IAAA;QAAA,OAAAzJ,mBAAA,GAAAuB,IAAA,UAAAkK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7F,IAAA,GAAA6F,SAAA,CAAAxH,IAAA;YAAA;cAAAwH,SAAA,CAAAxH,IAAA;cAAA,OACW4C,GAAG,CAACsD,YAAY,CAAC;gBAAEuB,MAAM,EAAE,CAAC;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE;kBAAEC,OAAO,EAAExE,KAAK,CAACuB;gBAAG;cAAE,CAAC,CAAC;YAAA;cAAAuC,qBAAA,GAAAM,SAAA,CAAA/H,IAAA;cAArGqG,IAAI,GAAAoB,qBAAA,CAAJpB,IAAI;cAAEqB,KAAK,GAAAD,qBAAA,CAALC,KAAK;cACbC,MAAM,GAAG,EAAE;cACXC,QAAQ,GAAG,EAAE;cACnB,KAASC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGxB,IAAI,CAACjF,MAAM,EAAEyG,KAAK,EAAE,EAAE;gBAC1C/B,IAAI,GAAGO,IAAI,CAACwB,KAAK,CAAC;gBACxB,IAAI/B,IAAI,CAAC6B,MAAM,KAAKpE,IAAI,CAACxG,KAAK,CAACmI,EAAE,EAAEd,OAAO,CAACrH,KAAK,GAAG,IAAI;gBACvD,IAAI,EAAC4K,MAAM,aAANA,MAAM,eAANA,MAAM,CAAES,QAAQ,CAACtC,IAAI,CAAC6B,MAAM,CAAC,GAAE;kBAClCA,MAAM,CAAC5G,IAAI,CAAC+E,IAAI,CAAC6B,MAAM,CAAC;kBACxBC,QAAQ,CAAC7G,IAAI,CAAC+E,IAAI,CAAC;gBACrB;gBACA,IAAIxB,eAAe,CAACvH,KAAK,CAAC+I,IAAI,CAACE,QAAQ,CAAC,EAAE;kBACxC1B,eAAe,CAACvH,KAAK,CAAC+I,IAAI,CAACE,QAAQ,CAAC,CAACjF,IAAI,CAAC+E,IAAI,CAAC6B,MAAM,CAAC;gBACxD,CAAC,MAAM;kBACLrD,eAAe,CAACvH,KAAK,CAAC+I,IAAI,CAACE,QAAQ,CAAC,GAAG,CAACF,IAAI,CAAC6B,MAAM,CAAC;gBACtD;cACF;cACAjD,SAAS,CAAC3H,KAAK,GAAGsJ,IAAI;cACtB5B,YAAY,CAAC1H,KAAK,GAAG6K,QAAQ;cAC7B1D,MAAM,CAACnH,KAAK,GAAG4K,MAAM,CAACvG,MAAM;cAC5B+C,SAAS,CAACpH,KAAK,GAAG2K,KAAK;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAA1F,IAAA;UAAA;QAAA,GAAAmF,QAAA;MAAA,CACxB;MAAA,gBArBKf,YAAYA,CAAA;QAAA,OAAAc,KAAA,CAAAvE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAqBjB;IACD,IAAMsF,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAA2B;MAAA,IAAvBC,GAAG,GAAAvF,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAwF,SAAA,GAAAxF,SAAA,MAAG,CAAC;MAAA,IAAE2E,KAAK,GAAA3E,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAAwF,SAAA,GAAAxF,SAAA,MAAG,CAAC;MACjC,IAAIuF,GAAG,IAAI,CAAC,IAAIZ,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC;MACpC,OAAQc,IAAI,CAACC,KAAK,CAACH,GAAG,GAAGZ,KAAK,GAAG,KAAK,CAAC,GAAG,MAAM;IAClD,CAAC;IACD,IAAMgB,UAAU;MAAA,IAAAC,KAAA,GAAA7F,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAmH,SAAA;QAAA,IAAAC,qBAAA,EAAA1B,IAAA,EAAA2B,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,eAAA;QAAA,OAAA5M,mBAAA,GAAAuB,IAAA,UAAAsL,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjH,IAAA,GAAAiH,SAAA,CAAA5I,IAAA;YAAA;cAAA4I,SAAA,CAAA5I,IAAA;cAAA,OACM4C,GAAG,CAACuF,UAAU,CAAC,0BAA0B,EAAE;gBAAEP,OAAO,EAAExE,KAAK,CAACuB,EAAE;gBAAEkE,SAAS,EAAEnF,OAAO,CAAClH;cAAM,CAAC,CAAC;YAAA;cAAA8L,qBAAA,GAAAM,SAAA,CAAAnJ,IAAA;cAA1GmH,IAAI,GAAA0B,qBAAA,CAAJ1B,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBxB,SAAS,CAAC;kBAAEzH,IAAI,EAAE,SAAS;kBAAE0H,OAAO,EAAE;gBAAO,CAAC,CAAC;gBAC/C7B,OAAO,CAAChH,KAAK,GAAG,CAAC,CAAC;gBAClBiH,aAAa,CAACjH,KAAK,GAAG,CAAC,CAAC;gBACxBkH,OAAO,CAAClH,KAAK,GAAG,EAAE;gBAClBmH,MAAM,CAACnH,KAAK,GAAG,CAAC;gBAChBoH,SAAS,CAACpH,KAAK,GAAG,CAAC;gBACnBqH,OAAO,CAACrH,KAAK,GAAG,KAAK;gBACrBsH,QAAQ,CAACtH,KAAK,GAAG,KAAK;gBACtBuH,eAAe,CAACvH,KAAK,GAAG,CAAC,CAAC;gBAC1BkJ,QAAQ,CAAC,CAAC;gBACJgD,eAAe,GAAG;kBACtBzH,IAAI,EAAE,IAAAsH,WAAA,GAAGvF,IAAI,CAACxG,KAAK,cAAA+L,WAAA,uBAAVA,WAAA,CAAYO,QAAQ,UAAUtF,OAAO,CAAChH,KAAK,CAACuM,KAAK,EAAE;kBAC5DjD,IAAI,EAAE,IAAA0C,YAAA,GAAGxF,IAAI,CAACxG,KAAK,cAAAgM,YAAA,uBAAVA,YAAA,CAAYM,QAAQ,SAAAL,YAAA,GAAQzF,IAAI,CAACxG,KAAK,cAAAiM,YAAA,uBAAVA,YAAA,CAAYO,SAAS,cAAcxF,OAAO,CAAChH,KAAK,CAACuM,KAAK,cAAcvF,OAAO,CAAChH,KAAK,CAACmI,EAAE;gBAC3H,CAAC;gBACDrB,IAAI,CAAC,aAAa,EAAEoF,eAAe,CAAC;cACtC;YAAC;YAAA;cAAA,OAAAE,SAAA,CAAA9G,IAAA;UAAA;QAAA,GAAAuG,QAAA;MAAA,CACF;MAAA,gBAnBKF,UAAUA,CAAA;QAAA,OAAAC,KAAA,CAAA3F,KAAA,OAAAD,SAAA;MAAA;IAAA,GAmBf;IACD,IAAMyG,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIjF,eAAe,CAACxH,KAAK,CAACmI,EAAE,EAAE;QAC5BX,eAAe,CAACxH,KAAK,GAAG,CAAC,CAAC;MAC5B,CAAC,MAAM;QACL8G,IAAI,CAAC,UAAU,CAAC;MAClB;IACF,CAAC;IACDR,KAAK,CAAC;MAAA,OAAMM,KAAK,CAACuB,EAAE;IAAA,GAAE,UAACuE,GAAG,EAAK;MAC7B,IAAIA,GAAG,EAAE;QACP1F,OAAO,CAAChH,KAAK,GAAG,CAAC,CAAC;QAClBiH,aAAa,CAACjH,KAAK,GAAG,CAAC,CAAC;QACxBkH,OAAO,CAAClH,KAAK,GAAG,EAAE;QAClBmH,MAAM,CAACnH,KAAK,GAAG,CAAC;QAChBoH,SAAS,CAACpH,KAAK,GAAG,CAAC;QACnBqH,OAAO,CAACrH,KAAK,GAAG,KAAK;QACrBsH,QAAQ,CAACtH,KAAK,GAAG,KAAK;QACtBuH,eAAe,CAACvH,KAAK,GAAG,CAAC,CAAC;QAC1BkJ,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,EAAE;MAAEyD,SAAS,EAAE;IAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}