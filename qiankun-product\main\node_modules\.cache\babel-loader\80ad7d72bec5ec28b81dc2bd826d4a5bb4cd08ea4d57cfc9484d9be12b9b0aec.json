{"ast": null, "code": "import { renderSlot as _renderSlot, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LayoutPersonalDoList\"\n};\nvar _hoisted_2 = {\n  class: \"LayoutPersonalDoListBody\"\n};\nvar _hoisted_3 = {\n  class: \"LayoutPersonalDoListHead\"\n};\nvar _hoisted_4 = {\n  class: \"LayoutPersonalDoListScroll\"\n};\nvar _hoisted_5 = [\"onClick\"];\nvar _hoisted_6 = {\n  class: \"LayoutPersonalDoListInfo\"\n};\nvar _hoisted_7 = {\n  class: \"LayoutPersonalDoListType\"\n};\nvar _hoisted_8 = {\n  class: \"LayoutPersonalDoListTime\"\n};\nvar _hoisted_9 = {\n  class: \"LayoutPersonalDoListTitle\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  class: \"LayoutPersonalDoListLoadingText\"\n};\nvar _hoisted_11 = {\n  key: 1,\n  class: \"LayoutPersonalDoListLoadingText\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_DArrowRight = _resolveComponent(\"DArrowRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  var _component_el_badge = _resolveComponent(\"el-badge\");\n  return _openBlock(), _createBlock(_component_el_badge, {\n    value: $setup.totals\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_popover, {\n        trigger: \"hover\",\n        \"popper-class\": \"LayoutPersonalDoListPopover\",\n        transition: \"zy-el-zoom-in-top\"\n      }, {\n        reference: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_1, [_renderSlot(_ctx.$slots, \"default\")])];\n        }),\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n            class: \"LayoutPersonalDoListName\"\n          }, \"个人待办\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n            onClick: _cache[0] || (_cache[0] = function ($event) {\n              return $setup.openPage({\n                key: 'routePath',\n                value: '/interaction/PersonalDoList'\n              });\n            }),\n            class: \"LayoutPersonalDoListText\"\n          }, [_cache[1] || (_cache[1] = _createTextVNode(\" 更多 \")), _createVNode(_component_el_icon, null, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_DArrowRight)];\n            }),\n            _: 1 /* STABLE */\n          })])]), _createVNode(_component_el_scrollbar, {\n            ref: \"scrollRef\",\n            class: \"LayoutPersonalDoListScrollbar\",\n            onScroll: $setup.handleScroll\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableData, function (item) {\n                return _openBlock(), _createElementBlock(\"div\", {\n                  key: item.id,\n                  class: \"LayoutPersonalDoListItem\",\n                  onClick: function onClick($event) {\n                    return $setup.handleDetails(item);\n                  }\n                }, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString(item.moduleName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.format(item.noticeTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_9, _toDisplayString(item.theme), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_5);\n              }), 128 /* KEYED_FRAGMENT */)), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \"加载中...\")) : _createCommentVNode(\"v-if\", true), $setup.isShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, \"没有更多了\")) : _createCommentVNode(\"v-if\", true)])];\n            }),\n            _: 1 /* STABLE */\n          }, 512 /* NEED_PATCH */)])];\n        }),\n        _: 3 /* FORWARDED */\n      })];\n    }),\n    _: 3 /* FORWARDED */\n  }, 8 /* PROPS */, [\"value\"]);\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_el_badge", "value", "$setup", "totals", "default", "_withCtx", "_createVNode", "_component_el_popover", "trigger", "transition", "reference", "_createElementVNode", "_hoisted_1", "_renderSlot", "_ctx", "$slots", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "$event", "openPage", "_createTextVNode", "_component_el_icon", "_component_DArrowRight", "_", "_component_el_scrollbar", "ref", "onScroll", "handleScroll", "_hoisted_4", "_createElementBlock", "_Fragment", "_renderList", "tableData", "item", "id", "handleDetails", "_hoisted_6", "_hoisted_7", "_toDisplayString", "moduleName", "_hoisted_8", "format", "noticeTime", "_hoisted_9", "theme", "_hoisted_5", "loading", "_hoisted_10", "_createCommentVNode", "isShow", "_hoisted_11"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\components\\LayoutPersonalDoList.vue"], "sourcesContent": ["<template>\r\n  <el-badge :value=\"totals\">\r\n    <el-popover trigger=\"hover\" popper-class=\"LayoutPersonalDoListPopover\" transition=\"zy-el-zoom-in-top\">\r\n      <template #reference>\r\n        <div class=\"LayoutPersonalDoList\">\r\n          <slot></slot>\r\n        </div>\r\n      </template>\r\n      <div class=\"LayoutPersonalDoListBody\">\r\n        <div class=\"LayoutPersonalDoListHead\">\r\n          <div class=\"LayoutPersonalDoListName\">个人待办</div>\r\n          <div @click=\"openPage({ key: 'routePath', value: '/interaction/PersonalDoList' })\"\r\n            class=\"LayoutPersonalDoListText\">\r\n            更多\r\n            <el-icon>\r\n              <DArrowRight />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <el-scrollbar ref=\"scrollRef\" class=\"LayoutPersonalDoListScrollbar\" @scroll=\"handleScroll\">\r\n          <div class=\"LayoutPersonalDoListScroll\">\r\n            <div v-for=\"item in tableData\" :key=\"item.id\" class=\"LayoutPersonalDoListItem\" @click=\"handleDetails(item)\">\r\n              <div class=\"LayoutPersonalDoListInfo\">\r\n                <div class=\"LayoutPersonalDoListType\">{{ item.moduleName }}</div>\r\n                <div class=\"LayoutPersonalDoListTime\">{{ format(item.noticeTime) }}</div>\r\n              </div>\r\n              <div class=\"LayoutPersonalDoListTitle\">{{ item.theme }}</div>\r\n            </div>\r\n            <div class=\"LayoutPersonalDoListLoadingText\" v-if=\"loading\">加载中...</div>\r\n            <div class=\"LayoutPersonalDoListLoadingText\" v-if=\"isShow\">没有更多了</div>\r\n          </div>\r\n        </el-scrollbar>\r\n      </div>\r\n    </el-popover>\r\n  </el-badge>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutPersonalDoList' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, inject, watch } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { format } from 'common/js/time.js'\r\nconst store = useStore()\r\nconst openPage = inject('openPage')\r\nconst scrollRef = ref()\r\nconst loadingScroll = ref(false)\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst totals = ref(0)\r\nconst isShow = ref(false)\r\nconst loading = ref(true)\r\nconst tableData = ref([])\r\n\r\nonMounted(() => { personalDoList() })\r\n\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (!scrollRef.value) return\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {\r\n    load()\r\n  }\r\n}\r\nconst load = () => {\r\n  if (pageNo.value * pageSize.value >= totals.value) return\r\n  loadingScroll.value = true\r\n  pageNo.value += 1\r\n  personalDoList()\r\n}\r\nconst personalDoList = async () => {\r\n  const { data, total } = await api.personalDoList({\r\n    pageNo: pageNo.value,\r\n    pageSize: pageSize.value,\r\n    query: { hasComplete: 0 }\r\n  })\r\n  tableData.value = [...tableData.value, ...data]\r\n  totals.value = total\r\n  loading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  loadingScroll.value = false\r\n}\r\nconst handleDetails = (item) => {\r\n  if (!item.redirectUrl && item.businessCode !== 'system') return ElMessage({ type: 'info', message: `当前${item.moduleName || ''}数据没有跳转路径，请维护好跳转路径在进行查看详情！` })\r\n  if (!item.isValidation) return ElMessage({ type: 'info', message: `当前${item.moduleName || ''}数据已被删除！` })\r\n  openPage({ key: 'routePath', value: '/interaction/PersonalDoList' })\r\n  sessionStorage.setItem('personalDoList', JSON.stringify(item || ''))\r\n}\r\nwatch(() => store.state.socket, (val) => {\r\n  if (val) {\r\n    store.state.socket.on('message', (res) => {\r\n      const data = JSON.parse(res)\r\n      if (data.messageType === 'pending') {\r\n        pageNo.value = 1\r\n        pageSize.value = 10\r\n        totals.value = 0\r\n        isShow.value = false\r\n        loading.value = true\r\n        tableData.value = []\r\n        personalDoList()\r\n      }\r\n    })\r\n  }\r\n})\r\nwatch(() => store.state.personalDoRefresh, (val) => {\r\n  if (val) {\r\n    pageNo.value = 1\r\n    pageSize.value = 10\r\n    totals.value = 0\r\n    isShow.value = false\r\n    loading.value = true\r\n    tableData.value = []\r\n    personalDoList()\r\n    store.commit('setPersonalDoRefresh', false)\r\n  }\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutPersonalDoListPopover {\r\n  width: 500px !important;\r\n  padding: 0 !important;\r\n\r\n  .LayoutPersonalDoListHead {\r\n    padding: var(--zy-distance-five) var(--zy-distance-two);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .LayoutPersonalDoListName {\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      color: var(--zy-el-text-color-primary);\r\n    }\r\n\r\n    .LayoutPersonalDoListText {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-regular);\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .LayoutPersonalDoListScrollbar {\r\n    width: 100%;\r\n    max-height: 60vh;\r\n\r\n    .zy-el-scrollbar__wrap {\r\n      max-height: 60vh;\r\n    }\r\n  }\r\n\r\n  .LayoutPersonalDoListScroll {\r\n    padding: var(--zy-distance-five) 0;\r\n\r\n    .LayoutPersonalDoListItem {\r\n      cursor: pointer;\r\n      padding: var(--zy-distance-five) var(--zy-distance-two);\r\n\r\n      .LayoutPersonalDoListInfo {\r\n        display: flex;\r\n        align-items: center;\r\n        position: relative;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 50%;\r\n          right: 0;\r\n          transform: translateY(-50%);\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 50%;\r\n          background: var(--zy-el-color-danger);\r\n        }\r\n\r\n        .LayoutPersonalDoListType {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          border: 1px solid var(--zy-el-color-primary);\r\n          border-radius: var(--el-border-radius-small);\r\n          color: var(--zy-el-color-primary);\r\n          padding: 0 12px;\r\n        }\r\n\r\n        .LayoutPersonalDoListTime {\r\n          margin-left: 20px;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          color: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n\r\n      .LayoutPersonalDoListTitle {\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-primary);\r\n        padding-top: var(--zy-font-name-distance-five);\r\n        text-overflow: -o-ellipsis-lastline;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        display: -webkit-box;\r\n        -webkit-line-clamp: 2;\r\n        line-clamp: 2;\r\n        -webkit-box-orient: vertical;\r\n      }\r\n    }\r\n\r\n    .LayoutPersonalDoListLoadingText {\r\n      text-align: center;\r\n      color: var(--zy-el-text-color-regular);\r\n      padding: var(--zy-distance-three) 0;\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAIaA,KAAK,EAAC;AAAsB;;EAI9BA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA0B;;EAW9BA,KAAK,EAAC;AAA4B;iBApBjD;;EAsBmBA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA0B;;EAChCA,KAAK,EAAC;AAA0B;;EAElCA,KAAK,EAAC;AAA2B;;EA1BpDC,GAAA;EA4BiBD,KAAK,EAAC;;;EA5BvBC,GAAA;EA6BiBD,KAAK,EAAC;;;;;;;;uBA5BrBE,YAAA,CAiCWC,mBAAA;IAjCAC,KAAK,EAAEC,MAAA,CAAAC;EAAM;IAD1BC,OAAA,EAAAC,QAAA,CAEI;MAAA,OA+Ba,CA/BbC,YAAA,CA+BaC,qBAAA;QA/BDC,OAAO,EAAC,OAAO;QAAC,cAAY,EAAC,6BAA6B;QAACC,UAAU,EAAC;;QACrEC,SAAS,EAAAL,QAAA,CAClB;UAAA,OAEM,CAFNM,mBAAA,CAEM,OAFNC,UAEM,GADJC,WAAA,CAAaC,IAAA,CAAAC,MAAA,a;;QALvBX,OAAA,EAAAC,QAAA,CAQM;UAAA,OAwBM,CAxBNM,mBAAA,CAwBM,OAxBNK,UAwBM,GAvBJL,mBAAA,CASM,OATNM,UASM,G,0BARJN,mBAAA,CAAgD;YAA3Cd,KAAK,EAAC;UAA0B,GAAC,MAAI,sBAC1Cc,mBAAA,CAMM;YANAO,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAElB,MAAA,CAAAmB,QAAQ;gBAAAvB,GAAA;gBAAAG,KAAA;cAAA;YAAA;YACnBJ,KAAK,EAAC;wCAZlByB,gBAAA,CAY6C,MAEjC,IAAAhB,YAAA,CAEUiB,kBAAA;YAhBtBnB,OAAA,EAAAC,QAAA,CAec;cAAA,OAAe,CAAfC,YAAA,CAAekB,sBAAA,E;;YAf7BC,CAAA;kBAmBQnB,YAAA,CAYeoB,uBAAA;YAZDC,GAAG,EAAC,WAAW;YAAC9B,KAAK,EAAC,+BAA+B;YAAE+B,QAAM,EAAE1B,MAAA,CAAA2B;;YAnBrFzB,OAAA,EAAAC,QAAA,CAoBU;cAAA,OAUM,CAVNM,mBAAA,CAUM,OAVNmB,UAUM,I,kBATJC,mBAAA,CAMMC,SAAA,QA3BlBC,WAAA,CAqBgC/B,MAAA,CAAAgC,SAAS,EArBzC,UAqBwBC,IAAI;qCAAhBJ,mBAAA,CAMM;kBAN0BjC,GAAG,EAAEqC,IAAI,CAACC,EAAE;kBAAEvC,KAAK,EAAC,0BAA0B;kBAAEqB,OAAK,WAALA,OAAKA,CAAAE,MAAA;oBAAA,OAAElB,MAAA,CAAAmC,aAAa,CAACF,IAAI;kBAAA;oBACvGxB,mBAAA,CAGM,OAHN2B,UAGM,GAFJ3B,mBAAA,CAAiE,OAAjE4B,UAAiE,EAAAC,gBAAA,CAAxBL,IAAI,CAACM,UAAU,kBACxD9B,mBAAA,CAAyE,OAAzE+B,UAAyE,EAAAF,gBAAA,CAAhCtC,MAAA,CAAAyC,MAAM,CAACR,IAAI,CAACS,UAAU,kB,GAEjEjC,mBAAA,CAA6D,OAA7DkC,UAA6D,EAAAL,gBAAA,CAAnBL,IAAI,CAACW,KAAK,iB,iBA1BlEC,UAAA;8CA4B+D7C,MAAA,CAAA8C,OAAO,I,cAA1DjB,mBAAA,CAAwE,OAAxEkB,WAAwE,EAAZ,QAAM,KA5B9EC,mBAAA,gBA6B+DhD,MAAA,CAAAiD,MAAM,I,cAAzDpB,mBAAA,CAAsE,OAAtEqB,WAAsE,EAAX,OAAK,KA7B5EF,mBAAA,e;;YAAAzB,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}