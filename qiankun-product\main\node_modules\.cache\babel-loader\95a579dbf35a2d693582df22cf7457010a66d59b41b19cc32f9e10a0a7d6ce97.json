{"ast": null, "code": "import { computed } from 'vue';\nimport { useStore } from 'vuex';\nvar __default__ = {\n  name: 'NotFoundPage'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var description = computed(function () {\n      return (store.getters.getMenuFn || []).length ? '找不到页面' : '暂未分配菜单权限，请联系管理员分配';\n    });\n    var __returned__ = {\n      store,\n      description,\n      computed,\n      get useStore() {\n        return useStore;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["computed", "useStore", "__default__", "name", "store", "description", "getters", "getMenuFn", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/NotFoundPage/NotFoundPage.vue"], "sourcesContent": ["<template>\r\n  <div class=\"NotFoundPage\">\r\n    <el-empty :description=\"description\"></el-empty>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'NotFoundPage' }\r\n</script>\r\n<script setup>\r\nimport { computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nconst store = useStore()\r\nconst description = computed(() => (store.getters.getMenuFn || []).length ? '找不到页面' : '暂未分配菜单权限，请联系管理员分配')\r\n</script>\r\n<style lang=\"scss\">\r\n.NotFoundPage {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"], "mappings": "AASA,SAASA,QAAQ,QAAQ,KAAK;AAC9B,SAASC,QAAQ,QAAQ,MAAM;AAJ/B,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAe,CAAC;;;;;IAKvC,IAAMC,KAAK,GAAGH,QAAQ,CAAC,CAAC;IACxB,IAAMI,WAAW,GAAGL,QAAQ,CAAC;MAAA,OAAM,CAACI,KAAK,CAACE,OAAO,CAACC,SAAS,IAAI,EAAE,EAAEC,MAAM,GAAG,OAAO,GAAG,mBAAmB;IAAA,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}