{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"barAndPie\",\n    onMouseenter: $setup.lzmouseenter,\n    onMouseleave: $setup.lzmouseleave,\n    ref: \"elChartRef\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "class", "onMouseenter", "$setup", "lzmouseenter", "onMouseleave", "lzmouseleave", "ref"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiUseStatistics\\common\\barAndPie.vue"], "sourcesContent": ["<template>\r\n  <div class=\"barAndPie\" @mouseenter=\"lzmouseenter\" @mouseleave=\"lzmouseleave\" ref=\"elChartRef\"></div>\r\n</template>\r\n<script>\r\nexport default { name: 'barAnd<PERSON>ie' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({\r\n  color: { type: String, default: '' },\r\n  data: { type: Array, default: () => [] },\r\n  showNum: { type: Number, default: 0 }\r\n})\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst option = ref()\r\nconst initChart = () => {\r\n  if (!elChart) {\r\n    elChart = echarts.init(elChartRef.value)\r\n  }\r\n  getSeroptin()\r\n  elChart.setOption(option.value)\r\n  if (props.showNum) {\r\n    chartfnc()\r\n  }\r\n}\r\n\r\nconst getSeroptin = () => {\r\n  const setOption = {\r\n    title: {\r\n      text: '关注趋势分布',\r\n      left: '10',\r\n      top: 0,\r\n      textStyle: {\r\n        color: '#000'\r\n      }\r\n    },\r\n    tooltip: {\r\n      trigger: 'axis',\r\n      show: true,\r\n      axisPointer: {\r\n        type: 'shadow',\r\n        textStyle: {\r\n          color: '#fff'\r\n        }\r\n      }\r\n    },\r\n    legend: {\r\n      // 图例\r\n      orient: 'horizontal', //horizontal 水平显示，vertical 垂直显示\r\n      top: 0,\r\n      right: 10,\r\n      itemGap: 20,\r\n      width: 600, // 单行图例的宽度\r\n      textStyle: {\r\n        color: '#666'\r\n      },\r\n      data: ['对话量', '活跃用户']\r\n    },\r\n    xAxis: {\r\n      type: 'category',\r\n      axisLabel: {\r\n        interval: 0,\r\n        margin: 12,\r\n        fontSize: 14,\r\n        color: '#ccc'\r\n        // formatter: (value) => (value.length > 6 ? `${value.slice(0, 6)}...` : value)\r\n      },\r\n      data: showData.value[0].data.map((v) => v.name)\r\n    },\r\n    yAxis: { type: 'value', splitLine: { show: false } },\r\n    grid: { bottom: '10%', top: 40, x2: 60, x: 60 },\r\n    series: showData.value.map((v) => {\r\n      return {\r\n        name: v.name,\r\n        type: v.type,\r\n        barWidth: 20,\r\n        smooth: true,\r\n        itemStyle: {\r\n          color: v.color\r\n          // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n          //   { offset: 0, color: v.color },\r\n          //   { offset: 1, color: 'rgba(31, 198, 255, 0)' }\r\n          // ])\r\n        },\r\n        label: {\r\n          show: v.type == 'bar' || showData.value.length == 1,\r\n          position: 'top',\r\n          fontSize: 14,\r\n          fontStyle: 'normal',\r\n          color: '#fff'\r\n        },\r\n        data: v.data.map((i) => i.value),\r\n        areaStyle: {\r\n          color: {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 0,\r\n            y2: 1,\r\n            colorStops: [\r\n              {\r\n                offset: 0,\r\n                color: v.color // 0% 处的颜色\r\n              },\r\n              {\r\n                offset: 1,\r\n                color: 'rgba(44, 249, 255, 0)' // 100% 处的颜色\r\n              }\r\n            ],\r\n            global: false // 缺省为 false\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n  option.value = setOption\r\n  console.log('🚀 ~ getSeroptin ~ setOption:', setOption)\r\n}\r\n\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    erd.listenTo(elChartRef.value, (element) => {})\r\n  })\r\n})\r\nconst timer = ref()\r\nconst startIndex = ref(0)\r\nconst chartfnc = () => {\r\n  if (props.showNum == 0) return\r\n  timer.value = setInterval(() => {\r\n    const arr = JSON.parse(JSON.stringify(props.data))\r\n    arr.forEach((v) => {\r\n      v.data = v.data.slice(startIndex.value, props.showNum + startIndex.value)\r\n    })\r\n    showData.value = arr\r\n    getSeroptin()\r\n    startIndex.value++\r\n    elChart.setOption(option.value)\r\n    if (startIndex.value > props.data[0].data.length - props.showNum) {\r\n      startIndex.value = 0\r\n    }\r\n  }, 5000)\r\n}\r\nconst lzmouseleave = () => {\r\n  chartfnc()\r\n}\r\nconst lzmouseenter = () => {\r\n  if (props.showNum == 0) return\r\n  clearInterval(timer.value)\r\n}\r\n\r\nonBeforeUnmount(() => {})\r\nconst showData = ref([])\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    if (props.data && props.data.length) {\r\n      if (props.showNum) {\r\n        const arr = JSON.parse(JSON.stringify(props.data))\r\n        arr.forEach((v) => {\r\n          v.data = v.data.slice(startIndex.value, props.showNum)\r\n        })\r\n        startIndex.value++\r\n        showData.value = arr\r\n      } else {\r\n        showData.value = props.data\r\n      }\r\n      initChart()\r\n    }\r\n  }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.barAndPie {\r\n  width: 100%;\r\n  height: 100%;\r\n  margin: auto;\r\n\r\n  .ColumnChartTooltip {\r\n    min-width: 180px;\r\n\r\n    .ColumnChartName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-primary);\r\n      padding-left: 16px;\r\n      position: relative;\r\n\r\n      span {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #ffffff;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ColumnChartText {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: var(--zy-font-name-distance-five);\r\n\r\n      span + span {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;uBACEA,mBAAA,CAAoG;IAA/FC,KAAK,EAAC,WAAW;IAAEC,YAAU,EAAEC,MAAA,CAAAC,YAAY;IAAGC,YAAU,EAAEF,MAAA,CAAAG,YAAY;IAAEC,GAAG,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}