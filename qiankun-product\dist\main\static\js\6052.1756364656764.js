"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[6052],{36052:function(e,t,n){n.r(t),n.d(t,{default:function(){return H}});var o=n(79590),a=(n(76945),n(88810),n(68294)),r=(n(1920),n(8507),n(62427)),l=(n(98773),n(74061)),i=n(4955),u=n(88609),s=n(98885);n(35894);function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var e,t={},n=Object.prototype,o=n.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},l=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",u=r.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function d(e,t,n,o){var r=t&&t.prototype instanceof h?t:h,l=Object.create(r.prototype),i=new T(o||[]);return a(l,"_invoke",{value:x(e,n,i)}),l}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var v="suspendedStart",m="suspendedYield",g="executing",f="completed",y={};function h(){}function N(){}function V(){}var S={};s(S,l,(function(){return this}));var E=Object.getPrototypeOf,_=E&&E(E(P([])));_&&_!==n&&o.call(_,l)&&(S=_);var L=V.prototype=h.prototype=Object.create(S);function C(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function n(a,r,l,i){var u=p(e[a],e,r);if("throw"!==u.type){var s=u.arg,c=s.value;return c&&"object"==typeof c&&o.call(c,"__await")?t.resolve(c.__await).then((function(e){n("next",e,l,i)}),(function(e){n("throw",e,l,i)})):t.resolve(c).then((function(e){s.value=e,l(s)}),(function(e){return n("throw",e,l,i)}))}i(u.arg)}var r;a(this,"_invoke",{value:function(e,o){function a(){return new t((function(t,a){n(e,o,t,a)}))}return r=r?r.then(a,a):a()}})}function x(t,n,o){var a=v;return function(r,l){if(a===g)throw Error("Generator is already running");if(a===f){if("throw"===r)throw l;return{value:e,done:!0}}for(o.method=r,o.arg=l;;){var i=o.delegate;if(i){var u=b(i,o);if(u){if(u===y)continue;return u}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(a===v)throw a=f,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);a=g;var s=p(t,n,o);if("normal"===s.type){if(a=o.done?f:m,s.arg===y)continue;return{value:s.arg,done:o.done}}"throw"===s.type&&(a=f,o.method="throw",o.arg=s.arg)}}}function b(t,n){var o=n.method,a=t.iterator[o];if(a===e)return n.delegate=null,"throw"===o&&t.iterator.return&&(n.method="return",n.arg=e,b(t,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),y;var r=p(a,t.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,y;var l=r.arg;return l?l.done?(n[t.resultName]=l.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):l:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[l];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,r=function n(){for(;++a<t.length;)if(o.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return r.next=r}}throw new TypeError(typeof t+" is not iterable")}return N.prototype=V,a(L,"constructor",{value:V,configurable:!0}),a(V,"constructor",{value:N,configurable:!0}),N.displayName=s(V,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===N||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,V):(e.__proto__=V,s(e,u,"GeneratorFunction")),e.prototype=Object.create(L),e},t.awrap=function(e){return{__await:e}},C(k.prototype),s(k.prototype,i,(function(){return this})),t.AsyncIterator=k,t.async=function(e,n,o,a,r){void 0===r&&(r=Promise);var l=new k(d(e,n,o,a),r);return t.isGeneratorFunction(n)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},C(L),s(L,u,"Generator"),s(L,l,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=P,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function a(o,a){return i.type="throw",i.arg=t,n.next=o,a&&(n.method="next",n.arg=e),!!a}for(var r=this.tryEntries.length-1;r>=0;--r){var l=this.tryEntries[r],i=l.completion;if("root"===l.tryLoc)return a("end");if(l.tryLoc<=this.prev){var u=o.call(l,"catchLoc"),s=o.call(l,"finallyLoc");if(u&&s){if(this.prev<l.catchLoc)return a(l.catchLoc,!0);if(this.prev<l.finallyLoc)return a(l.finallyLoc)}else if(u){if(this.prev<l.catchLoc)return a(l.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return a(l.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var r=a;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var l=r?r.completion:{};return l.type=e,l.arg=t,r?(this.method="next",this.next=r.finallyLoc,y):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),D(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;D(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,o){return this.delegate={iterator:P(t),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=e),y}},t}function d(e,t,n,o,a,r,l){try{var i=e[r](l),u=i.value}catch(e){return void n(e)}i.done?t(u):Promise.resolve(u).then(o,a)}function p(e){return function(){var t=this,n=arguments;return new Promise((function(o,a){var r=e.apply(t,n);function l(e){d(r,o,a,l,i,"next",e)}function i(e){d(r,o,a,l,i,"throw",e)}l(void 0)}))}}var v={key:0,class:"content"},m={class:"suggestPopContentHeader"},g={key:0,class:"suggestPopContentChooseRole"},f={class:"suggestPopContentBody"},y={class:"mb20"},h={class:"hasColorBox"},N={class:"nocolorSpan"},V={class:"nocolorSpan"},S={class:"nocolorSpan"},E={class:"mb20"},_={class:"mb20"},L={class:"hasColorBox"},C={class:"nocolorSpan"},k={class:"nocolorSpan"},x={class:"nocolorSpan"},b={class:"mb20"},w={class:"hasColorBox"},D={class:"nocolorSpan"},T={class:"nocolorSpan"},P={class:"nocolorSpan"},A={class:"nocolorSpan"},B={class:"mb20"},j={class:"nocolorSpan"},I={class:"nocolorSpan"},F={name:"suggestPop"},O=Object.assign(F,{props:{isVisible:{type:Boolean,default:!1},routePth:{type:String,default:""}},setup(e){var t=(0,l.computed)((function(){var e;return(null===(e=u.FR.value)||void 0===e?void 0:e.systemPlatform)||""})),n=(0,l.inject)("openPage"),d=function(){var e=(new Date).getHours();return e<12?"早上好":e<18?"下午好":"晚上好"},F=(0,l.ref)(!1),O=(0,l.ref)(!1),R=function(){O.value?O.value=!O.value:setTimeout((function(){O.value=!O.value}),300),F.value=!F.value},H=(0,l.ref)({}),G=(0,l.ref)("CPPCC"==t.value?["proposal_committee","suggestion_office_user","cppcc_member"]:["npc_contact_committee","suggestion_office_user","delegation_manager","npc_member"]),J=(0,l.ref)(""),M=(0,l.ref)([]),U=(0,l.ref)([]),z=(0,l.ref)(!1),Y=function(){var e=p(c().mark((function e(){var n,o;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,i.A.globalReadOpenConfig({codes:["suggestion_enable_pre_assign","proposal_enable_pre_assign"]});case 2:n=e.sent,o=n.data,o.suggestion_enable_pre_assign&&"CPPCC"!=t.value&&(z.value="true"==o.suggestion_enable_pre_assign),o.proposal_enable_pre_assign&&"CPPCC"==t.value&&(z.value="true"==o.proposal_enable_pre_assign);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();(0,l.onMounted)((function(){Y(),H.value=JSON.parse(sessionStorage.getItem("user")),U.value=H.value.specialRoleKeys.filter((function(e){return G.value.includes(e)})),U.value.includes("npc_contact_committee")&&M.value.push({value:"npc_contact_committee",label:"联工委",param:"remind_admin"}),U.value.includes("proposal_committee")&&M.value.push({value:"proposal_committee",label:"提案委",param:"remind_admin"}),U.value.includes("suggestion_office_user")&&M.value.push({value:"suggestion_office_user",label:"办理单位",param:"remind_office"}),U.value.includes("delegation_manager")&&M.value.push({value:"delegation_manager",label:"代表团管理员",param:"remind_delegation"}),U.value.includes("npc_member")&&M.value.push({value:"npc_member",label:"人大代表",param:"remind_npc_member"}),U.value.includes("cppcc_member")&&M.value.push({value:"cppcc_member",label:"政协委员",param:"remind_member"}),J.value=M.value[0].value,q(),setTimeout((function(){F.value=!0,setTimeout((function(){O.value=!0}),100)}),300)}));var $=(0,l.ref)({}),K=(0,l.ref)(!0),q=function(){var e=p(c().mark((function e(){var n,o;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n="CPPCC"===t.value?"/proposalStatistics/composite":"/suggestionStatistics/composite",e.next=3,i.A.globalJson(n,{countView:M.value.filter((function(e){return e.value===J.value}))[0].param});case 3:o=e.sent,$.value=o.data.tableData.length?o.data.tableData[0]:{},K.value=!1;case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Q=function(){K.value=!0,q()},W=function(e,o){var a,r,l,i=(null===(a=$.value[o])||void 0===a?void 0:a.amount)||0;if(0!==i){var u=(null===(r=$.value[o])||void 0===r?void 0:r.suggestionIds)||(null===(l=$.value[o])||void 0===l?void 0:l.proposalIds)||[];console.log(u),u.length?sessionStorage.setItem("suggestIds",JSON.stringify(u)):sessionStorage.removeItem("suggestIds");var c="CPPCC"==t.value?"proposal":"suggest";n({key:"routePath",value:`/${c}/`+e})}else(0,s.nk)({type:"info",message:`暂无${"draftsList"===o?"草稿":"相关"}数据`})};return function(n,i){var u,s,c,p,G,Y,X,Z,ee,te,ne,oe,ae,re,le,ie,ue,se,ce,de,pe,ve,me,ge,fe,ye,he,Ne,Ve,Se,Ee,_e,Le,Ce,ke,xe,be,we,De,Te,Pe,Ae,Be,je,Ie,Fe,Oe,Re,He=(0,l.resolveComponent)("RefreshRight"),Ge=r.tk,Je=(0,l.resolveComponent)("Close"),Me=(0,l.resolveComponent)("More"),Ue=a.P9,ze=a.AV,Ye=o.L;return(0,l.withDirectives)(((0,l.openBlock)(),(0,l.createElementBlock)("div",{class:(0,l.normalizeClass)(["suggestPop",{show:e.isVisible||F.value}])},[(0,l.createElementVNode)("div",{class:(0,l.normalizeClass)(["suggestPopHead",{showHead:F.value}])},[F.value?((0,l.openBlock)(),(0,l.createBlock)(Ge,{key:0},{default:(0,l.withCtx)((function(){return[(0,l.createVNode)(He,{onClick:Q})]})),_:1})):(0,l.createCommentVNode)("",!0),F.value?((0,l.openBlock)(),(0,l.createBlock)(Ge,{key:1,onClick:R},{default:(0,l.withCtx)((function(){return[(0,l.createVNode)(Je)]})),_:1})):((0,l.openBlock)(),(0,l.createBlock)(Ge,{key:2,onClick:R},{default:(0,l.withCtx)((function(){return[(0,l.createVNode)(Me)]})),_:1}))],2),O.value?((0,l.openBlock)(),(0,l.createElementBlock)("div",v,[(0,l.createElementVNode)("div",m,(0,l.toDisplayString)(H.value.userName)+"，"+(0,l.toDisplayString)(d()),1),U.value.length>1?((0,l.openBlock)(),(0,l.createElementBlock)("div",g,[i[37]||(i[37]=(0,l.createElementVNode)("div",null,"选择您的身份以查看更多待办",-1)),(0,l.createVNode)(ze,{modelValue:J.value,"onUpdate:modelValue":i[0]||(i[0]=function(e){return J.value=e}),size:"small",style:{width:"120px"},onChange:q},{default:(0,l.withCtx)((function(){return[((0,l.openBlock)(!0),(0,l.createElementBlock)(l.Fragment,null,(0,l.renderList)(M.value,(function(e){return(0,l.openBlock)(),(0,l.createBlock)(Ue,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])])):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("div",f,[(0,l.createElementVNode)("div",null,(0,l.toDisplayString)($.value.termYear),1),"npc_contact_committee"==J.value?((0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,{key:0},[(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[1]||(i[1]=function(e){return W("AllSuggest","meetList")})},(0,l.toDisplayString)((null===(u=$.value.meetList)||void 0===u?void 0:u.amount)||0),1),i[38]||(i[38]=(0,l.createTextVNode)(" 件大会建议， ")),(0,l.createElementVNode)("span",{onClick:i[2]||(i[2]=function(e){return W("AllSuggest","usualList")})},(0,l.toDisplayString)((null===(s=$.value.usualList)||void 0===s?void 0:s.amount)||0),1),i[39]||(i[39]=(0,l.createTextVNode)(" 件闭会建议， ")),(0,l.createElementVNode)("span",{onClick:i[3]||(i[3]=function(e){return W("SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议","importantList")})},(0,l.toDisplayString)((null===(c=$.value.importantList)||void 0===c?void 0:c.amount)||0),1),i[40]||(i[40]=(0,l.createTextVNode)(" 件重点督办建议 "))]),(0,l.createElementVNode)("div",y,[(0,l.createElementVNode)("span",{onClick:i[4]||(i[4]=function(e){return W("SuggestReview?tableId=id_sgsn_suggestion_prepareVerify&moduleName=待审查建议","auditList")})},(0,l.toDisplayString)((null===(p=$.value.auditList)||void 0===p?void 0:p.amount)||0),1),i[42]||(i[42]=(0,l.createTextVNode)(" 件待审查， ")),z.value?((0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,{key:0},[(0,l.createElementVNode)("span",{onClick:i[5]||(i[5]=function(e){return W("SuggestAdvanceAssign?tableId=id_sgsn_suggestion_preAssignSuggestion&moduleName=预交办","preAssignList")})},(0,l.toDisplayString)((null===(G=$.value.preAssignList)||void 0===G?void 0:G.amount)||0),1),i[41]||(i[41]=(0,l.createTextVNode)(" 件预交办， "))],64)):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("span",{onClick:i[6]||(i[6]=function(e){return W("SuggestAssign?tableId=id_sgsn_suggestion_prepareSubmitHandle&moduleName=人大交办中","prepareSubmitHandleList")})},(0,l.toDisplayString)((null===(Y=$.value.prepareSubmitHandleList)||void 0===Y?void 0:Y.amount)||0),1),i[43]||(i[43]=(0,l.createTextVNode)(" 件人大交办中 "))]),(0,l.createElementVNode)("div",h,[(0,l.createElementVNode)("span",{onClick:i[7]||(i[7]=function(e){return W("SuggestTransact","handleList")})},(0,l.toDisplayString)((null===(X=$.value.handleList)||void 0===X?void 0:X.amount)||0),1),i[44]||(i[44]=(0,l.createTextVNode)(" 件办理中，其中 ")),i[45]||(i[45]=(0,l.createElementVNode)("span",{class:"red"},null,-1)),(0,l.createElementVNode)("span",N,(0,l.toDisplayString)((null===(Z=$.value.redAnswerDate)||void 0===Z?void 0:Z.amount)||0),1),i[46]||(i[46]=(0,l.createTextVNode)(" 件， ")),i[47]||(i[47]=(0,l.createElementVNode)("span",{class:"yellow"},null,-1)),(0,l.createElementVNode)("span",V,(0,l.toDisplayString)((null===(ee=$.value.yellowAnswerDate)||void 0===ee?void 0:ee.amount)||0),1),i[48]||(i[48]=(0,l.createTextVNode)(" 件， ")),i[49]||(i[49]=(0,l.createElementVNode)("span",{class:"green"},null,-1)),(0,l.createElementVNode)("span",S,(0,l.toDisplayString)((null===(te=$.value.greenAnswerDate)||void 0===te?void 0:te.amount)||0),1),i[50]||(i[50]=(0,l.createTextVNode)(" 件 "))]),(0,l.createElementVNode)("div",E,[(0,l.createElementVNode)("span",{onClick:i[8]||(i[8]=function(e){return W("SuggestApplyForAdjust","adjustList")})},(0,l.toDisplayString)((null===(ne=$.value.adjustList)||void 0===ne?void 0:ne.amount)||0),1),i[51]||(i[51]=(0,l.createTextVNode)(" 件调整申请待审核， ")),(0,l.createElementVNode)("span",{onClick:i[9]||(i[9]=function(e){return W("SuggestApplyForPostpone","delayList")})},(0,l.toDisplayString)((null===(oe=$.value.delayList)||void 0===oe?void 0:oe.amount)||0),1),i[52]||(i[52]=(0,l.createTextVNode)(" 件延期申请待审核 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[10]||(i[10]=function(e){return W("SuggestReply","answerList")})},(0,l.toDisplayString)((null===(ae=$.value.answerList)||void 0===ae?void 0:ae.amount)||0),1),i[53]||(i[53]=(0,l.createTextVNode)(" 件已答复 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[11]||(i[11]=function(e){return W("AllSuggest","satisfactionList")})},(0,l.toDisplayString)((null===(re=$.value.satisfactionList)||void 0===re?void 0:re.amount)||0),1),i[54]||(i[54]=(0,l.createTextVNode)(" 件已答复待代表满意度测评 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[12]||(i[12]=function(e){return W("SuggestConclude","finishList")})},(0,l.toDisplayString)((null===(le=$.value.finishList)||void 0===le?void 0:le.amount)||0),1),i[55]||(i[55]=(0,l.createTextVNode)(" 件已办结 "))])],64)):(0,l.createCommentVNode)("",!0),"proposal_committee"===J.value?((0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,{key:1},[(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[13]||(i[13]=function(e){return W("AllSuggest","meetList")})},(0,l.toDisplayString)((null===(ie=$.value.meetList)||void 0===ie?void 0:ie.amount)||0),1),i[56]||(i[56]=(0,l.createTextVNode)(" 件大会提案， ")),(0,l.createElementVNode)("span",{onClick:i[14]||(i[14]=function(e){return W("AllSuggest","usualList")})},(0,l.toDisplayString)((null===(ue=$.value.usualList)||void 0===ue?void 0:ue.amount)||0),1),i[57]||(i[57]=(0,l.createTextVNode)(" 件闭会提案， ")),(0,l.createElementVNode)("span",{onClick:i[15]||(i[15]=function(e){return W("SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案","importantList")})},(0,l.toDisplayString)((null===(se=$.value.importantList)||void 0===se?void 0:se.amount)||0),1),i[58]||(i[58]=(0,l.createTextVNode)(" 件重点督办提案 "))]),(0,l.createElementVNode)("div",_,[(0,l.createElementVNode)("span",{onClick:i[16]||(i[16]=function(e){return W("SuggestReview?tableId=id_prop_proposal_prepareVerify&moduleName=待审查提案","auditList")})},(0,l.toDisplayString)((null===(ce=$.value.auditList)||void 0===ce?void 0:ce.amount)||0),1),i[60]||(i[60]=(0,l.createTextVNode)(" 件待审查， ")),z.value?((0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,{key:0},[(0,l.createElementVNode)("span",{onClick:i[17]||(i[17]=function(e){return W("SuggestAdvanceAssign?tableId=id_prop_proposal_preAssignPropoasl&moduleName=预交办","preAssignList")})},(0,l.toDisplayString)((null===(de=$.value.preAssignList)||void 0===de?void 0:de.amount)||0),1),i[59]||(i[59]=(0,l.createTextVNode)(" 件预交办， "))],64)):(0,l.createCommentVNode)("",!0),(0,l.createElementVNode)("span",{onClick:i[18]||(i[18]=function(e){return W("SuggestAssign?tableId=id_prop_proposal_prepareSubmitHandle&moduleName=政协交办中","prepareSubmitHandleList")})},(0,l.toDisplayString)((null===(pe=$.value.prepareSubmitHandleList)||void 0===pe?void 0:pe.amount)||0),1),i[61]||(i[61]=(0,l.createTextVNode)(" 件政协交办中 "))]),(0,l.createElementVNode)("div",L,[(0,l.createElementVNode)("span",{onClick:i[19]||(i[19]=function(e){return W("SuggestTransact","handleList")})},(0,l.toDisplayString)((null===(ve=$.value.handleList)||void 0===ve?void 0:ve.amount)||0),1),i[62]||(i[62]=(0,l.createTextVNode)(" 件办理中，其中 ")),i[63]||(i[63]=(0,l.createElementVNode)("span",{class:"red"},null,-1)),(0,l.createElementVNode)("span",C,(0,l.toDisplayString)((null===(me=$.value.redAnswerDate)||void 0===me?void 0:me.amount)||0),1),i[64]||(i[64]=(0,l.createTextVNode)(" 件， ")),i[65]||(i[65]=(0,l.createElementVNode)("span",{class:"yellow"},null,-1)),(0,l.createElementVNode)("span",k,(0,l.toDisplayString)((null===(ge=$.value.yellowAnswerDate)||void 0===ge?void 0:ge.amount)||0),1),i[66]||(i[66]=(0,l.createTextVNode)(" 件， ")),i[67]||(i[67]=(0,l.createElementVNode)("span",{class:"green"},null,-1)),(0,l.createElementVNode)("span",x,(0,l.toDisplayString)((null===(fe=$.value.greenAnswerDate)||void 0===fe?void 0:fe.amount)||0),1),i[68]||(i[68]=(0,l.createTextVNode)(" 件 "))]),(0,l.createElementVNode)("div",b,[(0,l.createElementVNode)("span",{onClick:i[20]||(i[20]=function(e){return W("SuggestApplyForAdjust","adjustList")})},(0,l.toDisplayString)((null===(ye=$.value.adjustList)||void 0===ye?void 0:ye.amount)||0),1),i[69]||(i[69]=(0,l.createTextVNode)(" 件调整申请待审核， ")),(0,l.createElementVNode)("span",{onClick:i[21]||(i[21]=function(e){return W("SuggestApplyForPostpone","delayList")})},(0,l.toDisplayString)((null===(he=$.value.delayList)||void 0===he?void 0:he.amount)||0),1),i[70]||(i[70]=(0,l.createTextVNode)(" 件延期申请待审核 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[22]||(i[22]=function(e){return W("SuggestReply","answerList")})},(0,l.toDisplayString)((null===(Ne=$.value.answerList)||void 0===Ne?void 0:Ne.amount)||0),1),i[71]||(i[71]=(0,l.createTextVNode)(" 件已答复 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[23]||(i[23]=function(e){return W("SuggestSatisfaction","satisfactionList")})},(0,l.toDisplayString)((null===(Ve=$.value.satisfactionList)||void 0===Ve?void 0:Ve.amount)||0),1),i[72]||(i[72]=(0,l.createTextVNode)(" 件已答复待委员满意度测评 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[24]||(i[24]=function(e){return W("SuggestConclude","finishList")})},(0,l.toDisplayString)((null===(Se=$.value.finishList)||void 0===Se?void 0:Se.amount)||0),1),i[73]||(i[73]=(0,l.createTextVNode)(" 件已办结 "))])],64)):(0,l.createCommentVNode)("",!0),"suggestion_office_user"==J.value?((0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,{key:2},[(0,l.createElementVNode)("div",null,[i[74]||(i[74]=(0,l.createTextVNode)(" 共 ")),(0,l.createElementVNode)("span",{onClick:i[25]||(i[25]=function(e){return W("UnitSuggestTransact","handleList")})},(0,l.toDisplayString)((null===(Ee=$.value.handleList)||void 0===Ee?void 0:Ee.amount)||0),1),i[75]||(i[75]=(0,l.createTextVNode)(" 件办理中， ")),(0,l.createElementVNode)("span",{onClick:i[26]||(i[26]=function(e){return W("CPPCC"==t.value?"SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案":"SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议","importantList")})},(0,l.toDisplayString)((null===(_e=$.value.importantList)||void 0===_e?void 0:_e.amount)||0),1),(0,l.createTextVNode)(" 件重点督办"+(0,l.toDisplayString)("CPPCC"==t.value?"提案":"建议"),1)]),(0,l.createElementVNode)("div",w,[i[76]||(i[76]=(0,l.createTextVNode)(" 其中 ")),i[77]||(i[77]=(0,l.createElementVNode)("span",{class:"red"},null,-1)),(0,l.createElementVNode)("span",D,(0,l.toDisplayString)((null===(Le=$.value.redAnswerDate)||void 0===Le?void 0:Le.amount)||0),1),i[78]||(i[78]=(0,l.createTextVNode)(" 件， ")),i[79]||(i[79]=(0,l.createElementVNode)("span",{class:"yellow"},null,-1)),(0,l.createElementVNode)("span",T,(0,l.toDisplayString)((null===(Ce=$.value.yellowAnswerDate)||void 0===Ce?void 0:Ce.amount)||0),1),i[80]||(i[80]=(0,l.createTextVNode)(" 件， ")),i[81]||(i[81]=(0,l.createElementVNode)("span",{class:"green"},null,-1)),(0,l.createElementVNode)("span",P,(0,l.toDisplayString)((null===(ke=$.value.greenAnswerDate)||void 0===ke?void 0:ke.amount)||0),1),i[82]||(i[82]=(0,l.createTextVNode)(" 件 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",A,(0,l.toDisplayString)((null===(xe=$.value.adjustList)||void 0===xe?void 0:xe.amount)||0),1),i[83]||(i[83]=(0,l.createTextVNode)(" 件调整申请待审核， "))]),(0,l.createElementVNode)("div",B,[(0,l.createElementVNode)("span",j,(0,l.toDisplayString)((null===(be=$.value.delayList)||void 0===be?void 0:be.amount)||0),1),i[84]||(i[84]=(0,l.createTextVNode)(" 件申请延期待审核， "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[27]||(i[27]=function(e){return W("UnitSuggestReply","answerList")})},(0,l.toDisplayString)((null===(we=$.value.answerList)||void 0===we?void 0:we.amount)||0),1),i[85]||(i[85]=(0,l.createTextVNode)(" 件已答复 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[28]||(i[28]=function(e){return W("UnitSuggestConclude","finishList")})},(0,l.toDisplayString)((null===(De=$.value.finishList)||void 0===De?void 0:De.amount)||0),1),i[86]||(i[86]=(0,l.createTextVNode)(" 件已办结 "))])],64)):(0,l.createCommentVNode)("",!0),"delegation_manager"==J.value?((0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,{key:3},[(0,l.createElementVNode)("div",null,[i[87]||(i[87]=(0,l.createTextVNode)(" 本代表团 ")),(0,l.createElementVNode)("span",{onClick:i[29]||(i[29]=function(e){return W("AllSuggest","memberList")})},(0,l.toDisplayString)((null===(Te=$.value.memberList)||void 0===Te?void 0:Te.amount)||0),1),i[88]||(i[88]=(0,l.createTextVNode)(" 件代表建议， ")),(0,l.createElementVNode)("span",{onClick:i[30]||(i[30]=function(e){return W("AllSuggest","teamList")})},(0,l.toDisplayString)((null===(Pe=$.value.teamList)||void 0===Pe?void 0:Pe.amount)||0),1),i[89]||(i[89]=(0,l.createTextVNode)(" 件全团建议 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[31]||(i[31]=function(e){return W("SuggestReview?tableId=id_sgsn_suggestion_base_delegation_verify&moduleName=代表团审查&nextNode=prepareVerify","delegationAuditList")})},(0,l.toDisplayString)((null===(Ae=$.value.delegationAuditList)||void 0===Ae?void 0:Ae.amount)||0),1),i[90]||(i[90]=(0,l.createTextVNode)(" 件待代表团审查建议 "))])],64)):(0,l.createCommentVNode)("",!0),"npc_member"==J.value||"cppcc_member"==J.value?((0,l.openBlock)(),(0,l.createElementBlock)(l.Fragment,{key:4},[(0,l.createElementVNode)("div",null,[i[91]||(i[91]=(0,l.createTextVNode)(" 您已提交 ")),(0,l.createElementVNode)("span",{onClick:i[32]||(i[32]=function(e){return W("MyLedSuggest","normalList")})},(0,l.toDisplayString)((null===(Be=$.value.normalList)||void 0===Be?void 0:Be.amount)||0),1),(0,l.createTextVNode)(" 件"+(0,l.toDisplayString)("CPPCC"==t.value?"提案":"建议")+"， ",1),(0,l.createElementVNode)("span",I,(0,l.toDisplayString)((null===(je=$.value.importantList)||void 0===je?void 0:je.amount)||0),1),(0,l.createTextVNode)(" 件形成重点督办"+(0,l.toDisplayString)("CPPCC"==t.value?"提案":"建议"),1)]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[33]||(i[33]=function(e){return W("MyJointSuggest","needJoinList")})},(0,l.toDisplayString)((null===(Ie=$.value.needJoinList)||void 0===Ie?void 0:Ie.amount)||0),1),(0,l.createTextVNode)(" 件需要确认是否"+(0,l.toDisplayString)("CPPCC"==t.value?"联名":"附议")+"， ",1),(0,l.createElementVNode)("span",{onClick:i[34]||(i[34]=function(e){return W("MyLedSuggest","backList")})},(0,l.toDisplayString)((null===(Fe=$.value.backList)||void 0===Fe?void 0:Fe.amount)||0),1),i[92]||(i[92]=(0,l.createTextVNode)(" 件被退回， ")),(0,l.createElementVNode)("span",{onClick:i[35]||(i[35]=function(e){return W((t.value,"SuggestDraftBox?nextNode=prepareVerify"),"draftsList")})},(0,l.toDisplayString)((null===(Oe=$.value.draftsList)||void 0===Oe?void 0:Oe.amount)||0),1),i[93]||(i[93]=(0,l.createTextVNode)(" 件在草稿箱 "))]),(0,l.createElementVNode)("div",null,[(0,l.createElementVNode)("span",{onClick:i[36]||(i[36]=function(e){return W("MyLedSuggest","satisfactionList")})},(0,l.toDisplayString)((null===(Re=$.value.satisfactionList)||void 0===Re?void 0:Re.amount)||0),1),i[94]||(i[94]=(0,l.createTextVNode)(" 件待满意度测评 "))])],64)):(0,l.createCommentVNode)("",!0)])])):(0,l.createCommentVNode)("",!0)],2)),[[Ye,K.value]])}}});const R=O;var H=R}}]);