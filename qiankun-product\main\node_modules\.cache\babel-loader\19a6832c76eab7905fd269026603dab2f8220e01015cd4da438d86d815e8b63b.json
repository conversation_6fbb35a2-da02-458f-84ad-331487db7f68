{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = [\"lement-loading-text\"];\nvar _hoisted_2 = {\n  class: \"IntelligentErrorCorrectionHead\"\n};\nvar _hoisted_3 = {\n  class: \"IntelligentErrorCorrectionButton\"\n};\nvar _hoisted_4 = {\n  class: \"IntelligentErrorCorrectionButtonItem\"\n};\nvar _hoisted_5 = {\n  class: \"IntelligentErrorCorrectionButtonItem\"\n};\nvar _hoisted_6 = {\n  class: \"IntelligentErrorCorrectionButton\"\n};\nvar _hoisted_7 = {\n  class: \"IntelligentErrorCorrectionButtonItem\"\n};\nvar _hoisted_8 = {\n  class: \"IntelligentErrorCorrectionButtonItem\"\n};\nvar _hoisted_9 = {\n  class: \"IntelligentErrorCorrectionBody\"\n};\nvar _hoisted_10 = {\n  class: \"IntelligentErrorCorrectionBodyLeft\"\n};\nvar _hoisted_11 = {\n  class: \"IntelligentErrorCorrectionBodyRight\"\n};\nvar _hoisted_12 = {\n  class: \"globalTable\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"IntelligentErrorCorrection\",\n    \"element-loading-spinner\": $setup.svg,\n    \"lement-loading-text\": $setup.loadingText,\n    \"element-loading-svg-view-box\": \"-10, -10, 50, 50\"\n  }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleImport\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"文档导入\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_button, {\n    type: \"primary\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"去一键排版\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleExportWord\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"导出\")]);\n    }),\n    _: 1 /* STABLE */\n  })])]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleWrongWord\n  }, {\n    default: _withCtx(function () {\n      return _cache[4] || (_cache[4] = [_createTextVNode(\"智能纠错\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleAllReplace\n  }, {\n    default: _withCtx(function () {\n      return _cache[5] || (_cache[5] = [_createTextVNode(\"全部替换\")]);\n    }),\n    _: 1 /* STABLE */\n  })])])]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_TinyMceEditor, {\n    ref: \"wordRef\",\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.content = $event;\n    }),\n    setting: $setup.setting,\n    content_style: $setup.content_style\n  }, null, 8 /* PROPS */, [\"modelValue\", \"setting\", \"content_style\"])]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_table, {\n    data: $setup.checklist\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        label: \"错误类型\",\n        \"min-width\": \"120\",\n        prop: \"type.name\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"错误内容\",\n        \"min-width\": \"120\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_link, {\n            onClick: function onClick($event) {\n              return $setup.handleDetails(scope.row);\n            },\n            disabled: $setup.arrId.includes(scope.row.id),\n            type: \"primary\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString(scope.row.word), 1 /* TEXT */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"修改建议\",\n        \"min-width\": \"120\"\n      }, {\n        default: _withCtx(function (scope) {\n          var _scope$row, _scope$row2;\n          return [_createTextVNode(_toDisplayString(((_scope$row = scope.row) === null || _scope$row === void 0 ? void 0 : _scope$row.suggest[0]) || ((_scope$row2 = scope.row) === null || _scope$row2 === void 0 ? void 0 : _scope$row2.explanation)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"100\",\n        fixed: \"right\",\n        \"class-name\": \"globalTableCustom\"\n      }, {\n        default: _withCtx(function (scope) {\n          var _scope$row3;\n          return [_createVNode(_component_el_button, {\n            onClick: function onClick($event) {\n              return $setup.handleReplace(scope.row);\n            },\n            disabled: $setup.arrId.includes(scope.row.id) || !((_scope$row3 = scope.row) !== null && _scope$row3 !== void 0 && (_scope$row3 = _scope$row3.suggest) !== null && _scope$row3 !== void 0 && _scope$row3.length),\n            type: \"primary\",\n            plain: \"\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[6] || (_cache[6] = [_createTextVNode(\" 替换 \")]);\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"])])])])], 8 /* PROPS */, _hoisted_1)), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "$setup", "svg", "loadingText", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_button", "type", "onClick", "handleImport", "default", "_withCtx", "_cache", "_createTextVNode", "_", "_hoisted_5", "handleExportWord", "_hoisted_6", "_hoisted_7", "handleWrongWord", "_hoisted_8", "handleAllReplace", "_hoisted_9", "_hoisted_10", "_component_TinyMceEditor", "ref", "modelValue", "content", "$event", "setting", "content_style", "_hoisted_11", "_hoisted_12", "_component_el_table", "data", "checklist", "_component_el_table_column", "label", "prop", "scope", "_component_el_link", "handleDetails", "row", "disabled", "arrId", "includes", "id", "_toDisplayString", "word", "_scope$row", "_scope$row2", "suggest", "explanation", "width", "fixed", "_scope$row3", "handleReplace", "length", "plain", "_hoisted_1", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBoxFunction\\IntelligentErrorCorrection\\IntelligentErrorCorrection.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"IntelligentErrorCorrection\"\r\n    v-loading=\"loading\"\r\n    :element-loading-spinner=\"svg\"\r\n    :lement-loading-text=\"loadingText\"\r\n    element-loading-svg-view-box=\"-10, -10, 50, 50\">\r\n    <div class=\"IntelligentErrorCorrectionHead\">\r\n      <div class=\"IntelligentErrorCorrectionButton\">\r\n        <div class=\"IntelligentErrorCorrectionButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleImport\">文档导入</el-button>\r\n        </div>\r\n        <div class=\"IntelligentErrorCorrectionButtonItem\">\r\n          <el-button type=\"primary\">去一键排版</el-button>\r\n          <el-button type=\"primary\" @click=\"handleExportWord\">导出</el-button>\r\n        </div>\r\n      </div>\r\n      <div class=\"IntelligentErrorCorrectionButton\">\r\n        <div class=\"IntelligentErrorCorrectionButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleWrongWord\">智能纠错</el-button>\r\n        </div>\r\n        <div class=\"IntelligentErrorCorrectionButtonItem\">\r\n          <el-button type=\"primary\" @click=\"handleAllReplace\">全部替换</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"IntelligentErrorCorrectionBody\">\r\n      <div class=\"IntelligentErrorCorrectionBodyLeft\">\r\n        <TinyMceEditor ref=\"wordRef\" v-model=\"content\" :setting=\"setting\" :content_style=\"content_style\" />\r\n      </div>\r\n      <div class=\"IntelligentErrorCorrectionBodyRight\">\r\n        <div class=\"globalTable\">\r\n          <el-table :data=\"checklist\">\r\n            <el-table-column label=\"错误类型\" min-width=\"120\" prop=\"type.name\" />\r\n            <el-table-column label=\"错误内容\" min-width=\"120\">\r\n              <template #default=\"scope\">\r\n                <el-link @click=\"handleDetails(scope.row)\" :disabled=\"arrId.includes(scope.row.id)\" type=\"primary\">\r\n                  {{ scope.row.word }}\r\n                </el-link>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"修改建议\" min-width=\"120\">\r\n              <template #default=\"scope\">{{ scope.row?.suggest[0] || scope.row?.explanation }}</template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"100\" fixed=\"right\" class-name=\"globalTableCustom\">\r\n              <template #default=\"scope\">\r\n                <el-button\r\n                  @click=\"handleReplace(scope.row)\"\r\n                  :disabled=\"arrId.includes(scope.row.id) || !scope.row?.suggest?.length\"\r\n                  type=\"primary\"\r\n                  plain>\r\n                  替换\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'IntelligentErrorCorrection' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { setting, content_style, guid, trigerUpload } from '../../AiToolBox/AiToolBox.js'\r\nimport { elAttr } from './IntelligentErrorCorrection.js'\r\nimport { ElMessage } from 'element-plus'\r\n\r\nconst store = useStore()\r\n\r\nconst svg =\r\n  '<path class=\"path\" d=\"M 30 15 L 28 17 M 25.61 25.61 A 15 15, 0, 0, 1, 15 30 A 15 15, 0, 1, 1, 27.99 7.5 L 15 15\" style=\"stroke-width: 4px; fill: rgba(0, 0, 0, 0)\"/>'\r\n\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\nconst wordRef = ref()\r\nconst oldId = ref('')\r\nconst arrId = ref([])\r\nconst content = ref('')\r\nconst checklist = ref([])\r\n\r\nconst handleImport = () => {\r\n  trigerUpload().then((file) => {\r\n    const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n    const isShow = ['doc', 'docx', 'wps'].includes(fileType)\r\n    if (!isShow) return ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'wps'].join('、')}格式!` })\r\n    loading.value = true\r\n    fileWordUpload(file)\r\n  })\r\n}\r\nconst fileWordUpload = async (file) => {\r\n  try {\r\n    const param = new FormData()\r\n    param.append('file', file)\r\n    const { data } = await api.fileword2html(param)\r\n    content.value = data\r\n      .replace(/<\\/?html[^>]*>/g, '')\r\n      .replace(/<head\\b[^<]*(?:(?!<\\/head>)<[^<]*)*<\\/head>/gi, '')\r\n      .replace(/<\\/?body[^>]*>/g, '')\r\n      .replace(/<\\/?div[^>]*>/g, '')\r\n    loading.value = false\r\n  } catch (err) {\r\n    loading.value = false\r\n  }\r\n}\r\nconst handleWrongWord = async () => {\r\n  if (!content.value) return ElMessage({ type: 'warning', message: '请先输入内容在进行智能纠错！' })\r\n  loading.value = true\r\n  const { data } = await api.typingVerification({\r\n    text: content.value.replace(/&ldquo;/gi, '“').replace(/&rdquo;/gi, '”')\r\n  })\r\n  oldId.value = ''\r\n  arrId.value = []\r\n  checklist.value = data?.checklist?.map((v) => ({ ...v, id: guid() })) || []\r\n  content.value = data?.replace_text || ''\r\n  loading.value = false\r\n}\r\nconst handleDetails = (row) => {\r\n  const iframe = wordRef.value?.getEditor()?.iframeElement\r\n  const body = iframe?.contentWindow?.document?.body\r\n  const elList = body?.childNodes || []\r\n  if (oldId.value) {\r\n    const oldObj = elAttr(elList, oldId.value)\r\n    if (oldObj.elArr.length) {\r\n      for (let index = 0; index < oldObj.elArr.length; index++) {\r\n        const item = oldObj.elArr[index]\r\n        if (!index) iframe.contentWindow.scrollTo(0, item.offsetTop - 100)\r\n        item.style.color = ''\r\n        item.style.backgroundColor = ''\r\n      }\r\n    }\r\n  }\r\n  oldId.value = row.position + ''\r\n  const obj = elAttr(elList, row.position + '')\r\n  if (obj.elArr.length) {\r\n    for (let index = 0; index < obj.elArr.length; index++) {\r\n      const item = obj.elArr[index]\r\n      if (!index) iframe.contentWindow.scrollTo(0, item.offsetTop - 100)\r\n      item.style.color = '#fff'\r\n      item.style.backgroundColor = 'red'\r\n    }\r\n  }\r\n}\r\nconst handleReplace = (row) => {\r\n  const iframe = wordRef.value?.getEditor()?.iframeElement\r\n  const body = iframe?.contentWindow?.document?.body\r\n  const elList = body?.childNodes || []\r\n  const obj = elAttr(elList, row.position + '')\r\n  if (obj.elArr.length > 1) {\r\n    let styleStr = ''\r\n    for (let key in obj.styleObj) {\r\n      styleStr += `${key}:${obj.styleObj[key]};`\r\n    }\r\n    for (let index = 0; index < obj.elArr.length; index++) {\r\n      const item = obj.elArr[index]\r\n      const elParent = item\r\n      if (!index) {\r\n        elParent.insertAdjacentHTML('beforebegin', `<span style=\"${styleStr}\">${row.suggest[0]}</span>`)\r\n      }\r\n      elParent.parentNode.removeChild(elParent)\r\n    }\r\n  } else {\r\n    obj.elArr[0].insertAdjacentHTML('beforebegin', row.suggest[0])\r\n    obj.elArr[0].parentNode.removeChild(obj.elArr[0])\r\n  }\r\n  arrId.value.push(row.id)\r\n}\r\nconst handleAllReplace = () => {\r\n  for (let index = 0; index < checklist.value.length; index++) {\r\n    const item = checklist.value[index]\r\n    if (!arrId.value.includes(item.id) && item?.suggest?.length) {\r\n      handleReplace(item)\r\n    }\r\n  }\r\n}\r\nconst handleExportWord = () => {\r\n  store.commit('setExportWordHtmlObj', {\r\n    code: 'exportWord',\r\n    name: '智能纠错 --- 文档导出',\r\n    key: 'content',\r\n    data: { content: content.value }\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.IntelligentErrorCorrection {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n  background: #f3f5f7;\r\n  .IntelligentErrorCorrectionHead {\r\n    width: 100%;\r\n    padding: var(--zy-distance-two) 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    .IntelligentErrorCorrectionButton {\r\n      width: 796px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      & + .IntelligentErrorCorrectionButton {\r\n        width: calc(100% - 840px);\r\n      }\r\n      .IntelligentErrorCorrectionButtonItem {\r\n        display: flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n  .IntelligentErrorCorrectionBody {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-two) * 2)));\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding-bottom: var(--zy-distance-two);\r\n    .IntelligentErrorCorrectionBodyLeft {\r\n      width: 820px;\r\n      height: 100%;\r\n      .TinyMceEditor {\r\n        height: 100%;\r\n        .tox-tinymce {\r\n          border: none;\r\n        }\r\n        .tox-editor-header {\r\n          width: 796px;\r\n          border: 1px solid #ccc;\r\n          border-bottom: none;\r\n          margin: auto;\r\n          margin-right: 30px;\r\n        }\r\n      }\r\n    }\r\n    .IntelligentErrorCorrectionBodyRight {\r\n      width: calc(100% - 840px);\r\n      height: 100%;\r\n      .globalTable {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";iBAAA;;EAOSA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAAkC;;EACtCA,KAAK,EAAC;AAAsC;;EAG5CA,KAAK,EAAC;AAAsC;;EAK9CA,KAAK,EAAC;AAAkC;;EACtCA,KAAK,EAAC;AAAsC;;EAG5CA,KAAK,EAAC;AAAsC;;EAKhDA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAAoC;;EAG1CA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAa;;;;;;;;wCA9B9BC,mBAAA,CA0DM;IAzDJD,KAAK,EAAC,4BAA4B;IAEjC,yBAAuB,EAAEE,MAAA,CAAAC,GAAG;IAC5B,qBAAmB,EAAED,MAAA,CAAAE,WAAW;IACjC,8BAA4B,EAAC;MAC7BC,mBAAA,CAkBM,OAlBNC,UAkBM,GAjBJD,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAEM,OAFNG,UAEM,GADJC,YAAA,CAAgEC,oBAAA;IAArDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEV,MAAA,CAAAW;;IAV5CC,OAAA,EAAAC,QAAA,CAU0D;MAAA,OAAIC,MAAA,QAAAA,MAAA,OAV9DC,gBAAA,CAU0D,MAAI,E;;IAV9DC,CAAA;QAYQb,mBAAA,CAGM,OAHNc,UAGM,GAFJV,YAAA,CAA2CC,oBAAA;IAAhCC,IAAI,EAAC;EAAS;IAbnCG,OAAA,EAAAC,QAAA,CAaoC;MAAA,OAAKC,MAAA,QAAAA,MAAA,OAbzCC,gBAAA,CAaoC,OAAK,E;;IAbzCC,CAAA;MAcUT,YAAA,CAAkEC,oBAAA;IAAvDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEV,MAAA,CAAAkB;;IAd5CN,OAAA,EAAAC,QAAA,CAc8D;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAdhEC,gBAAA,CAc8D,IAAE,E;;IAdhEC,CAAA;UAiBMb,mBAAA,CAOM,OAPNgB,UAOM,GANJhB,mBAAA,CAEM,OAFNiB,UAEM,GADJb,YAAA,CAAmEC,oBAAA;IAAxDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEV,MAAA,CAAAqB;;IAnB5CT,OAAA,EAAAC,QAAA,CAmB6D;MAAA,OAAIC,MAAA,QAAAA,MAAA,OAnBjEC,gBAAA,CAmB6D,MAAI,E;;IAnBjEC,CAAA;QAqBQb,mBAAA,CAEM,OAFNmB,UAEM,GADJf,YAAA,CAAoEC,oBAAA;IAAzDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEV,MAAA,CAAAuB;;IAtB5CX,OAAA,EAAAC,QAAA,CAsB8D;MAAA,OAAIC,MAAA,QAAAA,MAAA,OAtBlEC,gBAAA,CAsB8D,MAAI,E;;IAtBlEC,CAAA;YA0BIb,mBAAA,CAgCM,OAhCNqB,UAgCM,GA/BJrB,mBAAA,CAEM,OAFNsB,WAEM,GADJlB,YAAA,CAAmGmB,wBAAA;IAApFC,GAAG,EAAC,SAAS;IA5BpCC,UAAA,EA4B8C5B,MAAA,CAAA6B,OAAO;IA5BrD,uBAAAf,MAAA,QAAAA,MAAA,gBAAAgB,MAAA;MAAA,OA4B8C9B,MAAA,CAAA6B,OAAO,GAAAC,MAAA;IAAA;IAAGC,OAAO,EAAE/B,MAAA,CAAA+B,OAAO;IAAGC,aAAa,EAAEhC,MAAA,CAAAgC;yEAEpF7B,mBAAA,CA2BM,OA3BN8B,WA2BM,GA1BJ9B,mBAAA,CAyBM,OAzBN+B,WAyBM,GAxBJ3B,YAAA,CAuBW4B,mBAAA;IAvBAC,IAAI,EAAEpC,MAAA,CAAAqC;EAAS;IAhCpCzB,OAAA,EAAAC,QAAA,CAiCY;MAAA,OAAiE,CAAjEN,YAAA,CAAiE+B,0BAAA;QAAhDC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAACC,IAAI,EAAC;UACnDjC,YAAA,CAMkB+B,0BAAA;QANDC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3B3B,OAAO,EAAAC,QAAA,CAChB,UAEU4B,KAHa;UAAA,QACvBlC,YAAA,CAEUmC,kBAAA;YAFAhC,OAAK,WAALA,OAAKA,CAAAoB,MAAA;cAAA,OAAE9B,MAAA,CAAA2C,aAAa,CAACF,KAAK,CAACG,GAAG;YAAA;YAAIC,QAAQ,EAAE7C,MAAA,CAAA8C,KAAK,CAACC,QAAQ,CAACN,KAAK,CAACG,GAAG,CAACI,EAAE;YAAGvC,IAAI,EAAC;;YApCzGG,OAAA,EAAAC,QAAA,CAqCkB;cAAA,OAAoB,CArCtCE,gBAAA,CAAAkC,gBAAA,CAqCqBR,KAAK,CAACG,GAAG,CAACM,IAAI,iB;;YArCnClC,CAAA;;;QAAAA,CAAA;UAyCYT,YAAA,CAEkB+B,0BAAA;QAFDC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3B3B,OAAO,EAAAC,QAAA,CAAS,UAAqD4B,KAAvD;UAAA,IAAAU,UAAA,EAAAC,WAAA;UAAA,QA1CvCrC,gBAAA,CAAAkC,gBAAA,CA0C4C,EAAAE,UAAA,GAAAV,KAAK,CAACG,GAAG,cAAAO,UAAA,uBAATA,UAAA,CAAWE,OAAO,UAAAD,WAAA,GAAOX,KAAK,CAACG,GAAG,cAAAQ,WAAA,uBAATA,WAAA,CAAWE,WAAW,kB;;QA1C3FtC,CAAA;UA4CYT,YAAA,CAUkB+B,0BAAA;QAVDC,KAAK,EAAC,IAAI;QAACgB,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC,OAAO;QAAC,YAAU,EAAC;;QACpD5C,OAAO,EAAAC,QAAA,CAChB,UAMY4B,KAPW;UAAA,IAAAgB,WAAA;UAAA,QACvBlD,YAAA,CAMYC,oBAAA;YALTE,OAAK,WAALA,OAAKA,CAAAoB,MAAA;cAAA,OAAE9B,MAAA,CAAA0D,aAAa,CAACjB,KAAK,CAACG,GAAG;YAAA;YAC9BC,QAAQ,EAAE7C,MAAA,CAAA8C,KAAK,CAACC,QAAQ,CAACN,KAAK,CAACG,GAAG,CAACI,EAAE,QAAAS,WAAA,GAAMhB,KAAK,CAACG,GAAG,cAAAa,WAAA,gBAAAA,WAAA,GAATA,WAAA,CAAWJ,OAAO,cAAAI,WAAA,eAAlBA,WAAA,CAAoBE,MAAM;YACtElD,IAAI,EAAC,SAAS;YACdmD,KAAK,EAAL;;YAlDlBhD,OAAA,EAAAC,QAAA,CAkDwB;cAAA,OAERC,MAAA,QAAAA,MAAA,OApDhBC,gBAAA,CAkDwB,MAER,E;;YApDhBC,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA;qDAAA6C,UAAA,K,qBAGe7D,MAAA,CAAA8D,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}