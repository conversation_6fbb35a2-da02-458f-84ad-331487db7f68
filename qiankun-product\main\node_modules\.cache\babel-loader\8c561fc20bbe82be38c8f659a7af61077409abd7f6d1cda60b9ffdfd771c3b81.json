{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AssistedWriting\"\n};\nvar _hoisted_2 = {\n  class: \"AssistedWritingBody\"\n};\nvar _hoisted_3 = {\n  class: \"AssistedWritingUserBody\"\n};\nvar _hoisted_4 = {\n  class: \"AssistedWritingUser\"\n};\nvar _hoisted_5 = {\n  class: \"AssistedWritingUserInfo\"\n};\nvar _hoisted_6 = {\n  class: \"AssistedWritingUserName\"\n};\nvar _hoisted_7 = {\n  class: \"AssistedWritingUserText\"\n};\nvar _hoisted_8 = {\n  class: \"AssistedWritingList\"\n};\nvar _hoisted_9 = [\"onClick\"];\nvar _hoisted_10 = [\"innerHTML\"];\nvar _hoisted_11 = {\n  class: \"AssistedWritingName\"\n};\nvar _hoisted_12 = {\n  class: \"AssistedWritingEditorBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_image, {\n    src: $setup.user.image,\n    fit: \"cover\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.user.userName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.user.position), 1 /* TEXT */)])])]), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"AssistedWritingTitle\"\n  }, \"文档撰写类型\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.toolData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: _normalizeClass([\"AssistedWritingItem\", {\n        'is-active': $setup.toolId === item.chatToolCode\n      }]),\n      key: item.chatToolCode,\n      onClick: function onClick($event) {\n        return $setup.handleTool(item);\n      }\n    }, [_createElementVNode(\"div\", {\n      class: \"AssistedWritingIcon\",\n      innerHTML: $setup.toolIconData[item.chatToolCode]\n    }, null, 8 /* PROPS */, _hoisted_10), _createElementVNode(\"div\", _hoisted_11, _toDisplayString(item.chatToolName), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_9);\n  }), 128 /* KEYED_FRAGMENT */)), (_openBlock(), _createElementBlock(_Fragment, null, _renderList(2, function (item) {\n    return _createElementVNode(\"div\", {\n      class: \"AssistedWritingPlaceholder\",\n      key: item + '_placeholder'\n    });\n  }), 64 /* STABLE_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_12, [_withDirectives(_createVNode($setup[\"GlobalAiChatFile\"], {\n    fileList: $setup.fileList,\n    fileData: $setup.fileData,\n    onClose: $setup.handleClose\n  }, null, 8 /* PROPS */, [\"fileList\", \"fileData\"]), [[_vShow, $setup.fileList.length || $setup.fileData.length]]), _createVNode($setup[\"GlobalAiChatEditor\"], {\n    ref: \"editorRef\",\n    modelValue: $setup.sendContent,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.sendContent = $event;\n    }),\n    onSend: $setup.handleSendMessage,\n    onUploadCallback: $setup.handleFileUpload,\n    onFileCallback: $setup.handleFileCallback\n  }, null, 8 /* PROPS */, [\"modelValue\"])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_image", "src", "$setup", "user", "image", "fit", "_hoisted_5", "_hoisted_6", "_toDisplayString", "userName", "_hoisted_7", "position", "_hoisted_8", "_Fragment", "_renderList", "toolData", "item", "_normalizeClass", "toolId", "chatToolCode", "key", "onClick", "$event", "handleTool", "innerHTML", "toolIconData", "_hoisted_10", "_hoisted_11", "chatToolName", "_hoisted_9", "_hoisted_12", "fileList", "fileData", "onClose", "handleClose", "length", "ref", "modelValue", "send<PERSON><PERSON><PERSON>", "_cache", "onSend", "handleSendMessage", "onUploadCallback", "handleFileUpload", "onFileCallback", "handleFileCallback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AssistedWriting\\AssistedWritingRD.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AssistedWriting\">\r\n    <div class=\"AssistedWritingBody\">\r\n      <div class=\"AssistedWritingUserBody\">\r\n        <div class=\"AssistedWritingUser\">\r\n          <el-image :src=\"user.image\" fit=\"cover\" />\r\n          <div class=\"AssistedWritingUserInfo\">\r\n            <div class=\"AssistedWritingUserName\">{{ user.userName }}</div>\r\n            <div class=\"AssistedWritingUserText\">{{ user.position }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AssistedWritingTitle\">文档撰写类型</div>\r\n      <div class=\"AssistedWritingList\">\r\n        <div\r\n          class=\"AssistedWritingItem\"\r\n          :class=\"{ 'is-active': toolId === item.chatToolCode }\"\r\n          v-for=\"item in toolData\"\r\n          :key=\"item.chatToolCode\"\r\n          @click=\"handleTool(item)\">\r\n          <div class=\"AssistedWritingIcon\" v-html=\"toolIconData[item.chatToolCode]\"></div>\r\n          <div class=\"AssistedWritingName\">{{ item.chatToolName }}</div>\r\n        </div>\r\n        <div class=\"AssistedWritingPlaceholder\" v-for=\"item in 2\" :key=\"item + '_placeholder'\"></div>\r\n      </div>\r\n      <div class=\"AssistedWritingEditorBody\">\r\n        <GlobalAiChatFile\r\n          :fileList=\"fileList\"\r\n          :fileData=\"fileData\"\r\n          @close=\"handleClose\"\r\n          v-show=\"fileList.length || fileData.length\" />\r\n        <GlobalAiChatEditor\r\n          ref=\"editorRef\"\r\n          v-model=\"sendContent\"\r\n          @send=\"handleSendMessage\"\r\n          @uploadCallback=\"handleFileUpload\"\r\n          @fileCallback=\"handleFileCallback\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AssistedWritingRD' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, inject, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue'\r\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue'\r\nconst store = useStore()\r\nconst suggestIcon =\r\n  '<svg t=\"1742816739845\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"1527\" width=\"32\" height=\"32\"><path d=\"M1014.472563 517.57762l-105.551874-108.296797c-12.575579-12.575579-22.310481-8.170934-31.981548 0L420.611611 877.322154c-11.58613 12.352155-17.746248 111.073638-3.191772 130.543443 14.554477 19.469804 129.298652 14.36297 138.490953 6.447377l458.561771-462.487649c12.160648-15.192831 5.71327-28.311011 0-34.247705zM531.81292 975.245695h-1.053285c-17.490906 10.692434-62.814058 9.256137-76.187579 3.255606-5.170669-16.820634-2.042734-49.472454 2.553417-72.325535l436.315125-446.84797 73.091561 75.070459-434.719239 440.84744zM655.749396 491.947697H302.420323a30.928263 30.928263 0 0 1 0-61.79269h353.329073a30.928263 30.928263 0 0 1 0 61.79269z m-353.329073 219.434271a30.928263 30.928263 0 0 1 0-61.79269h129.905088a30.928263 30.928263 0 0 1 0 61.79269h-129.905088zM161.790883 461.051352c0 17.075976 13.437357 30.896345 30.034567 30.896345s30.034567-13.820369 30.034567-30.896345c0-17.075976-13.437357-30.896345-30.034567-30.896345s-30.034567 13.852287-30.034567 30.896345zM161.790883 680.485623c0 17.075976 13.437357 30.896345 30.034567 30.896345s30.034567-13.820369 30.034567-30.896345-13.437357-30.896345-30.034567-30.896345-30.034567 13.852287-30.034567 30.896345z\" p-id=\"1528\" fill=\"#b41916\"></path><path d=\"M269.034396 957.946295H62.750222V310.527421h211.135666c18.225014 0 33.002914-14.777901 33.002914-33.002914V65.973911h481.606359v235.105868a33.002914 33.002914 0 0 0 66.005829 0v-268.108782A33.034832 33.034832 0 0 0 821.466158 0H270.78987l-6.160119 1.755474a32.077301 32.077301 0 0 0-14.937489 10.564763L6.447378 253.777729c-2.553417 2.713006-4.659986 5.840941-6.160119 9.224219v3.0641a33.89661 33.89661 0 0 0 0 8.809289v716.041954c0 18.225014 14.777901 33.002914 33.002915 33.002915H269.066313a33.002914 33.002914 0 1 0-0.031917-65.973911zM240.882974 112.605688v131.947822H108.935152l131.947822-131.947822z\" p-id=\"1529\" fill=\"#b41916\"></path></svg>'\r\n\r\nconst openPage = inject('openPage')\r\nconst toolId = ref('')\r\nconst toolInfo = ref({})\r\nconst toolData = ref([])\r\nconst toolIconData = { suggestion: suggestIcon }\r\n\r\nconst editorRef = ref()\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst sendContent = ref('')\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleTips = (text) => {\r\n  const parts = text.split(/(\\{[^}]+\\})/)\r\n  const result = parts\r\n    .map((part) => {\r\n      if (part.startsWith('{') && part.endsWith('}')) {\r\n        return { value: part.slice(1, -1), type: true }\r\n      } else if (part.trim() !== '') {\r\n        return { value: part, type: false }\r\n      }\r\n    })\r\n    .filter((item) => item !== undefined)\r\n  return result\r\n}\r\nconst handleTool = (item) => {\r\n  toolInfo.value = item\r\n  toolId.value = item.chatToolCode\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n  nextTick(() => {\r\n    editorRef.value?.handleInsertPlaceholder(handleTips(item.userPromptTip))\r\n  })\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!toolId.value) return ElMessage({ type: 'warning', message: '请先选择文档撰写类型！' })\r\n  const openAiParams = {\r\n    toolId: toolInfo.value.id,\r\n    toolCode: toolId.value,\r\n    toolContent: value,\r\n    fileData: fileData.value\r\n  }\r\n  sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams))\r\n  if (toolId.value === 'suggestion') openPage({ key: 'routePath', value: '/suggest/SubmitSuggest' })\r\n  editorRef.value?.handleSetFile([])\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const { data } = await api.aigptChatSceneDetail({ query: { chatSceneCode: 'ai-intelligent-write-all-chat' } })\r\n  toolData.value = data?.tools?.filter((v) => v.isUsing) || []\r\n}\r\nonActivated(() => {\r\n  aigptChatSceneDetail()\r\n  store.commit('setAiChatElShow', false)\r\n})\r\nonDeactivated(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\nonUnmounted(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.AssistedWriting {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  position: relative;\r\n\r\n  .AssistedWritingBody {\r\n    padding: var(--zy-distance-two);\r\n  }\r\n\r\n  .AssistedWritingUserBody {\r\n    width: 800px;\r\n    border-radius: 6px 6px 6px 6px;\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .AssistedWritingUser {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: var(--zy-distance-two);\r\n\r\n      .zy-el-image {\r\n        width: 62px;\r\n        height: 62px;\r\n        border-radius: 50%;\r\n      }\r\n\r\n      .AssistedWritingUserInfo {\r\n        width: calc(100% - 62px);\r\n        height: 58px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        padding-left: var(--zy-distance-two);\r\n\r\n        .AssistedWritingUserName {\r\n          font-weight: bold;\r\n          line-height: var(--zy-line-height);\r\n          font-size: calc(var(--zy-name-font-size) + 2px);\r\n        }\r\n\r\n        .AssistedWritingUserText {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-name-font-size);\r\n          color: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .AssistedWritingTitle {\r\n    width: 100%;\r\n    font-weight: bold;\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-name-font-size);\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .AssistedWritingList {\r\n    width: 800px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .AssistedWritingPlaceholder {\r\n      width: 185px;\r\n      height: 126px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .AssistedWritingItem {\r\n      width: 185px;\r\n      height: 126px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      border-radius: 6px 6px 6px 6px;\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n      padding: 20px;\r\n      margin-bottom: 20px;\r\n      cursor: pointer;\r\n\r\n      &.is-active {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AssistedWritingName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AssistedWritingName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .AssistedWritingIcon {\r\n        width: 48px;\r\n        height: 48px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n\r\n      .AssistedWritingName {\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n    }\r\n  }\r\n\r\n  .AssistedWritingEditorBody {\r\n    width: 800px;\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: 20px;\r\n    transform: translateX(-50%);\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalAiChatEditorContainer {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAyB;;EAC/BA,KAAK,EAAC;AAAyB;;EAKrCA,KAAK,EAAC;AAAqB;iBAbtC;kBAAA;;EAqBeA,KAAK,EAAC;AAAqB;;EAI/BA,KAAK,EAAC;AAA2B;;;uBAxB1CC,mBAAA,CAsCM,OAtCNC,UAsCM,GArCJC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJD,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAMM,OANNG,UAMM,GALJC,YAAA,CAA0CC,mBAAA;IAA/BC,GAAG,EAAEC,MAAA,CAAAC,IAAI,CAACC,KAAK;IAAEC,GAAG,EAAC;oCAChCV,mBAAA,CAGM,OAHNW,UAGM,GAFJX,mBAAA,CAA8D,OAA9DY,UAA8D,EAAAC,gBAAA,CAAtBN,MAAA,CAAAC,IAAI,CAACM,QAAQ,kBACrDd,mBAAA,CAA8D,OAA9De,UAA8D,EAAAF,gBAAA,CAAtBN,MAAA,CAAAC,IAAI,CAACQ,QAAQ,iB,iCAI3DhB,mBAAA,CAA8C;IAAzCH,KAAK,EAAC;EAAsB,GAAC,QAAM,sBACxCG,mBAAA,CAWM,OAXNiB,UAWM,I,kBAVJnB,mBAAA,CAQMoB,SAAA,QAtBdC,WAAA,CAiByBZ,MAAA,CAAAa,QAAQ,EAjBjC,UAiBiBC,IAAI;yBAHbvB,mBAAA,CAQM;MAPJD,KAAK,EAffyB,eAAA,EAegB,qBAAqB;QAAA,aACJf,MAAA,CAAAgB,MAAM,KAAKF,IAAI,CAACG;MAAY;MAElDC,GAAG,EAAEJ,IAAI,CAACG,YAAY;MACtBE,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAEpB,MAAA,CAAAqB,UAAU,CAACP,IAAI;MAAA;QACvBrB,mBAAA,CAAgF;MAA3EH,KAAK,EAAC,qBAAqB;MAACgC,SAAwC,EAAhCtB,MAAA,CAAAuB,YAAY,CAACT,IAAI,CAACG,YAAY;4BApBjFO,WAAA,GAqBU/B,mBAAA,CAA8D,OAA9DgC,WAA8D,EAAAnB,gBAAA,CAA1BQ,IAAI,CAACY,YAAY,iB,yBArB/DC,UAAA;iDAuBQpC,mBAAA,CAA6FoB,SAAA,QAvBrGC,WAAA,CAuB+D,CAAC,EAvBhE,UAuBuDE,IAAI;WAAnDrB,mBAAA,CAA6F;MAAxFH,KAAK,EAAC,4BAA4B;MAAoB4B,GAAG,EAAEJ,IAAI;;oCAEtErB,mBAAA,CAYM,OAZNmC,WAYM,G,gBAXJ/B,YAAA,CAIgDG,MAAA;IAH7C6B,QAAQ,EAAE7B,MAAA,CAAA6B,QAAQ;IAClBC,QAAQ,EAAE9B,MAAA,CAAA8B,QAAQ;IAClBC,OAAK,EAAE/B,MAAA,CAAAgC;+DACAhC,MAAA,CAAA6B,QAAQ,CAACI,MAAM,IAAIjC,MAAA,CAAA8B,QAAQ,CAACG,MAAM,E,GAC5CpC,YAAA,CAKuCG,MAAA;IAJrCkC,GAAG,EAAC,WAAW;IAhCzBC,UAAA,EAiCmBnC,MAAA,CAAAoC,WAAW;IAjC9B,uBAAAC,MAAA,QAAAA,MAAA,gBAAAjB,MAAA;MAAA,OAiCmBpB,MAAA,CAAAoC,WAAW,GAAAhB,MAAA;IAAA;IACnBkB,MAAI,EAAEtC,MAAA,CAAAuC,iBAAiB;IACvBC,gBAAc,EAAExC,MAAA,CAAAyC,gBAAgB;IAChCC,cAAY,EAAE1C,MAAA,CAAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}