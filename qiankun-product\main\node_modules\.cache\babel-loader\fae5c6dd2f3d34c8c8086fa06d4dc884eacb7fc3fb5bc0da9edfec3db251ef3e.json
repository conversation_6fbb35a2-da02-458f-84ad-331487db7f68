{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"IntelligentManuscriptMerging\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, \"智能合稿\");\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBoxFunction\\IntelligentManuscriptMerging\\IntelligentManuscriptMerging.vue"], "sourcesContent": ["<template>\r\n  <div class=\"IntelligentManuscriptMerging\">智能合稿</div>\r\n</template>\r\n<script>\r\nexport default { name: 'IntelligentManuscriptMerging' }\r\n</script>\r\n\r\n<script setup></script>\r\n<style lang=\"scss\">\r\n.IntelligentManuscriptMerging {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n  background: #f3f5f7;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA8B;;uBAAzCC,mBAAA,CAAoD,OAApDC,UAAoD,EAAV,MAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}