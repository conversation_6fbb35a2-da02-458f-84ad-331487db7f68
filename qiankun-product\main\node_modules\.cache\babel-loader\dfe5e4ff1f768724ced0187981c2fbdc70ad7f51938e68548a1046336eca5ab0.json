{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"NotFoundPage\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_empty, {\n    description: $setup.description\n  }, null, 8 /* PROPS */, [\"description\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_empty", "description", "$setup"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\NotFoundPage\\NotFoundPage.vue"], "sourcesContent": ["<template>\r\n  <div class=\"NotFoundPage\">\r\n    <el-empty :description=\"description\"></el-empty>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'NotFoundPage' }\r\n</script>\r\n<script setup>\r\nimport { computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nconst store = useStore()\r\nconst description = computed(() => (store.getters.getMenuFn || []).length ? '找不到页面' : '暂未分配菜单权限，请联系管理员分配')\r\n</script>\r\n<style lang=\"scss\">\r\n.NotFoundPage {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;;uBAAzBC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAgDC,mBAAA;IAArCC,WAAW,EAAEC,MAAA,CAAAD;EAAW,yC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}