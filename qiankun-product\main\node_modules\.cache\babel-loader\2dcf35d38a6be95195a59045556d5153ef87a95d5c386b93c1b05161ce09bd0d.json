{"ast": null, "code": "function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { ref, onBeforeUnmount, onMounted, nextTick, watch } from 'vue';\nimport Hls from 'hls.js';\nvar __default__ = {\n  name: 'VideoPlayer'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    liveUrl: {\n      type: String,\n      default: ''\n    },\n    replayUrl: {\n      type: String,\n      default: ''\n    },\n    isReplay: {\n      type: Boolean,\n      default: false\n    },\n    autoInit: {\n      type: Boolean,\n      default: true\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    var props = __props;\n\n    // 视频播放器相关\n    var videoPlayer = ref(null);\n    var player = ref(null);\n    var hls = ref(null);\n    var isPlayerInitialized = ref(false);\n\n    // 初始化视频播放器\n    var _initVideoPlayer = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var video, hlsUrl;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              console.log('initVideoPlayer: 开始初始化直播播放器');\n              console.log('initVideoPlayer: isPlayerInitialized =', isPlayerInitialized.value);\n              console.log('initVideoPlayer: videoPlayer.value =', !!videoPlayer.value);\n              if (videoPlayer.value) {\n                _context.next = 6;\n                break;\n              }\n              console.log('initVideoPlayer: video元素不存在，跳过初始化');\n              return _context.abrupt(\"return\");\n            case 6:\n              // 销毁现有播放器\n              destroyVideoPlayer();\n              video = videoPlayer.value;\n              player.value = video;\n              isPlayerInitialized.value = true;\n              console.log('initVideoPlayer: 原始直播URL:', props.liveUrl);\n\n              // HLS视频流地址 - 使用动态的liveUrl\n              hlsUrl = getHlsUrl(props.liveUrl);\n              console.log('initVideoPlayer: 解析后的直播URL:', hlsUrl);\n              if (hlsUrl) {\n                _context.next = 16;\n                break;\n              }\n              console.warn('initVideoPlayer: 没有推流地址');\n              return _context.abrupt(\"return\");\n            case 16:\n              // 检查浏览器是否原生支持HLS\n              if (video.canPlayType('application/vnd.apple.mpegurl')) {\n                // 原生支持HLS\n                video.src = hlsUrl;\n                setupVideoEvents();\n              } else if (Hls.isSupported()) {\n                // 使用HLS.js库\n                hls.value = new Hls({\n                  maxBufferLength: 30,\n                  maxMaxBufferLength: 60,\n                  startLevel: -1,\n                  // 自动选择适合的初始清晰度\n                  maxBufferHole: 0.5,\n                  highLatencyMode: false\n                });\n\n                // 加载视频流\n                hls.value.loadSource(hlsUrl);\n                hls.value.attachMedia(video);\n\n                // HLS事件监听\n                hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\n                  console.log('视频流准备就绪，点击播放按钮开始');\n                });\n\n                // 错误处理\n                hls.value.on(Hls.Events.ERROR, function (_, data) {\n                  console.error('HLS错误:', data);\n                  switch (data.type) {\n                    case Hls.ErrorTypes.NETWORK_ERROR:\n                      hls.value.startLoad(); // 尝试重新加载\n                      break;\n                    case Hls.ErrorTypes.MEDIA_ERROR:\n                      hls.value.recoverMediaError(); // 尝试恢复媒体错误\n                      break;\n                    default:\n                      // 无法恢复的错误，尝试重新初始化\n                      setTimeout(_initVideoPlayer, 3000);\n                      break;\n                  }\n                });\n                setupVideoEvents();\n              }\n            case 17:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function initVideoPlayer() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n\n    // 初始化回放播放器\n    var initReplayPlayer = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var video, replayUrl;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              console.log('initReplayPlayer: 开始初始化回放播放器');\n              console.log('initReplayPlayer: isPlayerInitialized =', isPlayerInitialized.value);\n              console.log('initReplayPlayer: videoPlayer.value =', !!videoPlayer.value);\n              if (videoPlayer.value) {\n                _context2.next = 6;\n                break;\n              }\n              console.log('initReplayPlayer: video元素不存在，跳过初始化');\n              return _context2.abrupt(\"return\");\n            case 6:\n              // 销毁现有播放器\n              destroyVideoPlayer();\n              video = videoPlayer.value;\n              player.value = video;\n              isPlayerInitialized.value = true;\n              console.log('initReplayPlayer: 原始回放URL:', props.replayUrl);\n\n              // 使用回放地址，也需要解析JSON格式\n              replayUrl = getHlsUrl(props.replayUrl);\n              console.log('initReplayPlayer: 解析后的回放URL:', replayUrl);\n              if (replayUrl) {\n                _context2.next = 16;\n                break;\n              }\n              console.warn('initReplayPlayer: 没有回放地址');\n              return _context2.abrupt(\"return\");\n            case 16:\n              console.log('initReplayPlayer: 开始播放回放:', replayUrl);\n\n              // 检查是否是HLS格式\n              if (replayUrl.includes('.m3u8') || replayUrl.includes('hls')) {\n                // HLS回放\n                if (video.canPlayType('application/vnd.apple.mpegurl')) {\n                  // 原生支持HLS\n                  video.src = replayUrl;\n                  setupVideoEvents();\n                } else if (Hls.isSupported()) {\n                  // 使用HLS.js库\n                  hls.value = new Hls({\n                    maxBufferLength: 30,\n                    maxMaxBufferLength: 60,\n                    startLevel: -1,\n                    maxBufferHole: 0.5,\n                    highLatencyMode: false\n                  });\n                  hls.value.loadSource(replayUrl);\n                  hls.value.attachMedia(video);\n                  hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\n                    console.log('回放视频准备就绪');\n                  });\n                  hls.value.on(Hls.Events.ERROR, function (_, data) {\n                    console.error('HLS回放错误:', data);\n                  });\n                  setupVideoEvents();\n                }\n              } else {\n                // 普通视频格式\n                video.src = replayUrl;\n                setupVideoEvents();\n              }\n            case 18:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function initReplayPlayer() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n\n    // 设置视频事件监听\n    var setupVideoEvents = function setupVideoEvents() {\n      console.log('setupVideoEvents: 开始设置视频事件监听');\n      var video = player.value;\n      if (!video) {\n        console.log('setupVideoEvents: player.value不存在');\n        return;\n      }\n      console.log('setupVideoEvents: 添加事件监听器');\n\n      // 视频可以播放时\n      video.addEventListener('canplay', function () {\n        console.log('视频准备就绪，点击播放按钮开始');\n      });\n\n      // 播放事件\n      video.addEventListener('play', function () {\n        console.log('正在播放HLS视频流');\n      });\n\n      // 暂停事件\n      video.addEventListener('pause', function () {\n        console.log('HLS视频流已暂停');\n      });\n\n      // 视频结束事件\n      video.addEventListener('ended', function () {\n        console.log('视频播放已结束');\n      });\n\n      // 音量变化事件\n      video.addEventListener('volumechange', function () {\n        console.log('音量变化');\n      });\n\n      // 添加错误事件监听\n      video.addEventListener('error', function (e) {\n        console.error('视频播放错误:', e);\n      });\n\n      // 添加加载开始事件\n      video.addEventListener('loadstart', function () {\n        console.log('开始加载视频');\n      });\n\n      // 添加元数据加载完成事件\n      video.addEventListener('loadedmetadata', function () {\n        console.log('视频元数据加载完成');\n      });\n    };\n\n    // 销毁视频播放器（包括直播和回放播放器）\n    var destroyVideoPlayer = function destroyVideoPlayer() {\n      console.log('destroyVideoPlayer: 开始销毁播放器');\n\n      // 销毁 hls.js 实例（直播和回放都可能使用）\n      if (hls.value) {\n        try {\n          console.log('destroyVideoPlayer: 销毁HLS实例');\n          hls.value.destroy();\n        } catch (error) {\n          console.error('销毁HLS实例错误:', error);\n        }\n        hls.value = null;\n      }\n\n      // 销毁video元素播放器（直播和回放共用同一个video元素）\n      if (player.value) {\n        try {\n          console.log('destroyVideoPlayer: 清理player引用');\n          player.value.pause(); // 停止播放\n          player.value.currentTime = 0; // 重置播放时间\n          player.value.src = ''; // 清空视频源\n          player.value.removeAttribute('src'); // 移除src属性\n          player.value.load(); // 重新加载空的video元素\n          player.value.muted = false; // 静音\n        } catch (error) {\n          console.error('销毁播放器错误:', error);\n        }\n        player.value = null;\n      }\n\n      // 确保video元素也被清理\n      if (videoPlayer.value) {\n        try {\n          console.log('destroyVideoPlayer: 清理video元素');\n          videoPlayer.value.pause();\n          videoPlayer.value.currentTime = 0;\n          videoPlayer.value.src = '';\n          videoPlayer.value.removeAttribute('src');\n          videoPlayer.value.load();\n          videoPlayer.value.muted = true;\n        } catch (error) {\n          console.error('清理video元素错误:', error);\n        }\n      }\n\n      // 重置状态\n      isPlayerInitialized.value = false;\n      console.log('视频播放器已完全销毁');\n    };\n\n    // 从推流地址中获取HLS地址\n    var getHlsUrl = function getHlsUrl(liveUrl) {\n      if (!liveUrl) return null;\n      console.log('原始推流地址:', liveUrl);\n\n      // 如果liveUrl是JSON格式，解析出HLS地址\n      try {\n        var urlData = JSON.parse(liveUrl);\n        console.log('解析的JSON数据:', urlData);\n        var hlsUrl = urlData.hls || urlData.m3u8 || liveUrl;\n        console.log('提取的HLS地址:', hlsUrl);\n        return hlsUrl;\n      } catch (error) {\n        console.log('不是JSON格式，直接使用原地址');\n        // 如果不是JSON格式，直接返回原地址\n        return liveUrl;\n      }\n    };\n\n    // 监听回放状态变化\n    watch(function () {\n      return props.isReplay;\n    }, function (isReplay) {\n      console.log('VideoPlayer: isReplay状态变化为:', isReplay);\n      nextTick(function () {\n        initPlayer();\n      });\n    });\n\n    // 监听URL变化\n    watch(function () {\n      return [props.liveUrl, props.replayUrl];\n    }, function (_ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n        newLiveUrl = _ref5[0],\n        newReplayUrl = _ref5[1];\n      console.log('VideoPlayer: URL变化检测');\n      console.log('VideoPlayer: 新的直播URL:', newLiveUrl);\n      console.log('VideoPlayer: 新的回放URL:', newReplayUrl);\n      if (props.autoInit) {\n        console.log('VideoPlayer: URL变化，重新初始化播放器');\n        nextTick(function () {\n          initPlayer();\n        });\n      }\n    });\n\n    // 统一的初始化方法\n    var initPlayer = function initPlayer() {\n      console.log('VideoPlayer: 开始初始化播放器');\n      console.log('VideoPlayer: isReplay =', props.isReplay);\n      console.log('VideoPlayer: liveUrl =', props.liveUrl);\n      console.log('VideoPlayer: replayUrl =', props.replayUrl);\n\n      // 先销毁现有播放器，确保每次初始化都是干净的\n      console.log('VideoPlayer: 先销毁现有播放器');\n      destroyVideoPlayer();\n\n      // 强制重置状态\n      console.log('VideoPlayer: 重置播放器状态');\n      isPlayerInitialized.value = false;\n\n      // 等待一小段时间确保销毁完成\n      setTimeout(function () {\n        if (props.isReplay) {\n          console.log('VideoPlayer: 初始化回放播放器');\n          initReplayPlayer();\n        } else {\n          console.log('VideoPlayer: 初始化直播播放器');\n          _initVideoPlayer();\n        }\n      }, 50);\n    };\n    onMounted(function () {\n      console.log('VideoPlayer: 组件已挂载');\n      // 组件挂载后自动初始化播放器\n      if (props.autoInit) {\n        nextTick(function () {\n          initPlayer();\n        });\n      }\n    });\n    onBeforeUnmount(function () {\n      destroyVideoPlayer();\n    });\n\n    // 暴露方法给父组件\n    __expose({\n      initVideoPlayer: _initVideoPlayer,\n      initReplayPlayer,\n      destroyVideoPlayer,\n      initPlayer\n    });\n    var __returned__ = {\n      props,\n      videoPlayer,\n      player,\n      hls,\n      isPlayerInitialized,\n      initVideoPlayer: _initVideoPlayer,\n      initReplayPlayer,\n      setupVideoEvents,\n      destroyVideoPlayer,\n      getHlsUrl,\n      initPlayer,\n      ref,\n      onBeforeUnmount,\n      onMounted,\n      nextTick,\n      watch,\n      get Hls() {\n        return Hls;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ref", "onBeforeUnmount", "onMounted", "nextTick", "watch", "Hls", "__default__", "props", "__props", "videoPlayer", "player", "hls", "isPlayerInitialized", "initVideoPlayer", "_ref2", "_callee", "video", "hlsUrl", "_callee$", "_context", "console", "log", "destroyVideoPlayer", "liveUrl", "getHlsUrl", "warn", "canPlayType", "src", "setupVideoEvents", "isSupported", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxMax<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "startLevel", "maxBufferHole", "highLatencyMode", "loadSource", "attachMedia", "on", "Events", "MANIFEST_PARSED", "ERROR", "_", "data", "error", "ErrorTypes", "NETWORK_ERROR", "startLoad", "MEDIA_ERROR", "recoverMediaError", "setTimeout", "initReplayPlayer", "_ref3", "_callee2", "replayUrl", "_callee2$", "_context2", "includes", "addEventListener", "destroy", "pause", "currentTime", "removeAttribute", "load", "muted", "urlData", "JSON", "parse", "m3u8", "isReplay", "initPlayer", "_ref4", "_ref5", "_slicedToArray", "newLiveUrl", "newReplayUrl", "autoInit", "__expose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/LiveManagement/components/VideoPlayer.vue"], "sourcesContent": ["<template>\r\n  <div class=\"video-player-container\">\r\n    <div class=\"player-container\">\r\n      <video ref=\"videoPlayer\" id=\"video-player\" controls></video>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'VideoPlayer' }\r\n</script>\r\n\r\n<script setup>\r\nimport { ref, onBeforeUnmount, onMounted, nextTick, watch } from 'vue'\r\nimport Hls from 'hls.js'\r\n\r\nconst props = defineProps({\r\n  liveUrl: {\r\n    type: String,\r\n    default: ''\r\n  },\r\n  replayUrl: {\r\n    type: String,\r\n    default: ''\r\n  },\r\n  isReplay: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  autoInit: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n})\r\n\r\n// 视频播放器相关\r\nconst videoPlayer = ref(null)\r\nconst player = ref(null)\r\nconst hls = ref(null)\r\nconst isPlayerInitialized = ref(false)\r\n\r\n// 初始化视频播放器\r\nconst initVideoPlayer = async () => {\r\n  console.log('initVideoPlayer: 开始初始化直播播放器')\r\n  console.log('initVideoPlayer: isPlayerInitialized =', isPlayerInitialized.value)\r\n  console.log('initVideoPlayer: videoPlayer.value =', !!videoPlayer.value)\r\n\r\n  if (!videoPlayer.value) {\r\n    console.log('initVideoPlayer: video元素不存在，跳过初始化')\r\n    return\r\n  }\r\n\r\n  // 销毁现有播放器\r\n  destroyVideoPlayer()\r\n\r\n  const video = videoPlayer.value\r\n  player.value = video\r\n  isPlayerInitialized.value = true\r\n\r\n  console.log('initVideoPlayer: 原始直播URL:', props.liveUrl)\r\n\r\n  // HLS视频流地址 - 使用动态的liveUrl\r\n  const hlsUrl = getHlsUrl(props.liveUrl)\r\n\r\n  console.log('initVideoPlayer: 解析后的直播URL:', hlsUrl)\r\n\r\n  if (!hlsUrl) {\r\n    console.warn('initVideoPlayer: 没有推流地址')\r\n    return\r\n  }\r\n\r\n  // 检查浏览器是否原生支持HLS\r\n  if (video.canPlayType('application/vnd.apple.mpegurl')) {\r\n    // 原生支持HLS\r\n    video.src = hlsUrl\r\n    setupVideoEvents()\r\n  } else if (Hls.isSupported()) {\r\n    // 使用HLS.js库\r\n    hls.value = new Hls({\r\n      maxBufferLength: 30,\r\n      maxMaxBufferLength: 60,\r\n      startLevel: -1, // 自动选择适合的初始清晰度\r\n      maxBufferHole: 0.5,\r\n      highLatencyMode: false\r\n    })\r\n\r\n    // 加载视频流\r\n    hls.value.loadSource(hlsUrl)\r\n    hls.value.attachMedia(video)\r\n\r\n    // HLS事件监听\r\n    hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\r\n      console.log('视频流准备就绪，点击播放按钮开始')\r\n    })\r\n\r\n    // 错误处理\r\n    hls.value.on(Hls.Events.ERROR, function (_, data) {\r\n      console.error('HLS错误:', data)\r\n      switch (data.type) {\r\n        case Hls.ErrorTypes.NETWORK_ERROR:\r\n          hls.value.startLoad() // 尝试重新加载\r\n          break\r\n        case Hls.ErrorTypes.MEDIA_ERROR:\r\n          hls.value.recoverMediaError() // 尝试恢复媒体错误\r\n          break\r\n        default:\r\n          // 无法恢复的错误，尝试重新初始化\r\n          setTimeout(initVideoPlayer, 3000)\r\n          break\r\n      }\r\n    })\r\n\r\n    setupVideoEvents()\r\n  }\r\n}\r\n\r\n// 初始化回放播放器\r\nconst initReplayPlayer = async () => {\r\n  console.log('initReplayPlayer: 开始初始化回放播放器')\r\n  console.log('initReplayPlayer: isPlayerInitialized =', isPlayerInitialized.value)\r\n  console.log('initReplayPlayer: videoPlayer.value =', !!videoPlayer.value)\r\n\r\n  if (!videoPlayer.value) {\r\n    console.log('initReplayPlayer: video元素不存在，跳过初始化')\r\n    return\r\n  }\r\n\r\n  // 销毁现有播放器\r\n  destroyVideoPlayer()\r\n\r\n  const video = videoPlayer.value\r\n  player.value = video\r\n  isPlayerInitialized.value = true\r\n\r\n  console.log('initReplayPlayer: 原始回放URL:', props.replayUrl)\r\n\r\n  // 使用回放地址，也需要解析JSON格式\r\n  const replayUrl = getHlsUrl(props.replayUrl)\r\n\r\n  console.log('initReplayPlayer: 解析后的回放URL:', replayUrl)\r\n\r\n  if (!replayUrl) {\r\n    console.warn('initReplayPlayer: 没有回放地址')\r\n    return\r\n  }\r\n\r\n  console.log('initReplayPlayer: 开始播放回放:', replayUrl)\r\n\r\n  // 检查是否是HLS格式\r\n  if (replayUrl.includes('.m3u8') || replayUrl.includes('hls')) {\r\n    // HLS回放\r\n    if (video.canPlayType('application/vnd.apple.mpegurl')) {\r\n      // 原生支持HLS\r\n      video.src = replayUrl\r\n      setupVideoEvents()\r\n    } else if (Hls.isSupported()) {\r\n      // 使用HLS.js库\r\n      hls.value = new Hls({\r\n        maxBufferLength: 30,\r\n        maxMaxBufferLength: 60,\r\n        startLevel: -1,\r\n        maxBufferHole: 0.5,\r\n        highLatencyMode: false\r\n      })\r\n\r\n      hls.value.loadSource(replayUrl)\r\n      hls.value.attachMedia(video)\r\n\r\n      hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\r\n        console.log('回放视频准备就绪')\r\n      })\r\n\r\n      hls.value.on(Hls.Events.ERROR, function (_, data) {\r\n        console.error('HLS回放错误:', data)\r\n      })\r\n\r\n      setupVideoEvents()\r\n    }\r\n  } else {\r\n    // 普通视频格式\r\n    video.src = replayUrl\r\n    setupVideoEvents()\r\n  }\r\n}\r\n\r\n// 设置视频事件监听\r\nconst setupVideoEvents = () => {\r\n  console.log('setupVideoEvents: 开始设置视频事件监听')\r\n  const video = player.value\r\n  if (!video) {\r\n    console.log('setupVideoEvents: player.value不存在')\r\n    return\r\n  }\r\n\r\n  console.log('setupVideoEvents: 添加事件监听器')\r\n\r\n  // 视频可以播放时\r\n  video.addEventListener('canplay', function () {\r\n    console.log('视频准备就绪，点击播放按钮开始')\r\n  })\r\n\r\n  // 播放事件\r\n  video.addEventListener('play', function () {\r\n    console.log('正在播放HLS视频流')\r\n  })\r\n\r\n  // 暂停事件\r\n  video.addEventListener('pause', function () {\r\n    console.log('HLS视频流已暂停')\r\n  })\r\n\r\n  // 视频结束事件\r\n  video.addEventListener('ended', function () {\r\n    console.log('视频播放已结束')\r\n  })\r\n\r\n  // 音量变化事件\r\n  video.addEventListener('volumechange', function () {\r\n    console.log('音量变化')\r\n  })\r\n\r\n  // 添加错误事件监听\r\n  video.addEventListener('error', function (e) {\r\n    console.error('视频播放错误:', e)\r\n  })\r\n\r\n  // 添加加载开始事件\r\n  video.addEventListener('loadstart', function () {\r\n    console.log('开始加载视频')\r\n  })\r\n\r\n  // 添加元数据加载完成事件\r\n  video.addEventListener('loadedmetadata', function () {\r\n    console.log('视频元数据加载完成')\r\n  })\r\n}\r\n\r\n// 销毁视频播放器（包括直播和回放播放器）\r\nconst destroyVideoPlayer = () => {\r\n  console.log('destroyVideoPlayer: 开始销毁播放器')\r\n\r\n  // 销毁 hls.js 实例（直播和回放都可能使用）\r\n  if (hls.value) {\r\n    try {\r\n      console.log('destroyVideoPlayer: 销毁HLS实例')\r\n      hls.value.destroy()\r\n    } catch (error) {\r\n      console.error('销毁HLS实例错误:', error)\r\n    }\r\n    hls.value = null\r\n  }\r\n\r\n  // 销毁video元素播放器（直播和回放共用同一个video元素）\r\n  if (player.value) {\r\n    try {\r\n      console.log('destroyVideoPlayer: 清理player引用')\r\n      player.value.pause() // 停止播放\r\n      player.value.currentTime = 0 // 重置播放时间\r\n      player.value.src = '' // 清空视频源\r\n      player.value.removeAttribute('src') // 移除src属性\r\n      player.value.load() // 重新加载空的video元素\r\n      player.value.muted = false // 静音\r\n    } catch (error) {\r\n      console.error('销毁播放器错误:', error)\r\n    }\r\n    player.value = null\r\n  }\r\n\r\n  // 确保video元素也被清理\r\n  if (videoPlayer.value) {\r\n    try {\r\n      console.log('destroyVideoPlayer: 清理video元素')\r\n      videoPlayer.value.pause()\r\n      videoPlayer.value.currentTime = 0\r\n      videoPlayer.value.src = ''\r\n      videoPlayer.value.removeAttribute('src')\r\n      videoPlayer.value.load()\r\n      videoPlayer.value.muted = true\r\n    } catch (error) {\r\n      console.error('清理video元素错误:', error)\r\n    }\r\n  }\r\n\r\n  // 重置状态\r\n  isPlayerInitialized.value = false\r\n\r\n  console.log('视频播放器已完全销毁')\r\n}\r\n\r\n// 从推流地址中获取HLS地址\r\nconst getHlsUrl = (liveUrl) => {\r\n  if (!liveUrl) return null\r\n\r\n  console.log('原始推流地址:', liveUrl)\r\n\r\n  // 如果liveUrl是JSON格式，解析出HLS地址\r\n  try {\r\n    const urlData = JSON.parse(liveUrl)\r\n    console.log('解析的JSON数据:', urlData)\r\n    const hlsUrl = urlData.hls || urlData.m3u8 || liveUrl\r\n    console.log('提取的HLS地址:', hlsUrl)\r\n    return hlsUrl\r\n  } catch (error) {\r\n    console.log('不是JSON格式，直接使用原地址')\r\n    // 如果不是JSON格式，直接返回原地址\r\n    return liveUrl\r\n  }\r\n}\r\n\r\n// 监听回放状态变化\r\nwatch(() => props.isReplay, (isReplay) => {\r\n  console.log('VideoPlayer: isReplay状态变化为:', isReplay)\r\n  nextTick(() => {\r\n    initPlayer()\r\n  })\r\n})\r\n\r\n// 监听URL变化\r\nwatch(() => [props.liveUrl, props.replayUrl], ([newLiveUrl, newReplayUrl]) => {\r\n  console.log('VideoPlayer: URL变化检测')\r\n  console.log('VideoPlayer: 新的直播URL:', newLiveUrl)\r\n  console.log('VideoPlayer: 新的回放URL:', newReplayUrl)\r\n\r\n  if (props.autoInit) {\r\n    console.log('VideoPlayer: URL变化，重新初始化播放器')\r\n    nextTick(() => {\r\n      initPlayer()\r\n    })\r\n  }\r\n})\r\n\r\n// 统一的初始化方法\r\nconst initPlayer = () => {\r\n  console.log('VideoPlayer: 开始初始化播放器')\r\n  console.log('VideoPlayer: isReplay =', props.isReplay)\r\n  console.log('VideoPlayer: liveUrl =', props.liveUrl)\r\n  console.log('VideoPlayer: replayUrl =', props.replayUrl)\r\n\r\n  // 先销毁现有播放器，确保每次初始化都是干净的\r\n  console.log('VideoPlayer: 先销毁现有播放器')\r\n  destroyVideoPlayer()\r\n\r\n  // 强制重置状态\r\n  console.log('VideoPlayer: 重置播放器状态')\r\n  isPlayerInitialized.value = false\r\n\r\n  // 等待一小段时间确保销毁完成\r\n  setTimeout(() => {\r\n    if (props.isReplay) {\r\n      console.log('VideoPlayer: 初始化回放播放器')\r\n      initReplayPlayer()\r\n    } else {\r\n      console.log('VideoPlayer: 初始化直播播放器')\r\n      initVideoPlayer()\r\n    }\r\n  }, 50)\r\n}\r\n\r\nonMounted(() => {\r\n  console.log('VideoPlayer: 组件已挂载')\r\n  // 组件挂载后自动初始化播放器\r\n  if (props.autoInit) {\r\n    nextTick(() => {\r\n      initPlayer()\r\n    })\r\n  }\r\n})\r\n\r\nonBeforeUnmount(() => {\r\n  destroyVideoPlayer()\r\n})\r\n\r\n// 暴露方法给父组件\r\ndefineExpose({\r\n  initVideoPlayer,\r\n  initReplayPlayer,\r\n  destroyVideoPlayer,\r\n  initPlayer\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.video-player-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 2;\r\n\r\n  .player-container {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n    #video-player {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;+CAcA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,SAASE,GAAG,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,KAAK;AACtE,OAAOC,GAAG,MAAM,QAAQ;AALxB,IAAAC,WAAA,GAAe;EAAEjC,IAAI,EAAE;AAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;IAOtC,IAAMkC,KAAK,GAAGC,OAiBZ;;IAEF;IACA,IAAMC,WAAW,GAAGT,GAAG,CAAC,IAAI,CAAC;IAC7B,IAAMU,MAAM,GAAGV,GAAG,CAAC,IAAI,CAAC;IACxB,IAAMW,GAAG,GAAGX,GAAG,CAAC,IAAI,CAAC;IACrB,IAAMY,mBAAmB,GAAGZ,GAAG,CAAC,KAAK,CAAC;;IAEtC;IACA,IAAMa,gBAAe;MAAA,IAAAC,KAAA,GAAAnB,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAyC,QAAA;QAAA,IAAAC,KAAA,EAAAC,MAAA;QAAA,OAAA/H,mBAAA,GAAAuB,IAAA,UAAAyG,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAApC,IAAA,GAAAoC,QAAA,CAAA/D,IAAA;YAAA;cACtBgE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;cAC1CD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAET,mBAAmB,CAAChH,KAAK,CAAC;cAChFwH,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,CAAC,CAACZ,WAAW,CAAC7G,KAAK,CAAC;cAAA,IAEnE6G,WAAW,CAAC7G,KAAK;gBAAAuH,QAAA,CAAA/D,IAAA;gBAAA;cAAA;cACpBgE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;cAAA,OAAAF,QAAA,CAAAnE,MAAA;YAAA;cAIlD;cACAsE,kBAAkB,CAAC,CAAC;cAEdN,KAAK,GAAGP,WAAW,CAAC7G,KAAK;cAC/B8G,MAAM,CAAC9G,KAAK,GAAGoH,KAAK;cACpBJ,mBAAmB,CAAChH,KAAK,GAAG,IAAI;cAEhCwH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEd,KAAK,CAACgB,OAAO,CAAC;;cAEvD;cACMN,MAAM,GAAGO,SAAS,CAACjB,KAAK,CAACgB,OAAO,CAAC;cAEvCH,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEJ,MAAM,CAAC;cAAA,IAE7CA,MAAM;gBAAAE,QAAA,CAAA/D,IAAA;gBAAA;cAAA;cACTgE,OAAO,CAACK,IAAI,CAAC,yBAAyB,CAAC;cAAA,OAAAN,QAAA,CAAAnE,MAAA;YAAA;cAIzC;cACA,IAAIgE,KAAK,CAACU,WAAW,CAAC,+BAA+B,CAAC,EAAE;gBACtD;gBACAV,KAAK,CAACW,GAAG,GAAGV,MAAM;gBAClBW,gBAAgB,CAAC,CAAC;cACpB,CAAC,MAAM,IAAIvB,GAAG,CAACwB,WAAW,CAAC,CAAC,EAAE;gBAC5B;gBACAlB,GAAG,CAAC/G,KAAK,GAAG,IAAIyG,GAAG,CAAC;kBAClByB,eAAe,EAAE,EAAE;kBACnBC,kBAAkB,EAAE,EAAE;kBACtBC,UAAU,EAAE,CAAC,CAAC;kBAAE;kBAChBC,aAAa,EAAE,GAAG;kBAClBC,eAAe,EAAE;gBACnB,CAAC,CAAC;;gBAEF;gBACAvB,GAAG,CAAC/G,KAAK,CAACuI,UAAU,CAAClB,MAAM,CAAC;gBAC5BN,GAAG,CAAC/G,KAAK,CAACwI,WAAW,CAACpB,KAAK,CAAC;;gBAE5B;gBACAL,GAAG,CAAC/G,KAAK,CAACyI,EAAE,CAAChC,GAAG,CAACiC,MAAM,CAACC,eAAe,EAAE,YAAY;kBACnDnB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;gBACjC,CAAC,CAAC;;gBAEF;gBACAV,GAAG,CAAC/G,KAAK,CAACyI,EAAE,CAAChC,GAAG,CAACiC,MAAM,CAACE,KAAK,EAAE,UAAUC,CAAC,EAAEC,IAAI,EAAE;kBAChDtB,OAAO,CAACuB,KAAK,CAAC,QAAQ,EAAED,IAAI,CAAC;kBAC7B,QAAQA,IAAI,CAAC3H,IAAI;oBACf,KAAKsF,GAAG,CAACuC,UAAU,CAACC,aAAa;sBAC/BlC,GAAG,CAAC/G,KAAK,CAACkJ,SAAS,CAAC,CAAC,EAAC;sBACtB;oBACF,KAAKzC,GAAG,CAACuC,UAAU,CAACG,WAAW;sBAC7BpC,GAAG,CAAC/G,KAAK,CAACoJ,iBAAiB,CAAC,CAAC,EAAC;sBAC9B;oBACF;sBACE;sBACAC,UAAU,CAACpC,gBAAe,EAAE,IAAI,CAAC;sBACjC;kBACJ;gBACF,CAAC,CAAC;gBAEFe,gBAAgB,CAAC,CAAC;cACpB;YAAC;YAAA;cAAA,OAAAT,QAAA,CAAAjC,IAAA;UAAA;QAAA,GAAA6B,OAAA;MAAA,CACF;MAAA,gBAxEKF,eAAeA,CAAA;QAAA,OAAAC,KAAA,CAAAjB,KAAA,OAAAD,SAAA;MAAA;IAAA,GAwEpB;;IAED;IACA,IAAMsD,gBAAgB;MAAA,IAAAC,KAAA,GAAAxD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8E,SAAA;QAAA,IAAApC,KAAA,EAAAqC,SAAA;QAAA,OAAAnK,mBAAA,GAAAuB,IAAA,UAAA6I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,IAAA,GAAAwE,SAAA,CAAAnG,IAAA;YAAA;cACvBgE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;cAC3CD,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAET,mBAAmB,CAAChH,KAAK,CAAC;cACjFwH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,CAAC,CAACZ,WAAW,CAAC7G,KAAK,CAAC;cAAA,IAEpE6G,WAAW,CAAC7G,KAAK;gBAAA2J,SAAA,CAAAnG,IAAA;gBAAA;cAAA;cACpBgE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;cAAA,OAAAkC,SAAA,CAAAvG,MAAA;YAAA;cAInD;cACAsE,kBAAkB,CAAC,CAAC;cAEdN,KAAK,GAAGP,WAAW,CAAC7G,KAAK;cAC/B8G,MAAM,CAAC9G,KAAK,GAAGoH,KAAK;cACpBJ,mBAAmB,CAAChH,KAAK,GAAG,IAAI;cAEhCwH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEd,KAAK,CAAC8C,SAAS,CAAC;;cAE1D;cACMA,SAAS,GAAG7B,SAAS,CAACjB,KAAK,CAAC8C,SAAS,CAAC;cAE5CjC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgC,SAAS,CAAC;cAAA,IAEjDA,SAAS;gBAAAE,SAAA,CAAAnG,IAAA;gBAAA;cAAA;cACZgE,OAAO,CAACK,IAAI,CAAC,0BAA0B,CAAC;cAAA,OAAA8B,SAAA,CAAAvG,MAAA;YAAA;cAI1CoE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgC,SAAS,CAAC;;cAEnD;cACA,IAAIA,SAAS,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,SAAS,CAACG,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC5D;gBACA,IAAIxC,KAAK,CAACU,WAAW,CAAC,+BAA+B,CAAC,EAAE;kBACtD;kBACAV,KAAK,CAACW,GAAG,GAAG0B,SAAS;kBACrBzB,gBAAgB,CAAC,CAAC;gBACpB,CAAC,MAAM,IAAIvB,GAAG,CAACwB,WAAW,CAAC,CAAC,EAAE;kBAC5B;kBACAlB,GAAG,CAAC/G,KAAK,GAAG,IAAIyG,GAAG,CAAC;oBAClByB,eAAe,EAAE,EAAE;oBACnBC,kBAAkB,EAAE,EAAE;oBACtBC,UAAU,EAAE,CAAC,CAAC;oBACdC,aAAa,EAAE,GAAG;oBAClBC,eAAe,EAAE;kBACnB,CAAC,CAAC;kBAEFvB,GAAG,CAAC/G,KAAK,CAACuI,UAAU,CAACkB,SAAS,CAAC;kBAC/B1C,GAAG,CAAC/G,KAAK,CAACwI,WAAW,CAACpB,KAAK,CAAC;kBAE5BL,GAAG,CAAC/G,KAAK,CAACyI,EAAE,CAAChC,GAAG,CAACiC,MAAM,CAACC,eAAe,EAAE,YAAY;oBACnDnB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;kBACzB,CAAC,CAAC;kBAEFV,GAAG,CAAC/G,KAAK,CAACyI,EAAE,CAAChC,GAAG,CAACiC,MAAM,CAACE,KAAK,EAAE,UAAUC,CAAC,EAAEC,IAAI,EAAE;oBAChDtB,OAAO,CAACuB,KAAK,CAAC,UAAU,EAAED,IAAI,CAAC;kBACjC,CAAC,CAAC;kBAEFd,gBAAgB,CAAC,CAAC;gBACpB;cACF,CAAC,MAAM;gBACL;gBACAZ,KAAK,CAACW,GAAG,GAAG0B,SAAS;gBACrBzB,gBAAgB,CAAC,CAAC;cACpB;YAAC;YAAA;cAAA,OAAA2B,SAAA,CAAArE,IAAA;UAAA;QAAA,GAAAkE,QAAA;MAAA,CACF;MAAA,gBAlEKF,gBAAgBA,CAAA;QAAA,OAAAC,KAAA,CAAAtD,KAAA,OAAAD,SAAA;MAAA;IAAA,GAkErB;;IAED;IACA,IAAMgC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7BR,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAML,KAAK,GAAGN,MAAM,CAAC9G,KAAK;MAC1B,IAAI,CAACoH,KAAK,EAAE;QACVI,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD;MACF;MAEAD,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACAL,KAAK,CAACyC,gBAAgB,CAAC,SAAS,EAAE,YAAY;QAC5CrC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAChC,CAAC,CAAC;;MAEF;MACAL,KAAK,CAACyC,gBAAgB,CAAC,MAAM,EAAE,YAAY;QACzCrC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MAC3B,CAAC,CAAC;;MAEF;MACAL,KAAK,CAACyC,gBAAgB,CAAC,OAAO,EAAE,YAAY;QAC1CrC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MAC1B,CAAC,CAAC;;MAEF;MACAL,KAAK,CAACyC,gBAAgB,CAAC,OAAO,EAAE,YAAY;QAC1CrC,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACxB,CAAC,CAAC;;MAEF;MACAL,KAAK,CAACyC,gBAAgB,CAAC,cAAc,EAAE,YAAY;QACjDrC,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;MACrB,CAAC,CAAC;;MAEF;MACAL,KAAK,CAACyC,gBAAgB,CAAC,OAAO,EAAE,UAAUtK,CAAC,EAAE;QAC3CiI,OAAO,CAACuB,KAAK,CAAC,SAAS,EAAExJ,CAAC,CAAC;MAC7B,CAAC,CAAC;;MAEF;MACA6H,KAAK,CAACyC,gBAAgB,CAAC,WAAW,EAAE,YAAY;QAC9CrC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACvB,CAAC,CAAC;;MAEF;MACAL,KAAK,CAACyC,gBAAgB,CAAC,gBAAgB,EAAE,YAAY;QACnDrC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/BF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;MAE1C;MACA,IAAIV,GAAG,CAAC/G,KAAK,EAAE;QACb,IAAI;UACFwH,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;UAC1CV,GAAG,CAAC/G,KAAK,CAAC8J,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,OAAOf,KAAK,EAAE;UACdvB,OAAO,CAACuB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QACpC;QACAhC,GAAG,CAAC/G,KAAK,GAAG,IAAI;MAClB;;MAEA;MACA,IAAI8G,MAAM,CAAC9G,KAAK,EAAE;QAChB,IAAI;UACFwH,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7CX,MAAM,CAAC9G,KAAK,CAAC+J,KAAK,CAAC,CAAC,EAAC;UACrBjD,MAAM,CAAC9G,KAAK,CAACgK,WAAW,GAAG,CAAC,EAAC;UAC7BlD,MAAM,CAAC9G,KAAK,CAAC+H,GAAG,GAAG,EAAE,EAAC;UACtBjB,MAAM,CAAC9G,KAAK,CAACiK,eAAe,CAAC,KAAK,CAAC,EAAC;UACpCnD,MAAM,CAAC9G,KAAK,CAACkK,IAAI,CAAC,CAAC,EAAC;UACpBpD,MAAM,CAAC9G,KAAK,CAACmK,KAAK,GAAG,KAAK,EAAC;QAC7B,CAAC,CAAC,OAAOpB,KAAK,EAAE;UACdvB,OAAO,CAACuB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC;QACAjC,MAAM,CAAC9G,KAAK,GAAG,IAAI;MACrB;;MAEA;MACA,IAAI6G,WAAW,CAAC7G,KAAK,EAAE;QACrB,IAAI;UACFwH,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5CZ,WAAW,CAAC7G,KAAK,CAAC+J,KAAK,CAAC,CAAC;UACzBlD,WAAW,CAAC7G,KAAK,CAACgK,WAAW,GAAG,CAAC;UACjCnD,WAAW,CAAC7G,KAAK,CAAC+H,GAAG,GAAG,EAAE;UAC1BlB,WAAW,CAAC7G,KAAK,CAACiK,eAAe,CAAC,KAAK,CAAC;UACxCpD,WAAW,CAAC7G,KAAK,CAACkK,IAAI,CAAC,CAAC;UACxBrD,WAAW,CAAC7G,KAAK,CAACmK,KAAK,GAAG,IAAI;QAChC,CAAC,CAAC,OAAOpB,KAAK,EAAE;UACdvB,OAAO,CAACuB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACtC;MACF;;MAEA;MACA/B,mBAAmB,CAAChH,KAAK,GAAG,KAAK;MAEjCwH,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IAC3B,CAAC;;IAED;IACA,IAAMG,SAAS,GAAG,SAAZA,SAASA,CAAID,OAAO,EAAK;MAC7B,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;MAEzBH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEE,OAAO,CAAC;;MAE/B;MACA,IAAI;QACF,IAAMyC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC3C,OAAO,CAAC;QACnCH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2C,OAAO,CAAC;QAClC,IAAM/C,MAAM,GAAG+C,OAAO,CAACrD,GAAG,IAAIqD,OAAO,CAACG,IAAI,IAAI5C,OAAO;QACrDH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEJ,MAAM,CAAC;QAChC,OAAOA,MAAM;MACf,CAAC,CAAC,OAAO0B,KAAK,EAAE;QACdvB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/B;QACA,OAAOE,OAAO;MAChB;IACF,CAAC;;IAED;IACAnB,KAAK,CAAC;MAAA,OAAMG,KAAK,CAAC6D,QAAQ;IAAA,GAAE,UAACA,QAAQ,EAAK;MACxChD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+C,QAAQ,CAAC;MACpDjE,QAAQ,CAAC,YAAM;QACbkE,UAAU,CAAC,CAAC;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACAjE,KAAK,CAAC;MAAA,OAAM,CAACG,KAAK,CAACgB,OAAO,EAAEhB,KAAK,CAAC8C,SAAS,CAAC;IAAA,GAAE,UAAAiB,KAAA,EAAgC;MAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,KAAA;QAA9BG,UAAU,GAAAF,KAAA;QAAEG,YAAY,GAAAH,KAAA;MACtEnD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnCD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoD,UAAU,CAAC;MAChDrD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEqD,YAAY,CAAC;MAElD,IAAInE,KAAK,CAACoE,QAAQ,EAAE;QAClBvD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1ClB,QAAQ,CAAC,YAAM;UACbkE,UAAU,CAAC,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACA,IAAMA,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvBjD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpCD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEd,KAAK,CAAC6D,QAAQ,CAAC;MACtDhD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEd,KAAK,CAACgB,OAAO,CAAC;MACpDH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEd,KAAK,CAAC8C,SAAS,CAAC;;MAExD;MACAjC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpCC,kBAAkB,CAAC,CAAC;;MAEpB;MACAF,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnCT,mBAAmB,CAAChH,KAAK,GAAG,KAAK;;MAEjC;MACAqJ,UAAU,CAAC,YAAM;QACf,IAAI1C,KAAK,CAAC6D,QAAQ,EAAE;UAClBhD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;UACpC6B,gBAAgB,CAAC,CAAC;QACpB,CAAC,MAAM;UACL9B,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;UACpCR,gBAAe,CAAC,CAAC;QACnB;MACF,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IAEDX,SAAS,CAAC,YAAM;MACdkB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjC;MACA,IAAId,KAAK,CAACoE,QAAQ,EAAE;QAClBxE,QAAQ,CAAC,YAAM;UACbkE,UAAU,CAAC,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEFpE,eAAe,CAAC,YAAM;MACpBqB,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC;;IAEF;IACAsD,QAAY,CAAC;MACX/D,eAAe,EAAfA,gBAAe;MACfqC,gBAAgB;MAChB5B,kBAAkB;MAClB+C;IACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}