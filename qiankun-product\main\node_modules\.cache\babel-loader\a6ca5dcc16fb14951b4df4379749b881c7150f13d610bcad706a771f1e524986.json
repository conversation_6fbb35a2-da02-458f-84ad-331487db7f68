{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, inject, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue';\nimport { useStore } from 'vuex';\nimport { user } from 'common/js/system_var.js';\nimport { ElMessage } from 'element-plus';\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue';\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue';\nvar __default__ = {\n  name: 'AssistedWritingRD'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var suggestIcon = '<svg t=\"1742816739845\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"1527\" width=\"32\" height=\"32\"><path d=\"M1014.472563 517.57762l-105.551874-108.296797c-12.575579-12.575579-22.310481-8.170934-31.981548 0L420.611611 877.322154c-11.58613 12.352155-17.746248 111.073638-3.191772 130.543443 14.554477 19.469804 129.298652 14.36297 138.490953 6.447377l458.561771-462.487649c12.160648-15.192831 5.71327-28.311011 0-34.247705zM531.81292 975.245695h-1.053285c-17.490906 10.692434-62.814058 9.256137-76.187579 3.255606-5.170669-16.820634-2.042734-49.472454 2.553417-72.325535l436.315125-446.84797 73.091561 75.070459-434.719239 440.84744zM655.749396 491.947697H302.420323a30.928263 30.928263 0 0 1 0-61.79269h353.329073a30.928263 30.928263 0 0 1 0 61.79269z m-353.329073 219.434271a30.928263 30.928263 0 0 1 0-61.79269h129.905088a30.928263 30.928263 0 0 1 0 61.79269h-129.905088zM161.790883 461.051352c0 17.075976 13.437357 30.896345 30.034567 30.896345s30.034567-13.820369 30.034567-30.896345c0-17.075976-13.437357-30.896345-30.034567-30.896345s-30.034567 13.852287-30.034567 30.896345zM161.790883 680.485623c0 17.075976 13.437357 30.896345 30.034567 30.896345s30.034567-13.820369 30.034567-30.896345-13.437357-30.896345-30.034567-30.896345-30.034567 13.852287-30.034567 30.896345z\" p-id=\"1528\" fill=\"#b41916\"></path><path d=\"M269.034396 957.946295H62.750222V310.527421h211.135666c18.225014 0 33.002914-14.777901 33.002914-33.002914V65.973911h481.606359v235.105868a33.002914 33.002914 0 0 0 66.005829 0v-268.108782A33.034832 33.034832 0 0 0 821.466158 0H270.78987l-6.160119 1.755474a32.077301 32.077301 0 0 0-14.937489 10.564763L6.447378 253.777729c-2.553417 2.713006-4.659986 5.840941-6.160119 9.224219v3.0641a33.89661 33.89661 0 0 0 0 8.809289v716.041954c0 18.225014 14.777901 33.002914 33.002915 33.002915H269.066313a33.002914 33.002914 0 1 0-0.031917-65.973911zM240.882974 112.605688v131.947822H108.935152l131.947822-131.947822z\" p-id=\"1529\" fill=\"#b41916\"></path></svg>';\n    var openPage = inject('openPage');\n    var toolId = ref('');\n    var toolInfo = ref({});\n    var toolData = ref([]);\n    var toolIconData = {\n      suggestion: suggestIcon\n    };\n    var editorRef = ref();\n    var fileList = ref([]);\n    var fileData = ref([]);\n    var sendContent = ref('');\n    var handleFileUpload = function handleFileUpload(data) {\n      fileList.value = data;\n    };\n    var handleFileCallback = function handleFileCallback(data) {\n      fileData.value = data;\n    };\n    var handleClose = function handleClose(item) {\n      var _editorRef$value;\n      (_editorRef$value = editorRef.value) === null || _editorRef$value === void 0 || _editorRef$value.handleSetFile(fileData.value.filter(function (v) {\n        return v.id !== item.id;\n      }));\n    };\n    var handleTips = function handleTips(text) {\n      var parts = text.split(/(\\{[^}]+\\})/);\n      var result = parts.map(function (part) {\n        if (part.startsWith('{') && part.endsWith('}')) {\n          return {\n            value: part.slice(1, -1),\n            type: true\n          };\n        } else if (part.trim() !== '') {\n          return {\n            value: part,\n            type: false\n          };\n        }\n      }).filter(function (item) {\n        return item !== undefined;\n      });\n      return result;\n    };\n    var handleTool = function handleTool(item) {\n      var _editorRef$value2, _editorRef$value3;\n      toolInfo.value = item;\n      toolId.value = item.chatToolCode;\n      (_editorRef$value2 = editorRef.value) === null || _editorRef$value2 === void 0 || _editorRef$value2.handleSetFile([]);\n      (_editorRef$value3 = editorRef.value) === null || _editorRef$value3 === void 0 || _editorRef$value3.handleSetContent('');\n      nextTick(function () {\n        var _editorRef$value4;\n        (_editorRef$value4 = editorRef.value) === null || _editorRef$value4 === void 0 || _editorRef$value4.handleInsertPlaceholder(handleTips(item.userPromptTip));\n      });\n    };\n    var handleSendMessage = function handleSendMessage(value) {\n      var _editorRef$value5;\n      if (!toolId.value) return ElMessage({\n        type: 'warning',\n        message: '请先选择文档撰写类型！'\n      });\n      var openAiParams = {\n        toolId: toolInfo.value.id,\n        toolCode: toolId.value,\n        toolContent: value,\n        fileData: fileData.value\n      };\n      sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams));\n      if (toolId.value === 'suggestion') openPage({\n        key: 'routePath',\n        value: '/suggest/SubmitSuggest'\n      });\n      (_editorRef$value5 = editorRef.value) === null || _editorRef$value5 === void 0 || _editorRef$value5.handleSetFile([]);\n    };\n    var aigptChatSceneDetail = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _data$tools;\n        var _yield$api$aigptChatS, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.aigptChatSceneDetail({\n                query: {\n                  chatSceneCode: 'ai-intelligent-write-all-chat'\n                }\n              });\n            case 2:\n              _yield$api$aigptChatS = _context.sent;\n              data = _yield$api$aigptChatS.data;\n              toolData.value = (data === null || data === void 0 || (_data$tools = data.tools) === null || _data$tools === void 0 ? void 0 : _data$tools.filter(function (v) {\n                return v.isUsing;\n              })) || [];\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function aigptChatSceneDetail() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    onActivated(function () {\n      aigptChatSceneDetail();\n      store.commit('setAiChatElShow', false);\n    });\n    onDeactivated(function () {\n      store.commit('setAiChatElShow', true);\n    });\n    onUnmounted(function () {\n      store.commit('setAiChatElShow', true);\n    });\n    var __returned__ = {\n      store,\n      suggestIcon,\n      openPage,\n      toolId,\n      toolInfo,\n      toolData,\n      toolIconData,\n      editorRef,\n      fileList,\n      fileData,\n      sendContent,\n      handleFileUpload,\n      handleFileCallback,\n      handleClose,\n      handleTips,\n      handleTool,\n      handleSendMessage,\n      aigptChatSceneDetail,\n      get api() {\n        return api;\n      },\n      ref,\n      inject,\n      nextTick,\n      onActivated,\n      onDeactivated,\n      onUnmounted,\n      get useStore() {\n        return useStore;\n      },\n      get user() {\n        return user;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      GlobalAiChatFile,\n      GlobalAiChatEditor\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "inject", "nextTick", "onActivated", "onDeactivated", "onUnmounted", "useStore", "user", "ElMessage", "GlobalAiChatFile", "GlobalAiChatEditor", "__default__", "store", "suggestIcon", "openPage", "toolId", "toolInfo", "toolData", "toolIconData", "suggestion", "editor<PERSON><PERSON>", "fileList", "fileData", "send<PERSON><PERSON><PERSON>", "handleFileUpload", "data", "handleFileCallback", "handleClose", "item", "_editorRef$value", "handleSetFile", "filter", "id", "handleTips", "text", "parts", "split", "result", "map", "part", "startsWith", "endsWith", "trim", "undefined", "handleTool", "_editorRef$value2", "_editorRef$value3", "chatToolCode", "handleSetContent", "_editorRef$value4", "handleInsertPlaceholder", "userPromptTip", "handleSendMessage", "_editorRef$value5", "message", "openAiParams", "toolCode", "toolContent", "sessionStorage", "setItem", "JSON", "stringify", "key", "aigptChatSceneDetail", "_ref2", "_callee", "_data$tools", "_yield$api$aigptChatS", "_callee$", "_context", "query", "chatSceneCode", "tools", "isUsing", "commit"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AssistedWriting/AssistedWritingRD.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AssistedWriting\">\r\n    <div class=\"AssistedWritingBody\">\r\n      <div class=\"AssistedWritingUserBody\">\r\n        <div class=\"AssistedWritingUser\">\r\n          <el-image :src=\"user.image\" fit=\"cover\" />\r\n          <div class=\"AssistedWritingUserInfo\">\r\n            <div class=\"AssistedWritingUserName\">{{ user.userName }}</div>\r\n            <div class=\"AssistedWritingUserText\">{{ user.position }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"AssistedWritingTitle\">文档撰写类型</div>\r\n      <div class=\"AssistedWritingList\">\r\n        <div\r\n          class=\"AssistedWritingItem\"\r\n          :class=\"{ 'is-active': toolId === item.chatToolCode }\"\r\n          v-for=\"item in toolData\"\r\n          :key=\"item.chatToolCode\"\r\n          @click=\"handleTool(item)\">\r\n          <div class=\"AssistedWritingIcon\" v-html=\"toolIconData[item.chatToolCode]\"></div>\r\n          <div class=\"AssistedWritingName\">{{ item.chatToolName }}</div>\r\n        </div>\r\n        <div class=\"AssistedWritingPlaceholder\" v-for=\"item in 2\" :key=\"item + '_placeholder'\"></div>\r\n      </div>\r\n      <div class=\"AssistedWritingEditorBody\">\r\n        <GlobalAiChatFile\r\n          :fileList=\"fileList\"\r\n          :fileData=\"fileData\"\r\n          @close=\"handleClose\"\r\n          v-show=\"fileList.length || fileData.length\" />\r\n        <GlobalAiChatEditor\r\n          ref=\"editorRef\"\r\n          v-model=\"sendContent\"\r\n          @send=\"handleSendMessage\"\r\n          @uploadCallback=\"handleFileUpload\"\r\n          @fileCallback=\"handleFileCallback\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AssistedWritingRD' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, inject, nextTick, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalAiChatFile from '../GlobalAiChat/GlobalAiChatFile.vue'\r\nimport GlobalAiChatEditor from '../GlobalAiChat/GlobalAiChatEditor.vue'\r\nconst store = useStore()\r\nconst suggestIcon =\r\n  '<svg t=\"1742816739845\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"1527\" width=\"32\" height=\"32\"><path d=\"M1014.472563 517.57762l-105.551874-108.296797c-12.575579-12.575579-22.310481-8.170934-31.981548 0L420.611611 877.322154c-11.58613 12.352155-17.746248 111.073638-3.191772 130.543443 14.554477 19.469804 129.298652 14.36297 138.490953 6.447377l458.561771-462.487649c12.160648-15.192831 5.71327-28.311011 0-34.247705zM531.81292 975.245695h-1.053285c-17.490906 10.692434-62.814058 9.256137-76.187579 3.255606-5.170669-16.820634-2.042734-49.472454 2.553417-72.325535l436.315125-446.84797 73.091561 75.070459-434.719239 440.84744zM655.749396 491.947697H302.420323a30.928263 30.928263 0 0 1 0-61.79269h353.329073a30.928263 30.928263 0 0 1 0 61.79269z m-353.329073 219.434271a30.928263 30.928263 0 0 1 0-61.79269h129.905088a30.928263 30.928263 0 0 1 0 61.79269h-129.905088zM161.790883 461.051352c0 17.075976 13.437357 30.896345 30.034567 30.896345s30.034567-13.820369 30.034567-30.896345c0-17.075976-13.437357-30.896345-30.034567-30.896345s-30.034567 13.852287-30.034567 30.896345zM161.790883 680.485623c0 17.075976 13.437357 30.896345 30.034567 30.896345s30.034567-13.820369 30.034567-30.896345-13.437357-30.896345-30.034567-30.896345-30.034567 13.852287-30.034567 30.896345z\" p-id=\"1528\" fill=\"#b41916\"></path><path d=\"M269.034396 957.946295H62.750222V310.527421h211.135666c18.225014 0 33.002914-14.777901 33.002914-33.002914V65.973911h481.606359v235.105868a33.002914 33.002914 0 0 0 66.005829 0v-268.108782A33.034832 33.034832 0 0 0 821.466158 0H270.78987l-6.160119 1.755474a32.077301 32.077301 0 0 0-14.937489 10.564763L6.447378 253.777729c-2.553417 2.713006-4.659986 5.840941-6.160119 9.224219v3.0641a33.89661 33.89661 0 0 0 0 8.809289v716.041954c0 18.225014 14.777901 33.002914 33.002915 33.002915H269.066313a33.002914 33.002914 0 1 0-0.031917-65.973911zM240.882974 112.605688v131.947822H108.935152l131.947822-131.947822z\" p-id=\"1529\" fill=\"#b41916\"></path></svg>'\r\n\r\nconst openPage = inject('openPage')\r\nconst toolId = ref('')\r\nconst toolInfo = ref({})\r\nconst toolData = ref([])\r\nconst toolIconData = { suggestion: suggestIcon }\r\n\r\nconst editorRef = ref()\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst sendContent = ref('')\r\nconst handleFileUpload = (data) => {\r\n  fileList.value = data\r\n}\r\nconst handleFileCallback = (data) => {\r\n  fileData.value = data\r\n}\r\nconst handleClose = (item) => {\r\n  editorRef.value?.handleSetFile(fileData.value.filter((v) => v.id !== item.id))\r\n}\r\nconst handleTips = (text) => {\r\n  const parts = text.split(/(\\{[^}]+\\})/)\r\n  const result = parts\r\n    .map((part) => {\r\n      if (part.startsWith('{') && part.endsWith('}')) {\r\n        return { value: part.slice(1, -1), type: true }\r\n      } else if (part.trim() !== '') {\r\n        return { value: part, type: false }\r\n      }\r\n    })\r\n    .filter((item) => item !== undefined)\r\n  return result\r\n}\r\nconst handleTool = (item) => {\r\n  toolInfo.value = item\r\n  toolId.value = item.chatToolCode\r\n  editorRef.value?.handleSetFile([])\r\n  editorRef.value?.handleSetContent('')\r\n  nextTick(() => {\r\n    editorRef.value?.handleInsertPlaceholder(handleTips(item.userPromptTip))\r\n  })\r\n}\r\nconst handleSendMessage = (value) => {\r\n  if (!toolId.value) return ElMessage({ type: 'warning', message: '请先选择文档撰写类型！' })\r\n  const openAiParams = {\r\n    toolId: toolInfo.value.id,\r\n    toolCode: toolId.value,\r\n    toolContent: value,\r\n    fileData: fileData.value\r\n  }\r\n  sessionStorage.setItem('openAiParams', JSON.stringify(openAiParams))\r\n  if (toolId.value === 'suggestion') openPage({ key: 'routePath', value: '/suggest/SubmitSuggest' })\r\n  editorRef.value?.handleSetFile([])\r\n}\r\nconst aigptChatSceneDetail = async () => {\r\n  const { data } = await api.aigptChatSceneDetail({ query: { chatSceneCode: 'ai-intelligent-write-all-chat' } })\r\n  toolData.value = data?.tools?.filter((v) => v.isUsing) || []\r\n}\r\nonActivated(() => {\r\n  aigptChatSceneDetail()\r\n  store.commit('setAiChatElShow', false)\r\n})\r\nonDeactivated(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\nonUnmounted(() => {\r\n  store.commit('setAiChatElShow', true)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.AssistedWriting {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  position: relative;\r\n\r\n  .AssistedWritingBody {\r\n    padding: var(--zy-distance-two);\r\n  }\r\n\r\n  .AssistedWritingUserBody {\r\n    width: 800px;\r\n    border-radius: 6px 6px 6px 6px;\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .AssistedWritingUser {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: var(--zy-distance-two);\r\n\r\n      .zy-el-image {\r\n        width: 62px;\r\n        height: 62px;\r\n        border-radius: 50%;\r\n      }\r\n\r\n      .AssistedWritingUserInfo {\r\n        width: calc(100% - 62px);\r\n        height: 58px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        padding-left: var(--zy-distance-two);\r\n\r\n        .AssistedWritingUserName {\r\n          font-weight: bold;\r\n          line-height: var(--zy-line-height);\r\n          font-size: calc(var(--zy-name-font-size) + 2px);\r\n        }\r\n\r\n        .AssistedWritingUserText {\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-name-font-size);\r\n          color: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .AssistedWritingTitle {\r\n    width: 100%;\r\n    font-weight: bold;\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-name-font-size);\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .AssistedWritingList {\r\n    width: 800px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .AssistedWritingPlaceholder {\r\n      width: 185px;\r\n      height: 126px;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .AssistedWritingItem {\r\n      width: 185px;\r\n      height: 126px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      border-radius: 6px 6px 6px 6px;\r\n      border: 1px solid var(--zy-el-border-color-lighter);\r\n      padding: 20px;\r\n      margin-bottom: 20px;\r\n      cursor: pointer;\r\n\r\n      &.is-active {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AssistedWritingName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        box-shadow: var(--zy-el-box-shadow);\r\n        border-color: var(--zy-el-color-primary);\r\n        background: var(--zy-el-color-primary-light-9);\r\n\r\n        .AssistedWritingName {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .AssistedWritingIcon {\r\n        width: 48px;\r\n        height: 48px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n\r\n      .AssistedWritingName {\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-name-font-size);\r\n      }\r\n    }\r\n  }\r\n\r\n  .AssistedWritingEditorBody {\r\n    width: 800px;\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: 20px;\r\n    transform: translateX(-50%);\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalAiChatEditorContainer {\r\n      border-radius: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA8CA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,QAAQ,KAAK;AACpF,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,kBAAkB,MAAM,wCAAwC;AATvE,IAAAC,WAAA,GAAe;EAAEvC,IAAI,EAAE;AAAoB,CAAC;;;;;IAU5C,IAAMwC,KAAK,GAAGN,QAAQ,CAAC,CAAC;IACxB,IAAMO,WAAW,GACf,+9DAA+9D;IAEj+D,IAAMC,QAAQ,GAAGb,MAAM,CAAC,UAAU,CAAC;IACnC,IAAMc,MAAM,GAAGf,GAAG,CAAC,EAAE,CAAC;IACtB,IAAMgB,QAAQ,GAAGhB,GAAG,CAAC,CAAC,CAAC,CAAC;IACxB,IAAMiB,QAAQ,GAAGjB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMkB,YAAY,GAAG;MAAEC,UAAU,EAAEN;IAAY,CAAC;IAEhD,IAAMO,SAAS,GAAGpB,GAAG,CAAC,CAAC;IACvB,IAAMqB,QAAQ,GAAGrB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMsB,QAAQ,GAAGtB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMuB,WAAW,GAAGvB,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAMwB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;MACjCJ,QAAQ,CAAC1H,KAAK,GAAG8H,IAAI;IACvB,CAAC;IACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAID,IAAI,EAAK;MACnCH,QAAQ,CAAC3H,KAAK,GAAG8H,IAAI;IACvB,CAAC;IACD,IAAME,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAAA,IAAAC,gBAAA;MAC5B,CAAAA,gBAAA,GAAAT,SAAS,CAACzH,KAAK,cAAAkI,gBAAA,eAAfA,gBAAA,CAAiBC,aAAa,CAACR,QAAQ,CAAC3H,KAAK,CAACoI,MAAM,CAAC,UAACpG,CAAC;QAAA,OAAKA,CAAC,CAACqG,EAAE,KAAKJ,IAAI,CAACI,EAAE;MAAA,EAAC,CAAC;IAChF,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B,IAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,aAAa,CAAC;MACvC,IAAMC,MAAM,GAAGF,KAAK,CACjBG,GAAG,CAAC,UAACC,IAAI,EAAK;QACb,IAAIA,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,IAAID,IAAI,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;UAC9C,OAAO;YAAE9I,KAAK,EAAE4I,IAAI,CAACvD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAAElE,IAAI,EAAE;UAAK,CAAC;QACjD,CAAC,MAAM,IAAIyH,IAAI,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;UAC7B,OAAO;YAAE/I,KAAK,EAAE4I,IAAI;YAAEzH,IAAI,EAAE;UAAM,CAAC;QACrC;MACF,CAAC,CAAC,CACDiH,MAAM,CAAC,UAACH,IAAI;QAAA,OAAKA,IAAI,KAAKe,SAAS;MAAA,EAAC;MACvC,OAAON,MAAM;IACf,CAAC;IACD,IAAMO,UAAU,GAAG,SAAbA,UAAUA,CAAIhB,IAAI,EAAK;MAAA,IAAAiB,iBAAA,EAAAC,iBAAA;MAC3B9B,QAAQ,CAACrH,KAAK,GAAGiI,IAAI;MACrBb,MAAM,CAACpH,KAAK,GAAGiI,IAAI,CAACmB,YAAY;MAChC,CAAAF,iBAAA,GAAAzB,SAAS,CAACzH,KAAK,cAAAkJ,iBAAA,eAAfA,iBAAA,CAAiBf,aAAa,CAAC,EAAE,CAAC;MAClC,CAAAgB,iBAAA,GAAA1B,SAAS,CAACzH,KAAK,cAAAmJ,iBAAA,eAAfA,iBAAA,CAAiBE,gBAAgB,CAAC,EAAE,CAAC;MACrC9C,QAAQ,CAAC,YAAM;QAAA,IAAA+C,iBAAA;QACb,CAAAA,iBAAA,GAAA7B,SAAS,CAACzH,KAAK,cAAAsJ,iBAAA,eAAfA,iBAAA,CAAiBC,uBAAuB,CAACjB,UAAU,CAACL,IAAI,CAACuB,aAAa,CAAC,CAAC;MAC1E,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIzJ,KAAK,EAAK;MAAA,IAAA0J,iBAAA;MACnC,IAAI,CAACtC,MAAM,CAACpH,KAAK,EAAE,OAAO6G,SAAS,CAAC;QAAE1F,IAAI,EAAE,SAAS;QAAEwI,OAAO,EAAE;MAAc,CAAC,CAAC;MAChF,IAAMC,YAAY,GAAG;QACnBxC,MAAM,EAAEC,QAAQ,CAACrH,KAAK,CAACqI,EAAE;QACzBwB,QAAQ,EAAEzC,MAAM,CAACpH,KAAK;QACtB8J,WAAW,EAAE9J,KAAK;QAClB2H,QAAQ,EAAEA,QAAQ,CAAC3H;MACrB,CAAC;MACD+J,cAAc,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACN,YAAY,CAAC,CAAC;MACpE,IAAIxC,MAAM,CAACpH,KAAK,KAAK,YAAY,EAAEmH,QAAQ,CAAC;QAAEgD,GAAG,EAAE,WAAW;QAAEnK,KAAK,EAAE;MAAyB,CAAC,CAAC;MAClG,CAAA0J,iBAAA,GAAAjC,SAAS,CAACzH,KAAK,cAAA0J,iBAAA,eAAfA,iBAAA,CAAiBvB,aAAa,CAAC,EAAE,CAAC;IACpC,CAAC;IACD,IAAMiC,oBAAoB;MAAA,IAAAC,KAAA,GAAAtE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4F,QAAA;QAAA,IAAAC,WAAA;QAAA,IAAAC,qBAAA,EAAA1C,IAAA;QAAA,OAAAxI,mBAAA,GAAAuB,IAAA,UAAA4J,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAvF,IAAA,GAAAuF,QAAA,CAAAlH,IAAA;YAAA;cAAAkH,QAAA,CAAAlH,IAAA;cAAA,OACJ4C,GAAG,CAACgE,oBAAoB,CAAC;gBAAEO,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAgC;cAAE,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAE,QAAA,CAAAzH,IAAA;cAAtG6E,IAAI,GAAA0C,qBAAA,CAAJ1C,IAAI;cACZR,QAAQ,CAACtH,KAAK,GAAG,CAAA8H,IAAI,aAAJA,IAAI,gBAAAyC,WAAA,GAAJzC,IAAI,CAAE+C,KAAK,cAAAN,WAAA,uBAAXA,WAAA,CAAanC,MAAM,CAAC,UAACpG,CAAC;gBAAA,OAAKA,CAAC,CAAC8I,OAAO;cAAA,EAAC,KAAI,EAAE;YAAA;YAAA;cAAA,OAAAJ,QAAA,CAAApF,IAAA;UAAA;QAAA,GAAAgF,OAAA;MAAA,CAC7D;MAAA,gBAHKF,oBAAoBA,CAAA;QAAA,OAAAC,KAAA,CAAApE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAGzB;IACDQ,WAAW,CAAC,YAAM;MAChB4D,oBAAoB,CAAC,CAAC;MACtBnD,KAAK,CAAC8D,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;IACxC,CAAC,CAAC;IACFtE,aAAa,CAAC,YAAM;MAClBQ,KAAK,CAAC8D,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC;IACFrE,WAAW,CAAC,YAAM;MAChBO,KAAK,CAAC8D,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}