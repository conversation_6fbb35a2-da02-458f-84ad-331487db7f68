{"ast": null, "code": "import { resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createBlock(_resolveDynamicComponent($setup.WorkBenchElement[$setup.elCode]));\n}", "map": {"version": 3, "names": ["_createBlock", "_resolveDynamicComponent", "$setup", "WorkBenchElement", "elCode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\WorkBench\\WorkBench.vue"], "sourcesContent": ["<template>\r\n  <component :is=\"WorkBenchElement[elCode]\"></component>\r\n</template>\r\n<script>\r\nexport default { name: 'WorkBench' }\r\n</script>\r\n<script setup>\r\nimport { computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { WorkBenchElement } from './WorkBench'\r\nconst store = useStore()\r\nconst elCode = computed(() => {\r\n  const getElCode = store.getters.getWorkBenchElement || 'WorkBenchOne'\r\n  return WorkBenchElement[getElCode] ? getElCode : 'WorkBenchOne'\r\n})\r\n</script>\r\n"], "mappings": ";;uBACEA,YAAA,CAAsDC,wBADxD,CACkBC,MAAA,CAAAC,gBAAgB,CAACD,MAAA,CAAAE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}