{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { ref, onMounted, computed } from 'vue';\nimport { useStore } from 'vuex';\nimport { useRouter, useRoute } from 'vue-router';\nimport { Search, ArrowDown, Timer, Refresh, Warning, Close } from '@element-plus/icons-vue';\nimport { defaultPageSize, pageSizes } from 'common/js/system_var.js';\nimport api from '@/api';\nexport default {\n  __name: 'HotspotPush',\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var parsedReport = ref([]);\n    // 分类数据\n    var categories = ref(['全部', '政治', '经济', '社会', '民生', '文化', '生态环境', '科技']);\n    var activeCategory = ref('全部');\n    var keyword = ref('');\n    var warning = ref(0);\n    var isRotating = ref(false);\n    var iccList = ref([]);\n    var searchText = ref('');\n    var newsList = ref([]);\n    var currentPage = ref(1);\n    var pageSize = ref(defaultPageSize.value);\n    var total = ref(0);\n    // 分类点击处理函数\n    var handleCategoryClick = function handleCategoryClick(category, index) {\n      activeCategory.value = category;\n      miduDataTopicList(index);\n\n      // 这里可以添加切换分类后的其他逻辑，比如获取对应分类的新闻列表等\n    };\n    var handleSearch = function handleSearch() {\n      // 处理搜索逻辑\n      miduDataTopicList(0, searchText.value);\n    };\n    var openWebpage = function openWebpage(url) {\n      if (!url) return;\n\n      // 检查 URL 格式并确保它有正确的协议\n      var finalUrl = url;\n      if (url && !url.startsWith('http://') && !url.startsWith('https://')) {\n        finalUrl = 'https://' + url;\n      }\n\n      // 安全检查 - 确保 URL 是有效的\n      try {\n        new URL(finalUrl);\n      } catch (e) {\n        console.error('无效的 URL:', finalUrl);\n        return;\n      }\n\n      // 在新标签页中打开链接，添加安全属性\n      var newWindow = window.open();\n      if (newWindow) {\n        newWindow.opener = null; // 断开与打开者的联系，防止钓鱼攻击\n        newWindow.location = finalUrl;\n        newWindow.target = '_blank';\n        newWindow.rel = 'noopener noreferrer'; // 防止新页面访问 window.opener\n      } else {\n        // 如果弹出窗口被阻止，则直接导航\n        window.open(finalUrl, '_blank', 'noopener,noreferrer');\n      }\n    };\n    var miduDataScribePoll = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.miduDataScribePoll();\n            case 2:\n              res = _context.sent;\n              warning.value = res.data.length;\n              warningList.value = res.data.map(function (item) {\n                return {\n                  id: item.textId,\n                  title: item.title,\n                  desc: item.keyword,\n                  source: item.captureWebsite,\n                  time: item.publishTime,\n                  content: item.content\n                };\n              });\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function miduDataScribePoll() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var refreshNews = function refreshNews() {\n      isRotating.value = true;\n      // 模拟刷新操作\n      setTimeout(function () {\n        isRotating.value = false;\n      }, 1000); // 1秒后停止旋转\n      activeCategory.value = '全部';\n      miduDataTopicList(0);\n    };\n    var miduDataTopicList = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(params, searchText) {\n        var res, _res$data$, _res$data$2;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.miduDataTopicList({\n                types: params === 0 ? '1,2,3,4,5,6,7' : params,\n                page: currentPage.value,\n                pageSize: pageSize.value,\n                keyword: searchText\n              });\n            case 2:\n              res = _context2.sent;\n              newsList.value = res.data.map(function (item) {\n                return {\n                  id: item.id,\n                  title: item.name,\n                  date: item.occurTime,\n                  description: item.keyword\n                };\n              });\n              total.value = res.total || 0;\n              if (res.data.length > 0) {\n                miduDataTopicDetail((_res$data$ = res.data[0]) === null || _res$data$ === void 0 ? void 0 : _res$data$.id);\n                keyword.value = (_res$data$2 = res.data[0]) === null || _res$data$2 === void 0 ? void 0 : _res$data$2.keyword;\n              } else {\n                keyword.value = '';\n                parsedReport.value = [];\n                iccList.value = [];\n              }\n            case 6:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function miduDataTopicList(_x, _x2) {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var miduDataTopicDetail = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.next = 2;\n              return api.miduDataTopicDetail({\n                id: params\n              });\n            case 2:\n              res = _context3.sent;\n              parsedReport.value = parseReportText(res.data.reportText);\n              iccList.value = res.data.iccList;\n            case 5:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function miduDataTopicDetail(_x3) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var parseReportText = function parseReportText(reportText) {\n      var sections = [];\n      // 检查是否存在 # 或 ## 标题\n      var hasMainTitles = /^#\\s|^##\\s/m.test(reportText);\n      if (hasMainTitles) {\n        var sectionRegex = /# (.*?)\\n([\\s\\S]*?)(?=\\n# |$)/g;\n        var sectionMatch;\n        while ((sectionMatch = sectionRegex.exec(reportText)) !== null) {\n          var section = {\n            title: sectionMatch[1].trim(),\n            content: '',\n            subsections: []\n          };\n          var subsectionRegex = /## (.*?)\\n([\\s\\S]*?)(?=\\n## |$)/g;\n          var subsectionMatch = void 0;\n          while ((subsectionMatch = subsectionRegex.exec(sectionMatch[2])) !== null) {\n            var subsection = {\n              title: subsectionMatch[1].trim(),\n              content: '',\n              subsubsections: [],\n              items: [] // 确保 items 被初始化为一个空数组\n            };\n            var subsubsectionRegex = /### (.*?)\\n([\\s\\S]*?)(?=\\n### |$)/g;\n            var subsubsectionMatch = void 0;\n            while ((subsubsectionMatch = subsubsectionRegex.exec(subsectionMatch[2])) !== null) {\n              var subsubsection = {\n                title: subsubsectionMatch[1].trim(),\n                content: subsubsectionMatch[2] ? subsubsectionMatch[2].trim() : '',\n                items: []\n              };\n              var listRegex = /(?:###\\s*|-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\n              var listMatch = void 0;\n              while ((listMatch = listRegex.exec(subsubsection.content)) !== null) {\n                subsubsection.items.push(listMatch[1].trim());\n              }\n              if (subsubsection.items.length > 0) {\n                subsubsection.content = '';\n              }\n              subsection.subsubsections.push(subsubsection);\n            }\n            if (subsection.subsubsections.length === 0) {\n              var _listRegex = /(?:-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\n              var _listMatch = void 0;\n              while ((_listMatch = _listRegex.exec(subsectionMatch[2])) !== null) {\n                subsection.items.push(_listMatch[1].trim());\n              }\n              if (subsection.items.length === 0) {\n                subsection.content = subsectionMatch[2] ? subsectionMatch[2].trim() : '';\n              }\n            }\n            section.subsections.push(subsection);\n          }\n          if (section.subsections.length === 0) {\n            section.content = sectionMatch[2] ? sectionMatch[2].trim() : '';\n          }\n          sections.push(section);\n        }\n      } else {\n        var _section = {\n          title: '',\n          content: '',\n          subsections: [{\n            title: '',\n            content: '',\n            items: []\n          }]\n        };\n\n        // 匹配 ### 开头的行及其后续内容\n        var titleContentRegex = /### (.*?)(?:\\n([\\s\\S]*?)(?=\\n### |$))/g;\n        var match;\n        while ((match = titleContentRegex.exec(reportText)) !== null) {\n          var title = match[1].trim();\n          var content = match[2] ? match[2].trim() : '';\n\n          // 如果内容中包含列表项（以 - 或数字开头）\n          if (content) {\n            var listItems = [];\n            var _listRegex2 = /(?:-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\n            var _listMatch2 = void 0;\n            while ((_listMatch2 = _listRegex2.exec(content)) !== null) {\n              listItems.push(_listMatch2[1].trim());\n            }\n            _section.subsections[0].items.push({\n              title: title,\n              content: listItems.length > 0 ? listItems : content\n            });\n          } else {\n            _section.subsections[0].items.push({\n              title: title,\n              content: []\n            });\n          }\n        }\n        sections.push(_section);\n      }\n      console.log('sections', sections);\n      return sections;\n    };\n    // 新闻列表数据\n    var handleNewsClick = function handleNewsClick(news) {\n      // 处理新闻点击事件，比如跳转到新闻详情页\n      miduDataTopicDetail(news.id);\n      keyword.value = news.description;\n    };\n    // 导航状态控制\n    var activeNav = ref('domestic');\n\n    // 导航切换处理函数\n    var handleNavClick = function handleNavClick(nav) {\n      activeNav.value = nav;\n    };\n    var showWarningPopup = ref(false);\n    var router = useRouter();\n    var route = useRoute();\n    var warningList = ref([]);\n    var handleWarningClick = function handleWarningClick(item) {\n      showWarningPopup.value = false;\n      store.commit('setOpenRoute', {\n        name: '预警详情',\n        path: '/WarningDetail',\n        query: {\n          id: item.id\n        }\n      });\n    };\n\n    // 根据路由参数加载对应的新闻内容\n    onMounted(function () {\n      miduDataScribePoll();\n      miduDataTopicList(0);\n    });\n\n    // 高亮文本的方法\n    var highlightText = function highlightText(text, keyword) {\n      if (!keyword) return text;\n      var reg = new RegExp(keyword, 'gi');\n      return text.replace(reg, function (match) {\n        return `<span class=\"highlight\">${match}</span>`;\n      });\n    };\n\n    // 处理新闻列表项的高亮\n    var highlightedNewsList = computed(function () {\n      return newsList.value.map(function (item) {\n        return _objectSpread(_objectSpread({}, item), {}, {\n          title: searchText.value ? highlightText(item.title, searchText.value) : item.title,\n          description: searchText.value ? highlightText(item.description, searchText.value) : item.description\n        });\n      });\n    });\n    var highlightedParsedReport = computed(function () {\n      var _parsedReport$value$;\n      var subsectionContent = (_parsedReport$value$ = parsedReport.value[0]) === null || _parsedReport$value$ === void 0 || (_parsedReport$value$ = _parsedReport$value$.subsections[0]) === null || _parsedReport$value$ === void 0 || (_parsedReport$value$ = _parsedReport$value$.items[1]) === null || _parsedReport$value$ === void 0 ? void 0 : _parsedReport$value$.content;\n      if (!subsectionContent) return '';\n      return searchText.value ? highlightText(subsectionContent, searchText.value) : subsectionContent;\n    });\n    var highlightTextNew = function highlightTextNew(text) {\n      if (!searchText.value) return text;\n      var regex = new RegExp(`(${searchText.value})`, 'gi');\n      return text.replace(regex, '<span class=\"highlight\">$1</span>');\n    };\n\n    // 处理分页变化\n    var handlePageChange = function handlePageChange(page) {\n      currentPage.value = page;\n      miduDataTopicList(0, searchText.value);\n    };\n\n    // 处理每页条数变化\n    var handleSizeChange = function handleSizeChange(size) {\n      pageSize.value = size;\n      currentPage.value = 1;\n      miduDataTopicList(0, searchText.value);\n    };\n    var showMore = function showMore() {\n      // 处理查看更多的逻辑\n      console.log('查看更多新闻');\n    };\n    var __returned__ = {\n      store,\n      parsedReport,\n      categories,\n      activeCategory,\n      keyword,\n      warning,\n      isRotating,\n      iccList,\n      searchText,\n      newsList,\n      currentPage,\n      pageSize,\n      total,\n      handleCategoryClick,\n      handleSearch,\n      openWebpage,\n      miduDataScribePoll,\n      refreshNews,\n      miduDataTopicList,\n      miduDataTopicDetail,\n      parseReportText,\n      handleNewsClick,\n      activeNav,\n      handleNavClick,\n      showWarningPopup,\n      router,\n      route,\n      warningList,\n      handleWarningClick,\n      highlightText,\n      highlightedNewsList,\n      highlightedParsedReport,\n      highlightTextNew,\n      handlePageChange,\n      handleSizeChange,\n      showMore,\n      ref,\n      onMounted,\n      computed,\n      get useStore() {\n        return useStore;\n      },\n      get useRouter() {\n        return useRouter;\n      },\n      get useRoute() {\n        return useRoute;\n      },\n      get Search() {\n        return Search;\n      },\n      get ArrowDown() {\n        return ArrowDown;\n      },\n      get Timer() {\n        return Timer;\n      },\n      get Refresh() {\n        return Refresh;\n      },\n      get Warning() {\n        return Warning;\n      },\n      get Close() {\n        return Close;\n      },\n      get defaultPageSize() {\n        return defaultPageSize;\n      },\n      get pageSizes() {\n        return pageSizes;\n      },\n      get api() {\n        return api;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ref", "onMounted", "computed", "useStore", "useRouter", "useRoute", "Search", "ArrowDown", "Timer", "Refresh", "Warning", "Close", "defaultPageSize", "pageSizes", "api", "store", "parsedReport", "categories", "activeCategory", "keyword", "warning", "isRotating", "iccList", "searchText", "newsList", "currentPage", "pageSize", "total", "handleCategoryClick", "category", "index", "miduDataTopicList", "handleSearch", "openWebpage", "url", "finalUrl", "startsWith", "URL", "console", "error", "newWindow", "window", "open", "opener", "location", "target", "rel", "miduDataScribePoll", "_ref2", "_callee", "res", "_callee$", "_context", "data", "warningList", "map", "item", "id", "textId", "title", "desc", "source", "captureWebsite", "time", "publishTime", "content", "refreshNews", "setTimeout", "_ref3", "_callee2", "params", "_res$data$", "_res$data$2", "_callee2$", "_context2", "types", "page", "date", "occurTime", "description", "miduDataTopicDetail", "_x", "_x2", "_ref4", "_callee3", "_callee3$", "_context3", "parseReportText", "reportText", "_x3", "sections", "has<PERSON>ain<PERSON><PERSON><PERSON>", "test", "sectionRegex", "sectionMatch", "exec", "section", "trim", "subsections", "subsectionRegex", "subsectionMatch", "subsection", "subsubsections", "items", "subsubsectionRegex", "subsubsectionMatch", "subsubsection", "listRegex", "listMatch", "titleContentRegex", "match", "listItems", "log", "handleNewsClick", "news", "activeNav", "handleNavClick", "nav", "showWarningPopup", "router", "route", "handleWarningClick", "commit", "path", "query", "highlightText", "text", "reg", "RegExp", "replace", "highlightedNewsList", "_objectSpread", "highlightedParsedReport", "_parsedReport$value$", "subsectionContent", "highlightTextNew", "regex", "handlePageChange", "handleSizeChange", "size", "showMore"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/HotspotPush/HotspotPush.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"HotspotPushScrollbar\" always>\r\n    <div class=\"news-detail\">\r\n      <!-- 顶部导航 -->\r\n      <div class=\"top-nav\">\r\n        <div class=\"nav-content\">\r\n          <div class=\"nav-left\">\r\n            <div class=\"logo\">\r\n              <span class=\"focus\">聚焦</span>\r\n              <span class=\"hot\">热点</span>\r\n            </div>\r\n            <div class=\"search-area\">\r\n              <el-input v-model=\"searchText\" placeholder=\"请输入关键词\" class=\"search-input\" @keyup.enter=\"handleSearch\">\r\n                <template #suffix>\r\n                  <el-icon @click=\"handleSearch\">\r\n                    <Search />\r\n                  </el-icon>\r\n                </template>\r\n              </el-input>\r\n            </div>\r\n          </div>\r\n          <div class=\"nav-right\">\r\n            <!-- <span class=\"nav-item\" :class=\"{ active: activeNav === 'domestic' }\" @click=\"handleNavClick('domestic')\">\r\n            国内热点\r\n          </span>\r\n          <span class=\"nav-item\" :class=\"{ active: activeNav === 'international' }\"\r\n            @click=\"handleNavClick('international')\">\r\n            国际热点\r\n          </span> -->\r\n            <div class=\"user-info\" @click=\"showWarningPopup = true\">\r\n              <img :size=\"24\" src=\"../img/warning.png\" />\r\n              <div class=\"warning-info\">\r\n                <!-- <el-icon class=\"warning-icon\">\r\n                <Warning />\r\n              </el-icon> -->\r\n                <span class=\"warning-text\">预警信息</span>\r\n              </div>\r\n              <div class=\"badge-wrapper\">\r\n                <el-badge :value=\"warning\" class=\"badge\" :max=\"99\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"main-content\">\r\n        <!-- 左侧部分 -->\r\n        <div class=\"left-section\">\r\n          <!-- 热点分类 -->\r\n          <div class=\"hot-categories\">\r\n            <div class=\"category-header\">\r\n              <span>热点分类</span>\r\n              <!-- <div class=\"location\">\r\n              全国\r\n              <el-icon>\r\n                <ArrowDown />\r\n              </el-icon>\r\n            </div> -->\r\n            </div>\r\n            <div class=\"category-list\">\r\n              <div v-for=\"(category, index) in categories\" :key=\"index\" class=\"category-item\"\r\n                :class=\"{ active: activeCategory === category }\" @click=\"handleCategoryClick(category, index)\">\r\n                {{ category }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 实时热点 -->\r\n          <div class=\"realtime-news\">\r\n            <div class=\"news-header\">\r\n              <div class=\"header-left\">\r\n                <!-- <el-icon>\r\n                <Timer />\r\n              </el-icon> -->\r\n                <img src=\"../img/hot.png\" width=\"20px\" height=\"20px\" alt=\"\">\r\n                <span>实时热点</span>\r\n              </div>\r\n              <div class=\"refresh\" @click=\"refreshNews\">\r\n                <el-icon :class=\"{ rotating: isRotating }\">\r\n                  <Refresh />\r\n                </el-icon>\r\n                刷新\r\n              </div>\r\n            </div>\r\n            <div class=\"news-list\">\r\n              <div class=\"news-item\" v-for=\"(item, index) in highlightedNewsList\" :key=\"index\"\r\n                @click=\"handleNewsClick(item)\">\r\n                <div class=\"news-title\" v-html=\"item.title\"></div>\r\n                <div class=\"news-meta\">{{ item.date }}</div>\r\n              </div>\r\n              <div class=\"pagination-container\">\r\n                <el-pagination v-model:current-page=\"currentPage\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n                  :total=\"total\" layout=\"total, sizes, prev, next, jumper\" @size-change=\"handleSizeChange\"\r\n                  @current-change=\"handlePageChange\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧部分 -->\r\n        <div class=\"right-section\" v-if=\"parsedReport.length > 0\">\r\n          <div class=\"article-header\">\r\n            <h1 v-html=\"highlightTextNew(parsedReport[0]?.subsections[0]?.items[0]?.content)\"></h1>\r\n            <div class=\"tags\">\r\n              <el-tag :type=\"['primary', 'success', 'warning', 'info', 'danger'][index % 5]\"\r\n                v-for=\"(item, index) in keyword?.split('、')\" :key=\"index\">{{ item }}</el-tag>\r\n              <!-- <el-tag type=\"warning\">网络安全</el-tag> -->\r\n            </div>\r\n          </div>\r\n          <div class=\"article-content\" v-html=\"highlightedParsedReport\"></div>\r\n          <div class=\"article-sections\">\r\n            <div class=\"section\" v-for=\"(item, index) in parsedReport[0].subsections[0].items.slice(2)\" :key=\"index\">\r\n              <h3 v-html=\"highlightTextNew(item.title)\"></h3>\r\n              <div class=\"timeline\" v-if=\"Array.isArray(item.content)\">\r\n                <div class=\"timeline-item\" v-for=\"(event, index) in item.content\" :key=\"index\">\r\n                  <div class=\"dot\"></div>\r\n                  <div class=\"event-content\">\r\n                    <div class=\"event-text\" v-html=\"highlightTextNew(event)\"></div>\r\n                    <!-- <div class=\"event-source\" v-else>{{ event.content }}</div> -->\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"impact-content\" v-else v-html=\"highlightTextNew(item.content)\">\r\n              </div>\r\n            </div>\r\n            <div class=\"section\" v-if=\"iccList.length > 0\">\r\n              <div class=\"news-header\">\r\n                <h3>新闻报道</h3>\r\n                <span class=\"more\" @click=\"showMore\">更多 ></span>\r\n              </div>\r\n              <div class=\"news-media-list\">\r\n                <div class=\"news-media-item\" v-for=\"(item, index) in iccList\" :key=\"index\"\r\n                  @click=\"openWebpage(item.webpageUrl)\">\r\n                  <div class=\"news-media-content\">\r\n                    <span class=\"news-media-title\" v-html=\"highlightTextNew(item.title)\"></span>\r\n                    <span class=\"news-media-date\">{{ item.publishTime || '2025-02-13' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"right-section rigth-notdata\" v-else>\r\n          暂无数据\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <!-- 预警信息弹窗 -->\r\n      <div class=\"warning-popup-overlay\" v-if=\"showWarningPopup\" @click.self=\"showWarningPopup = false\">\r\n        <div class=\"warning-popup\">\r\n          <div class=\"popup-header\">\r\n            <div class=\"popup-title\">\r\n              <span>预警信息</span>\r\n              <span class=\"warning-count\">{{ warning }}</span>\r\n            </div>\r\n            <el-icon class=\"close-icon\" @click=\"showWarningPopup = false\">\r\n              <Close />\r\n            </el-icon>\r\n          </div>\r\n          <div class=\"popup-content\">\r\n            <div class=\"warning-item\" v-for=\"item in warningList\" :key=\"item.id\" @click=\"handleWarningClick(item)\">\r\n              <h3 class=\"warning-title\">{{ item.title }}</h3>\r\n              <p class=\"warning-desc\">{{ item.desc }}</p>\r\n              <div class=\"warning-meta\">\r\n                <span>来源：{{ item.source }}</span>\r\n                <span>时间：{{ item.time }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport { Search, ArrowDown, Timer, Refresh, Warning, Close } from '@element-plus/icons-vue'\r\nimport { defaultPageSize, pageSizes } from 'common/js/system_var.js'\r\nimport api from '@/api'\r\nconst store = useStore()\r\nconst parsedReport = ref([]);\r\n// 分类数据\r\nconst categories = ref(['全部', '政治', '经济', '社会', '民生', '文化', '生态环境', '科技'])\r\nconst activeCategory = ref('全部')\r\nconst keyword = ref('')\r\nconst warning = ref(0)\r\nconst isRotating = ref(false)\r\nconst iccList = ref([])\r\nconst searchText = ref('')\r\nconst newsList = ref([])\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(defaultPageSize.value)\r\nconst total = ref(0)\r\n// 分类点击处理函数\r\nconst handleCategoryClick = (category, index) => {\r\n  activeCategory.value = category\r\n  miduDataTopicList(index)\r\n\r\n  // 这里可以添加切换分类后的其他逻辑，比如获取对应分类的新闻列表等\r\n}\r\nconst handleSearch = () => {\r\n  // 处理搜索逻辑\r\n  miduDataTopicList(0, searchText.value)\r\n}\r\nconst openWebpage = (url) => {\r\n  if (!url) return;\r\n\r\n  // 检查 URL 格式并确保它有正确的协议\r\n  let finalUrl = url;\r\n  if (url && !url.startsWith('http://') && !url.startsWith('https://')) {\r\n    finalUrl = 'https://' + url;\r\n  }\r\n\r\n  // 安全检查 - 确保 URL 是有效的\r\n  try {\r\n    new URL(finalUrl);\r\n  } catch (e) {\r\n    console.error('无效的 URL:', finalUrl);\r\n    return;\r\n  }\r\n\r\n  // 在新标签页中打开链接，添加安全属性\r\n  const newWindow = window.open();\r\n  if (newWindow) {\r\n    newWindow.opener = null; // 断开与打开者的联系，防止钓鱼攻击\r\n    newWindow.location = finalUrl;\r\n    newWindow.target = '_blank';\r\n    newWindow.rel = 'noopener noreferrer'; // 防止新页面访问 window.opener\r\n  } else {\r\n    // 如果弹出窗口被阻止，则直接导航\r\n    window.open(finalUrl, '_blank', 'noopener,noreferrer');\r\n  }\r\n}\r\nconst miduDataScribePoll = async () => {\r\n  const res = await api.miduDataScribePoll()\r\n  warning.value = res.data.length\r\n  warningList.value = res.data.map(item => ({\r\n    id: item.textId,\r\n    title: item.title,\r\n    desc: item.keyword,\r\n    source: item.captureWebsite,\r\n    time: item.publishTime,\r\n    content: item.content\r\n  }))\r\n}\r\nconst refreshNews = () => {\r\n  isRotating.value = true;\r\n  // 模拟刷新操作\r\n  setTimeout(() => {\r\n    isRotating.value = false;\r\n  }, 1000); // 1秒后停止旋转\r\n  activeCategory.value = '全部'\r\n  miduDataTopicList(0)\r\n}\r\nconst miduDataTopicList = async (params, searchText) => {\r\n  const res = await api.miduDataTopicList({\r\n    types: params === 0 ? '1,2,3,4,5,6,7' : params,\r\n    page: currentPage.value,\r\n    pageSize: pageSize.value,\r\n    keyword: searchText\r\n  })\r\n  newsList.value = res.data.map(item => ({\r\n    id: item.id,\r\n    title: item.name,\r\n    date: item.occurTime,\r\n    description: item.keyword\r\n  }))\r\n  total.value = res.total || 0\r\n  if (res.data.length > 0) {\r\n    miduDataTopicDetail(res.data[0]?.id)\r\n    keyword.value = res.data[0]?.keyword\r\n  } else {\r\n    keyword.value = ''\r\n    parsedReport.value = []\r\n    iccList.value = []\r\n  }\r\n}\r\nconst miduDataTopicDetail = async (params) => {\r\n  const res = await api.miduDataTopicDetail({ id: params })\r\n  parsedReport.value = parseReportText(res.data.reportText);\r\n  iccList.value = res.data.iccList\r\n}\r\n\r\nconst parseReportText = (reportText) => {\r\n  const sections = [];\r\n  // 检查是否存在 # 或 ## 标题\r\n  const hasMainTitles = /^#\\s|^##\\s/m.test(reportText);\r\n  if (hasMainTitles) {\r\n\r\n    const sectionRegex = /# (.*?)\\n([\\s\\S]*?)(?=\\n# |$)/g;\r\n    let sectionMatch;\r\n\r\n    while ((sectionMatch = sectionRegex.exec(reportText)) !== null) {\r\n      const section = {\r\n        title: sectionMatch[1].trim(),\r\n        content: '',\r\n        subsections: []\r\n      };\r\n\r\n      const subsectionRegex = /## (.*?)\\n([\\s\\S]*?)(?=\\n## |$)/g;\r\n      let subsectionMatch;\r\n\r\n      while ((subsectionMatch = subsectionRegex.exec(sectionMatch[2])) !== null) {\r\n        const subsection = {\r\n          title: subsectionMatch[1].trim(),\r\n          content: '',\r\n          subsubsections: [],\r\n          items: []  // 确保 items 被初始化为一个空数组\r\n        };\r\n\r\n        const subsubsectionRegex = /### (.*?)\\n([\\s\\S]*?)(?=\\n### |$)/g;\r\n        let subsubsectionMatch;\r\n\r\n        while ((subsubsectionMatch = subsubsectionRegex.exec(subsectionMatch[2])) !== null) {\r\n          const subsubsection = {\r\n            title: subsubsectionMatch[1].trim(),\r\n            content: subsubsectionMatch[2] ? subsubsectionMatch[2].trim() : '',\r\n            items: []\r\n          };\r\n\r\n          const listRegex = /(?:###\\s*|-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\r\n          let listMatch;\r\n          while ((listMatch = listRegex.exec(subsubsection.content)) !== null) {\r\n            subsubsection.items.push(listMatch[1].trim());\r\n          }\r\n\r\n          if (subsubsection.items.length > 0) {\r\n            subsubsection.content = '';\r\n          }\r\n\r\n          subsection.subsubsections.push(subsubsection);\r\n        }\r\n\r\n        if (subsection.subsubsections.length === 0) {\r\n          const listRegex = /(?:-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\r\n          let listMatch;\r\n          while ((listMatch = listRegex.exec(subsectionMatch[2])) !== null) {\r\n            subsection.items.push(listMatch[1].trim());\r\n          }\r\n\r\n          if (subsection.items.length === 0) {\r\n            subsection.content = subsectionMatch[2] ? subsectionMatch[2].trim() : '';\r\n          }\r\n        }\r\n\r\n        section.subsections.push(subsection);\r\n      }\r\n\r\n      if (section.subsections.length === 0) {\r\n        section.content = sectionMatch[2] ? sectionMatch[2].trim() : '';\r\n      }\r\n\r\n      sections.push(section);\r\n    }\r\n  } else {\r\n    const section = {\r\n      title: '',\r\n      content: '',\r\n      subsections: [{\r\n        title: '',\r\n        content: '',\r\n        items: []\r\n      }]\r\n    };\r\n\r\n    // 匹配 ### 开头的行及其后续内容\r\n    const titleContentRegex = /### (.*?)(?:\\n([\\s\\S]*?)(?=\\n### |$))/g;\r\n    let match;\r\n\r\n    while ((match = titleContentRegex.exec(reportText)) !== null) {\r\n      const title = match[1].trim();\r\n      const content = match[2] ? match[2].trim() : '';\r\n\r\n      // 如果内容中包含列表项（以 - 或数字开头）\r\n      if (content) {\r\n        const listItems = [];\r\n        const listRegex = /(?:-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\r\n        let listMatch;\r\n\r\n        while ((listMatch = listRegex.exec(content)) !== null) {\r\n          listItems.push(listMatch[1].trim());\r\n        }\r\n\r\n        section.subsections[0].items.push({\r\n          title: title,\r\n          content: listItems.length > 0 ? listItems : content\r\n        });\r\n      } else {\r\n        section.subsections[0].items.push({\r\n          title: title,\r\n          content: []\r\n        });\r\n      }\r\n    }\r\n\r\n    sections.push(section);\r\n  }\r\n  console.log('sections', sections);\r\n\r\n  return sections;\r\n};\r\n// 新闻列表数据\r\nconst handleNewsClick = (news) => {\r\n  // 处理新闻点击事件，比如跳转到新闻详情页\r\n  miduDataTopicDetail(news.id)\r\n  keyword.value = news.description\r\n}\r\n// 导航状态控制\r\nconst activeNav = ref('domestic')\r\n\r\n// 导航切换处理函数\r\nconst handleNavClick = (nav) => {\r\n  activeNav.value = nav\r\n}\r\n\r\nconst showWarningPopup = ref(false)\r\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\n\r\nconst warningList = ref([])\r\n\r\nconst handleWarningClick = (item) => {\r\n  showWarningPopup.value = false\r\n  store.commit('setOpenRoute', { name: '预警详情', path: '/WarningDetail', query: { id: item.id } })\r\n}\r\n\r\n// 根据路由参数加载对应的新闻内容\r\nonMounted(() => {\r\n  miduDataScribePoll()\r\n  miduDataTopicList(0)\r\n})\r\n\r\n// 高亮文本的方法\r\nconst highlightText = (text, keyword) => {\r\n  if (!keyword) return text\r\n  const reg = new RegExp(keyword, 'gi')\r\n  return text.replace(reg, match => `<span class=\"highlight\">${match}</span>`)\r\n}\r\n\r\n// 处理新闻列表项的高亮\r\nconst highlightedNewsList = computed(() => {\r\n  return newsList.value.map(item => ({\r\n    ...item,\r\n    title: searchText.value ? highlightText(item.title, searchText.value) : item.title,\r\n    description: searchText.value ? highlightText(item.description, searchText.value) : item.description\r\n  }))\r\n})\r\nconst highlightedParsedReport = computed(() => {\r\n  const subsectionContent = parsedReport.value[0]?.subsections[0]?.items[1]?.content;\r\n  if (!subsectionContent) return '';\r\n  return searchText.value ? highlightText(subsectionContent, searchText.value) : subsectionContent;\r\n})\r\nconst highlightTextNew = (text) => {\r\n  if (!searchText.value) return text;\r\n  const regex = new RegExp(`(${searchText.value})`, 'gi');\r\n  return text.replace(regex, '<span class=\"highlight\">$1</span>');\r\n};\r\n\r\n// 处理分页变化\r\nconst handlePageChange = (page) => {\r\n  currentPage.value = page\r\n  miduDataTopicList(0, searchText.value)\r\n}\r\n\r\n// 处理每页条数变化\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n  miduDataTopicList(0, searchText.value)\r\n}\r\n\r\nconst showMore = () => {\r\n  // 处理查看更多的逻辑\r\n  console.log('查看更多新闻')\r\n}\r\n</script>\r\n<style scoped>\r\n/* 整体布局 */\r\n.news-detail {\r\n  min-height: 100vh;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.HotspotPushScrollbar {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.rotating {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  width: 100%;\r\n  background: #F2F3FF;\r\n  padding: 12px 0;\r\n}\r\n\r\n.nav-content {\r\n  /* max-width: 1200px; */\r\n  margin: 0 auto;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 50px;\r\n}\r\n\r\n.nav-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 24px;\r\n  margin-left: auto;\r\n}\r\n\r\n.logo {\r\n  width: 144px;\r\n  height: 44px;\r\n  font-family: \"Alimama ShuHeiTi\", sans-serif;\r\n  font-weight: bold;\r\n  font-size: 36px;\r\n  line-height: 42px;\r\n  text-align: left;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.focus {\r\n  color: #3657C0;\r\n}\r\n\r\n.hot {\r\n  color: #FF4B4B;\r\n}\r\n\r\n.search-area {\r\n  position: relative;\r\n}\r\n\r\n.search-input {\r\n  width: 600px;\r\n  height: 54px;\r\n\r\n  .search-icon {\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: #409EFF;\r\n    }\r\n  }\r\n}\r\n\r\n.search-input :deep(.el-input__wrapper) {\r\n  background-color: #fff;\r\n  box-shadow: none;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.search-input :deep(.el-input__suffix) {\r\n  color: #999;\r\n}\r\n\r\n.nav-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 32px;\r\n  margin-left: 10%;\r\n}\r\n\r\n.nav-item {\r\n  width: 75px;\r\n  height: 24px;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n  font-weight: 400;\r\n  font-size: 18px;\r\n  line-height: 21px;\r\n  text-align: left;\r\n  color: #333333;\r\n  cursor: pointer;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.nav-item.active {\r\n  color: #3657C0;\r\n  font-weight: 400;\r\n}\r\n\r\n.user-info {\r\n  width: 142px;\r\n  height: 54px;\r\n  background: #FFFFFF;\r\n  border-radius: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 0 16px;\r\n  position: relative;\r\n}\r\n\r\n.warning-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  position: relative;\r\n}\r\n\r\n.warning-icon {\r\n  font-size: 24px;\r\n  color: #333333;\r\n}\r\n\r\n.warning-text {\r\n  width: 72px;\r\n  height: 24px;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n  font-weight: 400;\r\n  font-size: 18px;\r\n  color: #333333;\r\n  line-height: 21px;\r\n  text-align: left;\r\n}\r\n\r\n.badge-wrapper {\r\n  position: absolute;\r\n  top: -6px;\r\n  right: -6px;\r\n  z-index: 1;\r\n}\r\n\r\n.badge :deep(.el-badge__content) {\r\n  background-color: #FF4B4B;\r\n  border: none;\r\n  height: 16px;\r\n  padding: 0 4px;\r\n  border-radius: 8px;\r\n  font-size: 12px;\r\n  font-weight: normal;\r\n  line-height: 16px;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  padding: 20px;\r\n  gap: 20px;\r\n}\r\n\r\n/* 左侧部分 */\r\n.left-section {\r\n  min-width: 430px !important;\r\n  max-width: 430px !important;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 热点分类 */\r\n.hot-categories {\r\n  background: white;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.location {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.category-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.category-item {\r\n  padding: 6px 12px;\r\n  border-radius: 15px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  cursor: pointer;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.category-item.active {\r\n  background: var(--zy-el-color-primary);\r\n  color: white;\r\n}\r\n\r\n/* 实时热点 */\r\n.realtime-news {\r\n  background: white;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n}\r\n\r\n.news-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.refresh {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #666;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.news-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.news-item {\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.news-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.news-meta {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 右侧部分 */\r\n.right-section {\r\n  flex: 1;\r\n  background: white;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  width: calc(100% - 430px);\r\n}\r\n\r\n.rigth-notdata {\r\n  display: flex;\r\n  align-items: center;\r\n  /* 垂直居中 */\r\n  justify-content: center;\r\n  /* 水平居中（可选） */\r\n}\r\n\r\n.article-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.article-header h1 {\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tags {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.article-content {\r\n  font-size: 14px;\r\n  line-height: 1.8;\r\n  color: #666;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n/* 文章各部分样式 */\r\n.article-sections {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30px;\r\n}\r\n\r\n.section {\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.section h3 {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.area-list {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.person-info {\r\n  display: flex;\r\n  gap: 15px;\r\n  background: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.person-detail {\r\n  flex: 1;\r\n}\r\n\r\n.person-detail .name {\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.person-detail .title {\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.person-detail .description {\r\n  font-size: 14px;\r\n  color: #999;\r\n  line-height: 1.6;\r\n}\r\n\r\n.timeline {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.timeline-item {\r\n  position: relative;\r\n  display: flex;\r\n  padding-left: 20px;\r\n}\r\n\r\n.dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 8px;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: var(--zy-el-color-primary);\r\n}\r\n\r\n.event-content {\r\n  flex: 1;\r\n}\r\n\r\n.event-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.event-source {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.impact-content,\r\n.reaction-content,\r\n.social-impact-content {\r\n  font-size: 14px;\r\n  line-height: 1.8;\r\n  color: #666;\r\n}\r\n\r\n.news-media {\r\n  position: relative;\r\n}\r\n\r\n.media-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n/* 当屏幕宽度小于1200px时，使用3列布局 */\r\n@media (max-width: 1200px) {\r\n  .media-grid {\r\n    grid-template-columns: repeat(3, 1fr);\r\n  }\r\n}\r\n\r\n/* 当屏幕宽度小于900px时，使用2列布局 */\r\n@media (max-width: 900px) {\r\n  .media-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n}\r\n\r\n/* 当屏幕宽度小于600px时，使用1列布局 */\r\n@media (max-width: 600px) {\r\n  .media-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n.media-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.media-item .el-image {\r\n  width: 100%;\r\n  height: 120px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.media-item .media-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.4;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  /* 显示两行 */\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.more {\r\n  position: absolute;\r\n  top: -40px;\r\n  right: 0;\r\n  color: #4169e1;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.warning-popup-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 2000;\r\n}\r\n\r\n.warning-popup {\r\n  width: 480px;\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 20px;\r\n  border-bottom: 1px solid #E4E7ED;\r\n}\r\n\r\n.popup-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.warning-count {\r\n  display: inline-block;\r\n  padding: 0 6px;\r\n  height: 20px;\r\n  line-height: 20px;\r\n  background: #FF4B4B;\r\n  color: #fff;\r\n  border-radius: 10px;\r\n  font-size: 12px;\r\n  font-weight: normal;\r\n}\r\n\r\n.close-icon {\r\n  font-size: 20px;\r\n  color: #999;\r\n  cursor: pointer;\r\n}\r\n\r\n.popup-content {\r\n  padding: 16px 20px;\r\n  max-height: 480px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.warning-item {\r\n  background: #F7F8FA;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  margin-bottom: 12px;\r\n  cursor: pointer;\r\n  border-left: 2px solid #FF4B4B;\r\n}\r\n\r\n.warning-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.warning-title {\r\n  font-size: 16px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.warning-desc {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.warning-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.highlight {\r\n  color: #FF4B4B;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 由于使用了 v-html，需要让 scoped 样式影响动态插入的内容 */\r\n:deep(.highlight) {\r\n  color: #FF4B4B;\r\n  font-weight: 500;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  padding: 10px 0;\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.pagination-container :deep(.el-pagination) {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.pagination-container :deep(.el-pagination .el-select .el-input) {\r\n  width: 70px;\r\n}\r\n\r\n.pagination-container :deep(.el-pagination__jump .el-input) {\r\n  width: 40px;\r\n  margin: 0 3px;\r\n}\r\n\r\n.pagination-container :deep(.el-pagination button) {\r\n  padding: 0 8px;\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n  .pagination-container :deep(.el-pagination) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n\r\n  .pagination-container :deep(.el-pagination .el-pagination__sizes),\r\n  .pagination-container :deep(.el-pagination .el-pagination__jump) {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n.news-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.news-header h3 {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n.more {\r\n  font-size: 14px;\r\n  color: #999;\r\n  cursor: pointer;\r\n}\r\n\r\n.news-media-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.news-media-item {\r\n  background: #f7f8fa;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  overflow: hidden;\r\n}\r\n\r\n.news-media-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px 20px;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.news-media-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-right: 16px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n}\r\n\r\n.news-media-date {\r\n  font-size: 14px;\r\n  color: #999;\r\n  white-space: nowrap;\r\n  width: 80px;\r\n  text-align: right;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.news-media-item:hover {\r\n  background: #f0f2f5;\r\n}\r\n</style>"], "mappings": ";;;;;+CAkLA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,SAASE,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,YAAY;AAChD,SAASC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,yBAAyB;AAC3F,SAASC,eAAe,EAAEC,SAAS,QAAQ,yBAAyB;AACpE,OAAOC,GAAG,MAAM,OAAO;;;;;;IACvB,IAAMC,KAAK,GAAGZ,QAAQ,CAAC,CAAC;IACxB,IAAMa,YAAY,GAAGhB,GAAG,CAAC,EAAE,CAAC;IAC5B;IACA,IAAMiB,UAAU,GAAGjB,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC1E,IAAMkB,cAAc,GAAGlB,GAAG,CAAC,IAAI,CAAC;IAChC,IAAMmB,OAAO,GAAGnB,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMoB,OAAO,GAAGpB,GAAG,CAAC,CAAC,CAAC;IACtB,IAAMqB,UAAU,GAAGrB,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMsB,OAAO,GAAGtB,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMuB,UAAU,GAAGvB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMwB,QAAQ,GAAGxB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMyB,WAAW,GAAGzB,GAAG,CAAC,CAAC,CAAC;IAC1B,IAAM0B,QAAQ,GAAG1B,GAAG,CAACY,eAAe,CAAChH,KAAK,CAAC;IAC3C,IAAM+H,KAAK,GAAG3B,GAAG,CAAC,CAAC,CAAC;IACpB;IACA,IAAM4B,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,QAAQ,EAAEC,KAAK,EAAK;MAC/CZ,cAAc,CAACtH,KAAK,GAAGiI,QAAQ;MAC/BE,iBAAiB,CAACD,KAAK,CAAC;;MAExB;IACF,CAAC;IACD,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzB;MACAD,iBAAiB,CAAC,CAAC,EAAER,UAAU,CAAC3H,KAAK,CAAC;IACxC,CAAC;IACD,IAAMqI,WAAW,GAAG,SAAdA,WAAWA,CAAIC,GAAG,EAAK;MAC3B,IAAI,CAACA,GAAG,EAAE;;MAEV;MACA,IAAIC,QAAQ,GAAGD,GAAG;MAClB,IAAIA,GAAG,IAAI,CAACA,GAAG,CAACE,UAAU,CAAC,SAAS,CAAC,IAAI,CAACF,GAAG,CAACE,UAAU,CAAC,UAAU,CAAC,EAAE;QACpED,QAAQ,GAAG,UAAU,GAAGD,GAAG;MAC7B;;MAEA;MACA,IAAI;QACF,IAAIG,GAAG,CAACF,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOhJ,CAAC,EAAE;QACVmJ,OAAO,CAACC,KAAK,CAAC,UAAU,EAAEJ,QAAQ,CAAC;QACnC;MACF;;MAEA;MACA,IAAMK,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,CAAC;MAC/B,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,MAAM,GAAG,IAAI,CAAC,CAAC;QACzBH,SAAS,CAACI,QAAQ,GAAGT,QAAQ;QAC7BK,SAAS,CAACK,MAAM,GAAG,QAAQ;QAC3BL,SAAS,CAACM,GAAG,GAAG,qBAAqB,CAAC,CAAC;MACzC,CAAC,MAAM;QACL;QACAL,MAAM,CAACC,IAAI,CAACP,QAAQ,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACxD;IACF,CAAC;IACD,IAAMY,kBAAkB;MAAA,IAAAC,KAAA,GAAArD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2E,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAhK,mBAAA,GAAAuB,IAAA,UAAA0I,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAArE,IAAA,GAAAqE,QAAA,CAAAhG,IAAA;YAAA;cAAAgG,QAAA,CAAAhG,IAAA;cAAA,OACP0D,GAAG,CAACiC,kBAAkB,CAAC,CAAC;YAAA;cAApCG,GAAG,GAAAE,QAAA,CAAAvG,IAAA;cACTuE,OAAO,CAACxH,KAAK,GAAGsJ,GAAG,CAACG,IAAI,CAACpF,MAAM;cAC/BqF,WAAW,CAAC1J,KAAK,GAAGsJ,GAAG,CAACG,IAAI,CAACE,GAAG,CAAC,UAAAC,IAAI;gBAAA,OAAK;kBACxCC,EAAE,EAAED,IAAI,CAACE,MAAM;kBACfC,KAAK,EAAEH,IAAI,CAACG,KAAK;kBACjBC,IAAI,EAAEJ,IAAI,CAACrC,OAAO;kBAClB0C,MAAM,EAAEL,IAAI,CAACM,cAAc;kBAC3BC,IAAI,EAAEP,IAAI,CAACQ,WAAW;kBACtBC,OAAO,EAAET,IAAI,CAACS;gBAChB,CAAC;cAAA,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAb,QAAA,CAAAlE,IAAA;UAAA;QAAA,GAAA+D,OAAA;MAAA,CACJ;MAAA,gBAXKF,kBAAkBA,CAAA;QAAA,OAAAC,KAAA,CAAAnD,KAAA,OAAAD,SAAA;MAAA;IAAA,GAWvB;IACD,IAAMsE,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB7C,UAAU,CAACzH,KAAK,GAAG,IAAI;MACvB;MACAuK,UAAU,CAAC,YAAM;QACf9C,UAAU,CAACzH,KAAK,GAAG,KAAK;MAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACVsH,cAAc,CAACtH,KAAK,GAAG,IAAI;MAC3BmI,iBAAiB,CAAC,CAAC,CAAC;IACtB,CAAC;IACD,IAAMA,iBAAiB;MAAA,IAAAqC,KAAA,GAAAzE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+F,SAAOC,MAAM,EAAE/C,UAAU;QAAA,IAAA2B,GAAA,EAAAqB,UAAA,EAAAC,WAAA;QAAA,OAAAtL,mBAAA,GAAAuB,IAAA,UAAAgK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,IAAA,GAAA2F,SAAA,CAAAtH,IAAA;YAAA;cAAAsH,SAAA,CAAAtH,IAAA;cAAA,OAC/B0D,GAAG,CAACiB,iBAAiB,CAAC;gBACtC4C,KAAK,EAAEL,MAAM,KAAK,CAAC,GAAG,eAAe,GAAGA,MAAM;gBAC9CM,IAAI,EAAEnD,WAAW,CAAC7H,KAAK;gBACvB8H,QAAQ,EAAEA,QAAQ,CAAC9H,KAAK;gBACxBuH,OAAO,EAAEI;cACX,CAAC,CAAC;YAAA;cALI2B,GAAG,GAAAwB,SAAA,CAAA7H,IAAA;cAMT2E,QAAQ,CAAC5H,KAAK,GAAGsJ,GAAG,CAACG,IAAI,CAACE,GAAG,CAAC,UAAAC,IAAI;gBAAA,OAAK;kBACrCC,EAAE,EAAED,IAAI,CAACC,EAAE;kBACXE,KAAK,EAAEH,IAAI,CAACnF,IAAI;kBAChBwG,IAAI,EAAErB,IAAI,CAACsB,SAAS;kBACpBC,WAAW,EAAEvB,IAAI,CAACrC;gBACpB,CAAC;cAAA,CAAC,CAAC;cACHQ,KAAK,CAAC/H,KAAK,GAAGsJ,GAAG,CAACvB,KAAK,IAAI,CAAC;cAC5B,IAAIuB,GAAG,CAACG,IAAI,CAACpF,MAAM,GAAG,CAAC,EAAE;gBACvB+G,mBAAmB,EAAAT,UAAA,GAACrB,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,cAAAkB,UAAA,uBAAXA,UAAA,CAAad,EAAE,CAAC;gBACpCtC,OAAO,CAACvH,KAAK,IAAA4K,WAAA,GAAGtB,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,cAAAmB,WAAA,uBAAXA,WAAA,CAAarD,OAAO;cACtC,CAAC,MAAM;gBACLA,OAAO,CAACvH,KAAK,GAAG,EAAE;gBAClBoH,YAAY,CAACpH,KAAK,GAAG,EAAE;gBACvB0H,OAAO,CAAC1H,KAAK,GAAG,EAAE;cACpB;YAAC;YAAA;cAAA,OAAA8K,SAAA,CAAAxF,IAAA;UAAA;QAAA,GAAAmF,QAAA;MAAA,CACF;MAAA,gBAtBKtC,iBAAiBA,CAAAkD,EAAA,EAAAC,GAAA;QAAA,OAAAd,KAAA,CAAAvE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAsBtB;IACD,IAAMoF,mBAAmB;MAAA,IAAAG,KAAA,GAAAxF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA8G,SAAOd,MAAM;QAAA,IAAApB,GAAA;QAAA,OAAAhK,mBAAA,GAAAuB,IAAA,UAAA4K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvG,IAAA,GAAAuG,SAAA,CAAAlI,IAAA;YAAA;cAAAkI,SAAA,CAAAlI,IAAA;cAAA,OACrB0D,GAAG,CAACkE,mBAAmB,CAAC;gBAAEvB,EAAE,EAAEa;cAAO,CAAC,CAAC;YAAA;cAAnDpB,GAAG,GAAAoC,SAAA,CAAAzI,IAAA;cACTmE,YAAY,CAACpH,KAAK,GAAG2L,eAAe,CAACrC,GAAG,CAACG,IAAI,CAACmC,UAAU,CAAC;cACzDlE,OAAO,CAAC1H,KAAK,GAAGsJ,GAAG,CAACG,IAAI,CAAC/B,OAAO;YAAA;YAAA;cAAA,OAAAgE,SAAA,CAAApG,IAAA;UAAA;QAAA,GAAAkG,QAAA;MAAA,CACjC;MAAA,gBAJKJ,mBAAmBA,CAAAS,GAAA;QAAA,OAAAN,KAAA,CAAAtF,KAAA,OAAAD,SAAA;MAAA;IAAA,GAIxB;IAED,IAAM2F,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,UAAU,EAAK;MACtC,IAAME,QAAQ,GAAG,EAAE;MACnB;MACA,IAAMC,aAAa,GAAG,aAAa,CAACC,IAAI,CAACJ,UAAU,CAAC;MACpD,IAAIG,aAAa,EAAE;QAEjB,IAAME,YAAY,GAAG,gCAAgC;QACrD,IAAIC,YAAY;QAEhB,OAAO,CAACA,YAAY,GAAGD,YAAY,CAACE,IAAI,CAACP,UAAU,CAAC,MAAM,IAAI,EAAE;UAC9D,IAAMQ,OAAO,GAAG;YACdrC,KAAK,EAAEmC,YAAY,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;YAC7BhC,OAAO,EAAE,EAAE;YACXiC,WAAW,EAAE;UACf,CAAC;UAED,IAAMC,eAAe,GAAG,kCAAkC;UAC1D,IAAIC,eAAe;UAEnB,OAAO,CAACA,eAAe,GAAGD,eAAe,CAACJ,IAAI,CAACD,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,EAAE;YACzE,IAAMO,UAAU,GAAG;cACjB1C,KAAK,EAAEyC,eAAe,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,CAAC;cAChChC,OAAO,EAAE,EAAE;cACXqC,cAAc,EAAE,EAAE;cAClBC,KAAK,EAAE,EAAE,CAAE;YACb,CAAC;YAED,IAAMC,kBAAkB,GAAG,oCAAoC;YAC/D,IAAIC,kBAAkB;YAEtB,OAAO,CAACA,kBAAkB,GAAGD,kBAAkB,CAACT,IAAI,CAACK,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,EAAE;cAClF,IAAMM,aAAa,GAAG;gBACpB/C,KAAK,EAAE8C,kBAAkB,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,CAAC;gBACnChC,OAAO,EAAEwC,kBAAkB,CAAC,CAAC,CAAC,GAAGA,kBAAkB,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,CAAC,GAAG,EAAE;gBAClEM,KAAK,EAAE;cACT,CAAC;cAED,IAAMI,SAAS,GAAG,wCAAwC;cAC1D,IAAIC,SAAS;cACb,OAAO,CAACA,SAAS,GAAGD,SAAS,CAACZ,IAAI,CAACW,aAAa,CAACzC,OAAO,CAAC,MAAM,IAAI,EAAE;gBACnEyC,aAAa,CAACH,KAAK,CAAC3I,IAAI,CAACgJ,SAAS,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC,CAAC,CAAC;cAC/C;cAEA,IAAIS,aAAa,CAACH,KAAK,CAACtI,MAAM,GAAG,CAAC,EAAE;gBAClCyI,aAAa,CAACzC,OAAO,GAAG,EAAE;cAC5B;cAEAoC,UAAU,CAACC,cAAc,CAAC1I,IAAI,CAAC8I,aAAa,CAAC;YAC/C;YAEA,IAAIL,UAAU,CAACC,cAAc,CAACrI,MAAM,KAAK,CAAC,EAAE;cAC1C,IAAM0I,UAAS,GAAG,iCAAiC;cACnD,IAAIC,UAAS;cACb,OAAO,CAACA,UAAS,GAAGD,UAAS,CAACZ,IAAI,CAACK,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,EAAE;gBAChEC,UAAU,CAACE,KAAK,CAAC3I,IAAI,CAACgJ,UAAS,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC,CAAC,CAAC;cAC5C;cAEA,IAAII,UAAU,CAACE,KAAK,CAACtI,MAAM,KAAK,CAAC,EAAE;gBACjCoI,UAAU,CAACpC,OAAO,GAAGmC,eAAe,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,CAAC,GAAG,EAAE;cAC1E;YACF;YAEAD,OAAO,CAACE,WAAW,CAACtI,IAAI,CAACyI,UAAU,CAAC;UACtC;UAEA,IAAIL,OAAO,CAACE,WAAW,CAACjI,MAAM,KAAK,CAAC,EAAE;YACpC+H,OAAO,CAAC/B,OAAO,GAAG6B,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG,EAAE;UACjE;UAEAP,QAAQ,CAAC9H,IAAI,CAACoI,OAAO,CAAC;QACxB;MACF,CAAC,MAAM;QACL,IAAMA,QAAO,GAAG;UACdrC,KAAK,EAAE,EAAE;UACTM,OAAO,EAAE,EAAE;UACXiC,WAAW,EAAE,CAAC;YACZvC,KAAK,EAAE,EAAE;YACTM,OAAO,EAAE,EAAE;YACXsC,KAAK,EAAE;UACT,CAAC;QACH,CAAC;;QAED;QACA,IAAMM,iBAAiB,GAAG,wCAAwC;QAClE,IAAIC,KAAK;QAET,OAAO,CAACA,KAAK,GAAGD,iBAAiB,CAACd,IAAI,CAACP,UAAU,CAAC,MAAM,IAAI,EAAE;UAC5D,IAAM7B,KAAK,GAAGmD,KAAK,CAAC,CAAC,CAAC,CAACb,IAAI,CAAC,CAAC;UAC7B,IAAMhC,OAAO,GAAG6C,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACb,IAAI,CAAC,CAAC,GAAG,EAAE;;UAE/C;UACA,IAAIhC,OAAO,EAAE;YACX,IAAM8C,SAAS,GAAG,EAAE;YACpB,IAAMJ,WAAS,GAAG,iCAAiC;YACnD,IAAIC,WAAS;YAEb,OAAO,CAACA,WAAS,GAAGD,WAAS,CAACZ,IAAI,CAAC9B,OAAO,CAAC,MAAM,IAAI,EAAE;cACrD8C,SAAS,CAACnJ,IAAI,CAACgJ,WAAS,CAAC,CAAC,CAAC,CAACX,IAAI,CAAC,CAAC,CAAC;YACrC;YAEAD,QAAO,CAACE,WAAW,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC3I,IAAI,CAAC;cAChC+F,KAAK,EAAEA,KAAK;cACZM,OAAO,EAAE8C,SAAS,CAAC9I,MAAM,GAAG,CAAC,GAAG8I,SAAS,GAAG9C;YAC9C,CAAC,CAAC;UACJ,CAAC,MAAM;YACL+B,QAAO,CAACE,WAAW,CAAC,CAAC,CAAC,CAACK,KAAK,CAAC3I,IAAI,CAAC;cAChC+F,KAAK,EAAEA,KAAK;cACZM,OAAO,EAAE;YACX,CAAC,CAAC;UACJ;QACF;QAEAyB,QAAQ,CAAC9H,IAAI,CAACoI,QAAO,CAAC;MACxB;MACA1D,OAAO,CAAC0E,GAAG,CAAC,UAAU,EAAEtB,QAAQ,CAAC;MAEjC,OAAOA,QAAQ;IACjB,CAAC;IACD;IACA,IAAMuB,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,IAAI,EAAK;MAChC;MACAlC,mBAAmB,CAACkC,IAAI,CAACzD,EAAE,CAAC;MAC5BtC,OAAO,CAACvH,KAAK,GAAGsN,IAAI,CAACnC,WAAW;IAClC,CAAC;IACD;IACA,IAAMoC,SAAS,GAAGnH,GAAG,CAAC,UAAU,CAAC;;IAEjC;IACA,IAAMoH,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,GAAG,EAAK;MAC9BF,SAAS,CAACvN,KAAK,GAAGyN,GAAG;IACvB,CAAC;IAED,IAAMC,gBAAgB,GAAGtH,GAAG,CAAC,KAAK,CAAC;IAEnC,IAAMuH,MAAM,GAAGnH,SAAS,CAAC,CAAC;IAC1B,IAAMoH,KAAK,GAAGnH,QAAQ,CAAC,CAAC;IAExB,IAAMiD,WAAW,GAAGtD,GAAG,CAAC,EAAE,CAAC;IAE3B,IAAMyH,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIjE,IAAI,EAAK;MACnC8D,gBAAgB,CAAC1N,KAAK,GAAG,KAAK;MAC9BmH,KAAK,CAAC2G,MAAM,CAAC,cAAc,EAAE;QAAErJ,IAAI,EAAE,MAAM;QAAEsJ,IAAI,EAAE,gBAAgB;QAAEC,KAAK,EAAE;UAAEnE,EAAE,EAAED,IAAI,CAACC;QAAG;MAAE,CAAC,CAAC;IAChG,CAAC;;IAED;IACAxD,SAAS,CAAC,YAAM;MACd8C,kBAAkB,CAAC,CAAC;MACpBhB,iBAAiB,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC;;IAEF;IACA,IAAM8F,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAE3G,OAAO,EAAK;MACvC,IAAI,CAACA,OAAO,EAAE,OAAO2G,IAAI;MACzB,IAAMC,GAAG,GAAG,IAAIC,MAAM,CAAC7G,OAAO,EAAE,IAAI,CAAC;MACrC,OAAO2G,IAAI,CAACG,OAAO,CAACF,GAAG,EAAE,UAAAjB,KAAK;QAAA,OAAI,2BAA2BA,KAAK,SAAS;MAAA,EAAC;IAC9E,CAAC;;IAED;IACA,IAAMoB,mBAAmB,GAAGhI,QAAQ,CAAC,YAAM;MACzC,OAAOsB,QAAQ,CAAC5H,KAAK,CAAC2J,GAAG,CAAC,UAAAC,IAAI;QAAA,OAAA2E,aAAA,CAAAA,aAAA,KACzB3E,IAAI;UACPG,KAAK,EAAEpC,UAAU,CAAC3H,KAAK,GAAGiO,aAAa,CAACrE,IAAI,CAACG,KAAK,EAAEpC,UAAU,CAAC3H,KAAK,CAAC,GAAG4J,IAAI,CAACG,KAAK;UAClFoB,WAAW,EAAExD,UAAU,CAAC3H,KAAK,GAAGiO,aAAa,CAACrE,IAAI,CAACuB,WAAW,EAAExD,UAAU,CAAC3H,KAAK,CAAC,GAAG4J,IAAI,CAACuB;QAAW;MAAA,CACpG,CAAC;IACL,CAAC,CAAC;IACF,IAAMqD,uBAAuB,GAAGlI,QAAQ,CAAC,YAAM;MAAA,IAAAmI,oBAAA;MAC7C,IAAMC,iBAAiB,IAAAD,oBAAA,GAAGrH,YAAY,CAACpH,KAAK,CAAC,CAAC,CAAC,cAAAyO,oBAAA,gBAAAA,oBAAA,GAArBA,oBAAA,CAAuBnC,WAAW,CAAC,CAAC,CAAC,cAAAmC,oBAAA,gBAAAA,oBAAA,GAArCA,oBAAA,CAAuC9B,KAAK,CAAC,CAAC,CAAC,cAAA8B,oBAAA,uBAA/CA,oBAAA,CAAiDpE,OAAO;MAClF,IAAI,CAACqE,iBAAiB,EAAE,OAAO,EAAE;MACjC,OAAO/G,UAAU,CAAC3H,KAAK,GAAGiO,aAAa,CAACS,iBAAiB,EAAE/G,UAAU,CAAC3H,KAAK,CAAC,GAAG0O,iBAAiB;IAClG,CAAC,CAAC;IACF,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIT,IAAI,EAAK;MACjC,IAAI,CAACvG,UAAU,CAAC3H,KAAK,EAAE,OAAOkO,IAAI;MAClC,IAAMU,KAAK,GAAG,IAAIR,MAAM,CAAC,IAAIzG,UAAU,CAAC3H,KAAK,GAAG,EAAE,IAAI,CAAC;MACvD,OAAOkO,IAAI,CAACG,OAAO,CAACO,KAAK,EAAE,mCAAmC,CAAC;IACjE,CAAC;;IAED;IACA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI7D,IAAI,EAAK;MACjCnD,WAAW,CAAC7H,KAAK,GAAGgL,IAAI;MACxB7C,iBAAiB,CAAC,CAAC,EAAER,UAAU,CAAC3H,KAAK,CAAC;IACxC,CAAC;;IAED;IACA,IAAM8O,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;MACjCjH,QAAQ,CAAC9H,KAAK,GAAG+O,IAAI;MACrBlH,WAAW,CAAC7H,KAAK,GAAG,CAAC;MACrBmI,iBAAiB,CAAC,CAAC,EAAER,UAAU,CAAC3H,KAAK,CAAC;IACxC,CAAC;IAED,IAAMgP,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrB;MACAtG,OAAO,CAAC0E,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}