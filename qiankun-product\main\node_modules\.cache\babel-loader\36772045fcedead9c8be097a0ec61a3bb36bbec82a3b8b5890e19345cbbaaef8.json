{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalAiChatEditorContainer\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalAiChatEditorBotton\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalAiChatEditorUpload\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalAiChatEditorFlex\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_upload = _resolveComponent(\"el-upload\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_scrollbar, {\n    always: \"\",\n    class: \"GlobalAiChatEditorScroll\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        ref: \"editorRef\",\n        contenteditable: \"true\",\n        class: \"GlobalAiChatEditor\",\n        onInput: $setup.handleInput,\n        onClick: $setup.handleEditorClick,\n        onKeydown: $setup.handleKeyDown,\n        onPaste: $setup.handlePaste\n      }, null, 544 /* NEED_HYDRATION, NEED_PATCH */)];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_upload, {\n    action: \"/\",\n    \"before-upload\": $setup.handleFile,\n    \"http-request\": $setup.fileUpload,\n    \"show-file-list\": false,\n    multiple: \"\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_button, {\n        icon: $setup.DocumentAdd\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"上传资料\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"icon\"])];\n    }),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString($setup.isMacText() ? 'Command + Enter 换行' : 'Ctrl + Enter 换行'), 1 /* TEXT */), !$setup.disabled ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 0,\n    type: \"primary\",\n    icon: $setup.Position,\n    circle: \"\",\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.handleSendMessage();\n    })\n  }, null, 8 /* PROPS */, [\"icon\"])) : _createCommentVNode(\"v-if\", true), $setup.disabled ? (_openBlock(), _createBlock(_component_el_button, {\n    key: 1,\n    type: \"primary\",\n    circle: \"\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.handleStopMessage();\n    })\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createElementVNode(\"span\", {\n        class: \"GlobalAiChatEditorStopStream\"\n      }, null, -1 /* HOISTED */)]);\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_scrollbar", "always", "default", "_withCtx", "_createElementVNode", "ref", "contenteditable", "onInput", "$setup", "handleInput", "onClick", "handleEditorClick", "onKeydown", "handleKeyDown", "onPaste", "handlePaste", "_", "_hoisted_2", "_hoisted_3", "_component_el_upload", "action", "handleFile", "fileUpload", "multiple", "_component_el_button", "icon", "DocumentAdd", "_cache", "_createTextVNode", "_hoisted_4", "_toDisplayString", "isMacText", "disabled", "_createBlock", "key", "type", "Position", "circle", "$event", "handleSendMessage", "_createCommentVNode", "handleStopMessage"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalAiChat\\GlobalAiChatEditor.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChatEditorContainer\">\r\n    <el-scrollbar always class=\"GlobalAiChatEditorScroll\">\r\n      <div\r\n        ref=\"editorRef\"\r\n        contenteditable=\"true\"\r\n        class=\"GlobalAiChatEditor\"\r\n        @input=\"handleInput\"\r\n        @click=\"handleEditorClick\"\r\n        @keydown=\"handleKeyDown\"\r\n        @paste=\"handlePaste\"></div>\r\n    </el-scrollbar>\r\n    <div class=\"GlobalAiChatEditorBotton\">\r\n      <div class=\"GlobalAiChatEditorUpload\">\r\n        <el-upload action=\"/\" :before-upload=\"handleFile\" :http-request=\"fileUpload\" :show-file-list=\"false\" multiple>\r\n          <el-button :icon=\"DocumentAdd\">上传资料</el-button>\r\n        </el-upload>\r\n      </div>\r\n      <div class=\"GlobalAiChatEditorFlex\">\r\n        <span>{{ isMacText() ? 'Command + Enter 换行' : 'Ctrl + Enter 换行' }}</span>\r\n        <el-button type=\"primary\" :icon=\"Position\" circle @click=\"handleSendMessage()\" v-if=\"!disabled\"></el-button>\r\n        <el-button type=\"primary\" circle @click=\"handleStopMessage()\" v-if=\"disabled\">\r\n          <span class=\"GlobalAiChatEditorStopStream\"></span>\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { DocumentAdd, Position } from '@element-plus/icons-vue'\r\nconst props = defineProps({\r\n  modelValue: [String, Number],\r\n  disabled: { type: Boolean, default: false }\r\n})\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst disabled = computed(() => props.disabled)\r\nconst emit = defineEmits(['update:modelValue', 'send', 'stop', 'uploadCallback', 'fileCallback'])\r\n// 编辑器的 DOM 引用\r\nconst editorRef = ref(null)\r\n// 存储编辑器内容\r\n// const content = ref('')\r\nconst content = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\n// 跟踪是否已经进入占位符编辑模式\r\nlet isEditingPlaceholder = ''\r\nconst isMacText = () => {\r\n  const userAgent = navigator.userAgent.toLowerCase()\r\n  return userAgent.includes('macintosh') || userAgent.includes('mac os x')\r\n}\r\nconst handleSendMessage = () => {\r\n  if (!content.value.replace(/^\\s+|\\s+$/g, '')) return\r\n  emit('send', content.value)\r\n  editorRef.value.innerHTML = ''\r\n  handleInput()\r\n}\r\nconst handleStopMessage = () => {\r\n  emit('stop', content.value)\r\n}\r\n// 初始化编辑器\r\nonMounted(() => {\r\n  const editorEl = editorRef.value\r\n  if (editorEl) {\r\n    editorEl.innerHTML = '' // 清空初始内容\r\n    handleInput()\r\n  }\r\n})\r\n// 检查当前光标是否在占位符内部\r\nconst isCursorInsidePlaceholder = () => {\r\n  const selection = window.getSelection()\r\n  if (!selection.rangeCount) return false\r\n  const range = selection.getRangeAt(0)\r\n  let node = range.startContainer\r\n  // 向上遍历 DOM 树，检查是否在占位符内部\r\n  while (node && node !== editorRef.value) {\r\n    if (node.classList && node.classList.contains('AiChatEditorPlaceholder')) return true // 光标在占位符内部\r\n    node = node.parentNode\r\n  }\r\n  return false // 光标不在占位符内部\r\n}\r\nconst createElPlaceholder = (data) => {\r\n  const elData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.type) {\r\n      // 创建占位符容器\r\n      const placeholderContainer = document.createElement('span')\r\n      placeholderContainer.className = 'AiChatEditorPlaceholderContainer'\r\n      // 创建占位符标签\r\n      const placeholder = document.createElement('span')\r\n      placeholder.className = 'AiChatEditorPlaceholder'\r\n      placeholder.contentEditable = 'false' // 默认不可编辑\r\n      placeholder.textContent = item.value\r\n      placeholder.dataset.id = guid()\r\n      placeholderContainer.appendChild(placeholder)\r\n      elData.push(placeholderContainer)\r\n    } else {\r\n      elData.push(document.createTextNode(item.value))\r\n    }\r\n  }\r\n  return elData\r\n}\r\n// 插入带样式的占位符\r\nconst handleInsertPlaceholder = (elDataText) => {\r\n  if (!elDataText.length) return\r\n  const editorEl = editorRef.value\r\n  if (!editorEl) return\r\n  editorEl.focus()\r\n  // 检查光标是否在占位符内部\r\n  if (isCursorInsidePlaceholder()) return // 不能在占位符内部插入占位符！\r\n  // 获取当前选中内容\r\n  const selection = window.getSelection()\r\n  if (!selection.rangeCount) return\r\n  const range = selection.getRangeAt(0)\r\n  // 如果选中了内容，先删除选中内容\r\n  if (!range.collapsed) range.deleteContents()\r\n  // 创建占位符容器\r\n  let placeholderContainer = null\r\n  const elData = createElPlaceholder(elDataText)\r\n  for (let index = elData.length - 1; index >= 0; index--) {\r\n    const item = elData[index]\r\n    range.insertNode(item)\r\n    if (index === elData.length - 1) {\r\n      placeholderContainer = item\r\n    }\r\n  }\r\n  // 插入占位符容器\r\n  // range.insertNode(placeholderContainer)\r\n  // 创建一个零宽空格节点\r\n  const space = document.createElement('br') // 插入 <br> 标签\r\n  // const space = document.createTextNode('\\u200B') // 零宽空格\r\n  // 将零宽空格插入到占位符容器的后面\r\n  range.setStartAfter(placeholderContainer)\r\n  range.setEndAfter(placeholderContainer)\r\n  range.insertNode(space)\r\n  // 将光标移动到零宽空格后面\r\n  const newRange = document.createRange()\r\n  newRange.setStartAfter(space)\r\n  newRange.setEndAfter(space)\r\n  selection.removeAllRanges()\r\n  selection.addRange(newRange)\r\n  // 聚焦编辑器\r\n  editorEl.focus()\r\n  handleInput()\r\n}\r\n// 监听编辑器内容变化\r\nconst handleInput = () => {\r\n  const editorEl = editorRef.value\r\n  if (editorEl) {\r\n    content.value = editorEl.innerHTML?.replace(/<br>/g, '\\n')?.replace(/<[^>]*>/g, '') || ''\r\n  }\r\n}\r\n// 处理编辑器点击事件\r\nconst handleEditorClick = (event) => {\r\n  const target = event.target\r\n  // 点击占位符文字时\r\n  if (target.classList.contains('AiChatEditorPlaceholder')) {\r\n    const elId = target.getAttribute('data-id')\r\n    if (isEditingPlaceholder !== elId) {\r\n      isEditingPlaceholder = elId\r\n      // 第一次点击，进入编辑模式\r\n      target.contentEditable = 'true'\r\n      target.focus()\r\n      // 选中整个占位符内容\r\n      const range = document.createRange()\r\n      range.selectNodeContents(target)\r\n      const selection = window.getSelection()\r\n      selection.removeAllRanges()\r\n      selection.addRange(range)\r\n    } else {\r\n      // 再次点击，将光标设置到点击的位置\r\n      const clickOffset = getClickOffset(target, event)\r\n      const selection = window.getSelection()\r\n      const range = document.createRange()\r\n      // 设置光标到点击的位置\r\n      range.setStart(target.firstChild, clickOffset)\r\n      range.setEnd(target.firstChild, clickOffset)\r\n      selection.removeAllRanges()\r\n      selection.addRange(range)\r\n    }\r\n  } else {\r\n    // 点击其他区域时退出编辑模式\r\n    isEditingPlaceholder = ''\r\n  }\r\n}\r\n// 获取点击位置在文本节点中的偏移量\r\nconst getClickOffset = (element, event) => {\r\n  const textNode = element.firstChild // 占位符的文本节点\r\n  const range = document.createRange()\r\n  range.selectNodeContents(textNode)\r\n  const rects = range.getClientRects() // 获取文本节点的所有矩形区域\r\n  const clickX = event.clientX // 点击的 X 坐标\r\n  // 遍历所有矩形区域，找到点击位置对应的偏移量\r\n  for (let i = 0; i < rects.length; i++) {\r\n    const rect = rects[i]\r\n    if (clickX >= rect.left && clickX <= rect.right) {\r\n      // 计算点击位置在文本节点中的偏移量\r\n      const relativeX = clickX - rect.left\r\n      const charWidth = rect.width / textNode.length\r\n      return Math.min(Math.floor(relativeX / charWidth), textNode.length)\r\n    }\r\n  }\r\n  // 如果点击位置超出文本节点范围，返回文本节点的长度\r\n  return textNode.length\r\n}\r\nconst hasBrAtEnd = (el) => {\r\n  const lastNode = el.lastElementChild\r\n  return lastNode && lastNode.tagName && lastNode.tagName.toLowerCase() === 'br'\r\n}\r\n// 处理键盘事件\r\nconst handleKeyDown = (event) => {\r\n  const target = event.target\r\n  if (event.keyCode == 13) {\r\n    event.preventDefault() // 阻止默认行为\r\n    if (!event.ctrlKey && !event.metaKey) {\r\n      handleSendMessage()\r\n    } else {\r\n      // 处理换行回车键，避免插入 <div> 或 <p>\r\n      const selection = window.getSelection()\r\n      if (selection.rangeCount > 0) {\r\n        const range = selection.getRangeAt(0)\r\n        const clonedRange = range.cloneRange()\r\n        clonedRange.selectNodeContents(target)\r\n        clonedRange.setEnd(range.endContainer, range.endOffset)\r\n        const text = clonedRange.toString()\r\n        const isPosition = text.length === editorRef.value.textContent.length\r\n        const br = document.createElement('br') // 插入 <br> 标签\r\n        const brOne = document.createElement('br') // 插入 <br> 标签\r\n        range.deleteContents() // 删除选中的内容（如果有）\r\n        if (isPosition && !hasBrAtEnd(target)) range.insertNode(brOne) // 插入 <br>\r\n        range.insertNode(br) // 插入 <br>\r\n        range.setStartAfter(br) // 将光标移动到 <br> 后面\r\n        range.setEndAfter(br)\r\n        selection.removeAllRanges()\r\n        selection.addRange(range)\r\n        handleInput()\r\n      }\r\n    }\r\n  }\r\n}\r\n// const handleKeyDownPlaceholder = (event) => {\r\n//   console.log(event)\r\n//   const target = event.target\r\n//   // 处理占位符内部的删除行为\r\n//   if (target.classList.contains('AiChatEditorPlaceholder')) {\r\n//     if (event.key === 'Backspace' || event.key === 'Delete') {\r\n//       // 如果占位符内容为空，删除整个占位符\r\n//       if (target.textContent === '') {\r\n//         event.preventDefault()\r\n//         const placeholderContainer = target.parentNode\r\n//         if (placeholderContainer && placeholderContainer.parentNode) {\r\n//           placeholderContainer.parentNode.removeChild(placeholderContainer)\r\n//         }\r\n//       }\r\n//     } else if (event.key === 'Enter') {\r\n//       // 如果按下 Enter 键，退出编辑模式\r\n//       event.preventDefault()\r\n//       target.contentEditable = 'false'\r\n//       const selection = window.getSelection()\r\n//       const range = document.createRange()\r\n//       range.setStartAfter(target)\r\n//       range.setEndAfter(target)\r\n//       selection.removeAllRanges()\r\n//       selection.addRange(range)\r\n//       isEditingPlaceholder = false // 退出编辑模式\r\n//     }\r\n//   }\r\n// }\r\n// 处理粘贴事件\r\nconst handlePaste = (event) => {\r\n  event.preventDefault() // 阻止默认粘贴行为\r\n  // 获取粘贴的纯文本内容\r\n  const text = (event.clipboardData || window.clipboardData).getData('text/plain')\r\n  // 将纯文本插入到光标位置\r\n  const selection = window.getSelection()\r\n  if (!selection.rangeCount) return\r\n  const range = selection.getRangeAt(0)\r\n  range.deleteContents() // 删除选中的内容（如果有）\r\n  // 插入纯文本\r\n  const textNode = document.createTextNode(text)\r\n  range.insertNode(textNode)\r\n  // 将光标移动到插入内容的后面\r\n  const newRange = document.createRange()\r\n  newRange.setStartAfter(textNode)\r\n  newRange.setEndAfter(textNode)\r\n  selection.removeAllRanges()\r\n  selection.addRange(newRange)\r\n  handleInput()\r\n}\r\nconst handleSetFile = (data) => {\r\n  fileData.value = data\r\n  emit('fileCallback', fileData.value)\r\n}\r\nconst handleSetContent = (contentData) => {\r\n  editorRef.value.innerHTML = contentData\r\n  handleInput()\r\n}\r\nconst handleAddContent = (contentData) => {\r\n  editorRef.value.innerHTML = editorRef.value.innerHTML + (contentData?.replace(/<[^>]*>/g, '') || '')\r\n  handleInput()\r\n}\r\n/**\r\n * 限制上传附件的文件类型\r\n */\r\nconst handleFile = () => {\r\n  // const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  // const isShow = ['doc', 'docx', 'pdf'].includes(fileType)\r\n  // if (!isShow) { ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!` }) }\r\n  // return isShow\r\n  return true\r\n}\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst onUploadProgress = (progressEvent, uid) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    fileList.value.forEach((item) => {\r\n      if (item.uid === uid) {\r\n        item.progress = parseInt(progress)\r\n      }\r\n    })\r\n  }\r\n}\r\n/**\r\n * 上传附件请求方法\r\n */\r\nconst fileUpload = (file) => {\r\n  const param = new FormData()\r\n  param.append('file', file.file)\r\n  globalUpload(param, guid(), file.file.name, file.file.uid, file.file.size)\r\n}\r\nconst globalUpload = async (params, uid, name, time, size) => {\r\n  try {\r\n    const fileType = name.substring(name.lastIndexOf('.') + 1)\r\n    fileList.value.push({ uid, fileName: name, fileType, fileSize: size, progress: 0 })\r\n    emit('uploadCallback', fileList.value)\r\n    const { data } = await api.globalUpload(params, onUploadProgress, uid)\r\n    fileList.value = fileList.value.filter((item) => item.uid !== uid)\r\n    emit('uploadCallback', fileList.value)\r\n    const newData = []\r\n    const newSortData = []\r\n    const newSucceedData = [...fileData.value, { ...data, uid: uid, time: time, progress: 100 }]\r\n    for (let index = 0; index < newSucceedData.length; index++) {\r\n      const item = newSucceedData[index]\r\n      if (item.time) {\r\n        newSortData.push(item)\r\n      } else {\r\n        newData.push(item)\r\n      }\r\n    }\r\n    fileData.value = [...newData, ...newSortData.sort((a, b) => a.time - b.time)]\r\n    emit('fileCallback', fileData.value)\r\n  } catch (err) {\r\n    fileList.value = fileList.value.filter((item) => item.uid !== uid)\r\n    emit('uploadCallback', fileList.value)\r\n  }\r\n}\r\ndefineExpose({ editorRef: editorRef.value, handleSetFile, handleSetContent, handleAddContent, handleInsertPlaceholder })\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalAiChatEditorContainer {\r\n  width: 100%;\r\n  height: 146px;\r\n  background: #fff;\r\n\r\n  .GlobalAiChatEditorScroll {\r\n    width: 100%;\r\n    height: calc(140px - var(--zy-height-routine));\r\n\r\n    .is-horizontal {\r\n      height: 9px;\r\n\r\n      .zy-el-scrollbar__thumb {\r\n        opacity: 0.5;\r\n\r\n        &:hover {\r\n          opacity: 0.9;\r\n        }\r\n      }\r\n    }\r\n\r\n    .is-vertical {\r\n      width: 6px;\r\n\r\n      .zy-el-scrollbar__thumb {\r\n        opacity: 0.5;\r\n\r\n        &:hover {\r\n          opacity: 0.9;\r\n        }\r\n      }\r\n    }\r\n\r\n    .zy-el-scrollbar__view {\r\n      padding: 6px 12px;\r\n    }\r\n\r\n    .GlobalAiChatEditor {\r\n      min-height: calc(120px - var(--zy-height-routine));\r\n      outline: none;\r\n      font-size: 14px;\r\n      line-height: 1.9;\r\n    }\r\n  }\r\n\r\n  .AiChatEditorPlaceholderContainer {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    line-height: normal;\r\n    padding: 0 2px;\r\n  }\r\n\r\n  .AiChatEditorPlaceholder {\r\n    display: inline-block;\r\n    padding: 4px 6px;\r\n    font-size: 14px;\r\n    line-height: 14px;\r\n    font-family: monospace;\r\n    color: #1976d2;\r\n    background: #e3f2fd;\r\n    border: 1px solid #90caf9;\r\n    border-radius: 4px;\r\n    user-select: none;\r\n    cursor: text;\r\n  }\r\n\r\n  .GlobalAiChatEditorBotton {\r\n    width: 100%;\r\n    height: var(--zy-height-routine);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 12px;\r\n\r\n    .GlobalAiChatEditorUpload {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .zy-el-button {\r\n        height: var(--zy-height-routine);\r\n        padding: 6px 12px;\r\n        font-weight: normal;\r\n\r\n        .zy-el-icon {\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatEditorFlex {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      & > span {\r\n        font-size: 12px;\r\n        color: var(--zy-el-text-color-regular);\r\n        padding: 0 12px;\r\n      }\r\n\r\n      .zy-el-button {\r\n        width: var(--zy-height-routine);\r\n        height: var(--zy-height-routine);\r\n        font-size: 16px;\r\n\r\n        .GlobalAiChatEditorStopStream {\r\n          width: 12px;\r\n          height: 12px;\r\n          display: inline-block;\r\n          background: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA6B;;EAWjCA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA0B;;EAKhCA,KAAK,EAAC;AAAwB;;;;;uBAjBvCC,mBAAA,CAyBM,OAzBNC,UAyBM,GAxBJC,YAAA,CASeC,uBAAA;IATDC,MAAM,EAAN,EAAM;IAACL,KAAK,EAAC;;IAF/BM,OAAA,EAAAC,QAAA,CAGM;MAAA,OAO6B,CAP7BC,mBAAA,CAO6B;QAN3BC,GAAG,EAAC,WAAW;QACfC,eAAe,EAAC,MAAM;QACtBV,KAAK,EAAC,oBAAoB;QACzBW,OAAK,EAAEC,MAAA,CAAAC,WAAW;QAClBC,OAAK,EAAEF,MAAA,CAAAG,iBAAiB;QACxBC,SAAO,EAAEJ,MAAA,CAAAK,aAAa;QACtBC,OAAK,EAAEN,MAAA,CAAAO;;;IAVhBC,CAAA;MAYIZ,mBAAA,CAaM,OAbNa,UAaM,GAZJb,mBAAA,CAIM,OAJNc,UAIM,GAHJnB,YAAA,CAEYoB,oBAAA;IAFDC,MAAM,EAAC,GAAG;IAAE,eAAa,EAAEZ,MAAA,CAAAa,UAAU;IAAG,cAAY,EAAEb,MAAA,CAAAc,UAAU;IAAG,gBAAc,EAAE,KAAK;IAAEC,QAAQ,EAAR;;IAd7GrB,OAAA,EAAAC,QAAA,CAeU;MAAA,OAA+C,CAA/CJ,YAAA,CAA+CyB,oBAAA;QAAnCC,IAAI,EAAEjB,MAAA,CAAAkB;MAAW;QAfvCxB,OAAA,EAAAC,QAAA,CAeyC;UAAA,OAAIwB,MAAA,QAAAA,MAAA,OAf7CC,gBAAA,CAeyC,MAAI,E;;QAf7CZ,CAAA;;;IAAAA,CAAA;QAkBMZ,mBAAA,CAMM,OANNyB,UAMM,GALJzB,mBAAA,CAAyE,cAAA0B,gBAAA,CAAhEtB,MAAA,CAAAuB,SAAS,+D,CACoEvB,MAAA,CAAAwB,QAAQ,I,cAA9FC,YAAA,CAA4GT,oBAAA;IApBpHU,GAAA;IAoBmBC,IAAI,EAAC,SAAS;IAAEV,IAAI,EAAEjB,MAAA,CAAA4B,QAAQ;IAAEC,MAAM,EAAN,EAAM;IAAE3B,OAAK,EAAAiB,MAAA,QAAAA,MAAA,gBAAAW,MAAA;MAAA,OAAE9B,MAAA,CAAA+B,iBAAiB;IAAA;uCApBnFC,mBAAA,gBAqB4EhC,MAAA,CAAAwB,QAAQ,I,cAA5EC,YAAA,CAEYT,oBAAA;IAvBpBU,GAAA;IAqBmBC,IAAI,EAAC,SAAS;IAACE,MAAM,EAAN,EAAM;IAAE3B,OAAK,EAAAiB,MAAA,QAAAA,MAAA,gBAAAW,MAAA;MAAA,OAAE9B,MAAA,CAAAiC,iBAAiB;IAAA;;IArBlEvC,OAAA,EAAAC,QAAA,CAsBU;MAAA,OAAkDwB,MAAA,QAAAA,MAAA,OAAlDvB,mBAAA,CAAkD;QAA5CR,KAAK,EAAC;MAA8B,2B;;IAtBpDoB,CAAA;QAAAwB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}