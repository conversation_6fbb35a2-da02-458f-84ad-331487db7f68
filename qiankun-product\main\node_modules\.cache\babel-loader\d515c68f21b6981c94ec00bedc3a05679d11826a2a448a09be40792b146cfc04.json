{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onActivated } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport SubmitAiReportGenera from './component/SubmitAiReportGenera';\nimport AiReportGeneraDetails from './component/AiReportGeneraDetails';\nvar __default__ = {\n  name: 'AiReportGeneraList'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [\n    // { id: 'new', name: '新增', type: 'primary', has: 'new' },\n    {\n      id: 'del',\n      name: '删除',\n      type: 'primary',\n      has: 'del'\n    }];\n    var tableButtonList = [{\n      id: 'edit',\n      name: '编辑',\n      width: 80,\n      has: 'edit'\n    }, {\n      id: 'view',\n      name: '查看',\n      width: 80,\n      has: ''\n    }];\n    var id = ref('');\n    var show = ref(false);\n    var isShow = ref(false);\n    var reportType = ref('');\n    var reportTypeData = ref([]);\n    var _GlobalTable = GlobalTable({\n        tableApi: 'aigptReportRecordList',\n        delApi: 'aigptReportRecordDel'\n      }),\n      keyword = _GlobalTable.keyword,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery,\n      tableDataArray = _GlobalTable.tableDataArray,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      handleDel = _GlobalTable.handleDel,\n      tableRefReset = _GlobalTable.tableRefReset,\n      tableQuery = _GlobalTable.tableQuery;\n    onActivated(function () {\n      handleQuery();\n      aigptReportRecordTypeList();\n    });\n    var aigptReportRecordTypeList = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _yield$api$aigptRepor, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.aigptReportRecordTypeList({\n                sceneCode: 'ai-general-report-main'\n              });\n            case 2:\n              _yield$api$aigptRepor = _context.sent;\n              data = _yield$api$aigptRepor.data;\n              reportTypeData.value = data;\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function aigptReportRecordTypeList() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'new':\n          handleNew();\n          break;\n        case 'del':\n          handleDel('敏感词');\n          break;\n        default:\n          break;\n      }\n    };\n    var handleCommand = function handleCommand(row, isType) {\n      switch (isType) {\n        case 'edit':\n          handleEdit(row);\n          break;\n        case 'view':\n          handleView(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var queryChange = function queryChange() {\n      tableQuery.value = {\n        query: {\n          reportType: reportType.value || null\n        }\n      };\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      reportType.value = '';\n      tableQuery.value = {\n        query: {\n          reportType: reportType.value || null\n        }\n      };\n      handleQuery();\n    };\n    var handleNew = function handleNew() {\n      id.value = '';\n      show.value = true;\n    };\n    var handleEdit = function handleEdit(item) {\n      id.value = item.id;\n      show.value = true;\n    };\n    var handleView = function handleView(item) {\n      id.value = item.id;\n      isShow.value = true;\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      show.value = false;\n    };\n    // 启用/禁用\n    var handleBatch = function handleBatch(type, text) {\n      if (tableDataArray.value.length) {\n        ElMessageBox.confirm(`此操作将${text}当前选中的敏感词, 是否继续?`, '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(function () {\n          AiReportGeneraListBatch({\n            batchType: type,\n            AiReportGeneraListIds: tableDataArray.value.map(function (v) {\n              return v.id;\n            })\n          }, `${text}成功`);\n        }).catch(function () {\n          ElMessage({\n            type: 'info',\n            message: `已取消${text}`\n          });\n        });\n      } else {\n        ElMessage({\n          type: 'warning',\n          message: '请至少选择一条数据'\n        });\n      }\n    };\n    var AiReportGeneraListBatch = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(params, text) {\n        var _yield$api$AiReportGe, code;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return api.AiReportGeneraListBatch(params);\n            case 2:\n              _yield$api$AiReportGe = _context2.sent;\n              code = _yield$api$AiReportGe.code;\n              if (code === 200) {\n                ElMessage({\n                  type: 'success',\n                  message: text\n                });\n                tableRefReset();\n                handleQuery();\n              }\n            case 5:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function AiReportGeneraListBatch(_x, _x2) {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      buttonList,\n      tableButtonList,\n      id,\n      show,\n      isShow,\n      reportType,\n      reportTypeData,\n      keyword,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      tableDataArray,\n      handleTableSelect,\n      handleDel,\n      tableRefReset,\n      tableQuery,\n      aigptReportRecordTypeList,\n      handleButton,\n      handleCommand,\n      queryChange,\n      handleReset,\n      handleNew,\n      handleEdit,\n      handleView,\n      callback,\n      handleBatch,\n      AiReportGeneraListBatch,\n      get api() {\n        return api;\n      },\n      ref,\n      onActivated,\n      get format() {\n        return format;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get SubmitAiReportGenera() {\n        return SubmitAiReportGenera;\n      },\n      get AiReportGeneraDetails() {\n        return AiReportGeneraDetails;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onActivated", "format", "GlobalTable", "ElMessage", "ElMessageBox", "SubmitAiReportGenera", "AiReportGeneraDetails", "__default__", "buttonList", "id", "has", "tableButtonList", "width", "show", "isShow", "reportType", "reportTypeData", "_GlobalTable", "tableApi", "del<PERSON><PERSON>", "keyword", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "tableDataArray", "handleTableSelect", "handleDel", "tableRefReset", "tableQuery", "aigptReportRecordTypeList", "_ref2", "_callee", "_yield$api$aigptRepor", "data", "_callee$", "_context", "sceneCode", "handleButton", "isType", "handleNew", "handleCommand", "row", "handleEdit", "handleView", "query<PERSON>hange", "query", "handleReset", "item", "callback", "handleBatch", "text", "confirm", "confirmButtonText", "cancelButtonText", "AiReportGeneraListBatch", "batchType", "AiReportGeneraListIds", "map", "message", "_ref3", "_callee2", "params", "_yield$api$AiReportGe", "code", "_callee2$", "_context2", "_x", "_x2"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiReportGenera/AiReportGeneraList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AiReportGeneraList\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n        <el-select v-model=\"reportType\" @change=\"queryChange\" placeholder=\"请选择报告类型\" clearable>\r\n          <el-option v-for=\"item in reportTypeData\" :key=\"item.id\" :label=\"item.label\" :value=\"item.id\" />\r\n        </el-select>\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <el-table-column label=\"报告类型\" min-width=\"160\" prop=\"reportTypeName\" />\r\n        <el-table-column label=\"报告内容\" min-width=\"280\" class-name=\"AiReportGeneraContent\">\r\n          <template #default=\"scope\">\r\n            <div v-html=\"scope.row.content?.replace(/<[^>]*>/g, '')\"></div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建人\" min-width=\"120\" prop=\"createUserName\" />\r\n        <el-table-column label=\"创建时间\" min-width=\"180\">\r\n          <template #default=\"scope\">{{ format(scope.row.createDate) }}</template>\r\n        </el-table-column>\r\n        <xyl-global-table-button :data=\"tableButtonList\" @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" :name=\"id ? '编辑报告' : '新增报告'\">\r\n      <SubmitAiReportGenera :id=\"id\" @callback=\"callback\"></SubmitAiReportGenera>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"isShow\" name=\"报告详情\">\r\n      <AiReportGeneraDetails :id=\"id\"></AiReportGeneraDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AiReportGeneraList' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport SubmitAiReportGenera from './component/SubmitAiReportGenera'\r\nimport AiReportGeneraDetails from './component/AiReportGeneraDetails'\r\nconst buttonList = [\r\n  // { id: 'new', name: '新增', type: 'primary', has: 'new' },\r\n  { id: 'del', name: '删除', type: 'primary', has: 'del' }\r\n]\r\nconst tableButtonList = [\r\n  { id: 'edit', name: '编辑', width: 80, has: 'edit' },\r\n  { id: 'view', name: '查看', width: 80, has: '' }\r\n]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst isShow = ref(false)\r\nconst reportType = ref('')\r\nconst reportTypeData = ref([])\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  tableDataArray,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset,\r\n  tableQuery\r\n} = GlobalTable({ tableApi: 'aigptReportRecordList', delApi: 'aigptReportRecordDel' })\r\n\r\nonActivated(() => {\r\n  handleQuery()\r\n  aigptReportRecordTypeList()\r\n})\r\n\r\nconst aigptReportRecordTypeList = async () => {\r\n  const { data } = await api.aigptReportRecordTypeList({ sceneCode: 'ai-general-report-main' })\r\n  reportTypeData.value = data\r\n}\r\n\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'del':\r\n      handleDel('敏感词')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    case 'view':\r\n      handleView(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst queryChange = () => {\r\n  tableQuery.value = { query: { reportType: reportType.value || null } }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  reportType.value = ''\r\n  tableQuery.value = { query: { reportType: reportType.value || null } }\r\n  handleQuery()\r\n}\r\nconst handleNew = () => {\r\n  id.value = ''\r\n  show.value = true\r\n}\r\nconst handleEdit = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst handleView = (item) => {\r\n  id.value = item.id\r\n  isShow.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n// 启用/禁用\r\nconst handleBatch = (type, text) => {\r\n  if (tableDataArray.value.length) {\r\n    ElMessageBox.confirm(`此操作将${text}当前选中的敏感词, 是否继续?`, '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(() => {\r\n      AiReportGeneraListBatch({ batchType: type, AiReportGeneraListIds: tableDataArray.value.map(v => v.id) }, `${text}成功`)\r\n    }).catch(() => { ElMessage({ type: 'info', message: `已取消${text}` }) })\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n  }\r\n}\r\nconst AiReportGeneraListBatch = async (params, text) => {\r\n  const { code } = await api.AiReportGeneraListBatch(params)\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: text })\r\n    tableRefReset()\r\n    handleQuery()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.AiReportGeneraList {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .xyl-search-button {\r\n    .xyl-button {\r\n      width: calc(100% - 660px);\r\n    }\r\n\r\n    .xyl-search {\r\n      width: 660px;\r\n\r\n      .zy-el-select {\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n\r\n    .AiReportGeneraContent {\r\n      .cell {\r\n        width: 100%;\r\n\r\n        div {\r\n          width: 100%;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA8CA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,OAAOC,qBAAqB,MAAM,mCAAmC;AATrE,IAAAC,WAAA,GAAe;EAAEpC,IAAI,EAAE;AAAqB,CAAC;;;;;IAU7C,IAAMqC,UAAU,GAAG;IACjB;IACA;MAAEC,EAAE,EAAE,KAAK;MAAEtC,IAAI,EAAE,IAAI;MAAEtD,IAAI,EAAE,SAAS;MAAE6F,GAAG,EAAE;IAAM,CAAC,CACvD;IACD,IAAMC,eAAe,GAAG,CACtB;MAAEF,EAAE,EAAE,MAAM;MAAEtC,IAAI,EAAE,IAAI;MAAEyC,KAAK,EAAE,EAAE;MAAEF,GAAG,EAAE;IAAO,CAAC,EAClD;MAAED,EAAE,EAAE,MAAM;MAAEtC,IAAI,EAAE,IAAI;MAAEyC,KAAK,EAAE,EAAE;MAAEF,GAAG,EAAE;IAAG,CAAC,CAC/C;IACD,IAAMD,EAAE,GAAGV,GAAG,CAAC,EAAE,CAAC;IAClB,IAAMc,IAAI,GAAGd,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMe,MAAM,GAAGf,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMgB,UAAU,GAAGhB,GAAG,CAAC,EAAE,CAAC;IAC1B,IAAMiB,cAAc,GAAGjB,GAAG,CAAC,EAAE,CAAC;IAC9B,IAAAkB,YAAA,GAcIf,WAAW,CAAC;QAAEgB,QAAQ,EAAE,uBAAuB;QAAEC,MAAM,EAAE;MAAuB,CAAC,CAAC;MAbpFC,OAAO,GAAAH,YAAA,CAAPG,OAAO;MACPC,QAAQ,GAAAJ,YAAA,CAARI,QAAQ;MACRC,MAAM,GAAAL,YAAA,CAANK,MAAM;MACNC,MAAM,GAAAN,YAAA,CAANM,MAAM;MACNC,QAAQ,GAAAP,YAAA,CAARO,QAAQ;MACRC,SAAS,GAAAR,YAAA,CAATQ,SAAS;MACTC,SAAS,GAAAT,YAAA,CAATS,SAAS;MACTC,WAAW,GAAAV,YAAA,CAAXU,WAAW;MACXC,cAAc,GAAAX,YAAA,CAAdW,cAAc;MACdC,iBAAiB,GAAAZ,YAAA,CAAjBY,iBAAiB;MACjBC,SAAS,GAAAb,YAAA,CAATa,SAAS;MACTC,aAAa,GAAAd,YAAA,CAAbc,aAAa;MACbC,UAAU,GAAAf,YAAA,CAAVe,UAAU;IAGZhC,WAAW,CAAC,YAAM;MAChB2B,WAAW,CAAC,CAAC;MACbM,yBAAyB,CAAC,CAAC;IAC7B,CAAC,CAAC;IAEF,IAAMA,yBAAyB;MAAA,IAAAC,KAAA,GAAAzC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA+D,QAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAArJ,mBAAA,GAAAuB,IAAA,UAAA+H,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA1D,IAAA,GAAA0D,QAAA,CAAArF,IAAA;YAAA;cAAAqF,QAAA,CAAArF,IAAA;cAAA,OACT4C,GAAG,CAACmC,yBAAyB,CAAC;gBAAEO,SAAS,EAAE;cAAyB,CAAC,CAAC;YAAA;cAAAJ,qBAAA,GAAAG,QAAA,CAAA5F,IAAA;cAArF0F,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZrB,cAAc,CAACtH,KAAK,GAAG2I,IAAI;YAAA;YAAA;cAAA,OAAAE,QAAA,CAAAvD,IAAA;UAAA;QAAA,GAAAmD,OAAA;MAAA,CAC5B;MAAA,gBAHKF,yBAAyBA,CAAA;QAAA,OAAAC,KAAA,CAAAvC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAG9B;IAED,IAAM+C,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,KAAK;UACRC,SAAS,CAAC,CAAC;UACX;QACF,KAAK,KAAK;UACRb,SAAS,CAAC,KAAK,CAAC;UAChB;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMc,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAEH,MAAM,EAAK;MACrC,QAAQA,MAAM;QACZ,KAAK,MAAM;UACTI,UAAU,CAACD,GAAG,CAAC;UACf;QACF,KAAK,MAAM;UACTE,UAAU,CAACF,GAAG,CAAC;UACf;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBhB,UAAU,CAACtI,KAAK,GAAG;QAAEuJ,KAAK,EAAE;UAAElC,UAAU,EAAEA,UAAU,CAACrH,KAAK,IAAI;QAAK;MAAE,CAAC;IACxE,CAAC;IACD,IAAMwJ,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB9B,OAAO,CAAC1H,KAAK,GAAG,EAAE;MAClBqH,UAAU,CAACrH,KAAK,GAAG,EAAE;MACrBsI,UAAU,CAACtI,KAAK,GAAG;QAAEuJ,KAAK,EAAE;UAAElC,UAAU,EAAEA,UAAU,CAACrH,KAAK,IAAI;QAAK;MAAE,CAAC;MACtEiI,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMgB,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtBlC,EAAE,CAAC/G,KAAK,GAAG,EAAE;MACbmH,IAAI,CAACnH,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMoJ,UAAU,GAAG,SAAbA,UAAUA,CAAIK,IAAI,EAAK;MAC3B1C,EAAE,CAAC/G,KAAK,GAAGyJ,IAAI,CAAC1C,EAAE;MAClBI,IAAI,CAACnH,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMqJ,UAAU,GAAG,SAAbA,UAAUA,CAAII,IAAI,EAAK;MAC3B1C,EAAE,CAAC/G,KAAK,GAAGyJ,IAAI,CAAC1C,EAAE;MAClBK,MAAM,CAACpH,KAAK,GAAG,IAAI;IACrB,CAAC;IACD,IAAM0J,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBrB,aAAa,CAAC,CAAC;MACfJ,WAAW,CAAC,CAAC;MACbd,IAAI,CAACnH,KAAK,GAAG,KAAK;IACpB,CAAC;IACD;IACA,IAAM2J,WAAW,GAAG,SAAdA,WAAWA,CAAIxI,IAAI,EAAEyI,IAAI,EAAK;MAClC,IAAI1B,cAAc,CAAClI,KAAK,CAACqE,MAAM,EAAE;QAC/BqC,YAAY,CAACmD,OAAO,CAAC,OAAOD,IAAI,iBAAiB,EAAE,IAAI,EAAE;UACvDE,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtB5I,IAAI,EAAE;QACR,CAAC,CAAC,CAACuB,IAAI,CAAC,YAAM;UACZsH,uBAAuB,CAAC;YAAEC,SAAS,EAAE9I,IAAI;YAAE+I,qBAAqB,EAAEhC,cAAc,CAAClI,KAAK,CAACmK,GAAG,CAAC,UAAAnI,CAAC;cAAA,OAAIA,CAAC,CAAC+E,EAAE;YAAA;UAAE,CAAC,EAAE,GAAG6C,IAAI,IAAI,CAAC;QACvH,CAAC,CAAC,CAACjE,KAAK,CAAC,YAAM;UAAEc,SAAS,CAAC;YAAEtF,IAAI,EAAE,MAAM;YAAEiJ,OAAO,EAAE,MAAMR,IAAI;UAAG,CAAC,CAAC;QAAC,CAAC,CAAC;MACxE,CAAC,MAAM;QACLnD,SAAS,CAAC;UAAEtF,IAAI,EAAE,SAAS;UAAEiJ,OAAO,EAAE;QAAY,CAAC,CAAC;MACtD;IACF,CAAC;IACD,IAAMJ,uBAAuB;MAAA,IAAAK,KAAA,GAAAtE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA4F,SAAOC,MAAM,EAAEX,IAAI;QAAA,IAAAY,qBAAA,EAAAC,IAAA;QAAA,OAAAnL,mBAAA,GAAAuB,IAAA,UAAA6J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,IAAA,GAAAwF,SAAA,CAAAnH,IAAA;YAAA;cAAAmH,SAAA,CAAAnH,IAAA;cAAA,OAC1B4C,GAAG,CAAC4D,uBAAuB,CAACO,MAAM,CAAC;YAAA;cAAAC,qBAAA,GAAAG,SAAA,CAAA1H,IAAA;cAAlDwH,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;gBAChBhE,SAAS,CAAC;kBAAEtF,IAAI,EAAE,SAAS;kBAAEiJ,OAAO,EAAER;gBAAK,CAAC,CAAC;gBAC7CvB,aAAa,CAAC,CAAC;gBACfJ,WAAW,CAAC,CAAC;cACf;YAAC;YAAA;cAAA,OAAA0C,SAAA,CAAArF,IAAA;UAAA;QAAA,GAAAgF,QAAA;MAAA,CACF;MAAA,gBAPKN,uBAAuBA,CAAAY,EAAA,EAAAC,GAAA;QAAA,OAAAR,KAAA,CAAApE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAO5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}