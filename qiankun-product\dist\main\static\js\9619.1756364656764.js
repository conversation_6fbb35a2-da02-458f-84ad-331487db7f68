"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[9619],{99619:function(e,t,r){r.r(t),r.d(t,{default:function(){return A}});var n=r(31167),o=(r(76945),r(52669),r(50859)),a=(r(99854),r(44863)),i=(r(4711),r(62427)),u=(r(98773),r(74061)),c=r(4955),s=r(59335),l=r(3671),f=r(98885),h=r(42714);r(35894),r(50389);function v(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */v=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),u=new T(n||[]);return o(i,"_invoke",{value:M(e,r,u)}),i}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var h="suspendedStart",p="suspendedYield",y="executing",d="completed",g={};function m(){}function x(){}function w(){}var L={};s(L,i,(function(){return this}));var b=Object.getPrototypeOf,k=b&&b(b(j([])));k&&k!==r&&n.call(k,i)&&(L=k);var E=w.prototype=m.prototype=Object.create(L);function N(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function B(e,t){function r(o,a,i,u){var c=f(e[o],e,a);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==typeof l&&n.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,i,u)}),(function(e){r("throw",e,i,u)})):t.resolve(l).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,u)}))}u(c.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function M(t,r,n){var o=h;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===d){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var c=S(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var s=f(t,r,n);if("normal"===s.type){if(o=n.done?d:p,s.arg===g)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=d,n.method="throw",n.arg=s.arg)}}}function S(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=f(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function V(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function j(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return x.prototype=w,o(E,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:x,configurable:!0}),x.displayName=s(w,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===x||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,s(e,c,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},N(B.prototype),s(B.prototype,u,(function(){return this})),t.AsyncIterator=B,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new B(l(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},N(E),s(E,c,"Generator"),s(E,i,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=j,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(V),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return u.type="throw",u.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),s=n.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),V(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;V(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:j(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},t}function p(e){return m(e)||g(e)||d(e)||y()}function y(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"==typeof e)return x(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?x(e,t):void 0}}function g(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function m(e){if(Array.isArray(e))return x(e)}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function w(e,t,r,n,o,a,i){try{var u=e[a](i),c=u.value}catch(e){return void r(e)}u.done?t(c):Promise.resolve(c).then(n,o)}function L(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){w(a,n,o,i,u,"next",e)}function u(e){w(a,n,o,i,u,"throw",e)}i(void 0)}))}}var b={class:"LayoutBoxMessage"},k={class:"LayoutBoxMessageBody"},E={class:"LayoutBoxMessageHead"},N={class:"LayoutBoxMessageScroll"},B=["onClick"],M={class:"LayoutBoxMessageInfo"},S={class:"LayoutBoxMessageType"},_={class:"LayoutBoxMessageTime"},V={class:"LayoutBoxMessageTitle"},T={key:0,class:"LayoutBoxMessageLoadingText"},j={key:1,class:"LayoutBoxMessageLoadingText"},O={name:"LayoutBoxMessage"},C=Object.assign(O,{setup(e){var t=(0,s.useStore)(),r=(0,u.inject)("openPage"),y=(0,u.ref)(),d=(0,u.ref)(!1),g=(0,u.ref)(1),m=(0,u.ref)(10),x=(0,u.ref)(0),w=(0,u.ref)(!1),O=(0,u.ref)(!0),C=(0,u.ref)([]);(0,u.onMounted)((function(){G()}));var P=function(e){var t=e.scrollTop;if(y.value){var r=y.value.wrapRef,n=r.scrollHeight,o=r.clientHeight;n-t<=o+50&&!d.value&&A()}},A=function(){g.value*m.value>=x.value||(d.value=!0,g.value+=1,G())},G=function(){var e=L(v().mark((function e(){var t,r,n;return v().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.A.boxMessageList({pageNo:g.value,pageSize:m.value,hasRead:0,objectParam:{}});case 2:t=e.sent,r=t.data,n=t.total,C.value=[].concat(p(C.value),p(r)),x.value=n,O.value=g.value*m.value<x.value,w.value=g.value*m.value>=x.value,d.value=!1;case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),I=function(e){return e.redirectUrl||"system"===e.businessCode?e.isDisabled?(0,f.nk)({type:"info",message:`当前${e.moduleName||""}数据已被删除！`}):(r({key:"routePath",value:"/interaction/BoxMessage"}),void sessionStorage.setItem("BoxMessage",JSON.stringify(e||""))):(0,f.nk)({type:"info",message:`当前${e.moduleName||""}数据没有跳转路径，请维护好跳转路径在进行查看详情！`})},R=function(){h.s.confirm("此操作将把所有的消息设为已读, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){D()})).catch((function(){(0,f.nk)({type:"info",message:"已取消操作"})}))},D=function(){var e=L(v().mark((function e(){var t,r;return v().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.A.boxMessageRead({businessCode:"box_message"});case 2:t=e.sent,r=t.code,200===r&&((0,f.nk)({type:"success",message:"全部设为已读成功"}),g.value=1,m.value=10,x.value=0,w.value=!1,O.value=!0,C.value=[],G());case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,u.watch)((function(){return t.state.socket}),(function(e){e&&t.state.socket.on("message",(function(e){var t=JSON.parse(e);"box_message"===t.messageType&&(g.value=1,m.value=10,x.value=0,w.value=!1,O.value=!0,C.value=[],G())}))})),(0,u.watch)((function(){return t.state.boxMessageRefresh}),(function(e){e&&(g.value=1,m.value=10,x.value=0,w.value=!1,O.value=!0,C.value=[],G(),t.commit("setBoxMessageRefresh",!1))})),function(e,t){var c=(0,u.resolveComponent)("DArrowRight"),s=i.tk,f=a.kA,h=o.Vc,v=n.z_;return(0,u.openBlock)(),(0,u.createBlock)(v,{value:x.value},{default:(0,u.withCtx)((function(){return[(0,u.createVNode)(h,{trigger:"hover","popper-class":"LayoutBoxMessagePopover",transition:"zy-el-zoom-in-top"},{reference:(0,u.withCtx)((function(){return[(0,u.createElementVNode)("div",b,[(0,u.renderSlot)(e.$slots,"default")])]})),default:(0,u.withCtx)((function(){return[(0,u.createElementVNode)("div",k,[(0,u.createElementVNode)("div",E,[(0,u.createElementVNode)("div",{class:"LayoutBoxMessageName"},[t[1]||(t[1]=(0,u.createTextVNode)("消息盒子")),(0,u.createElementVNode)("span",{onClick:R},"全部已读")]),(0,u.createElementVNode)("div",{onClick:t[0]||(t[0]=function(e){return(0,u.unref)(r)({key:"routePath",value:"/interaction/BoxMessage"})}),class:"LayoutBoxMessageText"},[t[2]||(t[2]=(0,u.createTextVNode)("更多 ")),(0,u.createVNode)(s,null,{default:(0,u.withCtx)((function(){return[(0,u.createVNode)(c)]})),_:1})])]),(0,u.createVNode)(f,{ref_key:"scrollRef",ref:y,class:"LayoutBoxMessageScrollbar",onScroll:P},{default:(0,u.withCtx)((function(){return[(0,u.createElementVNode)("div",N,[((0,u.openBlock)(!0),(0,u.createElementBlock)(u.Fragment,null,(0,u.renderList)(C.value,(function(e){return(0,u.openBlock)(),(0,u.createElementBlock)("div",{key:e.id,class:"LayoutBoxMessageItem",onClick:function(t){return I(e)}},[(0,u.createElementVNode)("div",M,[(0,u.createElementVNode)("div",S,(0,u.toDisplayString)(e.moduleName),1),(0,u.createElementVNode)("div",_,(0,u.toDisplayString)((0,u.unref)(l.G)(e.createDate)),1)]),(0,u.createElementVNode)("div",V,(0,u.toDisplayString)(e.content),1)],8,B)})),128)),O.value?((0,u.openBlock)(),(0,u.createElementBlock)("div",T,"加载中...")):(0,u.createCommentVNode)("",!0),w.value?((0,u.openBlock)(),(0,u.createElementBlock)("div",j,"没有更多了")):(0,u.createCommentVNode)("",!0)])]})),_:1},512)])]})),_:3})]})),_:3},8,["value"])}}});const P=C;var A=P}}]);