{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, vShow as _vShow, normalizeStyle as _normalizeStyle, createBlock as _createBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalChatEditor\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalChatViewControls\"\n};\nvar _hoisted_3 = [\"innerHTML\"];\nvar _hoisted_4 = {\n  class: \"GlobalChatEmotion\"\n};\nvar _hoisted_5 = [\"onClick\"];\nvar _hoisted_6 = [\"innerHTML\"];\nvar _hoisted_7 = [\"innerHTML\"];\nvar _hoisted_8 = [\"innerHTML\"];\nvar _hoisted_9 = {\n  class: \"GlobalChatViewControlsBotton\"\n};\nvar _hoisted_10 = {\n  class: \"GlobalChatUserPickerList\"\n};\nvar _hoisted_11 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  var _component_el_upload = _resolveComponent(\"el-upload\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_popover, {\n    placement: \"top\",\n    trigger: \"click\",\n    \"popper-class\": \"GlobalChatEmotionPopover\"\n  }, {\n    reference: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        class: \"GlobalChatViewControlsItem\",\n        innerHTML: $setup.emoteIcon,\n        title: \"表情\"\n      }, null, 8 /* PROPS */, _hoisted_3)];\n    }),\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_scrollbar, {\n        class: \"GlobalChatEmotionScroll\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.emotion, function (item) {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"GlobalChatEmotionItem\",\n              key: item.name,\n              onClick: function onClick($event) {\n                return $setup.appendEmotion(item);\n              }\n            }, [_createElementVNode(\"div\", {\n              class: _normalizeClass(item.name)\n            }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_5);\n          }), 128 /* KEYED_FRAGMENT */)), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n            class: \"GlobalChatEmotionItem\"\n          }, null, -1 /* HOISTED */)), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n            class: \"GlobalChatEmotionItem\"\n          }, null, -1 /* HOISTED */)), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n            class: \"GlobalChatEmotionItem\"\n          }, null, -1 /* HOISTED */))])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_upload, {\n    action: \"/\",\n    \"http-request\": $setup.fileUpload,\n    \"show-file-list\": false,\n    multiple: \"\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        class: \"GlobalChatViewControlsItem\",\n        innerHTML: $setup.folderIcon,\n        title: \"文件\"\n      }, null, 8 /* PROPS */, _hoisted_6)];\n    }),\n    _: 1 /* STABLE */\n  }), $setup.props.isVote ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"GlobalChatViewControlsItem\",\n    innerHTML: $setup.voteIcon,\n    title: \"投票\",\n    onClick: $setup.handleVote\n  }, null, 8 /* PROPS */, _hoisted_7)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    class: \"GlobalChatViewControlsItem is-min\",\n    innerHTML: $setup.lineFeedIcon,\n    title: \"换行\",\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.appendEmotion({\n        text: '\\n'\n      });\n    })\n  }, null, 8 /* PROPS */, _hoisted_8)]), _createVNode(_component_el_input, {\n    ref: \"inputRef\",\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.content = $event;\n    }),\n    type: \"textarea\",\n    resize: \"none\",\n    rows: 4,\n    onInput: $setup.handleInput,\n    onKeydown: $setup.handleKeyDown,\n    onPaste: $setup.handlePaste,\n    onBlur: $setup.handleBlur\n  }, null, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", null, \"Enter 发送 ｜ \" + _toDisplayString($setup.isMacText() ? 'Command + Enter 换行' : 'Ctrl + Enter 换行'), 1 /* TEXT */), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSendMessage\n  }, {\n    default: _withCtx(function () {\n      return _cache[5] || (_cache[5] = [_createTextVNode(\"发送\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" 用户选择器 \"), _withDirectives((_openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"GlobalChatUserPicker\",\n    style: _normalizeStyle($setup.mentionStyle)\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_10, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.filteredUserList, function (user) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"GlobalChatUserPickerItem\",\n          key: user.id,\n          onClick: function onClick($event) {\n            return $setup.insertUser(user);\n          }\n        }, _toDisplayString(user.userName), 9 /* TEXT, PROPS */, _hoisted_11);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"style\"])), [[_vShow, $setup.show], [$setup[\"vClickOutside\"], $setup.hideUserPicker]])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_popover", "placement", "trigger", "reference", "_withCtx", "innerHTML", "$setup", "emoteIcon", "title", "_hoisted_3", "default", "_component_el_scrollbar", "_hoisted_4", "_Fragment", "_renderList", "emotion", "item", "key", "name", "onClick", "$event", "appendEmotion", "_normalizeClass", "_hoisted_5", "_", "_component_el_upload", "action", "fileUpload", "multiple", "folderIcon", "_hoisted_6", "props", "isVote", "voteIcon", "handleVote", "_hoisted_7", "_createCommentVNode", "lineFeedIcon", "_cache", "text", "_hoisted_8", "_component_el_input", "ref", "modelValue", "content", "type", "resize", "rows", "onInput", "handleInput", "onKeydown", "handleKeyDown", "onPaste", "handlePaste", "onBlur", "handleBlur", "_hoisted_9", "_toDisplayString", "isMacText", "_component_el_button", "handleSendMessage", "_createTextVNode", "_createBlock", "style", "_normalizeStyle", "mentionStyle", "_hoisted_10", "filteredUserList", "user", "id", "insertUser", "userName", "_hoisted_11", "show", "hideUserPicker"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalChatEditor.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatEditor\">\r\n    <div class=\"GlobalChatViewControls\">\r\n      <el-popover placement=\"top\" trigger=\"click\" popper-class=\"GlobalChatEmotionPopover\">\r\n        <template #reference>\r\n          <div class=\"GlobalChatViewControlsItem\" v-html=\"emoteIcon\" title=\"表情\"></div>\r\n        </template>\r\n        <el-scrollbar class=\"GlobalChatEmotionScroll\">\r\n          <div class=\"GlobalChatEmotion\">\r\n            <div class=\"GlobalChatEmotionItem\" v-for=\"item in emotion\" :key=\"item.name\" @click=\"appendEmotion(item)\">\r\n              <div :class=\"item.name\"></div>\r\n            </div>\r\n            <div class=\"GlobalChatEmotionItem\"></div>\r\n            <div class=\"GlobalChatEmotionItem\"></div>\r\n            <div class=\"GlobalChatEmotionItem\"></div>\r\n          </div>\r\n        </el-scrollbar>\r\n      </el-popover>\r\n      <el-upload action=\"/\" :http-request=\"fileUpload\" :show-file-list=\"false\" multiple>\r\n        <div class=\"GlobalChatViewControlsItem\" v-html=\"folderIcon\" title=\"文件\"></div>\r\n      </el-upload>\r\n      <div\r\n        class=\"GlobalChatViewControlsItem\"\r\n        v-html=\"voteIcon\"\r\n        title=\"投票\"\r\n        @click=\"handleVote\"\r\n        v-if=\"props.isVote\"></div>\r\n      <div\r\n        class=\"GlobalChatViewControlsItem is-min\"\r\n        v-html=\"lineFeedIcon\"\r\n        title=\"换行\"\r\n        @click=\"appendEmotion({ text: '\\n' })\"></div>\r\n    </div>\r\n    <el-input\r\n      ref=\"inputRef\"\r\n      v-model=\"content\"\r\n      type=\"textarea\"\r\n      resize=\"none\"\r\n      :rows=\"4\"\r\n      @input=\"handleInput\"\r\n      @keydown=\"handleKeyDown\"\r\n      @paste=\"handlePaste\"\r\n      @blur=\"handleBlur\" />\r\n    <div class=\"GlobalChatViewControlsBotton\">\r\n      <span>Enter 发送 ｜ {{ isMacText() ? 'Command + Enter 换行' : 'Ctrl + Enter 换行' }}</span>\r\n      <el-button type=\"primary\" @click=\"handleSendMessage\">发送</el-button>\r\n    </div>\r\n    <!-- 用户选择器 -->\r\n    <el-scrollbar v-show=\"show\" class=\"GlobalChatUserPicker\" :style=\"mentionStyle\" v-click-outside=\"hideUserPicker\">\r\n      <div class=\"GlobalChatUserPickerList\">\r\n        <div class=\"GlobalChatUserPickerItem\" v-for=\"user in filteredUserList\" :key=\"user.id\" @click=\"insertUser(user)\">\r\n          {{ user.userName }}\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script setup name=\"GlobalChatEditor\">\r\nimport { ref, computed, nextTick } from 'vue'\r\nimport { emotion } from '../js/emotion.js'\r\nimport { emoteIcon, folderIcon, lineFeedIcon, voteIcon } from '../js/icon.js'\r\nconst props = defineProps({ isVote: { type: Boolean, default: false }, userData: { type: Array, default: () => [] } })\r\nconst emit = defineEmits(['handleFile', 'handleVote', 'handlePasteImg', 'handleSendMessage'])\r\n\r\nconst inputRef = ref()\r\nconst content = ref('')\r\n\r\nconst show = ref(false)\r\nconst keyword = ref('')\r\nconst cursorPos = ref(0)\r\nconst mentionStyle = ref({ top: '0px', left: '0px' })\r\n// 存储@用户的位置信息\r\nconst mentionPositions = ref([])\r\nlet mentionIdCounter = 0 // 用于生成唯一的mention ID\r\n\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\n// 根据关键字过滤用户列表\r\nconst filteredUserList = computed(() => {\r\n  if (!keyword.value) return props.userData\r\n  return props.userData.filter((user) => user.userName?.toLowerCase()?.includes(keyword.value?.toLowerCase()))\r\n})\r\n// 检查是否在@用户的位置\r\nconst isInMention = (position) => mentionPositions.value.some((pos) => position >= pos.start && position <= pos.end + 1)\r\n// 获取光标所在的@用户位置\r\nconst getMentionAtPosition = (position) =>\r\n  mentionPositions.value.find((pos) => position >= pos.start && position <= pos.end)\r\n\r\nconst isMacText = () => {\r\n  const userAgent = navigator.userAgent.toLowerCase()\r\n  return userAgent?.includes('macintosh') || userAgent?.includes('mac os x')\r\n}\r\nconst fileUpload = (file) => {\r\n  emit('handleFile', file)\r\n}\r\nconst handleVote = () => {\r\n  emit('handleVote')\r\n}\r\n// 隐藏用户选择器\r\nconst hideUserPicker = () => {\r\n  show.value = false\r\n  keyword.value = ''\r\n}\r\n\r\n// 计算@符号的位置\r\nconst calculateAtPosition = () => {\r\n  if (!inputRef.value) return\r\n  const textarea = inputRef.value.$el.querySelector('textarea')\r\n  const text = textarea.value\r\n  const lastAtIndex = text.lastIndexOf('@')\r\n  if (lastAtIndex === -1) return\r\n  // 获取textarea的位置信息\r\n  const rect = textarea.getBoundingClientRect()\r\n  const textareaStyle = window.getComputedStyle(textarea)\r\n  const lineHeight = parseInt(textareaStyle.lineHeight)\r\n  const paddingTop = parseInt(textareaStyle.paddingTop)\r\n  const paddingLeft = parseInt(textareaStyle.paddingLeft)\r\n  // 计算@符号之前的文本有多少行\r\n  const textBeforeAt = text.substring(0, lastAtIndex)\r\n  const lines = textBeforeAt.split('\\n')\r\n  const currentLineIndex = lines.length - 1\r\n  // 计算@符号在当前行的位置\r\n  const currentLine = lines[currentLineIndex]\r\n  const atPositionInLine = currentLine ? currentLine.length : 0\r\n  // 计算位置\r\n  const top = rect.top + paddingTop + currentLineIndex * lineHeight\r\n  const left = rect.left + paddingLeft + atPositionInLine * 8 // 8px 是一个近似的字符宽度\r\n  // 计算用户选择器的实际高度\r\n  const userItemHeight = 32 // 每个用户项的高度（包含padding）\r\n  const maxHeight = 220 // 最大高度\r\n  const actualHeight = Math.min(filteredUserList.value.length * userItemHeight, maxHeight) + 14\r\n  // 设置选择器位置\r\n  mentionStyle.value = {\r\n    top: `${top - actualHeight - 5}px`, // 在@符号上方5px的位置\r\n    left: `${left}px`,\r\n    height: `${actualHeight}px`, // 使用固定高度而不是maxHeight\r\n    maxHeight: 'none' // 移除maxHeight限制\r\n  }\r\n}\r\n\r\n// 处理输入\r\nconst handleInput = (value) => {\r\n  if (!props.userData.length) return\r\n  const textarea = inputRef.value.$el.querySelector('textarea')\r\n  const cursorPosition = textarea.selectionStart\r\n  // 检查是否在@用户的位置\r\n  if (isInMention(cursorPosition)) {\r\n    content.value = value\r\n    return\r\n  }\r\n  const lastChar = value.slice(-1)\r\n  if (lastChar === '@') {\r\n    show.value = true\r\n    keyword.value = ''\r\n    nextTick(() => {\r\n      calculateAtPosition()\r\n    })\r\n  } else if (show.value) {\r\n    const lastAtIndex = value.lastIndexOf('@')\r\n    if (lastAtIndex !== -1) {\r\n      keyword.value = value.slice(lastAtIndex + 1)\r\n      nextTick(() => {\r\n        calculateAtPosition()\r\n      })\r\n    } else {\r\n      show.value = false\r\n    }\r\n  }\r\n  // 更新所有@用户的位置\r\n  updateMentionPositions(value)\r\n}\r\n\r\n// 更新@用户的位置\r\nconst updateMentionPositions = (text) => {\r\n  // 保留现有的mention信息，只更新位置\r\n  const newPositions = []\r\n  let currentIndex = 0\r\n  while (currentIndex < text.length) {\r\n    const atIndex = text.indexOf('@', currentIndex)\r\n    if (atIndex === -1) break\r\n    // 查找@后面的用户名（直到空格或文本结束）\r\n    let endIndex = atIndex + 1\r\n    while (endIndex < text.length && text[endIndex] !== ' ') {\r\n      endIndex++\r\n    }\r\n    // 如果找到了完整的@用户（@后面有内容且以空格结束）\r\n    if (endIndex > atIndex + 1) {\r\n      const mentionText = text.slice(atIndex, endIndex)\r\n      // 查找是否已存在相同的mention\r\n      const existingMention = mentionPositions.value.find(\r\n        (m) => m.text === mentionText && !newPositions.some((n) => n.id === m.id)\r\n      )\r\n      if (existingMention) {\r\n        // 如果存在，更新位置\r\n        newPositions.push({ ...existingMention, start: atIndex, end: endIndex - 1 })\r\n      }\r\n    }\r\n    currentIndex = endIndex + 1\r\n  }\r\n  // 更新位置信息\r\n  mentionPositions.value = newPositions\r\n}\r\n\r\nconst appendEmotion = (item) => {\r\n  const textarea = inputRef.value.$el.querySelector('textarea')\r\n  const cursorPosition = textarea.selectionStart\r\n  // 检查是否在@用户的位置\r\n  if (isInMention(cursorPosition)) return\r\n  if (!content.value) {\r\n    content.value = item.text\r\n  } else {\r\n    const start = content.value.slice(0, cursorPos.value)\r\n    const end = content.value.slice(cursorPos.value)\r\n    content.value = `${start}${item.text}${end}`\r\n    nextTick(() => {\r\n      inputRef.value.focus()\r\n      if (inputRef.value.$el.querySelector('textarea')) {\r\n        inputRef.value.$el\r\n          .querySelector('textarea')\r\n          .setSelectionRange(cursorPos.value + item.text.length, cursorPos.value + item.text.length)\r\n      }\r\n    })\r\n  }\r\n}\r\nconst handleBlur = (e) => {\r\n  cursorPos.value = e.srcElement.selectionStart\r\n}\r\n// 处理键盘事件\r\nconst handleKeyDown = (e) => {\r\n  if (e.keyCode == 13) {\r\n    if (!e.ctrlKey && !e.metaKey) {\r\n      // 处理回车发送\r\n      e.preventDefault()\r\n      handleSendMessage()\r\n    } else {\r\n      cursorPos.value = e.srcElement.selectionStart\r\n      appendEmotion({ text: '\\n' })\r\n    }\r\n  } else {\r\n    const textarea = inputRef.value.$el.querySelector('textarea')\r\n    const cursorPosition = textarea.selectionStart\r\n    if (e.key === 'Backspace') {\r\n      // 检查是否在@用户的位置或紧跟在@用户后面\r\n      for (let i = mentionPositions.value.length - 1; i >= 0; i--) {\r\n        const mention = mentionPositions.value[i]\r\n        if (cursorPosition > mention.start && cursorPosition <= mention.end + 1) {\r\n          e.preventDefault()\r\n          // 删除整个@用户（包括末尾空格）\r\n          const beforeText = content.value.slice(0, mention.start)\r\n          const afterText = content.value.slice(mention.end + 1)\r\n          content.value = beforeText + afterText\r\n          // 删除当前@用户的位置信息\r\n          mentionPositions.value.splice(i, 1)\r\n          // 更新后面的@用户的位置\r\n          const deleteLength = mention.end - mention.start + 2\r\n          for (let j = i; j < mentionPositions.value.length; j++) {\r\n            mentionPositions.value[j].start -= deleteLength\r\n            mentionPositions.value[j].end -= deleteLength\r\n          }\r\n          break\r\n        }\r\n      }\r\n    } else if (e.key !== 'ArrowLeft' && e.key !== 'ArrowRight' && e.key !== 'ArrowUp' && e.key !== 'ArrowDown') {\r\n      // 如果不是方向键，检查是否在@用户的位置\r\n      if (isInMention(cursorPosition)) e.preventDefault()\r\n    }\r\n  }\r\n}\r\n\r\n// 处理粘贴事件\r\nconst handlePaste = (e) => {\r\n  const clipboardData = event.clipboardData || window.clipboardData\r\n  if (clipboardData) {\r\n    for (let i = 0; i < clipboardData.items.length; i++) {\r\n      const item = clipboardData.items[i]\r\n      if (item.kind === 'file' && item.type?.includes('image/')) {\r\n        const file = item.getAsFile()\r\n        const reader = new FileReader()\r\n        reader.onload = (e) => {\r\n          emit('handlePasteImg', { id: guid(), url: e.target.result, file: file })\r\n        }\r\n        reader.readAsDataURL(file)\r\n      }\r\n    }\r\n  } else {\r\n    const textarea = inputRef.value.$el.querySelector('textarea')\r\n    const cursorPosition = textarea.selectionStart\r\n    // 检查是否在@用户的位置\r\n    if (isInMention(cursorPosition)) e.preventDefault()\r\n  }\r\n}\r\n\r\n// 插入@用户\r\nconst insertUser = (user) => {\r\n  const lastAtIndex = content.value.lastIndexOf('@')\r\n  if (lastAtIndex !== -1) {\r\n    const mentionText = `@${user.userName} `\r\n    const newMessage =\r\n      content.value.slice(0, lastAtIndex) + mentionText + content.value.slice(lastAtIndex + 1 + keyword.value.length)\r\n    // 先重置状态\r\n    show.value = false\r\n    keyword.value = ''\r\n    // 然后更新消息\r\n    content.value = newMessage\r\n    // 添加新的mention信息\r\n    mentionPositions.value.push({\r\n      id: ++mentionIdCounter, // 生成新的唯一ID\r\n      text: mentionText.trim(),\r\n      start: lastAtIndex,\r\n      end: lastAtIndex + mentionText.length - 2, // 不包含末尾空格\r\n      userInfo: user\r\n    })\r\n  }\r\n}\r\n// 发送消息\r\nconst getMessage = () => {\r\n  if (content.value.trim()) {\r\n    // 构建发送的消息对象\r\n    return {\r\n      content: content.value,\r\n      mentions: mentionPositions.value.map((m) => ({ id: m.id, userInfo: m.userInfo }))\r\n    }\r\n  }\r\n  return null\r\n}\r\nconst clearMessage = () => {\r\n  content.value = ''\r\n  mentionPositions.value = []\r\n  mentionIdCounter = 0 // 重置ID计数器\r\n}\r\n// 发送消息\r\nconst handleSendMessage = () => {\r\n  const messageData = getMessage()\r\n  if (messageData) {\r\n    emit('handleSendMessage', messageData)\r\n    clearMessage()\r\n  }\r\n}\r\n\r\n// 点击外部指令\r\nconst vClickOutside = {\r\n  mounted(el, binding) {\r\n    el._clickOutside = (event) => {\r\n      if (!(el === event.target || el.contains(event.target))) {\r\n        binding.value(event)\r\n      }\r\n    }\r\n    document.addEventListener('click', el._clickOutside)\r\n  },\r\n  unmounted(el) {\r\n    document.removeEventListener('click', el._clickOutside)\r\n  }\r\n}\r\ndefineExpose({ getMessage, clearMessage })\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalChatEditor {\r\n  width: 100%;\r\n  height: 166px;\r\n  padding: 12px 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  padding-bottom: calc(var(--zy-height-secondary) + 12px);\r\n  border-radius: var(--el-border-radius-base);\r\n  background: #fff;\r\n  position: relative;\r\n\r\n  .GlobalChatViewControls {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 var(--zy-distance-two);\r\n\r\n    & > div {\r\n      width: 22px;\r\n      height: 22px;\r\n      cursor: pointer;\r\n\r\n      & + div {\r\n        margin-left: 12px;\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewControlsItem {\r\n      width: 22px;\r\n      height: 22px;\r\n      cursor: pointer;\r\n\r\n      &.is-min {\r\n        .icon {\r\n          width: 21px;\r\n          height: 21px;\r\n        }\r\n      }\r\n\r\n      .icon {\r\n        width: 22px;\r\n        height: 22px;\r\n\r\n        path {\r\n          fill: var(--zy-el-text-color-regular);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .zy-el-textarea__inner {\r\n    box-shadow: none !important;\r\n    padding: 0 var(--zy-distance-two);\r\n  }\r\n\r\n  .GlobalChatViewControlsBotton {\r\n    width: 100%;\r\n    height: var(--zy-height-secondary);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: 12px;\r\n    padding: 0 var(--zy-distance-two);\r\n\r\n    & > span {\r\n      font-size: 12px;\r\n      color: var(--zy-el-text-color-regular);\r\n      padding: 0 12px;\r\n    }\r\n\r\n    .zy-el-button {\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n\r\n  .GlobalChatUserPicker {\r\n    position: fixed;\r\n    background: #fff;\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    border-radius: var(--el-border-radius-base);\r\n    border: 1px solid var(--zy-el-border-color-lighter);\r\n    z-index: 2000;\r\n\r\n    .GlobalChatUserPickerList {\r\n      padding: 6px;\r\n\r\n      .GlobalChatUserPickerItem {\r\n        height: 32px;\r\n        line-height: 32px;\r\n        font-size: 12px;\r\n        padding: 0 12px;\r\n        cursor: pointer;\r\n        white-space: nowrap;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        border-radius: var(--el-border-radius-base);\r\n\r\n        &:hover {\r\n          color: var(--zy-el-color-primary);\r\n          background: var(--zy-el-color-primary-light-9);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatEmotionPopover {\r\n  width: 380px !important;\r\n  padding: 8px 0 !important;\r\n\r\n  .GlobalChatEmotionScroll {\r\n    width: 100%;\r\n    height: 360px;\r\n\r\n    .zy-el-scrollbar__view {\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .GlobalChatEmotion {\r\n    width: auto;\r\n    padding: 0 8px;\r\n    cursor: pointer;\r\n\r\n    .GlobalChatEmotionItem {\r\n      width: 38px;\r\n      height: 38px;\r\n      padding: 8px;\r\n\r\n      & > div {\r\n        width: 22px;\r\n        height: 22px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAwB;iBAFvC;;EAQeA,KAAK,EAAC;AAAmB;iBARxC;iBAAA;iBAAA;iBAAA;;EA2CSA,KAAK,EAAC;AAA8B;;EAMlCA,KAAK,EAAC;AAA0B;kBAjD3C;;;;;;;uBACEC,mBAAA,CAsDM,OAtDNC,UAsDM,GArDJC,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJC,YAAA,CAcaC,qBAAA;IAdDC,SAAS,EAAC,KAAK;IAACC,OAAO,EAAC,OAAO;IAAC,cAAY,EAAC;;IAC5CC,SAAS,EAAAC,QAAA,CAClB;MAAA,OAA4E,CAA5EP,mBAAA,CAA4E;QAAvEH,KAAK,EAAC,4BAA4B;QAACW,SAAkB,EAAVC,MAAA,CAAAC,SAAS;QAAEC,KAAK,EAAC;8BAL3EC,UAAA,E;;IAAAC,OAAA,EAAAN,QAAA,CAOQ;MAAA,OASe,CATfL,YAAA,CASeY,uBAAA;QATDjB,KAAK,EAAC;MAAyB;QAPrDgB,OAAA,EAAAN,QAAA,CAQU;UAAA,OAOM,CAPNP,mBAAA,CAOM,OAPNe,UAOM,I,kBANJjB,mBAAA,CAEMkB,SAAA,QAXlBC,WAAA,CAS8DR,MAAA,CAAAS,OAAO,EATrE,UASsDC,IAAI;iCAA9CrB,mBAAA,CAEM;cAFDD,KAAK,EAAC,uBAAuB;cAA0BuB,GAAG,EAAED,IAAI,CAACE,IAAI;cAAGC,OAAK,WAALA,OAAKA,CAAAC,MAAA;gBAAA,OAAEd,MAAA,CAAAe,aAAa,CAACL,IAAI;cAAA;gBACpGnB,mBAAA,CAA8B;cAAxBH,KAAK,EAVzB4B,eAAA,CAU2BN,IAAI,CAACE,IAAI;qDAVpCK,UAAA;oEAYY1B,mBAAA,CAAyC;YAApCH,KAAK,EAAC;UAAuB,6B,0BAClCG,mBAAA,CAAyC;YAApCH,KAAK,EAAC;UAAuB,6B,0BAClCG,mBAAA,CAAyC;YAApCH,KAAK,EAAC;UAAuB,4B;;QAd9C8B,CAAA;;;IAAAA,CAAA;MAkBMzB,YAAA,CAEY0B,oBAAA;IAFDC,MAAM,EAAC,GAAG;IAAE,cAAY,EAAEpB,MAAA,CAAAqB,UAAU;IAAG,gBAAc,EAAE,KAAK;IAAEC,QAAQ,EAAR;;IAlB/ElB,OAAA,EAAAN,QAAA,CAmBQ;MAAA,OAA6E,CAA7EP,mBAAA,CAA6E;QAAxEH,KAAK,EAAC,4BAA4B;QAACW,SAAmB,EAAXC,MAAA,CAAAuB,UAAU;QAAErB,KAAK,EAAC;8BAnB1EsB,UAAA,E;;IAAAN,CAAA;MA0BclB,MAAA,CAAAyB,KAAK,CAACC,MAAM,I,cALpBrC,mBAAA,CAK4B;IA1BlCsB,GAAA;IAsBQvB,KAAK,EAAC,4BAA4B;IAClCW,SAAiB,EAATC,MAAA,CAAA2B,QAAQ;IAChBzB,KAAK,EAAC,IAAI;IACTW,OAAK,EAAEb,MAAA,CAAA4B;0BAzBhBC,UAAA,KAAAC,mBAAA,gBA2BMvC,mBAAA,CAI+C;IAH7CH,KAAK,EAAC,mCAAmC;IACzCW,SAAqB,EAAbC,MAAA,CAAA+B,YAAY;IACpB7B,KAAK,EAAC,IAAI;IACTW,OAAK,EAAAmB,MAAA,QAAAA,MAAA,gBAAAlB,MAAA;MAAA,OAAEd,MAAA,CAAAe,aAAa;QAAAkB,IAAA;MAAA;IAAA;0BA/B7BC,UAAA,E,GAiCIzC,YAAA,CASuB0C,mBAAA;IARrBC,GAAG,EAAC,UAAU;IAlCpBC,UAAA,EAmCerC,MAAA,CAAAsC,OAAO;IAnCtB,uBAAAN,MAAA,QAAAA,MAAA,gBAAAlB,MAAA;MAAA,OAmCed,MAAA,CAAAsC,OAAO,GAAAxB,MAAA;IAAA;IAChByB,IAAI,EAAC,UAAU;IACfC,MAAM,EAAC,MAAM;IACZC,IAAI,EAAE,CAAC;IACPC,OAAK,EAAE1C,MAAA,CAAA2C,WAAW;IAClBC,SAAO,EAAE5C,MAAA,CAAA6C,aAAa;IACtBC,OAAK,EAAE9C,MAAA,CAAA+C,WAAW;IAClBC,MAAI,EAAEhD,MAAA,CAAAiD;2CACT1D,mBAAA,CAGM,OAHN2D,UAGM,GAFJ3D,mBAAA,CAAoF,cAA9E,aAAW,GAAA4D,gBAAA,CAAGnD,MAAA,CAAAoD,SAAS,+DAC7B3D,YAAA,CAAmE4D,oBAAA;IAAxDd,IAAI,EAAC,SAAS;IAAE1B,OAAK,EAAEb,MAAA,CAAAsD;;IA7CxClD,OAAA,EAAAN,QAAA,CA6C2D;MAAA,OAAEkC,MAAA,QAAAA,MAAA,OA7C7DuB,gBAAA,CA6C2D,IAAE,E;;IA7C7DrC,CAAA;QA+CIY,mBAAA,WAAc,E,+BACd0B,YAAA,CAMenD,uBAAA;IANajB,KAAK,EAAC,sBAAsB;IAAEqE,KAAK,EAhDnEC,eAAA,CAgDqE1D,MAAA,CAAA2D,YAAY;;IAhDjFvD,OAAA,EAAAN,QAAA,CAiDM;MAAA,OAIM,CAJNP,mBAAA,CAIM,OAJNqE,WAIM,I,kBAHJvE,mBAAA,CAEMkB,SAAA,QApDdC,WAAA,CAkD6DR,MAAA,CAAA6D,gBAAgB,EAlD7E,UAkDqDC,IAAI;6BAAjDzE,mBAAA,CAEM;UAFDD,KAAK,EAAC,0BAA0B;UAAmCuB,GAAG,EAAEmD,IAAI,CAACC,EAAE;UAAGlD,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAEd,MAAA,CAAAgE,UAAU,CAACF,IAAI;UAAA;4BACxGA,IAAI,CAACG,QAAQ,wBAnD1BC,WAAA;;;IAAAhD,CAAA;2CAgD0BlB,MAAA,CAAAmE,IAAI,G,0BAAsEnE,MAAA,CAAAoE,cAAc,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}