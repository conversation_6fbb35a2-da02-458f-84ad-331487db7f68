{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ResetPassword\"\n};\nvar _hoisted_2 = {\n  class: \"ResetPasswordSlideVerify\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_xyl_slide_verify = _resolveComponent(\"xyl-slide-verify\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"LoginForm\",\n    model: $setup.form,\n    rules: $setup.rules,\n    class: \"ResetPasswordForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        prop: \"userName\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.userName,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.userName = $event;\n            }),\n            placeholder: \"姓名\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        prop: \"mobile\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.mobile,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.mobile = $event;\n            }),\n            onInput: _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.mobile = $setup.validNum($setup.form.mobile);\n            }),\n            placeholder: \"手机号\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        prop: \"password\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            type: \"password\",\n            modelValue: $setup.form.password,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.password = $event;\n            }),\n            placeholder: \"新密码\",\n            \"show-password\": \"\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        prop: \"verifyPassword\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            type: \"verifyPassword\",\n            modelValue: $setup.form.verifyPassword,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.verifyPassword = $event;\n            }),\n            placeholder: \"确认新密码\",\n            \"show-password\": \"\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_xyl_slide_verify, {\n        ref: \"slideVerify\",\n        w: 360,\n        h: 200,\n        onAgain: $setup.onAgain,\n        onSuccess: $setup.onSuccess,\n        disabled: $setup.disabled\n      }, null, 8 /* PROPS */, [\"disabled\"])]), _createVNode(_component_el_form_item, {\n        class: \"smsValidation\",\n        prop: \"verifyCode\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.verifyCode,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n              return $setup.form.verifyCode = $event;\n            }),\n            placeholder: \"短信验证码\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: $setup.handleGetVerifyCode,\n            disabled: !$setup.disabled || $setup.countDownText != '获取验证码'\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.countDownText), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: _cache[6] || (_cache[6] = function ($event) {\n              return $setup.submitForm($setup.LoginForm);\n            }),\n            class: \"ResetPasswordFormButton\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[7] || (_cache[7] = [_createTextVNode(\"确定\")]);\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "default", "_withCtx", "_component_el_form_item", "prop", "_component_el_input", "modelValue", "userName", "_cache", "$event", "placeholder", "clearable", "_", "mobile", "onInput", "validNum", "type", "password", "verifyPassword", "_createElementVNode", "_hoisted_2", "_component_xyl_slide_verify", "w", "h", "onAgain", "onSuccess", "disabled", "verifyCode", "_component_el_button", "onClick", "handleGetVerifyCode", "countDownText", "_createTextVNode", "_toDisplayString", "submitForm", "LoginForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LoginView\\component\\ResetPassword.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ResetPassword\">\r\n    <el-form ref=\"LoginForm\" :model=\"form\" :rules=\"rules\" class=\"ResetPasswordForm\">\r\n      <el-form-item prop=\"userName\">\r\n        <el-input v-model=\"form.userName\" placeholder=\"姓名\" clearable />\r\n      </el-form-item>\r\n      <el-form-item prop=\"mobile\">\r\n        <el-input v-model=\"form.mobile\" @input=\"form.mobile = validNum(form.mobile)\" placeholder=\"手机号\" clearable />\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input type=\"password\" v-model=\"form.password\" placeholder=\"新密码\" show-password clearable />\r\n      </el-form-item>\r\n      <el-form-item prop=\"verifyPassword\">\r\n        <el-input type=\"verifyPassword\" v-model=\"form.verifyPassword\" placeholder=\"确认新密码\" show-password clearable />\r\n      </el-form-item>\r\n      <div class=\"ResetPasswordSlideVerify\">\r\n        <xyl-slide-verify ref=\"slideVerify\" :w=\"360\" :h=\"200\" @again=\"onAgain\" @success=\"onSuccess\"\r\n          :disabled=\"disabled\" />\r\n      </div>\r\n      <el-form-item class=\"smsValidation\" prop=\"verifyCode\">\r\n        <el-input v-model=\"form.verifyCode\" placeholder=\"短信验证码\" clearable> </el-input>\r\n        <el-button type=\"primary\" @click=\"handleGetVerifyCode\" :disabled=\"!disabled || countDownText != '获取验证码'\">{{\r\n          countDownText }}</el-button>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"submitForm(LoginForm)\" class=\"ResetPasswordFormButton\">确定</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ResetPassword' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport utils, { validNum } from 'common/js/utils.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst LoginForm = ref()\r\nconst regexName = ref('')\r\nconst form = reactive({ userName: '', mobile: '', password: '', verifyPassword: '', verifyCode: '' })\r\n\r\nconst verifyPassword = (rule, value, callback) => {\r\n  if (value === '') {\r\n    callback(new Error('请再次输入新密码'))\r\n  } else if (value !== form.password) {\r\n    callback(new Error('两次输入密码不一致!'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\nconst rules = reactive({\r\n  userName: [{ required: true, message: '请输入姓名', trigger: ['blur', 'change'] }],\r\n  mobile: [{ required: true, message: '请输入手机号', trigger: ['blur', 'change'] }],\r\n  password: [{ required: true, message: '请输入新密码', trigger: ['blur', 'change'] }],\r\n  verifyPassword: [{ validator: verifyPassword, required: true, trigger: ['blur', 'change'] }],\r\n  verifyCode: [{ required: true, message: '请输入验证码', trigger: ['blur', 'change'] }]\r\n})\r\nconst slideVerify = ref()\r\nconst disabled = ref(false)\r\nconst verifyCodeId = ref('')\r\nconst countDownText = ref('获取验证码')\r\nconst countDown = ref(0)\r\n\r\nonMounted(() => { passwordStrengthMessage() })\r\n\r\nconst passwordStrengthMessage = async () => {\r\n  const { data } = await api.passwordStrengthMessage()\r\n  regexName.value = data\r\n}\r\nconst onAgain = () => {\r\n  ElMessage.error('检测到非人为操作的哦！!')\r\n  handleFefresh()\r\n}\r\nconst onSuccess = () => { disabled.value = true }\r\nconst handleFefresh = () => {\r\n  disabled.value = false\r\n  slideVerify.value?.refresh()\r\n}\r\nconst handleGetVerifyCode = async () => {\r\n  if (!form.mobile) return ElMessage({ type: 'warning', message: '请输入手机号！' })\r\n  const { data, code } = await api.openVerifyCodeSend({ mobile: form.mobile, sendType: 'no_login' })\r\n  if (code === 200) {\r\n    verifyCodeId.value = data\r\n    countDown.value = 60\r\n    handleCountDown()\r\n    ElMessage({ type: 'success', message: '短信验证码已发送！' })\r\n  }\r\n}\r\nconst handleCountDown = () => {\r\n  if (countDown.value === 0) {\r\n    countDownText.value = '获取验证码'\r\n    countDown.value = 60\r\n    return\r\n  } else {\r\n    countDownText.value = '重新发送' + countDown.value + 'S'\r\n    countDown.value--\r\n  }\r\n  setTimeout(() => { handleCountDown() }, 1000)\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { passwordStrengthChecker() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\n\r\nconst passwordStrengthChecker = async () => {\r\n  const { code } = await api.passwordStrengthChecker({ password: utils.encrypt(form.password, new Date().getTime(), '1') })\r\n  if (code === 200) { globalJson() }\r\n}\r\n\r\nconst globalJson = async () => {\r\n  const passwordContent = { userName: form.userName, mobile: form.mobile, verifyCodeId: verifyCodeId.value, verifyCode: form.verifyCode, newPassword: form.password }\r\n  const { code } = await api.findPassword({ passwordContent: utils.encrypt(JSON.stringify(passwordContent), new Date().getTime(), '1') })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '修改成功，请重新登录！' })\r\n    emit('callback')\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.ResetPassword {\r\n  width: 520px;\r\n  padding: var(--zy-distance-two) var(--zy-distance-one);\r\n\r\n  .ResetPasswordForm {\r\n    width: 360px;\r\n    margin: auto;\r\n    padding-top: var(--zy-distance-two);\r\n\r\n    input:-webkit-autofill {\r\n      transition: background-color 5000s ease-in-out 0s;\r\n    }\r\n\r\n    .zy-el-form-item {\r\n      margin-bottom: var(--zy-form-distance-bottom);\r\n\r\n      .ResetPasswordFormButton {\r\n        width: 100%;\r\n      }\r\n    }\r\n\r\n    .ResetPasswordSlideVerify {\r\n      margin-bottom: var(--zy-form-distance-bottom);\r\n    }\r\n\r\n    .smsValidation {\r\n      .zy-el-form-item__content {\r\n        display: flex;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .zy-el-input {\r\n        width: 62%;\r\n      }\r\n    }\r\n\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAe;;EAcjBA,KAAK,EAAC;AAA0B;;;;;;;uBAdzCC,mBAAA,CA2BM,OA3BNC,UA2BM,GA1BJC,YAAA,CAyBUC,kBAAA;IAzBDC,GAAG,EAAC,WAAW;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAET,KAAK,EAAC;;IAFhEU,OAAA,EAAAC,QAAA,CAGM;MAAA,OAEe,CAFfR,YAAA,CAEeS,uBAAA;QAFDC,IAAI,EAAC;MAAU;QAHnCH,OAAA,EAAAC,QAAA,CAIQ;UAAA,OAA+D,CAA/DR,YAAA,CAA+DW,mBAAA;YAJvEC,UAAA,EAI2BR,MAAA,CAAAC,IAAI,CAACQ,QAAQ;YAJxC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAI2BX,MAAA,CAAAC,IAAI,CAACQ,QAAQ,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,IAAI;YAACC,SAAS,EAAT;;;QAJ3DC,CAAA;UAMMlB,YAAA,CAEeS,uBAAA;QAFDC,IAAI,EAAC;MAAQ;QANjCH,OAAA,EAAAC,QAAA,CAOQ;UAAA,OAA2G,CAA3GR,YAAA,CAA2GW,mBAAA;YAPnHC,UAAA,EAO2BR,MAAA,CAAAC,IAAI,CAACc,MAAM;YAPtC,uBAAAL,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAO2BX,MAAA,CAAAC,IAAI,CAACc,MAAM,GAAAJ,MAAA;YAAA;YAAGK,OAAK,EAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEX,MAAA,CAAAC,IAAI,CAACc,MAAM,GAAGf,MAAA,CAAAiB,QAAQ,CAACjB,MAAA,CAAAC,IAAI,CAACc,MAAM;YAAA;YAAGH,WAAW,EAAC,KAAK;YAACC,SAAS,EAAT;;;QAPvGC,CAAA;UASMlB,YAAA,CAEeS,uBAAA;QAFDC,IAAI,EAAC;MAAU;QATnCH,OAAA,EAAAC,QAAA,CAUQ;UAAA,OAA8F,CAA9FR,YAAA,CAA8FW,mBAAA;YAApFW,IAAI,EAAC,UAAU;YAVjCV,UAAA,EAU2CR,MAAA,CAAAC,IAAI,CAACkB,QAAQ;YAVxD,uBAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAU2CX,MAAA,CAAAC,IAAI,CAACkB,QAAQ,GAAAR,MAAA;YAAA;YAAEC,WAAW,EAAC,KAAK;YAAC,eAAa,EAAb,EAAa;YAACC,SAAS,EAAT;;;QAV1FC,CAAA;UAYMlB,YAAA,CAEeS,uBAAA;QAFDC,IAAI,EAAC;MAAgB;QAZzCH,OAAA,EAAAC,QAAA,CAaQ;UAAA,OAA4G,CAA5GR,YAAA,CAA4GW,mBAAA;YAAlGW,IAAI,EAAC,gBAAgB;YAbvCV,UAAA,EAaiDR,MAAA,CAAAC,IAAI,CAACmB,cAAc;YAbpE,uBAAAV,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAaiDX,MAAA,CAAAC,IAAI,CAACmB,cAAc,GAAAT,MAAA;YAAA;YAAEC,WAAW,EAAC,OAAO;YAAC,eAAa,EAAb,EAAa;YAACC,SAAS,EAAT;;;QAbxGC,CAAA;UAeMO,mBAAA,CAGM,OAHNC,UAGM,GAFJ1B,YAAA,CACyB2B,2BAAA;QADPzB,GAAG,EAAC,aAAa;QAAE0B,CAAC,EAAE,GAAG;QAAGC,CAAC,EAAE,GAAG;QAAGC,OAAK,EAAE1B,MAAA,CAAA0B,OAAO;QAAGC,SAAO,EAAE3B,MAAA,CAAA2B,SAAS;QACvFC,QAAQ,EAAE5B,MAAA,CAAA4B;+CAEfhC,YAAA,CAIeS,uBAAA;QAJDZ,KAAK,EAAC,eAAe;QAACa,IAAI,EAAC;;QAnB/CH,OAAA,EAAAC,QAAA,CAoBQ;UAAA,OAA8E,CAA9ER,YAAA,CAA8EW,mBAAA;YApBtFC,UAAA,EAoB2BR,MAAA,CAAAC,IAAI,CAAC4B,UAAU;YApB1C,uBAAAnB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAoB2BX,MAAA,CAAAC,IAAI,CAAC4B,UAAU,GAAAlB,MAAA;YAAA;YAAEC,WAAW,EAAC,OAAO;YAACC,SAAS,EAAT;mDACxDjB,YAAA,CAC8BkC,oBAAA;YADnBZ,IAAI,EAAC,SAAS;YAAEa,OAAK,EAAE/B,MAAA,CAAAgC,mBAAmB;YAAGJ,QAAQ,GAAG5B,MAAA,CAAA4B,QAAQ,IAAI5B,MAAA,CAAAiC,aAAa;;YArBpG9B,OAAA,EAAAC,QAAA,CAqBiH;cAAA,OACvF,CAtB1B8B,gBAAA,CAAAC,gBAAA,CAsBUnC,MAAA,CAAAiC,aAAa,iB;;YAtBvBnB,CAAA;;;QAAAA,CAAA;UAwBMlB,YAAA,CAEeS,uBAAA;QA1BrBF,OAAA,EAAAC,QAAA,CAyBQ;UAAA,OAAuG,CAAvGR,YAAA,CAAuGkC,oBAAA;YAA5FZ,IAAI,EAAC,SAAS;YAAEa,OAAK,EAAArB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEX,MAAA,CAAAoC,UAAU,CAACpC,MAAA,CAAAqC,SAAS;YAAA;YAAG5C,KAAK,EAAC;;YAzBvEU,OAAA,EAAAC,QAAA,CAyBiG;cAAA,OAAEM,MAAA,QAAAA,MAAA,OAzBnGwB,gBAAA,CAyBiG,IAAE,E;;YAzBnGpB,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}