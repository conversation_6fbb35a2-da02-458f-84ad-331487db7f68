{"ast": null, "code": "import { createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, withModifiers as _withModifiers, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, createCommentVNode as _createCommentVNode, Transition as _Transition, Teleport as _Teleport, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"modal-header\"\n};\nvar _hoisted_2 = {\n  class: \"modal-title\"\n};\nvar _hoisted_3 = {\n  class: \"modal-body\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createBlock(_Teleport, {\n    to: \"body\"\n  }, [_createVNode(_Transition, {\n    name: \"modal-fade\"\n  }, {\n    default: _withCtx(function () {\n      return [$props.modelValue ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"custom-satisfaction-modal\",\n        onClick: $setup.handleMaskClick\n      }, [_createElementVNode(\"div\", {\n        class: \"modal-container\",\n        onClick: _cache[0] || (_cache[0] = _withModifiers(function () {}, [\"stop\"]))\n      }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_icon, {\n        class: \"title-icon\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"Star\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _cache[1] || (_cache[1] = _createTextVNode(\" 满意度调查 \"))]), _createElementVNode(\"div\", {\n        class: \"modal-close\",\n        onClick: $setup.handleClose\n      }, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"Close\"])];\n        }),\n        _: 1 /* STABLE */\n      })])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode($setup[\"SatisfactionSurvey\"], {\n        data: $props.data,\n        onCallback: $setup.handleCallback\n      }, null, 8 /* PROPS */, [\"data\"])])])])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_Teleport", "to", "_createVNode", "_Transition", "name", "default", "_withCtx", "$props", "modelValue", "_createElementBlock", "key", "onClick", "$setup", "handleMaskClick", "_createElementVNode", "_cache", "_withModifiers", "_hoisted_1", "_hoisted_2", "_component_el_icon", "_", "_createTextVNode", "handleClose", "_hoisted_3", "data", "onCallback", "handleCallback", "_createCommentVNode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalAiChat\\CustomSatisfactionModal.vue"], "sourcesContent": ["<template>\r\n  <Teleport to=\"body\">\r\n    <Transition name=\"modal-fade\">\r\n      <div v-if=\"modelValue\" class=\"custom-satisfaction-modal\" @click=\"handleMaskClick\">\r\n        <div class=\"modal-container\" @click.stop>\r\n          <div class=\"modal-header\">\r\n            <div class=\"modal-title\">\r\n              <el-icon class=\"title-icon\">\r\n                <Star />\r\n              </el-icon>\r\n              满意度调查\r\n            </div>\r\n            <div class=\"modal-close\" @click=\"handleClose\">\r\n              <el-icon>\r\n                <Close />\r\n              </el-icon>\r\n            </div>\r\n          </div>\r\n          <div class=\"modal-body\">\r\n            <SatisfactionSurvey :data=\"data\" @callback=\"handleCallback\"></SatisfactionSurvey>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Transition>\r\n  </Teleport>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'CustomSatisfactionModal' }\r\n</script>\r\n\r\n<script setup>\r\nimport { Close, Star } from '@element-plus/icons-vue'\r\nimport SatisfactionSurvey from './SatisfactionSurvey.vue'\r\n\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  data: {\r\n    type: Object,\r\n    default: () => ({})\r\n  }\r\n})\r\n\r\nconst emit = defineEmits(['update:modelValue'])\r\n\r\nconst handleClose = () => {\r\n  emit('update:modelValue', false)\r\n}\r\n\r\nconst handleMaskClick = () => {\r\n  emit('update:modelValue', false)\r\n}\r\n\r\nconst handleCallback = () => {\r\n  emit('update:modelValue', false)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.custom-satisfaction-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(4px);\r\n\r\n  .modal-container {\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);\r\n    max-width: 90vw;\r\n    max-height: 90vh;\r\n    overflow: hidden;\r\n    position: relative;\r\n    transform: scale(0.9);\r\n    opacity: 0;\r\n    animation: modalSlideIn 0.3s ease-out forwards;\r\n\r\n    .modal-header {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 20px 24px;\r\n      border-bottom: 1px solid #f0f0f0;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      .modal-title {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        .title-icon {\r\n          font-size: 20px;\r\n          color: #ffd700;\r\n        }\r\n      }\r\n\r\n      .modal-close {\r\n        width: 36px;\r\n        height: 36px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        cursor: pointer;\r\n        transition: all 0.2s ease;\r\n        background: rgba(255, 255, 255, 0.1);\r\n\r\n        &:hover {\r\n          background: rgba(255, 255, 255, 0.2);\r\n          transform: scale(1.1);\r\n        }\r\n\r\n        .el-icon {\r\n          font-size: 18px;\r\n          color: white;\r\n        }\r\n      }\r\n    }\r\n\r\n    .modal-body {\r\n      max-height: calc(90vh - 100px);\r\n      overflow-y: auto;\r\n      \r\n      &::-webkit-scrollbar {\r\n        width: 6px;\r\n      }\r\n      \r\n      &::-webkit-scrollbar-track {\r\n        background: #f1f1f1;\r\n        border-radius: 3px;\r\n      }\r\n      \r\n      &::-webkit-scrollbar-thumb {\r\n        background: #c1c1c1;\r\n        border-radius: 3px;\r\n        \r\n        &:hover {\r\n          background: #a8a8a8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n  0% {\r\n    transform: scale(0.9) translateY(-20px);\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    transform: scale(1) translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n// 过渡动画\r\n.modal-fade-enter-active,\r\n.modal-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.modal-fade-enter-from,\r\n.modal-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.modal-fade-enter-from .modal-container,\r\n.modal-fade-leave-to .modal-container {\r\n  transform: scale(0.9) translateY(-20px);\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .custom-satisfaction-modal {\r\n    padding: 16px;\r\n    \r\n    .modal-container {\r\n      width: 100%;\r\n      max-width: 100%;\r\n      max-height: calc(100vh - 32px);\r\n      \r\n      .modal-header {\r\n        padding: 16px 20px;\r\n        \r\n        .modal-title {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n      \r\n      .modal-body {\r\n        max-height: calc(100vh - 132px);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .custom-satisfaction-modal {\r\n    padding: 8px;\r\n    \r\n    .modal-container {\r\n      .modal-header {\r\n        padding: 12px 16px;\r\n        \r\n        .modal-title {\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";;EAKeA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAYrBA,KAAK,EAAC;AAAY;;;uBAjB/BC,YAAA,CAuBWC,SAAA;IAvBDC,EAAE,EAAC;EAAM,IACjBC,YAAA,CAqBaC,WAAA;IArBDC,IAAI,EAAC;EAAY;IAFjCC,OAAA,EAAAC,QAAA,CAEqC;MAAA,OAoBrB,CAnBCC,MAAA,CAAAC,UAAU,I,cAArBC,mBAAA,CAmBM;QAtBZC,GAAA;QAG6BZ,KAAK,EAAC,2BAA2B;QAAEa,OAAK,EAAEC,MAAA,CAAAC;UAC/DC,mBAAA,CAiBM;QAjBDhB,KAAK,EAAC,iBAAiB;QAAEa,OAAK,EAAAI,MAAA,QAAAA,MAAA,MAJ3CC,cAAA,CAIqC,cAAW;UACtCF,mBAAA,CAYM,OAZNG,UAYM,GAXJH,mBAAA,CAKM,OALNI,UAKM,GAJJhB,YAAA,CAEUiB,kBAAA;QAFDrB,KAAK,EAAC;MAAY;QAPzCO,OAAA,EAAAC,QAAA,CAQgB;UAAA,OAAQ,CAARJ,YAAA,CAAQU,MAAA,U;;QARxBQ,CAAA;oCAAAC,gBAAA,CASwB,SAEZ,G,GACAP,mBAAA,CAIM;QAJDhB,KAAK,EAAC,aAAa;QAAEa,OAAK,EAAEC,MAAA,CAAAU;UAC/BpB,YAAA,CAEUiB,kBAAA;QAfxBd,OAAA,EAAAC,QAAA,CAcgB;UAAA,OAAS,CAATJ,YAAA,CAASU,MAAA,W;;QAdzBQ,CAAA;cAkBUN,mBAAA,CAEM,OAFNS,UAEM,GADJrB,YAAA,CAAiFU,MAAA;QAA5DY,IAAI,EAAEjB,MAAA,CAAAiB,IAAI;QAAGC,UAAQ,EAAEb,MAAA,CAAAc;iDAnBxDC,mBAAA,e;;IAAAP,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}