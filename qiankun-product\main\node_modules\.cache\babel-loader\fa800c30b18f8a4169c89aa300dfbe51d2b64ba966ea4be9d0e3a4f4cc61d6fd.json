{"ast": null, "code": "import { ref, onMounted, computed, defineAsyncComponent } from 'vue';\nimport { systemLogo, systemName, platformAreaName, loginNameLineFeedPosition, appDownloadUrl, systemLoginContact } from 'common/js/system_var.js';\nimport { LoginView } from '../LoginView/LoginView.js';\nvar __default__ = {\n  name: 'LoginViewOne'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var QrcodeVue = defineAsyncComponent(function () {\n      return import('qrcode.vue');\n    });\n    var ResetPassword = defineAsyncComponent(function () {\n      return import('../LoginView/component/ResetPassword.vue');\n    });\n    var show = ref(false);\n    var loginSystemName = computed(function () {\n      var name = (platformAreaName.value || '') + systemName.value;\n      var num = Number(loginNameLineFeedPosition.value || '0') || 0;\n      return num ? name.substring(0, num) + '\\n' + name.substring(num) : name;\n    });\n    var _LoginView = LoginView('/LoginViewOne'),\n      loginVerifyShow = _LoginView.loginVerifyShow,\n      whetherVerifyCode = _LoginView.whetherVerifyCode,\n      loginDisabled = _LoginView.loginDisabled,\n      loading = _LoginView.loading,\n      checked = _LoginView.checked,\n      LoginForm = _LoginView.LoginForm,\n      form = _LoginView.form,\n      rules = _LoginView.rules,\n      countDownText = _LoginView.countDownText,\n      slideVerify = _LoginView.slideVerify,\n      disabled = _LoginView.disabled,\n      loginQrcode = _LoginView.loginQrcode,\n      loginQrcodeShow = _LoginView.loginQrcodeShow,\n      handleBlur = _LoginView.handleBlur,\n      handleGetVerifyCode = _LoginView.handleGetVerifyCode,\n      onAgain = _LoginView.onAgain,\n      onSuccess = _LoginView.onSuccess,\n      submitForm = _LoginView.submitForm,\n      loginInfo = _LoginView.loginInfo,\n      refresh = _LoginView.refresh,\n      hideQrcode = _LoginView.hideQrcode;\n    onMounted(function () {\n      loginInfo();\n    });\n    var __returned__ = {\n      QrcodeVue,\n      ResetPassword,\n      show,\n      loginSystemName,\n      loginVerifyShow,\n      whetherVerifyCode,\n      loginDisabled,\n      loading,\n      checked,\n      LoginForm,\n      form,\n      rules,\n      countDownText,\n      slideVerify,\n      disabled,\n      loginQrcode,\n      loginQrcodeShow,\n      handleBlur,\n      handleGetVerifyCode,\n      onAgain,\n      onSuccess,\n      submitForm,\n      loginInfo,\n      refresh,\n      hideQrcode,\n      ref,\n      onMounted,\n      computed,\n      defineAsyncComponent,\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get platformAreaName() {\n        return platformAreaName;\n      },\n      get loginNameLineFeedPosition() {\n        return loginNameLineFeedPosition;\n      },\n      get appDownloadUrl() {\n        return appDownloadUrl;\n      },\n      get systemLoginContact() {\n        return systemLoginContact;\n      },\n      get LoginView() {\n        return LoginView;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "defineAsyncComponent", "systemLogo", "systemName", "platformAreaName", "loginNameLineFeedPosition", "appDownloadUrl", "systemLoginContact", "<PERSON><PERSON><PERSON>ie<PERSON>", "__default__", "name", "QrcodeVue", "ResetPassword", "show", "loginSystemName", "value", "num", "Number", "substring", "_<PERSON><PERSON><PERSON>ie<PERSON>", "loginVerifyShow", "whetherVerifyCode", "loginDisabled", "loading", "checked", "LoginForm", "form", "rules", "countDownText", "slideVerify", "disabled", "loginQrcode", "loginQrcodeShow", "handleBlur", "handleGetVerifyCode", "onAgain", "onSuccess", "submitForm", "loginInfo", "refresh", "hideQrcode"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LoginViewOne/LoginViewOne.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LoginViewOne\">\r\n    <div class=\"LoginViewOneBox\">\r\n      <div class=\"LoginViewOneLogo\">\r\n        <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n      </div>\r\n      <div class=\"LoginViewOneName\" v-html=\"loginSystemName\"></div>\r\n      <el-form ref=\"LoginForm\" :model=\"form\" :rules=\"rules\" class=\"LoginViewOneForm\">\r\n        <el-form-item prop=\"account\">\r\n          <el-input v-model=\"form.account\" placeholder=\"账号/手机号\" @blur=\"handleBlur\" clearable />\r\n        </el-form-item>\r\n        <el-form-item prop=\"password\">\r\n          <el-input type=\"password\" v-model=\"form.password\" placeholder=\"密码\" show-password clearable />\r\n        </el-form-item>\r\n        <el-form-item class=\"smsValidation\" v-if=\"loginVerifyShow && whetherVerifyCode\" prop=\"verifyCode\">\r\n          <el-input v-model=\"form.verifyCode\" placeholder=\"短信验证码\" clearable></el-input>\r\n          <el-button type=\"primary\" @click=\"handleGetVerifyCode\" :disabled=\"countDownText != '获取验证码'\">\r\n            {{ countDownText }}\r\n          </el-button>\r\n        </el-form-item>\r\n        <div class=\"LoginViewOneSlideVerify\" v-if=\"loginVerifyShow && !whetherVerifyCode\">\r\n          <xyl-slide-verify ref=\"slideVerify\" @again=\"onAgain\" @success=\"onSuccess\" :disabled=\"disabled\" />\r\n        </div>\r\n        <div class=\"LoginViewOneFormOperation\">\r\n          <el-checkbox v-model=\"checked\">记住用户名和密码</el-checkbox>\r\n          <div class=\"LoginViewOneFormOperationText\" @click=\"show = !show\">忘记密码？</div>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"submitForm(LoginForm)\" class=\"LoginViewOneFormButton\" :loading=\"loading\"\r\n          :disabled=\"loginDisabled\">\r\n          {{ loading ? '登录中' : '登录' }}\r\n        </el-button>\r\n      </el-form>\r\n      <div class=\"LoginViewOneOperation\" v-if=\"appDownloadUrl\">\r\n        <div class=\"LoginViewOneOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\" @show=\"refresh\" @hide=\"hideQrcode\">\r\n            <div class=\"LoginViewOneQrCodeBox\">\r\n              <div class=\"LoginViewOneQrCodeNameBody\">\r\n                <div class=\"LoginViewOneQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewOneQrCodeName\">APP扫码登录</div>\r\n              </div>\r\n              <div class=\"LoginViewOneQrCodeRefreshBody\">\r\n                <qrcode-vue :value=\"loginQrcode\" :size=\"120\" />\r\n                <div class=\"LoginViewOneQrCodeRefresh\" v-show=\"loginQrcodeShow\">\r\n                  <el-button type=\"primary\" @click=\"refresh\">刷新</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"LoginViewOneQrCodeText\">请使用{{ systemName }}APP扫码登录</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewOneQrCode\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewOneOperationText\">APP扫码登录</div>\r\n        </div>\r\n        <div class=\"LoginViewOneOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\">\r\n            <div class=\"LoginViewOneQrCodeBox\">\r\n              <div class=\"LoginViewOneQrCodeNameBody\">\r\n                <div class=\"LoginViewOneQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewOneQrCodeName\">手机APP下载</div>\r\n              </div>\r\n              <qrcode-vue :value=\"appDownloadUrl\" :size=\"120\" />\r\n              <div class=\"LoginViewOneQrCodeText\">使用其他软件扫码下载{{ systemName }}APP</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewOneApp\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewOneOperationText\">手机APP下载</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"LoginViewOneSystemTips\" v-if=\"systemLoginContact\">{{ systemLoginContact }}</div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"重置密码\">\r\n      <ResetPassword @callback=\"show = !show\"></ResetPassword>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LoginViewOne' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted, computed, defineAsyncComponent } from 'vue'\r\nimport {\r\n  systemLogo,\r\n  systemName,\r\n  platformAreaName,\r\n  loginNameLineFeedPosition,\r\n  appDownloadUrl,\r\n  systemLoginContact\r\n} from 'common/js/system_var.js'\r\nimport { LoginView } from '../LoginView/LoginView.js'\r\nconst QrcodeVue = defineAsyncComponent(() => import('qrcode.vue'))\r\nconst ResetPassword = defineAsyncComponent(() => import('../LoginView/component/ResetPassword.vue'))\r\nconst show = ref(false)\r\nconst loginSystemName = computed(() => {\r\n  const name = (platformAreaName.value || '') + systemName.value\r\n  const num = Number(loginNameLineFeedPosition.value || '0') || 0\r\n  return num ? name.substring(0, num) + '\\n' + name.substring(num) : name\r\n})\r\nconst {\r\n  loginVerifyShow,\r\n  whetherVerifyCode,\r\n  loginDisabled,\r\n  loading,\r\n  checked,\r\n  LoginForm,\r\n  form,\r\n  rules,\r\n  countDownText,\r\n  slideVerify,\r\n  disabled,\r\n  loginQrcode,\r\n  loginQrcodeShow,\r\n  handleBlur,\r\n  handleGetVerifyCode,\r\n  onAgain,\r\n  onSuccess,\r\n  submitForm,\r\n  loginInfo,\r\n  refresh,\r\n  hideQrcode\r\n} = LoginView('/LoginViewOne')\r\nonMounted(() => {\r\n  loginInfo()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.LoginViewOne {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 50%;\r\n    background: var(--zy-el-color-primary);\r\n  }\r\n\r\n  .LoginViewOneBox {\r\n    padding: var(--zy-distance-one);\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    padding-bottom: var(--zy-distance-two);\r\n    border-radius: var(--el-border-radius-base);\r\n    background: #fff;\r\n    position: relative;\r\n    z-index: 2;\r\n\r\n    .LoginViewOneLogo {\r\n      width: 60px;\r\n      margin: auto;\r\n      margin-bottom: var(--zy-distance-two);\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .LoginViewOneName {\r\n      width: 320px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-align: center;\r\n      font-size: var(--zy-system-font-size);\r\n      line-height: var(--zy-line-height);\r\n      font-weight: bold;\r\n      letter-spacing: 2px;\r\n      padding-bottom: var(--zy-distance-one);\r\n      white-space: pre-wrap;\r\n      margin: auto;\r\n    }\r\n\r\n    .LoginViewOneForm {\r\n      width: 320px;\r\n      margin: auto;\r\n      padding-bottom: var(--zy-distance-one);\r\n\r\n      input:-webkit-autofill {\r\n        transition: background-color 5000s ease-in-out 0s;\r\n      }\r\n\r\n      .zy-el-form-item {\r\n        margin-bottom: var(--zy-form-distance-bottom);\r\n      }\r\n\r\n      .LoginViewOneFormButton {\r\n        width: 100%;\r\n      }\r\n\r\n      .smsValidation {\r\n        .zy-el-form-item__content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n        }\r\n\r\n        .zy-el-input {\r\n          width: 56%;\r\n        }\r\n      }\r\n\r\n      .LoginViewOneSlideVerify {\r\n        margin-bottom: var(--zy-distance-five);\r\n      }\r\n\r\n      .LoginViewOneFormOperation {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: var(--zy-distance-three);\r\n\r\n        .zy-el-checkbox {\r\n          height: var(--zy-height-secondary);\r\n        }\r\n\r\n        .LoginViewOneFormOperationText {\r\n          cursor: pointer;\r\n          color: var(--zy-el-color-primary);\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewOneOperation {\r\n      width: 100%;\r\n      padding-bottom: var(--zy-distance-two);\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .LoginViewOneOperationBox {\r\n        margin: 0 var(--zy-distance-two);\r\n        cursor: pointer;\r\n\r\n        .LoginViewOneQrCode {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url('../img/login_qr_code.png');\r\n          background-size: 100% 100%;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewOneApp {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url('../img/login_app.png') no-repeat;\r\n          background-size: auto 100%;\r\n          background-position: center;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewOneOperationText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: var(--el-border-radius-small) 0;\r\n          text-align: center;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewOneForm+.LoginViewOneSystemTips {\r\n      padding-top: var(--zy-distance-one);\r\n    }\r\n\r\n    .LoginViewOneSystemTips {\r\n      color: var(--zy-el-text-color-secondary);\r\n      font-size: var(--zy-text-font-size);\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.LoginViewOneQrCodeBox {\r\n  width: 320px;\r\n  background-color: #fff;\r\n\r\n  canvas {\r\n    display: block;\r\n    margin: auto;\r\n  }\r\n\r\n  .LoginViewOneQrCodeNameBody {\r\n    padding: var(--zy-distance-three);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LoginViewOneQrCodeLogo {\r\n      width: 26px;\r\n      margin-right: 6px;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .LoginViewOneQrCodeName {\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .LoginViewOneQrCodeRefreshBody {\r\n    position: relative;\r\n\r\n    .LoginViewOneQrCodeRefresh {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 120px;\r\n      height: 120px;\r\n      background-color: rgba(000, 000, 000, 0.6);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .zy-el-button {\r\n        --zy-el-button-size: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewOneQrCodeText {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding: var(--zy-distance-three);\r\n    color: var(--zy-el-color-primary);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAsFA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,KAAK;AACpE,SACEC,UAAU,EACVC,UAAU,EACVC,gBAAgB,EAChBC,yBAAyB,EACzBC,cAAc,EACdC,kBAAkB,QACb,yBAAyB;AAChC,SAASC,SAAS,QAAQ,2BAA2B;AAZrD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAe,CAAC;;;;;IAavC,IAAMC,SAAS,GAAGV,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,YAAY,CAAC;IAAA,EAAC;IAClE,IAAMW,aAAa,GAAGX,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,0CAA0C,CAAC;IAAA,EAAC;IACpG,IAAMY,IAAI,GAAGf,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMgB,eAAe,GAAGd,QAAQ,CAAC,YAAM;MACrC,IAAMU,IAAI,GAAG,CAACN,gBAAgB,CAACW,KAAK,IAAI,EAAE,IAAIZ,UAAU,CAACY,KAAK;MAC9D,IAAMC,GAAG,GAAGC,MAAM,CAACZ,yBAAyB,CAACU,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC;MAC/D,OAAOC,GAAG,GAAGN,IAAI,CAACQ,SAAS,CAAC,CAAC,EAAEF,GAAG,CAAC,GAAG,IAAI,GAAGN,IAAI,CAACQ,SAAS,CAACF,GAAG,CAAC,GAAGN,IAAI;IACzE,CAAC,CAAC;IACF,IAAAS,UAAA,GAsBIX,SAAS,CAAC,eAAe,CAAC;MArB5BY,eAAe,GAAAD,UAAA,CAAfC,eAAe;MACfC,iBAAiB,GAAAF,UAAA,CAAjBE,iBAAiB;MACjBC,aAAa,GAAAH,UAAA,CAAbG,aAAa;MACbC,OAAO,GAAAJ,UAAA,CAAPI,OAAO;MACPC,OAAO,GAAAL,UAAA,CAAPK,OAAO;MACPC,SAAS,GAAAN,UAAA,CAATM,SAAS;MACTC,IAAI,GAAAP,UAAA,CAAJO,IAAI;MACJC,KAAK,GAAAR,UAAA,CAALQ,KAAK;MACLC,aAAa,GAAAT,UAAA,CAAbS,aAAa;MACbC,WAAW,GAAAV,UAAA,CAAXU,WAAW;MACXC,QAAQ,GAAAX,UAAA,CAARW,QAAQ;MACRC,WAAW,GAAAZ,UAAA,CAAXY,WAAW;MACXC,eAAe,GAAAb,UAAA,CAAfa,eAAe;MACfC,UAAU,GAAAd,UAAA,CAAVc,UAAU;MACVC,mBAAmB,GAAAf,UAAA,CAAnBe,mBAAmB;MACnBC,OAAO,GAAAhB,UAAA,CAAPgB,OAAO;MACPC,SAAS,GAAAjB,UAAA,CAATiB,SAAS;MACTC,UAAU,GAAAlB,UAAA,CAAVkB,UAAU;MACVC,SAAS,GAAAnB,UAAA,CAATmB,SAAS;MACTC,OAAO,GAAApB,UAAA,CAAPoB,OAAO;MACPC,UAAU,GAAArB,UAAA,CAAVqB,UAAU;IAEZzC,SAAS,CAAC,YAAM;MACduC,SAAS,CAAC,CAAC;IACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}