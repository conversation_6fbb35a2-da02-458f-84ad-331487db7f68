hoistPattern:
  - '*'
hoistedDependencies:
  /@achrinza/node-ipc/9.2.9:
    '@achrinza/node-ipc': private
  /@amap/amap-jsapi-loader/1.0.1:
    '@amap/amap-jsapi-loader': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@antfu/utils/0.7.10:
    '@antfu/utils': private
  /@babel/code-frame/7.26.2:
    '@babel/code-frame': private
  /@babel/compat-data/7.26.2:
    '@babel/compat-data': private
  /@babel/core/7.26.0:
    '@babel/core': private
  /@babel/eslint-parser/7.25.9_5halpyz7ntfch2rsjwvdflzl7u:
    '@babel/eslint-parser': public
  /@babel/generator/7.26.2:
    '@babel/generator': private
  /@babel/helper-annotate-as-pure/7.25.9:
    '@babel/helper-annotate-as-pure': private
  /@babel/helper-builder-binary-assignment-operator-visitor/7.25.9:
    '@babel/helper-builder-binary-assignment-operator-visitor': private
  /@babel/helper-compilation-targets/7.25.9:
    '@babel/helper-compilation-targets': private
  /@babel/helper-create-class-features-plugin/7.25.9_@babel+core@7.26.0:
    '@babel/helper-create-class-features-plugin': private
  /@babel/helper-create-regexp-features-plugin/7.25.9_@babel+core@7.26.0:
    '@babel/helper-create-regexp-features-plugin': private
  /@babel/helper-define-polyfill-provider/0.6.3_@babel+core@7.26.0:
    '@babel/helper-define-polyfill-provider': private
  /@babel/helper-member-expression-to-functions/7.25.9:
    '@babel/helper-member-expression-to-functions': private
  /@babel/helper-module-imports/7.25.9:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.26.0_@babel+core@7.26.0:
    '@babel/helper-module-transforms': private
  /@babel/helper-optimise-call-expression/7.25.9:
    '@babel/helper-optimise-call-expression': private
  /@babel/helper-plugin-utils/7.25.9:
    '@babel/helper-plugin-utils': private
  /@babel/helper-remap-async-to-generator/7.25.9_@babel+core@7.26.0:
    '@babel/helper-remap-async-to-generator': private
  /@babel/helper-replace-supers/7.25.9_@babel+core@7.26.0:
    '@babel/helper-replace-supers': private
  /@babel/helper-simple-access/7.25.9:
    '@babel/helper-simple-access': private
  /@babel/helper-skip-transparent-expression-wrappers/7.25.9:
    '@babel/helper-skip-transparent-expression-wrappers': private
  /@babel/helper-string-parser/7.25.9:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.25.9:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.25.9:
    '@babel/helper-validator-option': private
  /@babel/helper-wrap-function/7.25.9:
    '@babel/helper-wrap-function': private
  /@babel/helpers/7.26.0:
    '@babel/helpers': private
  /@babel/highlight/7.25.9:
    '@babel/highlight': private
  /@babel/parser/7.26.2:
    '@babel/parser': private
  /@babel/plugin-bugfix-firefox-class-in-computed-class-key/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  /@babel/plugin-bugfix-safari-class-field-initializer-scope/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  /@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  /@babel/plugin-proposal-class-properties/7.18.6_@babel+core@7.26.0:
    '@babel/plugin-proposal-class-properties': private
  /@babel/plugin-proposal-decorators/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-proposal-decorators': private
  /@babel/plugin-proposal-private-methods/7.18.6_@babel+core@7.26.0:
    '@babel/plugin-proposal-private-methods': private
  /@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2_@babel+core@7.26.0:
    '@babel/plugin-proposal-private-property-in-object': private
  /@babel/plugin-syntax-decorators/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-syntax-decorators': private
  /@babel/plugin-syntax-dynamic-import/7.8.3_@babel+core@7.26.0:
    '@babel/plugin-syntax-dynamic-import': private
  /@babel/plugin-syntax-import-assertions/7.26.0_@babel+core@7.26.0:
    '@babel/plugin-syntax-import-assertions': private
  /@babel/plugin-syntax-import-attributes/7.26.0_@babel+core@7.26.0:
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-jsx/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-unicode-sets-regex/7.18.6_@babel+core@7.26.0:
    '@babel/plugin-syntax-unicode-sets-regex': private
  /@babel/plugin-transform-arrow-functions/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-arrow-functions': private
  /@babel/plugin-transform-async-generator-functions/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-async-generator-functions': private
  /@babel/plugin-transform-async-to-generator/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-async-to-generator': private
  /@babel/plugin-transform-block-scoped-functions/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-block-scoped-functions': private
  /@babel/plugin-transform-block-scoping/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-block-scoping': private
  /@babel/plugin-transform-class-properties/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-class-properties': private
  /@babel/plugin-transform-class-static-block/7.26.0_@babel+core@7.26.0:
    '@babel/plugin-transform-class-static-block': private
  /@babel/plugin-transform-classes/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-classes': private
  /@babel/plugin-transform-computed-properties/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-computed-properties': private
  /@babel/plugin-transform-destructuring/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-destructuring': private
  /@babel/plugin-transform-dotall-regex/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-dotall-regex': private
  /@babel/plugin-transform-duplicate-keys/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-duplicate-keys': private
  /@babel/plugin-transform-duplicate-named-capturing-groups-regex/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  /@babel/plugin-transform-dynamic-import/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-dynamic-import': private
  /@babel/plugin-transform-exponentiation-operator/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-exponentiation-operator': private
  /@babel/plugin-transform-export-namespace-from/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-export-namespace-from': private
  /@babel/plugin-transform-for-of/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-for-of': private
  /@babel/plugin-transform-function-name/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-function-name': private
  /@babel/plugin-transform-json-strings/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-json-strings': private
  /@babel/plugin-transform-literals/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-literals': private
  /@babel/plugin-transform-logical-assignment-operators/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-logical-assignment-operators': private
  /@babel/plugin-transform-member-expression-literals/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-member-expression-literals': private
  /@babel/plugin-transform-modules-amd/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-modules-amd': private
  /@babel/plugin-transform-modules-commonjs/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-modules-commonjs': private
  /@babel/plugin-transform-modules-systemjs/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-modules-systemjs': private
  /@babel/plugin-transform-modules-umd/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-modules-umd': private
  /@babel/plugin-transform-named-capturing-groups-regex/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-named-capturing-groups-regex': private
  /@babel/plugin-transform-new-target/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-new-target': private
  /@babel/plugin-transform-nullish-coalescing-operator/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-nullish-coalescing-operator': private
  /@babel/plugin-transform-numeric-separator/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-numeric-separator': private
  /@babel/plugin-transform-object-rest-spread/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-object-rest-spread': private
  /@babel/plugin-transform-object-super/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-object-super': private
  /@babel/plugin-transform-optional-catch-binding/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-optional-catch-binding': private
  /@babel/plugin-transform-optional-chaining/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-optional-chaining': private
  /@babel/plugin-transform-parameters/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-parameters': private
  /@babel/plugin-transform-private-methods/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-private-methods': private
  /@babel/plugin-transform-private-property-in-object/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-private-property-in-object': private
  /@babel/plugin-transform-property-literals/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-property-literals': private
  /@babel/plugin-transform-regenerator/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-regenerator': private
  /@babel/plugin-transform-regexp-modifiers/7.26.0_@babel+core@7.26.0:
    '@babel/plugin-transform-regexp-modifiers': private
  /@babel/plugin-transform-reserved-words/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-reserved-words': private
  /@babel/plugin-transform-runtime/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-runtime': private
  /@babel/plugin-transform-shorthand-properties/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-shorthand-properties': private
  /@babel/plugin-transform-spread/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-spread': private
  /@babel/plugin-transform-sticky-regex/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-sticky-regex': private
  /@babel/plugin-transform-template-literals/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-template-literals': private
  /@babel/plugin-transform-typeof-symbol/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-typeof-symbol': private
  /@babel/plugin-transform-unicode-escapes/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-unicode-escapes': private
  /@babel/plugin-transform-unicode-property-regex/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-unicode-property-regex': private
  /@babel/plugin-transform-unicode-regex/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-unicode-regex': private
  /@babel/plugin-transform-unicode-sets-regex/7.25.9_@babel+core@7.26.0:
    '@babel/plugin-transform-unicode-sets-regex': private
  /@babel/preset-env/7.26.0_@babel+core@7.26.0:
    '@babel/preset-env': private
  /@babel/preset-modules/0.1.6-no-external-plugins_@babel+core@7.26.0:
    '@babel/preset-modules': private
  /@babel/runtime/7.26.0:
    '@babel/runtime': private
  /@babel/template/7.25.9:
    '@babel/template': private
  /@babel/traverse/7.25.9:
    '@babel/traverse': private
  /@babel/types/7.26.0:
    '@babel/types': private
  /@braintree/sanitize-url/3.1.0:
    '@braintree/sanitize-url': private
  /@ctrl/tinycolor/3.6.1:
    '@ctrl/tinycolor': private
  /@datatraccorporation/markdown-it-mermaid/0.5.0:
    '@DatatracCorporation/markdown-it-mermaid': private
  /@discoveryjs/json-ext/0.5.7:
    '@discoveryjs/json-ext': private
  /@element-plus/icons-vue/2.3.1_vue@3.5.12:
    '@element-plus/icons-vue': private
  /@eslint/eslintrc/0.4.3:
    '@eslint/eslintrc': public
  /@fast-csv/format/4.3.5:
    '@fast-csv/format': private
  /@fast-csv/parse/4.3.6:
    '@fast-csv/parse': private
  /@floating-ui/core/1.6.8:
    '@floating-ui/core': private
  /@floating-ui/dom/1.6.12:
    '@floating-ui/dom': private
  /@floating-ui/utils/0.2.8:
    '@floating-ui/utils': private
  /@hapi/hoek/9.3.0:
    '@hapi/hoek': private
  /@hapi/topo/5.1.0:
    '@hapi/topo': private
  /@humanwhocodes/config-array/0.5.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/object-schema/1.2.1:
    '@humanwhocodes/object-schema': private
  /@javascript-obfuscator/escodegen/2.3.0:
    '@javascript-obfuscator/escodegen': private
  /@javascript-obfuscator/estraverse/5.4.0:
    '@javascript-obfuscator/estraverse': private
  /@jridgewell/gen-mapping/0.3.5:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  /@jridgewell/source-map/0.3.6:
    '@jridgewell/source-map': private
  /@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.25:
    '@jridgewell/trace-mapping': private
  /@leichtgewicht/ip-codec/2.0.5:
    '@leichtgewicht/ip-codec': private
  /@microsoft/fetch-event-source/2.0.1:
    '@microsoft/fetch-event-source': private
  /@nicolo-ribaudo/eslint-scope-5-internals/5.1.1-v1:
    '@nicolo-ribaudo/eslint-scope-5-internals': public
  /@node-ipc/js-queue/2.0.3:
    '@node-ipc/js-queue': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@parcel/watcher-android-arm64/2.5.0:
    '@parcel/watcher-android-arm64': private
  /@parcel/watcher-darwin-arm64/2.5.0:
    '@parcel/watcher-darwin-arm64': private
  /@parcel/watcher-darwin-x64/2.5.0:
    '@parcel/watcher-darwin-x64': private
  /@parcel/watcher-freebsd-x64/2.5.0:
    '@parcel/watcher-freebsd-x64': private
  /@parcel/watcher-linux-arm-glibc/2.5.0:
    '@parcel/watcher-linux-arm-glibc': private
  /@parcel/watcher-linux-arm-musl/2.5.0:
    '@parcel/watcher-linux-arm-musl': private
  /@parcel/watcher-linux-arm64-glibc/2.5.0:
    '@parcel/watcher-linux-arm64-glibc': private
  /@parcel/watcher-linux-arm64-musl/2.5.0:
    '@parcel/watcher-linux-arm64-musl': private
  /@parcel/watcher-linux-x64-glibc/2.5.0:
    '@parcel/watcher-linux-x64-glibc': private
  /@parcel/watcher-linux-x64-musl/2.5.0:
    '@parcel/watcher-linux-x64-musl': private
  /@parcel/watcher-win32-arm64/2.5.0:
    '@parcel/watcher-win32-arm64': private
  /@parcel/watcher-win32-ia32/2.5.0:
    '@parcel/watcher-win32-ia32': private
  /@parcel/watcher-win32-x64/2.5.0:
    '@parcel/watcher-win32-x64': private
  /@parcel/watcher/2.5.0:
    '@parcel/watcher': private
  /@polka/url/1.0.0-next.28:
    '@polka/url': private
  /@rollup/pluginutils/5.1.3:
    '@rollup/pluginutils': private
  /@sideway/address/4.1.5:
    '@sideway/address': private
  /@sideway/formula/3.0.1:
    '@sideway/formula': private
  /@sideway/pinpoint/2.0.0:
    '@sideway/pinpoint': private
  /@soda/friendly-errors-webpack-plugin/1.8.1_webpack@5.96.1:
    '@soda/friendly-errors-webpack-plugin': private
  /@soda/get-current-script/1.0.2:
    '@soda/get-current-script': private
  /@sxzz/popperjs-es/2.11.7:
    '@popperjs/core': private
  /@trysound/sax/0.2.0:
    '@trysound/sax': private
  /@types/body-parser/1.19.5:
    '@types/body-parser': private
  /@types/bonjour/3.5.13:
    '@types/bonjour': private
  /@types/connect-history-api-fallback/1.5.4:
    '@types/connect-history-api-fallback': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/eslint-scope/3.7.7:
    '@types/eslint-scope': public
  /@types/eslint/8.56.12:
    '@types/eslint': public
  /@types/estree/1.0.6:
    '@types/estree': private
  /@types/express-serve-static-core/5.0.1:
    '@types/express-serve-static-core': private
  /@types/express/4.17.21:
    '@types/express': private
  /@types/html-minifier-terser/6.1.0:
    '@types/html-minifier-terser': private
  /@types/http-errors/2.0.4:
    '@types/http-errors': private
  /@types/http-proxy/1.17.15:
    '@types/http-proxy': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/lodash-es/4.17.12:
    '@types/lodash-es': private
  /@types/lodash/4.17.13:
    '@types/lodash': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/minimatch/3.0.5:
    '@types/minimatch': private
  /@types/minimist/1.2.5:
    '@types/minimist': private
  /@types/node-forge/1.3.11:
    '@types/node-forge': private
  /@types/node/14.18.63:
    '@types/node': private
  /@types/normalize-package-data/2.4.4:
    '@types/normalize-package-data': private
  /@types/parse-json/4.0.2:
    '@types/parse-json': private
  /@types/qs/6.9.17:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/retry/0.12.0:
    '@types/retry': private
  /@types/send/0.17.4:
    '@types/send': private
  /@types/serve-index/1.9.4:
    '@types/serve-index': private
  /@types/serve-static/1.15.7:
    '@types/serve-static': private
  /@types/sockjs/0.3.36:
    '@types/sockjs': private
  /@types/validator/13.12.2:
    '@types/validator': private
  /@types/web-bluetooth/0.0.16:
    '@types/web-bluetooth': private
  /@types/ws/8.5.13:
    '@types/ws': private
  /@videojs-player/vue/1.0.0_video.js@8.19.1+vue@3.5.12:
    '@videojs-player/vue': private
  /@videojs/http-streaming/3.15.0:
    '@videojs/http-streaming': private
  /@videojs/vhs-utils/4.1.1:
    '@videojs/vhs-utils': private
  /@videojs/xhr/2.7.0:
    '@videojs/xhr': private
  /@vue-office/excel/1.7.14_izysylmlogu5i6lej63uxrpfjm:
    '@vue-office/excel': private
  /@vue-office/pdf/2.0.10_izysylmlogu5i6lej63uxrpfjm:
    '@vue-office/pdf': private
  /@vue-office/pptx/1.0.1_izysylmlogu5i6lej63uxrpfjm:
    '@vue-office/pptx': private
  /@vue/babel-helper-vue-jsx-merge-props/1.4.0:
    '@vue/babel-helper-vue-jsx-merge-props': private
  /@vue/babel-helper-vue-transform-on/1.2.5:
    '@vue/babel-helper-vue-transform-on': private
  /@vue/babel-plugin-jsx/1.2.5_@babel+core@7.26.0:
    '@vue/babel-plugin-jsx': private
  /@vue/babel-plugin-resolve-type/1.2.5_@babel+core@7.26.0:
    '@vue/babel-plugin-resolve-type': private
  /@vue/babel-plugin-transform-vue-jsx/1.4.0_@babel+core@7.26.0:
    '@vue/babel-plugin-transform-vue-jsx': private
  /@vue/babel-preset-app/5.0.8_vue@3.5.12:
    '@vue/babel-preset-app': private
  /@vue/babel-preset-jsx/1.4.0_dmc4s4fmf7m3aknne2bqvm6rru:
    '@vue/babel-preset-jsx': private
  /@vue/babel-sugar-composition-api-inject-h/1.4.0_@babel+core@7.26.0:
    '@vue/babel-sugar-composition-api-inject-h': private
  /@vue/babel-sugar-composition-api-render-instance/1.4.0_@babel+core@7.26.0:
    '@vue/babel-sugar-composition-api-render-instance': private
  /@vue/babel-sugar-functional-vue/1.4.0_@babel+core@7.26.0:
    '@vue/babel-sugar-functional-vue': private
  /@vue/babel-sugar-inject-h/1.4.0_@babel+core@7.26.0:
    '@vue/babel-sugar-inject-h': private
  /@vue/babel-sugar-v-model/1.4.0_@babel+core@7.26.0:
    '@vue/babel-sugar-v-model': private
  /@vue/babel-sugar-v-on/1.4.0_@babel+core@7.26.0:
    '@vue/babel-sugar-v-on': private
  /@vue/cli-overlay/5.0.8:
    '@vue/cli-overlay': private
  /@vue/cli-plugin-babel/5.0.8_eavnbawsmdhce67dk3dhk2ekl4:
    '@vue/cli-plugin-babel': private
  /@vue/cli-plugin-eslint/5.0.8_yvnkslq6l73c7sesu7e5hzum2a:
    '@vue/cli-plugin-eslint': public
  /@vue/cli-plugin-router/5.0.8_@vue+cli-service@5.0.8:
    '@vue/cli-plugin-router': private
  /@vue/cli-plugin-vuex/5.0.8_@vue+cli-service@5.0.8:
    '@vue/cli-plugin-vuex': private
  /@vue/cli-service/5.0.8_4jp72jak5eviujki75df3sfmju:
    '@vue/cli-service': private
  /@vue/cli-shared-utils/5.0.8:
    '@vue/cli-shared-utils': private
  /@vue/compiler-core/3.5.12:
    '@vue/compiler-core': private
  /@vue/compiler-dom/3.5.12:
    '@vue/compiler-dom': private
  /@vue/compiler-sfc/3.5.12:
    '@vue/compiler-sfc': private
  /@vue/compiler-ssr/3.5.12:
    '@vue/compiler-ssr': private
  /@vue/component-compiler-utils/3.3.0:
    '@vue/component-compiler-utils': private
  /@vue/devtools-api/6.6.4:
    '@vue/devtools-api': private
  /@vue/reactivity/3.5.12:
    '@vue/reactivity': private
  /@vue/runtime-core/3.5.12:
    '@vue/runtime-core': private
  /@vue/runtime-dom/3.5.12:
    '@vue/runtime-dom': private
  /@vue/server-renderer/3.5.12_vue@3.5.12:
    '@vue/server-renderer': private
  /@vue/shared/3.5.12:
    '@vue/shared': private
  /@vue/web-component-wrapper/1.3.0:
    '@vue/web-component-wrapper': private
  /@vueuse/core/9.13.0_vue@3.5.12:
    '@vueuse/core': private
  /@vueuse/metadata/9.13.0:
    '@vueuse/metadata': private
  /@vueuse/shared/9.13.0_vue@3.5.12:
    '@vueuse/shared': private
  /@webassemblyjs/ast/1.14.1:
    '@webassemblyjs/ast': private
  /@webassemblyjs/floating-point-hex-parser/1.13.2:
    '@webassemblyjs/floating-point-hex-parser': private
  /@webassemblyjs/helper-api-error/1.13.2:
    '@webassemblyjs/helper-api-error': private
  /@webassemblyjs/helper-buffer/1.14.1:
    '@webassemblyjs/helper-buffer': private
  /@webassemblyjs/helper-numbers/1.13.2:
    '@webassemblyjs/helper-numbers': private
  /@webassemblyjs/helper-wasm-bytecode/1.13.2:
    '@webassemblyjs/helper-wasm-bytecode': private
  /@webassemblyjs/helper-wasm-section/1.14.1:
    '@webassemblyjs/helper-wasm-section': private
  /@webassemblyjs/ieee754/1.13.2:
    '@webassemblyjs/ieee754': private
  /@webassemblyjs/leb128/1.13.2:
    '@webassemblyjs/leb128': private
  /@webassemblyjs/utf8/1.13.2:
    '@webassemblyjs/utf8': private
  /@webassemblyjs/wasm-edit/1.14.1:
    '@webassemblyjs/wasm-edit': private
  /@webassemblyjs/wasm-gen/1.14.1:
    '@webassemblyjs/wasm-gen': private
  /@webassemblyjs/wasm-opt/1.14.1:
    '@webassemblyjs/wasm-opt': private
  /@webassemblyjs/wasm-parser/1.14.1:
    '@webassemblyjs/wasm-parser': private
  /@webassemblyjs/wast-printer/1.14.1:
    '@webassemblyjs/wast-printer': private
  /@xmldom/xmldom/0.8.10:
    '@xmldom/xmldom': private
  /@xtuc/ieee754/1.2.0:
    '@xtuc/ieee754': private
  /@xtuc/long/4.2.2:
    '@xtuc/long': private
  /@ylink-sdk/web/0.7.4:
    '@ylink-sdk/web': private
  /accepts/1.3.8:
    accepts: private
  /acorn-jsx/5.3.2_acorn@7.4.1:
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.14.0:
    acorn: private
  /address/1.2.2:
    address: private
  /aes-decrypter/4.0.2:
    aes-decrypter: private
  /after/0.8.2:
    after: private
  /ajv-formats/2.1.1:
    ajv-formats: private
  /ajv-keywords/3.5.2_ajv@6.12.6:
    ajv-keywords: private
  /ajv/6.12.6:
    ajv: private
  /amdefine/1.0.1:
    amdefine: private
  /animate.css/4.1.1:
    animate.css: private
  /ansi-colors/4.1.3:
    ansi-colors: private
  /ansi-escapes/3.2.0:
    ansi-escapes: private
  /ansi-html-community/0.0.8:
    ansi-html-community: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arch/2.2.0:
    arch: private
  /archiver-utils/2.1.0:
    archiver-utils: private
  /archiver/5.3.2:
    archiver: private
  /argparse/2.0.1:
    argparse: private
  /array-differ/3.0.0:
    array-differ: private
  /array-flatten/1.1.1:
    array-flatten: private
  /array-union/2.1.0:
    array-union: private
  /arraybuffer.slice/0.0.7:
    arraybuffer.slice: private
  /arrify/2.0.1:
    arrify: private
  /assert/2.0.0:
    assert: private
  /astral-regex/2.0.0:
    astral-regex: private
  /async-validator/4.2.5:
    async-validator: private
  /async/3.2.6:
    async: private
  /at-least-node/1.0.0:
    at-least-node: private
  /autoprefixer/10.4.20_postcss@8.4.49:
    autoprefixer: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /axios/0.21.4:
    axios: private
  /babel-loader/8.4.1_yktivyy3t55lzef7wjs5tfhpte:
    babel-loader: private
  /babel-plugin-dynamic-import-node/2.3.3:
    babel-plugin-dynamic-import-node: private
  /babel-plugin-polyfill-corejs2/0.4.12_@babel+core@7.26.0:
    babel-plugin-polyfill-corejs2: private
  /babel-plugin-polyfill-corejs3/0.10.6_@babel+core@7.26.0:
    babel-plugin-polyfill-corejs3: private
  /babel-plugin-polyfill-regenerator/0.6.3_@babel+core@7.26.0:
    babel-plugin-polyfill-regenerator: private
  /backo2/1.0.2:
    backo2: private
  /balanced-match/1.0.2:
    balanced-match: private
  /base64-arraybuffer/0.1.4:
    base64-arraybuffer: private
  /base64-js/1.5.1:
    base64-js: private
  /batch-processor/1.0.0:
    batch-processor: private
  /batch/0.6.1:
    batch: private
  /big-integer/1.6.52:
    big-integer: private
  /big.js/5.2.2:
    big.js: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /binary/0.3.0:
    binary: private
  /bl/4.1.0:
    bl: private
  /blob/0.0.5:
    blob: private
  /bluebird/3.4.7:
    bluebird: private
  /body-parser/1.20.3:
    body-parser: private
  /bonjour-service/1.2.1:
    bonjour-service: private
  /boolbase/1.0.0:
    boolbase: private
  /brace-expansion/1.1.11:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.24.2:
    browserslist: private
  /buffer-crc32/0.2.13:
    buffer-crc32: private
  /buffer-from/1.1.2:
    buffer-from: private
  /buffer-indexof-polyfill/1.0.2:
    buffer-indexof-polyfill: private
  /buffer/5.7.1:
    buffer: private
  /buffers/0.1.1:
    buffers: private
  /bytes/3.1.2:
    bytes: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.7:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camel-case/4.1.2:
    camel-case: private
  /camelcase/1.2.1:
    camelcase: private
  /caniuse-api/3.0.0:
    caniuse-api: private
  /caniuse-lite/1.0.30001680:
    caniuse-lite: private
  /case-sensitive-paths-webpack-plugin/2.4.0:
    case-sensitive-paths-webpack-plugin: private
  /chainsaw/0.1.0:
    chainsaw: private
  /chalk/4.1.2:
    chalk: private
  /chance/1.1.9:
    chance: private
  /char-regex/1.0.2:
    char-regex: private
  /charenc/0.0.2:
    charenc: private
  /chokidar/4.0.1:
    chokidar: private
  /chrome-trace-event/1.0.4:
    chrome-trace-event: private
  /ci-info/1.6.0:
    ci-info: private
  /class-validator/0.14.1:
    class-validator: private
  /claygl/1.3.0:
    claygl: private
  /clean-css/5.3.3:
    clean-css: private
  /cli-cursor/2.1.0:
    cli-cursor: private
  /cli-highlight/2.1.11:
    cli-highlight: private
  /cli-spinners/2.9.2:
    cli-spinners: private
  /clipboard/2.0.11:
    clipboard: private
  /clipboardy/2.3.0:
    clipboardy: private
  /cliui/7.0.4:
    cliui: private
  /clone-deep/4.0.1:
    clone-deep: private
  /clone/1.0.4:
    clone: private
  /color-convert/1.9.3:
    color-convert: private
  /color-name/1.1.3:
    color-name: private
  /colord/2.9.3:
    colord: private
  /colorette/2.0.20:
    colorette: private
  /commander/10.0.0:
    commander: private
  /commondir/1.0.1:
    commondir: private
  /component-bind/1.0.0:
    component-bind: private
  /component-emitter/1.3.1:
    component-emitter: private
  /component-inherit/0.0.3:
    component-inherit: private
  /compress-commons/4.1.2:
    compress-commons: private
  /compressible/2.0.18:
    compressible: private
  /compression-webpack-plugin/11.1.0:
    compression-webpack-plugin: private
  /compression/1.7.5:
    compression: private
  /comutils/1.1.19:
    comutils: private
  /concat-map/0.0.1:
    concat-map: private
  /confbox/0.1.8:
    confbox: private
  /connect-history-api-fallback/2.0.0:
    connect-history-api-fallback: private
  /consolidate/0.15.1:
    consolidate: private
  /content-disposition/0.5.4:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie-signature/1.0.6:
    cookie-signature: private
  /cookie/0.7.1:
    cookie: private
  /copy-webpack-plugin/9.1.0_webpack@5.96.1:
    copy-webpack-plugin: private
  /core-js-compat/3.39.0:
    core-js-compat: private
  /core-js/3.39.0:
    core-js: private
  /core-util-is/1.0.3:
    core-util-is: private
  /cos-js-sdk-v5/1.8.6:
    cos-js-sdk-v5: private
  /cosmiconfig/7.1.0:
    cosmiconfig: private
  /crc-32/1.2.2:
    crc-32: private
  /crc32-stream/4.0.3:
    crc32-stream: private
  /cronstrue/2.51.0:
    cronstrue: private
  /cross-spawn/7.0.5:
    cross-spawn: private
  /crypt/0.0.2:
    crypt: private
  /crypto-js/4.2.0:
    crypto-js: private
  /css-declaration-sorter/6.4.1_postcss@8.4.49:
    css-declaration-sorter: private
  /css-loader/6.11.0_webpack@5.96.1:
    css-loader: private
  /css-minimizer-webpack-plugin/3.4.1_webpack@5.96.1:
    css-minimizer-webpack-plugin: private
  /css-select/4.3.0:
    css-select: private
  /css-tree/1.1.3:
    css-tree: private
  /css-what/6.1.0:
    css-what: private
  /cssesc/3.0.0:
    cssesc: private
  /cssnano-preset-default/5.2.14_postcss@8.4.49:
    cssnano-preset-default: private
  /cssnano-utils/3.1.0_postcss@8.4.49:
    cssnano-utils: private
  /cssnano/5.1.15_postcss@8.4.49:
    cssnano: private
  /csso/4.2.0:
    csso: private
  /csstype/3.1.3:
    csstype: private
  /d3-array/3.2.4:
    d3-array: private
  /d3-axis/3.0.0:
    d3-axis: private
  /d3-brush/3.0.0:
    d3-brush: private
  /d3-chord/3.0.1:
    d3-chord: private
  /d3-collection/1.0.7:
    d3-collection: private
  /d3-color/3.1.0:
    d3-color: private
  /d3-contour/4.0.2:
    d3-contour: private
  /d3-delaunay/6.0.4:
    d3-delaunay: private
  /d3-dispatch/3.0.1:
    d3-dispatch: private
  /d3-drag/3.0.0:
    d3-drag: private
  /d3-dsv/3.0.1:
    d3-dsv: private
  /d3-ease/3.0.1:
    d3-ease: private
  /d3-fetch/3.0.1:
    d3-fetch: private
  /d3-force/3.0.0:
    d3-force: private
  /d3-format/3.1.0:
    d3-format: private
  /d3-geo/3.1.1:
    d3-geo: private
  /d3-hierarchy/3.1.2:
    d3-hierarchy: private
  /d3-interpolate/3.0.1:
    d3-interpolate: private
  /d3-path/3.1.0:
    d3-path: private
  /d3-polygon/3.0.1:
    d3-polygon: private
  /d3-quadtree/3.0.1:
    d3-quadtree: private
  /d3-random/3.0.1:
    d3-random: private
  /d3-scale-chromatic/3.1.0:
    d3-scale-chromatic: private
  /d3-scale/4.0.2:
    d3-scale: private
  /d3-selection/3.0.0:
    d3-selection: private
  /d3-shape/3.2.0:
    d3-shape: private
  /d3-time-format/4.1.0:
    d3-time-format: private
  /d3-time/3.1.0:
    d3-time: private
  /d3-timer/3.0.1:
    d3-timer: private
  /d3-transition/3.0.1_d3-selection@3.0.0:
    d3-transition: private
  /d3-voronoi/1.1.4:
    d3-voronoi: private
  /d3-zoom/3.0.0:
    d3-zoom: private
  /d3/7.9.0:
    d3: private
  /dagre-d3/0.6.4:
    dagre-d3: private
  /dagre/0.8.5:
    dagre: private
  /dayjs/1.11.13:
    dayjs: private
  /debounce/1.2.1:
    debounce: private
  /debug/4.3.7:
    debug: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/1.5.2:
    deepmerge: private
  /default-gateway/6.0.3:
    default-gateway: private
  /defaults/1.0.4:
    defaults: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-lazy-prop/2.0.0:
    define-lazy-prop: private
  /define-properties/1.2.1:
    define-properties: private
  /delaunator/5.0.1:
    delaunator: private
  /delegate/3.2.0:
    delegate: private
  /depd/2.0.0:
    depd: private
  /destroy/1.2.0:
    destroy: private
  /detect-libc/1.0.3:
    detect-libc: private
  /detect-node/2.1.0:
    detect-node: private
  /dir-glob/3.0.1:
    dir-glob: private
  /dns-packet/5.6.1:
    dns-packet: private
  /doctrine/3.0.0:
    doctrine: private
  /docxtemplater-image-module-free/1.1.1:
    docxtemplater-image-module-free: private
  /docxtemplater/3.52.0:
    docxtemplater: private
  /dom-converter/0.2.0:
    dom-converter: private
  /dom-serializer/1.4.1:
    dom-serializer: private
  /dom-walk/0.1.2:
    dom-walk: private
  /domelementtype/2.3.0:
    domelementtype: private
  /domhandler/4.3.1:
    domhandler: private
  /dommatrix/1.0.3:
    dommatrix: private
  /dompurify/2.3.5:
    dompurify: private
  /domutils/2.8.0:
    domutils: private
  /dot-case/3.0.4:
    dot-case: private
  /dotenv-expand/5.1.0:
    dotenv-expand: private
  /dotenv/10.0.0:
    dotenv: private
  /dunder-proto/1.0.0:
    dunder-proto: private
  /duplexer/0.1.2:
    duplexer: private
  /duplexer2/0.1.4:
    duplexer2: private
  /easy-stack/1.0.1:
    easy-stack: private
  /echarts-gl/2.0.9_echarts@5.5.1:
    echarts-gl: private
  /echarts-wordcloud/2.1.0_echarts@5.5.1:
    echarts-wordcloud: private
  /echarts/5.5.1:
    echarts: private
  /ee-first/1.1.1:
    ee-first: private
  /electron-to-chromium/1.5.57:
    electron-to-chromium: private
  /element-plus/2.7.6_vue@3.5.12:
    element-plus: private
  /element-resize-detector/1.2.4:
    element-resize-detector: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /emojis-list/3.0.0:
    emojis-list: private
  /encodeurl/2.0.0:
    encodeurl: private
  /end-of-stream/1.4.4:
    end-of-stream: private
  /engine.io-client/3.5.4:
    engine.io-client: private
  /engine.io-parser/2.2.1:
    engine.io-parser: private
  /enhanced-resolve/5.17.1:
    enhanced-resolve: private
  /enquirer/2.4.1:
    enquirer: private
  /entities/2.1.0:
    entities: private
  /error-ex/1.3.2:
    error-ex: private
  /error-stack-parser/2.1.4:
    error-stack-parser: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-module-lexer/1.5.4:
    es-module-lexer: private
  /es-object-atoms/1.0.0:
    es-object-atoms: private
  /es6-object-assign/1.1.0:
    es6-object-assign: private
  /es6-promise/4.2.8:
    es6-promise: private
  /escalade/3.2.0:
    escalade: private
  /escape-html/1.0.3:
    escape-html: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /eslint-plugin-vue/8.7.1_eslint@7.32.0:
    eslint-plugin-vue: public
  /eslint-scope/5.1.1:
    eslint-scope: public
  /eslint-utils/3.0.0_eslint@7.32.0:
    eslint-utils: public
  /eslint-visitor-keys/2.1.0:
    eslint-visitor-keys: public
  /eslint-webpack-plugin/3.2.0_5le5hsyd7k2ow4j74u4uwamobq:
    eslint-webpack-plugin: public
  /eslint/7.32.0:
    eslint: public
  /espree/7.3.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/4.3.0:
    estraverse: private
  /estree-walker/2.0.2:
    estree-walker: private
  /esutils/2.0.3:
    esutils: private
  /etag/1.8.1:
    etag: private
  /event-pubsub/4.3.0:
    event-pubsub: private
  /eventemitter3/4.0.7:
    eventemitter3: private
  /events/3.3.0:
    events: private
  /exceljs/4.4.0:
    exceljs: private
  /execa/1.0.0:
    execa: private
  /express/4.21.1:
    express: private
  /fast-csv/4.3.6:
    fast-csv: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-glob/3.3.2:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-uri/3.0.3:
    fast-uri: private
  /fast-xml-parser/4.5.0:
    fast-xml-parser: private
  /fastq/1.17.1:
    fastq: private
  /faye-websocket/0.11.4:
    faye-websocket: private
  /figures/2.0.0:
    figures: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /file-saver/2.0.5:
    file-saver: private
  /fill-range/7.1.1:
    fill-range: private
  /finalhandler/1.3.1:
    finalhandler: private
  /find-cache-dir/3.3.2:
    find-cache-dir: private
  /find-up/4.1.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flat/5.0.2:
    flat: private
  /flatted/3.3.1:
    flatted: private
  /flv.js/1.6.2:
    flv.js: private
  /follow-redirects/1.15.9:
    follow-redirects: private
  /for-each/0.3.5:
    for-each: private
  /forwarded/0.2.0:
    forwarded: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fresh/0.5.2:
    fresh: private
  /fs-constants/1.0.0:
    fs-constants: private
  /fs-extra/9.1.0:
    fs-extra: private
  /fs-monkey/1.0.6:
    fs-monkey: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /fstream/1.0.12:
    fstream: private
  /function-bind/1.1.2:
    function-bind: private
  /functional-red-black-tree/1.0.1:
    functional-red-black-tree: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-intrinsic/1.2.6:
    get-intrinsic: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/3.0.0:
    get-stream: private
  /glob-parent/5.1.2:
    glob-parent: private
  /glob-to-regexp/0.4.1:
    glob-to-regexp: private
  /glob/7.2.3:
    glob: private
  /global/4.4.0:
    global: private
  /globals/13.24.0:
    globals: private
  /globby/11.1.0:
    globby: private
  /gm-crypto/0.1.12:
    gm-crypto: private
  /good-listener/1.2.2:
    good-listener: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphlib/2.1.8:
    graphlib: private
  /gzip-size/6.0.0:
    gzip-size: private
  /handle-thing/2.0.1:
    handle-thing: private
  /has-binary2/1.0.3:
    has-binary2: private
  /has-cors/1.1.0:
    has-cors: private
  /has-flag/3.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hash-sum/2.0.0:
    hash-sum: private
  /hasown/2.0.2:
    hasown: private
  /he/1.2.0:
    he: private
  /highlight.js/11.11.1:
    highlight.js: private
  /hls.js/1.6.10:
    hls.js: private
  /hls/0.0.1:
    hls: private
  /hosted-git-info/2.8.9:
    hosted-git-info: private
  /hpack.js/2.1.6:
    hpack.js: private
  /html-entities/2.5.2:
    html-entities: private
  /html-escaper/2.0.2:
    html-escaper: private
  /html-minifier-terser/6.1.0:
    html-minifier-terser: private
  /html-tags/3.3.1:
    html-tags: private
  /html-webpack-plugin/5.6.3_webpack@5.96.1:
    html-webpack-plugin: private
  /htmlparser2/6.1.0:
    htmlparser2: private
  /http-deceiver/1.2.7:
    http-deceiver: private
  /http-errors/2.0.0:
    http-errors: private
  /http-parser-js/0.5.8:
    http-parser-js: private
  /http-proxy-middleware/2.0.7_utguotmo3jjpm2bhksntt72gcu:
    http-proxy-middleware: private
  /http-proxy/1.18.1_debug@4.3.7:
    http-proxy: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.6.3:
    iconv-lite: private
  /icss-utils/5.1.0_postcss@8.4.49:
    icss-utils: private
  /ieee754/1.2.1:
    ieee754: private
  /ignore/4.0.6:
    ignore: private
  /immediate/3.0.6:
    immediate: private
  /immutable/5.0.2:
    immutable: private
  /import-fresh/3.3.0:
    import-fresh: private
  /import-html-entry/1.17.0:
    import-html-entry: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indexof/0.0.1:
    indexof: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /internmap/2.0.3:
    internmap: private
  /inversify/6.0.1:
    inversify: private
  /ipaddr.js/2.2.0:
    ipaddr.js: private
  /is-arguments/1.2.0:
    is-arguments: private
  /is-arrayish/0.2.1:
    is-arrayish: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-buffer/1.1.6:
    is-buffer: private
  /is-callable/1.2.7:
    is-callable: private
  /is-ci/1.2.1:
    is-ci: private
  /is-core-module/2.15.1:
    is-core-module: private
  /is-docker/2.2.1:
    is-docker: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-file-esm/1.0.0:
    is-file-esm: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-function/1.0.2:
    is-function: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-interactive/1.0.0:
    is-interactive: private
  /is-nan/1.3.2:
    is-nan: private
  /is-number/7.0.0:
    is-number: private
  /is-plain-obj/3.0.0:
    is-plain-obj: private
  /is-plain-object/2.0.4:
    is-plain-object: private
  /is-regex/1.2.1:
    is-regex: private
  /is-stream/1.1.0:
    is-stream: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-unicode-supported/0.1.0:
    is-unicode-supported: private
  /is-wsl/2.2.0:
    is-wsl: private
  /isarray/2.0.1:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /isobject/3.0.1:
    isobject: private
  /javascript-obfuscator/4.1.1:
    javascript-obfuscator: private
  /javascript-stringify/2.1.0:
    javascript-stringify: private
  /jest-worker/27.5.1:
    jest-worker: private
  /joi/17.13.3:
    joi: private
  /js-message/1.0.7:
    js-message: private
  /js-sha1/0.6.0:
    js-sha1: private
  /js-string-escape/1.0.1:
    js-string-escape: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/3.14.1:
    js-yaml: private
  /jsbn/1.1.0:
    jsbn: private
  /jsesc/3.0.2:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-parse-better-errors/1.0.2:
    json-parse-better-errors: private
  /json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/2.2.3:
    json5: private
  /jsonfile/6.1.0:
    jsonfile: private
  /jszip-utils/0.1.0:
    jszip-utils: private
  /jszip/3.10.1:
    jszip: private
  /keyv/4.5.4:
    keyv: private
  /khroma/1.4.1:
    khroma: private
  /kind-of/6.0.3:
    kind-of: private
  /klona/2.0.6:
    klona: private
  /launch-editor-middleware/2.9.1:
    launch-editor-middleware: private
  /launch-editor/2.9.1:
    launch-editor: private
  /lazystream/1.0.1:
    lazystream: private
  /levn/0.4.1:
    levn: private
  /libphonenumber-js/1.12.4:
    libphonenumber-js: private
  /lie/3.3.0:
    lie: private
  /lilconfig/2.1.0:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /linkify-it/3.0.3:
    linkify-it: private
  /listenercount/1.0.1:
    listenercount: private
  /loader-runner/4.3.0:
    loader-runner: private
  /loader-utils/1.4.2:
    loader-utils: private
  /local-pkg/0.5.0:
    local-pkg: private
  /locate-path/5.0.0:
    locate-path: private
  /lodash-es/4.17.21:
    lodash-es: private
  /lodash-unified/1.0.3_vpgwo5v3ie2bia5ss74pgoa5ly:
    lodash-unified: private
  /lodash.debounce/4.0.8:
    lodash.debounce: private
  /lodash.defaults/4.2.0:
    lodash.defaults: private
  /lodash.defaultsdeep/4.6.1:
    lodash.defaultsdeep: private
  /lodash.difference/4.5.0:
    lodash.difference: private
  /lodash.escaperegexp/4.1.2:
    lodash.escaperegexp: private
  /lodash.flatten/4.4.0:
    lodash.flatten: private
  /lodash.groupby/4.6.0:
    lodash.groupby: private
  /lodash.isboolean/3.0.3:
    lodash.isboolean: private
  /lodash.isequal/4.5.0:
    lodash.isequal: private
  /lodash.isfunction/3.0.9:
    lodash.isfunction: private
  /lodash.isnil/4.0.0:
    lodash.isnil: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.isundefined/3.0.1:
    lodash.isundefined: private
  /lodash.kebabcase/4.1.1:
    lodash.kebabcase: private
  /lodash.mapvalues/4.6.0:
    lodash.mapvalues: private
  /lodash.memoize/4.1.2:
    lodash.memoize: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.truncate/4.4.2:
    lodash.truncate: private
  /lodash.union/4.6.0:
    lodash.union: private
  /lodash.uniq/4.5.0:
    lodash.uniq: private
  /lodash/4.17.21:
    lodash: private
  /log-symbols/4.1.0:
    log-symbols: private
  /log-update/2.3.0:
    log-update: private
  /lower-case/2.0.2:
    lower-case: private
  /lru-cache/5.1.1:
    lru-cache: private
  /m3u8-parser/7.2.0:
    m3u8-parser: private
  /magic-string/0.30.12:
    magic-string: private
  /make-dir/3.1.0:
    make-dir: private
  /markdown-it-abbr/1.0.4:
    markdown-it-abbr: private
  /markdown-it-container/3.0.0:
    markdown-it-container: private
  /markdown-it-deflist/2.1.0:
    markdown-it-deflist: private
  /markdown-it-emoji/2.0.2:
    markdown-it-emoji: private
  /markdown-it-footnote/3.0.3:
    markdown-it-footnote: private
  /markdown-it-ins/3.0.1:
    markdown-it-ins: private
  /markdown-it-mark/3.0.1:
    markdown-it-mark: private
  /markdown-it-sub/1.0.0:
    markdown-it-sub: private
  /markdown-it-sup/1.0.0:
    markdown-it-sup: private
  /markdown-it-task-lists/2.1.1:
    markdown-it-task-lists: private
  /markdown-it-toc-done-right/4.2.0:
    markdown-it-toc-done-right: private
  /markdown-it-toc/1.1.0:
    markdown-it-toc: private
  /markdown-it/12.3.2:
    markdown-it: private
  /math-intrinsics/1.0.0:
    math-intrinsics: private
  /md5/2.3.0:
    md5: private
  /mdn-data/2.0.14:
    mdn-data: private
  /mdurl/1.0.1:
    mdurl: private
  /media-typer/0.3.0:
    media-typer: private
  /memfs/3.5.3:
    memfs: private
  /memoize-one/6.0.0:
    memoize-one: private
  /merge-descriptors/1.0.3:
    merge-descriptors: private
  /merge-source-map/1.1.0:
    merge-source-map: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /mermaid/8.14.0:
    mermaid: private
  /methods/1.1.2:
    methods: private
  /micromatch/4.0.8:
    micromatch: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/1.6.0:
    mime: private
  /mimic-fn/1.2.0:
    mimic-fn: private
  /min-document/2.19.0:
    min-document: private
  /mini-css-extract-plugin/2.9.2_webpack@5.96.1:
    mini-css-extract-plugin: private
  /minimalistic-assert/1.0.1:
    minimalistic-assert: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/3.3.6:
    minipass: private
  /mkdirp/2.1.3:
    mkdirp: private
  /mlly/1.7.3:
    mlly: private
  /module-alias/2.2.3:
    module-alias: private
  /moment-mini/2.29.4:
    moment-mini: private
  /mpd-parser/1.3.1:
    mpd-parser: private
  /mrmime/2.0.0:
    mrmime: private
  /ms/2.0.0:
    ms: private
  /multi-stage-sourcemap/0.3.1:
    multi-stage-sourcemap: private
  /multicast-dns/7.2.5:
    multicast-dns: private
  /multimatch/5.0.0:
    multimatch: private
  /mux.js/7.1.0:
    mux.js: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.7:
    nanoid: private
  /natural-compare/1.4.0:
    natural-compare: private
  /negotiator/0.6.4:
    negotiator: private
  /neo-async/2.6.2:
    neo-async: private
  /nice-try/1.0.5:
    nice-try: private
  /no-case/3.0.4:
    no-case: private
  /node-addon-api/7.1.1:
    node-addon-api: private
  /node-fetch/2.7.0:
    node-fetch: private
  /node-forge/1.3.1:
    node-forge: private
  /node-releases/2.0.18:
    node-releases: private
  /normalize-package-data/2.5.0:
    normalize-package-data: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /normalize-url/6.1.0:
    normalize-url: private
  /normalize-wheel-es/1.2.0:
    normalize-wheel-es: private
  /npm-run-path/2.0.2:
    npm-run-path: private
  /nth-check/2.1.1:
    nth-check: private
  /object-assign/4.1.1:
    object-assign: private
  /object-inspect/1.13.3:
    object-inspect: private
  /object-is/1.1.6:
    object-is: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.5:
    object.assign: private
  /obuf/1.1.2:
    obuf: private
  /on-finished/2.4.1:
    on-finished: private
  /on-headers/1.0.2:
    on-headers: private
  /once/1.4.0:
    once: private
  /onetime/5.1.2:
    onetime: private
  /open/8.4.2:
    open: private
  /opencollective-postinstall/2.0.3:
    opencollective-postinstall: private
  /opener/1.5.2:
    opener: private
  /optionator/0.9.4:
    optionator: private
  /ora/5.4.1:
    ora: private
  /p-finally/1.0.0:
    p-finally: private
  /p-limit/2.3.0:
    p-limit: private
  /p-locate/4.1.0:
    p-locate: private
  /p-retry/4.6.2:
    p-retry: private
  /p-try/2.2.0:
    p-try: private
  /pako/2.1.0:
    pako: private
  /param-case/3.0.4:
    param-case: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-json/5.2.0:
    parse-json: private
  /parse5-htmlparser2-tree-adapter/6.0.1:
    parse5-htmlparser2-tree-adapter: private
  /parse5/5.1.1:
    parse5: private
  /parseqs/0.0.6:
    parseqs: private
  /parseuri/0.0.6:
    parseuri: private
  /parseurl/1.3.3:
    parseurl: private
  /pascal-case/3.1.2:
    pascal-case: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-to-regexp/0.1.10:
    path-to-regexp: private
  /path-type/4.0.0:
    path-type: private
  /pathe/1.1.2:
    pathe: private
  /pdfjs-dist/2.16.105_worker-loader@3.0.8:
    pdfjs-dist: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/4.0.2:
    picomatch: private
  /pizzip/3.1.7:
    pizzip: private
  /pkcs7/1.0.4:
    pkcs7: private
  /pkg-dir/4.2.0:
    pkg-dir: private
  /pkg-types/1.2.1:
    pkg-types: private
  /portfinder/1.0.32:
    portfinder: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-calc/8.2.4_postcss@8.4.49:
    postcss-calc: private
  /postcss-colormin/5.3.1_postcss@8.4.49:
    postcss-colormin: private
  /postcss-convert-values/5.1.3_postcss@8.4.49:
    postcss-convert-values: private
  /postcss-discard-comments/5.1.2_postcss@8.4.49:
    postcss-discard-comments: private
  /postcss-discard-duplicates/5.1.0_postcss@8.4.49:
    postcss-discard-duplicates: private
  /postcss-discard-empty/5.1.1_postcss@8.4.49:
    postcss-discard-empty: private
  /postcss-discard-overridden/5.1.0_postcss@8.4.49:
    postcss-discard-overridden: private
  /postcss-loader/6.2.1_eiaaeh2lctqom72kn4zujmt3tq:
    postcss-loader: private
  /postcss-merge-longhand/5.1.7_postcss@8.4.49:
    postcss-merge-longhand: private
  /postcss-merge-rules/5.1.4_postcss@8.4.49:
    postcss-merge-rules: private
  /postcss-minify-font-values/5.1.0_postcss@8.4.49:
    postcss-minify-font-values: private
  /postcss-minify-gradients/5.1.1_postcss@8.4.49:
    postcss-minify-gradients: private
  /postcss-minify-params/5.1.4_postcss@8.4.49:
    postcss-minify-params: private
  /postcss-minify-selectors/5.2.1_postcss@8.4.49:
    postcss-minify-selectors: private
  /postcss-modules-extract-imports/3.1.0_postcss@8.4.49:
    postcss-modules-extract-imports: private
  /postcss-modules-local-by-default/4.1.0_postcss@8.4.49:
    postcss-modules-local-by-default: private
  /postcss-modules-scope/3.2.1_postcss@8.4.49:
    postcss-modules-scope: private
  /postcss-modules-values/4.0.0_postcss@8.4.49:
    postcss-modules-values: private
  /postcss-normalize-charset/5.1.0_postcss@8.4.49:
    postcss-normalize-charset: private
  /postcss-normalize-display-values/5.1.0_postcss@8.4.49:
    postcss-normalize-display-values: private
  /postcss-normalize-positions/5.1.1_postcss@8.4.49:
    postcss-normalize-positions: private
  /postcss-normalize-repeat-style/5.1.1_postcss@8.4.49:
    postcss-normalize-repeat-style: private
  /postcss-normalize-string/5.1.0_postcss@8.4.49:
    postcss-normalize-string: private
  /postcss-normalize-timing-functions/5.1.0_postcss@8.4.49:
    postcss-normalize-timing-functions: private
  /postcss-normalize-unicode/5.1.1_postcss@8.4.49:
    postcss-normalize-unicode: private
  /postcss-normalize-url/5.1.0_postcss@8.4.49:
    postcss-normalize-url: private
  /postcss-normalize-whitespace/5.1.1_postcss@8.4.49:
    postcss-normalize-whitespace: private
  /postcss-ordered-values/5.1.3_postcss@8.4.49:
    postcss-ordered-values: private
  /postcss-reduce-initial/5.1.2_postcss@8.4.49:
    postcss-reduce-initial: private
  /postcss-reduce-transforms/5.1.0_postcss@8.4.49:
    postcss-reduce-transforms: private
  /postcss-selector-parser/6.1.2:
    postcss-selector-parser: private
  /postcss-svgo/5.1.0_postcss@8.4.49:
    postcss-svgo: private
  /postcss-unique-selectors/5.1.1_postcss@8.4.49:
    postcss-unique-selectors: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /postcss/8.4.49:
    postcss: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier/2.8.8:
    prettier: public
  /pretty-error/4.0.0:
    pretty-error: private
  /process-nextick-args/2.0.1:
    process-nextick-args: private
  /process/0.11.10:
    process: private
  /progress-webpack-plugin/1.0.16_webpack@5.96.1:
    progress-webpack-plugin: private
  /progress/2.0.3:
    progress: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /pseudomap/1.0.2:
    pseudomap: private
  /pump/3.0.2:
    pump: private
  /punycode/2.3.1:
    punycode: private
  /qiankun/2.10.16:
    qiankun: private
  /qrcode.vue/3.6.0_vue@3.5.12:
    qrcode.vue: private
  /qs/6.13.0:
    qs: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /randombytes/2.1.0:
    randombytes: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/2.5.2:
    raw-body: private
  /read-pkg-up/7.0.1:
    read-pkg-up: private
  /read-pkg/5.2.0:
    read-pkg: private
  /readable-stream/3.6.2:
    readable-stream: private
  /readdir-glob/1.1.3:
    readdir-glob: private
  /readdirp/3.6.0:
    readdirp: private
  /reflect-metadata/0.1.13:
    reflect-metadata: private
  /regenerate-unicode-properties/10.2.0:
    regenerate-unicode-properties: private
  /regenerate/1.4.2:
    regenerate: private
  /regenerator-runtime/0.14.1:
    regenerator-runtime: private
  /regenerator-transform/0.15.2:
    regenerator-transform: private
  /regexpp/3.2.0:
    regexpp: private
  /regexpu-core/6.1.1:
    regexpu-core: private
  /regjsgen/0.8.0:
    regjsgen: private
  /regjsparser/0.11.2:
    regjsparser: private
  /relateurl/0.2.7:
    relateurl: private
  /renderkid/3.0.0:
    renderkid: private
  /require-directory/2.1.1:
    require-directory: private
  /require-from-string/2.0.2:
    require-from-string: private
  /requires-port/1.0.0:
    requires-port: private
  /resolve-from/4.0.0:
    resolve-from: private
  /resolve/1.22.8:
    resolve: private
  /restore-cursor/2.0.0:
    restore-cursor: private
  /retry/0.13.1:
    retry: private
  /reusify/1.0.4:
    reusify: private
  /rimraf/3.0.2:
    rimraf: private
  /robust-predicates/3.0.2:
    robust-predicates: private
  /run-parallel/1.2.0:
    run-parallel: private
  /rw/1.3.3:
    rw: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /sass-loader/12.6.0_sass@1.80.7:
    sass-loader: private
  /sass/1.80.7:
    sass: private
  /saxes/5.0.1:
    saxes: private
  /schema-utils/4.2.0:
    schema-utils: private
  /scule/1.3.0:
    scule: private
  /select-hose/2.0.0:
    select-hose: private
  /select/1.1.2:
    select: private
  /selfsigned/2.4.1:
    selfsigned: private
  /semver/6.3.1:
    semver: private
  /send/0.19.0:
    send: private
  /serialize-javascript/6.0.2:
    serialize-javascript: private
  /serve-index/1.9.1:
    serve-index: private
  /serve-static/1.16.2:
    serve-static: private
  /set-function-length/1.2.2:
    set-function-length: private
  /setimmediate/1.0.5:
    setimmediate: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /shallow-clone/3.0.1:
    shallow-clone: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /shell-quote/1.8.1:
    shell-quote: private
  /side-channel/1.0.6:
    side-channel: private
  /signal-exit/3.0.7:
    signal-exit: private
  /single-spa/5.9.5:
    single-spa: private
  /sirv/2.0.4:
    sirv: private
  /slash/3.0.0:
    slash: private
  /slice-ansi/4.0.0:
    slice-ansi: private
  /socket.io-client/2.5.0:
    socket.io-client: private
  /socket.io-parser/3.3.4:
    socket.io-parser: private
  /sockjs/0.3.24:
    sockjs: private
  /sortablejs/1.15.3:
    sortablejs: private
  /source-list-map/2.0.1:
    source-list-map: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.21:
    source-map-support: private
  /source-map/0.6.1:
    source-map: private
  /spdx-correct/3.2.0:
    spdx-correct: private
  /spdx-exceptions/2.5.0:
    spdx-exceptions: private
  /spdx-expression-parse/3.0.1:
    spdx-expression-parse: private
  /spdx-license-ids/3.0.20:
    spdx-license-ids: private
  /spdy-transport/3.0.0:
    spdy-transport: private
  /spdy/4.0.2:
    spdy: private
  /sprintf-js/1.0.3:
    sprintf-js: private
  /ssri/8.0.1:
    ssri: private
  /stable/0.1.8:
    stable: private
  /stackframe/1.3.4:
    stackframe: private
  /statuses/2.0.1:
    statuses: private
  /string-template/1.0.0:
    string-template: private
  /string-width/4.2.3:
    string-width: private
  /string_decoder/1.3.0:
    string_decoder: private
  /stringz/2.1.0:
    stringz: private
  /strip-ansi/6.0.1:
    strip-ansi: private
  /strip-eof/1.0.0:
    strip-eof: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-indent/2.0.0:
    strip-indent: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /strip-literal/2.1.0:
    strip-literal: private
  /strnum/1.0.5:
    strnum: private
  /stylehacks/5.1.1_postcss@8.4.49:
    stylehacks: private
  /stylis/4.3.6:
    stylis: private
  /supports-color/7.2.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /svg-tags/1.0.0:
    svg-tags: private
  /svgo/2.8.0:
    svgo: private
  /table/6.8.2:
    table: private
  /tapable/2.2.1:
    tapable: private
  /tar-stream/2.2.0:
    tar-stream: private
  /terser-webpack-plugin/5.3.10_webpack@5.96.1:
    terser-webpack-plugin: private
  /terser/5.36.0:
    terser: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /thread-loader/3.0.4_webpack@5.96.1:
    thread-loader: private
  /throttle-debounce/5.0.0:
    throttle-debounce: private
  /thunky/1.1.0:
    thunky: private
  /tiny-emitter/2.1.0:
    tiny-emitter: private
  /tmp/0.2.3:
    tmp: private
  /to-array/0.1.4:
    to-array: private
  /to-arraybuffer/1.0.1:
    to-arraybuffer: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toidentifier/1.0.1:
    toidentifier: private
  /totalist/3.0.1:
    totalist: private
  /tr46/0.0.3:
    tr46: private
  /traverse/0.3.9:
    traverse: private
  /tslib/2.3.0:
    tslib: private
  /type-check/0.4.0:
    type-check: private
  /type-fest/0.20.2:
    type-fest: private
  /type-is/1.6.18:
    type-is: private
  /typescript/5.6.3:
    typescript: private
  /uc.micro/1.0.6:
    uc.micro: private
  /ufo/1.5.4:
    ufo: private
  /undici-types/6.19.8:
    undici-types: private
  /unicode-canonical-property-names-ecmascript/2.0.1:
    unicode-canonical-property-names-ecmascript: private
  /unicode-match-property-ecmascript/2.0.0:
    unicode-match-property-ecmascript: private
  /unicode-match-property-value-ecmascript/2.2.0:
    unicode-match-property-value-ecmascript: private
  /unicode-property-aliases-ecmascript/2.1.0:
    unicode-property-aliases-ecmascript: private
  /unimport/3.13.1:
    unimport: private
  /universalify/2.0.1:
    universalify: private
  /unpipe/1.0.0:
    unpipe: private
  /unplugin-auto-import/0.16.7:
    unplugin-auto-import: private
  /unplugin-element-plus/0.7.2:
    unplugin-element-plus: private
  /unplugin-vue-components/0.25.2_vue@3.5.12:
    unplugin-vue-components: private
  /unplugin/1.15.0:
    unplugin: private
  /unzipper/0.10.14:
    unzipper: private
  /update-browserslist-db/1.1.1_browserslist@4.24.2:
    update-browserslist-db: private
  /uppercamelcase/1.1.0:
    uppercamelcase: private
  /uri-js/4.4.1:
    uri-js: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /util/0.12.5:
    util: private
  /utila/0.4.0:
    utila: private
  /utils-merge/1.0.1:
    utils-merge: private
  /uuid/8.3.2:
    uuid: private
  /v8-compile-cache/2.4.0:
    v8-compile-cache: private
  /validate-npm-package-license/3.0.4:
    validate-npm-package-license: private
  /validator/13.12.0:
    validator: private
  /vary/1.1.2:
    vary: private
  /video-player/0.0.1:
    video-player: private
  /video.js/8.19.1:
    video.js: private
  /videojs-contrib-quality-levels/4.1.0_video.js@8.19.1:
    videojs-contrib-quality-levels: private
  /videojs-font/4.2.0:
    videojs-font: private
  /videojs-vtt.js/0.15.5:
    videojs-vtt.js: private
  /vod-js-sdk-v6/1.7.0:
    vod-js-sdk-v6: private
  /vue-amap/0.5.10:
    vue-amap: private
  /vue-cropper/1.1.4:
    vue-cropper: private
  /vue-demi/0.14.10_vue@3.5.12:
    vue-demi: private
  /vue-eslint-parser/8.3.0_eslint@7.32.0:
    vue-eslint-parser: public
  /vue-hot-reload-api/2.3.4:
    vue-hot-reload-api: private
  /vue-loader/15.11.1:
    vue-loader: private
  /vue-loader/15.11.1_ohclkcoioedvhq4e6lj7eak2ca:
    '@vue/vue-loader-v15': private
  /vue-router/4.4.5_vue@3.5.12:
    vue-router: private
  /vue-seamless-scroll/1.1.23:
    vue-seamless-scroll: private
  /vue-style-loader/4.1.3:
    vue-style-loader: private
  /vue-template-es2015-compiler/1.9.1:
    vue-template-es2015-compiler: private
  /vue-video-player/6.0.0_video.js@8.19.1+vue@3.5.12:
    vue-video-player: private
  /vue/3.5.12:
    vue: private
  /vue3-seamless-scroll/2.0.1:
    vue3-seamless-scroll: private
  /vue3-video-play/1.3.2:
    vue3-video-play: private
  /vuex/4.1.0_vue@3.5.12:
    vuex: private
  /watchpack/2.4.2:
    watchpack: private
  /wbuf/1.7.3:
    wbuf: private
  /wcwidth/1.0.1:
    wcwidth: private
  /web-streams-polyfill/3.3.3:
    web-streams-polyfill: private
  /webidl-conversions/3.0.1:
    webidl-conversions: private
  /webpack-bundle-analyzer/4.10.2:
    webpack-bundle-analyzer: private
  /webpack-chain/6.5.1:
    webpack-chain: private
  /webpack-dev-middleware/5.3.4_webpack@5.96.1:
    webpack-dev-middleware: private
  /webpack-dev-server/4.15.2_debug@4.3.7+webpack@5.96.1:
    webpack-dev-server: private
  /webpack-merge/5.10.0:
    webpack-merge: private
  /webpack-obfuscator/3.5.1:
    webpack-obfuscator: private
  /webpack-sources/2.3.1:
    webpack-sources: private
  /webpack-virtual-modules/0.4.6:
    webpack-virtual-modules: private
  /webpack/5.96.1:
    webpack: private
  /websocket-driver/0.7.4:
    websocket-driver: private
  /websocket-extensions/0.1.4:
    websocket-extensions: private
  /webworkify-webpack/2.1.5:
    webworkify-webpack: private
  /whatwg-fetch/3.6.20:
    whatwg-fetch: private
  /whatwg-url/5.0.0:
    whatwg-url: private
  /which-typed-array/1.1.18:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /wildcard/2.0.1:
    wildcard: private
  /word-wrap/1.2.5:
    word-wrap: private
  /worker-loader/3.0.8:
    worker-loader: private
  /wrap-ansi/7.0.0:
    wrap-ansi: private
  /wrappy/1.0.2:
    wrappy: private
  /ws/7.5.10:
    ws: private
  /xmlchars/2.2.0:
    xmlchars: private
  /xmldom/0.1.31:
    xmldom: private
  /xmlhttprequest-ssl/1.6.3:
    xmlhttprequest-ssl: private
  /y18n/5.0.8:
    y18n: private
  /yallist/3.1.1:
    yallist: private
  /yaml/1.10.2:
    yaml: private
  /yargs-parser/20.2.9:
    yargs-parser: private
  /yargs/16.2.0:
    yargs: private
  /yeast/0.1.2:
    yeast: private
  /yorkie/2.0.0:
    yorkie: private
  /zip-stream/4.1.1:
    zip-stream: private
  /zrender/5.6.0:
    zrender: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@7.9.1
pendingBuilds: []
prunedAt: Mon, 25 Aug 2025 03:18:31 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmmirror.com/
skipped:
  - /@parcel/watcher-android-arm64/2.5.0
  - /@parcel/watcher-darwin-arm64/2.5.0
  - /@parcel/watcher-darwin-x64/2.5.0
  - /@parcel/watcher-freebsd-x64/2.5.0
  - /@parcel/watcher-linux-arm-glibc/2.5.0
  - /@parcel/watcher-linux-arm-musl/2.5.0
  - /@parcel/watcher-linux-arm64-glibc/2.5.0
  - /@parcel/watcher-linux-arm64-musl/2.5.0
  - /@parcel/watcher-linux-x64-glibc/2.5.0
  - /@parcel/watcher-linux-x64-musl/2.5.0
  - /@parcel/watcher-win32-arm64/2.5.0
  - /@parcel/watcher-win32-ia32/2.5.0
  - /fsevents/2.3.3
storeDir: D:\.pnpm-store\v3
virtualStoreDir: D:\zy\xm\pc\5.0\qiankun-product\node_modules\.pnpm
