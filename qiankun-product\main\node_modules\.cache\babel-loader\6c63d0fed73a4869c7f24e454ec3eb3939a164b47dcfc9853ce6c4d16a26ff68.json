{"ast": null, "code": "import { ref } from 'vue';\nimport { AiToolBoxElement } from './AiToolBox.js';\nexport default {\n  __name: 'AiToolBox',\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var toolId = ref('IntelligentErrorCorrection');\n    var navList = ref([{\n      id: '1',\n      title: '工作通用工具',\n      tool: [{\n        id: 'IntelligentErrorCorrection',\n        name: '智能纠错'\n      }, {\n        id: 'OneClickLayout',\n        name: '一件排版'\n      }, {\n        id: 'ContentExtraction',\n        name: '内容提炼'\n      }, {\n        id: 'IntelligentManuscriptMerging',\n        name: '智能合稿'\n      }, {\n        id: 'TextComparison',\n        name: '文本比对'\n      }, {\n        id: 'TextPolishing',\n        name: '文本润色'\n      }, {\n        id: 'TextExpansion',\n        name: '文本扩写'\n      }, {\n        id: 'TextContinuation',\n        name: '文本续写'\n      }, {\n        id: 'TextRewrite',\n        name: '文本重写'\n      }, {\n        id: 'TextRecognition',\n        name: '文本识别'\n      }]\n    }, {\n      id: '2',\n      title: '业务专用工具',\n      tool: [{\n        id: 'ProposalAuxiliaryWriting',\n        name: '提案辅助撰写'\n      }]\n    }]);\n    var handleToolClick = function handleToolClick(tool) {\n      toolId.value = tool.id;\n    };\n    var __returned__ = {\n      toolId,\n      navList,\n      handleToolClick,\n      ref,\n      get AiToolBoxElement() {\n        return AiToolBoxElement;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "AiToolBoxElement", "toolId", "navList", "id", "title", "tool", "name", "handleToolClick", "value"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiToolBox/AiToolBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AiToolBox\">\r\n    <el-scrollbar class=\"AiToolBoxNav\">\r\n      <div class=\"AiToolBoxNavItem\" v-for=\"item in navList\" :key=\"item.id\">\r\n        <div class=\"AiToolBoxNavTitle\">{{ item.title }}</div>\r\n        <div class=\"AiToolBoxNavList\">\r\n          <div\r\n            class=\"AiToolBoxNavToolItem\"\r\n            v-for=\"tool in item.tool\"\r\n            :key=\"tool.id\"\r\n            :class=\"{ 'is-active': toolId == tool.id }\"\r\n            @click=\"handleToolClick(tool)\">\r\n            <div class=\"AiToolBoxNavToolItemIcon\">\r\n              <el-icon><Menu /></el-icon>\r\n            </div>\r\n            <div class=\"AiToolBoxNavToolItemTitle\">{{ tool.name }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"AiToolBoxBody\">\r\n      <keep-alive>\r\n        <component :is=\"AiToolBoxElement[toolId]\" />\r\n      </keep-alive>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { AiToolBoxElement } from './AiToolBox.js'\r\n\r\nconst toolId = ref('IntelligentErrorCorrection')\r\nconst navList = ref([\r\n  {\r\n    id: '1',\r\n    title: '工作通用工具',\r\n    tool: [\r\n      { id: 'IntelligentErrorCorrection', name: '智能纠错' },\r\n      { id: 'OneClickLayout', name: '一件排版' },\r\n      { id: 'ContentExtraction', name: '内容提炼' },\r\n      { id: 'IntelligentManuscriptMerging', name: '智能合稿' },\r\n      { id: 'TextComparison', name: '文本比对' },\r\n      { id: 'TextPolishing', name: '文本润色' },\r\n      { id: 'TextExpansion', name: '文本扩写' },\r\n      { id: 'TextContinuation', name: '文本续写' },\r\n      { id: 'TextRewrite', name: '文本重写' },\r\n      { id: 'TextRecognition', name: '文本识别' }\r\n    ]\r\n  },\r\n  {\r\n    id: '2',\r\n    title: '业务专用工具',\r\n    tool: [{ id: 'ProposalAuxiliaryWriting', name: '提案辅助撰写' }]\r\n  }\r\n])\r\nconst handleToolClick = (tool) => {\r\n  toolId.value = tool.id\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.AiToolBox {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  .AiToolBoxNav {\r\n    width: calc((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four));\r\n    height: 100%;\r\n    background: var(--zy-el-color-primary);\r\n    .AiToolBoxNavItem {\r\n      width: 100%;\r\n      padding: 0 var(--zy-distance-two);\r\n      .AiToolBoxNavTitle {\r\n        width: 100%;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-top: var(--zy-distance-two);\r\n      }\r\n      .AiToolBoxNavList {\r\n        width: 100%;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding-top: var(--zy-distance-four);\r\n        .AiToolBoxNavToolItem {\r\n          width: 112px;\r\n          height: 112px;\r\n          display: flex;\r\n          align-items: center;\r\n          flex-direction: column;\r\n          justify-content: center;\r\n          background: rgba(255, 255, 255, 0.3);\r\n          border-radius: var(--el-border-radius-base);\r\n          border: 1px solid;\r\n          border-image: linear-gradient(131deg, rgba(255, 255, 255, 0.2), rgba(224, 232, 255, 0.1)) 1 1;\r\n          margin-bottom: var(--zy-distance-four);\r\n          cursor: pointer;\r\n          &:hover {\r\n            background: rgba(255, 255, 255, 0.9);\r\n            border: 1px solid rgba(255, 255, 255, 1);\r\n            .AiToolBoxNavToolItemTitle {\r\n              color: var(--zy-el-text-color-primary);\r\n            }\r\n          }\r\n          &.is-active {\r\n            background: rgba(255, 255, 255, 1);\r\n            border: 1px solid rgba(255, 255, 255, 1);\r\n            .AiToolBoxNavToolItemIcon {\r\n              background: var(--zy-el-color-primary);\r\n            }\r\n            .AiToolBoxNavToolItemTitle {\r\n              color: var(--zy-el-text-color-primary);\r\n            }\r\n          }\r\n          .AiToolBoxNavToolItemIcon {\r\n            width: 52px;\r\n            height: 52px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            background: var(--zy-el-color-primary-light-3);\r\n            border-radius: var(--el-border-radius-base);\r\n            margin: var(--zy-font-text-distance-five) 0;\r\n            .zy-el-icon {\r\n              font-size: 38px;\r\n              color: #fff;\r\n            }\r\n          }\r\n          .AiToolBoxNavToolItemTitle {\r\n            width: 100%;\r\n            color: #fff;\r\n            text-align: center;\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n            padding-top: var(--zy-font-text-distance-five);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .AiToolBoxBody {\r\n    width: calc(100% - ((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four)));\r\n    height: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA6BA,SAASA,GAAG,QAAQ,KAAK;AACzB,SAASC,gBAAgB,QAAQ,gBAAgB;;;;;;IAEjD,IAAMC,MAAM,GAAGF,GAAG,CAAC,4BAA4B,CAAC;IAChD,IAAMG,OAAO,GAAGH,GAAG,CAAC,CAClB;MACEI,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,CACJ;QAAEF,EAAE,EAAE,4BAA4B;QAAEG,IAAI,EAAE;MAAO,CAAC,EAClD;QAAEH,EAAE,EAAE,gBAAgB;QAAEG,IAAI,EAAE;MAAO,CAAC,EACtC;QAAEH,EAAE,EAAE,mBAAmB;QAAEG,IAAI,EAAE;MAAO,CAAC,EACzC;QAAEH,EAAE,EAAE,8BAA8B;QAAEG,IAAI,EAAE;MAAO,CAAC,EACpD;QAAEH,EAAE,EAAE,gBAAgB;QAAEG,IAAI,EAAE;MAAO,CAAC,EACtC;QAAEH,EAAE,EAAE,eAAe;QAAEG,IAAI,EAAE;MAAO,CAAC,EACrC;QAAEH,EAAE,EAAE,eAAe;QAAEG,IAAI,EAAE;MAAO,CAAC,EACrC;QAAEH,EAAE,EAAE,kBAAkB;QAAEG,IAAI,EAAE;MAAO,CAAC,EACxC;QAAEH,EAAE,EAAE,aAAa;QAAEG,IAAI,EAAE;MAAO,CAAC,EACnC;QAAEH,EAAE,EAAE,iBAAiB;QAAEG,IAAI,EAAE;MAAO,CAAC;IAE3C,CAAC,EACD;MACEH,EAAE,EAAE,GAAG;MACPC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,CAAC;QAAEF,EAAE,EAAE,0BAA0B;QAAEG,IAAI,EAAE;MAAS,CAAC;IAC3D,CAAC,CACF,CAAC;IACF,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAIF,IAAI,EAAK;MAChCJ,MAAM,CAACO,KAAK,GAAGH,IAAI,CAACF,EAAE;IACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}