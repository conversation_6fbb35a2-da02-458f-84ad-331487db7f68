{"ast": null, "code": "import { useRoute } from 'vue-router';\nimport { elComponent } from './elComponent';\nvar __default__ = {\n  name: 'GlobalHome'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var __returned__ = {\n      route,\n      get useRoute() {\n        return useRoute;\n      },\n      get elComponent() {\n        return elComponent;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["useRoute", "elComponent", "__default__", "name", "route"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalHome/GlobalHome.vue"], "sourcesContent": ["<template>\r\n  <component :is=\"elComponent[route.query.code]\"></component>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalHome' }\r\n</script>\r\n<script setup>\r\nimport { useRoute } from 'vue-router'\r\nimport { elComponent } from './elComponent'\r\nconst route = useRoute() \r\n</script>\r\n"], "mappings": "AAOA,SAASA,QAAQ,QAAQ,YAAY;AACrC,SAASC,WAAW,QAAQ,eAAe;AAJ3C,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAa,CAAC;;;;;IAKrC,IAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}