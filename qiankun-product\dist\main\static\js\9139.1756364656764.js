"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[9139],{59139:function(e,t,n){n.r(t),n.d(t,{default:function(){return T}});var r=n(50859),o=(n(76945),n(99854),n(31167)),a=(n(52669),n(44917)),i=(n(40065),n(74061)),u=n(88609),l=n(59335),c=n(3671),s=n(44500),f=n(67761);function v(e){return y(e)||p(e)||d(e)||h()}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function p(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function y(e){if(Array.isArray(e))return m(e)}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function b(e,t,n){return(t=x(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e){var t=E(e,"string");return"symbol"==typeof t?t:t+""}function E(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function L(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */L=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),u=new P(r||[]);return o(i,"_invoke",{value:j(e,n,u)}),i}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var v="suspendedStart",h="suspendedYield",d="executing",p="completed",y={};function m(){}function g(){}function w(){}var b={};c(b,i,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(D([])));E&&E!==n&&r.call(E,i)&&(b=E);var O=w.prototype=m.prototype=Object.create(b);function k(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function n(o,a,i,u){var l=f(e[o],e,a);if("throw"!==l.type){var c=l.arg,s=c.value;return s&&"object"==typeof s&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,i,u)}),(function(e){n("throw",e,i,u)})):t.resolve(s).then((function(e){c.value=e,i(c)}),(function(e){return n("throw",e,i,u)}))}u(l.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function j(t,n,r){var o=v;return function(a,i){if(o===d)throw Error("Generator is already running");if(o===p){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var u=r.delegate;if(u){var l=S(u,r);if(l){if(l===y)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===v)throw o=p,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=d;var c=f(t,n,r);if("normal"===c.type){if(o=r.done?p:h,c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=p,r.method="throw",r.arg=c.arg)}}}function S(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var a=f(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,y;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function G(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function D(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return g.prototype=w,o(O,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:g,configurable:!0}),g.displayName=c(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,l,"GeneratorFunction")),e.prototype=Object.create(O),e},t.awrap=function(e){return{__await:e}},k(C.prototype),c(C.prototype,u,(function(){return this})),t.AsyncIterator=C,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new C(s(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},k(O),c(O,l,"Generator"),c(O,i,(function(){return this})),c(O,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=D,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(G),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return u.type="throw",u.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),c=r.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),G(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;G(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:D(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function O(e,t,n,r,o,a,i){try{var u=e[a](i),l=u.value}catch(e){return void n(e)}u.done?t(l):Promise.resolve(l).then(r,o)}function k(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){O(a,r,o,i,u,"next",e)}function u(e){O(a,r,o,i,u,"throw",e)}i(void 0)}))}}var C={class:"GlobalChat"},j={class:"GlobalChatBody"},S={class:"GlobalChatBody"},N={class:"GlobalChatBody"},G={name:"GlobalChat"},P=Object.assign(G,{emits:["callback"],setup(e,t){var r=t.emit,o=(0,i.defineAsyncComponent)((function(){return Promise.all([n.e(3955),n.e(8322),n.e(1)]).then(n.bind(n,48322))})),a=(0,i.defineAsyncComponent)((function(){return Promise.all([n.e(3955),n.e(2953)]).then(n.bind(n,42953))})),u=(0,i.defineAsyncComponent)((function(){return Promise.all([n.e(7954),n.e(3955),n.e(6600),n.e(2714)]).then(n.bind(n,46600))})),h=(0,i.defineAsyncComponent)((function(){return Promise.all([n.e(4966),n.e(5203)]).then(n.bind(n,24966))})),d=(0,l.useStore)(),p=r,y=(0,i.computed)((function(){return d.getters.getRongCloudToken})),m=(0,i.ref)("1"),g=(0,i.ref)(""),b=(0,i.ref)([]),x=(0,i.computed)((function(){for(var e=0,t=0;t<b.value.length;t++){var n=b.value[t];1!==n.isNotInform&&(e+=n.count)}return p("callback",e),e})),E=(0,i.ref)(),O=(0,i.ref)(),G=(0,i.ref)(),P=(0,i.ref)(""),D=(0,i.ref)([]),Y=function(){var e=k(L().mark((function e(t){var n;return L().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=s.Events,s.addEventListener(n.CONNECTED,(function(){console.log("链接成功"),M(),A()})),e.next=4,s.connect(t);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),M=function(){var e=k(L().mark((function e(){var t;return L().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=s.Events,s.addEventListener(t.MESSAGES,function(){var e=k(L().mark((function e(t){var n,r,o,a,i,u,l,v,h;return L().wrap((function(e){while(1)switch(e.prev=e.next){case 0:console.log("新消息来了",t.messages),n=[],r=[],o=0;case 4:if(!(o<t.messages.length)){e.next=17;break}if(a=t.messages[o],null!==r&&void 0!==r&&r.includes(a.targetId)){e.next=14;break}return r.push(a.targetId),e.next=10,s.getConversation({conversationType:a.conversationType,targetId:a.targetId});case 10:i=e.sent,u=i.code,l=i.data,(null===l||void 0===l?void 0:l.targetId)===g.value?(null===(v=E.value)||void 0===v||v.getNewestMessages(),u||n.push(w(w({},l),{},{unreadMessageCount:0}))):u||n.push(l);case 14:o++,e.next=4;break;case 17:return h=P.value===(0,c.G)(new Date,"YYYY-MM-DD HH"),e.next=20,(0,f.iO)(n,h,D.value);case 20:return D.value=e.sent,e.next=23,(0,f.J0)(n,[],b.value,D.value);case 23:b.value=e.sent,P.value=(0,c.G)(new Date,"YYYY-MM-DD HH");case 25:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),V=function(e){P.value=e?(0,c.G)(new Date,"YYYY-MM-DD HH"):""},_=function(e){var t,n;"2"===e&&(null===(t=O.value)||void 0===t||t.refresh()),"3"===e&&(null===(n=G.value)||void 0===n||n.refresh())},T=function(e,t){"del"===e&&(b.value=b.value.filter((function(e){return e.id!==t.id}))),A()},A=function(){var e=k(L().mark((function e(){var t,n,r,o,a,i,u,l;return L().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,s.getConversationList();case 2:if(t=e.sent,n=t.code,r=t.data,o=t.msg,0!==n){e.next=20;break}for(console.log("获取会话列表成功",r),a=[],i=0;i<b.value.length;i++)u=b.value[i],u.isTemporary&&a.push(u);return l=P.value===(0,c.G)(new Date,"YYYY-MM-DD HH"),e.next=13,(0,f.iO)(r,l,D.value);case 13:return D.value=e.sent,e.next=16,(0,f.J0)(r,a,[],D.value);case 16:b.value=e.sent,P.value=(0,c.G)(new Date,"YYYY-MM-DD HH"),e.next=21;break;case 20:console.log("获取会话列表失败: ",n,o);case 21:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),H=function(e){var t=b.value.map((function(e){return e.id}));null!==t&&void 0!==t&&t.includes(e.id)?(g.value=e.id,m.value="1"):(g.value=e.id,b.value=[e].concat(v(b.value)),m.value="1")};return(0,i.onUnmounted)((function(){var e=s.Events;s.removeEventListeners(e.MESSAGES),s.removeEventListeners(e.CONNECTED),s.disconnect().then((function(){console.log("成功断开")}))})),(0,i.watch)((function(){return y.value}),(function(){y.value&&Y(y.value)}),{immediate:!0}),function(e,t){return(0,i.openBlock)(),(0,i.createElementBlock)("div",{class:"GlobalChatBox",onContextmenu:t[2]||(t[2]=(0,i.withModifiers)((function(){}),["prevent"]))},[(0,i.createElementVNode)("div",C,[(0,i.createVNode)((0,i.unref)(o),{modelValue:m.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return m.value=e}),chatTotal:x.value,onChange:_},null,8,["modelValue","chatTotal"]),(0,i.withDirectives)((0,i.createElementVNode)("div",j,[(0,i.createVNode)((0,i.unref)(a),{ref_key:"chatViewRef",ref:E,modelValue:g.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return g.value=e}),chatList:b.value,onTime:V,onRefresh:T,onSend:H},null,8,["modelValue","chatList"])],512),[[i.vShow,"1"===m.value]]),(0,i.withDirectives)((0,i.createElementVNode)("div",S,[(0,i.createVNode)((0,i.unref)(u),{ref_key:"addressBookRef",ref:O,onSend:H},null,512)],512),[[i.vShow,"2"===m.value]]),(0,i.withDirectives)((0,i.createElementVNode)("div",N,[(0,i.createVNode)((0,i.unref)(h),{ref_key:"groupRef",ref:G,onSend:H},null,512)],512),[[i.vShow,"3"===m.value]])])],32)}}});const D=P;var Y=D,M={name:"GlobalChatFloating"},V=Object.assign(M,{setup(e){var t=(0,i.ref)(),n=(0,i.ref)(!1),l=(0,i.ref)(window.innerWidth-100),c=(0,i.ref)(168),s=(0,i.ref)(0),f=(0,i.ref)(0),v=(0,i.ref)(!1),h=(0,i.ref)(!1),d=(0,i.ref)([0,0]),p=(0,i.ref)(0),y=(0,i.ref)(!1),m=function(e){d.value=e<10?[0,0]:e<100?[-4,0]:[-8,0],p.value=e},g=function(){n.value||h.value||(v.value=!v.value),h.value=!1},w=function(e){n.value=!0,s.value=e.clientX-l.value,f.value=e.clientY-c.value,document.addEventListener("mousemove",x),document.addEventListener("mouseup",b)},b=function(){n.value&&(n.value=!1,document.removeEventListener("mousemove",x),document.removeEventListener("mouseup",b),E())},x=function(e){if(n.value){v.value=!1,h.value=!0;var r=e.clientX-s.value,o=e.clientY-f.value,a=window.innerWidth,i=window.innerHeight,u=t.value.offsetWidth,d=t.value.offsetHeight;l.value=Math.max(0,Math.min(a-u,r)),c.value=Math.max(0,Math.min(i-d,o)),t.value.style.top=c.value+"px",t.value.style.left=l.value+"px"}},E=function(){var e=window.innerWidth,n=t.value.offsetWidth;l.value+n/2<e/2?(l.value=0,y.value=!0):(y.value=!1,l.value=e-n),t.value.style.top=c.value+"px",t.value.style.left=l.value+"px"};return(0,i.onMounted)((function(){l.value=window.innerWidth-t.value.offsetWidth,t.value.style.top=c.value+"px",t.value.style.left=l.value+"px",window.addEventListener("resize",E)})),(0,i.onUnmounted)((function(){window.removeEventListener("resize",E)})),function(e,n){var l=a.Zq,c=o.z_,s=r.Vc;return(0,i.openBlock)(),(0,i.createElementBlock)("div",{class:"GlobalChatFloating forbidSelect",ref_key:"elContainer",ref:t,onMousedown:w,onMouseup:b},[(0,i.createVNode)(s,{visible:v.value,placement:"left","popper-class":"GlobalChatFloatingPopover"},{reference:(0,i.withCtx)((function(){return[(0,i.createElementVNode)("div",{class:(0,i.normalizeClass)(["GlobalChatFloatingBody",y.value?"is-left ":"is-right",{"is-active":h.value}]),onClick:g},[(0,i.createVNode)(c,{value:p.value,max:99,hidden:!p.value,offset:d.value},{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(l,{src:(0,i.unref)(u.Vj),loading:"lazy",fit:"cover",draggable:"false"},null,8,["src"])]})),_:1},8,["value","hidden","offset"])],2)]})),default:(0,i.withCtx)((function(){return[(0,i.createVNode)(Y,{onCallback:m})]})),_:1},8,["visible"])],544)}}});const _=V;var T=_}}]);