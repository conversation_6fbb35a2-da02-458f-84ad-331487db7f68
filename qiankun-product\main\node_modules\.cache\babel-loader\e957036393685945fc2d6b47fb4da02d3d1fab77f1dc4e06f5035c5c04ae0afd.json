{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"HotSpot\"\n};\nvar _hoisted_2 = {\n  class: \"HotSpotSearch\"\n};\nvar _hoisted_3 = {\n  class: \"HotSpotList\"\n};\nvar _hoisted_4 = [\"onClick\"];\nvar _hoisted_5 = {\n  key: 0,\n  class: \"HotSpotTitle\"\n};\nvar _hoisted_6 = {\n  class: \"HotSpotIndex\"\n};\nvar _hoisted_7 = {\n  class: \"HotSpotHeat\"\n};\nvar _hoisted_8 = [\"innerHTML\"];\nvar _hoisted_9 = {\n  class: \"HotSpotText\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  var _component_el_tabs = _resolveComponent(\"el-tabs\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_input, {\n    modelValue: $setup.keyword,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.keyword = $event;\n    }),\n    placeholder: \"请输入关键词\",\n    clearable: \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n    type: \"primary\",\n    icon: $setup.Search,\n    onClick: $setup.handleSearch\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"搜索\")]);\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"icon\"])]), _createVNode(_component_el_tabs, {\n    modelValue: $setup.activeName,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.activeName = $event;\n    }),\n    onTabChange: $setup.handleClick\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_tab_pane, {\n        label: \"抖音热搜\",\n        name: \"1\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"微博话题\",\n        name: \"9\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"百度热搜\",\n        name: \"7\"\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_scrollbar, {\n    always: \"\",\n    class: \"HotSpotBody\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableData, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"HotSpotItem\",\n          key: index,\n          onClick: function onClick($event) {\n            return $setup.handleRowClick(item);\n          }\n        }, [$setup.type ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, _toDisplayString(index + 1) + \".\", 1 /* TEXT */), _createTextVNode(_toDisplayString(item.heatContent) + \" \", 1 /* TEXT */), _createElementVNode(\"div\", {\n          class: _normalizeClass([\"HotSpotTag\", {\n            'is-first': item.heatTag === '首发',\n            'is-heat': item.heatTag === '热',\n            'is-new': item.heatTag === '新'\n          }])\n        }, _toDisplayString(item.heatTag), 3 /* TEXT, CLASS */), _createElementVNode(\"div\", _hoisted_7, \"🔥 \" + _toDisplayString(item.nowHot), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), !$setup.type ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 1\n        }, [_createElementVNode(\"div\", {\n          class: \"HotSpotName\",\n          innerHTML: item.title || item.summary\n        }, null, 8 /* PROPS */, _hoisted_8), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", null, \"来源：\" + _toDisplayString(item.captureWebsite), 1 /* TEXT */), _createElementVNode(\"span\", null, \"发布时间：\" + _toDisplayString(item.publishTime), 1 /* TEXT */)])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)], 8 /* PROPS */, _hoisted_4);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_input", "modelValue", "$setup", "keyword", "_cache", "$event", "placeholder", "clearable", "_component_el_button", "type", "icon", "Search", "onClick", "handleSearch", "default", "_withCtx", "_createTextVNode", "_", "_component_el_tabs", "activeName", "onTabChange", "handleClick", "_component_el_tab_pane", "label", "name", "_component_el_scrollbar", "always", "_hoisted_3", "_Fragment", "_renderList", "tableData", "item", "index", "handleRowClick", "_hoisted_5", "_hoisted_6", "_toDisplayString", "heatContent", "_normalizeClass", "heatTag", "_hoisted_7", "nowHot", "_createCommentVNode", "innerHTML", "title", "summary", "_hoisted_8", "_hoisted_9", "captureWebsite", "publishTime", "_hoisted_4"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\HotSpot\\HotSpot.vue"], "sourcesContent": ["<template>\r\n  <div class=\"HotSpot\">\r\n    <div class=\"HotSpotSearch\">\r\n      <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" clearable />\r\n      <el-button type=\"primary\" :icon=\"Search\" @click=\"handleSearch\">搜索</el-button>\r\n    </div>\r\n    <el-tabs v-model=\"activeName\" @tab-change=\"handleClick\">\r\n      <el-tab-pane label=\"抖音热搜\" name=\"1\"></el-tab-pane>\r\n      <el-tab-pane label=\"微博话题\" name=\"9\"></el-tab-pane>\r\n      <el-tab-pane label=\"百度热搜\" name=\"7\"></el-tab-pane>\r\n    </el-tabs>\r\n    <el-scrollbar always class=\"HotSpotBody\">\r\n      <div class=\"HotSpotList\">\r\n        <div class=\"HotSpotItem\" v-for=\"(item, index) in tableData\" :key=\"index\" @click=\"handleRowClick(item)\">\r\n          <div class=\"HotSpotTitle\" v-if=\"type\">\r\n            <div class=\"HotSpotIndex\">{{ index + 1 }}.</div>{{ item.heatContent }}\r\n            <div class=\"HotSpotTag\"\r\n              :class=\"{ 'is-first': item.heatTag === '首发', 'is-heat': item.heatTag === '热', 'is-new': item.heatTag === '新' }\">\r\n              {{ item.heatTag }}</div>\r\n            <div class=\"HotSpotHeat\">🔥 {{ item.nowHot }}</div>\r\n          </div>\r\n          <template v-if=\"!type\">\r\n            <div class=\"HotSpotName\" v-html=\"item.title || item.summary\"></div>\r\n            <div class=\"HotSpotText\">\r\n              <span>来源：{{ item.captureWebsite }}</span>\r\n              <span>发布时间：{{ item.publishTime }}</span>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'HotSpot' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { Search } from '@element-plus/icons-vue'\r\n\r\nconst keyword = ref('')\r\nconst activeName = ref('1')\r\nconst tableData = ref([])\r\nconst type = ref(true)\r\nconst handleSearch = () => {\r\n  if (keyword.value) {\r\n    openSmartSearch()\r\n  } else {\r\n    openClueBankHot()\r\n  }\r\n}\r\nconst handleClick = () => {\r\n  if (keyword.value) return\r\n  openClueBankHot()\r\n}\r\n\r\nconst openClueBankHot = async () => {\r\n  const { data } = await api.openClueBankHot({ hotType: activeName.value })\r\n  type.value = true\r\n  tableData.value = data\r\n}\r\nconst openSmartSearch = async () => {\r\n  const { data } = await api.openSmartSearch({\r\n    searchKeyword: keyword.value,\r\n    searchType: '2',\r\n    pageNo: 1,\r\n    pageSize: 999\r\n  })\r\n  type.value = false\r\n  tableData.value = data?.list\r\n}\r\nconst handleRowClick = (row) => {\r\n  window.open(row.url, '_blank')\r\n}\r\nonMounted(() => {\r\n  openClueBankHot()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.HotSpot {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .HotSpotSearch {\r\n    padding: var(--zy-distance-two) 0;\r\n    padding-bottom: var(--zy-distance-five);\r\n\r\n    .zy-el-input {\r\n      width: 360px;\r\n    }\r\n\r\n    .zy-el-button {\r\n      margin-left: var(--zy-distance-two);\r\n    }\r\n  }\r\n\r\n  .zy-el-tabs {\r\n    .zy-el-tabs__header {\r\n      margin: 0;\r\n    }\r\n\r\n    .zy-el-tabs__item {\r\n      height: auto;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: var(--zy-distance-five) 16px;\r\n    }\r\n  }\r\n\r\n  .HotSpotBody {\r\n    height: calc(100% - (var(--zy-height) + var(--zy-distance-two) + (var(--zy-distance-five) * 3) + (var(--zy-name-font-size) * var(--zy-line-height))));\r\n  }\r\n\r\n  .HotSpotList {\r\n    padding: var(--zy-distance-five);\r\n    padding-bottom: var(--zy-distance-two);\r\n  }\r\n\r\n  .HotSpotItem {\r\n    width: 100%;\r\n    padding: var(--zy-distance-five) 0;\r\n    cursor: pointer;\r\n\r\n    &:nth-child(1) {\r\n      .HotSpotIndex {\r\n        color: #D52222;\r\n      }\r\n    }\r\n\r\n    &:nth-child(2) {\r\n      .HotSpotIndex {\r\n        color: #EE4F36;\r\n      }\r\n    }\r\n\r\n    &:nth-child(3) {\r\n      .HotSpotIndex {\r\n        color: #EED536;\r\n      }\r\n    }\r\n\r\n    .HotSpotTitle {\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding-right: 120px;\r\n      position: relative;\r\n\r\n      .HotSpotIndex {\r\n        display: inline-block;\r\n        width: 32px;\r\n        text-align: center;\r\n      }\r\n\r\n      .HotSpotHeat {\r\n        position: absolute;\r\n        top: 50%;\r\n        right: 0;\r\n        width: 120px;\r\n        transform: translateY(-50%);\r\n      }\r\n\r\n      .HotSpotTag {\r\n        display: inline-block;\r\n        color: #fff;\r\n        padding: 0 6px;\r\n        border-radius: 2px;\r\n        font-size: var(--zy-text-font-size);\r\n        margin-left: 6px;\r\n      }\r\n\r\n      .is-first {\r\n        background: linear-gradient(145deg, #a1bef2, #7995c5);\r\n      }\r\n\r\n      .is-heat {\r\n        background: linear-gradient(156deg, #f9cc2d, #f09d14);\r\n      }\r\n\r\n      .is-new {\r\n        background: linear-gradient(322deg, #1abfc9, #3fc3e3);\r\n      }\r\n    }\r\n\r\n    .HotSpotName {\r\n      padding-top: var(--zy-distance-five);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .HotSpotText {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-secondary);\r\n      padding-top: var(--zy-font-name-distance-five);\r\n      padding-bottom: var(--zy-distance-five);\r\n\r\n      span+span {\r\n        margin-left: 40px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAe;;EAUnBA,KAAK,EAAC;AAAa;iBAZ9B;;EAAAC,GAAA;EAceD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAa;iBAnBpC;;EAuBiBA,KAAK,EAAC;AAAa;;;;;;;uBAtBlCE,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAA6DC,mBAAA;IAHnEC,UAAA,EAGyBC,MAAA,CAAAC,OAAO;IAHhC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAGyBH,MAAA,CAAAC,OAAO,GAAAE,MAAA;IAAA;IAAEC,WAAW,EAAC,QAAQ;IAACC,SAAS,EAAT;2CACjDR,YAAA,CAA6ES,oBAAA;IAAlEC,IAAI,EAAC,SAAS;IAAEC,IAAI,EAAER,MAAA,CAAAS,MAAM;IAAGC,OAAK,EAAEV,MAAA,CAAAW;;IAJvDC,OAAA,EAAAC,QAAA,CAIqE;MAAA,OAAEX,MAAA,QAAAA,MAAA,OAJvEY,gBAAA,CAIqE,IAAE,E;;IAJvEC,CAAA;iCAMIlB,YAAA,CAIUmB,kBAAA;IAVdjB,UAAA,EAMsBC,MAAA,CAAAiB,UAAU;IANhC,uBAAAf,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAMsBH,MAAA,CAAAiB,UAAU,GAAAd,MAAA;IAAA;IAAGe,WAAU,EAAElB,MAAA,CAAAmB;;IAN/CP,OAAA,EAAAC,QAAA,CAOM;MAAA,OAAiD,CAAjDhB,YAAA,CAAiDuB,sBAAA;QAApCC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;UAC/BzB,YAAA,CAAiDuB,sBAAA;QAApCC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;UAC/BzB,YAAA,CAAiDuB,sBAAA;QAApCC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;;IATrCP,CAAA;qCAWIlB,YAAA,CAmBe0B,uBAAA;IAnBDC,MAAM,EAAN,EAAM;IAACjC,KAAK,EAAC;;IAX/BqB,OAAA,EAAAC,QAAA,CAYM;MAAA,OAiBM,CAjBNlB,mBAAA,CAiBM,OAjBN8B,UAiBM,I,kBAhBJhC,mBAAA,CAeMiC,SAAA,QA5BdC,WAAA,CAayD3B,MAAA,CAAA4B,SAAS,EAblE,UAayCC,IAAI,EAAEC,KAAK;6BAA5CrC,mBAAA,CAeM;UAfDF,KAAK,EAAC,aAAa;UAAqCC,GAAG,EAAEsC,KAAK;UAAGpB,OAAK,WAALA,OAAKA,CAAAP,MAAA;YAAA,OAAEH,MAAA,CAAA+B,cAAc,CAACF,IAAI;UAAA;YAClE7B,MAAA,CAAAO,IAAI,I,cAApCd,mBAAA,CAMM,OANNuC,UAMM,GALJrC,mBAAA,CAAgD,OAAhDsC,UAAgD,EAAAC,gBAAA,CAAnBJ,KAAK,QAAO,GAAC,iBAftDhB,gBAAA,CAAAoB,gBAAA,CAe+DL,IAAI,CAACM,WAAW,IAAG,GACtE,iBAAAxC,mBAAA,CAE0B;UAFrBJ,KAAK,EAhBtB6C,eAAA,EAgBuB,YAAY;YAAA,YACCP,IAAI,CAACQ,OAAO;YAAA,WAAsBR,IAAI,CAACQ,OAAO;YAAA,UAAoBR,IAAI,CAACQ,OAAO;UAAA;4BACjGR,IAAI,CAACQ,OAAO,yBACjB1C,mBAAA,CAAmD,OAAnD2C,UAAmD,EAA1B,KAAG,GAAAJ,gBAAA,CAAGL,IAAI,CAACU,MAAM,iB,KAnBtDC,mBAAA,gB,CAqB2BxC,MAAA,CAAAO,IAAI,I,cAArBd,mBAAA,CAMWiC,SAAA;UA3BrBlC,GAAA;QAAA,IAsBYG,mBAAA,CAAmE;UAA9DJ,KAAK,EAAC,aAAa;UAACkD,SAAmC,EAA3BZ,IAAI,CAACa,KAAK,IAAIb,IAAI,CAACc;gCAtBhEC,UAAA,GAuBYjD,mBAAA,CAGM,OAHNkD,UAGM,GAFJlD,mBAAA,CAAyC,cAAnC,KAAG,GAAAuC,gBAAA,CAAGL,IAAI,CAACiB,cAAc,kBAC/BnD,mBAAA,CAAwC,cAAlC,OAAK,GAAAuC,gBAAA,CAAGL,IAAI,CAACkB,WAAW,iB,iCAzB5CP,mBAAA,e,iBAAAQ,UAAA;;;IAAAjC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}