{"ast": null, "code": "import api from '@/api';\nimport { ref, computed, onMounted } from 'vue';\nimport { size2Str } from 'common/js/utils.js';\nvar __default__ = {\n  name: 'ChatSendFile'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    chatInfo: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    fileList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var chatInfo = computed(function () {\n      return props.chatInfo;\n    });\n    var fileList = ref([]);\n    // 图片地址拼接组合\n    var imgUrl = function imgUrl(url) {\n      return url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg');\n    };\n    var fileIcon = function fileIcon(fileType) {\n      var IconClass = {\n        docx: 'globalFileWord',\n        doc: 'globalFileWord',\n        wps: 'globalFileWPS',\n        xlsx: 'globalFileExcel',\n        xls: 'globalFileExcel',\n        pdf: 'globalFilePDF',\n        pptx: 'globalFilePPT',\n        ppt: 'globalFilePPT',\n        txt: 'globalFileTXT',\n        jpg: 'globalFilePicture',\n        png: 'globalFilePicture',\n        gif: 'globalFilePicture',\n        avi: 'globalFileVideo',\n        mp4: 'globalFileVideo',\n        zip: 'globalFileCompress',\n        rar: 'globalFileCompress'\n      };\n      return IconClass[fileType] || 'globalFileUnknown';\n    };\n    onMounted(function () {\n      fileList.value = props.fileList.map(function (v) {\n        return v;\n      });\n    });\n    var handleDel = function handleDel(file) {\n      fileList.value = fileList.value.filter(function (v) {\n        return v.id !== file.id;\n      });\n      if (!fileList.value.length) emit('callback');\n    };\n    var handleSubmit = function handleSubmit() {\n      emit('callback', fileList.value);\n    };\n    var handleReset = function handleReset() {\n      emit('callback');\n    };\n    var __returned__ = {\n      props,\n      emit,\n      chatInfo,\n      fileList,\n      imgUrl,\n      fileIcon,\n      handleDel,\n      handleSubmit,\n      handleReset,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      onMounted,\n      get size2Str() {\n        return size2Str;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["api", "ref", "computed", "onMounted", "size2Str", "__default__", "name", "props", "__props", "emit", "__emit", "chatInfo", "fileList", "imgUrl", "url", "fileURL", "defaultImgURL", "fileIcon", "fileType", "IconClass", "docx", "doc", "wps", "xlsx", "xls", "pdf", "pptx", "ppt", "txt", "jpg", "png", "gif", "avi", "mp4", "zip", "rar", "value", "map", "v", "handleDel", "file", "filter", "id", "length", "handleSubmit", "handleReset"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/components/ChatSendFile/ChatSendFile.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ChatSendFile\">\r\n    <div class=\"ChatSendFileObject\">发送给：</div>\r\n    <div class=\"ChatSendFileUser\">\r\n      <el-image :src=\"imgUrl(chatInfo.chatObjectInfo.img)\" fit=\"cover\" draggable=\"false\" />\r\n      <div class=\"ChatSendFileUserName ellipsis\">{{ chatInfo.chatObjectInfo.name }}</div>\r\n    </div>\r\n    <el-scrollbar class=\"ChatSendFileBody\">\r\n      <div class=\"ChatSendFileList\">\r\n        <div class=\"ChatSendFileItem\" v-for=\"item in fileList\" :key=\"item.id\">\r\n          <div class=\"globalFileIcon\" :class=\"fileIcon(item.extName)\"></div>\r\n          <div class=\"ChatSendFileName\">{{ item.name }}</div>\r\n          <div class=\"ChatSendFileSize\">{{ item.size ? size2Str(item.size) : '' }}</div>\r\n          <div class=\"ChatSendFileDel\" @click.stop=\"handleDel(item)\">\r\n            <el-icon>\r\n              <Close />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"ChatSendFileButton\">\r\n      <el-button @click=\"handleReset\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">发送</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChatSendFile' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { size2Str } from 'common/js/utils.js'\r\nconst props = defineProps({\r\n  chatInfo: { type: Object, default: () => ({}) },\r\n  fileList: { type: Array, default: () => ([]) }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst chatInfo = computed(() => props.chatInfo)\r\nconst fileList = ref([])\r\n// 图片地址拼接组合\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\nconst fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nonMounted(() => {\r\n  fileList.value = props.fileList.map(v => v)\r\n})\r\nconst handleDel = (file) => {\r\n  fileList.value = fileList.value.filter(v => v.id !== file.id)\r\n  if (!fileList.value.length) emit('callback')\r\n}\r\nconst handleSubmit = () => { emit('callback', fileList.value) }\r\nconst handleReset = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.ChatSendFile {\r\n  width: 360px;\r\n  height: 100%;\r\n  padding: 20px 0;\r\n\r\n  .ChatSendFileObject {\r\n    width: 100%;\r\n    height: 26px;\r\n    line-height: 26px;\r\n    font-size: 16px;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .ChatSendFileUser {\r\n    width: 100%;\r\n    height: 68px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    position: relative;\r\n    padding: 0 20px 10px 20px;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: calc(100% - 40px);\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      position: absolute;\r\n      left: 20px;\r\n      bottom: 10px;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 38px;\r\n      height: 38px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .ChatSendFileUserName {\r\n      width: calc(100% - 52px);\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .ChatSendFileBody {\r\n    height: calc(100% - 140px);\r\n\r\n    .ChatSendFileList {\r\n      width: 100%;\r\n      padding: 0 20px;\r\n    }\r\n\r\n    .ChatSendFileItem {\r\n      width: 100%;\r\n      padding: 12px 16px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      background: #fff;\r\n      position: relative;\r\n      padding-right: 58px;\r\n      border-radius: var(--el-border-radius-base);\r\n      border: 1px solid var(--zy-el-border-color-light);\r\n      word-wrap: break-word;\r\n      white-space: pre-wrap;\r\n      overflow: hidden;\r\n      cursor: pointer;\r\n\r\n      &+.ChatSendFileItem {\r\n        margin-top: 10px;\r\n      }\r\n\r\n      .ChatSendFileName {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-bottom: var(--zy-font-text-distance-five);\r\n        word-break: break-all;\r\n      }\r\n\r\n      .ChatSendFileSize {\r\n        color: var(--zy-el-text-color-secondary);\r\n        font-size: calc(var(--zy-text-font-size) - 2px);\r\n      }\r\n\r\n      .ChatSendFileDel {\r\n        width: 40px;\r\n        height: 24px;\r\n        position: absolute;\r\n        right: -15px;\r\n        top: -6px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        transform: rotate(45deg);\r\n        background: rgba(0, 0, 0, 0.5);\r\n        color: #fff;\r\n        padding-top: 6px;\r\n        padding-right: 2px;\r\n\r\n        .zy-el-icon {\r\n          font-size: 12px;\r\n          transform: rotate(-45deg);\r\n        }\r\n      }\r\n\r\n      .globalFileIcon {\r\n        width: 40px;\r\n        height: 40px;\r\n        vertical-align: middle;\r\n        position: absolute;\r\n        top: 12px;\r\n        right: 12px;\r\n      }\r\n\r\n      .globalFileUnknown {\r\n        background: url(\"../../img/unknown.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFilePDF {\r\n        background: url(\"../../img/PDF.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileWord {\r\n        background: url(\"../../img/Word.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileExcel {\r\n        background: url(\"../../img/Excel.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFilePicture {\r\n        background: url(\"../../img/picture.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileVideo {\r\n        background: url(\"../../img/video.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileTXT {\r\n        background: url(\"../../img/TXT.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileCompress {\r\n        background: url(\"../../img/compress.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileWPS {\r\n        background: url(\"../../img/WPS.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFilePPT {\r\n        background: url(\"../../img/PPT.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ChatSendFileButton {\r\n    width: 100%;\r\n    height: 46px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: center;\r\n\r\n    .zy-el-button {\r\n      width: 120px;\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA+BA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAK;AAC9C,SAASC,QAAQ,QAAQ,oBAAoB;AAL7C,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAe,CAAC;;;;;;;;;;;;;;;;;;;;;IAMvC,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAAyB;IAEtC,IAAMC,QAAQ,GAAGT,QAAQ,CAAC;MAAA,OAAMK,KAAK,CAACI,QAAQ;IAAA,EAAC;IAC/C,IAAMC,QAAQ,GAAGX,GAAG,CAAC,EAAE,CAAC;IACxB;IACA,IAAMY,MAAM,GAAG,SAATA,MAAMA,CAAGC,GAAG;MAAA,OAAIA,GAAG,GAAGd,GAAG,CAACe,OAAO,CAACD,GAAG,CAAC,GAAGd,GAAG,CAACgB,aAAa,CAAC,uBAAuB,CAAC;IAAA;IACzF,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,QAAQ,EAAK;MAC7B,IAAMC,SAAS,GAAG;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,GAAG,EAAE,gBAAgB;QACrBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,iBAAiB;QACvBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,eAAe;QACrBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,oBAAoB;QACzBC,GAAG,EAAE;MACP,CAAC;MACD,OAAOhB,SAAS,CAACD,QAAQ,CAAC,IAAI,mBAAmB;IACnD,CAAC;IACDf,SAAS,CAAC,YAAM;MACdS,QAAQ,CAACwB,KAAK,GAAG7B,KAAK,CAACK,QAAQ,CAACyB,GAAG,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC;MAAA,EAAC;IAC7C,CAAC,CAAC;IACF,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAIC,IAAI,EAAK;MAC1B5B,QAAQ,CAACwB,KAAK,GAAGxB,QAAQ,CAACwB,KAAK,CAACK,MAAM,CAAC,UAAAH,CAAC;QAAA,OAAIA,CAAC,CAACI,EAAE,KAAKF,IAAI,CAACE,EAAE;MAAA,EAAC;MAC7D,IAAI,CAAC9B,QAAQ,CAACwB,KAAK,CAACO,MAAM,EAAElC,IAAI,CAAC,UAAU,CAAC;IAC9C,CAAC;IACD,IAAMmC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MAAEnC,IAAI,CAAC,UAAU,EAAEG,QAAQ,CAACwB,KAAK,CAAC;IAAC,CAAC;IAC/D,IAAMS,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MAAEpC,IAAI,CAAC,UAAU,CAAC;IAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}