{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createCommentVNode as _createCommentVNode, vShow as _vShow, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"DevelopContent\"\n};\nvar _hoisted_2 = [\"innerHTML\"];\nvar _hoisted_3 = {\n  class: \"DevelopContentLeft\"\n};\nvar _hoisted_4 = {\n  class: \"DevelopContentButton\"\n};\nvar _hoisted_5 = {\n  class: \"DevelopContentRight\"\n};\nvar _hoisted_6 = {\n  key: 0,\n  class: \"DevelopContentTab\"\n};\nvar _hoisted_7 = [\"onClick\"];\nvar _hoisted_8 = {\n  class: \"DevelopContentCorrection\"\n};\nvar _hoisted_9 = {\n  class: \"DevelopContentButton\"\n};\nvar _hoisted_10 = {\n  class: \"globalTable\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_TinyMceEditor = _resolveComponent(\"TinyMceEditor\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n    class: \"DevelopContentWord\",\n    ref: \"wordRef\",\n    innerHTML: $setup.content\n  }, null, 8 /* PROPS */, _hoisted_2), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleCopy\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"复制\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleDownload\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"下载\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_TinyMceEditor, {\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.content = $event;\n    }),\n    setting: {\n      height: '100%'\n    }\n  }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_5, [$setup.whetherAiChat ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList($setup.tabList, function (item) {\n    return _createElementVNode(\"div\", {\n      class: _normalizeClass([\"DevelopContentTabItem\", {\n        'is-active': $setup.tabId === item.id\n      }]),\n      key: item.id,\n      onClick: function onClick($event) {\n        return $setup.handleClick(item);\n      }\n    }, _toDisplayString(item.name), 11 /* TEXT, CLASS, PROPS */, _hoisted_7);\n  }), 64 /* STABLE_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"DevelopContentRightContent\", {\n      'DevelopContentRightContentAiChat': $setup.whetherAiChat\n    }])\n  }, [_withDirectives(_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.handleChat();\n    })\n  }, {\n    default: _withCtx(function () {\n      return _cache[4] || (_cache[4] = [_createTextVNode(\"智能校正\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_table, {\n    data: $setup.checklist\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        label: \"错误类型\",\n        \"min-width\": \"120\",\n        prop: \"type.name\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"错误内容\",\n        \"min-width\": \"120\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_link, {\n            onClick: function onClick($event) {\n              return $setup.handleDetails(scope.row);\n            },\n            disabled: $setup.arrId.includes(scope.row.id),\n            type: \"primary\"\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString(scope.row.word), 1 /* TEXT */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"修改建议\",\n        \"min-width\": \"120\"\n      }, {\n        default: _withCtx(function (scope) {\n          var _scope$row, _scope$row2;\n          return [_createTextVNode(_toDisplayString(((_scope$row = scope.row) === null || _scope$row === void 0 ? void 0 : _scope$row.suggest[0]) || ((_scope$row2 = scope.row) === null || _scope$row2 === void 0 ? void 0 : _scope$row2.explanation)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"100\",\n        fixed: \"right\",\n        \"class-name\": \"globalTableCustom\"\n      }, {\n        default: _withCtx(function (scope) {\n          var _scope$row3;\n          return [_createVNode(_component_el_button, {\n            onClick: function onClick($event) {\n              return $setup.handleReplace(scope.row);\n            },\n            disabled: $setup.arrId.includes(scope.row.id) || !((_scope$row3 = scope.row) !== null && _scope$row3 !== void 0 && (_scope$row3 = _scope$row3.suggest) !== null && _scope$row3 !== void 0 && _scope$row3.length),\n            type: \"primary\",\n            plain: \"\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[5] || (_cache[5] = [_createTextVNode(\"替换\")]);\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\"])])], 512 /* NEED_PATCH */), [[_vShow, $setup.tabId === 'correction']]), _withDirectives(_createVNode($setup[\"GlobalAiChat\"], null, null, 512 /* NEED_PATCH */), [[_vShow, $setup.tabId === 'aiChat']])], 2 /* CLASS */)])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "ref", "innerHTML", "$setup", "content", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_button", "type", "onClick", "handleCopy", "default", "_withCtx", "_cache", "_createTextVNode", "_", "handleDownload", "_component_TinyMceEditor", "modelValue", "$event", "setting", "height", "_hoisted_5", "whetherAiChat", "_hoisted_6", "_Fragment", "_renderList", "tabList", "item", "_normalizeClass", "tabId", "id", "handleClick", "name", "_hoisted_7", "_createCommentVNode", "_hoisted_8", "_hoisted_9", "handleChat", "_hoisted_10", "_component_el_table", "data", "checklist", "_component_el_table_column", "label", "prop", "scope", "_component_el_link", "handleDetails", "row", "disabled", "arrId", "includes", "_toDisplayString", "word", "_scope$row", "_scope$row2", "suggest", "explanation", "width", "fixed", "_scope$row3", "handleReplace", "length", "plain"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\Intelligentize\\DevelopContent\\DevelopContent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DevelopContent\">\r\n    <div class=\"DevelopContentWord\" ref=\"wordRef\" v-html=\"content\"></div>\r\n    <div class=\"DevelopContentLeft\">\r\n      <div class=\"DevelopContentButton\">\r\n        <el-button type=\"primary\" @click=\"handleCopy\">复制</el-button>\r\n        <el-button type=\"primary\" @click=\"handleDownload\">下载</el-button>\r\n      </div>\r\n      <TinyMceEditor v-model=\"content\" :setting=\"{ height: '100%' }\" />\r\n    </div>\r\n    <div class=\"DevelopContentRight\">\r\n      <div class=\"DevelopContentTab\" v-if=\"whetherAiChat\">\r\n        <div class=\"DevelopContentTabItem\" v-for=\"item in tabList\" :key=\"item.id\"\r\n          :class=\"{ 'is-active': tabId === item.id }\" @click=\"handleClick(item)\">{{ item.name }}</div>\r\n      </div>\r\n      <div class=\"DevelopContentRightContent\" :class=\"{ 'DevelopContentRightContentAiChat': whetherAiChat }\">\r\n        <div class=\"DevelopContentCorrection\" v-show=\"tabId === 'correction'\">\r\n          <div class=\"DevelopContentButton\">\r\n            <el-button type=\"primary\" @click=\"handleChat()\">智能校正</el-button>\r\n          </div>\r\n          <div class=\"globalTable\">\r\n            <el-table :data=\"checklist\">\r\n              <el-table-column label=\"错误类型\" min-width=\"120\" prop=\"type.name\" />\r\n              <el-table-column label=\"错误内容\" min-width=\"120\">\r\n                <template #default=\"scope\">\r\n                  <el-link @click=\"handleDetails(scope.row)\" :disabled=\"arrId.includes(scope.row.id)\" type=\"primary\">{{\r\n                    scope.row.word }}</el-link>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"修改建议\" min-width=\"120\">\r\n                <template #default=\"scope\">{{ scope.row?.suggest[0] || scope.row?.explanation }}</template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"100\" fixed=\"right\" class-name=\"globalTableCustom\">\r\n                <template #default=\"scope\">\r\n                  <el-button @click=\"handleReplace(scope.row)\"\r\n                    :disabled=\"arrId.includes(scope.row.id) || !scope.row?.suggest?.length\" type=\"primary\"\r\n                    plain>替换</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </div>\r\n        <GlobalAiChat v-show=\"tabId === 'aiChat'\"></GlobalAiChat>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DevelopContent' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { whetherAiChat } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalAiChat from '../../GlobalAiChat/GlobalAiChat'\r\nconst store = useStore()\r\nconst wordRef = ref()\r\nconst oldId = ref('')\r\nconst arrId = ref([])\r\nconst content = ref('')\r\nconst checklist = ref([])\r\nconst tabId = ref('correction')\r\nconst tabList = [\r\n  { id: 'correction', name: '文本校正' },\r\n  { id: 'aiChat', name: '智能小助手' }\r\n]\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst typingVerification = async () => {\r\n  const { data } = await api.typingVerification({ text: content.value.replace(/&ldquo;/ig, '“').replace(/&rdquo;/ig, '”') })\r\n  checklist.value = data?.checklist?.map(v => ({ ...v, id: guid() })) || []\r\n  content.value = data?.replace_text || ''\r\n}\r\nconst handleChat = () => {\r\n  if (content.value === '') return ElMessage({ type: 'warning', message: '请输入内容' })\r\n  typingVerification()\r\n}\r\nconst handleCopy = () => {\r\n  if (!content.value.replace(/<[^>]*>/g, '')) return ElMessage({ message: '无复制内容', type: 'warning' })\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = content.value.replace(/<[^>]*>/g, '')\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) ElMessage({ message: '复制成功', type: 'success' })\r\n  document.body.removeChild(textarea)\r\n}\r\nconst handleDownload = () => {\r\n  store.commit('setExportWordHtmlObj', { code: 'exportWord', name: '导出内容', key: 'content', data: { content: content.value } })\r\n}\r\nconst elAttr = (elList, id) => {\r\n  let elArr = []\r\n  let styleObj = {}\r\n  for (let index = 0; index < elList.length; index++) {\r\n    const item = elList[index]\r\n    if (item.nodeName !== '#text') {\r\n      if (item.getAttribute('data-umpos') === id) {\r\n        const elParent = item.parentNode\r\n        let is = 0\r\n        while (elParent.style[is]) {\r\n          const elParentStyleKey = elParent.style[is].replace(/-([a-z])/g, (p, m) => m.toUpperCase())\r\n          styleObj[elParent.style[is]] = elParent.style[elParentStyleKey]\r\n          is++\r\n        }\r\n        elArr.push(item)\r\n      }\r\n    }\r\n    if (item.childNodes.length) {\r\n      const obj = elAttr(item.childNodes, id)\r\n      elArr = [...elArr, ...obj.elArr]\r\n      styleObj = { ...styleObj, ...obj.styleObj }\r\n    }\r\n  }\r\n\r\n  return { elArr, styleObj }\r\n}\r\nconst handleDetails = (row) => {\r\n  const elList = wordRef.value.childNodes\r\n  if (oldId.value) {\r\n    const oldObj = elAttr(elList, oldId.value)\r\n    if (oldObj.elArr.length) {\r\n      for (let index = 0; index < oldObj.elArr.length; index++) {\r\n        const item = oldObj.elArr[index]\r\n        item.style.color = ''\r\n        item.style.backgroundColor = ''\r\n      }\r\n    }\r\n  }\r\n  oldId.value = row.position + ''\r\n  const obj = elAttr(elList, row.position + '')\r\n  if (obj.elArr.length) {\r\n    for (let index = 0; index < obj.elArr.length; index++) {\r\n      const item = obj.elArr[index]\r\n      item.style.color = '#fff'\r\n      item.style.backgroundColor = 'red'\r\n    }\r\n  }\r\n  let htmlContent = ''\r\n  for (let index = 0; index < elList.length; index++) {\r\n    htmlContent += elList[index]?.outerHTML || ''\r\n  }\r\n  content.value = htmlContent\r\n}\r\nconst handleReplace = (row) => {\r\n  const elList = wordRef.value.childNodes\r\n  const obj = elAttr(elList, row.position + '')\r\n  if (obj.elArr.length > 1) {\r\n    let styleStr = ''\r\n    for (let key in obj.styleObj) {\r\n      styleStr += `${key}:${obj.styleObj[key]};`\r\n    }\r\n    for (let index = 0; index < obj.elArr.length; index++) {\r\n      const item = obj.elArr[index]\r\n      const elParent = item\r\n      if (!index) {\r\n        elParent.insertAdjacentHTML('beforebegin', `<span style=\"${styleStr}\">${row.suggest[0]}</span>`)\r\n      }\r\n      elParent.parentNode.removeChild(elParent)\r\n    }\r\n  } else {\r\n    obj.elArr[0].insertAdjacentHTML('beforebegin', row.suggest[0])\r\n    obj.elArr[0].parentNode.removeChild(obj.elArr[0])\r\n  }\r\n  arrId.value.push(row.id)\r\n  if (arrId.value.length) {\r\n    let htmlContent = ''\r\n    const elList = wordRef.value.childNodes\r\n    for (let index = 0; index < elList.length; index++) {\r\n      htmlContent += elList[index]?.outerHTML || ''\r\n    }\r\n    content.value = htmlContent\r\n  }\r\n}\r\nconst handleClick = (item) => {\r\n  if (tabId.value === item.id) return\r\n  tabId.value = item.id\r\n  if (item.id === 'aiChat') {\r\n    store.commit('setAiChatCode', 'test_tool_chat')\r\n  }\r\n}\r\nonActivated(() => {\r\n  store.commit('setAiChatElShow', false)\r\n})\r\nonDeactivated(() => {\r\n  store.commit('setAiChatWindow', false)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatSetContent', '')\r\n  store.commit('setAiChatElShow', true)\r\n})\r\nonUnmounted(() => {\r\n  store.commit('setAiChatWindow', false)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatSetContent', '')\r\n  store.commit('setAiChatElShow', true)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.DevelopContent {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  padding: 20px;\r\n\r\n  .DevelopContentWord {\r\n    position: fixed;\r\n    top: -999px;\r\n    left: -999px;\r\n    width: 660px;\r\n    min-height: 842pt;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    margin: 20px auto;\r\n    padding: 99pt 52pt;\r\n\r\n    span {\r\n      span {\r\n        font-family: inherit;\r\n        font-size: inherit;\r\n      }\r\n    }\r\n  }\r\n\r\n  .DevelopContentButton {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    padding-bottom: var(--zy-distance-three);\r\n  }\r\n\r\n  .DevelopContentLeft {\r\n    width: 50%;\r\n    height: 100%;\r\n\r\n    .DevelopContentButton {\r\n      justify-content: flex-end;\r\n    }\r\n\r\n    .TinyMceEditor {\r\n      height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n    }\r\n  }\r\n\r\n  .DevelopContentRight {\r\n    width: 50%;\r\n    height: 100%;\r\n    padding-left: var(--zy-distance-two);\r\n\r\n    .DevelopContentTab {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding-bottom: var(--zy-distance-three);\r\n\r\n      .DevelopContentTabItem {\r\n        width: 50%;\r\n        height: var(--zy-height);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        border-top-left-radius: var(--el-border-radius-base);\r\n        border-bottom-left-radius: var(--el-border-radius-base);\r\n\r\n        &+.DevelopContentTabItem {\r\n          border-top-left-radius: 0;\r\n          border-bottom-left-radius: 0;\r\n          border-top-right-radius: var(--el-border-radius-base);\r\n          border-bottom-right-radius: var(--el-border-radius-base);\r\n        }\r\n\r\n        &.is-active {\r\n          color: #fff;\r\n          background: var(--zy-el-color-primary);\r\n          border: 1px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n    }\r\n\r\n    .DevelopContentRightContent {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      &.DevelopContentRightContentAiChat {\r\n        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n      }\r\n    }\r\n\r\n    .DevelopContentCorrection {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .globalTable {\r\n        width: 100%;\r\n        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n      }\r\n    }\r\n\r\n    .GlobalAiChat {\r\n      padding: 0;\r\n\r\n      .GlobalAiChatClose {\r\n        display: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;iBAD7B;;EAGSA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAsB;;EAM9BA,KAAK,EAAC;AAAqB;;EAVpCC,GAAA;EAWWD,KAAK,EAAC;;iBAXjB;;EAgBaA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAsB;;EAG5BA,KAAK,EAAC;AAAa;;;;;;;uBAnBhCE,mBAAA,CA4CM,OA5CNC,UA4CM,GA3CJC,mBAAA,CAAqE;IAAhEJ,KAAK,EAAC,oBAAoB;IAACK,GAAG,EAAC,SAAS;IAACC,SAAgB,EAARC,MAAA,CAAAC;0BAF1DC,UAAA,GAGIL,mBAAA,CAMM,OANNM,UAMM,GALJN,mBAAA,CAGM,OAHNO,UAGM,GAFJC,YAAA,CAA4DC,oBAAA;IAAjDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAER,MAAA,CAAAS;;IAL1CC,OAAA,EAAAC,QAAA,CAKsD;MAAA,OAAEC,MAAA,QAAAA,MAAA,OALxDC,gBAAA,CAKsD,IAAE,E;;IALxDC,CAAA;MAMQT,YAAA,CAAgEC,oBAAA;IAArDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAER,MAAA,CAAAe;;IAN1CL,OAAA,EAAAC,QAAA,CAM0D;MAAA,OAAEC,MAAA,QAAAA,MAAA,OAN5DC,gBAAA,CAM0D,IAAE,E;;IAN5DC,CAAA;QAQMT,YAAA,CAAiEW,wBAAA;IARvEC,UAAA,EAQ8BjB,MAAA,CAAAC,OAAO;IARrC,uBAAAW,MAAA,QAAAA,MAAA,gBAAAM,MAAA;MAAA,OAQ8BlB,MAAA,CAAAC,OAAO,GAAAiB,MAAA;IAAA;IAAGC,OAAO,EAAE;MAAAC,MAAA;IAAA;6CAE7CvB,mBAAA,CAkCM,OAlCNwB,UAkCM,GAjCiCrB,MAAA,CAAAsB,aAAa,I,cAAlD3B,mBAAA,CAGM,OAHN4B,UAGM,I,cAFJ5B,mBAAA,CAC8F6B,SAAA,QAbtGC,WAAA,CAY0DzB,MAAA,CAAA0B,OAAO,EAZjE,UAYkDC,IAAI;WAA9C9B,mBAAA,CAC8F;MADzFJ,KAAK,EAZlBmC,eAAA,EAYmB,uBAAuB;QAAA,aACT5B,MAAA,CAAA6B,KAAK,KAAKF,IAAI,CAACG;MAAE;MADkBpC,GAAG,EAAEiC,IAAI,CAACG,EAAE;MACzBtB,OAAK,WAALA,OAAKA,CAAAU,MAAA;QAAA,OAAElB,MAAA,CAAA+B,WAAW,CAACJ,IAAI;MAAA;wBAAMA,IAAI,CAACK,IAAI,gCAb7FC,UAAA;sCAAAC,mBAAA,gBAeMrC,mBAAA,CA4BM;IA5BDJ,KAAK,EAfhBmC,eAAA,EAeiB,4BAA4B;MAAA,oCAA+C5B,MAAA,CAAAsB;IAAa;sBACjGzB,mBAAA,CAyBM,OAzBNsC,UAyBM,GAxBJtC,mBAAA,CAEM,OAFNuC,UAEM,GADJ/B,YAAA,CAAgEC,oBAAA;IAArDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAAI,MAAA,QAAAA,MAAA,gBAAAM,MAAA;MAAA,OAAElB,MAAA,CAAAqC,UAAU;IAAA;;IAlBxD3B,OAAA,EAAAC,QAAA,CAkB4D;MAAA,OAAIC,MAAA,QAAAA,MAAA,OAlBhEC,gBAAA,CAkB4D,MAAI,E;;IAlBhEC,CAAA;QAoBUjB,mBAAA,CAoBM,OApBNyC,WAoBM,GAnBJjC,YAAA,CAkBWkC,mBAAA;IAlBAC,IAAI,EAAExC,MAAA,CAAAyC;EAAS;IArBtC/B,OAAA,EAAAC,QAAA,CAsBc;MAAA,OAAiE,CAAjEN,YAAA,CAAiEqC,0BAAA;QAAhDC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAACC,IAAI,EAAC;UACnDvC,YAAA,CAKkBqC,0BAAA;QALDC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3BjC,OAAO,EAAAC,QAAA,CAChB,UAC6BkC,KAFN;UAAA,QACvBxC,YAAA,CAC6ByC,kBAAA;YADnBtC,OAAK,WAALA,OAAKA,CAAAU,MAAA;cAAA,OAAElB,MAAA,CAAA+C,aAAa,CAACF,KAAK,CAACG,GAAG;YAAA;YAAIC,QAAQ,EAAEjD,MAAA,CAAAkD,KAAK,CAACC,QAAQ,CAACN,KAAK,CAACG,GAAG,CAAClB,EAAE;YAAGvB,IAAI,EAAC;;YAzB3GG,OAAA,EAAAC,QAAA,CAyBqH;cAAA,OAChF,CA1BrCE,gBAAA,CAAAuC,gBAAA,CA0BoBP,KAAK,CAACG,GAAG,CAACK,IAAI,iB;;YA1BlCvC,CAAA;;;QAAAA,CAAA;UA6BcT,YAAA,CAEkBqC,0BAAA;QAFDC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3BjC,OAAO,EAAAC,QAAA,CAAS,UAAqDkC,KAAvD;UAAA,IAAAS,UAAA,EAAAC,WAAA;UAAA,QA9BzC1C,gBAAA,CAAAuC,gBAAA,CA8B8C,EAAAE,UAAA,GAAAT,KAAK,CAACG,GAAG,cAAAM,UAAA,uBAATA,UAAA,CAAWE,OAAO,UAAAD,WAAA,GAAOV,KAAK,CAACG,GAAG,cAAAO,WAAA,uBAATA,WAAA,CAAWE,WAAW,kB;;QA9B7F3C,CAAA;UAgCcT,YAAA,CAMkBqC,0BAAA;QANDC,KAAK,EAAC,IAAI;QAACe,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC,OAAO;QAAC,YAAU,EAAC;;QACpDjD,OAAO,EAAAC,QAAA,CAChB,UAEsBkC,KAHC;UAAA,IAAAe,WAAA;UAAA,QACvBvD,YAAA,CAEsBC,oBAAA;YAFVE,OAAK,WAALA,OAAKA,CAAAU,MAAA;cAAA,OAAElB,MAAA,CAAA6D,aAAa,CAAChB,KAAK,CAACG,GAAG;YAAA;YACvCC,QAAQ,EAAEjD,MAAA,CAAAkD,KAAK,CAACC,QAAQ,CAACN,KAAK,CAACG,GAAG,CAAClB,EAAE,QAAA8B,WAAA,GAAMf,KAAK,CAACG,GAAG,cAAAY,WAAA,gBAAAA,WAAA,GAATA,WAAA,CAAWJ,OAAO,cAAAI,WAAA,eAAlBA,WAAA,CAAoBE,MAAM;YAAEvD,IAAI,EAAC,SAAS;YACtFwD,KAAK,EAAL;;YApCpBrD,OAAA,EAAAC,QAAA,CAoC0B;cAAA,OAAEC,MAAA,QAAAA,MAAA,OApC5BC,gBAAA,CAoC0B,IAAE,E;;YApC5BC,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA;mEAgBsDd,MAAA,CAAA6B,KAAK,mB,mBA0BnDxB,YAAA,CAAyDL,MAAA,sD,SAAnCA,MAAA,CAAA6B,KAAK,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}