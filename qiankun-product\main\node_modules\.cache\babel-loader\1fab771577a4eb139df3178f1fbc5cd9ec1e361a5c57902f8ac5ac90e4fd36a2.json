{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalGroupName\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalGroupNameButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_input, {\n    modelValue: $setup.groupName,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.groupName = $event;\n    }),\n    placeholder: \"群聊名称\",\n    clearable: \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSubmit,\n    disabled: !$setup.groupName\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"完成\")]);\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_input", "modelValue", "$setup", "groupName", "_cache", "$event", "placeholder", "clearable", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "handleReset", "default", "_withCtx", "_createTextVNode", "_", "type", "handleSubmit", "disabled"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalGroupName\\GlobalGroupName.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalGroupName\">\r\n    <el-input v-model=\"groupName\" placeholder=\"群聊名称\" clearable />\r\n    <div class=\"GlobalGroupNameButton\">\r\n      <el-button @click=\"handleReset\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\" :disabled=\"(!groupName)\">完成</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalGroupName' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { user } from 'common/js/system_var.js'\r\nconst props = defineProps({ infoId: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\nconst groupInfo = ref({})\r\nconst groupName = ref('')\r\n\r\nconst handleSubmit = async () => {\r\n  const { code } = await api.chatGroupEdit({\r\n    form: { id: props.infoId, groupName: groupName.value },\r\n    ownerUserId: groupInfo.value.ownerUserId, memberUserIds: groupInfo.value.memberUserIds\r\n  })\r\n  if (code === 200) {\r\n    const sendMessageData = {\r\n      name: `${user.value?.userName} 修改群名字为“${groupName.value}”`,\r\n      data: `${user.value?.userName}|OUI|${user.value?.accountId}|| 修改群名字为“${groupName.value}”`,\r\n    }\r\n    emit('callback', true, sendMessageData)\r\n  }\r\n}\r\nconst handleReset = () => { emit('callback', false) }\r\nconst chatGroupInfo = async () => {\r\n  const { data } = await api.chatGroupInfo({ detailId: props.infoId })\r\n  groupInfo.value = data\r\n  groupName.value = data.groupName\r\n}\r\nonMounted(() => {\r\n  chatGroupInfo()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalGroupName {\r\n  width: 420px;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  padding: 0 40px;\r\n\r\n  .GlobalGroupNameButton {\r\n    width: 100%;\r\n    height: 46px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: center;\r\n\r\n    .zy-el-button {\r\n      width: 120px;\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAuB;;;;uBAFpCC,mBAAA,CAMM,OANNC,UAMM,GALJC,YAAA,CAA6DC,mBAAA;IAFjEC,UAAA,EAEuBC,MAAA,CAAAC,SAAS;IAFhC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAEuBH,MAAA,CAAAC,SAAS,GAAAE,MAAA;IAAA;IAAEC,WAAW,EAAC,MAAM;IAACC,SAAS,EAAT;2CACjDC,mBAAA,CAGM,OAHNC,UAGM,GAFJV,YAAA,CAA8CW,oBAAA;IAAlCC,OAAK,EAAET,MAAA,CAAAU;EAAW;IAJpCC,OAAA,EAAAC,QAAA,CAIsC;MAAA,OAAEV,MAAA,QAAAA,MAAA,OAJxCW,gBAAA,CAIsC,IAAE,E;;IAJxCC,CAAA;MAKMjB,YAAA,CAAuFW,oBAAA;IAA5EO,IAAI,EAAC,SAAS;IAAEN,OAAK,EAAET,MAAA,CAAAgB,YAAY;IAAGC,QAAQ,GAAIjB,MAAA,CAAAC;;IALnEU,OAAA,EAAAC,QAAA,CAK+E;MAAA,OAAEV,MAAA,QAAAA,MAAA,OALjFW,gBAAA,CAK+E,IAAE,E;;IALjFC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}