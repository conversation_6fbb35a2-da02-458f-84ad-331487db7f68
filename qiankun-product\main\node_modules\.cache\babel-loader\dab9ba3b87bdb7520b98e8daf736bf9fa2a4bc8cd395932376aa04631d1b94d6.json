{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"WorkBenchColumnName\"\n};\nvar _hoisted_2 = {\n  class: \"WorkBenchList\"\n};\nvar _hoisted_3 = [\"onClick\"];\nvar _hoisted_4 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    always: \"\",\n    class: \"LayoutViewOneWorkBench\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.WorkBench, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"WorkBenchBox\",\n          key: item.id\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"WorkBenchColumn\", {\n            WorkBenchColumnOther: item.id === 'other' && !item.name\n          }])\n        }, [_createElementVNode(\"div\", _hoisted_1, _toDisplayString(item.name), 1 /* TEXT */)], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.data, function (row) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            class: \"WorkBenchItem\",\n            key: row.id,\n            onClick: function onClick($event) {\n              return $setup.handleWorkBench(row);\n            }\n          }, [_createElementVNode(\"div\", {\n            class: \"WorkBenchItemName\",\n            innerHTML: row.name\n          }, null, 8 /* PROPS */, _hoisted_4)], 8 /* PROPS */, _hoisted_3);\n        }), 128 /* KEYED_FRAGMENT */))])]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_scrollbar", "always", "default", "_withCtx", "_createElementBlock", "_Fragment", "_renderList", "$setup", "WorkBench", "item", "key", "id", "_createElementVNode", "_normalizeClass", "WorkBenchColumnOther", "name", "_hoisted_1", "_toDisplayString", "_hoisted_2", "data", "row", "onClick", "$event", "handleWorkBench", "innerHTML", "_hoisted_4", "_hoisted_3", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutViewOne\\component\\LayoutViewOneWorkBench.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"LayoutViewOneWorkBench\">\r\n    <div class=\"WorkBenchBox\" v-for=\"item in WorkBench\" :key=\"item.id\">\r\n      <div class=\"WorkBenchColumn\" :class=\"{ WorkBenchColumnOther: item.id === 'other' && !item.name }\">\r\n        <div class=\"WorkBenchColumnName\">{{ item.name }}</div>\r\n      </div>\r\n      <div class=\"WorkBenchList\">\r\n        <div class=\"WorkBenchItem\" v-for=\"row in item.data\" :key=\"row.id\" @click=\"handleWorkBench(row)\">\r\n          <div class=\"WorkBenchItemName\" v-html=\"row.name\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutViewOneWorkBench' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, inject, computed, watch, onMounted } from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' }, data: { type: Array, default: () => ([]) } })\r\n\r\nconst menuFunction = ref([])\r\nconst WorkBench = ref([])\r\n// const WorkBenchList = inject('WorkBenchList')\r\nconst WorkBenchList = computed(() => props.data)\r\nconst WorkBenchMenu = inject('WorkBenchMenu')\r\n\r\nonMounted(() => { dictionaryData() })\r\n\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['menu_function'], key: props.id })\r\n  var { data } = res\r\n  menuFunction.value = data.menu_function || []\r\n  if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {\r\n    handleWorkBenchData(menuFunction.value.map(v => ({ id: v.key, name: v.name, data: [] })))\r\n  }\r\n}\r\nconst handleWorkBenchData = (arr) => {\r\n  if (!WorkBenchList || !WorkBenchList.value) return\r\n\r\n  var other = ''\r\n  var arrData = arr\r\n  var arrIndex = arr.map(v => v.id)\r\n  for (let i = 0, len = WorkBenchList.value.length; i < len; i++) {\r\n    const item = WorkBenchList.value[i]\r\n    if (item.menuFunction?.value) {\r\n      if (arrIndex.includes(item.menuFunction.value)) {\r\n        arrData.forEach(row => { if (item.menuFunction.value === row.id) { row.data.push(item) } })\r\n      } else {\r\n        arrIndex.push(item.menuFunction.value)\r\n        arrData.push({ id: item.menuFunction.value, name: item.menuFunction.label, data: [item] })\r\n      }\r\n    } else {\r\n      if (other) { other.data.push(item) } else { other = { id: 'other', data: [item] } }\r\n    }\r\n  }\r\n  WorkBench.value = arrData.filter(v => v.data.length)\r\n  if (other) { WorkBench.value.push({ ...other, name: '其他' }) }\r\n  // if (other) { WorkBench.value.push({ ...other, name: WorkBench.value.length ? '其他' : '' }) }\r\n}\r\nconst handleWorkBench = (item) => { WorkBenchMenu(props.id, item) }\r\n\r\nwatch(() => WorkBenchList.value, () => {\r\n  if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {\r\n    handleWorkBenchData(menuFunction.value.map(v => ({ id: v.key, name: v.name, data: [] })))\r\n  }\r\n}, { deep: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutViewOneWorkBench {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .WorkBenchBox {\r\n    width: 100%;\r\n    padding: 20px;\r\n\r\n    &+.WorkBenchBox {\r\n      padding-top: 0;\r\n    }\r\n\r\n    .WorkBenchColumn {\r\n      width: 100%;\r\n      position: relative;\r\n\r\n      .WorkBenchColumnName {\r\n        display: inline-block;\r\n        font-weight: bold;\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-name-font-size);\r\n        color: var(--zy-el-color-primary);\r\n        padding-right: 6px;\r\n        background: #fff;\r\n        position: relative;\r\n        z-index: 2;\r\n      }\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 1px;\r\n        transform: translateY(-50%);\r\n        background: var(--zy-el-border-color);\r\n        z-index: 1;\r\n      }\r\n    }\r\n\r\n    .WorkBenchColumnOther {\r\n\r\n      &::after {\r\n        border-left: 6px solid transparent;\r\n      }\r\n\r\n      &::before {\r\n        border-left: 6px solid transparent;\r\n      }\r\n    }\r\n\r\n    .WorkBenchList {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      padding-top: 6px;\r\n\r\n      .WorkBenchItem {\r\n        width: 33.333%;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        padding: 6px 20px 6px 12px;\r\n        cursor: pointer;\r\n        position: relative;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 0;\r\n          width: 4px;\r\n          height: 4px;\r\n          border-radius: 50%;\r\n          transform: translateY(-50%);\r\n          background: var(--zy-el-color-primary);\r\n        }\r\n\r\n        &:hover {\r\n          .WorkBenchItemName {\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n        }\r\n\r\n        .WorkBenchItemName {\r\n          width: 100%;\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-text-font-size);\r\n          overflow: hidden;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAIaA,KAAK,EAAC;AAAqB;;EAE7BA,KAAK,EAAC;AAAe;iBANhC;iBAAA;;;uBACEC,YAAA,CAWeC,uBAAA;IAXDC,MAAM,EAAN,EAAM;IAACH,KAAK,EAAC;;IAD7BI,OAAA,EAAAC,QAAA,CAE8B;MAAA,OAAyB,E,kBAAnDC,mBAAA,CASMC,SAAA,QAXVC,WAAA,CAE6CC,MAAA,CAAAC,SAAS,EAFtD,UAEqCC,IAAI;6BAArCL,mBAAA,CASM;UATDN,KAAK,EAAC,cAAc;UAA4BY,GAAG,EAAED,IAAI,CAACE;YAC7DC,mBAAA,CAEM;UAFDd,KAAK,EAHhBe,eAAA,EAGiB,iBAAiB;YAAAC,oBAAA,EAAiCL,IAAI,CAACE,EAAE,iBAAiBF,IAAI,CAACM;UAAI;YAC5FH,mBAAA,CAAsD,OAAtDI,UAAsD,EAAAC,gBAAA,CAAlBR,IAAI,CAACM,IAAI,iB,kBAE/CH,mBAAA,CAIM,OAJNM,UAIM,I,kBAHJd,mBAAA,CAEMC,SAAA,QATdC,WAAA,CAOiDG,IAAI,CAACU,IAAI,EAP1D,UAO0CC,GAAG;+BAArChB,mBAAA,CAEM;YAFDN,KAAK,EAAC,eAAe;YAA2BY,GAAG,EAAEU,GAAG,CAACT,EAAE;YAAGU,OAAK,WAALA,OAAKA,CAAAC,MAAA;cAAA,OAAEf,MAAA,CAAAgB,eAAe,CAACH,GAAG;YAAA;cAC3FR,mBAAA,CAAuD;YAAlDd,KAAK,EAAC,mBAAmB;YAAC0B,SAAiB,EAATJ,GAAG,CAACL;kCARrDU,UAAA,E,iBAAAC,UAAA;;;;IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}