{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, normalizeClass as _normalizeClass, resolveDynamicComponent as _resolveDynamicComponent, createBlock as _createBlock, KeepAlive as _KeepAlive } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AiToolBox\"\n};\nvar _hoisted_2 = {\n  class: \"AiToolBoxNavTitle\"\n};\nvar _hoisted_3 = {\n  class: \"AiToolBoxNavList\"\n};\nvar _hoisted_4 = [\"onClick\"];\nvar _hoisted_5 = {\n  class: \"AiToolBoxNavToolItemIcon\"\n};\nvar _hoisted_6 = {\n  class: \"AiToolBoxNavToolItemTitle\"\n};\nvar _hoisted_7 = {\n  class: \"AiToolBoxBody\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_Menu = _resolveComponent(\"Menu\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_scrollbar, {\n    class: \"AiToolBoxNav\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.navList, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"AiToolBoxNavItem\",\n          key: item.id\n        }, [_createElementVNode(\"div\", _hoisted_2, _toDisplayString(item.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.tool, function (tool) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            class: _normalizeClass([\"AiToolBoxNavToolItem\", {\n              'is-active': $setup.toolId == tool.id\n            }]),\n            key: tool.id,\n            onClick: function onClick($event) {\n              return $setup.handleToolClick(tool);\n            }\n          }, [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_Menu)];\n            }),\n            _: 1 /* STABLE */\n          })]), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(tool.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_4);\n        }), 128 /* KEYED_FRAGMENT */))])]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(), _createBlock(_KeepAlive, null, [(_openBlock(), _createBlock(_resolveDynamicComponent($setup.AiToolBoxElement[$setup.toolId])))], 1024 /* DYNAMIC_SLOTS */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_scrollbar", "default", "_withCtx", "_Fragment", "_renderList", "$setup", "navList", "item", "key", "id", "_createElementVNode", "_hoisted_2", "_toDisplayString", "title", "_hoisted_3", "tool", "_normalizeClass", "toolId", "onClick", "$event", "handleToolClick", "_hoisted_5", "_component_el_icon", "_component_Menu", "_", "_hoisted_6", "name", "_hoisted_4", "_hoisted_7", "_createBlock", "_KeepAlive", "_resolveDynamicComponent", "AiToolBoxElement"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBox\\AiToolBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AiToolBox\">\r\n    <el-scrollbar class=\"AiToolBoxNav\">\r\n      <div class=\"AiToolBoxNavItem\" v-for=\"item in navList\" :key=\"item.id\">\r\n        <div class=\"AiToolBoxNavTitle\">{{ item.title }}</div>\r\n        <div class=\"AiToolBoxNavList\">\r\n          <div\r\n            class=\"AiToolBoxNavToolItem\"\r\n            v-for=\"tool in item.tool\"\r\n            :key=\"tool.id\"\r\n            :class=\"{ 'is-active': toolId == tool.id }\"\r\n            @click=\"handleToolClick(tool)\">\r\n            <div class=\"AiToolBoxNavToolItemIcon\">\r\n              <el-icon><Menu /></el-icon>\r\n            </div>\r\n            <div class=\"AiToolBoxNavToolItemTitle\">{{ tool.name }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"AiToolBoxBody\">\r\n      <keep-alive>\r\n        <component :is=\"AiToolBoxElement[toolId]\" />\r\n      </keep-alive>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { AiToolBoxElement } from './AiToolBox.js'\r\n\r\nconst toolId = ref('IntelligentErrorCorrection')\r\nconst navList = ref([\r\n  {\r\n    id: '1',\r\n    title: '工作通用工具',\r\n    tool: [\r\n      { id: 'IntelligentErrorCorrection', name: '智能纠错' },\r\n      { id: 'OneClickLayout', name: '一件排版' },\r\n      { id: 'ContentExtraction', name: '内容提炼' },\r\n      { id: 'IntelligentManuscriptMerging', name: '智能合稿' },\r\n      { id: 'TextComparison', name: '文本比对' },\r\n      { id: 'TextPolishing', name: '文本润色' },\r\n      { id: 'TextExpansion', name: '文本扩写' },\r\n      { id: 'TextContinuation', name: '文本续写' },\r\n      { id: 'TextRewrite', name: '文本重写' },\r\n      { id: 'TextRecognition', name: '文本识别' }\r\n    ]\r\n  },\r\n  {\r\n    id: '2',\r\n    title: '业务专用工具',\r\n    tool: [{ id: 'ProposalAuxiliaryWriting', name: '提案辅助撰写' }]\r\n  }\r\n])\r\nconst handleToolClick = (tool) => {\r\n  toolId.value = tool.id\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.AiToolBox {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  .AiToolBoxNav {\r\n    width: calc((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four));\r\n    height: 100%;\r\n    background: var(--zy-el-color-primary);\r\n    .AiToolBoxNavItem {\r\n      width: 100%;\r\n      padding: 0 var(--zy-distance-two);\r\n      .AiToolBoxNavTitle {\r\n        width: 100%;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-top: var(--zy-distance-two);\r\n      }\r\n      .AiToolBoxNavList {\r\n        width: 100%;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding-top: var(--zy-distance-four);\r\n        .AiToolBoxNavToolItem {\r\n          width: 112px;\r\n          height: 112px;\r\n          display: flex;\r\n          align-items: center;\r\n          flex-direction: column;\r\n          justify-content: center;\r\n          background: rgba(255, 255, 255, 0.3);\r\n          border-radius: var(--el-border-radius-base);\r\n          border: 1px solid;\r\n          border-image: linear-gradient(131deg, rgba(255, 255, 255, 0.2), rgba(224, 232, 255, 0.1)) 1 1;\r\n          margin-bottom: var(--zy-distance-four);\r\n          cursor: pointer;\r\n          &:hover {\r\n            background: rgba(255, 255, 255, 0.9);\r\n            border: 1px solid rgba(255, 255, 255, 1);\r\n            .AiToolBoxNavToolItemTitle {\r\n              color: var(--zy-el-text-color-primary);\r\n            }\r\n          }\r\n          &.is-active {\r\n            background: rgba(255, 255, 255, 1);\r\n            border: 1px solid rgba(255, 255, 255, 1);\r\n            .AiToolBoxNavToolItemIcon {\r\n              background: var(--zy-el-color-primary);\r\n            }\r\n            .AiToolBoxNavToolItemTitle {\r\n              color: var(--zy-el-text-color-primary);\r\n            }\r\n          }\r\n          .AiToolBoxNavToolItemIcon {\r\n            width: 52px;\r\n            height: 52px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            background: var(--zy-el-color-primary-light-3);\r\n            border-radius: var(--el-border-radius-base);\r\n            margin: var(--zy-font-text-distance-five) 0;\r\n            .zy-el-icon {\r\n              font-size: 38px;\r\n              color: #fff;\r\n            }\r\n          }\r\n          .AiToolBoxNavToolItemTitle {\r\n            width: 100%;\r\n            color: #fff;\r\n            text-align: center;\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n            padding-top: var(--zy-font-text-distance-five);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .AiToolBoxBody {\r\n    width: calc(100% - ((112px * 2) + (var(--zy-distance-two) * 2) + var(--zy-distance-four)));\r\n    height: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EAGXA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAkB;iBALrC;;EAYiBA,KAAK,EAAC;AAA0B;;EAGhCA,KAAK,EAAC;AAA2B;;EAKzCA,KAAK,EAAC;AAAe;;;;;uBAnB5BC,mBAAA,CAwBM,OAxBNC,UAwBM,GAvBJC,YAAA,CAiBeC,uBAAA;IAjBDJ,KAAK,EAAC;EAAc;IAFtCK,OAAA,EAAAC,QAAA,CAGoC;MAAA,OAAuB,E,kBAArDL,mBAAA,CAeMM,SAAA,QAlBZC,WAAA,CAGmDC,MAAA,CAAAC,OAAO,EAH1D,UAG2CC,IAAI;6BAAzCV,mBAAA,CAeM;UAfDD,KAAK,EAAC,kBAAkB;UAA0BY,GAAG,EAAED,IAAI,CAACE;YAC/DC,mBAAA,CAAqD,OAArDC,UAAqD,EAAAC,gBAAA,CAAnBL,IAAI,CAACM,KAAK,kBAC5CH,mBAAA,CAYM,OAZNI,UAYM,I,kBAXJjB,mBAAA,CAUMM,SAAA,QAhBhBC,WAAA,CAQ2BG,IAAI,CAACQ,IAAI,EARpC,UAQmBA,IAAI;+BAFblB,mBAAA,CAUM;YATJD,KAAK,EAPjBoB,eAAA,EAOkB,sBAAsB;cAAA,aAGLX,MAAA,CAAAY,MAAM,IAAIF,IAAI,CAACN;YAAE;YADvCD,GAAG,EAAEO,IAAI,CAACN,EAAE;YAEZS,OAAK,WAALA,OAAKA,CAAAC,MAAA;cAAA,OAAEd,MAAA,CAAAe,eAAe,CAACL,IAAI;YAAA;cAC5BL,mBAAA,CAEM,OAFNW,UAEM,GADJtB,YAAA,CAA2BuB,kBAAA;YAbzCrB,OAAA,EAAAC,QAAA,CAauB;cAAA,OAAQ,CAARH,YAAA,CAAQwB,eAAA,E;;YAb/BC,CAAA;gBAeYd,mBAAA,CAA4D,OAA5De,UAA4D,EAAAb,gBAAA,CAAlBG,IAAI,CAACW,IAAI,iB,yBAf/DC,UAAA;;;;IAAAH,CAAA;MAoBId,mBAAA,CAIM,OAJNkB,UAIM,I,cAHJC,YAAA,CAEaC,UAAA,U,cADXD,YAAA,CAA4CE,wBAtBpD,CAsBwB1B,MAAA,CAAA2B,gBAAgB,CAAC3B,MAAA,CAAAY,MAAM,K", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}