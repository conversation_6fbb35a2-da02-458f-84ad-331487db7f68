{"ast": null, "code": "import { createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"BackgroundCheck\"\n};\nvar _hoisted_2 = {\n  class: \"BackgroundCheckTop\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"BackgroundCheckName\"\n  }, \"司法背景调查平台\", -1 /* HOISTED */)), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n    class: \"BackgroundCheckText\"\n  }, \"实时背景调查\", -1 /* HOISTED */)), _createVNode($setup[\"BackgroundCheckHistory\"], {\n    onCallback: $setup.callback\n  })]), _createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    class: \"BackgroundCheckForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        prop: \"name\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.name,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.name = $event;\n            }),\n            placeholder: \"输入姓名\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" <el-form-item prop=\\\"mobile\\\">\\r\\n        <el-input v-model=\\\"form.mobile\\\" placeholder=\\\"输入手机号\\\" clearable />\\r\\n      </el-form-item> \"), _createVNode(_component_el_form_item, {\n        prop: \"idcard\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.idcard,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.idcard = $event;\n            }),\n            placeholder: \"输入身份证号\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        type: \"primary\",\n        icon: $setup.Search,\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\"查询\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"icon\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "$setup", "onCallback", "callback", "_component_el_form", "ref", "model", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "prop", "_component_el_input", "modelValue", "name", "_cache", "$event", "placeholder", "clearable", "_", "_createCommentVNode", "idcard", "_component_el_button", "type", "icon", "Search", "onClick", "submitForm", "formRef", "_createTextVNode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\BackgroundCheck\\BackgroundCheck.vue"], "sourcesContent": ["<template>\r\n  <div class=\"BackgroundCheck\">\r\n    <div class=\"BackgroundCheckTop\">\r\n      <div class=\"BackgroundCheckName\">司法背景调查平台</div>\r\n      <div class=\"BackgroundCheckText\">实时背景调查</div>\r\n      <BackgroundCheckHistory @callback=\"callback\"></BackgroundCheckHistory>\r\n    </div>\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline class=\"BackgroundCheckForm\">\r\n      <el-form-item prop=\"name\">\r\n        <el-input v-model=\"form.name\" placeholder=\"输入姓名\" clearable />\r\n      </el-form-item>\r\n      <!-- <el-form-item prop=\"mobile\">\r\n        <el-input v-model=\"form.mobile\" placeholder=\"输入手机号\" clearable />\r\n      </el-form-item> -->\r\n      <el-form-item prop=\"idcard\">\r\n        <el-input v-model=\"form.idcard\" placeholder=\"输入身份证号\" clearable />\r\n      </el-form-item>\r\n      <el-button type=\"primary\" :icon=\"Search\" @click=\"submitForm(formRef)\">查询</el-button>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'BackgroundCheck' }\r\n</script>\r\n<script setup>\r\n// import api from '@/api'\r\nimport { reactive, ref } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { Search } from '@element-plus/icons-vue'\r\nimport BackgroundCheckHistory from './components/BackgroundCheckHistory'\r\nconst store = useStore()\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  name: '',\r\n  // mobile: '',\r\n  idcard: ''\r\n})\r\nconst rules = reactive({\r\n  name: [{ required: true, message: '请输入姓名', trigger: ['blur', 'change'] }],\r\n  // mobile: [{ required: true, message: '请输入手机号', trigger: ['blur', 'change'] }],\r\n  idcard: [{ required: true, message: '请输入身份证号', trigger: ['blur', 'change'] }]\r\n})\r\nconst callback = (item) => {\r\n  store.commit('setOpenRoute', { name: '司法背景调查详情', path: '/BackgroundCheckDetails', query: { name: item.userName, idcard: item.idCard, createDate: item.createDate } })\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      store.commit('setOpenRoute', { name: '司法背景调查详情', path: '/BackgroundCheckDetails', query: { name: form.name, idcard: form.idcard } })\r\n    } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\n// const faceDetect = async () => {\r\n//   const { data } = await api.faceDetect({ authorize: 1, name: form.name, idcard: form.idcard })\r\n//   store.commit('setOpenRoute', { name: '司法背景调查详情', path: '/BackgroundCheckDetails', query: { ...data, idcard: form.idcard } })\r\n// }\r\n</script>\r\n<style lang=\"scss\">\r\n.BackgroundCheck {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n  position: relative;\r\n\r\n  .BackgroundCheckTop {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 40%;\r\n    background-color: var(--zy-el-color-primary);\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    align-content: center;\r\n\r\n    .BackgroundCheckName {\r\n      width: 100%;\r\n      color: #FFFFFF;\r\n      text-align: center;\r\n      font-size: 32px;\r\n      padding-bottom: var(--zy-distance-four);\r\n    }\r\n\r\n    .BackgroundCheckText {\r\n      width: 100%;\r\n      color: #FFFFFF;\r\n      text-align: center;\r\n      font-size: 22px;\r\n      padding-bottom: var(--zy-distance-four);\r\n    }\r\n  }\r\n\r\n  .BackgroundCheckForm {\r\n    position: absolute;\r\n    top: 40%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 2;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: var(--zy-distance-one);\r\n    background: #FFFFFF;\r\n    box-shadow: 0px 2px 20px 1px rgba(0, 0, 0, 0.08);\r\n    border-radius: var(--el-border-radius-base);\r\n\r\n    .zy-el-form-item {\r\n      width: 260px;\r\n      margin: 0 !important;\r\n      margin-right: var(--zy-distance-two) !important;\r\n\r\n      &:nth-child(1) {\r\n        width: 180px;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        width: 220px;\r\n      }\r\n\r\n      .zy-el-form-item__content {\r\n        &>.zy-el-input {\r\n          width: 290px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAoB;;;;;;uBADjCC,mBAAA,CAkBM,OAlBNC,UAkBM,GAjBJC,mBAAA,CAIM,OAJNC,UAIM,G,0BAHJD,mBAAA,CAA+C;IAA1CH,KAAK,EAAC;EAAqB,GAAC,UAAQ,sB,0BACzCG,mBAAA,CAA6C;IAAxCH,KAAK,EAAC;EAAqB,GAAC,QAAM,sBACvCK,YAAA,CAAsEC,MAAA;IAA7CC,UAAQ,EAAED,MAAA,CAAAE;EAAQ,G,GAE7CH,YAAA,CAWUI,kBAAA;IAXDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEL,MAAA,CAAAM,IAAI;IAAGC,KAAK,EAAEP,MAAA,CAAAO,KAAK;IAAEC,MAAM,EAAN,EAAM;IAACd,KAAK,EAAC;;IAPrEe,OAAA,EAAAC,QAAA,CAQM;MAAA,OAEe,CAFfX,YAAA,CAEeY,uBAAA;QAFDC,IAAI,EAAC;MAAM;QAR/BH,OAAA,EAAAC,QAAA,CASQ;UAAA,OAA6D,CAA7DX,YAAA,CAA6Dc,mBAAA;YATrEC,UAAA,EAS2Bd,MAAA,CAAAM,IAAI,CAACS,IAAI;YATpC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAS2BjB,MAAA,CAAAM,IAAI,CAACS,IAAI,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,MAAM;YAACC,SAAS,EAAT;;;QATzDC,CAAA;UAWMC,mBAAA,6IAEmB,EACnBtB,YAAA,CAEeY,uBAAA;QAFDC,IAAI,EAAC;MAAQ;QAdjCH,OAAA,EAAAC,QAAA,CAeQ;UAAA,OAAiE,CAAjEX,YAAA,CAAiEc,mBAAA;YAfzEC,UAAA,EAe2Bd,MAAA,CAAAM,IAAI,CAACgB,MAAM;YAftC,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAe2BjB,MAAA,CAAAM,IAAI,CAACgB,MAAM,GAAAL,MAAA;YAAA;YAAEC,WAAW,EAAC,QAAQ;YAACC,SAAS,EAAT;;;QAf7DC,CAAA;UAiBMrB,YAAA,CAAoFwB,oBAAA;QAAzEC,IAAI,EAAC,SAAS;QAAEC,IAAI,EAAEzB,MAAA,CAAA0B,MAAM;QAAGC,OAAK,EAAAX,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEjB,MAAA,CAAA4B,UAAU,CAAC5B,MAAA,CAAA6B,OAAO;QAAA;;QAjBzEpB,OAAA,EAAAC,QAAA,CAiB4E;UAAA,OAAEM,MAAA,QAAAA,MAAA,OAjB9Ec,gBAAA,CAiB4E,IAAE,E;;QAjB9EV,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}