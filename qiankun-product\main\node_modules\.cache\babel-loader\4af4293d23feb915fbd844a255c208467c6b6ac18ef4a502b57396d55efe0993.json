{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalGroupAnnouncement\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"GlobalGroupAnnouncementForm\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalGroupAnnouncementButton\"\n};\nvar _hoisted_4 = {\n  key: 1,\n  class: \"GlobalGroupAnnouncementBody\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalChatGroupAnnouncementTitle\"\n};\nvar _hoisted_6 = [\"innerHTML\"];\nvar _hoisted_7 = {\n  class: \"GlobalGroupAnnouncementText\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_CircleCloseFilled = _resolveComponent(\"CircleCloseFilled\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [$setup.isOwner ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_input, {\n    modelValue: $setup.callBoard,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.callBoard = $event;\n    }),\n    type: \"textarea\",\n    rows: 6,\n    resize: \"none\",\n    placeholder: \"群公告\",\n    clearable: \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSubmit\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"完成\")]);\n    }),\n    _: 1 /* STABLE */\n  })])])) : _createCommentVNode(\"v-if\", true), !$setup.isOwner ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    innerHTML: $setup.announcementIcon\n  }, null, 8 /* PROPS */, _hoisted_6), _cache[3] || (_cache[3] = _createTextVNode(\" 群公告\"))]), _createVNode(_component_el_icon, {\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_CircleCloseFilled)];\n    }),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.callBoard), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "$setup", "isOwner", "_hoisted_2", "_createVNode", "_component_el_input", "modelValue", "callBoard", "_cache", "$event", "type", "rows", "resize", "placeholder", "clearable", "_createElementVNode", "_hoisted_3", "_component_el_button", "onClick", "handleReset", "default", "_withCtx", "_createTextVNode", "_", "handleSubmit", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "innerHTML", "announcementIcon", "_hoisted_6", "_component_el_icon", "_component_CircleCloseFilled", "_hoisted_7", "_toDisplayString"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalGroupAnnouncement\\GlobalGroupAnnouncement.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalGroupAnnouncement\">\r\n    <div class=\"GlobalGroupAnnouncementForm\" v-if=\"isOwner\">\r\n      <el-input v-model=\"callBoard\" type=\"textarea\" :rows=\"6\" resize=\"none\" placeholder=\"群公告\" clearable />\r\n      <div class=\"GlobalGroupAnnouncementButton\">\r\n        <el-button @click=\"handleReset\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleSubmit\">完成</el-button>\r\n      </div>\r\n    </div>\r\n    <div class=\"GlobalGroupAnnouncementBody\" v-if=\"!isOwner\">\r\n      <div class=\"GlobalChatGroupAnnouncementTitle\">\r\n        <div><span v-html=\"announcementIcon\"></span> 群公告</div>\r\n        <el-icon @click=\"handleReset\">\r\n          <CircleCloseFilled />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"GlobalGroupAnnouncementText\">{{ callBoard }}</div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalGroupAnnouncement' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { announcementIcon } from '../../js/icon.js'\r\nconst props = defineProps({ infoId: { type: String, default: '' }, isOwner: { type: Boolean, default: false } })\r\nconst emit = defineEmits(['callback'])\r\nconst isOwner = computed(() => props.isOwner)\r\nconst groupInfo = ref({})\r\nconst callBoard = ref('')\r\n\r\nconst handleSubmit = async () => {\r\n  const { code } = await api.chatGroupEdit({\r\n    form: { id: props.infoId, groupName: groupInfo.value.groupName, callBoard: callBoard.value },\r\n    ownerUserId: groupInfo.value.ownerUserId, memberUserIds: groupInfo.value.memberUserIds\r\n  })\r\n  if (code === 200) {\r\n    emit('callback', true, `群公告：\\n${callBoard.value}`)\r\n  }\r\n}\r\nconst handleReset = () => { emit('callback', false) }\r\nconst chatGroupInfo = async () => {\r\n  const { data } = await api.chatGroupInfo({ detailId: props.infoId })\r\n  groupInfo.value = data\r\n  callBoard.value = data.callBoard\r\n}\r\nonMounted(() => {\r\n  chatGroupInfo()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalGroupAnnouncement {\r\n  .GlobalGroupAnnouncementForm {\r\n    width: 420px;\r\n    height: 268px;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    padding: 0 40px;\r\n\r\n    .GlobalGroupAnnouncementButton {\r\n      width: 100%;\r\n      height: 46px;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      justify-content: center;\r\n\r\n      .zy-el-button {\r\n        width: 120px;\r\n        height: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalGroupAnnouncementBody {\r\n    width: 520px;\r\n    min-height: 220px;\r\n    padding: 20px 20px 40px 20px;\r\n\r\n    .GlobalChatGroupAnnouncementTitle {\r\n      width: 100%;\r\n      height: 32px;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      justify-content: space-between;\r\n      padding-bottom: 9px;\r\n\r\n      div {\r\n        display: flex;\r\n        align-items: flex-end;\r\n        justify-content: center;\r\n        font-size: 14px;\r\n        line-height: 14px;\r\n        font-weight: bold;\r\n        padding-left: 2px;\r\n\r\n        span {\r\n          width: 22px;\r\n          height: 22px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 6px;\r\n\r\n          .icon {\r\n            width: 22px;\r\n            height: 22px;\r\n\r\n            path {\r\n              fill: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-icon {\r\n        color: #ccc;\r\n        font-size: 18px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EADtCC,GAAA;EAESD,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAA+B;;EAJhDC,GAAA;EASSD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAkC;iBAVnD;;EAgBWA,KAAK,EAAC;AAA6B;;;;;;uBAf5CE,mBAAA,CAiBM,OAjBNC,UAiBM,GAhB2CC,MAAA,CAAAC,OAAO,I,cAAtDH,mBAAA,CAMM,OANNI,UAMM,GALJC,YAAA,CAAoGC,mBAAA;IAH1GC,UAAA,EAGyBL,MAAA,CAAAM,SAAS;IAHlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAGyBR,MAAA,CAAAM,SAAS,GAAAE,MAAA;IAAA;IAAEC,IAAI,EAAC,UAAU;IAAEC,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAC,MAAM;IAACC,WAAW,EAAC,KAAK;IAACC,SAAS,EAAT;2CACxFC,mBAAA,CAGM,OAHNC,UAGM,GAFJZ,YAAA,CAA8Ca,oBAAA;IAAlCC,OAAK,EAAEjB,MAAA,CAAAkB;EAAW;IALtCC,OAAA,EAAAC,QAAA,CAKwC;MAAA,OAAEb,MAAA,QAAAA,MAAA,OAL1Cc,gBAAA,CAKwC,IAAE,E;;IAL1CC,CAAA;MAMQnB,YAAA,CAA8Da,oBAAA;IAAnDP,IAAI,EAAC,SAAS;IAAEQ,OAAK,EAAEjB,MAAA,CAAAuB;;IAN1CJ,OAAA,EAAAC,QAAA,CAMwD;MAAA,OAAEb,MAAA,QAAAA,MAAA,OAN1Dc,gBAAA,CAMwD,IAAE,E;;IAN1DC,CAAA;YAAAE,mBAAA,gB,CASoDxB,MAAA,CAAAC,OAAO,I,cAAvDH,mBAAA,CAQM,OARN2B,UAQM,GAPJX,mBAAA,CAKM,OALNY,UAKM,GAJJZ,mBAAA,CAAsD,cAAjDA,mBAAA,CAAuC;IAAjCa,SAAyB,EAAjB3B,MAAA,CAAA4B;EAAgB,wBAX3CC,UAAA,G,0BAAAR,gBAAA,CAWoD,MAAI,G,GAChDlB,YAAA,CAEU2B,kBAAA;IAFAb,OAAK,EAAEjB,MAAA,CAAAkB;EAAW;IAZpCC,OAAA,EAAAC,QAAA,CAaU;MAAA,OAAqB,CAArBjB,YAAA,CAAqB4B,4BAAA,E;;IAb/BT,CAAA;QAgBMR,mBAAA,CAA8D,OAA9DkB,UAA8D,EAAAC,gBAAA,CAAlBjC,MAAA,CAAAM,SAAS,iB,KAhB3DkB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}