{"ast": null, "code": "import api from '@/api';\nimport { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';\nimport elementResizeDetectorMaker from 'element-resize-detector';\nimport * as echarts from 'echarts';\nvar __default__ = {\n  name: 'GlobalAiChart'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    option: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var erd = elementResizeDetectorMaker();\n    var props = __props;\n    var elChart = null;\n    var elChartRef = ref();\n    var initChart = function initChart() {\n      if (!elChart) {\n        elChart = echarts.init(elChartRef.value);\n      }\n      elChart.setOption(props.option);\n    };\n    onMounted(function () {\n      initChart();\n      nextTick(function () {\n        erd.listenTo(elChartRef.value, function (element) {\n          var _elChart;\n          (_elChart = elChart) === null || _elChart === void 0 || _elChart.resize();\n        });\n      });\n    });\n    onUnmounted(function () {\n      erd.uninstall(elChartRef.value);\n    });\n    watch(function () {\n      return props.option;\n    }, function () {\n      initChart();\n    });\n    var __returned__ = {\n      erd,\n      props,\n      get elChart() {\n        return elChart;\n      },\n      set elChart(v) {\n        elChart = v;\n      },\n      elChartRef,\n      initChart,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      onMounted,\n      onUnmounted,\n      nextTick,\n      watch,\n      get elementResizeDetectorMaker() {\n        return elementResizeDetectorMaker;\n      },\n      get echarts() {\n        return echarts;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["api", "ref", "computed", "onMounted", "onUnmounted", "nextTick", "watch", "elementResizeDetectorMaker", "echarts", "__default__", "name", "erd", "props", "__props", "<PERSON><PERSON><PERSON>", "elChartRef", "initChart", "init", "value", "setOption", "option", "listenTo", "element", "_<PERSON><PERSON><PERSON>", "resize", "uninstall"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalAiChat/GlobalAiChart.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChart\" ref=\"elChartRef\"></div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChart' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nimport * as echarts from 'echarts'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({ option: { type: Object, default: () => ({}) } })\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst initChart = () => {\r\n  if (!elChart) { elChart = echarts.init(elChartRef.value) }\r\n  elChart.setOption(props.option)\r\n}\r\nonMounted(() => {\r\n  initChart()\r\n  nextTick(() => {\r\n    erd.listenTo(elChartRef.value, (element) => {\r\n      elChart?.resize()\r\n    })\r\n  })\r\n})\r\nonUnmounted(() => { erd.uninstall(elChartRef.value) })\r\nwatch(() => props.option, () => {\r\n  initChart()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiChart {\r\n  width: 100%;\r\n  height: 320px;\r\n}\r\n</style>\r\n"], "mappings": "AAOA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,KAAK;AAC5E,OAAOC,0BAA0B,MAAM,yBAAyB;AAChE,OAAO,KAAKC,OAAO,MAAM,SAAS;AANlC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAgB,CAAC;;;;;;;;;;;;;IAOxC,IAAMC,GAAG,GAAGJ,0BAA0B,CAAC,CAAC;IACxC,IAAMK,KAAK,GAAGC,OAA8D;IAC5E,IAAIC,OAAO,GAAG,IAAI;IAClB,IAAMC,UAAU,GAAGd,GAAG,CAAC,CAAC;IACxB,IAAMe,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAI,CAACF,OAAO,EAAE;QAAEA,OAAO,GAAGN,OAAO,CAACS,IAAI,CAACF,UAAU,CAACG,KAAK,CAAC;MAAC;MACzDJ,OAAO,CAACK,SAAS,CAACP,KAAK,CAACQ,MAAM,CAAC;IACjC,CAAC;IACDjB,SAAS,CAAC,YAAM;MACda,SAAS,CAAC,CAAC;MACXX,QAAQ,CAAC,YAAM;QACbM,GAAG,CAACU,QAAQ,CAACN,UAAU,CAACG,KAAK,EAAE,UAACI,OAAO,EAAK;UAAA,IAAAC,QAAA;UAC1C,CAAAA,QAAA,GAAAT,OAAO,cAAAS,QAAA,eAAPA,QAAA,CAASC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACFpB,WAAW,CAAC,YAAM;MAAEO,GAAG,CAACc,SAAS,CAACV,UAAU,CAACG,KAAK,CAAC;IAAC,CAAC,CAAC;IACtDZ,KAAK,CAAC;MAAA,OAAMM,KAAK,CAACQ,MAAM;IAAA,GAAE,YAAM;MAC9BJ,SAAS,CAAC,CAAC;IACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}