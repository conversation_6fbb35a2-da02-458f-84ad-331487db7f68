{"ast": null, "code": "import { ref, watch, nextTick } from 'vue';\nvar __default__ = {\n  name: 'ChatPopupWindow'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    beforeClose: Function\n  },\n  emits: ['update:modelValue'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var show = ref(props.modelValue);\n    var isShow = ref(false);\n    var bodyShow = ref(false);\n    watch(function () {\n      return props.modelValue;\n    }, function () {\n      show.value = props.modelValue;\n      if (props.modelValue) {\n        isShow.value = true;\n        nextTick(function () {\n          bodyShow.value = true;\n        });\n      } else {\n        bodyShow.value = false;\n        setTimeout(function () {\n          isShow.value = false;\n        }, 99);\n      }\n    });\n    var closeClick = function closeClick() {\n      if (typeof props.beforeClose === 'function') {\n        props.beforeClose(function () {\n          emit('update:modelValue', false);\n        });\n      } else {\n        emit('update:modelValue', false);\n      }\n    };\n    var __returned__ = {\n      props,\n      emit,\n      show,\n      isShow,\n      bodyShow,\n      closeClick,\n      ref,\n      watch,\n      nextTick\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "watch", "nextTick", "__default__", "name", "props", "__props", "emit", "__emit", "show", "modelValue", "isShow", "bodyShow", "value", "setTimeout", "closeClick", "beforeClose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/components/chat-popup-window/chat-popup-window.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chat-popup-window\" @click.stop=\"closeClick\" v-if=\"isShow\">\r\n    <transition name=\"chat-popup-window-fade\">\r\n      <div class=\"chat-popup-window-body forbidSelect\" @click.stop v-if=\"bodyShow\">\r\n        <slot></slot>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChatPopupWindow' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch, nextTick } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  beforeClose: Function\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\n\r\nconst show = ref(props.modelValue)\r\nconst isShow = ref(false)\r\nconst bodyShow = ref(false)\r\nwatch(() => props.modelValue, () => {\r\n  show.value = props.modelValue\r\n  if (props.modelValue) {\r\n    isShow.value = true\r\n    nextTick(() => { bodyShow.value = true })\r\n  } else {\r\n    bodyShow.value = false\r\n    setTimeout(() => { isShow.value = false }, 99)\r\n  }\r\n})\r\nconst closeClick = () => {\r\n  if (typeof props.beforeClose === 'function') {\r\n    props.beforeClose(() => { emit('update:modelValue', false) })\r\n  } else {\r\n    emit('update:modelValue', false)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.chat-popup-window {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 998;\r\n  overflow: hidden;\r\n\r\n  .chat-popup-window-body {\r\n    min-width: 360px;\r\n    height: 68%;\r\n    background: #fff;\r\n    border-radius: 6px;\r\n    box-shadow: var(--zy-el-box-shadow-dark);\r\n  }\r\n\r\n  .chat-popup-window-fade-enter-active {\r\n    -webkit-animation: chat-popup-window-fade-in 0.3s;\r\n    animation: chat-popup-window-fade-in 0.3s;\r\n  }\r\n\r\n  .chat-popup-window-fade-leave-active {\r\n    -webkit-animation: chat-popup-window-fade-out 0.3s;\r\n    animation: chat-popup-window-fade-out 0.3s;\r\n  }\r\n\r\n  @keyframes chat-popup-window-fade-in {\r\n    0% {\r\n      transform: translate3d(0, -20px, 0);\r\n      opacity: 0;\r\n    }\r\n\r\n    100% {\r\n      transform: translate3d(0, 0, 0);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  @keyframes chat-popup-window-fade-out {\r\n    0% {\r\n      transform: translate3d(0, 0, 0);\r\n      opacity: 1;\r\n    }\r\n\r\n    100% {\r\n      transform: translate3d(0, -20px, 0);\r\n      opacity: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAaA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,KAAK;AAH1C,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAkB,CAAC;;;;;;;;;;;;;;IAI1C,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAAkC;IAE/C,IAAMC,IAAI,GAAGT,GAAG,CAACK,KAAK,CAACK,UAAU,CAAC;IAClC,IAAMC,MAAM,GAAGX,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMY,QAAQ,GAAGZ,GAAG,CAAC,KAAK,CAAC;IAC3BC,KAAK,CAAC;MAAA,OAAMI,KAAK,CAACK,UAAU;IAAA,GAAE,YAAM;MAClCD,IAAI,CAACI,KAAK,GAAGR,KAAK,CAACK,UAAU;MAC7B,IAAIL,KAAK,CAACK,UAAU,EAAE;QACpBC,MAAM,CAACE,KAAK,GAAG,IAAI;QACnBX,QAAQ,CAAC,YAAM;UAAEU,QAAQ,CAACC,KAAK,GAAG,IAAI;QAAC,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLD,QAAQ,CAACC,KAAK,GAAG,KAAK;QACtBC,UAAU,CAAC,YAAM;UAAEH,MAAM,CAACE,KAAK,GAAG,KAAK;QAAC,CAAC,EAAE,EAAE,CAAC;MAChD;IACF,CAAC,CAAC;IACF,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAI,OAAOV,KAAK,CAACW,WAAW,KAAK,UAAU,EAAE;QAC3CX,KAAK,CAACW,WAAW,CAAC,YAAM;UAAET,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;QAAC,CAAC,CAAC;MAC/D,CAAC,MAAM;QACLA,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;MAClC;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}