#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="$NODE_PATH:/mnt/d/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/sass@1.80.7/node_modules/sass/sass.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/sass@1.80.7/node_modules/sass/sass.js" "$@"
fi
