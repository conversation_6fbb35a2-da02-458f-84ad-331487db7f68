"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[8239],{28239:function(e,t,n){n.r(t),n.d(t,{default:function(){return L}});var o=n(79590),r=(n(76945),n(88810),n(68294)),a=(n(1920),n(8507),n(62427)),i=(n(98773),n(74061)),l=n(4955),u=n(88609);function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var e,t={},n=Object.prototype,o=n.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function d(e,t,n,o){var a=t&&t.prototype instanceof y?t:y,i=Object.create(a.prototype),l=new D(o||[]);return r(i,"_invoke",{value:b(e,n,l)}),i}function v(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var p="suspendedStart",m="suspendedYield",g="executing",f="completed",h={};function y(){}function N(){}function V(){}var E={};s(E,i,(function(){return this}));var _=Object.getPrototypeOf,k=_&&_(_(B([])));k&&k!==n&&o.call(k,i)&&(E=k);var S=V.prototype=y.prototype=Object.create(E);function x(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function n(r,a,i,l){var u=v(e[r],e,a);if("throw"!==u.type){var c=u.arg,s=c.value;return s&&"object"==typeof s&&o.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,i,l)}),(function(e){n("throw",e,i,l)})):t.resolve(s).then((function(e){c.value=e,i(c)}),(function(e){return n("throw",e,i,l)}))}l(u.arg)}var a;r(this,"_invoke",{value:function(e,o){function r(){return new t((function(t,r){n(e,o,t,r)}))}return a=a?a.then(r,r):r()}})}function b(t,n,o){var r=p;return function(a,i){if(r===g)throw Error("Generator is already running");if(r===f){if("throw"===a)throw i;return{value:e,done:!0}}for(o.method=a,o.arg=i;;){var l=o.delegate;if(l){var u=C(l,o);if(u){if(u===h)continue;return u}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===p)throw r=f,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=g;var c=v(t,n,o);if("normal"===c.type){if(r=o.done?f:m,c.arg===h)continue;return{value:c.arg,done:o.done}}"throw"===c.type&&(r=f,o.method="throw",o.arg=c.arg)}}}function C(t,n){var o=n.method,r=t.iterator[o];if(r===e)return n.delegate=null,"throw"===o&&t.iterator.return&&(n.method="return",n.arg=e,C(t,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),h;var a=v(r,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,h;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,h):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function B(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,a=function n(){for(;++r<t.length;)if(o.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return N.prototype=V,r(S,"constructor",{value:V,configurable:!0}),r(V,"constructor",{value:N,configurable:!0}),N.displayName=s(V,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===N||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,V):(e.__proto__=V,s(e,u,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},x(w.prototype),s(w.prototype,l,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,o,r,a){void 0===a&&(a=Promise);var i=new w(d(e,n,o,r),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},x(S),s(S,u,"Generator"),s(S,i,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=B,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(o,r){return l.type="throw",l.arg=t,n.next=o,r&&(n.method="next",n.arg=e),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var u=o.call(i,"catchLoc"),c=o.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;T(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,n,o){return this.delegate={iterator:B(t),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=e),h}},t}function s(e,t,n,o,r,a,i){try{var l=e[a](i),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(o,r)}function d(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var a=e.apply(t,n);function i(e){s(a,o,r,i,l,"next",e)}function l(e){s(a,o,r,i,l,"throw",e)}i(void 0)}))}}var v={class:"suggestPopHead"},p={key:0,class:"content"},m={class:"suggestPopContentHeader"},g={key:0,class:"suggestPopContentChooseRole"},f={class:"suggestPopContentBody"},h={class:"mb20"},y={class:"mb20"},N={class:"hasColorBox"},V={class:""},E={class:""},_={class:"nocolorSpan"},k={class:"mb20"},S={class:"nocolorSpan"},x={class:"nocolorSpan"},w={name:"suggestPop"},b=Object.assign(w,{props:{isVisible:{type:Boolean,default:!1},routePth:{type:String,default:""}},setup(e){var t=(0,i.computed)((function(){var e;return(null===(e=u.FR.value)||void 0===e?void 0:e.systemPlatform)||""})),n=(0,i.inject)("openPage"),s=function(){var e=(new Date).getHours();return e<12?"早上好":e<18?"下午好":"晚上好"},w=(0,i.ref)(!1),b=(0,i.ref)(!1),C=function(){b.value?b.value=!b.value:setTimeout((function(){b.value=!b.value}),300),w.value=!w.value},L=(0,i.ref)({}),T=(0,i.ref)("CPPCC"==t.value?["proposal_committee","suggestion_office_user","cppcc_member"]:["npc_contact_committee","suggestion_office_user","delegation_manager","npc_member"]),D=(0,i.ref)(""),B=(0,i.ref)([]),P=(0,i.ref)([]);(0,i.onMounted)((function(){setTimeout((function(){w.value=!0,setTimeout((function(){b.value=!0}),100)}),300),L.value=JSON.parse(sessionStorage.getItem("user")),P.value=L.value.specialRoleKeys.filter((function(e){return T.value.includes(e)})),P.value.includes("npc_contact_committee")&&B.value.push({value:"npc_contact_committee",label:"联工委",param:"remind_admin"}),P.value.includes("proposal_committee")&&B.value.push({value:"proposal_committee",label:"提案委",param:"remind_admin"}),P.value.includes("suggestion_office_user")&&B.value.push({value:"suggestion_office_user",label:"办理单位",param:"remind_office"}),P.value.includes("delegation_manager")&&B.value.push({value:"delegation_manager",label:"代表团管理员",param:"remind_delegation"}),P.value.includes("npc_member")&&B.value.push({value:"npc_member",label:"人大代表",param:"remind_npc_member"}),P.value.includes("cppcc_member")&&B.value.push({value:"cppcc_member",label:"政协委员",param:"remind_member"}),D.value=P.value[0],O()}));var A=(0,i.ref)({}),j=(0,i.ref)(!0),O=function(){var e=d(c().mark((function e(){var n,o;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n="CPPCC"===t.value?"/proposalStatistics/composite":"/suggestionStatistics/composite",e.next=3,l.A.globalJson(n,{countView:B.value.filter((function(e){return e.value===D.value}))[0].param,isReceive:"0"});case 3:o=e.sent,A.value=o.data.tableData.length?o.data.tableData[0]:{},j.value=!1;case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),F=function(){j.value=!0,O()},I=function(e){console.log("🚀 ~ goSuggestList ~ type:",e),n({key:"routePath",value:"/suggest/"+e})};return function(t,n){var l,u,c,d,T,R,G,M,H,J,U,Y,z,K,q,Q,W,X,Z,$,ee,te,ne,oe,re,ae,ie,le,ue,ce=(0,i.resolveComponent)("RefreshRight"),se=a.tk,de=(0,i.resolveComponent)("Close"),ve=(0,i.resolveComponent)("Check"),pe=r.P9,me=r.AV,ge=o.L;return(0,i.withDirectives)(((0,i.openBlock)(),(0,i.createElementBlock)("div",{class:(0,i.normalizeClass)(["suggestPop",{show:e.isVisible||w.value}])},[(0,i.createElementVNode)("div",v,[w.value?((0,i.openBlock)(),(0,i.createBlock)(se,{key:0},{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(ce,{onClick:F})]})),_:1})):(0,i.createCommentVNode)("",!0),w.value?((0,i.openBlock)(),(0,i.createBlock)(se,{key:1,onClick:C},{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(de)]})),_:1})):((0,i.openBlock)(),(0,i.createBlock)(se,{key:2,onClick:C},{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(ve)]})),_:1}))]),b.value?((0,i.openBlock)(),(0,i.createElementBlock)("div",p,[(0,i.createElementVNode)("div",m,(0,i.toDisplayString)(L.value.userName)+"，"+(0,i.toDisplayString)(s()),1),P.value.length>1?((0,i.openBlock)(),(0,i.createElementBlock)("div",g,[n[24]||(n[24]=(0,i.createElementVNode)("div",null,"选择您的身份以查看更多待办",-1)),(0,i.createVNode)(me,{modelValue:D.value,"onUpdate:modelValue":n[0]||(n[0]=function(e){return D.value=e}),size:"small",style:{width:"120px"},onChange:O},{default:(0,i.withCtx)((function(){return[((0,i.openBlock)(!0),(0,i.createElementBlock)(i.Fragment,null,(0,i.renderList)(B.value,(function(e){return(0,i.openBlock)(),(0,i.createBlock)(pe,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])])):(0,i.createCommentVNode)("",!0),(0,i.createElementVNode)("div",f,[(0,i.createElementVNode)("div",null,(0,i.toDisplayString)(A.value.termYear),1),"npc_contact_committee"==D.value?((0,i.openBlock)(),(0,i.createElementBlock)(i.Fragment,{key:0},[(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[1]||(n[1]=function(e){return I("AllSuggest")})},(0,i.toDisplayString)((null===(l=A.value.meetList)||void 0===l?void 0:l.amount)||0),1),n[25]||(n[25]=(0,i.createTextVNode)(" 件大会建议， ")),(0,i.createElementVNode)("span",{onClick:n[2]||(n[2]=function(e){return I("AllSuggest")})},(0,i.toDisplayString)((null===(u=A.value.usualList)||void 0===u?void 0:u.amount)||0),1),n[26]||(n[26]=(0,i.createTextVNode)(" 件闭会建议, ")),(0,i.createElementVNode)("span",{onClick:n[3]||(n[3]=function(e){return I("SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议")})},(0,i.toDisplayString)((null===(c=A.value.importantList)||void 0===c?void 0:c.amount)||0),1),n[27]||(n[27]=(0,i.createTextVNode)(" 件重点督办建议 "))]),(0,i.createElementVNode)("div",h,[(0,i.createElementVNode)("span",{onClick:n[4]||(n[4]=function(e){return I("SuggestReview?tableId=id_sgsn_suggestion_prepareVerify&moduleName=待审查建议")})},(0,i.toDisplayString)((null===(d=A.value.auditList)||void 0===d?void 0:d.amount)||0),1),n[28]||(n[28]=(0,i.createTextVNode)(" 待审查， ")),(0,i.createElementVNode)("span",{onClick:n[5]||(n[5]=function(e){return I("SuggestAdvanceAssign?tableId=id_sgsn_suggestion_preAssignSuggestion&moduleName=预交办")})},(0,i.toDisplayString)((null===(T=A.value.preAssignList)||void 0===T?void 0:T.amount)||0),1),n[29]||(n[29]=(0,i.createTextVNode)(" 件预交办, ")),(0,i.createElementVNode)("span",{onClick:n[6]||(n[6]=function(e){return I("SuggestAssign?tableId=id_sgsn_suggestion_prepareSubmitHandle&moduleName=人大交办中")})},(0,i.toDisplayString)((null===(R=A.value.prepareSubmitHandleList)||void 0===R?void 0:R.amount)||0),1),n[30]||(n[30]=(0,i.createTextVNode)(" 件人大交办中 "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[7]||(n[7]=function(e){return I("SuggestTransact")})},(0,i.toDisplayString)((null===(G=A.value.handleList)||void 0===G?void 0:G.amount)||0),1),n[31]||(n[31]=(0,i.createTextVNode)(" 办理中， 其中 "))]),(0,i.createElementVNode)("div",y,[(0,i.createElementVNode)("span",{onClick:n[8]||(n[8]=function(e){return I("SuggestApplyForAdjust")})},(0,i.toDisplayString)((null===(M=A.value.adjustList)||void 0===M?void 0:M.amount)||0),1),n[32]||(n[32]=(0,i.createTextVNode)(" 件调整申请待审核， ")),(0,i.createElementVNode)("span",{onClick:n[9]||(n[9]=function(e){return I("SuggestApplyForPostpone")})},(0,i.toDisplayString)((null===(H=A.value.delayList)||void 0===H?void 0:H.amount)||0),1),n[33]||(n[33]=(0,i.createTextVNode)(" 件延期申请待审核 "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[10]||(n[10]=function(e){return I("SuggestReply")})},(0,i.toDisplayString)((null===(J=A.value.answerList)||void 0===J?void 0:J.amount)||0),1),n[34]||(n[34]=(0,i.createTextVNode)(" 件已答复 "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[11]||(n[11]=function(e){return I("SuggestConclude")})},(0,i.toDisplayString)((null===(U=A.value.finishList)||void 0===U?void 0:U.amount)||0),1),n[35]||(n[35]=(0,i.createTextVNode)(" 件已办结 "))])],64)):(0,i.createCommentVNode)("",!0),"suggestion_office_user"==D.value?((0,i.openBlock)(),(0,i.createElementBlock)(i.Fragment,{key:1},[(0,i.createElementVNode)("div",null,[n[36]||(n[36]=(0,i.createTextVNode)(" 共 ")),(0,i.createElementVNode)("span",{onClick:n[12]||(n[12]=function(e){return I("UnitSuggestTransact")})},(0,i.toDisplayString)((null===(Y=A.value.handleList)||void 0===Y?void 0:Y.amount)||0),1),n[37]||(n[37]=(0,i.createTextVNode)(" 件办理中， ")),(0,i.createElementVNode)("span",{onClick:n[13]||(n[13]=function(e){return I("SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议")})},(0,i.toDisplayString)((null===(z=A.value.importantList)||void 0===z?void 0:z.amount)||0),1),n[38]||(n[38]=(0,i.createTextVNode)(" 件重点督办建议 "))]),(0,i.createElementVNode)("div",N,[n[39]||(n[39]=(0,i.createTextVNode)(" 其中 ")),n[40]||(n[40]=(0,i.createElementVNode)("span",{class:"red"},null,-1)),(0,i.createElementVNode)("span",null,(0,i.toDisplayString)((null===(K=A.value.redAnswerDate)||void 0===K?void 0:K.amount)||0),1),n[41]||(n[41]=(0,i.createTextVNode)(" 件, ")),n[42]||(n[42]=(0,i.createElementVNode)("span",{class:"yellow"},null,-1)),(0,i.createElementVNode)("span",V,(0,i.toDisplayString)((null===(q=A.value.yellowAnswerDate)||void 0===q?void 0:q.amount)||0),1),n[43]||(n[43]=(0,i.createTextVNode)(" 件， ")),n[44]||(n[44]=(0,i.createElementVNode)("span",{class:"green"},null,-1)),(0,i.createElementVNode)("span",E,(0,i.toDisplayString)((null===(Q=A.value.greenAnswerDate)||void 0===Q?void 0:Q.amount)||0),1),n[45]||(n[45]=(0,i.createTextVNode)(" 件 "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",_,(0,i.toDisplayString)((null===(W=A.value.adjustList)||void 0===W?void 0:W.amount)||0),1),n[46]||(n[46]=(0,i.createTextVNode)(" 件调整申请待审核， "))]),(0,i.createElementVNode)("div",k,[(0,i.createElementVNode)("span",S,(0,i.toDisplayString)((null===(X=A.value.delayList)||void 0===X?void 0:X.amount)||0),1),n[47]||(n[47]=(0,i.createTextVNode)(" 件申请延期待审核， "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[14]||(n[14]=function(e){return I("UnitSuggestReply")})},(0,i.toDisplayString)((null===(Z=A.value.answerList)||void 0===Z?void 0:Z.amount)||0),1),n[48]||(n[48]=(0,i.createTextVNode)(" 件已答复 "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[15]||(n[15]=function(e){return I("UnitSuggestConclude")})},(0,i.toDisplayString)((null===($=A.value.finishList)||void 0===$?void 0:$.amount)||0),1),n[49]||(n[49]=(0,i.createTextVNode)(" 件已办结 "))])],64)):(0,i.createCommentVNode)("",!0),"delegation_manager"==D.value?((0,i.openBlock)(),(0,i.createElementBlock)(i.Fragment,{key:2},[(0,i.createElementVNode)("div",null,[n[50]||(n[50]=(0,i.createTextVNode)(" 本代表团 ")),(0,i.createElementVNode)("span",{onClick:n[16]||(n[16]=function(e){return I("AllSuggest")})},(0,i.toDisplayString)((null===(ee=A.value.allDelegationList)||void 0===ee?void 0:ee.amount)||0),1),n[51]||(n[51]=(0,i.createTextVNode)(" 件代表建议， ")),(0,i.createElementVNode)("span",{onClick:n[17]||(n[17]=function(e){return I("AllSuggest")})},(0,i.toDisplayString)((null===(te=A.value.teamList)||void 0===te?void 0:te.amount)||0),1),n[52]||(n[52]=(0,i.createTextVNode)(" 件全团建议 "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[18]||(n[18]=function(e){return I("SuggestReview?tableId=id_sgsn_suggestion_base_delegation_verify&moduleName=代表团审查&nextNode=prepareVerify")})},(0,i.toDisplayString)((null===(ne=A.value.delegationAuditList)||void 0===ne?void 0:ne.amount)||0),1),n[53]||(n[53]=(0,i.createTextVNode)(" 件代表团审查建议 "))])],64)):(0,i.createCommentVNode)("",!0),"npc_member"==D.value?((0,i.openBlock)(),(0,i.createElementBlock)(i.Fragment,{key:3},[(0,i.createElementVNode)("div",null,[n[54]||(n[54]=(0,i.createTextVNode)(" 您已提交 ")),(0,i.createElementVNode)("span",{onClick:n[19]||(n[19]=function(e){return I("MyLedSuggest")})},(0,i.toDisplayString)((null===(oe=A.value.totalList)||void 0===oe?void 0:oe.amount)||0),1),n[55]||(n[55]=(0,i.createTextVNode)(" 件建议， ")),(0,i.createElementVNode)("span",x,(0,i.toDisplayString)((null===(re=A.value.importantList)||void 0===re?void 0:re.amount)||0),1),n[56]||(n[56]=(0,i.createTextVNode)(" 件形成重点督办建议 "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[20]||(n[20]=function(e){return I("MyJointSuggest")})},(0,i.toDisplayString)((null===(ae=A.value.needJoinList)||void 0===ae?void 0:ae.amount)||0),1),n[57]||(n[57]=(0,i.createTextVNode)(" 件需要确认是否附议， ")),(0,i.createElementVNode)("span",{onClick:n[21]||(n[21]=function(e){return I("MyLedSuggest")})},(0,i.toDisplayString)((null===(ie=A.value.backList)||void 0===ie?void 0:ie.amount)||0),1),n[58]||(n[58]=(0,i.createTextVNode)(" 件被退回， ")),(0,i.createElementVNode)("span",{onClick:n[22]||(n[22]=function(e){return I("SuggestDraftBox")})},(0,i.toDisplayString)((null===(le=A.value.SuggestDraftBox)||void 0===le?void 0:le.amount)||0),1),n[59]||(n[59]=(0,i.createTextVNode)(" 件在草稿箱 "))]),(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{onClick:n[23]||(n[23]=function(e){return I("MyLedSuggest")})},(0,i.toDisplayString)((null===(ue=A.value.MyLedSuggest)||void 0===ue?void 0:ue.amount)||0),1),n[60]||(n[60]=(0,i.createTextVNode)(" 件待满意度测评 "))])],64)):(0,i.createCommentVNode)("",!0)])])):(0,i.createCommentVNode)("",!0)],2)),[[ge,j.value]])}}});const C=b;var L=C}}]);