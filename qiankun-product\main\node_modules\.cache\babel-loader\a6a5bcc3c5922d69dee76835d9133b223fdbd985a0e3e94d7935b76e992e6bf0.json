{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"OtherRouter\"\n};\nvar _hoisted_2 = [\"src\"];\nvar _hoisted_3 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [$setup.route.query.menuRouteType === '2' ? (_openBlock(), _createBlock(_component_el_scrollbar, {\n    key: 0,\n    class: \"OtherRouterImg\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"img\", {\n        src: $setup.url\n      }, null, 8 /* PROPS */, _hoisted_2)];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), $setup.route.query.menuRouteType === '3' ? (_openBlock(), _createElementBlock(\"iframe\", {\n    key: 1,\n    class: \"OtherRouterIframe\",\n    frameborder: \"0\",\n    src: $setup.url\n  }, null, 8 /* PROPS */, _hoisted_3)) : _createCommentVNode(\"v-if\", true), $setup.route.query.menuRouteType === '4' ? (_openBlock(), _createBlock(_component_el_empty, {\n    key: 2,\n    image: $setup.IEPNG,\n    description: $setup.url\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.blankOpen\n      }, {\n        default: _withCtx(function () {\n          return _cache[0] || (_cache[0] = [_createTextVNode(\"打开系统\")]);\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"image\", \"description\"])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "$setup", "route", "query", "menuRouteType", "_createBlock", "_component_el_scrollbar", "key", "default", "_withCtx", "_createElementVNode", "src", "url", "_hoisted_2", "_", "_createCommentVNode", "frameborder", "_hoisted_3", "_component_el_empty", "image", "IEPNG", "description", "_createVNode", "_component_el_button", "type", "onClick", "blankOpen", "_cache", "_createTextVNode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\OtherRouter\\OtherRouter.vue"], "sourcesContent": ["<template>\r\n  <div class=\"OtherRouter\">\r\n    <template v-if=\"route.query.menuRouteType === '2'\">\r\n      <el-scrollbar class=\"OtherRouterImg\">\r\n        <img :src=\"url\" />\r\n      </el-scrollbar>\r\n    </template>\r\n    <template v-if=\"route.query.menuRouteType === '3'\">\r\n      <iframe class=\"OtherRouterIframe\" frameborder=\"0\" :src=\"url\"></iframe>\r\n    </template>\r\n    <template v-if=\"route.query.menuRouteType === '4'\">\r\n      <el-empty :image=\"IEPNG\" :description=\"url\">\r\n        <el-button type=\"primary\" @click=\"blankOpen\">打开系统</el-button>\r\n      </el-empty>\r\n    </template>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'OtherRouter' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport utils from 'common/js/utils.js'\r\nimport { user } from 'common/js/system_var.js'\r\nimport IEPNG from '../img/IE.png'\r\nconst route = useRoute()\r\nlet isEnter = true\r\nconst url = ref('')\r\nonMounted(() => {\r\n  if (!isEnter) return\r\n  if (route.query.url) {\r\n    if (route.query.menuRouteType === '2') {\r\n      url.value = api.fileURL(utils.decrypt(route.query.url, new Date().getTime(), '2'))\r\n    } else {\r\n      const path = utils.decrypt(route.query.url, new Date().getTime(), '2')\r\n      url.value = pathData(path)\r\n      if (route.query.menuRouteType === '4') {\r\n        window.open(url.value, '_blank')\r\n      }\r\n    }\r\n  }\r\n})\r\nonActivated(() => {\r\n  if (isEnter) { return isEnter = false }\r\n  if (route.query.url) {\r\n    if (route.query.menuRouteType === '2') {\r\n      url.value = api.fileURL(utils.decrypt(route.query.url, new Date().getTime(), '2'))\r\n    } else {\r\n      const path = utils.decrypt(route.query.url, new Date().getTime(), '2')\r\n      url.value = pathData(path)\r\n      if (route.query.menuRouteType === '4') {\r\n        window.open(url.value, '_blank')\r\n      }\r\n    }\r\n  }\r\n})\r\nconst pathData = (path) => {\r\n  const token = sessionStorage.getItem('token') || ''\r\n  const areaId = sessionStorage.getItem('AreaId') || ''\r\n  var paramsData = []\r\n  if (route.query.token) {\r\n    paramsData.push(`${route.query.token}=${token}`)\r\n  }\r\n  if (route.query.areaId) {\r\n    paramsData.push(`${route.query.areaId}=${areaId}`)\r\n  }\r\n  if (route.query.mobile) {\r\n    paramsData.push(`${route.query.mobile}=${user.value.mobile}`)\r\n  }\r\n  if (route.query.params) {\r\n    const paramsData = route.query.params.split(',')\r\n    for (const i in paramsData) {\r\n      paramsData.push(`${paramsData[i]}=${route.query[paramsData[i]]}`)\r\n    }\r\n  }\r\n  const urlPath = path + (path.indexOf('?') !== -1 ? '&' : '?') + paramsData.join('&')\r\n  const mainUrlPath = api.authorize(`${path}&${paramsData.join('&')}`)\r\n  return route.query.main === 'true' ? mainUrlPath : urlPath\r\n}\r\nconst blankOpen = () => { window.open(url.value, '_blank') }\r\n</script>\r\n<style lang=\"scss\">\r\n.OtherRouter {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n\r\n  .OtherRouterImg {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n    img {\r\n      width: 100%;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .OtherRouterIframe {\r\n    width: 100%;\r\n    height: 100%;\r\n    overflow: auto;\r\n  }\r\n\r\n  .zy-el-empty__description {\r\n    display: none;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;iBAD1B;iBAAA;;;;;uBACEC,mBAAA,CAcM,OAdNC,UAcM,GAbYC,MAAA,CAAAC,KAAK,CAACC,KAAK,CAACC,aAAa,Y,cACvCC,YAAA,CAEeC,uBAAA;IALrBC,GAAA;IAGoBT,KAAK,EAAC;;IAH1BU,OAAA,EAAAC,QAAA,CAIQ;MAAA,OAAkB,CAAlBC,mBAAA,CAAkB;QAAZC,GAAG,EAAEV,MAAA,CAAAW;MAAG,wBAJtBC,UAAA,E;;IAAAC,CAAA;QAAAC,mBAAA,gBAOoBd,MAAA,CAAAC,KAAK,CAACC,KAAK,CAACC,aAAa,Y,cACvCL,mBAAA,CAAsE;IAR5EQ,GAAA;IAQcT,KAAK,EAAC,mBAAmB;IAACkB,WAAW,EAAC,GAAG;IAAEL,GAAG,EAAEV,MAAA,CAAAW;0BAR9DK,UAAA,KAAAF,mBAAA,gBAUoBd,MAAA,CAAAC,KAAK,CAACC,KAAK,CAACC,aAAa,Y,cACvCC,YAAA,CAEWa,mBAAA;IAbjBX,GAAA;IAWiBY,KAAK,EAAElB,MAAA,CAAAmB,KAAK;IAAGC,WAAW,EAAEpB,MAAA,CAAAW;;IAX7CJ,OAAA,EAAAC,QAAA,CAYQ;MAAA,OAA6D,CAA7Da,YAAA,CAA6DC,oBAAA;QAAlDC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAExB,MAAA,CAAAyB;;QAZ1ClB,OAAA,EAAAC,QAAA,CAYqD;UAAA,OAAIkB,MAAA,QAAAA,MAAA,OAZzDC,gBAAA,CAYqD,MAAI,E;;QAZzDd,CAAA;;;IAAAA,CAAA;iDAAAC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}