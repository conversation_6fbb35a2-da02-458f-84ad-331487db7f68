{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"NoneAccessAuthority\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_empty, {\n    description: \"您暂无访问权限\"\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_empty", "description"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\NoneAccessAuthority\\NoneAccessAuthority.vue"], "sourcesContent": ["<template>\r\n  <div class=\"NoneAccessAuthority\">\r\n    <el-empty description=\"您暂无访问权限\"></el-empty>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'NoneAccessAuthority' }\r\n</script>\r\n<script setup>\r\n</script>\r\n<style lang=\"scss\">\r\n.NoneAccessAuthority {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;;uBAAhCC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAA2CC,mBAAA;IAAjCC,WAAW,EAAC;EAAS,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}