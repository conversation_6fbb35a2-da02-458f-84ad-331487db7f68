"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[4453],{74453:function(e,t,r){r.r(t),r.d(t,{default:function(){return C}});var n=r(49744),o=(r(76945),r(98326),r(84098)),a=(r(63584),r(44863)),i=(r(4711),r(74061)),l=r(4955),c=r(59335);function u(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */u=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var a=t&&t.prototype instanceof g?t:g,i=Object.create(a.prototype),l=new O(n||[]);return o(i,"_invoke",{value:B(e,r,l)}),i}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var d="suspendedStart",v="suspendedYield",p="executing",m="completed",y={};function g(){}function w(){}function b(){}var k={};s(k,i,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(I([])));E&&E!==r&&n.call(E,i)&&(k=E);var S=b.prototype=g.prototype=Object.create(k);function L(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function _(e,t){function r(o,a,i,l){var c=h(e[o],e,a);if("throw"!==c.type){var u=c.arg,s=u.value;return s&&"object"==typeof s&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(s).then((function(e){u.value=e,i(u)}),(function(e){return r("throw",e,i,l)}))}l(c.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function B(t,r,n){var o=d;return function(a,i){if(o===p)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var l=n.delegate;if(l){var c=N(l,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var u=h(t,r,n);if("normal"===u.type){if(o=n.done?m:v,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=m,n.method="throw",n.arg=u.arg)}}}function N(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,N(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=h(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function G(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(G,this),this.reset(!0)}function I(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return w.prototype=b,o(S,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=s(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,s(e,c,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},L(_.prototype),s(_.prototype,l,(function(){return this})),t.AsyncIterator=_,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new _(f(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},L(S),s(S,c,"Generator"),s(S,i,(function(){return this})),s(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=I,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return l.type="throw",l.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),C(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;C(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:I(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function s(e,t,r,n,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,o)}function f(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){s(a,n,o,i,l,"next",e)}function l(e){s(a,n,o,i,l,"throw",e)}i(void 0)}))}}function h(e){return m(e)||p(e)||v(e)||d()}function d(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(e,t){if(e){if("string"==typeof e)return y(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?y(e,t):void 0}}function p(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function m(e){if(Array.isArray(e))return y(e)}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var g={class:"GlobalRegionSelect"},w={key:0,class:"GlobalRegionSelectBody"},b={key:0,class:"GlobalRegionSelectBodyInfo"},k={class:"GlobalRegionSelectBodyInfoOld"},x={key:0,class:"GlobalRegionSelectColumn"},E=["onClick"],S={class:"GlobalRegionSelectName"},L={key:2,class:"GlobalRegionSelectBodyInfo"},_={class:"GlobalRegionSelectBodyInfoOld"},B={name:"GlobalRegionSelect"},N=Object.assign(B,{emits:["callback"],setup(e,t){var r=t.emit,s=r,d=(0,c.useStore)(),v=(0,i.computed)((function(){return d.getters.getUserFn})),p=(0,i.computed)((function(){return d.getters.getAreaFn})),m='<svg t="1744792270111" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19596" width="24" height="24"><path d="M753.810286 731.428571c74.459429 10.678857 137.142857 26.185143 181.76 44.909715 58.002286 24.283429 88.429714 55.588571 88.429714 93.330285 0 29.988571-19.017143 56.027429-55.588571 77.385143-27.209143 15.872-64.731429 29.842286-111.542858 41.398857C764.196571 1011.419429 641.828571 1024 512 1024s-252.196571-12.580571-344.868571-35.547429c-46.811429-11.629714-84.406857-25.6-111.616-41.398857C19.163429 925.769143 0 899.510857 0 869.668571c0-37.814857 30.134857-69.046857 87.917714-93.330285 39.789714-16.676571 93.622857-30.793143 156.818286-41.106286l24.137143-3.657143 10.605714 72.411429c-67.876571 9.728-124.050286 23.478857-162.596571 39.716571-28.598857 12.068571-42.788571 24.137143-42.788572 25.965714 0 6.875429 38.546286 29.769143 111.030857 47.762286C271.872 939.008 387.949714 950.857143 512 950.857143c123.611429 0 239.835429-11.922286 326.875429-33.426286 72.557714-17.993143 111.030857-40.886857 111.030857-47.762286 0-1.828571-14.336-13.897143-43.154286-26.038857-34.523429-14.482286-82.944-26.916571-141.165714-36.352l-22.308572-3.437714 10.532572-72.411429z m15.652571-623.908571a352.841143 352.841143 0 0 1 102.180572 253.44 376.685714 376.685714 0 0 1-99.181715 252.342857l-13.019428 13.458286-213.357715 211.821714a54.198857 54.198857 0 0 1-69.266285 5.632l-6.656-5.632L256.804571 626.834286a376.685714 376.685714 0 0 1-110.445714-265.801143A352.841143 352.841143 0 0 1 248.466286 107.52a369.298286 369.298286 0 0 1 520.923428 0z m-475.721143 46.518857a288.402286 288.402286 0 0 0-83.382857 206.921143 312.100571 312.100571 0 0 0 79.725714 207.140571l12.214858 12.8 206.701714 204.361143 206.701714-204.361143a312.100571 312.100571 0 0 0 91.940572-219.940571 288.402286 288.402286 0 0 0-84.699429-206.994286 304.274286 304.274286 0 0 0-429.202286 0z m215.259429 38.765714a170.642286 170.642286 0 1 1 0 341.357715 170.642286 170.642286 0 0 1 0-341.284572z m0 64.073143a106.642286 106.642286 0 1 0 0 213.284572 106.642286 106.642286 0 0 0 0-213.284572z" fill="#ffffff" p-id="19597"></path></svg>',y=(0,i.ref)(!1),B=(0,i.ref)(),N=(0,i.ref)(""),G=(0,i.ref)(""),C=(0,i.ref)([]),O=(0,i.ref)([]),I=(0,i.ref)({}),R=(0,i.ref)(""),V=function(e){if(!Array.isArray(e))return[];var t={province:{id:"province",name:"省",data:[]},city:{id:"city",name:"市",data:[]},county:{id:"county",name:"区县",data:[]},other:{id:"other",name:"其他",data:[]}},r=e.reduce((function(e,r){var n=r.areaLevel,o=t[n]||t.other;return o.data.push(r),e}),t);return Object.values(r).filter((function(e){return e.data.length>0}))},j=function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r];t.push(n),t=[].concat(h(t),h(j(n.children)))}return t},A=function(e,t){return!e||t.name.includes(e)},T=function(e){sessionStorage.setItem("isRegionSelect","1"),R.value="",s("callback",e)},F=function(){var e=f(u().mark((function e(){var t,r,n;return u().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=sessionStorage.getItem("oldRegionInfo")||"",!t){e.next=5;break}I.value=JSON.parse(t),e.next=10;break;case 5:return e.next=7,l.A.loginLastAreaId();case 7:r=e.sent,n=r.data,I.value=n;case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,i.onMounted)((function(){F(),setTimeout((function(){y.value=!0,(0,i.nextTick)((function(){var e;null===(e=B.value)||void 0===e||e.setCurrentKey(N.value)}))}),99)})),(0,i.watch)((function(){return v.value}),(function(){var e;N.value=null===(e=v.value)||void 0===e?void 0:e.areaId,(0,i.nextTick)((function(){var e;null===(e=B.value)||void 0===e||e.setCurrentKey(N.value)}))}),{immediate:!0}),(0,i.watch)((function(){return p.value}),(function(){var e=j(p.value);G.value=e.length<10?"list":"tree","list"===G.value?C.value=V(e):"tree"===G.value&&(O.value=[p.value[0].id]||0,(0,i.nextTick)((function(){var e;null===(e=B.value)||void 0===e||e.setCurrentKey(N.value)})))}),{immediate:!0}),(0,i.watch)(R,(function(e){var t;null===(t=B.value)||void 0===t||t.filter(e)})),function(e,t){var r=a.kA,l=o.WK,c=n.q;return(0,i.openBlock)(),(0,i.createElementBlock)("div",g,[(0,i.createVNode)(i.Transition,{"enter-active-class":"animate__animated animate__zoomIn animate__faster","leave-active-class":"animate__animated animate__zoomOut animate__faster"},{default:(0,i.withCtx)((function(){return[y.value?((0,i.openBlock)(),(0,i.createElementBlock)("div",w,[t[2]||(t[2]=(0,i.createElementVNode)("div",{class:"GlobalRegionSelectBodyTitle"},"选择登录地区",-1)),"list"===G.value?((0,i.openBlock)(),(0,i.createElementBlock)("div",b,[(0,i.createElementVNode)("div",k,"上次登录地区："+(0,i.toDisplayString)(I.value.name),1),t[1]||(t[1]=(0,i.createElementVNode)("div",{class:"GlobalRegionSelectBodyInfoTip"},"请在下方选择本次登录地区！",-1))])):(0,i.createCommentVNode)("",!0),"list"===G.value?((0,i.openBlock)(),(0,i.createBlock)(r,{key:1,class:"GlobalRegionSelectScrollbar"},{default:(0,i.withCtx)((function(){return[((0,i.openBlock)(!0),(0,i.createElementBlock)(i.Fragment,null,(0,i.renderList)(C.value,(function(e){return(0,i.openBlock)(),(0,i.createElementBlock)("div",{class:"GlobalRegionSelectList",key:e.id},["province"!==e.id?((0,i.openBlock)(),(0,i.createElementBlock)("div",x,(0,i.toDisplayString)(e.name),1)):(0,i.createCommentVNode)("",!0),((0,i.openBlock)(!0),(0,i.createElementBlock)(i.Fragment,null,(0,i.renderList)(e.data,(function(e){return(0,i.openBlock)(),(0,i.createElementBlock)("div",{class:(0,i.normalizeClass)(["GlobalRegionSelectItem",{"is-active":e.id===N.value}]),key:e.id,onClick:function(t){return T(e)}},[(0,i.createElementVNode)("div",{class:"GlobalRegionSelectIcon",innerHTML:m}),(0,i.createElementVNode)("div",S,(0,i.toDisplayString)(e.name),1)],10,E)})),128))])})),128))]})),_:1})):(0,i.createCommentVNode)("",!0),"tree"===G.value?((0,i.openBlock)(),(0,i.createElementBlock)("div",L,[(0,i.createElementVNode)("div",_,"上次登录地区："+(0,i.toDisplayString)(I.value.name),1),(0,i.createVNode)(l,{modelValue:R.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return R.value=e}),placeholder:"请输入地区名称",clearable:""},null,8,["modelValue"])])):(0,i.createCommentVNode)("",!0),"tree"===G.value?((0,i.openBlock)(),(0,i.createBlock)(r,{key:3,class:"GlobalRegionSelectTree"},{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(c,{ref_key:"treeRef",ref:B,"node-key":"id",data:p.value,"highlight-current":"",props:{label:"name",children:"children"},"filter-node-method":A,"default-expanded-keys":O.value,onNodeClick:T},null,8,["data","default-expanded-keys"])]})),_:1})):(0,i.createCommentVNode)("",!0)])):(0,i.createCommentVNode)("",!0)]})),_:1})])}}});const G=N;var C=G}}]);