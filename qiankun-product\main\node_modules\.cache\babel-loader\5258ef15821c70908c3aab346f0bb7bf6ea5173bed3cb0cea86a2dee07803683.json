{"ast": null, "code": "import { Close, Star } from '@element-plus/icons-vue';\nimport SatisfactionSurvey from './SatisfactionSurvey.vue';\nvar __default__ = {\n  name: 'CustomSatisfactionModal'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  emits: ['update:modelValue'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var handleClose = function handleClose() {\n      emit('update:modelValue', false);\n    };\n    var handleMaskClick = function handleMaskClick() {\n      emit('update:modelValue', false);\n    };\n    var handleCallback = function handleCallback() {\n      emit('update:modelValue', false);\n    };\n    var __returned__ = {\n      props,\n      emit,\n      handleClose,\n      handleMaskClick,\n      handleCallback,\n      get Close() {\n        return Close;\n      },\n      get Star() {\n        return Star;\n      },\n      SatisfactionSurvey\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["Close", "Star", "SatisfactionSurvey", "__default__", "name", "props", "__props", "emit", "__emit", "handleClose", "handleMaskClick", "handleCallback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalAiChat/CustomSatisfactionModal.vue"], "sourcesContent": ["<template>\r\n  <Teleport to=\"body\">\r\n    <Transition name=\"modal-fade\">\r\n      <div v-if=\"modelValue\" class=\"custom-satisfaction-modal\" @click=\"handleMaskClick\">\r\n        <div class=\"modal-container\" @click.stop>\r\n          <div class=\"modal-header\">\r\n            <div class=\"modal-title\">\r\n              <el-icon class=\"title-icon\">\r\n                <Star />\r\n              </el-icon>\r\n              满意度调查\r\n            </div>\r\n            <div class=\"modal-close\" @click=\"handleClose\">\r\n              <el-icon>\r\n                <Close />\r\n              </el-icon>\r\n            </div>\r\n          </div>\r\n          <div class=\"modal-body\">\r\n            <SatisfactionSurvey :data=\"data\" @callback=\"handleCallback\"></SatisfactionSurvey>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Transition>\r\n  </Teleport>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'CustomSatisfactionModal' }\r\n</script>\r\n\r\n<script setup>\r\nimport { Close, Star } from '@element-plus/icons-vue'\r\nimport SatisfactionSurvey from './SatisfactionSurvey.vue'\r\n\r\nconst props = defineProps({\r\n  modelValue: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  data: {\r\n    type: Object,\r\n    default: () => ({})\r\n  }\r\n})\r\n\r\nconst emit = defineEmits(['update:modelValue'])\r\n\r\nconst handleClose = () => {\r\n  emit('update:modelValue', false)\r\n}\r\n\r\nconst handleMaskClick = () => {\r\n  emit('update:modelValue', false)\r\n}\r\n\r\nconst handleCallback = () => {\r\n  emit('update:modelValue', false)\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.custom-satisfaction-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(4px);\r\n\r\n  .modal-container {\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);\r\n    max-width: 90vw;\r\n    max-height: 90vh;\r\n    overflow: hidden;\r\n    position: relative;\r\n    transform: scale(0.9);\r\n    opacity: 0;\r\n    animation: modalSlideIn 0.3s ease-out forwards;\r\n\r\n    .modal-header {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 20px 24px;\r\n      border-bottom: 1px solid #f0f0f0;\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      .modal-title {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        .title-icon {\r\n          font-size: 20px;\r\n          color: #ffd700;\r\n        }\r\n      }\r\n\r\n      .modal-close {\r\n        width: 36px;\r\n        height: 36px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        cursor: pointer;\r\n        transition: all 0.2s ease;\r\n        background: rgba(255, 255, 255, 0.1);\r\n\r\n        &:hover {\r\n          background: rgba(255, 255, 255, 0.2);\r\n          transform: scale(1.1);\r\n        }\r\n\r\n        .el-icon {\r\n          font-size: 18px;\r\n          color: white;\r\n        }\r\n      }\r\n    }\r\n\r\n    .modal-body {\r\n      max-height: calc(90vh - 100px);\r\n      overflow-y: auto;\r\n      \r\n      &::-webkit-scrollbar {\r\n        width: 6px;\r\n      }\r\n      \r\n      &::-webkit-scrollbar-track {\r\n        background: #f1f1f1;\r\n        border-radius: 3px;\r\n      }\r\n      \r\n      &::-webkit-scrollbar-thumb {\r\n        background: #c1c1c1;\r\n        border-radius: 3px;\r\n        \r\n        &:hover {\r\n          background: #a8a8a8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n  0% {\r\n    transform: scale(0.9) translateY(-20px);\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    transform: scale(1) translateY(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n// 过渡动画\r\n.modal-fade-enter-active,\r\n.modal-fade-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.modal-fade-enter-from,\r\n.modal-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.modal-fade-enter-from .modal-container,\r\n.modal-fade-leave-to .modal-container {\r\n  transform: scale(0.9) translateY(-20px);\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .custom-satisfaction-modal {\r\n    padding: 16px;\r\n    \r\n    .modal-container {\r\n      width: 100%;\r\n      max-width: 100%;\r\n      max-height: calc(100vh - 32px);\r\n      \r\n      .modal-header {\r\n        padding: 16px 20px;\r\n        \r\n        .modal-title {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n      \r\n      .modal-body {\r\n        max-height: calc(100vh - 132px);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .custom-satisfaction-modal {\r\n    padding: 8px;\r\n    \r\n    .modal-container {\r\n      .modal-header {\r\n        padding: 12px 16px;\r\n        \r\n        .modal-title {\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": "AAgCA,SAASA,KAAK,EAAEC,IAAI,QAAQ,yBAAyB;AACrD,OAAOC,kBAAkB,MAAM,0BAA0B;AALzD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAA0B,CAAC;;;;;;;;;;;;;;;;;;;IAOlD,IAAMC,KAAK,GAAGC,OASZ;IAEF,IAAMC,IAAI,GAAGC,MAAkC;IAE/C,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBF,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;IAClC,CAAC;IAED,IAAMG,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5BH,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;IAClC,CAAC;IAED,IAAMI,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3BJ,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;IAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}