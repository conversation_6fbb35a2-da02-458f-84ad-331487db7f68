"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[2726],{12726:function(e,t,r){r.r(t),r.d(t,{default:function(){return O}});var n=r(13776),o=(r(76945),r(77213),r(81474)),a=(r(64352),r(44863)),i=(r(4711),r(74061)),l=r(4955),u=r(24652);function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(e){f=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var a=t&&t.prototype instanceof y?t:y,i=Object.create(a.prototype),l=new P(n||[]);return o(i,"_invoke",{value:S(e,r,l)}),i}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var h="suspendedStart",v="suspendedYield",p="executing",g="completed",m={};function y(){}function b(){}function w(){}var x={};f(x,i,(function(){return this}));var E=Object.getPrototypeOf,C=E&&E(E(R([])));C&&C!==r&&n.call(C,i)&&(x=C);var A=w.prototype=y.prototype=Object.create(x);function L(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function r(o,a,i,l){var u=d(e[o],e,a);if("throw"!==u.type){var c=u.arg,f=c.value;return f&&"object"==typeof f&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(f).then((function(e){c.value=e,i(c)}),(function(e){return r("throw",e,i,l)}))}l(u.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function S(t,r,n){var o=h;return function(a,i){if(o===p)throw Error("Generator is already running");if(o===g){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var l=n.delegate;if(l){var u=k(l,n);if(u){if(u===m)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=p;var c=d(t,r,n);if("normal"===c.type){if(o=n.done?g:v,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=g,n.method="throw",n.arg=c.arg)}}}function k(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,k(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function R(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return b.prototype=w,o(A,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:b,configurable:!0}),b.displayName=f(w,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,f(e,u,"GeneratorFunction")),e.prototype=Object.create(A),e},t.awrap=function(e){return{__await:e}},L(N.prototype),f(N.prototype,l,(function(){return this})),t.AsyncIterator=N,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new N(s(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},L(A),f(A,u,"Generator"),f(A,i,(function(){return this})),f(A,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=R,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(j),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return l.type="throw",l.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var u=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),j(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:R(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){d(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(e,t,r){return(t=h(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e){var t=v(e,"string");return"symbol"==typeof t?t:t+""}function v(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function p(e){return b(e)||y(e)||m(e)||g()}function g(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"==typeof e)return w(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?w(e,t):void 0}}function y(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function b(e){if(Array.isArray(e))return w(e)}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function x(e,t,r,n,o,a,i){try{var l=e[a](i),u=l.value}catch(e){return void r(e)}l.done?t(u):Promise.resolve(u).then(n,o)}function E(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){x(a,n,o,i,l,"next",e)}function l(e){x(a,n,o,i,l,"throw",e)}i(void 0)}))}}var C={class:"GlobalAiChatEditorContainer"},A={class:"GlobalAiChatEditorBotton"},L={class:"GlobalAiChatEditorUpload"},N={class:"GlobalAiChatEditorFlex"},S={__name:"GlobalAiChatEditor",props:{modelValue:[String,Number],disabled:{type:Boolean,default:!1}},emits:["update:modelValue","send","stop","uploadCallback","fileCallback"],setup(e,t){var r=t.expose,f=t.emit,d=e,h=(0,i.ref)([]),v=(0,i.ref)([]),g=(0,i.computed)((function(){return d.disabled})),m=f,y=(0,i.ref)(null),b=(0,i.computed)({get(){return d.modelValue},set(e){m("update:modelValue",e)}}),w="",x=function(){var e=navigator.userAgent.toLowerCase();return e.includes("macintosh")||e.includes("mac os x")},S=function(){b.value.replace(/^\s+|\s+$/g,"")&&(m("send",b.value),y.value.innerHTML="",R())},k=function(){m("stop",b.value)};(0,i.onMounted)((function(){var e=y.value;e&&(e.innerHTML="",R())}));var O=function(){var e=window.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0),r=t.startContainer;while(r&&r!==y.value){if(r.classList&&r.classList.contains("AiChatEditorPlaceholder"))return!0;r=r.parentNode}return!1},j=function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r];if(n.type){var o=document.createElement("span");o.className="AiChatEditorPlaceholderContainer";var a=document.createElement("span");a.className="AiChatEditorPlaceholder",a.contentEditable="false",a.textContent=n.value,a.dataset.id=H(),o.appendChild(a),t.push(o)}else t.push(document.createTextNode(n.value))}return t},P=function(e){if(e.length){var t=y.value;if(t&&(t.focus(),!O())){var r=window.getSelection();if(r.rangeCount){var n=r.getRangeAt(0);n.collapsed||n.deleteContents();for(var o=null,a=j(e),i=a.length-1;i>=0;i--){var l=a[i];n.insertNode(l),i===a.length-1&&(o=l)}var u=document.createElement("br");n.setStartAfter(o),n.setEndAfter(o),n.insertNode(u);var c=document.createRange();c.setStartAfter(u),c.setEndAfter(u),r.removeAllRanges(),r.addRange(c),t.focus(),R()}}}},R=function(){var e,t=y.value;t&&(b.value=(null===(e=t.innerHTML)||void 0===e||null===(e=e.replace(/<br>/g,"\n"))||void 0===e?void 0:e.replace(/<[^>]*>/g,""))||"")},_=function(e){var t=e.target;if(t.classList.contains("AiChatEditorPlaceholder")){var r=t.getAttribute("data-id");if(w!==r){w=r,t.contentEditable="true",t.focus();var n=document.createRange();n.selectNodeContents(t);var o=window.getSelection();o.removeAllRanges(),o.addRange(n)}else{var a=T(t,e),i=window.getSelection(),l=document.createRange();l.setStart(t.firstChild,a),l.setEnd(t.firstChild,a),i.removeAllRanges(),i.addRange(l)}}else w=""},T=function(e,t){var r=e.firstChild,n=document.createRange();n.selectNodeContents(r);for(var o=n.getClientRects(),a=t.clientX,i=0;i<o.length;i++){var l=o[i];if(a>=l.left&&a<=l.right){var u=a-l.left,c=l.width/r.length;return Math.min(Math.floor(u/c),r.length)}}return r.length},G=function(e){var t=e.lastElementChild;return t&&t.tagName&&"br"===t.tagName.toLowerCase()},V=function(e){var t=e.target;if(13==e.keyCode)if(e.preventDefault(),e.ctrlKey||e.metaKey){var r=window.getSelection();if(r.rangeCount>0){var n=r.getRangeAt(0),o=n.cloneRange();o.selectNodeContents(t),o.setEnd(n.endContainer,n.endOffset);var a=o.toString(),i=a.length===y.value.textContent.length,l=document.createElement("br"),u=document.createElement("br");n.deleteContents(),i&&!G(t)&&n.insertNode(u),n.insertNode(l),n.setStartAfter(l),n.setEndAfter(l),r.removeAllRanges(),r.addRange(n),R()}}else S()},D=function(e){e.preventDefault();var t=(e.clipboardData||window.clipboardData).getData("text/plain"),r=window.getSelection();if(r.rangeCount){var n=r.getRangeAt(0);n.deleteContents();var o=document.createTextNode(t);n.insertNode(o);var a=document.createRange();a.setStartAfter(o),a.setEndAfter(o),r.removeAllRanges(),r.addRange(a),R()}},M=function(e){v.value=e,m("fileCallback",v.value)},I=function(e){y.value.innerHTML=e,R()},F=function(e){y.value.innerHTML=y.value.innerHTML+((null===e||void 0===e?void 0:e.replace(/<[^>]*>/g,""))||""),R()},B=function(){return!0},H=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,r="x"==e?t:3&t|8;return r.toString(16)}))},K=function(e,t){var r;if(null!==e&&void 0!==e&&null!==(r=e.event)&&void 0!==r&&r.lengthComputable){var n=(e.loaded/e.total*100).toFixed(0);h.value.forEach((function(e){e.uid===t&&(e.progress=parseInt(n))}))}},U=function(e){var t=new FormData;t.append("file",e.file),z(t,H(),e.file.name,e.file.uid,e.file.size)},z=function(){var e=E(c().mark((function e(t,r,n,o,a){var i,u,f,d,g,y,b,w;return c().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,i=n.substring(n.lastIndexOf(".")+1),h.value.push({uid:r,fileName:n,fileType:i,fileSize:a,progress:0}),m("uploadCallback",h.value),e.next=6,l.A.globalUpload(t,K,r);case 6:for(u=e.sent,f=u.data,h.value=h.value.filter((function(e){return e.uid!==r})),m("uploadCallback",h.value),d=[],g=[],y=[].concat(p(v.value),[s(s({},f),{},{uid:r,time:o,progress:100})]),b=0;b<y.length;b++)w=y[b],w.time?g.push(w):d.push(w);v.value=[].concat(d,p(g.sort((function(e,t){return e.time-t.time})))),m("fileCallback",v.value),e.next=22;break;case 18:e.prev=18,e.t0=e["catch"](0),h.value=h.value.filter((function(e){return e.uid!==r})),m("uploadCallback",h.value);case 22:case"end":return e.stop()}}),e,null,[[0,18]])})));return function(t,r,n,o,a){return e.apply(this,arguments)}}();return r({editorRef:y.value,handleSetFile:M,handleSetContent:I,handleAddContent:F,handleInsertPlaceholder:P}),function(e,t){var r=a.kA,l=o.S2,c=n.j5;return(0,i.openBlock)(),(0,i.createElementBlock)("div",C,[(0,i.createVNode)(r,{always:"",class:"GlobalAiChatEditorScroll"},{default:(0,i.withCtx)((function(){return[(0,i.createElementVNode)("div",{ref_key:"editorRef",ref:y,contenteditable:"true",class:"GlobalAiChatEditor",onInput:R,onClick:_,onKeydown:V,onPaste:D},null,544)]})),_:1}),(0,i.createElementVNode)("div",A,[(0,i.createElementVNode)("div",L,[(0,i.createVNode)(c,{action:"/","before-upload":B,"http-request":U,"show-file-list":!1,multiple:""},{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(l,{icon:(0,i.unref)(u.DocumentAdd)},{default:(0,i.withCtx)((function(){return t[2]||(t[2]=[(0,i.createTextVNode)("上传资料")])})),_:1},8,["icon"])]})),_:1})]),(0,i.createElementVNode)("div",N,[(0,i.createElementVNode)("span",null,(0,i.toDisplayString)(x()?"Command + Enter 换行":"Ctrl + Enter 换行"),1),g.value?(0,i.createCommentVNode)("",!0):((0,i.openBlock)(),(0,i.createBlock)(l,{key:0,type:"primary",icon:(0,i.unref)(u.Position),circle:"",onClick:t[0]||(t[0]=function(e){return S()})},null,8,["icon"])),g.value?((0,i.openBlock)(),(0,i.createBlock)(l,{key:1,type:"primary",circle:"",onClick:t[1]||(t[1]=function(e){return k()})},{default:(0,i.withCtx)((function(){return t[3]||(t[3]=[(0,i.createElementVNode)("span",{class:"GlobalAiChatEditorStopStream"},null,-1)])})),_:1})):(0,i.createCommentVNode)("",!0)])])])}}};const k=S;var O=k}}]);