{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_badge = _resolveComponent(\"el-badge\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"GlobalChatFloating forbidSelect\",\n    ref: \"elContainer\",\n    onMousedown: $setup.handleMousedown,\n    onMouseup: $setup.handleMouseup\n  }, [_createVNode(_component_el_popover, {\n    visible: $setup.elShow,\n    placement: \"left\",\n    \"popper-class\": \"GlobalChatFloatingPopover\"\n  }, {\n    reference: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        class: _normalizeClass(['GlobalChatFloatingBody', $setup.isActiveType ? 'is-left ' : 'is-right', {\n          'is-active': $setup.isActive\n        }]),\n        onClick: $setup.handleClick\n      }, [_createVNode(_component_el_badge, {\n        value: $setup.chatTotal,\n        max: 99,\n        hidden: !$setup.chatTotal,\n        offset: $setup.offset\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_image, {\n            src: $setup.systemChatIcon,\n            loading: \"lazy\",\n            fit: \"cover\",\n            draggable: \"false\"\n          }, null, 8 /* PROPS */, [\"src\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"value\", \"hidden\", \"offset\"])], 2 /* CLASS */)];\n    }),\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"GlobalChat\"], {\n        onCallback: $setup.callback\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"visible\"])], 544 /* NEED_HYDRATION, NEED_PATCH */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "class", "ref", "onMousedown", "$setup", "handleMousedown", "onMouseup", "handleMouseup", "_createVNode", "_component_el_popover", "visible", "elShow", "placement", "reference", "_withCtx", "_createElementVNode", "_normalizeClass", "isActiveType", "isActive", "onClick", "handleClick", "_component_el_badge", "value", "chatTotal", "max", "hidden", "offset", "default", "_component_el_image", "src", "systemChatIcon", "loading", "fit", "draggable", "_", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\components\\GlobalChatFloating.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatFloating forbidSelect\" ref=\"elContainer\" @mousedown=\"handleMousedown\" @mouseup=\"handleMouseup\">\r\n    <el-popover :visible=\"elShow\" placement=\"left\" popper-class=\"GlobalChatFloatingPopover\">\r\n      <template #reference>\r\n        <div :class=\"['GlobalChatFloatingBody', isActiveType ? 'is-left ' : 'is-right', { 'is-active': isActive }]\"\r\n          @click=\"handleClick\">\r\n          <el-badge :value=\"chatTotal\" :max=\"99\" :hidden=\"!chatTotal\" :offset=\"offset\">\r\n            <el-image :src=\"systemChatIcon\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" />\r\n          </el-badge>\r\n        </div>\r\n      </template>\r\n      <GlobalChat @callback=\"callback\"></GlobalChat>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatFloating' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted } from 'vue'\r\nimport { systemChatIcon } from 'common/js/system_var.js'\r\nimport GlobalChat from '../../GlobalChat/GlobalChat.vue'\r\nconst elContainer = ref()\r\nconst isDragging = ref(false)\r\nconst positionX = ref(window.innerWidth - 100)\r\nconst positionY = ref(168)\r\nconst startPosX = ref(0)\r\nconst startPosY = ref(0)\r\nconst elShow = ref(false)\r\nconst isActive = ref(false)\r\nconst offset = ref([0, 0])\r\nconst chatTotal = ref(0)\r\nconst isActiveType = ref(false)\r\nconst callback = (value) => {\r\n  if (value < 10) {\r\n    offset.value = [0, 0]\r\n  } else if (value < 100) {\r\n    offset.value = [-4, 0]\r\n  } else {\r\n    offset.value = [-8, 0]\r\n  }\r\n  chatTotal.value = value\r\n}\r\nconst handleClick = () => {\r\n  if (!isDragging.value && !isActive.value) elShow.value = !elShow.value\r\n  isActive.value = false\r\n}\r\nconst handleMousedown = (event) => {\r\n  isDragging.value = true\r\n  startPosX.value = event.clientX - positionX.value\r\n  startPosY.value = event.clientY - positionY.value\r\n  document.addEventListener('mousemove', handleMousemove)\r\n  document.addEventListener('mouseup', handleMouseup)\r\n}\r\nconst handleMouseup = () => {\r\n  if (isDragging.value) {\r\n    isDragging.value = false\r\n    document.removeEventListener('mousemove', handleMousemove)\r\n    document.removeEventListener('mouseup', handleMouseup)\r\n    handleAutoSnap()\r\n  }\r\n}\r\nconst handleMousemove = (event) => {\r\n  if (isDragging.value) {\r\n    elShow.value = false\r\n    isActive.value = true\r\n    // requestAnimationFrame(() => {    \r\n    const newX = event.clientX - startPosX.value\r\n    const newY = event.clientY - startPosY.value\r\n    const windowWidth = window.innerWidth\r\n    const windowHeight = window.innerHeight\r\n    const elWidth = elContainer.value.offsetWidth\r\n    const elHeight = elContainer.value.offsetHeight\r\n    positionX.value = Math.max(0, Math.min(windowWidth - elWidth, newX))\r\n    positionY.value = Math.max(0, Math.min(windowHeight - elHeight, newY))\r\n    elContainer.value.style.top = positionY.value + 'px'\r\n    elContainer.value.style.left = positionX.value + 'px'\r\n    // })\r\n  }\r\n}\r\n// 自动吸附到最近的侧边\r\nconst handleAutoSnap = () => {\r\n  const windowWidth = window.innerWidth\r\n  const elWidth = elContainer.value.offsetWidth\r\n  if (positionX.value + elWidth / 2 < windowWidth / 2) {\r\n    positionX.value = 0\r\n    isActiveType.value = true\r\n  } else {\r\n    isActiveType.value = false\r\n    positionX.value = windowWidth - elWidth\r\n  }\r\n  elContainer.value.style.top = positionY.value + 'px'\r\n  elContainer.value.style.left = positionX.value + 'px'\r\n}\r\nonMounted(() => {\r\n  positionX.value = window.innerWidth - elContainer.value.offsetWidth\r\n  elContainer.value.style.top = positionY.value + 'px'\r\n  elContainer.value.style.left = positionX.value + 'px'\r\n  window.addEventListener('resize', handleAutoSnap)\r\n})\r\nonUnmounted(() => {\r\n  window.removeEventListener('resize', handleAutoSnap)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalChatFloating {\r\n  width: 66px;\r\n  height: 52px;\r\n  position: fixed;\r\n  cursor: pointer;\r\n  z-index: 99;\r\n\r\n  .GlobalChatFloatingBody {\r\n    width: 66px;\r\n    height: 52px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding-top: 6px;\r\n    padding-left: 12px;\r\n    background: #fff;\r\n    box-shadow: var(--zy-el-box-shadow-light);\r\n    overflow: hidden;\r\n\r\n    &.is-left {\r\n      padding-left: 2px;\r\n      border-radius: 0 26px 26px 0;\r\n    }\r\n\r\n    &.is-right {\r\n      border-radius: 26px 0 0 26px;\r\n    }\r\n\r\n    &.is-active {\r\n      padding: 0;\r\n      padding-top: 6px;\r\n      padding-left: 12px;\r\n      border-radius: 28px;\r\n    }\r\n\r\n    .zy-el-badge {\r\n      width: 38px;\r\n      height: 38px;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatFloatingPopover {\r\n  width: 880px !important;\r\n  height: 580px !important;\r\n  padding: 0 !important;\r\n\r\n  .GlobalChatBox {\r\n\r\n    .GlobalChat {\r\n      width: 880px;\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;uBACEA,mBAAA,CAYM;IAZDC,KAAK,EAAC,iCAAiC;IAACC,GAAG,EAAC,aAAa;IAAEC,WAAS,EAAEC,MAAA,CAAAC,eAAe;IAAGC,SAAO,EAAEF,MAAA,CAAAG;MACpGC,YAAA,CAUaC,qBAAA;IAVAC,OAAO,EAAEN,MAAA,CAAAO,MAAM;IAAEC,SAAS,EAAC,MAAM;IAAC,cAAY,EAAC;;IAC/CC,SAAS,EAAAC,QAAA,CAClB;MAAA,OAKM,CALNC,mBAAA,CAKM;QALAd,KAAK,EAJnBe,eAAA,4BAIgDZ,MAAA,CAAAa,YAAY;UAAA,aAA2Cb,MAAA,CAAAc;QAAQ;QACpGC,OAAK,EAAEf,MAAA,CAAAgB;UACRZ,YAAA,CAEWa,mBAAA;QAFAC,KAAK,EAAElB,MAAA,CAAAmB,SAAS;QAAGC,GAAG,EAAE,EAAE;QAAGC,MAAM,GAAGrB,MAAA,CAAAmB,SAAS;QAAGG,MAAM,EAAEtB,MAAA,CAAAsB;;QAN/EC,OAAA,EAAAb,QAAA,CAOY;UAAA,OAA+E,CAA/EN,YAAA,CAA+EoB,mBAAA;YAApEC,GAAG,EAAEzB,MAAA,CAAA0B,cAAc;YAAEC,OAAO,EAAC,MAAM;YAACC,GAAG,EAAC,OAAO;YAACC,SAAS,EAAC;;;QAPjFC,CAAA;;;IAAAP,OAAA,EAAAb,QAAA,CAWM;MAAA,OAA8C,CAA9CN,YAAA,CAA8CJ,MAAA;QAAjC+B,UAAQ,EAAE/B,MAAA,CAAAgC;MAAQ,G;;IAXrCF,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}