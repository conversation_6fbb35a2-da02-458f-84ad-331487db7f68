{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';\nimport { size2Str } from 'common/js/utils.js';\nimport { globalFileLocation } from 'common/config/location';\nimport elementResizeDetectorMaker from 'element-resize-detector';\nvar __default__ = {\n  name: 'GlobalAiChatFile'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    fileList: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    fileData: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    distance: {\n      type: Number,\n      default: 168\n    }\n  },\n  emits: ['close'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var erd = elementResizeDetectorMaker();\n    var props = __props;\n    var emit = __emit;\n    var fileList = computed(function () {\n      return props.fileList;\n    });\n    var fileData = computed(function () {\n      return props.fileData;\n    });\n    var fileIcon = function fileIcon(fileType) {\n      var IconClass = {\n        docx: 'globalFileWord',\n        doc: 'globalFileWord',\n        wps: 'globalFileWPS',\n        xlsx: 'globalFileExcel',\n        xls: 'globalFileExcel',\n        pdf: 'globalFilePDF',\n        pptx: 'globalFilePPT',\n        ppt: 'globalFilePPT',\n        txt: 'globalFileTXT',\n        jpg: 'globalFilePicture',\n        png: 'globalFilePicture',\n        gif: 'globalFilePicture',\n        avi: 'globalFileVideo',\n        mp4: 'globalFileVideo',\n        zip: 'globalFileCompress',\n        rar: 'globalFileCompress'\n      };\n      return IconClass[fileType] || 'globalFileUnknown';\n    };\n    var scrollStyle = computed(function () {\n      return {\n        transform: `translateX(${scrollLeft.value}px)`\n      };\n    });\n    var tag = ref();\n    var scroll = ref();\n    var prevShow = ref(false);\n    var nextShow = ref(false);\n    var scrollLeft = ref(0);\n    var scrollClick = function scrollClick(type) {\n      var left = tag.value.offsetWidth - scroll.value.scrollWidth;\n      if (type === 'prev') {\n        scrollLeft.value = scrollLeft.value + props.distance > 0 ? 0 : scrollLeft.value + props.distance;\n      } else if (type === 'next') {\n        scrollLeft.value = scrollLeft.value - props.distance < left ? left : scrollLeft.value - props.distance;\n      }\n      delay(function () {\n        prevShow.value = scrollLeft.value !== 0;\n        nextShow.value = scrollLeft.value !== left;\n      }, 520);\n    };\n    var delay = function () {\n      var timer = 0;\n      return function (callback, ms) {\n        clearTimeout(timer);\n        timer = setTimeout(callback, ms);\n      };\n    }();\n    var handleClose = function handleClose(item) {\n      emit('close', item);\n    };\n    var handleResizeWindow = function handleResizeWindow() {\n      nextTick(function () {\n        if (tag.value.offsetWidth < scroll.value.offsetWidth) {\n          nextShow.value = true;\n        }\n      });\n    };\n    var handlePreview = function handlePreview(row) {\n      globalFileLocation({\n        name: process.env.VUE_APP_NAME,\n        fileId: row.id,\n        fileType: row.extName,\n        fileName: row.originalFileName,\n        fileSize: row.fileSize\n      });\n    };\n    onMounted(function () {\n      nextTick(function () {\n        erd.listenTo(tag.value, function () {\n          handleResizeWindow();\n        });\n      });\n      nextTick(function () {\n        erd.listenTo(scroll.value, function () {\n          handleResizeWindow();\n        });\n      });\n    });\n    onUnmounted(function () {\n      erd.uninstall(tag.value);\n      erd.uninstall(scroll.value);\n    });\n    var __returned__ = {\n      erd,\n      props,\n      emit,\n      fileList,\n      fileData,\n      fileIcon,\n      scrollStyle,\n      tag,\n      scroll,\n      prevShow,\n      nextShow,\n      scrollLeft,\n      scrollClick,\n      delay,\n      handleClose,\n      handleResizeWindow,\n      handlePreview,\n      ref,\n      computed,\n      onMounted,\n      onUnmounted,\n      nextTick,\n      get size2Str() {\n        return size2Str;\n      },\n      get globalFileLocation() {\n        return globalFileLocation;\n      },\n      get elementResizeDetectorMaker() {\n        return elementResizeDetectorMaker;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "nextTick", "size2Str", "globalFileLocation", "elementResizeDetectorMaker", "__default__", "name", "erd", "props", "__props", "emit", "__emit", "fileList", "fileData", "fileIcon", "fileType", "IconClass", "docx", "doc", "wps", "xlsx", "xls", "pdf", "pptx", "ppt", "txt", "jpg", "png", "gif", "avi", "mp4", "zip", "rar", "scrollStyle", "transform", "scrollLeft", "value", "tag", "scroll", "prevShow", "nextShow", "scrollClick", "type", "left", "offsetWidth", "scrollWidth", "distance", "delay", "timer", "callback", "ms", "clearTimeout", "setTimeout", "handleClose", "item", "handleResizeWindow", "handlePreview", "row", "process", "env", "VUE_APP_NAME", "fileId", "id", "extName", "fileName", "originalFileName", "fileSize", "listenTo", "uninstall"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalAiChat/GlobalAiChatFile.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChatFile\" ref=\"tag\">\r\n    <div class=\"GlobalAiChatFileWrap\">\r\n      <div class=\"GlobalAiChatFileScroll\" :style=\"scrollStyle\" ref=\"scroll\">\r\n        <div class=\"GlobalAiChatFileItem\" v-for=\"item in fileData\" :key=\"item.id\" @click=\"handlePreview(item)\">\r\n          <div class=\"GlobalAiChatFileItemClose\" @click=\"handleClose(item)\">\r\n            <el-icon>\r\n              <CircleCloseFilled />\r\n            </el-icon>\r\n          </div>\r\n          <div class=\"globalFileIcon\" :class=\"fileIcon(item?.extName)\"></div>\r\n          <div class=\"GlobalChatMessagesFileName ellipsis\">{{ item?.originalFileName || '未知文件' }}</div>\r\n          <div class=\"GlobalChatMessagesFileSize\">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>\r\n        </div>\r\n        <div class=\"GlobalAiChatFileItem\" v-for=\"item in fileList\" :key=\"item.uid\">\r\n          <div class=\"globalFileIcon\" :class=\"fileIcon(item?.fileType)\"></div>\r\n          <div class=\"GlobalChatMessagesFileName ellipsis\">{{ item?.fileName || '未知文件' }}</div>\r\n          <div class=\"GlobalChatMessagesFileSize\">{{ item?.fileSize ? size2Str(item?.fileSize) : '0KB' }}</div>\r\n          <el-progress :percentage=\"item.progress\" :show-text=\"false\" :stroke-width=\"2\" />\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalAiChatFilePrev\" v-if=\"prevShow\" @click=\"scrollClick('prev')\">\r\n        <el-icon>\r\n          <DArrowLeft />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"GlobalAiChatFileNext\" v-if=\"nextShow\" @click=\"scrollClick('next')\">\r\n        <el-icon>\r\n          <DArrowRight />\r\n        </el-icon>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChatFile' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'\r\nimport { size2Str } from 'common/js/utils.js'\r\nimport { globalFileLocation } from 'common/config/location'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst erd = elementResizeDetectorMaker()\r\nconst props = defineProps({\r\n  fileList: { type: Array, default: () => [] },\r\n  fileData: { type: Array, default: () => [] },\r\n  distance: { type: Number, default: 168 }\r\n})\r\nconst emit = defineEmits(['close'])\r\nconst fileList = computed(() => props.fileList)\r\nconst fileData = computed(() => props.fileData)\r\nconst fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nconst scrollStyle = computed(() => ({ transform: `translateX(${scrollLeft.value}px)` }))\r\nconst tag = ref()\r\nconst scroll = ref()\r\nconst prevShow = ref(false)\r\nconst nextShow = ref(false)\r\nconst scrollLeft = ref(0)\r\nconst scrollClick = (type) => {\r\n  const left = tag.value.offsetWidth - scroll.value.scrollWidth\r\n  if (type === 'prev') {\r\n    scrollLeft.value = scrollLeft.value + props.distance > 0 ? 0 : scrollLeft.value + props.distance\r\n  } else if (type === 'next') {\r\n    scrollLeft.value = scrollLeft.value - props.distance < left ? left : scrollLeft.value - props.distance\r\n  }\r\n  delay(() => {\r\n    prevShow.value = scrollLeft.value !== 0\r\n    nextShow.value = scrollLeft.value !== left\r\n  }, 520)\r\n}\r\nconst delay = (() => {\r\n  let timer = 0\r\n  return (callback, ms) => {\r\n    clearTimeout(timer)\r\n    timer = setTimeout(callback, ms)\r\n  }\r\n})()\r\nconst handleClose = (item) => {\r\n  emit('close', item)\r\n}\r\nconst handleResizeWindow = () => {\r\n  nextTick(() => {\r\n    if (tag.value.offsetWidth < scroll.value.offsetWidth) {\r\n      nextShow.value = true\r\n    }\r\n  })\r\n}\r\nconst handlePreview = (row) => {\r\n  globalFileLocation({\r\n    name: process.env.VUE_APP_NAME,\r\n    fileId: row.id,\r\n    fileType: row.extName,\r\n    fileName: row.originalFileName,\r\n    fileSize: row.fileSize\r\n  })\r\n}\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    erd.listenTo(tag.value, () => {\r\n      handleResizeWindow()\r\n    })\r\n  })\r\n  nextTick(() => {\r\n    erd.listenTo(scroll.value, () => {\r\n      handleResizeWindow()\r\n    })\r\n  })\r\n})\r\nonUnmounted(() => {\r\n  erd.uninstall(tag.value)\r\n  erd.uninstall(scroll.value)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiChatFile {\r\n  width: 100%;\r\n  background-color: #fff;\r\n  position: relative;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n\r\n  .GlobalAiChatFileWrap {\r\n    width: 100%;\r\n    overflow: hidden;\r\n\r\n    .GlobalAiChatFilePrev,\r\n    .GlobalAiChatFileNext {\r\n      position: absolute;\r\n      top: 0;\r\n      height: 100%;\r\n      width: 22px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background-color: #fff;\r\n      cursor: pointer;\r\n      z-index: 9;\r\n    }\r\n\r\n    .GlobalAiChatFilePrev {\r\n      left: 0;\r\n    }\r\n\r\n    .GlobalAiChatFileNext {\r\n      right: 0;\r\n    }\r\n\r\n    .GlobalAiChatFileScroll {\r\n      padding: 6px 12px;\r\n      white-space: nowrap;\r\n      position: relative;\r\n      transition: transform 0.3s;\r\n      float: left;\r\n      display: block;\r\n      z-index: 3;\r\n\r\n      .GlobalAiChatFileItem {\r\n        width: 180px;\r\n        height: 52px;\r\n        display: inline-flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        background: #fff;\r\n        position: relative;\r\n        padding: 0 40px 0 12px;\r\n        border-radius: var(--el-border-radius-base);\r\n        border: 1px solid var(--zy-el-border-color-light);\r\n        background: var(--zy-el-color-info-light-9);\r\n        word-wrap: break-word;\r\n        white-space: pre-wrap;\r\n        cursor: pointer;\r\n\r\n        & + .GlobalAiChatFileItem {\r\n          margin-left: 12px;\r\n        }\r\n\r\n        &:hover {\r\n          .GlobalAiChatFileItemClose {\r\n            display: inline-block;\r\n          }\r\n        }\r\n\r\n        .GlobalAiChatFileItemClose {\r\n          width: 14px;\r\n          height: 14px;\r\n          font-size: 14px;\r\n          display: none;\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          transform: translate(50%, -50%);\r\n        }\r\n\r\n        .zy-el-progress {\r\n          position: absolute;\r\n          left: 50%;\r\n          bottom: 0;\r\n          width: 100%;\r\n          transform: translateX(-50%);\r\n        }\r\n\r\n        .GlobalChatMessagesFileName {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding-bottom: 2px;\r\n        }\r\n\r\n        .GlobalChatMessagesFileSize {\r\n          color: var(--zy-el-text-color-secondary);\r\n          font-size: calc(var(--zy-text-font-size) - 2px);\r\n        }\r\n\r\n        .globalFileIcon {\r\n          width: 28px;\r\n          height: 28px;\r\n          vertical-align: middle;\r\n          position: absolute;\r\n          top: 50%;\r\n          right: 6px;\r\n          transform: translateY(-50%);\r\n        }\r\n\r\n        .globalFileUnknown {\r\n          background: url('./img/unknown.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePDF {\r\n          background: url('./img/PDF.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWord {\r\n          background: url('./img/Word.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileExcel {\r\n          background: url('./img/Excel.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePicture {\r\n          background: url('./img/picture.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileVideo {\r\n          background: url('./img/video.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileTXT {\r\n          background: url('./img/TXT.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileCompress {\r\n          background: url('./img/compress.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFileWPS {\r\n          background: url('./img/WPS.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n\r\n        .globalFilePPT {\r\n          background: url('./img/PPT.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          background-position: center;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAsCA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,KAAK;AACrE,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,OAAOC,0BAA0B,MAAM,yBAAyB;AANhE,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAmB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;IAO3C,IAAMC,GAAG,GAAGH,0BAA0B,CAAC,CAAC;IACxC,IAAMI,KAAK,GAAGC,OAIZ;IACF,IAAMC,IAAI,GAAGC,MAAsB;IACnC,IAAMC,QAAQ,GAAGd,QAAQ,CAAC;MAAA,OAAMU,KAAK,CAACI,QAAQ;IAAA,EAAC;IAC/C,IAAMC,QAAQ,GAAGf,QAAQ,CAAC;MAAA,OAAMU,KAAK,CAACK,QAAQ;IAAA,EAAC;IAC/C,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,QAAQ,EAAK;MAC7B,IAAMC,SAAS,GAAG;QAChBC,IAAI,EAAE,gBAAgB;QACtBC,GAAG,EAAE,gBAAgB;QACrBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,iBAAiB;QACvBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,eAAe;QACrBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,eAAe;QACpBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,mBAAmB;QACxBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,iBAAiB;QACtBC,GAAG,EAAE,oBAAoB;QACzBC,GAAG,EAAE;MACP,CAAC;MACD,OAAOhB,SAAS,CAACD,QAAQ,CAAC,IAAI,mBAAmB;IACnD,CAAC;IACD,IAAMkB,WAAW,GAAGnC,QAAQ,CAAC;MAAA,OAAO;QAAEoC,SAAS,EAAE,cAAcC,UAAU,CAACC,KAAK;MAAM,CAAC;IAAA,CAAC,CAAC;IACxF,IAAMC,GAAG,GAAGxC,GAAG,CAAC,CAAC;IACjB,IAAMyC,MAAM,GAAGzC,GAAG,CAAC,CAAC;IACpB,IAAM0C,QAAQ,GAAG1C,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAM2C,QAAQ,GAAG3C,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMsC,UAAU,GAAGtC,GAAG,CAAC,CAAC,CAAC;IACzB,IAAM4C,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAC5B,IAAMC,IAAI,GAAGN,GAAG,CAACD,KAAK,CAACQ,WAAW,GAAGN,MAAM,CAACF,KAAK,CAACS,WAAW;MAC7D,IAAIH,IAAI,KAAK,MAAM,EAAE;QACnBP,UAAU,CAACC,KAAK,GAAGD,UAAU,CAACC,KAAK,GAAG5B,KAAK,CAACsC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGX,UAAU,CAACC,KAAK,GAAG5B,KAAK,CAACsC,QAAQ;MAClG,CAAC,MAAM,IAAIJ,IAAI,KAAK,MAAM,EAAE;QAC1BP,UAAU,CAACC,KAAK,GAAGD,UAAU,CAACC,KAAK,GAAG5B,KAAK,CAACsC,QAAQ,GAAGH,IAAI,GAAGA,IAAI,GAAGR,UAAU,CAACC,KAAK,GAAG5B,KAAK,CAACsC,QAAQ;MACxG;MACAC,KAAK,CAAC,YAAM;QACVR,QAAQ,CAACH,KAAK,GAAGD,UAAU,CAACC,KAAK,KAAK,CAAC;QACvCI,QAAQ,CAACJ,KAAK,GAAGD,UAAU,CAACC,KAAK,KAAKO,IAAI;MAC5C,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IACD,IAAMI,KAAK,GAAI,YAAM;MACnB,IAAIC,KAAK,GAAG,CAAC;MACb,OAAO,UAACC,QAAQ,EAAEC,EAAE,EAAK;QACvBC,YAAY,CAACH,KAAK,CAAC;QACnBA,KAAK,GAAGI,UAAU,CAACH,QAAQ,EAAEC,EAAE,CAAC;MAClC,CAAC;IACH,CAAC,CAAE,CAAC;IACJ,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAIC,IAAI,EAAK;MAC5B5C,IAAI,CAAC,OAAO,EAAE4C,IAAI,CAAC;IACrB,CAAC;IACD,IAAMC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/BtD,QAAQ,CAAC,YAAM;QACb,IAAIoC,GAAG,CAACD,KAAK,CAACQ,WAAW,GAAGN,MAAM,CAACF,KAAK,CAACQ,WAAW,EAAE;UACpDJ,QAAQ,CAACJ,KAAK,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAMoB,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAK;MAC7BtD,kBAAkB,CAAC;QACjBG,IAAI,EAAEoD,OAAO,CAACC,GAAG,CAACC,YAAY;QAC9BC,MAAM,EAAEJ,GAAG,CAACK,EAAE;QACd/C,QAAQ,EAAE0C,GAAG,CAACM,OAAO;QACrBC,QAAQ,EAAEP,GAAG,CAACQ,gBAAgB;QAC9BC,QAAQ,EAAET,GAAG,CAACS;MAChB,CAAC,CAAC;IACJ,CAAC;IACDnE,SAAS,CAAC,YAAM;MACdE,QAAQ,CAAC,YAAM;QACbM,GAAG,CAAC4D,QAAQ,CAAC9B,GAAG,CAACD,KAAK,EAAE,YAAM;UAC5BmB,kBAAkB,CAAC,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;MACFtD,QAAQ,CAAC,YAAM;QACbM,GAAG,CAAC4D,QAAQ,CAAC7B,MAAM,CAACF,KAAK,EAAE,YAAM;UAC/BmB,kBAAkB,CAAC,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACFvD,WAAW,CAAC,YAAM;MAChBO,GAAG,CAAC6D,SAAS,CAAC/B,GAAG,CAACD,KAAK,CAAC;MACxB7B,GAAG,CAAC6D,SAAS,CAAC9B,MAAM,CAACF,KAAK,CAAC;IAC7B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}