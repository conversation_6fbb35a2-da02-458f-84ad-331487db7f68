{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AiReportGeneraDetails\"\n};\nvar _hoisted_2 = {\n  class: \"AiReportGeneraDetailsName\"\n};\nvar _hoisted_3 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, _toDisplayString($setup.details.reportTypeName), 1 /* TEXT */), _createElementVNode(\"div\", {\n    class: \"AiReportGeneraDetailsContent\",\n    innerHTML: $setup.details.content\n  }, null, 8 /* PROPS */, _hoisted_3)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_toDisplayString", "$setup", "details", "reportTypeName", "innerHTML", "content", "_hoisted_3"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiReportGenera\\component\\AiReportGeneraDetails.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AiReportGeneraDetails\">\r\n    <div class=\"AiReportGeneraDetailsName\">{{ details.reportTypeName }}</div>\r\n    <div class=\"AiReportGeneraDetailsContent\" v-html=\"details.content\"></div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AiReportGeneraDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst details = ref({})\r\n\r\nonMounted(() => { aigptReportRecordInfo() })\r\n\r\nconst aigptReportRecordInfo = async () => {\r\n  const { data } = await api.aigptReportRecordInfo({ detailId: props.id })\r\n  details.value = data\r\n}  \r\n</script>\r\n<style lang=\"scss\">\r\n.AiReportGeneraDetails {\r\n  width: 990px;\r\n  padding: 20px 40px;\r\n\r\n  .AiReportGeneraDetailsName {\r\n    font-size: var(--zy-title-font-size);\r\n    font-weight: bold;\r\n    color: var(--zy-el-color-primary);\r\n    border-bottom: 1px solid var(--zy-el-color-primary);\r\n    text-align: center;\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .AiReportGeneraDetailsContent {\r\n    padding: 20px 0;\r\n    overflow: hidden;\r\n    line-height: var(--zy-line-height);\r\n\r\n    img,\r\n    video {\r\n      max-width: 100%;\r\n      height: auto !important;\r\n    }\r\n\r\n    table {\r\n      border-collapse: collapse;\r\n      border-spacing: 0;\r\n\r\n      tr {\r\n        page-break-inside: avoid;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAA2B;iBAF1C;;uBACEC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,mBAAA,CAAyE,OAAzEC,UAAyE,EAAAC,gBAAA,CAA/BC,MAAA,CAAAC,OAAO,CAACC,cAAc,kBAChEL,mBAAA,CAAyE;IAApEH,KAAK,EAAC,8BAA8B;IAACS,SAAwB,EAAhBH,MAAA,CAAAC,OAAO,CAACG;0BAH9DC,UAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}