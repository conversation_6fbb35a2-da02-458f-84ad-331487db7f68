"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[5464],{45464:function(t,e,r){r.r(e),r.d(e,{default:function(){return E}});var n=r(62427),o=(r(76945),r(98773),r(81474)),a=(r(64352),r(84098)),i=(r(63584),r(74061)),c=r(4955),u=r(43955);function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var a=e&&e.prototype instanceof g?e:g,i=Object.create(a.prototype),c=new C(n||[]);return o(i,"_invoke",{value:V(t,r,c)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",d="suspendedYield",v="executing",y="completed",m={};function g(){}function w(){}function b(){}var x={};s(x,i,(function(){return this}));var E=Object.getPrototypeOf,L=E&&E(E(T([])));L&&L!==r&&n.call(L,i)&&(x=L);var k=b.prototype=g.prototype=Object.create(x);function N(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function G(t,e){function r(o,a,i,c){var u=h(t[o],t,a);if("throw"!==u.type){var l=u.arg,s=l.value;return s&&"object"==typeof s&&n.call(s,"__await")?e.resolve(s.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):e.resolve(s).then((function(t){l.value=t,i(l)}),(function(t){return r("throw",t,i,c)}))}c(u.arg)}var a;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return a=a?a.then(o,o):o()}})}function V(e,r,n){var o=p;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:t,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var u=_(c,n);if(u){if(u===m)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var l=h(e,r,n);if("normal"===l.type){if(o=n.done?y:d,l.arg===m)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=y,n.method="throw",n.arg=l.arg)}}}function _(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,_(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=h(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return a.next=a}}throw new TypeError(typeof e+" is not iterable")}return w.prototype=b,o(k,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=s(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},e.awrap=function(t){return{__await:t}},N(G.prototype),s(G.prototype,c,(function(){return this})),e.AsyncIterator=G,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new G(f(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},N(k),s(k,u,"Generator"),s(k,i,(function(){return this})),s(k,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var u=n.call(i,"catchLoc"),l=n.call(i,"finallyLoc");if(u&&l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function s(t,e,r,n,o,a,i){try{var c=t[a](i),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function f(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(t){s(a,n,o,i,c,"next",t)}function c(t){s(a,n,o,i,c,"throw",t)}i(void 0)}))}}var h={class:"GlobalGroupAnnouncement"},p={key:0,class:"GlobalGroupAnnouncementForm"},d={class:"GlobalGroupAnnouncementButton"},v={key:1,class:"GlobalGroupAnnouncementBody"},y={class:"GlobalChatGroupAnnouncementTitle"},m=["innerHTML"],g={class:"GlobalGroupAnnouncementText"},w={name:"GlobalGroupAnnouncement"},b=Object.assign(w,{props:{infoId:{type:String,default:""},isOwner:{type:Boolean,default:!1}},emits:["callback"],setup(t,e){var r=e.emit,s=t,w=r,b=(0,i.computed)((function(){return s.isOwner})),x=(0,i.ref)({}),E=(0,i.ref)(""),L=function(){var t=f(l().mark((function t(){var e,r;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,c.A.chatGroupEdit({form:{id:s.infoId,groupName:x.value.groupName,callBoard:E.value},ownerUserId:x.value.ownerUserId,memberUserIds:x.value.memberUserIds});case 2:e=t.sent,r=e.code,200===r&&w("callback",!0,`群公告：\n${E.value}`);case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),k=function(){w("callback",!1)},N=function(){var t=f(l().mark((function t(){var e,r;return l().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,c.A.chatGroupInfo({detailId:s.infoId});case 2:e=t.sent,r=e.data,x.value=r,E.value=r.callBoard;case 6:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();return(0,i.onMounted)((function(){N()})),function(t,e){var r=a.WK,c=o.S2,l=(0,i.resolveComponent)("CircleCloseFilled"),s=n.tk;return(0,i.openBlock)(),(0,i.createElementBlock)("div",h,[b.value?((0,i.openBlock)(),(0,i.createElementBlock)("div",p,[(0,i.createVNode)(r,{modelValue:E.value,"onUpdate:modelValue":e[0]||(e[0]=function(t){return E.value=t}),type:"textarea",rows:6,resize:"none",placeholder:"群公告",clearable:""},null,8,["modelValue"]),(0,i.createElementVNode)("div",d,[(0,i.createVNode)(c,{onClick:k},{default:(0,i.withCtx)((function(){return e[1]||(e[1]=[(0,i.createTextVNode)("取消")])})),_:1}),(0,i.createVNode)(c,{type:"primary",onClick:L},{default:(0,i.withCtx)((function(){return e[2]||(e[2]=[(0,i.createTextVNode)("完成")])})),_:1})])])):(0,i.createCommentVNode)("",!0),b.value?(0,i.createCommentVNode)("",!0):((0,i.openBlock)(),(0,i.createElementBlock)("div",v,[(0,i.createElementVNode)("div",y,[(0,i.createElementVNode)("div",null,[(0,i.createElementVNode)("span",{innerHTML:(0,i.unref)(u.Jt)},null,8,m),e[3]||(e[3]=(0,i.createTextVNode)(" 群公告"))]),(0,i.createVNode)(s,{onClick:k},{default:(0,i.withCtx)((function(){return[(0,i.createVNode)(l)]})),_:1})]),(0,i.createElementVNode)("div",g,(0,i.toDisplayString)(E.value),1)]))])}}});const x=b;var E=x}}]);