{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, withCtx as _withCtx, withModifiers as _withModifiers, createTextVNode as _createTextVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ChatSendFile\"\n};\nvar _hoisted_2 = {\n  class: \"ChatSendFileUser\"\n};\nvar _hoisted_3 = {\n  class: \"ChatSendFileUserName ellipsis\"\n};\nvar _hoisted_4 = {\n  class: \"ChatSendFileList\"\n};\nvar _hoisted_5 = {\n  class: \"ChatSendFileName\"\n};\nvar _hoisted_6 = {\n  class: \"ChatSendFileSize\"\n};\nvar _hoisted_7 = [\"onClick\"];\nvar _hoisted_8 = {\n  class: \"ChatSendFileButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_Close = _resolveComponent(\"Close\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"ChatSendFileObject\"\n  }, \"发送给：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n    src: $setup.imgUrl($setup.chatInfo.chatObjectInfo.img),\n    fit: \"cover\",\n    draggable: \"false\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.chatInfo.chatObjectInfo.name), 1 /* TEXT */)]), _createVNode(_component_el_scrollbar, {\n    class: \"ChatSendFileBody\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.fileList, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"ChatSendFileItem\",\n          key: item.id\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"globalFileIcon\", $setup.fileIcon(item.extName)])\n        }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(item.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, _toDisplayString(item.size ? $setup.size2Str(item.size) : ''), 1 /* TEXT */), _createElementVNode(\"div\", {\n          class: \"ChatSendFileDel\",\n          onClick: _withModifiers(function ($event) {\n            return $setup.handleDel(item);\n          }, [\"stop\"])\n        }, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_Close)];\n          }),\n          _: 1 /* STABLE */\n        })], 8 /* PROPS */, _hoisted_7)]);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_button, {\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return _cache[0] || (_cache[0] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSubmit\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"发送\")]);\n    }),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_image", "src", "$setup", "imgUrl", "chatInfo", "chatObjectInfo", "img", "fit", "draggable", "_hoisted_3", "_toDisplayString", "name", "_component_el_scrollbar", "default", "_withCtx", "_hoisted_4", "_Fragment", "_renderList", "fileList", "item", "key", "id", "_normalizeClass", "fileIcon", "extName", "_hoisted_5", "_hoisted_6", "size", "size2Str", "onClick", "_withModifiers", "$event", "handleDel", "_component_el_icon", "_component_Close", "_", "_hoisted_7", "_hoisted_8", "_component_el_button", "handleReset", "_cache", "_createTextVNode", "type", "handleSubmit"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\ChatSendFile\\ChatSendFile.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ChatSendFile\">\r\n    <div class=\"ChatSendFileObject\">发送给：</div>\r\n    <div class=\"ChatSendFileUser\">\r\n      <el-image :src=\"imgUrl(chatInfo.chatObjectInfo.img)\" fit=\"cover\" draggable=\"false\" />\r\n      <div class=\"ChatSendFileUserName ellipsis\">{{ chatInfo.chatObjectInfo.name }}</div>\r\n    </div>\r\n    <el-scrollbar class=\"ChatSendFileBody\">\r\n      <div class=\"ChatSendFileList\">\r\n        <div class=\"ChatSendFileItem\" v-for=\"item in fileList\" :key=\"item.id\">\r\n          <div class=\"globalFileIcon\" :class=\"fileIcon(item.extName)\"></div>\r\n          <div class=\"ChatSendFileName\">{{ item.name }}</div>\r\n          <div class=\"ChatSendFileSize\">{{ item.size ? size2Str(item.size) : '' }}</div>\r\n          <div class=\"ChatSendFileDel\" @click.stop=\"handleDel(item)\">\r\n            <el-icon>\r\n              <Close />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"ChatSendFileButton\">\r\n      <el-button @click=\"handleReset\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">发送</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChatSendFile' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { size2Str } from 'common/js/utils.js'\r\nconst props = defineProps({\r\n  chatInfo: { type: Object, default: () => ({}) },\r\n  fileList: { type: Array, default: () => ([]) }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst chatInfo = computed(() => props.chatInfo)\r\nconst fileList = ref([])\r\n// 图片地址拼接组合\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\nconst fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nonMounted(() => {\r\n  fileList.value = props.fileList.map(v => v)\r\n})\r\nconst handleDel = (file) => {\r\n  fileList.value = fileList.value.filter(v => v.id !== file.id)\r\n  if (!fileList.value.length) emit('callback')\r\n}\r\nconst handleSubmit = () => { emit('callback', fileList.value) }\r\nconst handleReset = () => { emit('callback') }\r\n</script>\r\n<style lang=\"scss\">\r\n.ChatSendFile {\r\n  width: 360px;\r\n  height: 100%;\r\n  padding: 20px 0;\r\n\r\n  .ChatSendFileObject {\r\n    width: 100%;\r\n    height: 26px;\r\n    line-height: 26px;\r\n    font-size: 16px;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .ChatSendFileUser {\r\n    width: 100%;\r\n    height: 68px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    position: relative;\r\n    padding: 0 20px 10px 20px;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: calc(100% - 40px);\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      position: absolute;\r\n      left: 20px;\r\n      bottom: 10px;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 38px;\r\n      height: 38px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .ChatSendFileUserName {\r\n      width: calc(100% - 52px);\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .ChatSendFileBody {\r\n    height: calc(100% - 140px);\r\n\r\n    .ChatSendFileList {\r\n      width: 100%;\r\n      padding: 0 20px;\r\n    }\r\n\r\n    .ChatSendFileItem {\r\n      width: 100%;\r\n      padding: 12px 16px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      background: #fff;\r\n      position: relative;\r\n      padding-right: 58px;\r\n      border-radius: var(--el-border-radius-base);\r\n      border: 1px solid var(--zy-el-border-color-light);\r\n      word-wrap: break-word;\r\n      white-space: pre-wrap;\r\n      overflow: hidden;\r\n      cursor: pointer;\r\n\r\n      &+.ChatSendFileItem {\r\n        margin-top: 10px;\r\n      }\r\n\r\n      .ChatSendFileName {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-bottom: var(--zy-font-text-distance-five);\r\n        word-break: break-all;\r\n      }\r\n\r\n      .ChatSendFileSize {\r\n        color: var(--zy-el-text-color-secondary);\r\n        font-size: calc(var(--zy-text-font-size) - 2px);\r\n      }\r\n\r\n      .ChatSendFileDel {\r\n        width: 40px;\r\n        height: 24px;\r\n        position: absolute;\r\n        right: -15px;\r\n        top: -6px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        transform: rotate(45deg);\r\n        background: rgba(0, 0, 0, 0.5);\r\n        color: #fff;\r\n        padding-top: 6px;\r\n        padding-right: 2px;\r\n\r\n        .zy-el-icon {\r\n          font-size: 12px;\r\n          transform: rotate(-45deg);\r\n        }\r\n      }\r\n\r\n      .globalFileIcon {\r\n        width: 40px;\r\n        height: 40px;\r\n        vertical-align: middle;\r\n        position: absolute;\r\n        top: 12px;\r\n        right: 12px;\r\n      }\r\n\r\n      .globalFileUnknown {\r\n        background: url(\"../../img/unknown.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFilePDF {\r\n        background: url(\"../../img/PDF.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileWord {\r\n        background: url(\"../../img/Word.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileExcel {\r\n        background: url(\"../../img/Excel.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFilePicture {\r\n        background: url(\"../../img/picture.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileVideo {\r\n        background: url(\"../../img/video.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileTXT {\r\n        background: url(\"../../img/TXT.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileCompress {\r\n        background: url(\"../../img/compress.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFileWPS {\r\n        background: url(\"../../img/WPS.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n\r\n      .globalFilePPT {\r\n        background: url(\"../../img/PPT.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ChatSendFileButton {\r\n    width: 100%;\r\n    height: 46px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: center;\r\n\r\n    .zy-el-button {\r\n      width: 120px;\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAA+B;;EAGrCA,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAkB;iBAZvC;;EAqBSA,KAAK,EAAC;AAAoB;;;;;;;uBApBjCC,mBAAA,CAwBM,OAxBNC,UAwBM,G,0BAvBJC,mBAAA,CAA0C;IAArCH,KAAK,EAAC;EAAoB,GAAC,MAAI,sBACpCG,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAAqFC,mBAAA;IAA1EC,GAAG,EAAEC,MAAA,CAAAC,MAAM,CAACD,MAAA,CAAAE,QAAQ,CAACC,cAAc,CAACC,GAAG;IAAGC,GAAG,EAAC,OAAO;IAACC,SAAS,EAAC;oCAC3EX,mBAAA,CAAmF,OAAnFY,UAAmF,EAAAC,gBAAA,CAArCR,MAAA,CAAAE,QAAQ,CAACC,cAAc,CAACM,IAAI,iB,GAE5EZ,YAAA,CAaea,uBAAA;IAbDlB,KAAK,EAAC;EAAkB;IAP1CmB,OAAA,EAAAC,QAAA,CAQM;MAAA,OAWM,CAXNjB,mBAAA,CAWM,OAXNkB,UAWM,I,kBAVJpB,mBAAA,CASMqB,SAAA,QAlBdC,WAAA,CASqDf,MAAA,CAAAgB,QAAQ,EAT7D,UAS6CC,IAAI;6BAAzCxB,mBAAA,CASM;UATDD,KAAK,EAAC,kBAAkB;UAA2B0B,GAAG,EAAED,IAAI,CAACE;YAChExB,mBAAA,CAAkE;UAA7DH,KAAK,EAVpB4B,eAAA,EAUqB,gBAAgB,EAASpB,MAAA,CAAAqB,QAAQ,CAACJ,IAAI,CAACK,OAAO;iCACzD3B,mBAAA,CAAmD,OAAnD4B,UAAmD,EAAAf,gBAAA,CAAlBS,IAAI,CAACR,IAAI,kBAC1Cd,mBAAA,CAA8E,OAA9E6B,UAA8E,EAAAhB,gBAAA,CAA7CS,IAAI,CAACQ,IAAI,GAAGzB,MAAA,CAAA0B,QAAQ,CAACT,IAAI,CAACQ,IAAI,wBAC/D9B,mBAAA,CAIM;UAJDH,KAAK,EAAC,iBAAiB;UAAEmC,OAAK,EAb7CC,cAAA,WAAAC,MAAA;YAAA,OAaoD7B,MAAA,CAAA8B,SAAS,CAACb,IAAI;UAAA;YACtDpB,YAAA,CAEUkC,kBAAA;UAhBtBpB,OAAA,EAAAC,QAAA,CAec;YAAA,OAAS,CAATf,YAAA,CAASmC,gBAAA,E;;UAfvBC,CAAA;4BAAAC,UAAA,E;;;IAAAD,CAAA;MAqBItC,mBAAA,CAGM,OAHNwC,UAGM,GAFJtC,YAAA,CAA8CuC,oBAAA;IAAlCT,OAAK,EAAE3B,MAAA,CAAAqC;EAAW;IAtBpC1B,OAAA,EAAAC,QAAA,CAsBsC;MAAA,OAAE0B,MAAA,QAAAA,MAAA,OAtBxCC,gBAAA,CAsBsC,IAAE,E;;IAtBxCN,CAAA;MAuBMpC,YAAA,CAA8DuC,oBAAA;IAAnDI,IAAI,EAAC,SAAS;IAAEb,OAAK,EAAE3B,MAAA,CAAAyC;;IAvBxC9B,OAAA,EAAAC,QAAA,CAuBsD;MAAA,OAAE0B,MAAA,QAAAA,MAAA,OAvBxDC,gBAAA,CAuBsD,IAAE,E;;IAvBxDN,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}