{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onMounted, nextTick } from 'vue';\nimport { useRoute } from 'vue-router';\nimport { user, IntelligentAssistant } from 'common/js/system_var.js';\nvar __default__ = {\n  name: 'QuestionsAndAnswers'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var route = useRoute();\n    var scrollRef = ref();\n    var content = ref('');\n    var dataList = ref([{\n      id: '1',\n      type: 0,\n      className: 'QuestionsAndAnswersOther',\n      text: '您好！我是您的小助手，您可以向我提出问题，我将基于AI给我的超能力，然后根据资源库内数据做出回答，赶紧试试吧！'\n    }\n    // { id: '2', type: 1, className: 'QuestionsAndAnswersOneself', text: '请根据关键词“长沙市\"' }\n    ]);\n    var history = ref({});\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    onMounted(function () {});\n    var scrollDown = function scrollDown() {\n      scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight;\n    };\n    var handleSend = function handleSend() {\n      var id = guid();\n      history.value[id] = [content.value];\n      dataList.value.push({\n        id: id,\n        type: 1,\n        className: 'QuestionsAndAnswersOneself',\n        text: content.value\n      });\n      dataList.value.push({\n        id: guid(),\n        uid: id,\n        type: 0,\n        loading: true,\n        className: 'QuestionsAndAnswersOther',\n        text: '正在思考中！'\n      });\n      nextTick(function () {\n        scrollDown();\n      });\n      chatGLMDocument(content.value, id);\n      content.value = '';\n    };\n    var chatGLMDocument = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(text, id) {\n        var _data$data;\n        var _yield$api$knowledgeQ, data, answer, newArray, index, item;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.knowledgeQuiz({\n                input: text,\n                zskId: route.query.type\n              });\n            case 2:\n              _yield$api$knowledgeQ = _context.sent;\n              data = _yield$api$knowledgeQ.data;\n              answer = (data === null || data === void 0 || (_data$data = data.data) === null || _data$data === void 0 ? void 0 : _data$data.output) || data.message;\n              if (id) {\n                newArray = [];\n                for (index = 0; index < dataList.value.length; index++) {\n                  item = dataList.value[index];\n                  if (item.uid === id) {\n                    newArray.push(_objectSpread(_objectSpread({}, item), {}, {\n                      loading: false,\n                      text: answer\n                    }));\n                  } else {\n                    newArray.push(item);\n                  }\n                }\n                dataList.value = newArray;\n              } else {\n                dataList.value.push({\n                  id: guid(),\n                  uid: guid(),\n                  type: 0,\n                  className: 'QuestionsAndAnswersOther',\n                  text: answer\n                });\n              }\n              nextTick(function () {\n                scrollDown();\n              });\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function chatGLMDocument(_x, _x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var __returned__ = {\n      route,\n      scrollRef,\n      content,\n      dataList,\n      history,\n      guid,\n      scrollDown,\n      handleSend,\n      chatGLMDocument,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      nextTick,\n      get useRoute() {\n        return useRoute;\n      },\n      get user() {\n        return user;\n      },\n      get IntelligentAssistant() {\n        return IntelligentAssistant;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread", "arguments", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "asyncGeneratorStep", "_asyncToGenerator", "_next", "_throw", "api", "ref", "onMounted", "nextTick", "useRoute", "user", "IntelligentAssistant", "__default__", "route", "scrollRef", "content", "dataList", "id", "className", "text", "history", "guid", "replace", "Math", "random", "toString", "scrollDown", "wrapRef", "scrollTop", "scrollHeight", "handleSend", "uid", "loading", "chatGLMDocument", "_ref2", "_callee", "_data$data", "_yield$api$knowledgeQ", "data", "answer", "newArray", "index", "item", "_callee$", "_context", "knowledgeQuiz", "input", "zskId", "query", "output", "message", "_x", "_x2"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/QuestionsAndAnswers/QuestionsAndAnswers.vue"], "sourcesContent": ["<template>\r\n  <div class=\"QuestionsAndAnswers\">\r\n    <div class=\"QuestionsAndAnswersBody\">\r\n      <el-scrollbar always class=\"QuestionsAndAnswersChat\" ref=\"scrollRef\">\r\n        <div v-for=\"item in dataList\" :class=\"item.className\" :key=\"item.id\">\r\n          <div class=\"QuestionsAndAnswersChatImg\">\r\n            <el-image :src=\"item.type ? user.image : IntelligentAssistant\" fit=\"cover\"></el-image>\r\n          </div>\r\n          <div class=\"QuestionsAndAnswersChatTextBody\">\r\n            <div class=\"answerLoading\" v-if=\"item.loading\">\r\n              <svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\">\r\n                <path\r\n                  d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2363\"></path>\r\n                <path\r\n                  d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2364\"></path>\r\n                <path\r\n                  d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2365\"></path>\r\n                <path\r\n                  d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2366\"></path>\r\n                <path\r\n                  d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2367\"></path>\r\n                <path\r\n                  d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2368\"></path>\r\n                <path\r\n                  d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2369\"></path>\r\n                <path\r\n                  d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2370\"></path>\r\n              </svg>\r\n            </div>\r\n            <div class=\"QuestionsAndAnswersChatText\"><span></span>{{ item.text }}</div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <div class=\"QuestionsAndAnswersChatInput\">\r\n        <el-input v-model=\"content\" type=\"textarea\" :autosize=\"{ minRows: 3, maxRows: 6 }\" placeholder=\"请输入您的需求...\" />\r\n        <el-button @click=\"handleSend\" type=\"primary\">发送</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'QuestionsAndAnswers' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { user, IntelligentAssistant } from 'common/js/system_var.js'\r\nconst route = useRoute()\r\nconst scrollRef = ref()\r\nconst content = ref('')\r\nconst dataList = ref([\r\n  { id: '1', type: 0, className: 'QuestionsAndAnswersOther', text: '您好！我是您的小助手，您可以向我提出问题，我将基于AI给我的超能力，然后根据资源库内数据做出回答，赶紧试试吧！' },\r\n  // { id: '2', type: 1, className: 'QuestionsAndAnswersOneself', text: '请根据关键词“长沙市\"' }\r\n])\r\nconst history = ref({})\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)\r\n    return v.toString(16)\r\n  })\r\n}\r\nonMounted(() => { })\r\nconst scrollDown = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight\r\n}\r\nconst handleSend = () => {\r\n  const id = guid()\r\n  history.value[id] = [content.value]\r\n  dataList.value.push({ id: id, type: 1, className: 'QuestionsAndAnswersOneself', text: content.value })\r\n  dataList.value.push({ id: guid(), uid: id, type: 0, loading: true, className: 'QuestionsAndAnswersOther', text: '正在思考中！' })\r\n  nextTick(() => { scrollDown() })\r\n  chatGLMDocument(content.value, id)\r\n  content.value = ''\r\n}\r\nconst chatGLMDocument = async (text, id) => {\r\n  const { data } = await api.knowledgeQuiz({ input: text, zskId: route.query.type })\r\n  const answer = data?.data?.output || data.message\r\n  if (id) {\r\n    let newArray = []\r\n    for (let index = 0; index < dataList.value.length; index++) {\r\n      const item = dataList.value[index]\r\n      if (item.uid === id) {\r\n        newArray.push({ ...item, loading: false, text: answer })\r\n      } else {\r\n        newArray.push(item)\r\n      }\r\n    }\r\n    dataList.value = newArray\r\n  } else {\r\n    dataList.value.push({ id: guid(), uid: guid(), type: 0, className: 'QuestionsAndAnswersOther', text: answer })\r\n  }\r\n  nextTick(() => { scrollDown() })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.QuestionsAndAnswers {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n  padding-top: 20px;\r\n  background-color: #fff;\r\n\r\n  .QuestionsAndAnswersBody {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    background: linear-gradient(180deg, #FFFBFA 0%, #FAF6F5 100%);\r\n    padding: 20px 0;\r\n\r\n    .QuestionsAndAnswersChatInput {\r\n      width: 100%;\r\n      max-width: 900px;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      padding: var(--zy-distance-five) var(--zy-distance-two);\r\n      border-radius: var(--el-border-radius-base);\r\n      background: #fff;\r\n\r\n      .zy-el-textarea__inner {\r\n        border-radius: 0;\r\n      }\r\n\r\n      .zy-el-button {\r\n        margin-left: var(--zy-distance-four);\r\n        --zy-el-button-size: var(--zy-height-routine);\r\n      }\r\n    }\r\n\r\n    .QuestionsAndAnswersChat {\r\n      flex: 1;\r\n      width: 100%;\r\n      max-width: 800px;\r\n\r\n      .QuestionsAndAnswersOther,\r\n      .QuestionsAndAnswersOneself {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: var(--zy-distance-five) var(--zy-distance-two);\r\n\r\n        .QuestionsAndAnswersChatImg {\r\n          width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n          height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n\r\n          .zy-el-image {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 50%;\r\n          }\r\n        }\r\n\r\n        .QuestionsAndAnswersChatTextBody {\r\n          display: flex;\r\n          width: calc(100% - ((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n          position: relative;\r\n\r\n          @keyframes circleRoate {\r\n            from {\r\n              transform: rotate(0deg);\r\n            }\r\n\r\n            to {\r\n              transform: rotate(360deg);\r\n            }\r\n          }\r\n\r\n          .answerLoading {\r\n            width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            z-index: 3;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            animation: circleRoate 1s infinite linear;\r\n\r\n            path {\r\n              fill: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n\r\n          .answerLoading+.QuestionsAndAnswersChatText {\r\n            color: var(--zy-el-color-primary);\r\n            padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n          }\r\n\r\n          .QuestionsAndAnswersChatText {\r\n            position: relative;\r\n            display: inline-block;\r\n            max-width: 100%;\r\n            padding: 10px var(--zy-distance-five);\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n            background-color: #fff;\r\n            border-radius: var(--el-border-radius-base);\r\n            border: 1px solid var(--zy-el-border-color-light);\r\n            word-wrap: break-word;\r\n            white-space: pre-wrap;\r\n            z-index: 2;\r\n\r\n\r\n            span {\r\n              position: absolute;\r\n              width: 10px;\r\n              height: 10px;\r\n              z-index: -1;\r\n              top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);\r\n\r\n              &::after {\r\n                content: \"\";\r\n                position: absolute;\r\n                width: 10px;\r\n                height: 10px;\r\n                transform: rotate(45deg);\r\n                background: #fff;\r\n                border: 1px solid var(--zy-el-border-color-light);\r\n                box-sizing: border-box;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .QuestionsAndAnswersOther {\r\n        .QuestionsAndAnswersChatTextBody {\r\n          padding-right: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n\r\n          .QuestionsAndAnswersChatText {\r\n\r\n            span {\r\n              left: 0;\r\n              transform: translate(-50%, -50%);\r\n\r\n              &::after {\r\n                border-top-color: transparent !important;\r\n                border-right-color: transparent !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .QuestionsAndAnswersOneself {\r\n        flex-direction: row-reverse;\r\n\r\n        .QuestionsAndAnswersChatTextBody {\r\n          justify-content: flex-end;\r\n          padding-left: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n\r\n          .QuestionsAndAnswersChatText {\r\n            span {\r\n              right: 0;\r\n              transform: translate(50%, -50%);\r\n\r\n              &::after {\r\n                border-left-color: transparent !important;\r\n                border-bottom-color: transparent !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CAqDA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,QAAAvG,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAqG,qBAAA,QAAAjG,CAAA,GAAAJ,MAAA,CAAAqG,qBAAA,CAAAxG,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAkG,MAAA,WAAAvG,CAAA,WAAAC,MAAA,CAAAuG,wBAAA,CAAA1G,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAkC,KAAA,CAAA1G,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2G,cAAA5G,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAA2G,SAAA,CAAA/B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAA4G,SAAA,CAAA3G,CAAA,IAAA2G,SAAA,CAAA3G,CAAA,QAAAA,CAAA,OAAAqG,OAAA,CAAApG,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA4G,eAAA,CAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA4G,yBAAA,GAAA5G,MAAA,CAAA6G,gBAAA,CAAAhH,CAAA,EAAAG,MAAA,CAAA4G,yBAAA,CAAA9G,CAAA,KAAAsG,OAAA,CAAApG,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAuG,wBAAA,CAAAzG,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA8G,gBAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA+G,cAAA,CAAA/G,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAiH,eAAAhH,CAAA,QAAAS,CAAA,GAAAwG,YAAA,CAAAjH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAwG,aAAAjH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAwG,WAAA,kBAAAnH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAkH,MAAA,GAAAC,MAAA,EAAApH,CAAA;AAAA,SAAAqH,mBAAAjH,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAgH,kBAAAlH,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA6G,SAAA,aAAArB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAAwH,MAAAnH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,UAAApH,CAAA,cAAAoH,OAAApH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,WAAApH,CAAA,KAAAmH,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,IAAI,EAAEC,oBAAoB,QAAQ,yBAAyB;AANpE,IAAAC,WAAA,GAAe;EAAE/C,IAAI,EAAE;AAAsB,CAAC;;;;;IAO9C,IAAMgD,KAAK,GAAGJ,QAAQ,CAAC,CAAC;IACxB,IAAMK,SAAS,GAAGR,GAAG,CAAC,CAAC;IACvB,IAAMS,OAAO,GAAGT,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMU,QAAQ,GAAGV,GAAG,CAAC,CACnB;MAAEW,EAAE,EAAE,GAAG;MAAE1G,IAAI,EAAE,CAAC;MAAE2G,SAAS,EAAE,0BAA0B;MAAEC,IAAI,EAAE;IAA0D;IAC3H;IAAA,CACD,CAAC;IACF,IAAMC,OAAO,GAAGd,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMe,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAC7H,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAG0I,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;UAAEpG,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;QAClE,OAAOuC,CAAC,CAACqG,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACDlB,SAAS,CAAC,YAAM,CAAE,CAAC,CAAC;IACpB,IAAMmB,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvBZ,SAAS,CAAC1H,KAAK,CAACuI,OAAO,CAACC,SAAS,GAAGd,SAAS,CAAC1H,KAAK,CAACuI,OAAO,CAACE,YAAY;IAC1E,CAAC;IACD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAMb,EAAE,GAAGI,IAAI,CAAC,CAAC;MACjBD,OAAO,CAAChI,KAAK,CAAC6H,EAAE,CAAC,GAAG,CAACF,OAAO,CAAC3H,KAAK,CAAC;MACnC4H,QAAQ,CAAC5H,KAAK,CAACgE,IAAI,CAAC;QAAE6D,EAAE,EAAEA,EAAE;QAAE1G,IAAI,EAAE,CAAC;QAAE2G,SAAS,EAAE,4BAA4B;QAAEC,IAAI,EAAEJ,OAAO,CAAC3H;MAAM,CAAC,CAAC;MACtG4H,QAAQ,CAAC5H,KAAK,CAACgE,IAAI,CAAC;QAAE6D,EAAE,EAAEI,IAAI,CAAC,CAAC;QAAEU,GAAG,EAAEd,EAAE;QAAE1G,IAAI,EAAE,CAAC;QAAEyH,OAAO,EAAE,IAAI;QAAEd,SAAS,EAAE,0BAA0B;QAAEC,IAAI,EAAE;MAAS,CAAC,CAAC;MAC3HX,QAAQ,CAAC,YAAM;QAAEkB,UAAU,CAAC,CAAC;MAAC,CAAC,CAAC;MAChCO,eAAe,CAAClB,OAAO,CAAC3H,KAAK,EAAE6H,EAAE,CAAC;MAClCF,OAAO,CAAC3H,KAAK,GAAG,EAAE;IACpB,CAAC;IACD,IAAM6I,eAAe;MAAA,IAAAC,KAAA,GAAAhC,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAqE,QAAOhB,IAAI,EAAEF,EAAE;QAAA,IAAAmB,UAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,KAAA,EAAAC,IAAA;QAAA,OAAAhK,mBAAA,GAAAuB,IAAA,UAAA0I,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAArE,IAAA,GAAAqE,QAAA,CAAAhG,IAAA;YAAA;cAAAgG,QAAA,CAAAhG,IAAA;cAAA,OACdyD,GAAG,CAACwC,aAAa,CAAC;gBAAEC,KAAK,EAAE3B,IAAI;gBAAE4B,KAAK,EAAElC,KAAK,CAACmC,KAAK,CAACzI;cAAK,CAAC,CAAC;YAAA;cAAA8H,qBAAA,GAAAO,QAAA,CAAAvG,IAAA;cAA1EiG,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACNC,MAAM,GAAG,CAAAD,IAAI,aAAJA,IAAI,gBAAAF,UAAA,GAAJE,IAAI,CAAEA,IAAI,cAAAF,UAAA,uBAAVA,UAAA,CAAYa,MAAM,KAAIX,IAAI,CAACY,OAAO;cACjD,IAAIjC,EAAE,EAAE;gBACFuB,QAAQ,GAAG,EAAE;gBACjB,KAASC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGzB,QAAQ,CAAC5H,KAAK,CAACqE,MAAM,EAAEgF,KAAK,EAAE,EAAE;kBACpDC,IAAI,GAAG1B,QAAQ,CAAC5H,KAAK,CAACqJ,KAAK,CAAC;kBAClC,IAAIC,IAAI,CAACX,GAAG,KAAKd,EAAE,EAAE;oBACnBuB,QAAQ,CAACpF,IAAI,CAAAmC,aAAA,CAAAA,aAAA,KAAMmD,IAAI;sBAAEV,OAAO,EAAE,KAAK;sBAAEb,IAAI,EAAEoB;oBAAM,EAAE,CAAC;kBAC1D,CAAC,MAAM;oBACLC,QAAQ,CAACpF,IAAI,CAACsF,IAAI,CAAC;kBACrB;gBACF;gBACA1B,QAAQ,CAAC5H,KAAK,GAAGoJ,QAAQ;cAC3B,CAAC,MAAM;gBACLxB,QAAQ,CAAC5H,KAAK,CAACgE,IAAI,CAAC;kBAAE6D,EAAE,EAAEI,IAAI,CAAC,CAAC;kBAAEU,GAAG,EAAEV,IAAI,CAAC,CAAC;kBAAE9G,IAAI,EAAE,CAAC;kBAAE2G,SAAS,EAAE,0BAA0B;kBAAEC,IAAI,EAAEoB;gBAAO,CAAC,CAAC;cAChH;cACA/B,QAAQ,CAAC,YAAM;gBAAEkB,UAAU,CAAC,CAAC;cAAC,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAkB,QAAA,CAAAlE,IAAA;UAAA;QAAA,GAAAyD,OAAA;MAAA,CACjC;MAAA,gBAlBKF,eAAeA,CAAAkB,EAAA,EAAAC,GAAA;QAAA,OAAAlB,KAAA,CAAA5C,KAAA,OAAAE,SAAA;MAAA;IAAA,GAkBpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}