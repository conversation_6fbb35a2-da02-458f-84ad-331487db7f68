"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[6600],{46600:function(e,t,r){r.r(t),r.d(t,{default:function(){return ne}});var n=r(81474),o=(r(76945),r(64352),r(62427)),a=(r(98773),r(44863)),i=(r(4711),r(49744)),l=(r(98326),r(97445)),s=(r(10406),r(44917)),c=(r(40065),r(74061)),u=r(4955),d=r(44500),f=r(3671),p=r(88609),v=r(43955),h=r(98885),m=(r(35894),r(24652)),y=r(46197),k=r(97954),g=r(59335);function b(e){return B(e)||C(e)||x(e)||w()}function w(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function x(e,t){if(e){if("string"==typeof e)return N(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?N(e,t):void 0}}function C(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function B(e){if(Array.isArray(e))return N(e)}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function A(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */A=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var a=t&&t.prototype instanceof y?t:y,i=Object.create(a.prototype),l=new S(n||[]);return o(i,"_invoke",{value:I(e,r,l)}),i}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",m={};function y(){}function k(){}function g(){}var b={};c(b,i,(function(){return this}));var w=Object.getPrototypeOf,x=w&&w(w(T([])));x&&x!==r&&n.call(x,i)&&(b=x);var C=g.prototype=y.prototype=Object.create(b);function B(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function N(e,t){function r(o,a,i,l){var s=d(e[o],e,a);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==typeof u&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(u).then((function(e){c.value=e,i(c)}),(function(e){return r("throw",e,i,l)}))}l(s.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function I(t,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var l=n.delegate;if(l){var s=E(l,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(t,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function E(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=d(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,m;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function V(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function T(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return k.prototype=g,o(C,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:k,configurable:!0}),k.displayName=c(g,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,c(e,s,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},B(N.prototype),c(N.prototype,l,(function(){return this})),t.AsyncIterator=N,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new N(u(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},B(C),c(C,s,"Generator"),c(C,i,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=T,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(V),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return l.type="throw",l.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],l=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),V(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;V(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:T(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function I(e,t,r,n,o,a,i){try{var l=e[a](i),s=l.value}catch(e){return void r(e)}l.done?t(s):Promise.resolve(s).then(n,o)}function E(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){I(a,n,o,i,l,"next",e)}function l(e){I(a,n,o,i,l,"throw",e)}i(void 0)}))}}var L={class:"GlobalChatAddressBookList forbidSelect"},V={class:"GlobalChatAddressBookInput"},S={class:"GlobalChatAddressBookItem forbidSelect"},T={class:"GlobalChatAddressBookName ellipsis"},G={key:0,class:"GlobalChatAddressBookLabel"},_=["onClick"],O={class:"GlobalChatAddressBookName ellipsis"},M={key:0,class:"GlobalChatAddressBookBody"},D={class:"GlobalChatAddressBookInfo"},j={class:"GlobalChatAddressBookInfoBody"},U=["innerHTML"],P=["innerHTML"],R={class:"GlobalChatAddressBookIcon"},Y={class:"GlobalChatAddressBookText ellipsis"},F=["innerHTML"],H={class:"GlobalChatAddressBookText ellipsis"},q={class:"GlobalChatAddressBookUserBody"},z={class:"GlobalChatAddressBookUserText ellipsis"},$={class:"GlobalChatAddressBookUserText ellipsis"},J={class:"GlobalChatAddressBookUserText ellipsis"},Z={key:0,class:"GlobalChatAddressBookUserText ellipsis"},K={key:1,class:"GlobalChatAddressBookUserText ellipsis"},Q={key:2,class:"GlobalChatAddressBookUserText ellipsis"},W={key:3,style:{margin:"10px 0"}},X={key:1,class:"GlobalChatAddressBookDrag"},ee={name:"GlobalChatAddressBook"},te=Object.assign(ee,{emits:["send"],setup(e,t){var r,w=t.expose,x=t.emit,C=(0,g.useStore)(),B=x,N=null===(r=window.electron)||void 0===r?void 0:r.isMac,I=(0,c.ref)(),ee=(0,c.ref)(""),te=(0,c.ref)([]),re=(0,c.ref)(""),ne=(0,c.ref)({}),oe=(0,c.ref)([]),ae=function(e){return e?u.A.fileURL(e):u.A.defaultImgURL("default_user_head.jpg")},ie=(0,c.ref)(""),le=function(){var e=E(A().mark((function e(t,r){var n;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t){e.next=6;break}return e.next=3,se(t);case 3:e.t0=e.sent,e.next=7;break;case 6:e.t0=[];case 7:n=e.t0,r(n);case 9:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),se=function(){var e=E(A().mark((function e(){var t,r,n,o;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=[],r=0;case 2:if(!(r<te.value.length)){e.next=11;break}return n=te.value[r],e.next=6,ce(n.id);case 6:o=e.sent,t=[].concat(b(t),b(o));case 8:r++,e.next=2;break;case 11:return e.abrupt("return",b(new Map(t.map((function(e){return[e.id,e]}))).values()));case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ce=function(){var e=E(A().mark((function e(t){var r,n,o,a,i;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,u.A.SelectPersonBookUser({isOpen:1,keyword:ee.value,labelCode:t,nodeId:"",relationBookId:"",tabCode:"relationBooksTemp"});case 3:for(r=e.sent,n=r.data,o=[],a=0;a<n.length;a++)i=n[a],i.userId&&o.push({id:i.userId,label:i.userName,children:[],type:"user",user:i,isLeaf:!0});return e.abrupt("return",o);case 10:return e.prev=10,e.t0=e["catch"](0),e.abrupt("return",[]);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(t){return e.apply(this,arguments)}}(),ue=function(e){re.value=e.id,de(e)},de=function(){var e=E(A().mark((function e(){var t,r;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.A.userInfo({detailId:re.value});case 2:t=e.sent,r=t.data,ne.value=r,fe();case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),fe=function(){var e=E(A().mark((function e(){var t,r;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.A.globalReadConfig({codes:["appShareAddress"]});case 2:t=e.sent,r=t.data,pe(r.appShareAddress);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),pe=function(){var e=E(A().mark((function e(t){var r;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r={n:"mo_npcinfo_details",u:"../mo_npcinfo_details/mo_npcinfo_details.stml",p:{id:re.value}},ve(t+"pages/index/?"+JSON.stringify(r).replace(/\{/g,"%7B").replace(/\}/g,"%7D").replace(/\u0022/g,"%22"));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ve=function(){var e=E(A().mark((function e(t){var r,n;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.A.longShortLink(encodeURIComponent(t));case 2:r=e.sent,n=r.data,ie.value=`${y.A.API_URL}/viewing/${n}`;case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),he=function(e){if(e.roleIds){if(e.roleIds.includes("1640259895343255554"))return void C.commit("setOpenRoute",{name:"委员信息详情",path:"/cppccMember/CppccMemberDetails",query:{id:e.id}});(e.roleIds.includes("1684119713426243586")||e.roleIds.includes("1743150705994133506")||e.roleIds.includes("1643857093725327362"))&&C.commit("setOpenRoute",{name:"用户信息详情",path:"/system/SubmitUser",query:{id:e.id,utype:1}})}},me=function(){var e=E(A().mark((function e(t,r){var n;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,d.getConversation({conversationType:t,targetId:r});case 2:return n=e.sent,e.abrupt("return",n);case 4:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),ye=function(){var e=E(A().mark((function e(){var t,r,n,o,a,i,l,s;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=p.TC.value+ne.value.accountId,e.next=3,me(1,t);case 3:r=e.sent,n=r.code,o=r.data,n||(s={isTemporary:!0,isTop:o.isTop,isNotInform:o.notificationStatus,id:o.targetId,targetId:o.targetId,type:o.conversationType,chatObjectInfo:{uid:o.targetId,id:ne.value.accountId,name:ne.value.userName,img:ne.value.photo||ne.value.headImg,userInfo:{userId:ne.value.id,userName:ne.value.userName,photo:ne.value.photo,headImg:ne.value.headImg}},sentTime:(null===(a=o.latestMessage)||void 0===a?void 0:a.sentTime)||Date.parse(new Date),messageType:(null===(i=o.latestMessage)||void 0===i?void 0:i.messageType)||"RC:TxtMsg",content:(null===(l=o.latestMessage)||void 0===l?void 0:l.content)||{content:""},count:o.unreadMessageCount},B("send",s));case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ke=function(){var e=E(A().mark((function e(t){var r,n,o,a,i,l,s;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.A.SelectPersonTab({tabCodes:["relationBooksTemp"]});case 2:for(r=e.sent,n=r.data,o=[],a=0;a<(null===(i=n[0])||void 0===i?void 0:i.chooseLabels.length);a++)s=null===(l=n[0])||void 0===l?void 0:l.chooseLabels[a],o.push({id:s.labelCode,label:s.name,children:[],type:"label",isLeaf:!1});te.value=o,t(o);case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ge=function(e,t){for(var r=[],n=0;n<t.length;n++){var o=t[n];if(o.code!==e){var a=ge(e,o.children);r.push({id:o.code,label:o.name,children:a,type:"tree",isLeaf:!1})}}return r},be=function(){var e=E(A().mark((function e(t){var r,n;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.A.SelectPersonGroup({labelCode:t,tabCode:"relationBooksTemp"});case 2:return r=e.sent,n=r.data,e.abrupt("return",ge(t,n));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),we=function(){var e=E(A().mark((function e(t,r){var n,o,a,i,l;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.A.SelectPersonBookUser({isOpen:1,keyword:"",labelCode:t,nodeId:r,relationBookId:r,tabCode:"relationBooksTemp"});case 2:for(n=e.sent,o=n.data,a=[],i=0;i<o.length;i++)l=o[i],l.userId&&a.push({id:l.userId,label:l.userName,children:[],type:"user",user:l,isLeaf:!0});return e.abrupt("return",a);case 7:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),xe=function(){var e=E(A().mark((function e(t,r){var n,o,a,i,l,s,c,u;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!==t.level){e.next=4;break}ke(r),e.next=27;break;case 4:if(null===(n=t.data)||void 0===n||null===(n=n.children)||void 0===n||!n.length){e.next=13;break}return a=null===(o=t.data)||void 0===o?void 0:o.children,e.next=8,we(t.parent.key,t.key);case 8:i=e.sent,l=[].concat(b(a),b(i)),r(l),e.next=27;break;case 13:if(!t.parent.level){e.next=20;break}return e.next=16,we(t.parent.key,t.key);case 16:s=e.sent,r(s),e.next=27;break;case 20:return e.next=22,be(t.key);case 22:return c=e.sent,e.next=25,we(t.key,t.key);case 25:u=e.sent,r([].concat(b(c),b(u)));case 27:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),Ce=function(){var e=E(A().mark((function e(t){var r,n;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.A.relationBookMemberOftenList();case 2:r=e.sent,n=r.data,oe.value=n;case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Be=function(e){Ne(e)},Ne=function(){var e=E(A().mark((function e(t){var r,n;return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.A.relationBookMemberSetOften({isOften:t,userIds:[re.value]});case 2:r=e.sent,n=r.code,200===n&&(Ce(),ke((function(e){var t;null===(t=I.value)||void 0===t||t.store.setData(e)})),(0,h.nk)({message:(t?"收藏为":"移除")+"常用联系人成功！",type:"success"}));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ae=function(){var e=E(A().mark((function e(){return A().wrap((function(e){while(1)switch(e.prev=e.next){case 0:ke((function(e){var t;null===(t=I.value)||void 0===t||t.store.setData(e)}));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,c.onMounted)((function(){Ce()})),w({refresh:Ae}),function(e,t){var r,u,d,p,h=s.Zq,y=l.s9,g=i.q,b=a.kA,w=(0,c.resolveComponent)("Star"),x=o.tk,C=(0,c.resolveComponent)("StarFilled"),B=n.S2;return(0,c.openBlock)(),(0,c.createElementBlock)("div",{class:(0,c.normalizeClass)(["GlobalChatAddressBook",{GlobalChatMacAddressBook:(0,c.unref)(N)}])},[(0,c.createElementVNode)("div",L,[(0,c.createElementVNode)("div",V,[(0,c.createVNode)(y,{modelValue:ee.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return ee.value=e}),"prefix-icon":(0,c.unref)(m.Search),"fetch-suggestions":le,placeholder:"搜索","popper-class":"GlobalChatAddressBookAutocomplete",clearable:"",onSelect:ue},{default:(0,c.withCtx)((function(e){var t=e.item;return[(0,c.createElementVNode)("div",S,[(0,c.createVNode)(h,{src:ae(t.user.photo||t.user.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,c.createElementVNode)("div",T,(0,c.toDisplayString)(t.user.userName),1)])]})),_:1},8,["modelValue","prefix-icon"])]),(0,c.createVNode)(b,{class:"GlobalChatAddressBookScrollbar"},{default:(0,c.withCtx)((function(){return[(0,c.createVNode)(g,{ref_key:"treeRef",ref:I,lazy:"",load:xe,"node-key":"id",props:{isLeaf:"isLeaf"}},{default:(0,c.withCtx)((function(e){var t=e.data;return["user"!==t.type?((0,c.openBlock)(),(0,c.createElementBlock)("div",G,(0,c.toDisplayString)(t.label),1)):(0,c.createCommentVNode)("",!0),"user"===t.type?((0,c.openBlock)(),(0,c.createElementBlock)("div",{key:1,class:(0,c.normalizeClass)(["GlobalChatAddressBookItem",{"is-active":t.id===re.value}]),onClick:function(e){return ue(t)}},[(0,c.createVNode)(h,{src:ae(t.user.photo||t.user.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,c.createElementVNode)("div",O,(0,c.toDisplayString)(t.user.userName),1)],10,_)):(0,c.createCommentVNode)("",!0)]})),_:1},512)]})),_:1})]),re.value?((0,c.openBlock)(),(0,c.createElementBlock)("div",M,[(0,c.createElementVNode)("div",D,[(0,c.createVNode)(h,{src:ae(ne.value.photo||ne.value.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,c.createElementVNode)("div",j,[(0,c.createElementVNode)("div",{class:"GlobalChatAddressBookName ellipsis",onClick:t[3]||(t[3]=function(e){return he(ne.value)})},[(0,c.createTextVNode)((0,c.toDisplayString)(ne.value.userName)+" ",1),"1"===(null===(r=ne.value)||void 0===r||null===(r=r.sex)||void 0===r?void 0:r.value)?((0,c.openBlock)(),(0,c.createElementBlock)("span",{key:0,innerHTML:(0,c.unref)(v.lm)},null,8,U)):(0,c.createCommentVNode)("",!0),"2"===(null===(u=ne.value)||void 0===u||null===(u=u.sex)||void 0===u?void 0:u.value)?((0,c.openBlock)(),(0,c.createElementBlock)("span",{key:1,innerHTML:(0,c.unref)(v.M5)},null,8,P)):(0,c.createCommentVNode)("",!0),(0,c.createElementVNode)("div",R,[null!==(d=oe.value)&&void 0!==d&&d.includes(re.value)?(0,c.createCommentVNode)("",!0):((0,c.openBlock)(),(0,c.createBlock)(x,{key:0,onClick:t[1]||(t[1]=function(e){return Be(1)})},{default:(0,c.withCtx)((function(){return[(0,c.createVNode)(w)]})),_:1})),null!==(p=oe.value)&&void 0!==p&&p.includes(re.value)?((0,c.openBlock)(),(0,c.createBlock)(x,{key:1,class:"is-active",onClick:t[2]||(t[2]=function(e){return Be(0)})},{default:(0,c.withCtx)((function(){return[(0,c.createVNode)(C)]})),_:1})):(0,c.createCommentVNode)("",!0)])]),(0,c.createElementVNode)("div",Y,[(0,c.createElementVNode)("span",{innerHTML:(0,c.unref)(v.gY)},null,8,F),(0,c.createTextVNode)(" "+(0,c.toDisplayString)(ne.value.mobile),1)]),(0,c.createElementVNode)("div",H,(0,c.toDisplayString)(ne.value.position),1)]),(0,c.createVNode)(B,{type:"primary",onClick:ye},{default:(0,c.withCtx)((function(){return t[4]||(t[4]=[(0,c.createTextVNode)("发送消息")])})),_:1})]),(0,c.createVNode)(b,{class:"GlobalChatAddressBookUserScroll"},{default:(0,c.withCtx)((function(){var e,t,r,n,o,a,i;return[(0,c.createElementVNode)("div",q,[(0,c.createElementVNode)("div",z,"民族："+(0,c.toDisplayString)(null===(e=ne.value)||void 0===e||null===(e=e.nation)||void 0===e?void 0:e.label),1),(0,c.createElementVNode)("div",$," 出生年月："+(0,c.toDisplayString)((0,c.unref)(f.G)(ne.value.birthday,"YYYY-MM-DD")),1),(0,c.createElementVNode)("div",J,"籍贯："+(0,c.toDisplayString)(ne.value.nativePlace),1),null!==(t=ne.value.roleIds)&&void 0!==t&&t.includes("1684119713426243586")||null!==(r=ne.value.roleIds)&&void 0!==r&&r.includes("1743150705994133506")||null!==(n=ne.value.roleIds)&&void 0!==n&&n.includes("1643857093725327362")?((0,c.openBlock)(),(0,c.createElementBlock)("div",Z," 办公室电话："+(0,c.toDisplayString)(ne.value.officePhone),1)):(0,c.createCommentVNode)("",!0),null!==(o=ne.value.roleIds)&&void 0!==o&&o.includes("1640259895343255554")?((0,c.openBlock)(),(0,c.createElementBlock)("div",K," 地址："+(0,c.toDisplayString)(ne.value.callAddress),1)):(0,c.createCommentVNode)("",!0),null!==(a=ne.value.roleIds)&&void 0!==a&&a.includes("1640259895343255554")?((0,c.openBlock)(),(0,c.createElementBlock)("div",Q," 个人二维码：")):(0,c.createCommentVNode)("",!0),null!==(i=ne.value.roleIds)&&void 0!==i&&i.includes("1640259895343255554")?((0,c.openBlock)(),(0,c.createElementBlock)("div",W,[(0,c.createVNode)(k["default"],{value:ie.value,"render-as":"svg",level:"L",size:90},null,8,["value"])])):(0,c.createCommentVNode)("",!0)])]})),_:1})])):(0,c.createCommentVNode)("",!0),re.value?(0,c.createCommentVNode)("",!0):((0,c.openBlock)(),(0,c.createElementBlock)("div",X))],2)}}});const re=te;var ne=re}}]);