{"ast": null, "code": "import { defineAsyncComponent } from 'vue';\nvar WorkBenchOne = defineAsyncComponent(function () {\n  return import('./components/WorkBenchOne.vue');\n});\nvar WorkBenchTwo = defineAsyncComponent(function () {\n  return import('./components/WorkBenchTwo.vue');\n});\nexport var WorkBenchElement = {\n  WorkBenchOne,\n  WorkBenchTwo\n};", "map": {"version": 3, "names": ["defineAsyncComponent", "WorkBenchOne", "WorkBenchTwo", "WorkBenchElement"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/WorkBench/WorkBench.js"], "sourcesContent": ["import { defineAsyncComponent } from 'vue'\r\nconst WorkBenchOne = defineAsyncComponent(() => import('./components/WorkBenchOne.vue'))\r\nconst WorkBenchTwo = defineAsyncComponent(() => import('./components/WorkBenchTwo.vue'))\r\nexport const WorkBenchElement = {\r\n  WorkBenchOne,\r\n  WorkBenchTwo\r\n}\r\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,KAAK;AAC1C,IAAMC,YAAY,GAAGD,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,+BAA+B,CAAC;AAAA,EAAC;AACxF,IAAME,YAAY,GAAGF,oBAAoB,CAAC;EAAA,OAAM,MAAM,CAAC,+BAA+B,CAAC;AAAA,EAAC;AACxF,OAAO,IAAMG,gBAAgB,GAAG;EAC9BF,YAAY;EACZC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}