{"ast": null, "code": "export default {\n  state: {\n    PublicSentimentInfo: {},\n    globalOrganizationId: '',\n    globalOrganizationData: null\n  },\n  getters: {\n    getPublicSentimentInfoFn(state) {\n      return state.PublicSentimentInfo;\n    },\n    getGlobalOrganizationId(state) {\n      return state.globalOrganizationId;\n    },\n    getGlobalOrganizationData(state) {\n      return state.globalOrganizationData;\n    }\n  },\n  mutations: {\n    setPublicSentimentInfo(state, data) {\n      state.PublicSentimentInfo[data.key] = data.params;\n    },\n    setGlobalOrganizationId(state, orgId) {\n      state.globalOrganizationId = orgId;\n    },\n    setGlobalOrganizationData(state, orgData) {\n      state.globalOrganizationData = orgData;\n    }\n  }\n};", "map": {"version": 3, "names": ["state", "PublicSentimentInfo", "globalOrganizationId", "globalOrganizationData", "getters", "getPublicSentimentInfoFn", "getGlobalOrganizationId", "getGlobalOrganizationData", "mutations", "setPublicSentimentInfo", "data", "key", "params", "setGlobalOrganizationId", "orgId", "setGlobalOrganizationData", "orgData"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/config/store.js"], "sourcesContent": ["export default {\r\n  state: {\r\n    PublicSentimentInfo: {},\r\n    globalOrganizationId: '',\r\n    globalOrganizationData: null\r\n  },\r\n  getters: {\r\n    getPublicSentimentInfoFn (state) {\r\n      return state.PublicSentimentInfo\r\n    },\r\n    getGlobalOrganizationId (state) {\r\n      return state.globalOrganizationId\r\n    },\r\n    getGlobalOrganizationData (state) {\r\n      return state.globalOrganizationData\r\n    }\r\n  },\r\n  mutations: {\r\n    setPublicSentimentInfo (state, data) {\r\n      state.PublicSentimentInfo[data.key] = data.params\r\n    },\r\n    setGlobalOrganizationId (state, orgId) {\r\n      state.globalOrganizationId = orgId\r\n    },\r\n    setGlobalOrganizationData (state, orgData) {\r\n      state.globalOrganizationData = orgData\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE;IACLC,mBAAmB,EAAE,CAAC,CAAC;IACvBC,oBAAoB,EAAE,EAAE;IACxBC,sBAAsB,EAAE;EAC1B,CAAC;EACDC,OAAO,EAAE;IACPC,wBAAwBA,CAAEL,KAAK,EAAE;MAC/B,OAAOA,KAAK,CAACC,mBAAmB;IAClC,CAAC;IACDK,uBAAuBA,CAAEN,KAAK,EAAE;MAC9B,OAAOA,KAAK,CAACE,oBAAoB;IACnC,CAAC;IACDK,yBAAyBA,CAAEP,KAAK,EAAE;MAChC,OAAOA,KAAK,CAACG,sBAAsB;IACrC;EACF,CAAC;EACDK,SAAS,EAAE;IACTC,sBAAsBA,CAAET,KAAK,EAAEU,IAAI,EAAE;MACnCV,KAAK,CAACC,mBAAmB,CAACS,IAAI,CAACC,GAAG,CAAC,GAAGD,IAAI,CAACE,MAAM;IACnD,CAAC;IACDC,uBAAuBA,CAAEb,KAAK,EAAEc,KAAK,EAAE;MACrCd,KAAK,CAACE,oBAAoB,GAAGY,KAAK;IACpC,CAAC;IACDC,yBAAyBA,CAAEf,KAAK,EAAEgB,OAAO,EAAE;MACzChB,KAAK,CAACG,sBAAsB,GAAGa,OAAO;IACxC;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}