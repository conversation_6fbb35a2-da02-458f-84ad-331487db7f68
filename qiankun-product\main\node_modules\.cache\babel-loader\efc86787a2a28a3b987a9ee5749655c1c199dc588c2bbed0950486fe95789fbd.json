{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"wordCloudChart\",\n  ref: \"elChartRef\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, null, 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiUseStatistics\\common\\wordCloudChart.vue"], "sourcesContent": ["<template>\r\n  <div class=\"wordCloudChart\" ref=\"elChartRef\"></div>\r\n</template>\r\n<script>\r\nexport default { name: 'wordCloudChart' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch, computed, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\nconst props = defineProps({\r\n  color: { type: String, default: '' },\r\n  data: { type: Object, default: () => ({}) }\r\n})\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst chartData = computed(() => props.data?.map((v, i) => ({ ...v, name: v.hotWord, value: v.appearTimes })) || [])\r\nconst randcolor = () => {\r\n  const r = Math.random() * 255\r\n  const g = Math.random() * 255\r\n  const b = Math.random() * 255\r\n  return `rgb(${r}, ${g}, ${b})`\r\n}\r\nconst initChart = () => {\r\n  if (!elChart) {\r\n    elChart = echarts.init(elChartRef.value)\r\n  }\r\n  const setOption = {\r\n    tooltip: {\r\n      trigger: 'item',\r\n      formatter(params) {\r\n        const { marker, data } = params\r\n        return `<div class=\"WordCloudChartTooltip\">\r\n          <div class=\"WordCloudChartName\">${marker}${data.name}</div>\r\n          <div class=\"WordCloudChartText\"><span>数量：${data.value}</span></div>\r\n        </div>`\r\n      }\r\n    },\r\n    series: [\r\n      {\r\n        type: 'wordCloud',\r\n        gridSize: 20,\r\n        sizeRange: [12, 32],\r\n        rotationRange: [0, 0],\r\n        width: '100%',\r\n        height: '100%',\r\n        shape: 'circle',\r\n        textStyle: { color: () => randcolor() },\r\n        data: chartData.value\r\n      }\r\n    ]\r\n  }\r\n  elChart.setOption(setOption)\r\n}\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    nextTick(() => {\r\n      initChart()\r\n    })\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.wordCloudChart {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n\r\n  & > div {\r\n    z-index: 3;\r\n  }\r\n\r\n  .WordCloudChartTooltip {\r\n    min-width: 180px;\r\n\r\n    .WordCloudChartName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-primary);\r\n      padding-left: 16px;\r\n      position: relative;\r\n\r\n      div {\r\n        display: inline-block;\r\n        font-weight: normal;\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n      }\r\n\r\n      span {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #ffffff;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .WordCloudChartText {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: var(--zy-font-name-distance-five);\r\n\r\n      span + span {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,gBAAgB;EAACC,GAAG,EAAC;;;uBAAhCC,mBAAA,CAAmD,OAAnDC,UAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}