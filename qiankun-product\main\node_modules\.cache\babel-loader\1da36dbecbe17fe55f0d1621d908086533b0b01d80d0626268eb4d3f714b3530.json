{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ChartOne\",\n  ref: \"elChartRef\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, null, 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\PublicSentimentInfo\\components\\ChartOne.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ChartOne\" ref=\"elChartRef\">\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChartOne' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport * as echarts from 'echarts'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst erd = elementResizeDetectorMaker()\r\nconst ticketType = ref('')\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst tableData = ref([])\r\nconst stateData = computed(() => store.getters.getPublicSentimentInfoFn)\r\nconst initChart = () => {\r\n  if (!elChart) { elChart = echarts.init(elChartRef.value) }\r\n  var colorListLabel = [\r\n    'rgb(148, 248, 199)',\r\n    'rgb(204, 218, 245)',\r\n    'rgb(173, 219, 246)',\r\n    'rgb(250, 231, 177)'\r\n  ]\r\n  var colorList = [\r\n    'rgba(0, 138, 255, 1)',\r\n    'rgba(255, 155, 48, 1)',\r\n    'rgba(0, 180, 76, 1)',\r\n    'rgba(151, 101, 254, 1)'\r\n  ]\r\n  var colorLabelTitle = [\r\n    'rgb(121, 240, 182)',\r\n    'rgb(186, 206, 246)',\r\n    'rgb(141, 204, 241)',\r\n    'rgb(250, 220, 135)'\r\n  ]\r\n  //把echartData数据遍历实现不同指线文字背景色\r\n  var seriesData = tableData.value.map((item, index) => {\r\n    return {\r\n      ...item,\r\n      actValue: item.value,\r\n      label: {\r\n        show: true,\r\n        position: 'outside',\r\n        borderRadius: 5,\r\n        padding: [10, -10, 3, -3],\r\n        color: colorList[index],\r\n        textStyle: { fontSize: 12 },\r\n        formatter: '{white|{b}}\\n{yellow|数量：  {c}}\\n{three|占比：  {d}%}',\r\n        rich: {\r\n          white: {\r\n            color: '#000',\r\n            align: 'left',\r\n            fontSize: 16,\r\n            backgroundColor: colorLabelTitle[index],\r\n            width: 160,\r\n            borderRadius: [4, 4, 0, 0],\r\n            padding: [8, 6]\r\n          },\r\n          yellow: {\r\n            color: '#666666',\r\n            align: 'left',\r\n            fontSize: 14,\r\n            padding: [6, 6],\r\n            backgroundColor: colorListLabel[index],\r\n            width: 160,\r\n            borderRadius: [0, 0, 0, 0]\r\n          },\r\n          three: {\r\n            color: '#666666',\r\n            align: 'left',\r\n            fontSize: 14,\r\n            padding: [6, 6],\r\n            backgroundColor: colorListLabel[index],\r\n            width: 160,\r\n            borderRadius: [0, 0, 4, 4]\r\n          }\r\n        }\r\n      }\r\n    }\r\n  })\r\n  const setOption = {\r\n    tooltip: {\r\n      trigger: 'item',\r\n      formatter (params) {\r\n        return `<div class=\"ColumnChartTooltip\">\r\n          <div class=\"ColumnChartName\">${params.marker}${params.name}</div>\r\n          <div class=\"ColumnChartText\"><span>数量：${params.value}</span></div>\r\n          <div class=\"ColumnChartText\"><span>数量：${params.percent}%</span></div>\r\n        </div>`\r\n      },\r\n      textStyle: { fontSize: 14 }\r\n    },\r\n    series: [\r\n      {\r\n        name: 'pie',\r\n        type: 'pie',\r\n        radius: ['32%', '52%'],\r\n        hoverAnimation: false,\r\n        color: ['#14cf75', '#73a0fa', '#17a6fa', '#fcc529'],\r\n        itemStyle: { //饼图之间颜色\r\n          normal: { borderColor: '#fff', borderWidth: 4 }\r\n        },\r\n        labelLine: {\r\n          length: 12, //视觉引导线第一段的长度\r\n          length2: 82 //视觉引导线第二段的长度\r\n        },\r\n        data: seriesData\r\n      }\r\n    ]\r\n  }\r\n  elChart.setOption(setOption)\r\n}\r\nconst publicChartAnalysis = async () => {\r\n  if (stateData.value[`ChartOne-${ticketType.value}`]) {\r\n    tableData.value = stateData.value[`ChartOne-${ticketType.value}`]\r\n  } else {\r\n    const { data } = await api.publicChartAnalysis({ ticketType: ticketType.value, chartType: '1' })\r\n    tableData.value = data?.charts?.map(v => ({ key: v.key, name: v.name, value: v.num, percent: v.percent })) || []\r\n    store.commit('setPublicSentimentInfo', { key: `ChartOne-${ticketType.value}`, params: tableData.value })\r\n  }\r\n  initChart()\r\n  nextTick(() => {\r\n    erd.listenTo(elChartRef.value, () => {\r\n      elChart.resize()\r\n    })\r\n  })\r\n}\r\nonMounted(() => {\r\n  ticketType.value = route.query.ticketType || '2'\r\n  publicChartAnalysis()\r\n})\r\nonUnmounted(() => { erd.uninstall(elChartRef.value) })\r\n</script>\r\n<style lang=\"scss\">\r\n.ChartOne {\r\n  width: 100%;\r\n  height: 320px;\r\n\r\n  .ColumnChartTooltip {\r\n    min-width: 180px;\r\n\r\n    .ColumnChartName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-primary);\r\n      padding-left: 16px;\r\n      position: relative;\r\n\r\n      span {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #FFFFFF;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ColumnChartText {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: var(--zy-font-name-distance-five);\r\n\r\n      span+span {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,UAAU;EAACC,GAAG,EAAC;;;uBAA1BC,mBAAA,CACM,OADNC,UACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}