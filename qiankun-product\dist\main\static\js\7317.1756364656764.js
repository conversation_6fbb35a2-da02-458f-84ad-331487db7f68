"use strict";(self["webpackChunkmain"]=self["webpackChunkmain"]||[]).push([[7317],{77317:function(e,t,r){r.r(t),r.d(t,{default:function(){return R}});var n=r(79590),o=(r(76945),r(88810),r(81474)),a=(r(64352),r(62427)),i=(r(98773),r(44863)),u=(r(4711),r(71928)),l=(r(31584),r(49744)),c=(r(98326),r(15934),r(44917)),s=(r(40065),r(84098)),d=(r(63584),r(74061)),f=r(4955),p=r(59335),v=r(88609),h=r(24652);function m(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */m=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var a=t&&t.prototype instanceof b?t:b,i=Object.create(a.prototype),u=new A(n||[]);return o(i,"_invoke",{value:L(e,r,u)}),i}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var f="suspendedStart",p="suspendedYield",v="executing",h="completed",y={};function b(){}function g(){}function w(){}var k={};c(k,i,(function(){return this}));var x=Object.getPrototypeOf,N=x&&x(x(B([])));N&&N!==r&&n.call(N,i)&&(k=N);var G=w.prototype=b.prototype=Object.create(k);function E(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function r(o,a,i,u){var l=d(e[o],e,a);if("throw"!==l.type){var c=l.arg,s=c.value;return s&&"object"==typeof s&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,i,u)}),(function(e){r("throw",e,i,u)})):t.resolve(s).then((function(e){c.value=e,i(c)}),(function(e){return r("throw",e,i,u)}))}u(l.arg)}var a;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return a=a?a.then(o,o):o()}})}function L(t,r,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var l=V(u,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=d(t,r,n);if("normal"===c.type){if(o=n.done?h:p,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function V(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,V(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=d(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function U(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function B(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return g.prototype=w,o(G,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:g,configurable:!0}),g.displayName=c(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,c(e,l,"GeneratorFunction")),e.prototype=Object.create(G),e},t.awrap=function(e){return{__await:e}},E(I.prototype),c(I.prototype,u,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,n,o,a){void 0===a&&(a=Promise);var i=new I(s(e,r,n,o),a);return t.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},E(G),c(G,l,"Generator"),c(G,i,(function(){return this})),c(G,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=B,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(U),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return u.type="throw",u.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),U(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;U(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:B(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function y(e){return k(e)||w(e)||g(e)||b()}function b(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function g(e,t){if(e){if("string"==typeof e)return x(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?x(e,t):void 0}}function w(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function k(e){if(Array.isArray(e))return x(e)}function x(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function N(e,t,r,n,o,a,i){try{var u=e[a](i),l=u.value}catch(e){return void r(e)}u.done?t(l):Promise.resolve(l).then(n,o)}function G(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var a=e.apply(t,r);function i(e){N(a,n,o,i,u,"next",e)}function u(e){N(a,n,o,i,u,"throw",e)}i(void 0)}))}}var E={class:"GlobalGroupAddUser"},I={class:"GlobalGroupAddUserList"},L={class:"GlobalGroupAddUserInput"},V={key:0,class:"GlobalGroupAddUserLabel"},C={class:"GlobalGroupAddUserItem"},U={class:"GlobalGroupAddUserName ellipsis"},A={key:0,class:"GlobalGroupAddUserLabel"},B={class:"GlobalGroupAddUserItem"},S={class:"GlobalGroupAddUserName ellipsis"},_={class:"GlobalGroupAddUserBody"},j={class:"GlobalGroupAddUserInfo"},O={class:"GlobalGroupAddUserInfoName"},T={class:"GlobalGroupAddUserUserBody"},P=["onClick"],$={class:"GlobalGroupAddUserUserName ellipsis"},D={class:"GlobalGroupAddUserButton"},F={name:"GlobalGroupAddUser"},M=Object.assign(F,{props:{infoId:{type:String,default:""}},emits:["callback"],setup(e,t){var r=t.emit,b=(0,p.useStore)(),g=e,w=r,k=(0,d.computed)((function(){return b.getters.getRongCloudUrl})),x=(0,d.computed)((function(){return b.getters.getIsPrivatization})),N=(0,d.ref)({}),F=(0,d.ref)(""),M=(0,d.ref)([]),Q=(0,d.ref)(!1),R=(0,d.ref)([]),q=(0,d.ref)([]),z=(0,d.ref)([]),Y=(0,d.ref)([]),K=function(e){return e?f.A.fileURL(e):f.A.defaultImgURL("default_user_head.jpg")},W=function(e,t){var r=0;return function(){var n=Date.now();if(n-r>=t){for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];e.apply(this,a),r=n}}},Z=W(G(m().mark((function e(){var t,r,n,o;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(Q.value=!!F.value,t=[],!F.value){e.next=13;break}r=0;case 4:if(!(r<M.value.length)){e.next=13;break}return n=M.value[r],e.next=8,H(n.id);case 8:o=e.sent,t=[].concat(y(t),y(o));case 10:r++,e.next=4;break;case 13:Q.value=!1,R.value=y(new Map(t.map((function(e){return[e.id,e]}))).values());case 15:case"end":return e.stop()}}),e)}))),300),H=function(){var e=G(m().mark((function e(t){var r,n,o,a,i;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,f.A.SelectPersonBookUser({isOpen:1,keyword:F.value,labelCode:t,nodeId:"",relationBookId:"",tabCode:"relationBooksTemp"});case 3:for(r=e.sent,n=r.data,o=[],a=0;a<n.length;a++)i=n[a],i.accountId&&o.push({id:i.accountId,label:i.userName,children:[],type:"user",user:i,isLeaf:!0});return e.abrupt("return",o);case 10:return e.prev=10,e.t0=e["catch"](0),e.abrupt("return",[]);case 13:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(t){return e.apply(this,arguments)}}(),J=function(e){var t;null!==(t=z.value)&&void 0!==t&&t.includes(e.id)?Y.value.push(e):Y.value=Y.value.filter((function(t){return t.id!==e.id}))},X=function(e){z.value=z.value.filter((function(t){return t!==e.id})),Y.value=Y.value.filter((function(t){return t.id!==e.id}))},ee=function(){var e=G(m().mark((function e(){var t,r;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.chatGroupEdit({form:{id:g.infoId,groupName:N.value.groupName},ownerUserId:N.value.ownerUserId,memberUserIds:z.value});case 2:t=e.sent,r=t.code,200===r&&te();case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),te=function(){var e=G(m().mark((function e(){var t,r,n,o,a,i,u,l;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.rongCloud(k.value,{type:"joinGroup",userIds:Y.value.map((function(e){return`${v.TC.value}${e.id}`})).join(","),groupId:`${v.TC.value}${g.infoId}`,groupName:N.value.groupName,environment:1},x.value);case 2:t=e.sent,r=t.code,200===r&&(i=Y.value.map((function(e){return e.label})).join("、"),u=Y.value.map((function(e){return`||${e.label}|OUI|${e.id}||`})).join("、"),l={name:`${null===(n=v.kQ.value)||void 0===n?void 0:n.userName} 将 ${i} 加入群聊`,data:`${null===(o=v.kQ.value)||void 0===o?void 0:o.userName}|OUI|${null===(a=v.kQ.value)||void 0===a?void 0:a.accountId}|| 将 ${u} 加入群聊`},w("callback",!0,l));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),re=function(){w("callback",!1)},ne=function(){var e=G(m().mark((function e(t){var r,n,o,a,i,u,l;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.SelectPersonTab({tabCodes:["relationBooksTemp"]});case 2:for(r=e.sent,n=r.data,o=[],a=0;a<(null===(i=n[0])||void 0===i?void 0:i.chooseLabels.length);a++)l=null===(u=n[0])||void 0===u?void 0:u.chooseLabels[a],o.push({id:l.labelCode,label:l.name,children:[],type:"label",isLeaf:!1});M.value=o,t(o);case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),oe=function(e,t){for(var r=[],n=0;n<t.length;n++){var o=t[n];if(o.code!==e){var a=oe(e,o.children);r.push({id:o.code,label:o.name,children:a,type:"tree",isLeaf:!1})}}return r},ae=function(){var e=G(m().mark((function e(t){var r,n;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.SelectPersonGroup({labelCode:t,tabCode:"relationBooksTemp"});case 2:return r=e.sent,n=r.data,e.abrupt("return",oe(t,n));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ie=function(){var e=G(m().mark((function e(t,r){var n,o,a,i,u;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.SelectPersonBookUser({isOpen:1,keyword:"",labelCode:t,nodeId:r,relationBookId:r,tabCode:"relationBooksTemp"});case 2:for(n=e.sent,o=n.data,a=[],i=0;i<o.length;i++)u=o[i],u.accountId&&a.push({id:u.accountId,label:u.userName,children:[],type:"user",user:u,isLeaf:!0});return e.abrupt("return",a);case 7:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),ue=function(){var e=G(m().mark((function e(t,r){var n,o,a,i,u,l,c;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!==t.level){e.next=4;break}ne(r),e.next=24;break;case 4:if(null===(n=t.data)||void 0===n||null===(n=n.children)||void 0===n||!n.length){e.next=13;break}return a=null===(o=t.data)||void 0===o?void 0:o.children,e.next=8,ie(t.parent.key,t.key);case 8:i=e.sent,u=[].concat(y(a),y(i)),r(u),e.next=24;break;case 13:if(!t.parent.level){e.next=20;break}return e.next=16,ie(t.parent.key,t.key);case 16:l=e.sent,r(l),e.next=24;break;case 20:return e.next=22,ae(t.key);case 22:c=e.sent,r(c);case 24:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),le=function(){var e=G(m().mark((function e(){var t,r;return m().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f.A.chatGroupInfo({detailId:g.infoId});case 2:t=e.sent,r=t.data,N.value=r,q.value=r.memberUserIds,z.value=r.memberUserIds;case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,d.onMounted)((function(){le()})),function(e,t){var r=s.WK,f=c.Zq,p=u.dI,v=l.q,m=u.o5,y=i.kA,b=(0,d.resolveComponent)("CircleCloseFilled"),g=a.tk,w=o.S2,k=n.L;return(0,d.openBlock)(),(0,d.createElementBlock)("div",E,[(0,d.createElementVNode)("div",I,[(0,d.createElementVNode)("div",L,[(0,d.createVNode)(r,{modelValue:F.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return F.value=e}),"prefix-icon":(0,d.unref)(h.Search),placeholder:"搜索",onInput:(0,d.unref)(Z),clearable:""},null,8,["modelValue","prefix-icon","onInput"])]),(0,d.withDirectives)(((0,d.openBlock)(),(0,d.createBlock)(y,{class:"GlobalGroupAddUserScrollbar"},{default:(0,d.withCtx)((function(){return[(0,d.withDirectives)((0,d.createVNode)(m,{modelValue:z.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return z.value=e})},{default:(0,d.withCtx)((function(){return[(0,d.createVNode)(v,{"node-key":"id",data:R.value},{default:(0,d.withCtx)((function(e){var t,r=e.data;return["user"!==r.type?((0,d.openBlock)(),(0,d.createElementBlock)("div",V,(0,d.toDisplayString)(r.label),1)):(0,d.createCommentVNode)("",!0),"user"===r.type?((0,d.openBlock)(),(0,d.createBlock)(p,{key:1,value:r.id,disabled:null===(t=q.value)||void 0===t?void 0:t.includes(r.id),onChange:function(e){return J(r)}},{default:(0,d.withCtx)((function(){return[(0,d.createElementVNode)("div",C,[(0,d.createVNode)(f,{src:K(r.user.photo||r.user.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,d.createElementVNode)("div",U,(0,d.toDisplayString)(r.user.userName),1)])]})),_:2},1032,["value","disabled","onChange"])):(0,d.createCommentVNode)("",!0)]})),_:1},8,["data"])]})),_:1},8,["modelValue"]),[[d.vShow,!!F.value]]),(0,d.withDirectives)((0,d.createVNode)(m,{modelValue:z.value,"onUpdate:modelValue":t[2]||(t[2]=function(e){return z.value=e})},{default:(0,d.withCtx)((function(){return[(0,d.createVNode)(v,{lazy:"",load:ue,"node-key":"id",props:{isLeaf:"isLeaf"}},{default:(0,d.withCtx)((function(e){var t,r=e.data;return["user"!==r.type?((0,d.openBlock)(),(0,d.createElementBlock)("div",A,(0,d.toDisplayString)(r.label),1)):(0,d.createCommentVNode)("",!0),"user"===r.type?((0,d.openBlock)(),(0,d.createBlock)(p,{key:1,value:r.id,disabled:null===(t=q.value)||void 0===t?void 0:t.includes(r.id),onChange:function(e){return J(r)}},{default:(0,d.withCtx)((function(){return[(0,d.createElementVNode)("div",B,[(0,d.createVNode)(f,{src:K(r.user.photo||r.user.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,d.createElementVNode)("div",S,(0,d.toDisplayString)(r.user.userName),1)])]})),_:2},1032,["value","disabled","onChange"])):(0,d.createCommentVNode)("",!0)]})),_:1})]})),_:1},8,["modelValue"]),[[d.vShow,!F.value]])]})),_:1})),[[k,Q.value]])]),(0,d.createElementVNode)("div",_,[(0,d.createElementVNode)("div",j,[(0,d.createElementVNode)("div",O,[t[3]||(t[3]=(0,d.createTextVNode)("选择联系人 ")),(0,d.createElementVNode)("span",null,"已选择"+(0,d.toDisplayString)(Y.value.length)+"位联系人",1)])]),(0,d.createVNode)(y,{class:"GlobalGroupAddUserUserScroll"},{default:(0,d.withCtx)((function(){return[(0,d.createElementVNode)("div",T,[((0,d.openBlock)(!0),(0,d.createElementBlock)(d.Fragment,null,(0,d.renderList)(Y.value,(function(e){var t;return(0,d.openBlock)(),(0,d.createElementBlock)("div",{class:"GlobalGroupAddUserUser",key:e.id},[null!==(t=q.value)&&void 0!==t&&t.includes(e.id)?(0,d.createCommentVNode)("",!0):((0,d.openBlock)(),(0,d.createElementBlock)("div",{key:0,class:"GlobalGroupAddUserUserDel",onClick:function(t){return X(e)}},[(0,d.createVNode)(g,null,{default:(0,d.withCtx)((function(){return[(0,d.createVNode)(b)]})),_:1})],8,P)),(0,d.createVNode)(f,{src:K(e.user.photo||e.user.headImg),fit:"cover",draggable:"false"},null,8,["src"]),(0,d.createElementVNode)("div",$,(0,d.toDisplayString)(e.user.userName),1)])})),128))])]})),_:1}),(0,d.createElementVNode)("div",D,[(0,d.createVNode)(w,{onClick:re},{default:(0,d.withCtx)((function(){return t[4]||(t[4]=[(0,d.createTextVNode)("取消")])})),_:1}),(0,d.createVNode)(w,{type:"primary",onClick:ee,disabled:!Y.value.length},{default:(0,d.withCtx)((function(){return t[5]||(t[5]=[(0,d.createTextVNode)("完成")])})),_:1},8,["disabled"])])])])}}});const Q=M;var R=Q}}]);