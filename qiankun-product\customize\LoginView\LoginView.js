import api from '@/api'
import { reactive, ref, computed } from 'vue'
import { useStore } from 'vuex'
import md5 from 'crypto-js/md5'
import utils from 'common/js/utils.js'
import { appOnlyHeader } from 'common/js/system_var.js'
import { ElMessage } from 'element-plus'
export const LoginView = (codeValue = '', queryValue = {}) => {
  const store = useStore()
  const openConfig = computed(() => store.getters.getReadOpenConfig)
  const uniqueId = ref('')
  const localityId = ref('')
  const loginAccount = ref('')
  const loginVerifyShow = ref(false)
  const whetherVerifyCode = ref(false)
  const loading = ref(false)
  const checked = ref(false)
  const imgList = ref([])
  const LoginForm = ref()
  const form = reactive({ account: '', password: '', verifyCode: '' })
  const rules = reactive({
    account: [{ required: true, message: '请输入账号/手机号', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    verifyCode: [{ required: false, message: '请输入验证码', trigger: 'blur' }]
  })
  const slideVerify = ref()
  const disabled = ref(false)
  const verifyCodeId = ref('')
  const countDownText = ref('获取验证码')
  const countDown = ref(0)
  const timer = ref()
  const loginQrcode = ref('')
  const loginQrcodeId = ref('')
  const loginQrcodeShow = ref(false)
  const loginDisabled = ref(true)
  const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      var r = (Math.random() * 16) | 0,
        v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }
  const generateUniqueId = (type) => {
    var navigatorInfo = window.navigator.userAgent // 获取用户代理信息
    var screenWidth = window.screen.width // 获取屏幕宽度
    var screenHeight = window.screen.height // 获取屏幕高度
    var language = window.navigator.language // 获取浏览器语言
    var platform = window.navigator.platform // 获取操作系统平台
    localityId.value = type ? guid() : localStorage.getItem('localityId') || guid()
    loginVerifyShow.value =
      loginAccount.value && form.account
        ? !(loginAccount.value === form.account && localStorage.getItem('localityId'))
        : true
    return (
      navigatorInfo + '-' + screenWidth + '-' + screenHeight + '-' + language + '-' + platform + '-' + localityId.value
    )
  }
  const handleBlur = () => {
    loginDisabled.value = true
    if (form.account) {
      verifyLoginCode()
    } else {
      loginVerifyShow.value = false
      whetherVerifyCode.value = false
      rules.verifyCode = [{ required: false, message: '请输入验证码', trigger: 'blur' }]
    }
  }

  const globalData = async () => {
    const { data } = await api.loginImg()
    data.forEach((item) => {
      item.imgPath = api.fileURL(item.imgPath)
    })
    imgList.value = data
  }

  const verifyLoginCode = async (type = false) => {
    uniqueId.value = generateUniqueId(type)
    const { data } = await api.verifyLoginCode({ account: form.account, pcMachineCode: md5(uniqueId.value).toString() })
    loginDisabled.value = false
    whetherVerifyCode.value = data
    if (data) loginVerifyShow.value = data
    rules.verifyCode = [{ required: data, message: '请输入验证码', trigger: 'blur' }]
  }
  const handleGetVerifyCode = async () => {
    if (!form.account) return ElMessage({ type: 'warning', message: '请输入账号/手机号！' })
    const { data, code } = await api.openVerifyCodeSend({ mobile: form.account, sendType: 'no_login' })
    if (code === 200) {
      verifyCodeId.value = data
      countDown.value = 60
      handleCountDown()
      ElMessage({ type: 'success', message: '短信验证码已发送！' })
    }
  }
  const handleCountDown = () => {
    if (countDown.value === 0) {
      countDownText.value = '获取验证码'
      countDown.value = 60
      return
    } else {
      countDownText.value = '重新发送' + countDown.value + 'S'
      countDown.value--
    }
    setTimeout(() => {
      handleCountDown()
    }, 1000)
  }
  const onAgain = () => {
    ElMessage.error('检测到非人为操作的哦！!')
    handleFefresh()
  }
  const onSuccess = () => {
    disabled.value = true
  }
  const handleFefresh = () => {
    disabled.value = false
    slideVerify.value?.refresh()
  }
  const submitForm = async (formEl, type) => {
    if (!formEl) return
    loading.value = true
    await formEl.validate((valid) => {
      if (valid) {
        if (!loginVerifyShow.value || disabled.value || whetherVerifyCode.value) {
          login(type)
        } else {
          loading.value = false
          ElMessage({ type: 'warning', message: '请先通过验证在登录！' })
        }
      } else {
        loading.value = false
      }
    })
  }
  const login = async (type) => {
    try {
      const { data } = await api.login({
        grant_type: 'password',
        username: form.account,
        password: utils.encrypt(form.password, new Date().getTime(), '1'),
        pcMachineCode: md5(uniqueId.value).toString(),
        ...(whetherVerifyCode.value ? { verifyCodeId: verifyCodeId.value, verifyCode: form.verifyCode } : {})
      })
      ElMessage({ message: '登录成功！', showClose: true, type: 'success' })
      if (data.passwordElementErrorMessage)
        ElMessage({ message: data.passwordElementErrorMessage, showClose: true, type: 'info' })
      if (codeValue === '/UnifyLogin') {
        handleAuthorize(data.token)
      } else {
        sessionStorage.setItem('token', data.token)
        sessionStorage.setItem('refresh_token', `bearer ${data.refreshToken.value}`)
        sessionStorage.setItem('expires', data.expires_in)
        sessionStorage.setItem('expiration', data.refreshToken.expiration)
        sessionStorage.setItem('verify', data.passwordElementMatchReg ? 1 : 0)
        if (openConfig.value?.whetherRegionSelect === 'true') loginLastAreaId()
        store.dispatch('loginUser', type ? '/GlobalLayoutChat' : 'login')
        localStorage.setItem('goal_login_router_path', codeValue)
        localStorage.setItem('goal_login_router_query', JSON.stringify(queryValue))
      }
      const loginUserInfo = {
        account: utils.encrypt(form.account, new Date().getTime(), '3'),
        password: utils.encrypt(form.password, new Date().getTime(), '2'),
        checked: checked.value
      }
      localStorage.setItem('localityId', localityId.value)
      localStorage.setItem('loginUserInfo', JSON.stringify(loginUserInfo))
    } catch {
      loading.value = false
      handleFefresh()
    }
  }
  const loginLastAreaId = async () => {
    try {
      const { data } = await api.loginLastAreaId()
      if (data?.id) sessionStorage.setItem('oldRegionInfo', JSON.stringify(data))
    } catch {
      loading.value = false
      handleFefresh()
    }
  }
  const loginInfo = () => {
    const loginUserInfo = JSON.parse(localStorage.getItem('loginUserInfo')) || ''
    loginAccount.value = loginUserInfo ? utils.decrypt(loginUserInfo.account, new Date().getTime(), '3') : ''
    if (loginUserInfo && loginUserInfo.checked) {
      checked.value = loginUserInfo.checked
      form.account = utils.decrypt(loginUserInfo.account, new Date().getTime(), '3')
      form.password = utils.decrypt(loginUserInfo.password, new Date().getTime(), '2')
      verifyLoginCode()
    }
  }
  const refresh = () => {
    clearTimeout(timer.value)
    loginQrcodeShow.value = false
    loginQrcodeId.value = guid()
    loginQrcode.value = `${appOnlyHeader.value}|login|${loginQrcodeId.value}`
    setTimeout(() => {
      loginQrcodeShow.value = true
    }, 180000)
    appToken()
  }
  const hideQrcode = () => {
    clearTimeout(timer.value)
  }
  const appToken = async () => {
    const { data } = await api.appToken({ qrCodeId: loginQrcodeId.value })
    if (!data?.token && !loginQrcodeShow.value) {
      timer.value = setTimeout(() => {
        appToken()
      }, 2000)
    }
    if (data?.token) {
      clearTimeout(timer.value)
      ElMessage({ message: '登录成功！', showClose: true, type: 'success' })
      if (codeValue === '/UnifyLogin') {
        handleAuthorize(data.token)
      } else {
        localStorage.setItem('goal_login_router_path', codeValue)
        localStorage.setItem('goal_login_router_query', queryValue)
        sessionStorage.setItem('token', data.token)
        store.dispatch('loginUser', 'login')
      }
    }
  }
  const handleAuthorize = (token) => {
    const client_id = sessionStorage.getItem('client_id')
    const redirect_uri = sessionStorage.getItem('redirect_uri')
    const params = `client_id=${client_id}&redirect_uri=${redirect_uri}&Authorization=${token}`
    window.open(api.authorize(params), '_self', '', true)
  }
  return {
    loginVerifyShow,
    whetherVerifyCode,
    loginDisabled,
    loading,
    checked,
    imgList,
    LoginForm,
    form,
    rules,
    countDownText,
    slideVerify,
    disabled,
    loginQrcode,
    loginQrcodeShow,
    handleBlur,
    handleGetVerifyCode,
    onAgain,
    onSuccess,
    submitForm,
    globalData,
    verifyLoginCode,
    loginInfo,
    refresh,
    hideQrcode
  }
}
