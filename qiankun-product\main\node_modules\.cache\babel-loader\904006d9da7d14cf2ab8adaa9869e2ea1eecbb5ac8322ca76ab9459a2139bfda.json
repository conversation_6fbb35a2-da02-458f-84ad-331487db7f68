{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { ref, watch, computed, nextTick } from 'vue';\nimport * as echarts from 'echarts';\nvar __default__ = {\n  name: 'wordCloudChart'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    color: {\n      type: String,\n      default: ''\n    },\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var elChart = null;\n    var elChartRef = ref();\n    var chartData = computed(function () {\n      var _props$data;\n      return ((_props$data = props.data) === null || _props$data === void 0 ? void 0 : _props$data.map(function (v, i) {\n        return _objectSpread(_objectSpread({}, v), {}, {\n          name: v.hotWord,\n          value: v.appearTimes\n        });\n      })) || [];\n    });\n    var randcolor = function randcolor() {\n      var r = Math.random() * 255;\n      var g = Math.random() * 255;\n      var b = Math.random() * 255;\n      return `rgb(${r}, ${g}, ${b})`;\n    };\n    var initChart = function initChart() {\n      if (!elChart) {\n        elChart = echarts.init(elChartRef.value);\n      }\n      var setOption = {\n        tooltip: {\n          trigger: 'item',\n          formatter(params) {\n            var marker = params.marker,\n              data = params.data;\n            return `<div class=\"WordCloudChartTooltip\">\n          <div class=\"WordCloudChartName\">${marker}${data.name}</div>\n          <div class=\"WordCloudChartText\"><span>数量：${data.value}</span></div>\n        </div>`;\n          }\n        },\n        series: [{\n          type: 'wordCloud',\n          gridSize: 20,\n          sizeRange: [12, 32],\n          rotationRange: [0, 0],\n          width: '100%',\n          height: '100%',\n          shape: 'circle',\n          textStyle: {\n            color: function color() {\n              return randcolor();\n            }\n          },\n          data: chartData.value\n        }]\n      };\n      elChart.setOption(setOption);\n    };\n    watch(function () {\n      return props.data;\n    }, function () {\n      nextTick(function () {\n        initChart();\n      });\n    }, {\n      immediate: true\n    });\n    var __returned__ = {\n      props,\n      get elChart() {\n        return elChart;\n      },\n      set elChart(v) {\n        elChart = v;\n      },\n      elChartRef,\n      chartData,\n      randcolor,\n      initChart,\n      ref,\n      watch,\n      computed,\n      nextTick,\n      get echarts() {\n        return echarts;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "watch", "computed", "nextTick", "echarts", "__default__", "name", "props", "__props", "<PERSON><PERSON><PERSON>", "elChartRef", "chartData", "_props$data", "data", "map", "v", "i", "_objectSpread", "hotWord", "value", "appearTimes", "randcolor", "r", "Math", "random", "g", "b", "initChart", "init", "setOption", "tooltip", "trigger", "formatter", "params", "marker", "series", "type", "gridSize", "sizeRange", "rotation<PERSON>ange", "width", "height", "shape", "textStyle", "color", "immediate"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/AiUseStatistics/common/wordCloudChart.vue"], "sourcesContent": ["<template>\r\n  <div class=\"wordCloudChart\" ref=\"elChartRef\"></div>\r\n</template>\r\n<script>\r\nexport default { name: 'wordCloudChart' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch, computed, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\nconst props = defineProps({\r\n  color: { type: String, default: '' },\r\n  data: { type: Object, default: () => ({}) }\r\n})\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst chartData = computed(() => props.data?.map((v, i) => ({ ...v, name: v.hotWord, value: v.appearTimes })) || [])\r\nconst randcolor = () => {\r\n  const r = Math.random() * 255\r\n  const g = Math.random() * 255\r\n  const b = Math.random() * 255\r\n  return `rgb(${r}, ${g}, ${b})`\r\n}\r\nconst initChart = () => {\r\n  if (!elChart) {\r\n    elChart = echarts.init(elChartRef.value)\r\n  }\r\n  const setOption = {\r\n    tooltip: {\r\n      trigger: 'item',\r\n      formatter(params) {\r\n        const { marker, data } = params\r\n        return `<div class=\"WordCloudChartTooltip\">\r\n          <div class=\"WordCloudChartName\">${marker}${data.name}</div>\r\n          <div class=\"WordCloudChartText\"><span>数量：${data.value}</span></div>\r\n        </div>`\r\n      }\r\n    },\r\n    series: [\r\n      {\r\n        type: 'wordCloud',\r\n        gridSize: 20,\r\n        sizeRange: [12, 32],\r\n        rotationRange: [0, 0],\r\n        width: '100%',\r\n        height: '100%',\r\n        shape: 'circle',\r\n        textStyle: { color: () => randcolor() },\r\n        data: chartData.value\r\n      }\r\n    ]\r\n  }\r\n  elChart.setOption(setOption)\r\n}\r\nwatch(\r\n  () => props.data,\r\n  () => {\r\n    nextTick(() => {\r\n      initChart()\r\n    })\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.wordCloudChart {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n\r\n  & > div {\r\n    z-index: 3;\r\n  }\r\n\r\n  .WordCloudChartTooltip {\r\n    min-width: 180px;\r\n\r\n    .WordCloudChartName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-primary);\r\n      padding-left: 16px;\r\n      position: relative;\r\n\r\n      div {\r\n        display: inline-block;\r\n        font-weight: normal;\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n      }\r\n\r\n      span {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        transform: translateY(-50%);\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%);\r\n          width: 6px;\r\n          height: 6px;\r\n          background: #ffffff;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .WordCloudChartText {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: var(--zy-font-name-distance-five);\r\n\r\n      span + span {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;AAOA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,KAAK;AACpD,OAAO,KAAKC,OAAO,MAAM,SAAS;AAJlC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAiB,CAAC;;;;;;;;;;;;;;;;;IAKzC,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAIC,OAAO,GAAG,IAAI;IAClB,IAAMC,UAAU,GAAGV,GAAG,CAAC,CAAC;IACxB,IAAMW,SAAS,GAAGT,QAAQ,CAAC;MAAA,IAAAU,WAAA;MAAA,OAAM,EAAAA,WAAA,GAAAL,KAAK,CAACM,IAAI,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAAC,aAAA,CAAAA,aAAA,KAAWF,CAAC;UAAET,IAAI,EAAES,CAAC,CAACG,OAAO;UAAEC,KAAK,EAAEJ,CAAC,CAACK;QAAW;MAAA,CAAG,CAAC,KAAI,EAAE;IAAA,EAAC;IACpH,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;MAC7B,IAAMC,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;MAC7B,IAAME,CAAC,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;MAC7B,OAAO,OAAOF,CAAC,KAAKG,CAAC,KAAKC,CAAC,GAAG;IAChC,CAAC;IACD,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAI,CAAClB,OAAO,EAAE;QACZA,OAAO,GAAGL,OAAO,CAACwB,IAAI,CAAClB,UAAU,CAACS,KAAK,CAAC;MAC1C;MACA,IAAMU,SAAS,GAAG;QAChBC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAASA,CAACC,MAAM,EAAE;YAChB,IAAQC,MAAM,GAAWD,MAAM,CAAvBC,MAAM;cAAErB,IAAI,GAAKoB,MAAM,CAAfpB,IAAI;YACpB,OAAO;AACf,4CAA4CqB,MAAM,GAAGrB,IAAI,CAACP,IAAI;AAC9D,qDAAqDO,IAAI,CAACM,KAAK;AAC/D,eAAe;UACT;QACF,CAAC;QACDgB,MAAM,EAAE,CACN;UACEC,IAAI,EAAE,WAAW;UACjBC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;UACnBC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UACrBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE;YAAEC,KAAK,EAAE,SAAPA,KAAKA,CAAA;cAAA,OAAQvB,SAAS,CAAC,CAAC;YAAA;UAAC,CAAC;UACvCR,IAAI,EAAEF,SAAS,CAACQ;QAClB,CAAC;MAEL,CAAC;MACDV,OAAO,CAACoB,SAAS,CAACA,SAAS,CAAC;IAC9B,CAAC;IACD5B,KAAK,CACH;MAAA,OAAMM,KAAK,CAACM,IAAI;IAAA,GAChB,YAAM;MACJV,QAAQ,CAAC,YAAM;QACbwB,SAAS,CAAC,CAAC;MACb,CAAC,CAAC;IACJ,CAAC,EACD;MAAEkB,SAAS,EAAE;IAAK,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}