{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, with<PERSON><PERSON><PERSON> as _with<PERSON>eys, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, createBlock as _createBlock, withModifiers as _withModifiers } from \"vue\";\nimport _imports_0 from '../img/warning.png';\nimport _imports_1 from '../img/hot.png';\nvar _hoisted_1 = {\n  class: \"news-detail\"\n};\nvar _hoisted_2 = {\n  class: \"top-nav\"\n};\nvar _hoisted_3 = {\n  class: \"nav-content\"\n};\nvar _hoisted_4 = {\n  class: \"nav-left\"\n};\nvar _hoisted_5 = {\n  class: \"search-area\"\n};\nvar _hoisted_6 = {\n  class: \"nav-right\"\n};\nvar _hoisted_7 = {\n  class: \"badge-wrapper\"\n};\nvar _hoisted_8 = {\n  class: \"main-content\"\n};\nvar _hoisted_9 = {\n  class: \"left-section\"\n};\nvar _hoisted_10 = {\n  class: \"hot-categories\"\n};\nvar _hoisted_11 = {\n  class: \"category-list\"\n};\nvar _hoisted_12 = [\"onClick\"];\nvar _hoisted_13 = {\n  class: \"realtime-news\"\n};\nvar _hoisted_14 = {\n  class: \"news-header\"\n};\nvar _hoisted_15 = {\n  class: \"news-list\"\n};\nvar _hoisted_16 = [\"onClick\"];\nvar _hoisted_17 = [\"innerHTML\"];\nvar _hoisted_18 = {\n  class: \"news-meta\"\n};\nvar _hoisted_19 = {\n  class: \"pagination-container\"\n};\nvar _hoisted_20 = {\n  key: 0,\n  class: \"right-section\"\n};\nvar _hoisted_21 = {\n  class: \"article-header\"\n};\nvar _hoisted_22 = [\"innerHTML\"];\nvar _hoisted_23 = {\n  class: \"tags\"\n};\nvar _hoisted_24 = [\"innerHTML\"];\nvar _hoisted_25 = {\n  class: \"article-sections\"\n};\nvar _hoisted_26 = [\"innerHTML\"];\nvar _hoisted_27 = {\n  key: 0,\n  class: \"timeline\"\n};\nvar _hoisted_28 = {\n  class: \"event-content\"\n};\nvar _hoisted_29 = [\"innerHTML\"];\nvar _hoisted_30 = [\"innerHTML\"];\nvar _hoisted_31 = {\n  key: 0,\n  class: \"section\"\n};\nvar _hoisted_32 = {\n  class: \"news-media-list\"\n};\nvar _hoisted_33 = [\"onClick\"];\nvar _hoisted_34 = {\n  class: \"news-media-content\"\n};\nvar _hoisted_35 = [\"innerHTML\"];\nvar _hoisted_36 = {\n  class: \"news-media-date\"\n};\nvar _hoisted_37 = {\n  key: 1,\n  class: \"right-section rigth-notdata\"\n};\nvar _hoisted_38 = {\n  class: \"warning-popup\"\n};\nvar _hoisted_39 = {\n  class: \"popup-header\"\n};\nvar _hoisted_40 = {\n  class: \"popup-title\"\n};\nvar _hoisted_41 = {\n  class: \"warning-count\"\n};\nvar _hoisted_42 = {\n  class: \"popup-content\"\n};\nvar _hoisted_43 = [\"onClick\"];\nvar _hoisted_44 = {\n  class: \"warning-title\"\n};\nvar _hoisted_45 = {\n  class: \"warning-desc\"\n};\nvar _hoisted_46 = {\n  class: \"warning-meta\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_badge = _resolveComponent(\"el-badge\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_el_tag = _resolveComponent(\"el-tag\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"HotspotPushScrollbar\",\n    always: \"\"\n  }, {\n    default: _withCtx(function () {\n      var _$setup$parsedReport$, _$setup$keyword;\n      return [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 顶部导航 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"logo\"\n      }, [_createElementVNode(\"span\", {\n        class: \"focus\"\n      }, \"聚焦\"), _createElementVNode(\"span\", {\n        class: \"hot\"\n      }, \"热点\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_input, {\n        modelValue: $setup.searchText,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.searchText = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        class: \"search-input\",\n        onKeyup: _withKeys($setup.handleSearch, [\"enter\"])\n      }, {\n        suffix: _withCtx(function () {\n          return [_createVNode(_component_el_icon, {\n            onClick: $setup.handleSearch\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode($setup[\"Search\"])];\n            }),\n            _: 1 /* STABLE */\n          })];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])])]), _createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" <span class=\\\"nav-item\\\" :class=\\\"{ active: activeNav === 'domestic' }\\\" @click=\\\"handleNavClick('domestic')\\\">\\r\\n            国内热点\\r\\n          </span>\\r\\n          <span class=\\\"nav-item\\\" :class=\\\"{ active: activeNav === 'international' }\\\"\\r\\n            @click=\\\"handleNavClick('international')\\\">\\r\\n            国际热点\\r\\n          </span> \"), _createElementVNode(\"div\", {\n        class: \"user-info\",\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.showWarningPopup = true;\n        })\n      }, [_cache[7] || (_cache[7] = _createElementVNode(\"img\", {\n        size: 24,\n        src: _imports_0\n      }, null, -1 /* HOISTED */)), _cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n        class: \"warning-info\"\n      }, [_createCommentVNode(\" <el-icon class=\\\"warning-icon\\\">\\r\\n                <Warning />\\r\\n              </el-icon> \"), _createElementVNode(\"span\", {\n        class: \"warning-text\"\n      }, \"预警信息\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_badge, {\n        value: $setup.warning,\n        class: \"badge\",\n        max: 99\n      }, null, 8 /* PROPS */, [\"value\"])])])])])]), _createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" 左侧部分 \"), _createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" 热点分类 \"), _createElementVNode(\"div\", _hoisted_10, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n        class: \"category-header\"\n      }, [_createElementVNode(\"span\", null, \"热点分类\"), _createCommentVNode(\" <div class=\\\"location\\\">\\r\\n              全国\\r\\n              <el-icon>\\r\\n                <ArrowDown />\\r\\n              </el-icon>\\r\\n            </div> \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.categories, function (category, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: index,\n          class: _normalizeClass([\"category-item\", {\n            active: $setup.activeCategory === category\n          }]),\n          onClick: function onClick($event) {\n            return $setup.handleCategoryClick(category, index);\n          }\n        }, _toDisplayString(category), 11 /* TEXT, CLASS, PROPS */, _hoisted_12);\n      }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 实时热点 \"), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n        class: \"header-left\"\n      }, [_createCommentVNode(\" <el-icon>\\r\\n                <Timer />\\r\\n              </el-icon> \"), _createElementVNode(\"img\", {\n        src: _imports_1,\n        width: \"20px\",\n        height: \"20px\",\n        alt: \"\"\n      }), _createElementVNode(\"span\", null, \"实时热点\")], -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: \"refresh\",\n        onClick: $setup.refreshNews\n      }, [_createVNode(_component_el_icon, {\n        class: _normalizeClass({\n          rotating: $setup.isRotating\n        })\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"Refresh\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"class\"]), _cache[10] || (_cache[10] = _createTextVNode(\" 刷新 \"))])]), _createElementVNode(\"div\", _hoisted_15, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.highlightedNewsList, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"news-item\",\n          key: index,\n          onClick: function onClick($event) {\n            return $setup.handleNewsClick(item);\n          }\n        }, [_createElementVNode(\"div\", {\n          class: \"news-title\",\n          innerHTML: item.title\n        }, null, 8 /* PROPS */, _hoisted_17), _createElementVNode(\"div\", _hoisted_18, _toDisplayString(item.date), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_16);\n      }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_pagination, {\n        \"current-page\": $setup.currentPage,\n        \"onUpdate:currentPage\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.currentPage = $event;\n        }),\n        \"page-size\": $setup.pageSize,\n        \"onUpdate:pageSize\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.pageSize = $event;\n        }),\n        \"page-sizes\": $setup.pageSizes,\n        total: $setup.total,\n        layout: \"total, sizes, prev, next, jumper\",\n        onSizeChange: $setup.handleSizeChange,\n        onCurrentChange: $setup.handlePageChange\n      }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"page-sizes\", \"total\"])])])])]), _createCommentVNode(\" 右侧部分 \"), $setup.parsedReport.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"h1\", {\n        innerHTML: $setup.highlightTextNew((_$setup$parsedReport$ = $setup.parsedReport[0]) === null || _$setup$parsedReport$ === void 0 || (_$setup$parsedReport$ = _$setup$parsedReport$.subsections[0]) === null || _$setup$parsedReport$ === void 0 || (_$setup$parsedReport$ = _$setup$parsedReport$.items[0]) === null || _$setup$parsedReport$ === void 0 ? void 0 : _$setup$parsedReport$.content)\n      }, null, 8 /* PROPS */, _hoisted_22), _createElementVNode(\"div\", _hoisted_23, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList((_$setup$keyword = $setup.keyword) === null || _$setup$keyword === void 0 ? void 0 : _$setup$keyword.split('、'), function (item, index) {\n        return _openBlock(), _createBlock(_component_el_tag, {\n          type: ['primary', 'success', 'warning', 'info', 'danger'][index % 5],\n          key: index\n        }, {\n          default: _withCtx(function () {\n            return [_createTextVNode(_toDisplayString(item), 1 /* TEXT */)];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"]);\n      }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" <el-tag type=\\\"warning\\\">网络安全</el-tag> \")])]), _createElementVNode(\"div\", {\n        class: \"article-content\",\n        innerHTML: $setup.highlightedParsedReport\n      }, null, 8 /* PROPS */, _hoisted_24), _createElementVNode(\"div\", _hoisted_25, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.parsedReport[0].subsections[0].items.slice(2), function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"section\",\n          key: index\n        }, [_createElementVNode(\"h3\", {\n          innerHTML: $setup.highlightTextNew(item.title)\n        }, null, 8 /* PROPS */, _hoisted_26), Array.isArray(item.content) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.content, function (event, index) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            class: \"timeline-item\",\n            key: index\n          }, [_cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n            class: \"dot\"\n          }, null, -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", {\n            class: \"event-text\",\n            innerHTML: $setup.highlightTextNew(event)\n          }, null, 8 /* PROPS */, _hoisted_29), _createCommentVNode(\" <div class=\\\"event-source\\\" v-else>{{ event.content }}</div> \")])]);\n        }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", {\n          key: 1,\n          class: \"impact-content\",\n          innerHTML: $setup.highlightTextNew(item.content)\n        }, null, 8 /* PROPS */, _hoisted_30))]);\n      }), 128 /* KEYED_FRAGMENT */)), $setup.iccList.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_createElementVNode(\"div\", {\n        class: \"news-header\"\n      }, [_cache[13] || (_cache[13] = _createElementVNode(\"h3\", null, \"新闻报道\", -1 /* HOISTED */)), _createElementVNode(\"span\", {\n        class: \"more\",\n        onClick: $setup.showMore\n      }, \"更多 >\")]), _createElementVNode(\"div\", _hoisted_32, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.iccList, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"news-media-item\",\n          key: index,\n          onClick: function onClick($event) {\n            return $setup.openWebpage(item.webpageUrl);\n          }\n        }, [_createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"span\", {\n          class: \"news-media-title\",\n          innerHTML: $setup.highlightTextNew(item.title)\n        }, null, 8 /* PROPS */, _hoisted_35), _createElementVNode(\"span\", _hoisted_36, _toDisplayString(item.publishTime || '2025-02-13'), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_33);\n      }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_37, \" 暂无数据 \"))]), _createCommentVNode(\" 预警信息弹窗 \"), $setup.showWarningPopup ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"warning-popup-overlay\",\n        onClick: _cache[5] || (_cache[5] = _withModifiers(function ($event) {\n          return $setup.showWarningPopup = false;\n        }, [\"self\"]))\n      }, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"预警信息\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_41, _toDisplayString($setup.warning), 1 /* TEXT */)]), _createVNode(_component_el_icon, {\n        class: \"close-icon\",\n        onClick: _cache[4] || (_cache[4] = function ($event) {\n          return $setup.showWarningPopup = false;\n        })\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"Close\"])];\n        }),\n        _: 1 /* STABLE */\n      })]), _createElementVNode(\"div\", _hoisted_42, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.warningList, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"warning-item\",\n          key: item.id,\n          onClick: function onClick($event) {\n            return $setup.handleWarningClick(item);\n          }\n        }, [_createElementVNode(\"h3\", _hoisted_44, _toDisplayString(item.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_45, _toDisplayString(item.desc), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"span\", null, \"来源：\" + _toDisplayString(item.source), 1 /* TEXT */), _createElementVNode(\"span\", null, \"时间：\" + _toDisplayString(item.time), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_43);\n      }), 128 /* KEYED_FRAGMENT */))])])])) : _createCommentVNode(\"v-if\", true)])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "class", "key", "_createBlock", "_component_el_scrollbar", "always", "default", "_withCtx", "_$setup$parsedReport$", "_$setup$keyword", "_createElementVNode", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_createVNode", "_component_el_input", "modelValue", "$setup", "searchText", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handleSearch", "suffix", "_component_el_icon", "onClick", "_", "_hoisted_6", "showWarningPopup", "size", "src", "_hoisted_7", "_component_el_badge", "value", "warning", "max", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_createElementBlock", "_Fragment", "_renderList", "categories", "category", "index", "_normalizeClass", "active", "activeCategory", "handleCategoryClick", "_hoisted_12", "_hoisted_13", "_hoisted_14", "width", "height", "alt", "refreshNews", "rotating", "isRotating", "_createTextVNode", "_hoisted_15", "highlightedNewsList", "item", "handleNewsClick", "innerHTML", "title", "_hoisted_17", "_hoisted_18", "_toDisplayString", "date", "_hoisted_16", "_hoisted_19", "_component_el_pagination", "currentPage", "pageSize", "pageSizes", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handlePageChange", "parsedReport", "length", "_hoisted_20", "_hoisted_21", "highlightTextNew", "subsections", "items", "content", "_hoisted_22", "_hoisted_23", "keyword", "split", "_component_el_tag", "type", "highlightedParsedReport", "_hoisted_24", "_hoisted_25", "slice", "_hoisted_26", "Array", "isArray", "_hoisted_27", "event", "_hoisted_28", "_hoisted_29", "_hoisted_30", "iccList", "_hoisted_31", "showMore", "_hoisted_32", "openWebpage", "webpageUrl", "_hoisted_34", "_hoisted_35", "_hoisted_36", "publishTime", "_hoisted_33", "_hoisted_37", "_withModifiers", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "warningList", "id", "handleWarningClick", "_hoisted_44", "_hoisted_45", "desc", "_hoisted_46", "source", "time", "_hoisted_43"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\HotspotPush\\HotspotPush.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"HotspotPushScrollbar\" always>\r\n    <div class=\"news-detail\">\r\n      <!-- 顶部导航 -->\r\n      <div class=\"top-nav\">\r\n        <div class=\"nav-content\">\r\n          <div class=\"nav-left\">\r\n            <div class=\"logo\">\r\n              <span class=\"focus\">聚焦</span>\r\n              <span class=\"hot\">热点</span>\r\n            </div>\r\n            <div class=\"search-area\">\r\n              <el-input v-model=\"searchText\" placeholder=\"请输入关键词\" class=\"search-input\" @keyup.enter=\"handleSearch\">\r\n                <template #suffix>\r\n                  <el-icon @click=\"handleSearch\">\r\n                    <Search />\r\n                  </el-icon>\r\n                </template>\r\n              </el-input>\r\n            </div>\r\n          </div>\r\n          <div class=\"nav-right\">\r\n            <!-- <span class=\"nav-item\" :class=\"{ active: activeNav === 'domestic' }\" @click=\"handleNavClick('domestic')\">\r\n            国内热点\r\n          </span>\r\n          <span class=\"nav-item\" :class=\"{ active: activeNav === 'international' }\"\r\n            @click=\"handleNavClick('international')\">\r\n            国际热点\r\n          </span> -->\r\n            <div class=\"user-info\" @click=\"showWarningPopup = true\">\r\n              <img :size=\"24\" src=\"../img/warning.png\" />\r\n              <div class=\"warning-info\">\r\n                <!-- <el-icon class=\"warning-icon\">\r\n                <Warning />\r\n              </el-icon> -->\r\n                <span class=\"warning-text\">预警信息</span>\r\n              </div>\r\n              <div class=\"badge-wrapper\">\r\n                <el-badge :value=\"warning\" class=\"badge\" :max=\"99\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"main-content\">\r\n        <!-- 左侧部分 -->\r\n        <div class=\"left-section\">\r\n          <!-- 热点分类 -->\r\n          <div class=\"hot-categories\">\r\n            <div class=\"category-header\">\r\n              <span>热点分类</span>\r\n              <!-- <div class=\"location\">\r\n              全国\r\n              <el-icon>\r\n                <ArrowDown />\r\n              </el-icon>\r\n            </div> -->\r\n            </div>\r\n            <div class=\"category-list\">\r\n              <div v-for=\"(category, index) in categories\" :key=\"index\" class=\"category-item\"\r\n                :class=\"{ active: activeCategory === category }\" @click=\"handleCategoryClick(category, index)\">\r\n                {{ category }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 实时热点 -->\r\n          <div class=\"realtime-news\">\r\n            <div class=\"news-header\">\r\n              <div class=\"header-left\">\r\n                <!-- <el-icon>\r\n                <Timer />\r\n              </el-icon> -->\r\n                <img src=\"../img/hot.png\" width=\"20px\" height=\"20px\" alt=\"\">\r\n                <span>实时热点</span>\r\n              </div>\r\n              <div class=\"refresh\" @click=\"refreshNews\">\r\n                <el-icon :class=\"{ rotating: isRotating }\">\r\n                  <Refresh />\r\n                </el-icon>\r\n                刷新\r\n              </div>\r\n            </div>\r\n            <div class=\"news-list\">\r\n              <div class=\"news-item\" v-for=\"(item, index) in highlightedNewsList\" :key=\"index\"\r\n                @click=\"handleNewsClick(item)\">\r\n                <div class=\"news-title\" v-html=\"item.title\"></div>\r\n                <div class=\"news-meta\">{{ item.date }}</div>\r\n              </div>\r\n              <div class=\"pagination-container\">\r\n                <el-pagination v-model:current-page=\"currentPage\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n                  :total=\"total\" layout=\"total, sizes, prev, next, jumper\" @size-change=\"handleSizeChange\"\r\n                  @current-change=\"handlePageChange\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧部分 -->\r\n        <div class=\"right-section\" v-if=\"parsedReport.length > 0\">\r\n          <div class=\"article-header\">\r\n            <h1 v-html=\"highlightTextNew(parsedReport[0]?.subsections[0]?.items[0]?.content)\"></h1>\r\n            <div class=\"tags\">\r\n              <el-tag :type=\"['primary', 'success', 'warning', 'info', 'danger'][index % 5]\"\r\n                v-for=\"(item, index) in keyword?.split('、')\" :key=\"index\">{{ item }}</el-tag>\r\n              <!-- <el-tag type=\"warning\">网络安全</el-tag> -->\r\n            </div>\r\n          </div>\r\n          <div class=\"article-content\" v-html=\"highlightedParsedReport\"></div>\r\n          <div class=\"article-sections\">\r\n            <div class=\"section\" v-for=\"(item, index) in parsedReport[0].subsections[0].items.slice(2)\" :key=\"index\">\r\n              <h3 v-html=\"highlightTextNew(item.title)\"></h3>\r\n              <div class=\"timeline\" v-if=\"Array.isArray(item.content)\">\r\n                <div class=\"timeline-item\" v-for=\"(event, index) in item.content\" :key=\"index\">\r\n                  <div class=\"dot\"></div>\r\n                  <div class=\"event-content\">\r\n                    <div class=\"event-text\" v-html=\"highlightTextNew(event)\"></div>\r\n                    <!-- <div class=\"event-source\" v-else>{{ event.content }}</div> -->\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"impact-content\" v-else v-html=\"highlightTextNew(item.content)\">\r\n              </div>\r\n            </div>\r\n            <div class=\"section\" v-if=\"iccList.length > 0\">\r\n              <div class=\"news-header\">\r\n                <h3>新闻报道</h3>\r\n                <span class=\"more\" @click=\"showMore\">更多 ></span>\r\n              </div>\r\n              <div class=\"news-media-list\">\r\n                <div class=\"news-media-item\" v-for=\"(item, index) in iccList\" :key=\"index\"\r\n                  @click=\"openWebpage(item.webpageUrl)\">\r\n                  <div class=\"news-media-content\">\r\n                    <span class=\"news-media-title\" v-html=\"highlightTextNew(item.title)\"></span>\r\n                    <span class=\"news-media-date\">{{ item.publishTime || '2025-02-13' }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"right-section rigth-notdata\" v-else>\r\n          暂无数据\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <!-- 预警信息弹窗 -->\r\n      <div class=\"warning-popup-overlay\" v-if=\"showWarningPopup\" @click.self=\"showWarningPopup = false\">\r\n        <div class=\"warning-popup\">\r\n          <div class=\"popup-header\">\r\n            <div class=\"popup-title\">\r\n              <span>预警信息</span>\r\n              <span class=\"warning-count\">{{ warning }}</span>\r\n            </div>\r\n            <el-icon class=\"close-icon\" @click=\"showWarningPopup = false\">\r\n              <Close />\r\n            </el-icon>\r\n          </div>\r\n          <div class=\"popup-content\">\r\n            <div class=\"warning-item\" v-for=\"item in warningList\" :key=\"item.id\" @click=\"handleWarningClick(item)\">\r\n              <h3 class=\"warning-title\">{{ item.title }}</h3>\r\n              <p class=\"warning-desc\">{{ item.desc }}</p>\r\n              <div class=\"warning-meta\">\r\n                <span>来源：{{ item.source }}</span>\r\n                <span>时间：{{ item.time }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport { Search, ArrowDown, Timer, Refresh, Warning, Close } from '@element-plus/icons-vue'\r\nimport { defaultPageSize, pageSizes } from 'common/js/system_var.js'\r\nimport api from '@/api'\r\nconst store = useStore()\r\nconst parsedReport = ref([]);\r\n// 分类数据\r\nconst categories = ref(['全部', '政治', '经济', '社会', '民生', '文化', '生态环境', '科技'])\r\nconst activeCategory = ref('全部')\r\nconst keyword = ref('')\r\nconst warning = ref(0)\r\nconst isRotating = ref(false)\r\nconst iccList = ref([])\r\nconst searchText = ref('')\r\nconst newsList = ref([])\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(defaultPageSize.value)\r\nconst total = ref(0)\r\n// 分类点击处理函数\r\nconst handleCategoryClick = (category, index) => {\r\n  activeCategory.value = category\r\n  miduDataTopicList(index)\r\n\r\n  // 这里可以添加切换分类后的其他逻辑，比如获取对应分类的新闻列表等\r\n}\r\nconst handleSearch = () => {\r\n  // 处理搜索逻辑\r\n  miduDataTopicList(0, searchText.value)\r\n}\r\nconst openWebpage = (url) => {\r\n  if (!url) return;\r\n\r\n  // 检查 URL 格式并确保它有正确的协议\r\n  let finalUrl = url;\r\n  if (url && !url.startsWith('http://') && !url.startsWith('https://')) {\r\n    finalUrl = 'https://' + url;\r\n  }\r\n\r\n  // 安全检查 - 确保 URL 是有效的\r\n  try {\r\n    new URL(finalUrl);\r\n  } catch (e) {\r\n    console.error('无效的 URL:', finalUrl);\r\n    return;\r\n  }\r\n\r\n  // 在新标签页中打开链接，添加安全属性\r\n  const newWindow = window.open();\r\n  if (newWindow) {\r\n    newWindow.opener = null; // 断开与打开者的联系，防止钓鱼攻击\r\n    newWindow.location = finalUrl;\r\n    newWindow.target = '_blank';\r\n    newWindow.rel = 'noopener noreferrer'; // 防止新页面访问 window.opener\r\n  } else {\r\n    // 如果弹出窗口被阻止，则直接导航\r\n    window.open(finalUrl, '_blank', 'noopener,noreferrer');\r\n  }\r\n}\r\nconst miduDataScribePoll = async () => {\r\n  const res = await api.miduDataScribePoll()\r\n  warning.value = res.data.length\r\n  warningList.value = res.data.map(item => ({\r\n    id: item.textId,\r\n    title: item.title,\r\n    desc: item.keyword,\r\n    source: item.captureWebsite,\r\n    time: item.publishTime,\r\n    content: item.content\r\n  }))\r\n}\r\nconst refreshNews = () => {\r\n  isRotating.value = true;\r\n  // 模拟刷新操作\r\n  setTimeout(() => {\r\n    isRotating.value = false;\r\n  }, 1000); // 1秒后停止旋转\r\n  activeCategory.value = '全部'\r\n  miduDataTopicList(0)\r\n}\r\nconst miduDataTopicList = async (params, searchText) => {\r\n  const res = await api.miduDataTopicList({\r\n    types: params === 0 ? '1,2,3,4,5,6,7' : params,\r\n    page: currentPage.value,\r\n    pageSize: pageSize.value,\r\n    keyword: searchText\r\n  })\r\n  newsList.value = res.data.map(item => ({\r\n    id: item.id,\r\n    title: item.name,\r\n    date: item.occurTime,\r\n    description: item.keyword\r\n  }))\r\n  total.value = res.total || 0\r\n  if (res.data.length > 0) {\r\n    miduDataTopicDetail(res.data[0]?.id)\r\n    keyword.value = res.data[0]?.keyword\r\n  } else {\r\n    keyword.value = ''\r\n    parsedReport.value = []\r\n    iccList.value = []\r\n  }\r\n}\r\nconst miduDataTopicDetail = async (params) => {\r\n  const res = await api.miduDataTopicDetail({ id: params })\r\n  parsedReport.value = parseReportText(res.data.reportText);\r\n  iccList.value = res.data.iccList\r\n}\r\n\r\nconst parseReportText = (reportText) => {\r\n  const sections = [];\r\n  // 检查是否存在 # 或 ## 标题\r\n  const hasMainTitles = /^#\\s|^##\\s/m.test(reportText);\r\n  if (hasMainTitles) {\r\n\r\n    const sectionRegex = /# (.*?)\\n([\\s\\S]*?)(?=\\n# |$)/g;\r\n    let sectionMatch;\r\n\r\n    while ((sectionMatch = sectionRegex.exec(reportText)) !== null) {\r\n      const section = {\r\n        title: sectionMatch[1].trim(),\r\n        content: '',\r\n        subsections: []\r\n      };\r\n\r\n      const subsectionRegex = /## (.*?)\\n([\\s\\S]*?)(?=\\n## |$)/g;\r\n      let subsectionMatch;\r\n\r\n      while ((subsectionMatch = subsectionRegex.exec(sectionMatch[2])) !== null) {\r\n        const subsection = {\r\n          title: subsectionMatch[1].trim(),\r\n          content: '',\r\n          subsubsections: [],\r\n          items: []  // 确保 items 被初始化为一个空数组\r\n        };\r\n\r\n        const subsubsectionRegex = /### (.*?)\\n([\\s\\S]*?)(?=\\n### |$)/g;\r\n        let subsubsectionMatch;\r\n\r\n        while ((subsubsectionMatch = subsubsectionRegex.exec(subsectionMatch[2])) !== null) {\r\n          const subsubsection = {\r\n            title: subsubsectionMatch[1].trim(),\r\n            content: subsubsectionMatch[2] ? subsubsectionMatch[2].trim() : '',\r\n            items: []\r\n          };\r\n\r\n          const listRegex = /(?:###\\s*|-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\r\n          let listMatch;\r\n          while ((listMatch = listRegex.exec(subsubsection.content)) !== null) {\r\n            subsubsection.items.push(listMatch[1].trim());\r\n          }\r\n\r\n          if (subsubsection.items.length > 0) {\r\n            subsubsection.content = '';\r\n          }\r\n\r\n          subsection.subsubsections.push(subsubsection);\r\n        }\r\n\r\n        if (subsection.subsubsections.length === 0) {\r\n          const listRegex = /(?:-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\r\n          let listMatch;\r\n          while ((listMatch = listRegex.exec(subsectionMatch[2])) !== null) {\r\n            subsection.items.push(listMatch[1].trim());\r\n          }\r\n\r\n          if (subsection.items.length === 0) {\r\n            subsection.content = subsectionMatch[2] ? subsectionMatch[2].trim() : '';\r\n          }\r\n        }\r\n\r\n        section.subsections.push(subsection);\r\n      }\r\n\r\n      if (section.subsections.length === 0) {\r\n        section.content = sectionMatch[2] ? sectionMatch[2].trim() : '';\r\n      }\r\n\r\n      sections.push(section);\r\n    }\r\n  } else {\r\n    const section = {\r\n      title: '',\r\n      content: '',\r\n      subsections: [{\r\n        title: '',\r\n        content: '',\r\n        items: []\r\n      }]\r\n    };\r\n\r\n    // 匹配 ### 开头的行及其后续内容\r\n    const titleContentRegex = /### (.*?)(?:\\n([\\s\\S]*?)(?=\\n### |$))/g;\r\n    let match;\r\n\r\n    while ((match = titleContentRegex.exec(reportText)) !== null) {\r\n      const title = match[1].trim();\r\n      const content = match[2] ? match[2].trim() : '';\r\n\r\n      // 如果内容中包含列表项（以 - 或数字开头）\r\n      if (content) {\r\n        const listItems = [];\r\n        const listRegex = /(?:-\\s*|\\d+\\.\\s*)(.*?)(?=\\n|$)/g;\r\n        let listMatch;\r\n\r\n        while ((listMatch = listRegex.exec(content)) !== null) {\r\n          listItems.push(listMatch[1].trim());\r\n        }\r\n\r\n        section.subsections[0].items.push({\r\n          title: title,\r\n          content: listItems.length > 0 ? listItems : content\r\n        });\r\n      } else {\r\n        section.subsections[0].items.push({\r\n          title: title,\r\n          content: []\r\n        });\r\n      }\r\n    }\r\n\r\n    sections.push(section);\r\n  }\r\n  console.log('sections', sections);\r\n\r\n  return sections;\r\n};\r\n// 新闻列表数据\r\nconst handleNewsClick = (news) => {\r\n  // 处理新闻点击事件，比如跳转到新闻详情页\r\n  miduDataTopicDetail(news.id)\r\n  keyword.value = news.description\r\n}\r\n// 导航状态控制\r\nconst activeNav = ref('domestic')\r\n\r\n// 导航切换处理函数\r\nconst handleNavClick = (nav) => {\r\n  activeNav.value = nav\r\n}\r\n\r\nconst showWarningPopup = ref(false)\r\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\n\r\nconst warningList = ref([])\r\n\r\nconst handleWarningClick = (item) => {\r\n  showWarningPopup.value = false\r\n  store.commit('setOpenRoute', { name: '预警详情', path: '/WarningDetail', query: { id: item.id } })\r\n}\r\n\r\n// 根据路由参数加载对应的新闻内容\r\nonMounted(() => {\r\n  miduDataScribePoll()\r\n  miduDataTopicList(0)\r\n})\r\n\r\n// 高亮文本的方法\r\nconst highlightText = (text, keyword) => {\r\n  if (!keyword) return text\r\n  const reg = new RegExp(keyword, 'gi')\r\n  return text.replace(reg, match => `<span class=\"highlight\">${match}</span>`)\r\n}\r\n\r\n// 处理新闻列表项的高亮\r\nconst highlightedNewsList = computed(() => {\r\n  return newsList.value.map(item => ({\r\n    ...item,\r\n    title: searchText.value ? highlightText(item.title, searchText.value) : item.title,\r\n    description: searchText.value ? highlightText(item.description, searchText.value) : item.description\r\n  }))\r\n})\r\nconst highlightedParsedReport = computed(() => {\r\n  const subsectionContent = parsedReport.value[0]?.subsections[0]?.items[1]?.content;\r\n  if (!subsectionContent) return '';\r\n  return searchText.value ? highlightText(subsectionContent, searchText.value) : subsectionContent;\r\n})\r\nconst highlightTextNew = (text) => {\r\n  if (!searchText.value) return text;\r\n  const regex = new RegExp(`(${searchText.value})`, 'gi');\r\n  return text.replace(regex, '<span class=\"highlight\">$1</span>');\r\n};\r\n\r\n// 处理分页变化\r\nconst handlePageChange = (page) => {\r\n  currentPage.value = page\r\n  miduDataTopicList(0, searchText.value)\r\n}\r\n\r\n// 处理每页条数变化\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n  miduDataTopicList(0, searchText.value)\r\n}\r\n\r\nconst showMore = () => {\r\n  // 处理查看更多的逻辑\r\n  console.log('查看更多新闻')\r\n}\r\n</script>\r\n<style scoped>\r\n/* 整体布局 */\r\n.news-detail {\r\n  min-height: 100vh;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.HotspotPushScrollbar {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.rotating {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* 顶部导航 */\r\n.top-nav {\r\n  width: 100%;\r\n  background: #F2F3FF;\r\n  padding: 12px 0;\r\n}\r\n\r\n.nav-content {\r\n  /* max-width: 1200px; */\r\n  margin: 0 auto;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 50px;\r\n}\r\n\r\n.nav-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 24px;\r\n  margin-left: auto;\r\n}\r\n\r\n.logo {\r\n  width: 144px;\r\n  height: 44px;\r\n  font-family: \"Alimama ShuHeiTi\", sans-serif;\r\n  font-weight: bold;\r\n  font-size: 36px;\r\n  line-height: 42px;\r\n  text-align: left;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.focus {\r\n  color: #3657C0;\r\n}\r\n\r\n.hot {\r\n  color: #FF4B4B;\r\n}\r\n\r\n.search-area {\r\n  position: relative;\r\n}\r\n\r\n.search-input {\r\n  width: 600px;\r\n  height: 54px;\r\n\r\n  .search-icon {\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: #409EFF;\r\n    }\r\n  }\r\n}\r\n\r\n.search-input :deep(.el-input__wrapper) {\r\n  background-color: #fff;\r\n  box-shadow: none;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.search-input :deep(.el-input__suffix) {\r\n  color: #999;\r\n}\r\n\r\n.nav-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 32px;\r\n  margin-left: 10%;\r\n}\r\n\r\n.nav-item {\r\n  width: 75px;\r\n  height: 24px;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n  font-weight: 400;\r\n  font-size: 18px;\r\n  line-height: 21px;\r\n  text-align: left;\r\n  color: #333333;\r\n  cursor: pointer;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.nav-item.active {\r\n  color: #3657C0;\r\n  font-weight: 400;\r\n}\r\n\r\n.user-info {\r\n  width: 142px;\r\n  height: 54px;\r\n  background: #FFFFFF;\r\n  border-radius: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 0 16px;\r\n  position: relative;\r\n}\r\n\r\n.warning-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  position: relative;\r\n}\r\n\r\n.warning-icon {\r\n  font-size: 24px;\r\n  color: #333333;\r\n}\r\n\r\n.warning-text {\r\n  width: 72px;\r\n  height: 24px;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n  font-weight: 400;\r\n  font-size: 18px;\r\n  color: #333333;\r\n  line-height: 21px;\r\n  text-align: left;\r\n}\r\n\r\n.badge-wrapper {\r\n  position: absolute;\r\n  top: -6px;\r\n  right: -6px;\r\n  z-index: 1;\r\n}\r\n\r\n.badge :deep(.el-badge__content) {\r\n  background-color: #FF4B4B;\r\n  border: none;\r\n  height: 16px;\r\n  padding: 0 4px;\r\n  border-radius: 8px;\r\n  font-size: 12px;\r\n  font-weight: normal;\r\n  line-height: 16px;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n  display: flex;\r\n  padding: 20px;\r\n  gap: 20px;\r\n}\r\n\r\n/* 左侧部分 */\r\n.left-section {\r\n  min-width: 430px !important;\r\n  max-width: 430px !important;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n/* 热点分类 */\r\n.hot-categories {\r\n  background: white;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.location {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #666;\r\n  cursor: pointer;\r\n}\r\n\r\n.category-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.category-item {\r\n  padding: 6px 12px;\r\n  border-radius: 15px;\r\n  font-size: 14px;\r\n  color: #666;\r\n  cursor: pointer;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.category-item.active {\r\n  background: var(--zy-el-color-primary);\r\n  color: white;\r\n}\r\n\r\n/* 实时热点 */\r\n.realtime-news {\r\n  background: white;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n}\r\n\r\n.news-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.refresh {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #666;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.news-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.news-item {\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.news-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-bottom: 8px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.news-meta {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 右侧部分 */\r\n.right-section {\r\n  flex: 1;\r\n  background: white;\r\n  border-radius: 4px;\r\n  padding: 20px;\r\n  width: calc(100% - 430px);\r\n}\r\n\r\n.rigth-notdata {\r\n  display: flex;\r\n  align-items: center;\r\n  /* 垂直居中 */\r\n  justify-content: center;\r\n  /* 水平居中（可选） */\r\n}\r\n\r\n.article-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.article-header h1 {\r\n  font-size: 24px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.tags {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.article-content {\r\n  font-size: 14px;\r\n  line-height: 1.8;\r\n  color: #666;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n/* 文章各部分样式 */\r\n.article-sections {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30px;\r\n}\r\n\r\n.section {\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.section h3 {\r\n  font-size: 18px;\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.area-list {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.person-info {\r\n  display: flex;\r\n  gap: 15px;\r\n  background: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.person-detail {\r\n  flex: 1;\r\n}\r\n\r\n.person-detail .name {\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.person-detail .title {\r\n  color: #666;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.person-detail .description {\r\n  font-size: 14px;\r\n  color: #999;\r\n  line-height: 1.6;\r\n}\r\n\r\n.timeline {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 15px;\r\n}\r\n\r\n.timeline-item {\r\n  position: relative;\r\n  display: flex;\r\n  padding-left: 20px;\r\n}\r\n\r\n.dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 8px;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: var(--zy-el-color-primary);\r\n}\r\n\r\n.event-content {\r\n  flex: 1;\r\n}\r\n\r\n.event-text {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.event-source {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.impact-content,\r\n.reaction-content,\r\n.social-impact-content {\r\n  font-size: 14px;\r\n  line-height: 1.8;\r\n  color: #666;\r\n}\r\n\r\n.news-media {\r\n  position: relative;\r\n}\r\n\r\n.media-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 15px;\r\n}\r\n\r\n/* 当屏幕宽度小于1200px时，使用3列布局 */\r\n@media (max-width: 1200px) {\r\n  .media-grid {\r\n    grid-template-columns: repeat(3, 1fr);\r\n  }\r\n}\r\n\r\n/* 当屏幕宽度小于900px时，使用2列布局 */\r\n@media (max-width: 900px) {\r\n  .media-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n}\r\n\r\n/* 当屏幕宽度小于600px时，使用1列布局 */\r\n@media (max-width: 600px) {\r\n  .media-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n}\r\n\r\n.media-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.media-item .el-image {\r\n  width: 100%;\r\n  height: 120px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.media-item .media-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.4;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  /* 显示两行 */\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.more {\r\n  position: absolute;\r\n  top: -40px;\r\n  right: 0;\r\n  color: #4169e1;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.warning-popup-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 2000;\r\n}\r\n\r\n.warning-popup {\r\n  width: 480px;\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 20px;\r\n  border-bottom: 1px solid #E4E7ED;\r\n}\r\n\r\n.popup-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.warning-count {\r\n  display: inline-block;\r\n  padding: 0 6px;\r\n  height: 20px;\r\n  line-height: 20px;\r\n  background: #FF4B4B;\r\n  color: #fff;\r\n  border-radius: 10px;\r\n  font-size: 12px;\r\n  font-weight: normal;\r\n}\r\n\r\n.close-icon {\r\n  font-size: 20px;\r\n  color: #999;\r\n  cursor: pointer;\r\n}\r\n\r\n.popup-content {\r\n  padding: 16px 20px;\r\n  max-height: 480px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.warning-item {\r\n  background: #F7F8FA;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  margin-bottom: 12px;\r\n  cursor: pointer;\r\n  border-left: 2px solid #FF4B4B;\r\n}\r\n\r\n.warning-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.warning-title {\r\n  font-size: 16px;\r\n  color: #333;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.warning-desc {\r\n  font-size: 14px;\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.warning-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.highlight {\r\n  color: #FF4B4B;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 由于使用了 v-html，需要让 scoped 样式影响动态插入的内容 */\r\n:deep(.highlight) {\r\n  color: #FF4B4B;\r\n  font-weight: 500;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  padding: 10px 0;\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.pagination-container :deep(.el-pagination) {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.pagination-container :deep(.el-pagination .el-select .el-input) {\r\n  width: 70px;\r\n}\r\n\r\n.pagination-container :deep(.el-pagination__jump .el-input) {\r\n  width: 40px;\r\n  margin: 0 3px;\r\n}\r\n\r\n.pagination-container :deep(.el-pagination button) {\r\n  padding: 0 8px;\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n  .pagination-container :deep(.el-pagination) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n\r\n  .pagination-container :deep(.el-pagination .el-pagination__sizes),\r\n  .pagination-container :deep(.el-pagination .el-pagination__jump) {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n.news-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.news-header h3 {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n.more {\r\n  font-size: 14px;\r\n  color: #999;\r\n  cursor: pointer;\r\n}\r\n\r\n.news-media-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.news-media-item {\r\n  background: #f7f8fa;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  overflow: hidden;\r\n}\r\n\r\n.news-media-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px 20px;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.news-media-title {\r\n  font-size: 14px;\r\n  color: #333;\r\n  margin-right: 16px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n}\r\n\r\n.news-media-date {\r\n  font-size: 14px;\r\n  color: #999;\r\n  white-space: nowrap;\r\n  width: 80px;\r\n  text-align: right;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.news-media-item:hover {\r\n  background: #f0f2f5;\r\n}\r\n</style>"], "mappings": ";OA8B8BA,UAAwB;OA4CjCC,UAAoB;;EAxEhCC,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAU;;EAKdA,KAAK,EAAC;AAAa;;EAUrBA,KAAK,EAAC;AAAW;;EAgBbA,KAAK,EAAC;AAAe;;EAQ7BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAgB;;EAUpBA,KAAK,EAAC;AAAe;kBA3DtC;;EAoEeA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EAenBA,KAAK,EAAC;AAAW;kBApFlC;kBAAA;;EAwFqBA,KAAK,EAAC;AAAW;;EAEnBA,KAAK,EAAC;AAAsB;;EA1F/CC,GAAA;EAoGaD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAgB;kBArGrC;;EAuGiBA,KAAK,EAAC;AAAM;kBAvG7B;;EA8GeA,KAAK,EAAC;AAAkB;kBA9GvC;;EAAAC,GAAA;EAiHmBD,KAAK,EAAC;;;EAGFA,KAAK,EAAC;AAAe;kBApH5C;kBAAA;;EAAAC,GAAA;EA6HiBD,KAAK,EAAC;;;EAKJA,KAAK,EAAC;AAAiB;kBAlI1C;;EAqIuBA,KAAK,EAAC;AAAoB;kBArIjD;;EAuI0BA,KAAK,EAAC;AAAiB;;EAvIjDC,GAAA;EA8IaD,KAAK,EAAC;;;EAQNA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAe;;EAM1BA,KAAK,EAAC;AAAe;kBAhKpC;;EAkKkBA,KAAK,EAAC;AAAe;;EACtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;;;;;;;uBAnKrCE,YAAA,CA4KeC,uBAAA;IA5KDH,KAAK,EAAC,sBAAsB;IAACI,MAAM,EAAN;;IAD7CC,OAAA,EAAAC,QAAA,CAEI;MAAA,IAAAC,qBAAA,EAAAC,eAAA;MAAA,OA0KM,CA1KNC,mBAAA,CA0KM,OA1KNC,UA0KM,GAzKJC,mBAAA,UAAa,EACbF,mBAAA,CAuCM,OAvCNG,UAuCM,GAtCJH,mBAAA,CAqCM,OArCNI,UAqCM,GApCJJ,mBAAA,CAcM,OAdNK,UAcM,G,0BAbJL,mBAAA,CAGM;QAHDT,KAAK,EAAC;MAAM,IACfS,mBAAA,CAA6B;QAAvBT,KAAK,EAAC;MAAO,GAAC,IAAE,GACtBS,mBAAA,CAA2B;QAArBT,KAAK,EAAC;MAAK,GAAC,IAAE,E,sBAEtBS,mBAAA,CAQM,OARNM,UAQM,GAPJC,YAAA,CAMWC,mBAAA;QAlBzBC,UAAA,EAYiCC,MAAA,CAAAC,UAAU;QAZ3C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAYiCH,MAAA,CAAAC,UAAU,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAACvB,KAAK,EAAC,cAAc;QAAEwB,OAAK,EAZ7FC,SAAA,CAYqGN,MAAA,CAAAO,YAAY;;QACtFC,MAAM,EAAArB,QAAA,CACf;UAAA,OAEU,CAFVU,YAAA,CAEUY,kBAAA;YAFAC,OAAK,EAAEV,MAAA,CAAAO;UAAY;YAd/CrB,OAAA,EAAAC,QAAA,CAeoB;cAAA,OAAU,CAAVU,YAAA,CAAUG,MAAA,Y;;YAf9BW,CAAA;;;QAAAA,CAAA;6CAqBUrB,mBAAA,CAoBM,OApBNsB,UAoBM,GAnBJpB,mBAAA,6VAMS,EACTF,mBAAA,CAWM;QAXDT,KAAK,EAAC,WAAW;QAAE6B,OAAK,EAAAR,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEH,MAAA,CAAAa,gBAAgB;QAAA;oCAC7CvB,mBAAA,CAA2C;QAArCwB,IAAI,EAAE,EAAE;QAAEC,GAAwB,EAAxBpC;6DAChBW,mBAAA,CAKM;QALDT,KAAK,EAAC;MAAc,IACvBW,mBAAA,iGAEY,EACZF,mBAAA,CAAsC;QAAhCT,KAAK,EAAC;MAAc,GAAC,MAAI,E,sBAEjCS,mBAAA,CAEM,OAFN0B,UAEM,GADJnB,YAAA,CAAqDoB,mBAAA;QAA1CC,KAAK,EAAElB,MAAA,CAAAmB,OAAO;QAAEtC,KAAK,EAAC,OAAO;QAAEuC,GAAG,EAAE;oDAOzD9B,mBAAA,CAqGM,OArGN+B,UAqGM,GApGJ7B,mBAAA,UAAa,EACbF,mBAAA,CAkDM,OAlDNgC,UAkDM,GAjDJ9B,mBAAA,UAAa,EACbF,mBAAA,CAgBM,OAhBNiC,WAgBM,G,0BAfJjC,mBAAA,CAQM;QARDT,KAAK,EAAC;MAAiB,IAC1BS,mBAAA,CAAiB,cAAX,MAAI,GACVE,mBAAA,gKAKQ,C,sBAEVF,mBAAA,CAKM,OALNkC,WAKM,I,kBAJJC,mBAAA,CAGMC,SAAA,QA/DpBC,WAAA,CA4D+C3B,MAAA,CAAA4B,UAAU,EA5DzD,UA4D2BC,QAAQ,EAAEC,KAAK;6BAA5BL,mBAAA,CAGM;UAHwC3C,GAAG,EAAEgD,KAAK;UAAEjD,KAAK,EA5D7EkD,eAAA,EA4D8E,eAAe;YAAAC,MAAA,EAC3DhC,MAAA,CAAAiC,cAAc,KAAKJ;UAAQ;UAAKnB,OAAK,WAALA,OAAKA,CAAAP,MAAA;YAAA,OAAEH,MAAA,CAAAkC,mBAAmB,CAACL,QAAQ,EAAEC,KAAK;UAAA;4BACzFD,QAAQ,gCA9D3BM,WAAA;0CAmEU3C,mBAAA,UAAa,EACbF,mBAAA,CA4BM,OA5BN8C,WA4BM,GA3BJ9C,mBAAA,CAcM,OAdN+C,WAcM,G,4BAbJ/C,mBAAA,CAMM;QANDT,KAAK,EAAC;MAAa,IACtBW,mBAAA,wEAEY,EACZF,mBAAA,CAA4D;QAAvDyB,GAAoB,EAApBnC,UAAoB;QAAC0D,KAAK,EAAC,MAAM;QAACC,MAAM,EAAC,MAAM;QAACC,GAAG,EAAC;UACzDlD,mBAAA,CAAiB,cAAX,MAAI,E,sBAEZA,mBAAA,CAKM;QALDT,KAAK,EAAC,SAAS;QAAE6B,OAAK,EAAEV,MAAA,CAAAyC;UAC3B5C,YAAA,CAEUY,kBAAA;QAFA5B,KAAK,EA9E/BkD,eAAA;UAAAW,QAAA,EA8E6C1C,MAAA,CAAA2C;QAAU;;QA9EvDzD,OAAA,EAAAC,QAAA,CA+EkB;UAAA,OAAW,CAAXU,YAAA,CAAWG,MAAA,a;;QA/E7BW,CAAA;gEAAAiC,gBAAA,CAgF0B,MAEZ,G,KAEFtD,mBAAA,CAWM,OAXNuD,WAWM,I,kBAVJpB,mBAAA,CAIMC,SAAA,QAzFpBC,WAAA,CAqF6D3B,MAAA,CAAA8C,mBAAmB,EArFhF,UAqF6CC,IAAI,EAAEjB,KAAK;6BAA1CL,mBAAA,CAIM;UAJD5C,KAAK,EAAC,WAAW;UAA+CC,GAAG,EAAEgD,KAAK;UAC5EpB,OAAK,WAALA,OAAKA,CAAAP,MAAA;YAAA,OAAEH,MAAA,CAAAgD,eAAe,CAACD,IAAI;UAAA;YAC5BzD,mBAAA,CAAkD;UAA7CT,KAAK,EAAC,YAAY;UAACoE,SAAmB,EAAXF,IAAI,CAACG;gCAvFrDC,WAAA,GAwFgB7D,mBAAA,CAA4C,OAA5C8D,WAA4C,EAAAC,gBAAA,CAAlBN,IAAI,CAACO,IAAI,iB,iBAxFnDC,WAAA;sCA0FcjE,mBAAA,CAIM,OAJNkE,WAIM,GAHJ3D,YAAA,CAEuC4D,wBAAA;QAFhB,cAAY,EAAEzD,MAAA,CAAA0D,WAAW;QA3FhE,wBAAAxD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA2FqDH,MAAA,CAAA0D,WAAW,GAAAvD,MAAA;QAAA;QAAU,WAAS,EAAEH,MAAA,CAAA2D,QAAQ;QA3F7F,qBAAAzD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA2FqFH,MAAA,CAAA2D,QAAQ,GAAAxD,MAAA;QAAA;QAAG,YAAU,EAAEH,MAAA,CAAA4D,SAAS;QAClGC,KAAK,EAAE7D,MAAA,CAAA6D,KAAK;QAAEC,MAAM,EAAC,kCAAkC;QAAEC,YAAW,EAAE/D,MAAA,CAAAgE,gBAAgB;QACtFC,eAAc,EAAEjE,MAAA,CAAAkE;6FAM3B1E,mBAAA,UAAa,EACoBQ,MAAA,CAAAmE,YAAY,CAACC,MAAM,Q,cAApD3C,mBAAA,CAyCM,OAzCN4C,WAyCM,GAxCJ/E,mBAAA,CAOM,OAPNgF,WAOM,GANJhF,mBAAA,CAAuF;QAAnF2D,SAA6E,EAArEjD,MAAA,CAAAuE,gBAAgB,EAAAnF,qBAAA,GAACY,MAAA,CAAAmE,YAAY,iBAAA/E,qBAAA,gBAAAA,qBAAA,GAAZA,qBAAA,CAAiBoF,WAAW,iBAAApF,qBAAA,gBAAAA,qBAAA,GAA5BA,qBAAA,CAAiCqF,KAAK,iBAAArF,qBAAA,uBAAtCA,qBAAA,CAA2CsF,OAAO;8BAtG3FC,WAAA,GAuGYrF,mBAAA,CAIM,OAJNsF,WAIM,I,kBAHJnD,mBAAA,CAC+EC,SAAA,QAzG7FC,WAAA,EAAAtC,eAAA,GAyGwCW,MAAA,CAAA6E,OAAO,cAAAxF,eAAA,uBAAPA,eAAA,CAASyF,KAAK,OAzGtD,UAyGwB/B,IAAI,EAAEjB,KAAK;6BADrB/C,YAAA,CAC+EgG,iBAAA;UADtEC,IAAI,sDAAsDlD,KAAK;UACxBhD,GAAG,EAAEgD;;UAzGnE5C,OAAA,EAAAC,QAAA,CAyG0E;YAAA,OAAU,CAzGpFyD,gBAAA,CAAAS,gBAAA,CAyG6EN,IAAI,iB;;UAzGjFpC,CAAA;;sCA0GcnB,mBAAA,4CAA6C,C,KAGjDF,mBAAA,CAAoE;QAA/DT,KAAK,EAAC,iBAAiB;QAACoE,SAAgC,EAAxBjD,MAAA,CAAAiF;8BA7G/CC,WAAA,GA8GU5F,mBAAA,CA8BM,OA9BN6F,WA8BM,I,kBA7BJ1D,mBAAA,CAaMC,SAAA,QA5HlBC,WAAA,CA+GyD3B,MAAA,CAAAmE,YAAY,IAAIK,WAAW,IAAIC,KAAK,CAACW,KAAK,KA/GnG,UA+GyCrC,IAAI,EAAEjB,KAAK;6BAAxCL,mBAAA,CAaM;UAbD5C,KAAK,EAAC,SAAS;UAAyEC,GAAG,EAAEgD;YAChGxC,mBAAA,CAA+C;UAA3C2D,SAAqC,EAA7BjD,MAAA,CAAAuE,gBAAgB,CAACxB,IAAI,CAACG,KAAK;gCAhHrDmC,WAAA,GAiH0CC,KAAK,CAACC,OAAO,CAACxC,IAAI,CAAC2B,OAAO,K,cAAtDjD,mBAAA,CAQM,OARN+D,WAQM,I,kBAPJ/D,mBAAA,CAMMC,SAAA,QAxHtBC,WAAA,CAkHoEoB,IAAI,CAAC2B,OAAO,EAlHhF,UAkHmDe,KAAK,EAAE3D,KAAK;+BAA/CL,mBAAA,CAMM;YAND5C,KAAK,EAAC,eAAe;YAAyCC,GAAG,EAAEgD;0CACtExC,mBAAA,CAAuB;YAAlBT,KAAK,EAAC;UAAK,6BAChBS,mBAAA,CAGM,OAHNoG,WAGM,GAFJpG,mBAAA,CAA+D;YAA1DT,KAAK,EAAC,YAAY;YAACoE,SAAgC,EAAxBjD,MAAA,CAAAuE,gBAAgB,CAACkB,KAAK;kCArH1EE,WAAA,GAsHoBnG,mBAAA,kEAAmE,C;2DAIzEiC,mBAAA,CACM;UA3HpB3C,GAAA;UA0HmBD,KAAK,EAAC,gBAAgB;UAAQoE,SAAuC,EAA/BjD,MAAA,CAAAuE,gBAAgB,CAACxB,IAAI,CAAC2B,OAAO;gCA1HtFkB,WAAA,G;sCA6HuC5F,MAAA,CAAA6F,OAAO,CAACzB,MAAM,Q,cAAzC3C,mBAAA,CAcM,OAdNqE,WAcM,GAbJxG,mBAAA,CAGM;QAHDT,KAAK,EAAC;MAAa,I,4BACtBS,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAAgD;QAA1CT,KAAK,EAAC,MAAM;QAAE6B,OAAK,EAAEV,MAAA,CAAA+F;SAAU,MAAI,E,GAE3CzG,mBAAA,CAQM,OARN0G,WAQM,I,kBAPJvE,mBAAA,CAMMC,SAAA,QAzItBC,WAAA,CAmIqE3B,MAAA,CAAA6F,OAAO,EAnI5E,UAmIqD9C,IAAI,EAAEjB,KAAK;6BAAhDL,mBAAA,CAMM;UAND5C,KAAK,EAAC,iBAAiB;UAAmCC,GAAG,EAAEgD,KAAK;UACtEpB,OAAK,WAALA,OAAKA,CAAAP,MAAA;YAAA,OAAEH,MAAA,CAAAiG,WAAW,CAAClD,IAAI,CAACmD,UAAU;UAAA;YACnC5G,mBAAA,CAGM,OAHN6G,WAGM,GAFJ7G,mBAAA,CAA4E;UAAtET,KAAK,EAAC,kBAAkB;UAACoE,SAAqC,EAA7BjD,MAAA,CAAAuE,gBAAgB,CAACxB,IAAI,CAACG,KAAK;gCAtItFkD,WAAA,GAuIoB9G,mBAAA,CAA2E,QAA3E+G,WAA2E,EAAAhD,gBAAA,CAA1CN,IAAI,CAACuD,WAAW,iC,mBAvIrEC,WAAA;4CAAA/G,mBAAA,e,sBA8IQiC,mBAAA,CAEM,OAFN+E,WAEM,EAF0C,QAEhD,G,GAIFhH,mBAAA,YAAe,EAC0BQ,MAAA,CAAAa,gBAAgB,I,cAAzDY,mBAAA,CAsBM;QA3KZ3C,GAAA;QAqJWD,KAAK,EAAC,uBAAuB;QAA0B6B,OAAK,EAAAR,MAAA,QAAAA,MAAA,MArJvEuG,cAAA,WAAAtG,MAAA;UAAA,OAqJ8EH,MAAA,CAAAa,gBAAgB;QAAA;UACtFvB,mBAAA,CAoBM,OApBNoH,WAoBM,GAnBJpH,mBAAA,CAQM,OARNqH,WAQM,GAPJrH,mBAAA,CAGM,OAHNsH,WAGM,G,4BAFJtH,mBAAA,CAAiB,cAAX,MAAI,sBACVA,mBAAA,CAAgD,QAAhDuH,WAAgD,EAAAxD,gBAAA,CAAjBrD,MAAA,CAAAmB,OAAO,iB,GAExCtB,YAAA,CAEUY,kBAAA;QAFD5B,KAAK,EAAC,YAAY;QAAE6B,OAAK,EAAAR,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEH,MAAA,CAAAa,gBAAgB;QAAA;;QA5JhE3B,OAAA,EAAAC,QAAA,CA6Jc;UAAA,OAAS,CAATU,YAAA,CAASG,MAAA,W;;QA7JvBW,CAAA;YAgKUrB,mBAAA,CASM,OATNwH,WASM,I,kBARJrF,mBAAA,CAOMC,SAAA,QAxKlBC,WAAA,CAiKqD3B,MAAA,CAAA+G,WAAW,EAjKhE,UAiK6ChE,IAAI;6BAArCtB,mBAAA,CAOM;UAPD5C,KAAK,EAAC,cAAc;UAA8BC,GAAG,EAAEiE,IAAI,CAACiE,EAAE;UAAGtG,OAAK,WAALA,OAAKA,CAAAP,MAAA;YAAA,OAAEH,MAAA,CAAAiH,kBAAkB,CAAClE,IAAI;UAAA;YAClGzD,mBAAA,CAA+C,MAA/C4H,WAA+C,EAAA7D,gBAAA,CAAlBN,IAAI,CAACG,KAAK,kBACvC5D,mBAAA,CAA2C,KAA3C6H,WAA2C,EAAA9D,gBAAA,CAAhBN,IAAI,CAACqE,IAAI,kBACpC9H,mBAAA,CAGM,OAHN+H,WAGM,GAFJ/H,mBAAA,CAAiC,cAA3B,KAAG,GAAA+D,gBAAA,CAAGN,IAAI,CAACuE,MAAM,kBACvBhI,mBAAA,CAA+B,cAAzB,KAAG,GAAA+D,gBAAA,CAAGN,IAAI,CAACwE,IAAI,iB,mBAtKrCC,WAAA;8CAAAhI,mBAAA,e;;IAAAmB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}