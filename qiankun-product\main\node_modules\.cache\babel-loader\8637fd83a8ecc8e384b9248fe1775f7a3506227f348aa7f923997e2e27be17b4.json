{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"homePageNavigationBar\"\n};\nvar _hoisted_2 = [\"onClick\"];\nvar _hoisted_3 = {\n  key: 0,\n  class: \"separator\"\n};\nvar _hoisted_4 = {\n  class: \"zy-el-container\"\n};\nvar _hoisted_5 = {\n  class: \"top-section\"\n};\nvar _hoisted_6 = {\n  class: \"carousel-container\"\n};\nvar _hoisted_7 = [\"src\"];\nvar _hoisted_8 = {\n  key: 1,\n  class: \"carousel-img placeholder-img\"\n};\nvar _hoisted_9 = {\n  class: \"carousel-overlay\"\n};\nvar _hoisted_10 = {\n  class: \"overlay-title\"\n};\nvar _hoisted_11 = {\n  class: \"overlay-indicator\"\n};\nvar _hoisted_12 = [\"onClick\"];\nvar _hoisted_13 = {\n  class: \"news-tabs-container\"\n};\nvar _hoisted_14 = {\n  class: \"news-tabs-header\"\n};\nvar _hoisted_15 = {\n  class: \"tabs-scroll-container\"\n};\nvar _hoisted_16 = [\"onClick\"];\nvar _hoisted_17 = {\n  class: \"news-tabs-content\"\n};\nvar _hoisted_18 = {\n  class: \"news-list\"\n};\nvar _hoisted_19 = {\n  key: 0,\n  class: \"list-item\",\n  style: {\n    \"display\": \"flex\",\n    \"align-items\": \"center\",\n    \"justify-content\": \"center\",\n    \"color\": \"#999\",\n    \"width\": \"100%\"\n  }\n};\nvar _hoisted_20 = [\"onClick\"];\nvar _hoisted_21 = {\n  class: \"title\"\n};\nvar _hoisted_22 = {\n  class: \"bottom-section\"\n};\nvar _hoisted_23 = {\n  class: \"committee-work-container\"\n};\nvar _hoisted_24 = {\n  class: \"list-container\"\n};\nvar _hoisted_25 = {\n  key: 0,\n  class: \"list-item\",\n  style: {\n    \"display\": \"flex\",\n    \"align-items\": \"center\",\n    \"justify-content\": \"center\",\n    \"color\": \"#999\",\n    \"width\": \"100%\"\n  }\n};\nvar _hoisted_26 = [\"onClick\"];\nvar _hoisted_27 = {\n  class: \"title\"\n};\nvar _hoisted_28 = {\n  class: \"date\"\n};\nvar _hoisted_29 = {\n  class: \"committee-work-container\"\n};\nvar _hoisted_30 = {\n  class: \"committee_title_box\"\n};\nvar _hoisted_31 = {\n  class: \"list-container\"\n};\nvar _hoisted_32 = {\n  key: 0,\n  class: \"list-item\",\n  style: {\n    \"display\": \"flex\",\n    \"align-items\": \"center\",\n    \"justify-content\": \"center\",\n    \"color\": \"#999\",\n    \"width\": \"100%\"\n  }\n};\nvar _hoisted_33 = [\"onClick\"];\nvar _hoisted_34 = [\"title\"];\nvar _hoisted_35 = {\n  class: \"date\"\n};\nvar _hoisted_36 = {\n  class: \"committee-work-container\"\n};\nvar _hoisted_37 = {\n  class: \"committee_title_box\"\n};\nvar _hoisted_38 = {\n  class: \"list-container\"\n};\nvar _hoisted_39 = {\n  key: 0,\n  class: \"list-item\",\n  style: {\n    \"display\": \"flex\",\n    \"align-items\": \"center\",\n    \"justify-content\": \"center\",\n    \"color\": \"#999\",\n    \"width\": \"100%\"\n  }\n};\nvar _hoisted_40 = [\"onClick\"];\nvar _hoisted_41 = {\n  class: \"title\"\n};\nvar _hoisted_42 = {\n  class: \"date\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_carousel_item = _resolveComponent(\"el-carousel-item\");\n  var _component_el_carousel = _resolveComponent(\"el-carousel\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createElementVNode(\"div\", _hoisted_1, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subMenus, function (item, index) {\n    return _openBlock(), _createElementBlock(_Fragment, {\n      key: index\n    }, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"nav-item\", {\n        active: $setup.activeNavItem === index\n      }]),\n      onClick: function onClick($event) {\n        return $setup.handleWorkBench(item, index);\n      }\n    }, _toDisplayString(item.name), 11 /* TEXT, CLASS, PROPS */, _hoisted_2), index < $setup.subMenus.length - 1 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_3, \"|\")) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n  }), 128 /* KEYED_FRAGMENT */))]), _createVNode(_component_el_scrollbar, {\n    class: \"homePage\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"section\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_carousel, {\n        interval: 5000,\n        \"indicator-position\": \"none\",\n        onChange: $setup.handleCarouselChange,\n        ref: \"carouselRef\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.newsData, function (item) {\n            return _openBlock(), _createBlock(_component_el_carousel_item, {\n              key: item.id,\n              onClick: function onClick($event) {\n                return $setup.newsHandle(item);\n              }\n            }, {\n              default: _withCtx(function () {\n                return [item.infoPic ? (_openBlock(), _createElementBlock(\"img\", {\n                  key: 0,\n                  src: $setup.imgUrl(item.infoPic),\n                  class: \"carousel-img\"\n                }, null, 8 /* PROPS */, _hoisted_7)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_8)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString(item.infoTitle), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.newsData.length, function (dot, index) {\n                  return _openBlock(), _createElementBlock(\"span\", {\n                    key: index,\n                    class: _normalizeClass([\"indicator-dot\", {\n                      active: index === $setup.currentCarouselIndex\n                    }]),\n                    onClick: function onClick($event) {\n                      return $setup.handleIndicatorClick(index);\n                    }\n                  }, null, 10 /* CLASS, PROPS */, _hoisted_12);\n                }), 128 /* KEYED_FRAGMENT */))])])];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.newColumnData, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: _normalizeClass([\"news-tab-item\", {\n            active: $setup.activeNewsTabIndex === index\n          }]),\n          onClick: function onClick($event) {\n            return $setup.clickNewColumn(item, index);\n          }\n        }, [_createElementVNode(\"span\", null, _toDisplayString(item.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_16);\n      }), 256 /* UNKEYED_FRAGMENT */))]), _createElementVNode(\"div\", {\n        class: \"more-link\",\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.openWinNews($setup.informationList[$setup.activeNewsTabIndex].id, 2);\n        })\n      }, \"更多 >\")]), _createElementVNode(\"div\", _hoisted_17, [_withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_18, [$setup.informationList.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, \"暂无数据\")) : (_openBlock(true), _createElementBlock(_Fragment, {\n        key: 1\n      }, _renderList($setup.informationList, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: item.id,\n          class: \"news-item\",\n          onClick: function onClick($event) {\n            return $setup.newsHandle(item);\n          }\n        }, [_cache[5] || (_cache[5] = _createElementVNode(\"span\", {\n          class: \"dot\"\n        }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_21, _toDisplayString(item.infoTitle), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_20);\n      }), 128 /* KEYED_FRAGMENT */))])), [[_directive_loading, $setup.loadingInformationList]])])])]), _createElementVNode(\"div\", _hoisted_22, [_createCommentVNode(\" 通知公告 \"), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", {\n        class: \"committee_title_box\"\n      }, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n        class: \"committee_title\"\n      }, \"通知公告\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: \"more-link\",\n        onClick: $setup.openWinNotice\n      }, \"更多 >\")]), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_24, [$setup.noticeData.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, \"暂无数据\")) : (_openBlock(true), _createElementBlock(_Fragment, {\n        key: 1\n      }, _renderList($setup.noticeData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: item.id,\n          class: \"list-item\",\n          onClick: function onClick($event) {\n            return $setup.noticeInfo(item);\n          }\n        }, [_cache[7] || (_cache[7] = _createElementVNode(\"span\", {\n          class: \"dot\"\n        }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_27, _toDisplayString(item.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_28, _toDisplayString($setup.formatTime(item.createDate)), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_26);\n      }), 128 /* KEYED_FRAGMENT */))])), [[_directive_loading, $setup.loadingNoticeList]])]), _createCommentVNode(\" 委员风采 \"), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n        class: \"committee_title\"\n      }, \"委员风采\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: \"more-link\",\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.openWinNews(null, 3);\n        })\n      }, \"更多 >\")]), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_31, [$setup.memberStyleData.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, \"暂无数据\")) : (_openBlock(true), _createElementBlock(_Fragment, {\n        key: 1\n      }, _renderList($setup.memberStyleData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: item.id,\n          class: \"list-item\",\n          onClick: function onClick($event) {\n            return $setup.newsHandle(item);\n          }\n        }, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n          class: \"dot\"\n        }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", {\n          class: \"title\",\n          title: item.infoTitle\n        }, _toDisplayString(item.infoTitle), 9 /* TEXT, PROPS */, _hoisted_34), _createElementVNode(\"div\", _hoisted_35, _toDisplayString($setup.formatTime(item.createDate)), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_33);\n      }), 128 /* KEYED_FRAGMENT */))])), [[_directive_loading, $setup.loadingMemberStyle]])]), _createCommentVNode(\" 委员眼中的西安 \"), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n        class: \"committee_title\"\n      }, \"委员眼中的西安\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n        class: \"more-link\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.openWinNews(null, 4);\n        })\n      }, \"更多 >\")]), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_38, [$setup.committeeMembersData.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, \"暂无数据\")) : (_openBlock(true), _createElementBlock(_Fragment, {\n        key: 1\n      }, _renderList($setup.committeeMembersData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: item.id,\n          class: \"list-item\",\n          onClick: function onClick($event) {\n            return $setup.newsHandle(item);\n          }\n        }, [_cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n          class: \"dot\"\n        }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_41, _toDisplayString(item.infoTitle), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_42, _toDisplayString($setup.formatTime(item.createDate)), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_40);\n      }), 128 /* KEYED_FRAGMENT */))])), [[_directive_loading, $setup.loadingCommitteeMembersList]])])])])];\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.newsShow,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.newsShow = $event;\n    }),\n    name: \"详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"AllInformationDetail\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.noticeShow,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.noticeShow = $event;\n    }),\n    name: \"通知公告详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"NoticeAnnouncementDetails\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "key", "style", "_createElementBlock", "_Fragment", "_createElementVNode", "_hoisted_1", "_renderList", "$setup", "subMenus", "item", "index", "_normalizeClass", "active", "activeNavItem", "onClick", "$event", "handleWorkBench", "name", "_hoisted_2", "length", "_hoisted_3", "_createCommentVNode", "_createVNode", "_component_el_scrollbar", "default", "_withCtx", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_component_el_carousel", "interval", "onChange", "handleCarouselChange", "ref", "newsData", "_createBlock", "_component_el_carousel_item", "id", "newsHandle", "infoPic", "src", "imgUrl", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_toDisplayString", "infoTitle", "_hoisted_11", "dot", "currentCarouselIndex", "handleIndicatorClick", "_hoisted_12", "_", "_hoisted_13", "_hoisted_14", "_hoisted_15", "newColumnData", "activeNewsTabIndex", "clickNewColumn", "_hoisted_16", "_cache", "openWinNews", "informationList", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_21", "_hoisted_20", "loadingInformationList", "_hoisted_22", "_hoisted_23", "openWinNotice", "_hoisted_24", "noticeData", "_hoisted_25", "noticeInfo", "_hoisted_27", "theme", "_hoisted_28", "formatTime", "createDate", "_hoisted_26", "loadingNoticeList", "_hoisted_29", "_hoisted_30", "_hoisted_31", "memberStyleData", "_hoisted_32", "title", "_hoisted_34", "_hoisted_35", "_hoisted_33", "loadingMemberStyle", "_hoisted_36", "_hoisted_37", "_hoisted_38", "committeeMembersData", "_hoisted_39", "_hoisted_41", "_hoisted_42", "_hoisted_40", "loadingCommitteeMembersList", "_component_xyl_popup_window", "modelValue", "newsShow", "onCallback", "callback", "noticeShow"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\homePage\\homePageNew.vue"], "sourcesContent": ["<!--\r\n * @FileDescription: 首页展示\r\n * @Author: 耿培宣\r\n * @Date: 2025/05/29\r\n * @LastEditors: 耿培宣\r\n * @LastEditTime: 2025/05/29\r\n -->\r\n<template style=\"overflow: hidden;\">\r\n  <div class=\"homePageNavigationBar\">\r\n    <template v-for=\"(item, index) in subMenus\" :key=\"index\">\r\n      <span class=\"nav-item\" :class=\"{ active: activeNavItem === index }\" @click=\"handleWorkBench(item, index)\">{{\r\n        item.name\r\n      }}</span>\r\n      <span v-if=\"index < subMenus.length - 1\" class=\"separator\">|</span>\r\n    </template>\r\n  </div>\r\n  <el-scrollbar class=\"homePage\">\r\n    <section class=\"zy-el-container\">\r\n      <div class=\"top-section\">\r\n        <div class=\"carousel-container\">\r\n          <el-carousel :interval=\"5000\" indicator-position=\"none\" @change=\"handleCarouselChange\" ref=\"carouselRef\">\r\n            <el-carousel-item v-for=\"item in newsData\" :key=\"item.id\" @click=\"newsHandle(item)\">\r\n              <img v-if=\"item.infoPic\" :src=\"imgUrl(item.infoPic)\" class=\"carousel-img\" />\r\n              <div v-else class=\"carousel-img placeholder-img\"></div>\r\n              <div class=\"carousel-overlay\">\r\n                <div class=\"overlay-title\">{{ item.infoTitle }}</div>\r\n                <div class=\"overlay-indicator\">\r\n                  <span v-for=\"(dot, index) in newsData.length\" :key=\"index\" class=\"indicator-dot\"\r\n                    :class=\"{ active: index === currentCarouselIndex }\" @click=\"handleIndicatorClick(index)\"></span>\r\n                </div>\r\n              </div>\r\n            </el-carousel-item>\r\n          </el-carousel>\r\n        </div>\r\n        <div class=\"news-tabs-container\">\r\n          <div class=\"news-tabs-header\">\r\n            <div class=\"tabs-scroll-container\">\r\n              <div class=\"news-tab-item\" v-for=\"(item, index) in newColumnData\"\r\n                :class=\"{ active: activeNewsTabIndex === index }\" @click=\"clickNewColumn(item, index)\">\r\n                <span>{{ item.name }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"more-link\" @click=\"openWinNews(informationList[activeNewsTabIndex].id, 2)\">更多 ></div>\r\n          </div>\r\n          <div class=\"news-tabs-content\">\r\n            <div class=\"news-list\" v-loading=\"loadingInformationList\">\r\n              <template v-if=\"informationList.length === 0\">\r\n                <div class=\"list-item\"\r\n                  style=\"display: flex;align-items: center;justify-content: center;color: #999;width:100%;\">暂无数据</div>\r\n              </template>\r\n              <template v-else>\r\n                <div v-for=\"item in informationList\" :key=\"item.id\" class=\"news-item\" @click=\"newsHandle(item)\">\r\n                  <span class=\"dot\">•</span>\r\n                  <span class=\"title\">{{ item.infoTitle }}</span>\r\n                </div>\r\n              </template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"bottom-section\">\r\n        <!-- 通知公告 -->\r\n        <div class=\"committee-work-container\">\r\n          <div class=\"committee_title_box\">\r\n            <div class=\"committee_title\">通知公告</div>\r\n            <div class=\"more-link\" @click=\"openWinNotice\">更多 ></div>\r\n          </div>\r\n          <div class=\"list-container\" v-loading=\"loadingNoticeList\">\r\n            <template v-if=\"noticeData.length === 0\">\r\n              <div class=\"list-item\"\r\n                style=\"display: flex;align-items: center;justify-content: center;color: #999;width:100%;\">暂无数据</div>\r\n            </template>\r\n            <template v-else>\r\n              <div v-for=\"item in noticeData\" :key=\"item.id\" class=\"list-item\" @click=\"noticeInfo(item)\">\r\n                <span class=\"dot\">•</span>\r\n                <div>\r\n                  <div class=\"title\">{{ item.theme }}</div>\r\n                  <div class=\"date\">{{ formatTime(item.createDate) }}</div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </div>\r\n        <!-- 委员风采 -->\r\n        <div class=\"committee-work-container\">\r\n          <div class=\"committee_title_box\">\r\n            <div class=\"committee_title\">委员风采</div>\r\n            <div class=\"more-link\" @click=\"openWinNews(null, 3)\">更多 ></div>\r\n          </div>\r\n          <div class=\"list-container\" v-loading=\"loadingMemberStyle\">\r\n            <template v-if=\"memberStyleData.length === 0\">\r\n              <div class=\"list-item\"\r\n                style=\"display: flex;align-items: center;justify-content: center;color: #999;width:100%;\">暂无数据</div>\r\n            </template>\r\n            <template v-else>\r\n              <div v-for=\"item in memberStyleData\" :key=\"item.id\" class=\"list-item\" @click=\"newsHandle(item)\">\r\n                <span class=\"dot\">•</span>\r\n                <div>\r\n                  <div class=\"title\" :title=\"item.infoTitle\">{{ item.infoTitle }}</div>\r\n                  <div class=\"date\">{{ formatTime(item.createDate) }}</div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </div>\r\n        <!-- 委员眼中的西安 -->\r\n        <div class=\"committee-work-container\">\r\n          <div class=\"committee_title_box\">\r\n            <div class=\"committee_title\">委员眼中的西安</div>\r\n            <div class=\"more-link\" @click=\"openWinNews(null, 4)\">更多 ></div>\r\n          </div>\r\n          <div class=\"list-container\" v-loading=\"loadingCommitteeMembersList\">\r\n            <template v-if=\"committeeMembersData.length === 0\">\r\n              <div class=\"list-item\"\r\n                style=\"display: flex;align-items: center;justify-content: center;color: #999;width:100%;\">暂无数据</div>\r\n            </template>\r\n            <template v-else>\r\n              <div v-for=\"item in committeeMembersData\" :key=\"item.id\" class=\"list-item\" @click=\"newsHandle(item)\">\r\n                <span class=\"dot\">•</span>\r\n                <div>\r\n                  <div class=\"title\">{{ item.infoTitle }}</div>\r\n                  <div class=\"date\">{{ formatTime(item.createDate) }}</div>\r\n                </div>\r\n              </div>\r\n            </template>\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </el-scrollbar>\r\n  <xyl-popup-window v-model=\"newsShow\" name=\"详情\">\r\n    <AllInformationDetail :id=\"id\" @callback=\"callback\"></AllInformationDetail>\r\n  </xyl-popup-window>\r\n  <xyl-popup-window v-model=\"noticeShow\" name=\"通知公告详情\">\r\n    <NoticeAnnouncementDetails :id=\"id\" @callback=\"callback\"></NoticeAnnouncementDetails>\r\n  </xyl-popup-window>\r\n</template>\r\n<script>\r\nexport default { name: 'homePage' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { onMounted, ref, inject, computed } from 'vue'\r\nimport AllInformationDetail from './components/AllInformationDetail'\r\nimport NoticeAnnouncementDetails from './components/NoticeAnnouncementDetails'\r\nconst openPage = inject('openPage')\r\nconst leftMenuData = inject('leftMenuData')\r\nconst menuListData = inject('WorkBenchList')\r\nconst activeNavItem = ref(0)\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\nconst subMenus = computed(() => {\r\n  const filtered = (menuListData.value || []).filter(item => item.routePath !== '/homePage')\r\n  const newArr = filtered.filter(item => item.name == '综合应用')\r\n  const result = newArr[0]?.children.filter(child => child.name !== '系统运维' && child.name !== '我的')\r\n  return result\r\n})\r\nconst handleWorkBench = (item, index) => {\r\n  activeNavItem.value = index\r\n  leftMenuData(item)\r\n}\r\n// 资讯轮播图\r\nconst newsData = ref([])\r\nconst currentCarouselIndex = ref(0)\r\nconst carouselRef = ref(null)\r\n// 资讯栏目\r\nconst activeNewsTabIndex = ref(0)\r\nconst newColumnData = ref([])\r\n// 资讯列表\r\nconst informationList = ref([])\r\nconst loadingInformationList = ref(false)\r\n// 通知公告\r\nconst loadingNoticeList = ref(false)\r\nconst noticeData = ref([])\r\n// 委员风采\r\nconst loadingMemberStyle = ref(false)\r\nconst memberStyleData = ref([])\r\n// 委员眼中的西安\r\nconst loadingCommitteeMembersList = ref(false)\r\nconst committeeMembersData = ref([])\r\n\r\nconst id = ref('')\r\nconst noticeShow = ref(false)\r\nconst newsShow = ref(false)\r\n\r\n// 获取资讯轮播图\r\nconst getNewsData = async () => {\r\n  let params = {\r\n    isAnd: 1,\r\n    objectParam: {},\r\n    orderBys: [],\r\n    pageNo: 1,\r\n    pageSize: 10,\r\n    query: { columnId: '1887325961586761729', moduleId: '1', passFlag: '' },\r\n    tableId: 'zy_news_content_1',\r\n    wheres: [\r\n      { columnId: 'zy_news_content_1_is_top', queryType: 'EQ', value: '1' }\r\n    ]\r\n  }\r\n  const res = await api.newsContentList(params)\r\n  var { data } = res\r\n  newsData.value = data || []\r\n  getSpecialCommitteeData() // 通知公告\r\n  if (window.location.host === 'localhost:2000' || window.location.host === 'xazx.cszysoft.com:8131') {\r\n    getSuggestionData('1735594801618776066') // 假设这是委员风采栏目ID  测试\r\n    getCommitteeMembersData('1721347440629542913')\r\n  } else {\r\n    getSuggestionData('1928270222481985537') // 假设这是委员风采栏目ID  正式\r\n    getCommitteeMembersData('1928269626215534594')\r\n  }\r\n}\r\n\r\n// 处理轮播图指示器点击事件\r\nconst handleIndicatorClick = (index) => {\r\n  if (carouselRef.value) {\r\n    carouselRef.value.setActiveItem(index);\r\n  }\r\n}\r\n\r\n// 处理轮播图切换事件\r\nconst handleCarouselChange = (index) => {\r\n  currentCarouselIndex.value = index;\r\n}\r\n\r\n// 获取资讯栏目\r\nconst getNewsColumnData = async () => {\r\n  let params = {\r\n    pageNo: 1,\r\n    pageSize: 10,\r\n    query: { moduleId: '1' }\r\n  }\r\n  const res = await api.newsColumnList(params)\r\n  var { data } = res\r\n  console.log('获取资讯栏目数据', data)\r\n  newColumnData.value = data || []\r\n  getInformationData(data[0].id) // 默认获取第一个栏目下的数据\r\n}\r\n\r\n// 点击资讯栏目\r\nconst clickNewColumn = (item, index) => {\r\n  activeNewsTabIndex.value = index\r\n  getInformationData(item.id)\r\n}\r\n\r\n// 获取资讯栏目下的数据\r\nconst getInformationData = async (id) => {\r\n  loadingInformationList.value = true\r\n  let params = {\r\n    isAnd: 1,\r\n    objectParam: {},\r\n    orderBys: [],\r\n    pageNo: 1,\r\n    pageSize: 10,\r\n    query: { columnId: id, moduleId: '1', passFlag: '' },\r\n    tableId: 'zy_news_content_1',\r\n    wheres: []\r\n  }\r\n  const res = await api.newsContentList(params)\r\n  var { data } = res\r\n  informationList.value = data || []\r\n  loadingInformationList.value = false\r\n}\r\n\r\n// 获取通知公告\r\nconst getSpecialCommitteeData = async (id) => {\r\n  loadingNoticeList.value = true\r\n  let params = {\r\n    isAnd: 1,\r\n    isSelectForManager: 1,\r\n    orderBys: [],\r\n    pageNo: 1,\r\n    pageSize: 5,\r\n    query: { isDraft: 0, channelId: null },\r\n    tableId: 'id_message_notification',\r\n    wheres: []\r\n  }\r\n  const res = await api.noticeHomePage(params)\r\n  var { data } = res\r\n  noticeData.value = data || []\r\n  loadingNoticeList.value = false\r\n}\r\n\r\n// 获取委员风采栏目下的数据\r\nconst getSuggestionData = async (id) => {\r\n  loadingMemberStyle.value = true\r\n  let params = {\r\n    isAnd: 1,\r\n    objectParam: {},\r\n    orderBys: [],\r\n    pageNo: 1,\r\n    pageSize: 10,\r\n    query: { columnId: id, moduleId: '7', passFlag: '' },\r\n    tableId: 'content_information_7',\r\n    wheres: []\r\n  }\r\n  const res = await api.newsContentList(params)\r\n  var { data } = res\r\n  memberStyleData.value = data || []\r\n  loadingMemberStyle.value = false\r\n}\r\n\r\n// 获取委员眼中的西安栏目下的数据\r\nconst getCommitteeMembersData = async (id) => {\r\n  loadingCommitteeMembersList.value = true\r\n  let params = {\r\n    isAnd: 1,\r\n    objectParam: {},\r\n    orderBys: [],\r\n    pageNo: 1,\r\n    pageSize: 10,\r\n    query: { columnId: id, moduleId: '7', passFlag: '' },\r\n    tableId: 'content_information_7',\r\n    wheres: []\r\n  }\r\n  const res = await api.newsContentList(params)\r\n  var { data } = res\r\n  committeeMembersData.value = data || []\r\n  loadingCommitteeMembersList.value = false\r\n}\r\n\r\n// 时间转换\r\nconst formatTime = (time) => {\r\n  time = Number(time)\r\n  return new Date(time).toLocaleString()\r\n}\r\n\r\n// 资讯详情处理\r\nconst newsHandle = (item) => {\r\n  id.value = item.id\r\n  newsShow.value = true\r\n}\r\n\r\n// 通知公告详情\r\nconst noticeInfo = (item) => {\r\n  id.value = item.id\r\n  noticeShow.value = true\r\n}\r\n\r\n// 打开资讯列表\r\nconst openWinNews = async (_item, _type) => {\r\n  let id = ''\r\n  if (_type == 2) {\r\n    openPage({ key: 'routePath', value: '/information/AllInformationPublicList?moduleId=1' })\r\n  } else if (_type == 3) {\r\n    if (window.location.host === 'localhost:2000' || window.location.host === 'xazx.cszysoft.com:8131') {\r\n      id = '1735594801618776066'\r\n    } else {\r\n      id = '1928270222481985537'\r\n    }\r\n    openPage({ key: 'routePath', value: '/information/MemberStyleList?moduleId=7&columnId=' + id })\r\n  } else if (_type == 4) {\r\n    if (window.location.host === 'localhost:2000' || window.location.host === 'xazx.cszysoft.com:8131') {\r\n      id = '1721347440629542913'\r\n    } else {\r\n      id = '1928269626215534594'\r\n    }\r\n    openPage({ key: 'routePath', value: '/information/CommitteeEyesXiAn?moduleId=7&columnId=' + id })\r\n  }\r\n}\r\n\r\n// 打开通知公告更多\r\nconst openWinNotice = () => {\r\n  openPage({ key: 'routePath', value: '/interaction/NoticeAnnouncementList' })\r\n}\r\n\r\n// 弹窗回调\r\nconst callback = () => {\r\n  id.value = \"\"\r\n  newsShow.value = false\r\n  noticeShow.value = false\r\n}\r\n\r\nonMounted(() => {\r\n  getNewsData()\r\n  getNewsColumnData()\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\nbody {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n.homePageNavigationBar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px 0;\r\n\r\n  .nav-item {\r\n    color: #4f4f4f;\r\n    font-size: 18px;\r\n    cursor: pointer;\r\n    padding: 0 10px;\r\n    white-space: nowrap;\r\n\r\n    &:hover {\r\n      color: #0056b3;\r\n    }\r\n\r\n    &.active {\r\n      color: #007bff;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  .separator {\r\n    color: #d3d3d3;\r\n    margin: 0 5px;\r\n  }\r\n}\r\n\r\n.homePage {\r\n  width: calc(100% - 500px);\r\n  margin: auto;\r\n  height: calc(100% - 64px);\r\n  background: #fff;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n\r\n  .zy-el-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    border: 1px solid #eee;\r\n    border-radius: 8px;\r\n    padding: 0;\r\n    overflow: hidden;\r\n\r\n    .top-section,\r\n    .bottom-section {\r\n      display: flex;\r\n      gap: 35px;\r\n      padding: 30px 40px;\r\n      background-color: #fff;\r\n    }\r\n\r\n    .top-section {\r\n      border-bottom: 1px solid #eee;\r\n      height: 450px;\r\n\r\n      .carousel-container {\r\n        width: 40%;\r\n        background-color: #ffffff;\r\n        border-radius: 8px;\r\n        overflow: hidden;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        position: relative;\r\n\r\n        .zy-el-carousel {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n\r\n        .carousel-img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: cover;\r\n        }\r\n\r\n        .placeholder-img {\r\n          width: 100%;\r\n          height: 100%;\r\n          background-color: #ccc;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          color: #666;\r\n          font-size: 18px;\r\n        }\r\n\r\n        .carousel-overlay {\r\n          position: absolute;\r\n          bottom: 0;\r\n          left: 0;\r\n          right: 0;\r\n          background-color: rgba(0, 0, 0, 0.5);\r\n          color: white;\r\n          padding: 15px 20px;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          z-index: 1;\r\n\r\n          .overlay-title {\r\n            font-size: 18px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            flex-grow: 1;\r\n            margin-right: 10px;\r\n          }\r\n\r\n          .overlay-indicator {\r\n            display: flex;\r\n            gap: 5px;\r\n            z-index: 10;\r\n\r\n            .indicator-dot {\r\n              width: 8px;\r\n              height: 8px;\r\n              background-color: rgba(255, 255, 255, 0.5);\r\n              border-radius: 50%;\r\n              cursor: pointer;\r\n            }\r\n\r\n            .indicator-dot.active {\r\n              background-color: white;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .carousel-container :deep(.zy-el-carousel__container) {\r\n        height: 100% !important;\r\n      }\r\n\r\n      .carousel-container :deep(.zy-el-carousel__indicators) {\r\n        display: none;\r\n      }\r\n\r\n      .news-tabs-container {\r\n        width: 60%;\r\n        background: rgb(248, 249, 253);\r\n        border-radius: 8px;\r\n        padding: 20px 0;\r\n        position: relative;\r\n\r\n        .news-tabs-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 0 20px;\r\n          position: relative;\r\n\r\n          .tabs-scroll-container {\r\n            display: flex;\r\n            align-items: center;\r\n            overflow-x: auto;\r\n            overflow-y: hidden;\r\n            max-width: calc(100% - 80px);\r\n            white-space: nowrap;\r\n            padding-bottom: 8px;\r\n          }\r\n\r\n          .news-tab-item {\r\n            font-size: 20px;\r\n            color: #606266;\r\n            padding: 0 15px;\r\n            height: 40px;\r\n            line-height: 40px;\r\n            cursor: pointer;\r\n            background-color: #fefefe;\r\n            border: 1px solid #eee;\r\n            border-radius: 4px;\r\n            margin-right: 10px;\r\n            flex-shrink: 0;\r\n\r\n            &.active {\r\n              color: #fff;\r\n              background-color: #007bff;\r\n              font-weight: bold;\r\n              position: relative;\r\n\r\n              &::after {\r\n                content: '';\r\n                position: absolute;\r\n                bottom: -10px;\r\n                left: 50%;\r\n                transform: translateX(-50%);\r\n                width: 0;\r\n                height: 0;\r\n                border-left: 6px solid transparent;\r\n                border-right: 6px solid transparent;\r\n                border-top: 10px solid #007bff;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .news-tabs-content {\r\n          height: 100%;\r\n\r\n          .news-list {\r\n            margin-top: 10px;\r\n            padding: 0 20px;\r\n            height: calc(100% - 50px);\r\n            overflow: hidden;\r\n\r\n            .news-item {\r\n              margin-bottom: 20px;\r\n              font-size: 18px;\r\n              color: #333;\r\n              display: flex;\r\n              align-items: flex-start;\r\n              cursor: pointer;\r\n\r\n              .dot {\r\n                margin-right: 5px;\r\n                color: #8a8a8a;\r\n                flex-shrink: 0;\r\n                margin-right: 10px;\r\n              }\r\n\r\n              .title {\r\n                flex-grow: 1;\r\n                white-space: nowrap;\r\n                overflow: hidden;\r\n                text-overflow: ellipsis;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .bottom-section {\r\n      padding: 15px 40px;\r\n      height: 370px;\r\n      width: 100%;\r\n\r\n      .committee-work-container {\r\n        width: 33.33%;\r\n      }\r\n\r\n      .committee-work-container,\r\n      .suggestions-container {\r\n        background-color: #ffffff;\r\n        border-radius: 8px;\r\n        position: relative;\r\n        height: 100%;\r\n\r\n        .committee_title_box {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding-bottom: 10px;\r\n\r\n          .committee_title {\r\n            font-size: 20px;\r\n            color: #333;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n\r\n        .list-container {\r\n          height: calc(100% - 35px);\r\n\r\n          .list-item {\r\n            margin-bottom: 15px;\r\n            font-size: 18px;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: flex-start;\r\n            cursor: pointer;\r\n\r\n            .dot {\r\n              margin-right: 5px;\r\n              color: #007bff;\r\n              flex-shrink: 0;\r\n            }\r\n\r\n            .title {\r\n              flex-grow: 1;\r\n              display: -webkit-box;\r\n              -webkit-line-clamp: 1;\r\n              -webkit-box-orient: vertical;\r\n              overflow: hidden;\r\n              margin-right: 10px;\r\n              line-height: 26px;\r\n            }\r\n\r\n            .date {\r\n              font-size: 14px;\r\n              color: #999;\r\n              flex-shrink: 0;\r\n              margin-top: 6px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .suggestions_title_box {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding-bottom: 10px;\r\n\r\n          .suggestions_title {\r\n            font-size: 20px;\r\n            color: #333;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 确保滚动条样式生效的全局样式 */\r\n.news-tabs-container .tabs-scroll-container::-webkit-scrollbar {\r\n  height: 8px !important;\r\n}\r\n\r\n.news-tabs-container .tabs-scroll-container::-webkit-scrollbar-track {\r\n  background: #f5f5f5 !important;\r\n  border-radius: 10px !important;\r\n  margin: 0 4px !important;\r\n}\r\n\r\n.news-tabs-container .tabs-scroll-container::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(90deg, #d0d0d0, #b8b8b8, #d0d0d0) !important;\r\n  border-radius: 10px !important;\r\n  border: 1px solid #e0e0e0 !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.news-tabs-container .tabs-scroll-container::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(90deg, #b8b8b8, #9a9a9a, #b8b8b8) !important;\r\n  border-color: #c0c0c0 !important;\r\n}\r\n\r\n.news-tabs-container .tabs-scroll-container::-webkit-scrollbar-thumb:active {\r\n  background: linear-gradient(90deg, #a0a0a0, #808080, #a0a0a0) !important;\r\n}\r\n\r\n@media screen and (max-width: 1280px) {\r\n  .news-header {\r\n    display: flex;\r\n    margin-bottom: 30px;\r\n    height: 100%;\r\n    cursor: pointer;\r\n    flex-direction: column;\r\n\r\n    img {\r\n      max-width: 100%;\r\n      height: 172px;\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.more-link {\r\n  cursor: pointer;\r\n  color: #989898;\r\n}\r\n</style>\r\n"], "mappings": ";;EAQOA,KAAK,EAAC;AAAuB;iBARpC;;EAAAC,GAAA;EAa+CD,KAAK,EAAC;;;EAIxCA,KAAK,EAAC;AAAiB;;EACzBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAoB;iBAnBvC;;EAAAC,GAAA;EAuB0BD,KAAK,EAAC;;;EACbA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAmB;kBA1B9C;;EAkCaA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAuB;kBApC9C;;EA4CeA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAW;;EA7ClCC,GAAA;EA+CqBD,KAAK,EAAC,WAAW;EACpBE,KAAyF,EAAzF;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;;kBAhDlB;;EAqDwBF,KAAK,EAAC;AAAO;;EAO1BA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAA0B;;EAK9BA,KAAK,EAAC;AAAgB;;EAnErCC,GAAA;EAqEmBD,KAAK,EAAC,WAAW;EACpBE,KAAyF,EAAzF;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;;kBAtEhB;;EA4EuBF,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAM;;EAOtBA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAqB;;EAI3BA,KAAK,EAAC;AAAgB;;EAzFrCC,GAAA;EA2FmBD,KAAK,EAAC,WAAW;EACpBE,KAAyF,EAAzF;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;;kBA5FhB;kBAAA;;EAmGuBF,KAAK,EAAC;AAAM;;EAOtBA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAqB;;EAI3BA,KAAK,EAAC;AAAgB;;EA/GrCC,GAAA;EAiHmBD,KAAK,EAAC,WAAW;EACpBE,KAAyF,EAAzF;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;;kBAlHhB;;EAwHuBF,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAM;;;;;;;uBAzHnCG,mBAAA,CAAAC,SAAA,SAQEC,mBAAA,CAOM,OAPNC,UAOM,I,kBANJH,mBAAA,CAKWC,SAAA,QAdfG,WAAA,CASsCC,MAAA,CAAAC,QAAQ,EAT9C,UASsBC,IAAI,EAAEC,KAAK;yBATjCR,mBAAA,CAAAC,SAAA;MAAAH,GAAA,EASsDU;IAAK,IACrDN,mBAAA,CAES;MAFHL,KAAK,EAVjBY,eAAA,EAUkB,UAAU;QAAAC,MAAA,EAAmBL,MAAA,CAAAM,aAAa,KAAKH;MAAK;MAAKI,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAER,MAAA,CAAAS,eAAe,CAACP,IAAI,EAAEC,KAAK;MAAA;wBACrGD,IAAI,CAACQ,IAAI,gCAXjBC,UAAA,GAakBR,KAAK,GAAGH,MAAA,CAAAC,QAAQ,CAACW,MAAM,Q,cAAnCjB,mBAAA,CAAmE,QAAnEkB,UAAmE,EAAR,GAAC,KAblEC,mBAAA,e;oCAgBEC,YAAA,CAkHeC,uBAAA;IAlHDxB,KAAK,EAAC;EAAU;IAhBhCyB,OAAA,EAAAC,QAAA,CAiBI;MAAA,OAgHU,CAhHVrB,mBAAA,CAgHU,WAhHVsB,UAgHU,GA/GRtB,mBAAA,CAyCM,OAzCNuB,UAyCM,GAxCJvB,mBAAA,CAcM,OAdNwB,UAcM,GAbJN,YAAA,CAYcO,sBAAA;QAZAC,QAAQ,EAAE,IAAI;QAAE,oBAAkB,EAAC,MAAM;QAAEC,QAAM,EAAExB,MAAA,CAAAyB,oBAAoB;QAAEC,GAAG,EAAC;;QApBrGT,OAAA,EAAAC,QAAA,CAqB8B;UAAA,OAAwB,E,kBAA1CvB,mBAAA,CAUmBC,SAAA,QA/B/BG,WAAA,CAqB6CC,MAAA,CAAA2B,QAAQ,EArBrD,UAqBqCzB,IAAI;iCAA7B0B,YAAA,CAUmBC,2BAAA;cAVyBpC,GAAG,EAAES,IAAI,CAAC4B,EAAE;cAAGvB,OAAK,WAALA,OAAKA,CAAAC,MAAA;gBAAA,OAAER,MAAA,CAAA+B,UAAU,CAAC7B,IAAI;cAAA;;cArB7Fe,OAAA,EAAAC,QAAA,CA2BsB;gBAAA,OAA4E,CALzEhB,IAAI,CAAC8B,OAAO,I,cAAvBrC,mBAAA,CAA4E;kBAtB1FF,GAAA;kBAsBwCwC,GAAG,EAAEjC,MAAA,CAAAkC,MAAM,CAAChC,IAAI,CAAC8B,OAAO;kBAAGxC,KAAK,EAAC;wCAtBzE2C,UAAA,M,cAuBcxC,mBAAA,CAAuD,OAAvDyC,UAAuD,IACvDvC,mBAAA,CAMM,OANNwC,UAMM,GALJxC,mBAAA,CAAqD,OAArDyC,WAAqD,EAAAC,gBAAA,CAAvBrC,IAAI,CAACsC,SAAS,kBAC5C3C,mBAAA,CAGM,OAHN4C,WAGM,I,kBAFJ9C,mBAAA,CACkGC,SAAA,QA5BpHG,WAAA,CA2B+CC,MAAA,CAAA2B,QAAQ,CAACf,MAAM,EA3B9D,UA2BgC8B,GAAG,EAAEvC,KAAK;uCAAxBR,mBAAA,CACkG;oBADnDF,GAAG,EAAEU,KAAK;oBAAEX,KAAK,EA3BlFY,eAAA,EA2BmF,eAAe;sBAAAC,MAAA,EAC5DF,KAAK,KAAKH,MAAA,CAAA2C;oBAAoB;oBAAKpC,OAAK,WAALA,OAAKA,CAAAC,MAAA;sBAAA,OAAER,MAAA,CAAA4C,oBAAoB,CAACzC,KAAK;oBAAA;kDA5B1G0C,WAAA;;;cAAAC,CAAA;;;;QAAAA,CAAA;kCAkCQjD,mBAAA,CAwBM,OAxBNkD,WAwBM,GAvBJlD,mBAAA,CAQM,OARNmD,WAQM,GAPJnD,mBAAA,CAKM,OALNoD,WAKM,I,kBAJJtD,mBAAA,CAGMC,SAAA,QAxCpBG,WAAA,CAqCiEC,MAAA,CAAAkD,aAAa,EArC9E,UAqCiDhD,IAAI,EAAEC,KAAK;6BAA9CR,mBAAA,CAGM;UAHDH,KAAK,EArCxBY,eAAA,EAqCyB,eAAe;YAAAC,MAAA,EACNL,MAAA,CAAAmD,kBAAkB,KAAKhD;UAAK;UAAKI,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAER,MAAA,CAAAoD,cAAc,CAAClD,IAAI,EAAEC,KAAK;UAAA;YACpFN,mBAAA,CAA4B,cAAA0C,gBAAA,CAAnBrC,IAAI,CAACQ,IAAI,iB,yBAvClC2C,WAAA;0CA0CYxD,mBAAA,CAAiG;QAA5FL,KAAK,EAAC,WAAW;QAAEe,OAAK,EAAA+C,MAAA,QAAAA,MAAA,gBAAA9C,MAAA;UAAA,OAAER,MAAA,CAAAuD,WAAW,CAACvD,MAAA,CAAAwD,eAAe,CAACxD,MAAA,CAAAmD,kBAAkB,EAAErB,EAAE;QAAA;SAAM,MAAI,E,GAE7FjC,mBAAA,CAaM,OAbN4D,WAaM,G,+BAZJ9D,mBAAA,CAWM,OAXN+D,WAWM,GAVY1D,MAAA,CAAAwD,eAAe,CAAC5C,MAAM,U,cACpCjB,mBAAA,CACsG,OADtGgE,WACsG,EAAV,MAAI,M,kBAGhGhE,mBAAA,CAGMC,SAAA;QAtDtBH,GAAA;MAAA,GAAAM,WAAA,CAmDoCC,MAAA,CAAAwD,eAAe,EAnDnD,UAmD4BtD,IAAI;6BAAhBP,mBAAA,CAGM;UAHgCF,GAAG,EAAES,IAAI,CAAC4B,EAAE;UAAEtC,KAAK,EAAC,WAAW;UAAEe,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAER,MAAA,CAAA+B,UAAU,CAAC7B,IAAI;UAAA;sCAC3FL,mBAAA,CAA0B;UAApBL,KAAK,EAAC;QAAK,GAAC,GAAC,sBACnBK,mBAAA,CAA+C,QAA/C+D,WAA+C,EAAArB,gBAAA,CAAxBrC,IAAI,CAACsC,SAAS,iB,iBArDvDqB,WAAA;+DA6C8C7D,MAAA,CAAA8D,sBAAsB,E,SAe9DjE,mBAAA,CAoEM,OApENkE,WAoEM,GAnEJjD,mBAAA,UAAa,EACbjB,mBAAA,CAoBM,OApBNmE,WAoBM,GAnBJnE,mBAAA,CAGM;QAHDL,KAAK,EAAC;MAAqB,I,0BAC9BK,mBAAA,CAAuC;QAAlCL,KAAK,EAAC;MAAiB,GAAC,MAAI,sBACjCK,mBAAA,CAAwD;QAAnDL,KAAK,EAAC,WAAW;QAAEe,OAAK,EAAEP,MAAA,CAAAiE;SAAe,MAAI,E,kCAEpDtE,mBAAA,CAcM,OAdNuE,WAcM,GAbYlE,MAAA,CAAAmE,UAAU,CAACvD,MAAM,U,cAC/BjB,mBAAA,CACsG,OADtGyE,WACsG,EAAV,MAAI,M,kBAGhGzE,mBAAA,CAMMC,SAAA;QA/EpBH,GAAA;MAAA,GAAAM,WAAA,CAyEkCC,MAAA,CAAAmE,UAAU,EAzE5C,UAyE0BjE,IAAI;6BAAhBP,mBAAA,CAMM;UAN2BF,GAAG,EAAES,IAAI,CAAC4B,EAAE;UAAEtC,KAAK,EAAC,WAAW;UAAEe,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAER,MAAA,CAAAqE,UAAU,CAACnE,IAAI;UAAA;sCACtFL,mBAAA,CAA0B;UAApBL,KAAK,EAAC;QAAK,GAAC,GAAC,sBACnBK,mBAAA,CAGM,cAFJA,mBAAA,CAAyC,OAAzCyE,WAAyC,EAAA/B,gBAAA,CAAnBrC,IAAI,CAACqE,KAAK,kBAChC1E,mBAAA,CAAyD,OAAzD2E,WAAyD,EAAAjC,gBAAA,CAApCvC,MAAA,CAAAyE,UAAU,CAACvE,IAAI,CAACwE,UAAU,kB,mBA7EjEC,WAAA;+DAmEiD3E,MAAA,CAAA4E,iBAAiB,E,KAgB1D9D,mBAAA,UAAa,EACbjB,mBAAA,CAoBM,OApBNgF,WAoBM,GAnBJhF,mBAAA,CAGM,OAHNiF,WAGM,G,0BAFJjF,mBAAA,CAAuC;QAAlCL,KAAK,EAAC;MAAiB,GAAC,MAAI,sBACjCK,mBAAA,CAA+D;QAA1DL,KAAK,EAAC,WAAW;QAAEe,OAAK,EAAA+C,MAAA,QAAAA,MAAA,gBAAA9C,MAAA;UAAA,OAAER,MAAA,CAAAuD,WAAW;QAAA;SAAW,MAAI,E,kCAE3D5D,mBAAA,CAcM,OAdNoF,WAcM,GAbY/E,MAAA,CAAAgF,eAAe,CAACpE,MAAM,U,cACpCjB,mBAAA,CACsG,OADtGsF,WACsG,EAAV,MAAI,M,kBAGhGtF,mBAAA,CAMMC,SAAA;QArGpBH,GAAA;MAAA,GAAAM,WAAA,CA+FkCC,MAAA,CAAAgF,eAAe,EA/FjD,UA+F0B9E,IAAI;6BAAhBP,mBAAA,CAMM;UANgCF,GAAG,EAAES,IAAI,CAAC4B,EAAE;UAAEtC,KAAK,EAAC,WAAW;UAAEe,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAER,MAAA,CAAA+B,UAAU,CAAC7B,IAAI;UAAA;sCAC3FL,mBAAA,CAA0B;UAApBL,KAAK,EAAC;QAAK,GAAC,GAAC,sBACnBK,mBAAA,CAGM,cAFJA,mBAAA,CAAqE;UAAhEL,KAAK,EAAC,OAAO;UAAE0F,KAAK,EAAEhF,IAAI,CAACsC;4BAActC,IAAI,CAACsC,SAAS,wBAlG9E2C,WAAA,GAmGkBtF,mBAAA,CAAyD,OAAzDuF,WAAyD,EAAA7C,gBAAA,CAApCvC,MAAA,CAAAyE,UAAU,CAACvE,IAAI,CAACwE,UAAU,kB,mBAnGjEW,WAAA;+DAyFiDrF,MAAA,CAAAsF,kBAAkB,E,KAgB3DxE,mBAAA,aAAgB,EAChBjB,mBAAA,CAqBM,OArBN0F,WAqBM,GApBJ1F,mBAAA,CAGM,OAHN2F,WAGM,G,4BAFJ3F,mBAAA,CAA0C;QAArCL,KAAK,EAAC;MAAiB,GAAC,SAAO,sBACpCK,mBAAA,CAA+D;QAA1DL,KAAK,EAAC,WAAW;QAAEe,OAAK,EAAA+C,MAAA,QAAAA,MAAA,gBAAA9C,MAAA;UAAA,OAAER,MAAA,CAAAuD,WAAW;QAAA;SAAW,MAAI,E,kCAE3D5D,mBAAA,CAeM,OAfN8F,WAeM,GAdYzF,MAAA,CAAA0F,oBAAoB,CAAC9E,MAAM,U,cACzCjB,mBAAA,CACsG,OADtGgG,WACsG,EAAV,MAAI,M,kBAGhGhG,mBAAA,CAMMC,SAAA;QA3HpBH,GAAA;MAAA,GAAAM,WAAA,CAqHkCC,MAAA,CAAA0F,oBAAoB,EArHtD,UAqH0BxF,IAAI;6BAAhBP,mBAAA,CAMM;UANqCF,GAAG,EAAES,IAAI,CAAC4B,EAAE;UAAEtC,KAAK,EAAC,WAAW;UAAEe,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAER,MAAA,CAAA+B,UAAU,CAAC7B,IAAI;UAAA;wCAChGL,mBAAA,CAA0B;UAApBL,KAAK,EAAC;QAAK,GAAC,GAAC,sBACnBK,mBAAA,CAGM,cAFJA,mBAAA,CAA6C,OAA7C+F,WAA6C,EAAArD,gBAAA,CAAvBrC,IAAI,CAACsC,SAAS,kBACpC3C,mBAAA,CAAyD,OAAzDgG,WAAyD,EAAAtD,gBAAA,CAApCvC,MAAA,CAAAyE,UAAU,CAACvE,IAAI,CAACwE,UAAU,kB,mBAzHjEoB,WAAA;+DA+GiD9F,MAAA,CAAA+F,2BAA2B,E;;IA/G5EjD,CAAA;MAmIE/B,YAAA,CAEmBiF,2BAAA;IArIrBC,UAAA,EAmI6BjG,MAAA,CAAAkG,QAAQ;IAnIrC,uBAAA5C,MAAA,QAAAA,MAAA,gBAAA9C,MAAA;MAAA,OAmI6BR,MAAA,CAAAkG,QAAQ,GAAA1F,MAAA;IAAA;IAAEE,IAAI,EAAC;;IAnI5CO,OAAA,EAAAC,QAAA,CAoII;MAAA,OAA2E,CAA3EH,YAAA,CAA2Ef,MAAA;QAApD8B,EAAE,EAAE9B,MAAA,CAAA8B,EAAE;QAAGqE,UAAQ,EAAEnG,MAAA,CAAAoG;;;IApI9CtD,CAAA;qCAsIE/B,YAAA,CAEmBiF,2BAAA;IAxIrBC,UAAA,EAsI6BjG,MAAA,CAAAqG,UAAU;IAtIvC,uBAAA/C,MAAA,QAAAA,MAAA,gBAAA9C,MAAA;MAAA,OAsI6BR,MAAA,CAAAqG,UAAU,GAAA7F,MAAA;IAAA;IAAEE,IAAI,EAAC;;IAtI9CO,OAAA,EAAAC,QAAA,CAuII;MAAA,OAAqF,CAArFH,YAAA,CAAqFf,MAAA;QAAzD8B,EAAE,EAAE9B,MAAA,CAAA8B,EAAE;QAAGqE,UAAQ,EAAEnG,MAAA,CAAAoG;;;IAvInDtD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}