{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, vShow as _vShow, withDirectives as _withDirectives, normalizeStyle as _normalizeStyle } from \"vue\";\nimport _imports_0 from '../img/search_icon.png';\nimport _imports_1 from '../img/mine_btn_bg.png';\nimport _imports_2 from '../img/mine_icon.png';\nvar _hoisted_1 = {\n  class: \"login-header\"\n};\nvar _hoisted_2 = {\n  class: \"login-logo-name\"\n};\nvar _hoisted_3 = [\"innerHTML\"];\nvar _hoisted_4 = {\n  class: \"login-header-right\"\n};\nvar _hoisted_5 = {\n  class: \"search-box\"\n};\nvar _hoisted_6 = {\n  class: \"LoginViewBox\"\n};\nvar _hoisted_7 = {\n  class: \"input-bg\"\n};\nvar _hoisted_8 = {\n  class: \"input-bg\"\n};\nvar _hoisted_9 = {\n  key: 1,\n  class: \"LoginViewSlideVerify\"\n};\nvar _hoisted_10 = {\n  class: \"LoginViewFormOperation\"\n};\nvar _hoisted_11 = {\n  key: 0,\n  class: \"LoginViewOperation\"\n};\nvar _hoisted_12 = {\n  class: \"LoginViewOperationBox\"\n};\nvar _hoisted_13 = {\n  class: \"LoginViewQrCodeBox\"\n};\nvar _hoisted_14 = {\n  class: \"LoginViewQrCodeNameBody\"\n};\nvar _hoisted_15 = {\n  class: \"LoginViewQrCodeLogo\"\n};\nvar _hoisted_16 = {\n  class: \"LoginViewQrCodeRefreshBody\"\n};\nvar _hoisted_17 = {\n  class: \"LoginViewQrCodeRefresh\"\n};\nvar _hoisted_18 = {\n  class: \"LoginViewQrCodeText\"\n};\nvar _hoisted_19 = {\n  class: \"LoginViewOperationBox\"\n};\nvar _hoisted_20 = {\n  class: \"LoginViewQrCodeBox\"\n};\nvar _hoisted_21 = {\n  class: \"LoginViewQrCodeNameBody\"\n};\nvar _hoisted_22 = {\n  class: \"LoginViewQrCodeLogo\"\n};\nvar _hoisted_23 = {\n  class: \"LoginViewQrCodeText\"\n};\nvar _hoisted_24 = {\n  key: 1,\n  class: \"LoginViewSystemTips\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_xyl_slide_verify = _resolveComponent(\"xyl-slide-verify\");\n  var _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"LoginViewCopy\",\n    style: _normalizeStyle({\n      backgroundImage: `url(${$setup.backgroundImage})`\n    })\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n    class: \"login-logo\",\n    src: $setup.systemLogo,\n    fit: \"contain\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", {\n    class: \"login-name\",\n    innerHTML: $setup.systemName\n  }, null, 8 /* PROPS */, _hoisted_3)]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[11] || (_cache[11] = _createElementVNode(\"input\", {\n    type: \"text\",\n    placeholder: \"请输入搜索内容\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    class: \"search-btn\",\n    onClick: _cache[0] || (_cache[0] = function () {\n      return _ctx.search && _ctx.search.apply(_ctx, arguments);\n    })\n  }, _cache[10] || (_cache[10] = [_createElementVNode(\"img\", {\n    class: \"search-icon\",\n    src: _imports_0,\n    alt: \"搜索\"\n  }, null, -1 /* HOISTED */)]))]), _createElementVNode(\"button\", {\n    class: \"mine-btn\",\n    onClick: _cache[1] || (_cache[1] = function () {\n      return _ctx.openLogin && _ctx.openLogin.apply(_ctx, arguments);\n    })\n  }, _cache[12] || (_cache[12] = [_createElementVNode(\"img\", {\n    src: _imports_1,\n    alt: \"我的\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", null, [_createElementVNode(\"img\", {\n    class: \"mine-icon\",\n    src: _imports_2,\n    alt: \"我的\"\n  }), _createTextVNode(\" 我的 \")], -1 /* HOISTED */)]))])]), _createElementVNode(\"div\", _hoisted_6, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    class: \"LoginViewName\"\n  }, \"欢迎登录\", -1 /* HOISTED */)), _createVNode(_component_el_form, {\n    ref: \"LoginForm\",\n    model: $setup.form,\n    rules: $setup.rules,\n    class: \"LoginViewForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        prop: \"account\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_input, {\n            modelValue: $setup.form.account,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.account = $event;\n            }),\n            placeholder: \"账号/手机号\",\n            onBlur: $setup.handleBlur,\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"onBlur\"])])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        prop: \"password\"\n      }, {\n        default: _withCtx(function () {\n          return [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_input, {\n            type: \"password\",\n            modelValue: $setup.form.password,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n              return $setup.form.password = $event;\n            }),\n            placeholder: \"密码\",\n            \"show-password\": \"\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])])];\n        }),\n        _: 1 /* STABLE */\n      }), $setup.loginVerifyShow && $setup.whetherVerifyCode ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        class: \"smsValidation\",\n        prop: \"verifyCode\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.verifyCode,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n              return $setup.form.verifyCode = $event;\n            }),\n            placeholder: \"短信验证码\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: $setup.handleGetVerifyCode,\n            disabled: $setup.countDownText != '获取验证码'\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString($setup.countDownText), 1 /* TEXT */)];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onClick\", \"disabled\"])];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $setup.loginVerifyShow && !$setup.whetherVerifyCode ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_xyl_slide_verify, {\n        ref: \"slideVerify\",\n        onAgain: $setup.onAgain,\n        onSuccess: $setup.onSuccess,\n        disabled: $setup.disabled\n      }, null, 8 /* PROPS */, [\"onAgain\", \"onSuccess\", \"disabled\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_checkbox, {\n        modelValue: $setup.checked,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n          return $setup.checked = $event;\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[13] || (_cache[13] = [_createTextVNode(\"记住用户名和密码\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"div\", {\n        class: \"LoginViewFormOperationText\",\n        onClick: _cache[6] || (_cache[6] = function ($event) {\n          return $setup.show = !$setup.show;\n        })\n      }, \"忘记密码？\")]), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[7] || (_cache[7] = function ($event) {\n          return $setup.submitForm($setup.LoginForm);\n        }),\n        class: \"LoginViewFormButton login-btn-bg\",\n        loading: $setup.loading,\n        disabled: $setup.loginDisabled\n      }, {\n        default: _withCtx(function () {\n          return [_createTextVNode(_toDisplayString($setup.loading ? '登录中' : '登录'), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"loading\", \"disabled\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"]), $setup.appDownloadUrl ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_popover, {\n    placement: \"top\",\n    width: \"auto\",\n    onShow: $setup.refresh,\n    onHide: $setup.hideQrcode\n  }, {\n    reference: _withCtx(function () {\n      return _cache[16] || (_cache[16] = [_createElementVNode(\"div\", {\n        class: \"LoginViewQrCode\"\n      }, null, -1 /* HOISTED */)]);\n    }),\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_image, {\n        src: $setup.systemLogo,\n        fit: \"cover\"\n      }, null, 8 /* PROPS */, [\"src\"])]), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n        class: \"LoginViewQrCodeName\"\n      }, \"APP扫码登录\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_16, [_createVNode($setup[\"QrcodeVue\"], {\n        value: $setup.loginQrcode,\n        size: 120\n      }, null, 8 /* PROPS */, [\"value\"]), _withDirectives(_createElementVNode(\"div\", _hoisted_17, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.refresh\n      }, {\n        default: _withCtx(function () {\n          return _cache[15] || (_cache[15] = [_createTextVNode(\"刷新\")]);\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])], 512 /* NEED_PATCH */), [[_vShow, $setup.loginQrcodeShow]])]), _createElementVNode(\"div\", _hoisted_18, \"请使用\" + _toDisplayString($setup.systemName) + \"APP扫码登录\", 1 /* TEXT */)])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onShow\", \"onHide\"]), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n    class: \"LoginViewOperationText\"\n  }, \"APP扫码登录\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_popover, {\n    placement: \"top\",\n    width: \"auto\"\n  }, {\n    reference: _withCtx(function () {\n      return _cache[19] || (_cache[19] = [_createElementVNode(\"div\", {\n        class: \"LoginViewApp\"\n      }, null, -1 /* HOISTED */)]);\n    }),\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_image, {\n        src: $setup.systemLogo,\n        fit: \"cover\"\n      }, null, 8 /* PROPS */, [\"src\"])]), _cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n        class: \"LoginViewQrCodeName\"\n      }, \"手机APP下载\", -1 /* HOISTED */))]), _createVNode($setup[\"QrcodeVue\"], {\n        value: $setup.appDownloadUrl,\n        size: 120\n      }, null, 8 /* PROPS */, [\"value\"]), _createElementVNode(\"div\", _hoisted_23, \"使用其他软件扫码下载\" + _toDisplayString($setup.systemName) + \"APP\", 1 /* TEXT */)])];\n    }),\n    _: 1 /* STABLE */\n  }), _cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n    class: \"LoginViewOperationText\"\n  }, \"手机APP下载\", -1 /* HOISTED */))])])) : _createCommentVNode(\"v-if\", true), $setup.systemLoginContact ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, _toDisplayString($setup.systemLoginContact), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"重置密码\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"ResetPassword\"], {\n        onCallback: _cache[8] || (_cache[8] = function ($event) {\n          return $setup.show = !$setup.show;\n        })\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])], 4 /* STYLE */);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "class", "key", "_createElementBlock", "style", "_normalizeStyle", "backgroundImage", "$setup", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_image", "src", "systemLogo", "fit", "innerHTML", "systemName", "_hoisted_3", "_hoisted_4", "_hoisted_5", "type", "placeholder", "onClick", "_cache", "_ctx", "search", "apply", "arguments", "alt", "openLogin", "_createTextVNode", "_hoisted_6", "_component_el_form", "ref", "model", "form", "rules", "default", "_withCtx", "_component_el_form_item", "prop", "_hoisted_7", "_component_el_input", "modelValue", "account", "$event", "onBlur", "handleBlur", "clearable", "_", "_hoisted_8", "password", "loginVerifyShow", "whetherVerifyCode", "_createBlock", "verifyCode", "_component_el_button", "handleGetVerifyCode", "disabled", "countDownText", "_toDisplayString", "_createCommentVNode", "_hoisted_9", "_component_xyl_slide_verify", "onAgain", "onSuccess", "_hoisted_10", "_component_el_checkbox", "checked", "show", "submitForm", "LoginForm", "loading", "loginDisabled", "appDownloadUrl", "_hoisted_11", "_hoisted_12", "_component_el_popover", "placement", "width", "onShow", "refresh", "onHide", "hideQrcode", "reference", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "value", "loginQrcode", "size", "_hoisted_17", "loginQrcodeShow", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "systemLoginContact", "_hoisted_24", "_component_xyl_popup_window", "name", "onCallback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LoginView\\LoginViewCopy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LoginViewCopy\" :style=\"{ backgroundImage: `url(${backgroundImage})` }\">\r\n    <div class=\"login-header\">\r\n      <div class=\"login-logo-name\">\r\n        <el-image class=\"login-logo\" :src=\"systemLogo\" fit=\"contain\" />\r\n        <div class=\"login-name\" v-html=\"systemName\"></div>\r\n      </div>\r\n      <div class=\"login-header-right\">\r\n        <div class=\"search-box\">\r\n          <input type=\"text\" placeholder=\"请输入搜索内容\" />\r\n          <button class=\"search-btn\" @click=\"search\">\r\n            <img class=\"search-icon\" src=\"../img/search_icon.png\" alt=\"搜索\" />\r\n          </button>\r\n        </div>\r\n        <button class=\"mine-btn\" @click=\"openLogin\">\r\n          <img src=\"../img/mine_btn_bg.png\" alt=\"我的\" />\r\n          <span>\r\n            <img class=\"mine-icon\" src=\"../img/mine_icon.png\" alt=\"我的\" />\r\n            我的\r\n          </span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"LoginViewBox\">\r\n      <div class=\"LoginViewName\">欢迎登录</div>\r\n      <el-form ref=\"LoginForm\" :model=\"form\" :rules=\"rules\" class=\"LoginViewForm\">\r\n        <el-form-item prop=\"account\">\r\n          <div class=\"input-bg\">\r\n            <el-input v-model=\"form.account\" placeholder=\"账号/手机号\" @blur=\"handleBlur\" clearable />\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item prop=\"password\">\r\n          <div class=\"input-bg\">\r\n            <el-input type=\"password\" v-model=\"form.password\" placeholder=\"密码\" show-password clearable />\r\n          </div>\r\n        </el-form-item>\r\n        <el-form-item class=\"smsValidation\" v-if=\"loginVerifyShow && whetherVerifyCode\" prop=\"verifyCode\">\r\n          <el-input v-model=\"form.verifyCode\" placeholder=\"短信验证码\" clearable> </el-input>\r\n          <el-button type=\"primary\" @click=\"handleGetVerifyCode\" :disabled=\"countDownText != '获取验证码'\">\r\n            {{ countDownText }}</el-button>\r\n        </el-form-item>\r\n        <div class=\"LoginViewSlideVerify\" v-if=\"loginVerifyShow && !whetherVerifyCode\">\r\n          <xyl-slide-verify ref=\"slideVerify\" @again=\"onAgain\" @success=\"onSuccess\" :disabled=\"disabled\" />\r\n        </div>\r\n        <div class=\"LoginViewFormOperation\">\r\n          <el-checkbox v-model=\"checked\">记住用户名和密码</el-checkbox>\r\n          <div class=\"LoginViewFormOperationText\" @click=\"show = !show\">忘记密码？</div>\r\n        </div>\r\n        <el-button type=\"primary\" @click=\"submitForm(LoginForm)\" class=\"LoginViewFormButton login-btn-bg\"\r\n          :loading=\"loading\" :disabled=\"loginDisabled\">{{ loading ? '登录中' : '登录' }}</el-button>\r\n      </el-form>\r\n      <div class=\"LoginViewOperation\" v-if=\"appDownloadUrl\">\r\n        <div class=\"LoginViewOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\" @show=\"refresh\" @hide=\"hideQrcode\">\r\n            <div class=\"LoginViewQrCodeBox\">\r\n              <div class=\"LoginViewQrCodeNameBody\">\r\n                <div class=\"LoginViewQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewQrCodeName\">APP扫码登录</div>\r\n              </div>\r\n              <div class=\"LoginViewQrCodeRefreshBody\">\r\n                <qrcode-vue :value=\"loginQrcode\" :size=\"120\" />\r\n                <div class=\"LoginViewQrCodeRefresh\" v-show=\"loginQrcodeShow\">\r\n                  <el-button type=\"primary\" @click=\"refresh\">刷新</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"LoginViewQrCodeText\">请使用{{ systemName }}APP扫码登录</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewQrCode\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewOperationText\">APP扫码登录</div>\r\n        </div>\r\n        <div class=\"LoginViewOperationBox\">\r\n          <el-popover placement=\"top\" width=\"auto\">\r\n            <div class=\"LoginViewQrCodeBox\">\r\n              <div class=\"LoginViewQrCodeNameBody\">\r\n                <div class=\"LoginViewQrCodeLogo\">\r\n                  <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n                </div>\r\n                <div class=\"LoginViewQrCodeName\">手机APP下载</div>\r\n              </div>\r\n              <qrcode-vue :value=\"appDownloadUrl\" :size=\"120\" />\r\n              <div class=\"LoginViewQrCodeText\">使用其他软件扫码下载{{ systemName }}APP</div>\r\n            </div>\r\n            <template #reference>\r\n              <div class=\"LoginViewApp\"></div>\r\n            </template>\r\n          </el-popover>\r\n          <div class=\"LoginViewOperationText\">手机APP下载</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"LoginViewSystemTips\" v-if=\"systemLoginContact\">{{ systemLoginContact }}</div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"重置密码\">\r\n      <ResetPassword @callback=\"show = !show\"></ResetPassword>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LoginViewCopy' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport QrcodeVue from 'qrcode.vue'\r\nimport { systemLogo, systemName, appDownloadUrl, systemLoginContact, user } from 'common/js/system_var.js'\r\nimport { LoginView } from './LoginView.js'\r\nimport config from 'common/config'\r\nimport ResetPassword from './component/ResetPassword.vue'\r\nconst backgroundImage = ref(`${config.API_URL}/pageImg/open/homePage?areaId=${user.value.areaId}`)\r\nconst show = ref(false)\r\nconst { loginVerifyShow, whetherVerifyCode, loginDisabled, loading, checked, imgList, LoginForm, form, rules, countDownText, slideVerify, disabled, loginQrcode, loginQrcodeShow, handleBlur, handleGetVerifyCode, onAgain, onSuccess, globalData, submitForm, loginInfo, refresh, hideQrcode } = LoginView()\r\n\r\n\r\nonMounted(() => {\r\n  loginInfo()\r\n  globalData()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.LoginViewCopy {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: no-repeat;\r\n  background-size: 100% 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n\r\n  .login-header {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 24px 40px 0 40px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    z-index: 2;\r\n    flex-shrink: 0;\r\n\r\n    .login-logo-name {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .login-logo {\r\n        height: 75px;\r\n        width: 75px;\r\n      }\r\n\r\n      .login-name {\r\n        font-size: 37px;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        margin-left: 15px;\r\n        letter-spacing: 5px;\r\n      }\r\n    }\r\n\r\n\r\n    .login-header-right {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 16px;\r\n\r\n      .search-box {\r\n        display: flex;\r\n        align-items: center;\r\n        border-radius: 20px;\r\n        padding: 0 0 0 8px;\r\n        height: 36px;\r\n        border: 1px solid #FFFFFF;\r\n        // width: 350px;\r\n\r\n        input {\r\n          border: none;\r\n          outline: none;\r\n          height: 100%;\r\n          padding: 0 8px;\r\n          border-radius: 20px 0 0 20px;\r\n          background: rgb(0, 0, 0, 0);\r\n          width: calc(100% - 55px);\r\n          color: #fff;\r\n\r\n          &::placeholder {\r\n            color: #fff;\r\n            opacity: 1;\r\n          }\r\n        }\r\n\r\n        .search-btn {\r\n          background: url(\"../img/search_btn_bg.png\") no-repeat center/cover;\r\n          border: none;\r\n          width: 55px;\r\n          height: 36px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          cursor: pointer;\r\n\r\n          .search-icon {\r\n            width: 18px;\r\n            height: 18px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .mine-btn {\r\n        background: none;\r\n        border: none;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          height: 39px;\r\n        }\r\n\r\n        span {\r\n          position: absolute;\r\n          left: 0;\r\n          width: 100%;\r\n          text-align: center;\r\n          color: #fff;\r\n          font-size: 14px;\r\n          line-height: 39px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n\r\n      .mine-btn .mine-icon {\r\n        width: 20px;\r\n        height: 20px;\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewBox {\r\n    position: absolute;\r\n    left: 100px; // 距离左侧 100px\r\n    top: 55%;\r\n    transform: translateY(-50%);\r\n    width: 460px;\r\n    height: 640px;\r\n    background: url(\"../img/login_form_bg.png\") no-repeat center/cover;\r\n    background-size: 100% 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center; // 水平居中\r\n    justify-content: flex-start;\r\n    padding: 0 40px;\r\n\r\n    .LoginViewName {\r\n      width: 100%;\r\n      text-align: center;\r\n      font-size: 28px;\r\n      font-weight: bold;\r\n      color: #fff;\r\n      margin-top: 82px; // 距离顶部\r\n      margin-bottom: 26px; // 与表单间距\r\n      margin-left: 45px;\r\n      letter-spacing: 2px;\r\n      line-height: 1.2;\r\n    }\r\n\r\n    .LoginViewForm {\r\n      width: 360px;\r\n      margin: 0px 20px 0 58px;\r\n      padding-bottom: 20px;\r\n\r\n      input:-webkit-autofill {\r\n        transition: background-color 5000s ease-in-out 0s;\r\n      }\r\n\r\n      .input-bg {\r\n        width: 100%;\r\n        height: 44px;\r\n        background: url(\"../img/input_bg.png\") no-repeat center/cover; // 你的输入框背景图\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0 16px;\r\n        box-sizing: border-box;\r\n\r\n        .zy-el-input {\r\n          background: transparent !important;\r\n\r\n          .zy-el-input__wrapper {\r\n            background: transparent !important;\r\n            box-shadow: none !important;\r\n            border: none !important;\r\n          }\r\n\r\n          .zy-el-input__inner {\r\n            background: transparent !important;\r\n            color: #fff;\r\n            border: none;\r\n            font-size: 16px;\r\n\r\n            &::placeholder {\r\n              color: #fff;\r\n              opacity: 0.7;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-form-item {\r\n        margin-bottom: 20px;\r\n      }\r\n\r\n      .LoginViewFormButton.login-btn-bg {\r\n        background: url(\"../img/login_btn.png\") no-repeat center/cover !important; // 按钮背景图\r\n        border: none !important;\r\n        color: #fff !important;\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        width: 100%;\r\n        height: 44px;\r\n        box-shadow: none;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        transition: filter 0.2s;\r\n\r\n        // 鼠标悬浮时可加亮\r\n        &:hover,\r\n        &:focus {\r\n          filter: brightness(1.08);\r\n        }\r\n\r\n        // 禁用时灰度\r\n        &.is-disabled,\r\n        &[disabled] {\r\n          filter: grayscale(0.6);\r\n          opacity: 0.7;\r\n          cursor: not-allowed;\r\n        }\r\n      }\r\n\r\n      .is-loading {\r\n        border-radius: 60px;\r\n      }\r\n\r\n      .smsValidation {\r\n        .zy-el-form-item__content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n        }\r\n\r\n        .zy-el-input {\r\n          width: 56%;\r\n        }\r\n      }\r\n\r\n      .LoginViewSlideVerify {\r\n        margin-bottom: var(--zy-distance-five);\r\n        width: 100%;\r\n\r\n        .xyl-slide-verify-slider {\r\n          width: 100% !important;\r\n          border: 1px solid #7ea4ff !important;\r\n\r\n          .xyl-slide-verify-slider-mask .xyl-slide-verify-slider-mask-item {\r\n            background: #8ecbff;\r\n          }\r\n\r\n          .container-success .xyl-slide-verify-slider-mask .xyl-slide-verify-slider-mask-item {\r\n            background-color: var(--zy-el-color-success);\r\n          }\r\n\r\n          .xyl-slide-verify-slider-text {\r\n            color: #cee4ff !important;\r\n          }\r\n        }\r\n      }\r\n\r\n      .zy-el-checkbox,\r\n      .zy-el-checkbox__label {\r\n        color: #fff !important;\r\n      }\r\n\r\n      .LoginViewFormOperation {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: var(--zy-distance-three);\r\n\r\n        .zy-el-checkbox {\r\n          height: var(--zy-height-secondary);\r\n        }\r\n\r\n        .LoginViewFormOperationText {\r\n          cursor: pointer;\r\n          color: #51DCFF;\r\n          font-size: var(--zy-text-font-size);\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewOperation {\r\n      width: 100%;\r\n      padding-bottom: var(--zy-distance-two);\r\n      display: flex;\r\n      justify-content: space-between;\r\n      margin: 0px 45px 0 80px;\r\n\r\n      .LoginViewOperationBox {\r\n        margin: 0 var(--zy-distance-two);\r\n        cursor: pointer;\r\n\r\n        .LoginViewQrCode {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url(\"../img/login_qr_code.png\");\r\n          background-size: 100% 100%;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewApp {\r\n          width: 50px;\r\n          height: 50px;\r\n          background: url(\"../img/login_app.png\") no-repeat;\r\n          background-size: auto 100%;\r\n          background-position: center;\r\n          margin: auto;\r\n        }\r\n\r\n        .LoginViewOperationText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: var(--el-border-radius-small) 0;\r\n          text-align: center;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LoginViewForm+.LoginViewSystemTips {\r\n      padding-top: var(--zy-distance-one);\r\n    }\r\n\r\n    .LoginViewSystemTips {\r\n      color: #fff;\r\n      font-size: var(--zy-text-font-size);\r\n      text-align: center;\r\n      margin-left: 32px;\r\n    }\r\n  }\r\n}\r\n\r\n.LoginViewQrCodeBox {\r\n  width: 320px;\r\n  background-color: #fff;\r\n\r\n  canvas {\r\n    display: block;\r\n    margin: auto;\r\n  }\r\n\r\n  .LoginViewQrCodeNameBody {\r\n    padding: var(--zy-distance-three);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LoginViewQrCodeLogo {\r\n      width: 26px;\r\n      margin-right: 6px;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .LoginViewQrCodeName {\r\n      color: var(--zy-el-color-primary);\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .LoginViewQrCodeRefreshBody {\r\n    position: relative;\r\n\r\n    .LoginViewQrCodeRefresh {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      width: 120px;\r\n      height: 120px;\r\n      background-color: rgba(000, 000, 000, 0.6);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .zy-el-button {\r\n        --zy-el-button-size: var(--zy-height-secondary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .LoginViewQrCodeText {\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    padding: var(--zy-distance-three);\r\n    color: var(--zy-el-color-primary);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAWqCA,UAA4B;OAIlDC,UAA4B;OAERC,UAA0B;;EAfpDC,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAiB;iBAHlC;;EAOWA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAY;;EAetBA,KAAK,EAAC;AAAc;;EAIdA,KAAK,EAAC;AAAU;;EAKhBA,KAAK,EAAC;AAAU;;EAhC/BC,GAAA;EAyCaD,KAAK,EAAC;;;EAGNA,KAAK,EAAC;AAAwB;;EA5C3CC,GAAA;EAmDWD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAuB;;EAEzBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAqB;;EAK7BA,KAAK,EAAC;AAA4B;;EAEhCA,KAAK,EAAC;AAAwB;;EAIhCA,KAAK,EAAC;AAAqB;;EAQjCA,KAAK,EAAC;AAAuB;;EAEzBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAqB;;EAM7BA,KAAK,EAAC;AAAqB;;EArF9CC,GAAA;EA8FWD,KAAK,EAAC;;;;;;;;;;;;uBA7FfE,mBAAA,CAkGM;IAlGDF,KAAK,EAAC,eAAe;IAAEG,KAAK,EADnCC,eAAA;MAAAC,eAAA,SAC+DC,MAAA,CAAAD,eAAe;IAAA;MAC1EE,mBAAA,CAoBM,OApBNC,UAoBM,GAnBJD,mBAAA,CAGM,OAHNE,UAGM,GAFJC,YAAA,CAA+DC,mBAAA;IAArDX,KAAK,EAAC,YAAY;IAAEY,GAAG,EAAEN,MAAA,CAAAO,UAAU;IAAEC,GAAG,EAAC;oCACnDP,mBAAA,CAAkD;IAA7CP,KAAK,EAAC,YAAY;IAACe,SAAmB,EAAXT,MAAA,CAAAU;0BALxCC,UAAA,E,GAOMV,mBAAA,CAcM,OAdNW,UAcM,GAbJX,mBAAA,CAKM,OALNY,UAKM,G,4BAJJZ,mBAAA,CAA2C;IAApCa,IAAI,EAAC,MAAM;IAACC,WAAW,EAAC;+BAC/Bd,mBAAA,CAES;IAFDP,KAAK,EAAC,YAAY;IAAEsB,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,IAAA,CAAAC,MAAA,IAAAD,IAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAF,IAAA,EAAAG,SAAA,CAAM;IAAA;kCACvCpB,mBAAA,CAAiE;IAA5DP,KAAK,EAAC,aAAa;IAACY,GAA4B,EAA5Bf,UAA4B;IAAC+B,GAAG,EAAC;mCAG9DrB,mBAAA,CAMS;IANDP,KAAK,EAAC,UAAU;IAAEsB,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,IAAA,CAAAK,SAAA,IAAAL,IAAA,CAAAK,SAAA,CAAAH,KAAA,CAAAF,IAAA,EAAAG,SAAA,CAAS;IAAA;kCACxCpB,mBAAA,CAA6C;IAAxCK,GAA4B,EAA5Bd,UAA4B;IAAC8B,GAAG,EAAC;8BACtCrB,mBAAA,CAGO,eAFLA,mBAAA,CAA6D;IAAxDP,KAAK,EAAC,WAAW;IAACY,GAA0B,EAA1Bb,UAA0B;IAAC6B,GAAG,EAAC;MAjBlEE,gBAAA,CAiByE,MAE/D,E,4BAINvB,mBAAA,CAwEM,OAxENwB,UAwEM,G,4BAvEJxB,mBAAA,CAAqC;IAAhCP,KAAK,EAAC;EAAe,GAAC,MAAI,sBAC/BU,YAAA,CAyBUsB,kBAAA;IAzBDC,GAAG,EAAC,WAAW;IAAEC,KAAK,EAAE5B,MAAA,CAAA6B,IAAI;IAAGC,KAAK,EAAE9B,MAAA,CAAA8B,KAAK;IAAEpC,KAAK,EAAC;;IAzBlEqC,OAAA,EAAAC,QAAA,CA0BQ;MAAA,OAIe,CAJf5B,YAAA,CAIe6B,uBAAA;QAJDC,IAAI,EAAC;MAAS;QA1BpCH,OAAA,EAAAC,QAAA,CA2BU;UAAA,OAEM,CAFN/B,mBAAA,CAEM,OAFNkC,UAEM,GADJ/B,YAAA,CAAqFgC,mBAAA;YA5BjGC,UAAA,EA4B+BrC,MAAA,CAAA6B,IAAI,CAACS,OAAO;YA5B3C,uBAAArB,MAAA,QAAAA,MAAA,gBAAAsB,MAAA;cAAA,OA4B+BvC,MAAA,CAAA6B,IAAI,CAACS,OAAO,GAAAC,MAAA;YAAA;YAAExB,WAAW,EAAC,QAAQ;YAAEyB,MAAI,EAAExC,MAAA,CAAAyC,UAAU;YAAEC,SAAS,EAAT;;;QA5BrFC,CAAA;UA+BQvC,YAAA,CAIe6B,uBAAA;QAJDC,IAAI,EAAC;MAAU;QA/BrCH,OAAA,EAAAC,QAAA,CAgCU;UAAA,OAEM,CAFN/B,mBAAA,CAEM,OAFN2C,UAEM,GADJxC,YAAA,CAA6FgC,mBAAA;YAAnFtB,IAAI,EAAC,UAAU;YAjCrCuB,UAAA,EAiC+CrC,MAAA,CAAA6B,IAAI,CAACgB,QAAQ;YAjC5D,uBAAA5B,MAAA,QAAAA,MAAA,gBAAAsB,MAAA;cAAA,OAiC+CvC,MAAA,CAAA6B,IAAI,CAACgB,QAAQ,GAAAN,MAAA;YAAA;YAAExB,WAAW,EAAC,IAAI;YAAC,eAAa,EAAb,EAAa;YAAC2B,SAAS,EAAT;;;QAjC7FC,CAAA;UAoCkD3C,MAAA,CAAA8C,eAAe,IAAI9C,MAAA,CAAA+C,iBAAiB,I,cAA9EC,YAAA,CAIef,uBAAA;QAxCvBtC,GAAA;QAoCsBD,KAAK,EAAC,eAAe;QAA6CwC,IAAI,EAAC;;QApC7FH,OAAA,EAAAC,QAAA,CAqCU;UAAA,OAA8E,CAA9E5B,YAAA,CAA8EgC,mBAAA;YArCxFC,UAAA,EAqC6BrC,MAAA,CAAA6B,IAAI,CAACoB,UAAU;YArC5C,uBAAAhC,MAAA,QAAAA,MAAA,gBAAAsB,MAAA;cAAA,OAqC6BvC,MAAA,CAAA6B,IAAI,CAACoB,UAAU,GAAAV,MAAA;YAAA;YAAExB,WAAW,EAAC,OAAO;YAAC2B,SAAS,EAAT;mDACxDtC,YAAA,CACiC8C,oBAAA;YADtBpC,IAAI,EAAC,SAAS;YAAEE,OAAK,EAAEhB,MAAA,CAAAmD,mBAAmB;YAAGC,QAAQ,EAAEpD,MAAA,CAAAqD,aAAa;;YAtCzFtB,OAAA,EAAAC,QAAA,CAuCY;cAAA,OAAmB,CAvC/BR,gBAAA,CAAA8B,gBAAA,CAuCetD,MAAA,CAAAqD,aAAa,iB;;YAvC5BV,CAAA;;;QAAAA,CAAA;YAAAY,mBAAA,gBAyCgDvD,MAAA,CAAA8C,eAAe,KAAK9C,MAAA,CAAA+C,iBAAiB,I,cAA7EnD,mBAAA,CAEM,OAFN4D,UAEM,GADJpD,YAAA,CAAiGqD,2BAAA;QAA/E9B,GAAG,EAAC,aAAa;QAAE+B,OAAK,EAAE1D,MAAA,CAAA0D,OAAO;QAAGC,SAAO,EAAE3D,MAAA,CAAA2D,SAAS;QAAGP,QAAQ,EAAEpD,MAAA,CAAAoD;yEA1C/FG,mBAAA,gBA4CQtD,mBAAA,CAGM,OAHN2D,WAGM,GAFJxD,YAAA,CAAqDyD,sBAAA;QA7C/DxB,UAAA,EA6CgCrC,MAAA,CAAA8D,OAAO;QA7CvC,uBAAA7C,MAAA,QAAAA,MAAA,gBAAAsB,MAAA;UAAA,OA6CgCvC,MAAA,CAAA8D,OAAO,GAAAvB,MAAA;QAAA;;QA7CvCR,OAAA,EAAAC,QAAA,CA6CyC;UAAA,OAAQf,MAAA,SAAAA,MAAA,QA7CjDO,gBAAA,CA6CyC,UAAQ,E;;QA7CjDmB,CAAA;yCA8CU1C,mBAAA,CAAyE;QAApEP,KAAK,EAAC,4BAA4B;QAAEsB,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAsB,MAAA;UAAA,OAAEvC,MAAA,CAAA+D,IAAI,IAAI/D,MAAA,CAAA+D,IAAI;QAAA;SAAE,OAAK,E,GAErE3D,YAAA,CACuF8C,oBAAA;QAD5EpC,IAAI,EAAC,SAAS;QAAEE,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAsB,MAAA;UAAA,OAAEvC,MAAA,CAAAgE,UAAU,CAAChE,MAAA,CAAAiE,SAAS;QAAA;QAAGvE,KAAK,EAAC,kCAAkC;QAC9FwE,OAAO,EAAElE,MAAA,CAAAkE,OAAO;QAAGd,QAAQ,EAAEpD,MAAA,CAAAmE;;QAjDxCpC,OAAA,EAAAC,QAAA,CAiDuD;UAAA,OAA4B,CAjDnFR,gBAAA,CAAA8B,gBAAA,CAiD0DtD,MAAA,CAAAkE,OAAO,gC;;QAjDjEvB,CAAA;;;IAAAA,CAAA;yCAmD4C3C,MAAA,CAAAoE,cAAc,I,cAApDxE,mBAAA,CA0CM,OA1CNyE,WA0CM,GAzCJpE,mBAAA,CAsBM,OAtBNqE,WAsBM,GArBJlE,YAAA,CAmBamE,qBAAA;IAnBDC,SAAS,EAAC,KAAK;IAACC,KAAK,EAAC,MAAM;IAAEC,MAAI,EAAE1E,MAAA,CAAA2E,OAAO;IAAGC,MAAI,EAAE5E,MAAA,CAAA6E;;IAgBnDC,SAAS,EAAA9C,QAAA,CAClB;MAAA,OAAmCf,MAAA,SAAAA,MAAA,QAAnChB,mBAAA,CAAmC;QAA9BP,KAAK,EAAC;MAAiB,2B;;IAtE1CqC,OAAA,EAAAC,QAAA,CAsDY;MAAA,OAcM,CAdN/B,mBAAA,CAcM,OAdN8E,WAcM,GAbJ9E,mBAAA,CAKM,OALN+E,WAKM,GAJJ/E,mBAAA,CAEM,OAFNgF,WAEM,GADJ7E,YAAA,CAA0CC,mBAAA;QAA/BC,GAAG,EAAEN,MAAA,CAAAO,UAAU;QAAEC,GAAG,EAAC;sEAElCP,mBAAA,CAA8C;QAAzCP,KAAK,EAAC;MAAqB,GAAC,SAAO,qB,GAE1CO,mBAAA,CAKM,OALNiF,WAKM,GAJJ9E,YAAA,CAA+CJ,MAAA;QAAlCmF,KAAK,EAAEnF,MAAA,CAAAoF,WAAW;QAAGC,IAAI,EAAE;0DACxCpF,mBAAA,CAEM,OAFNqF,WAEM,GADJlF,YAAA,CAAyD8C,oBAAA;QAA9CpC,IAAI,EAAC,SAAS;QAAEE,OAAK,EAAEhB,MAAA,CAAA2E;;QAhEpD5C,OAAA,EAAAC,QAAA,CAgE6D;UAAA,OAAEf,MAAA,SAAAA,MAAA,QAhE/DO,gBAAA,CAgE6D,IAAE,E;;QAhE/DmB,CAAA;wEA+D4D3C,MAAA,CAAAuF,eAAe,E,KAI7DtF,mBAAA,CAAiE,OAAjEuF,WAAiE,EAAhC,KAAG,GAAAlC,gBAAA,CAAGtD,MAAA,CAAAU,UAAU,IAAG,SAAO,gB;;IAnEzEiC,CAAA;uEAyEU1C,mBAAA,CAAiD;IAA5CP,KAAK,EAAC;EAAwB,GAAC,SAAO,qB,GAE7CO,mBAAA,CAiBM,OAjBNwF,WAiBM,GAhBJrF,YAAA,CAcamE,qBAAA;IAdDC,SAAS,EAAC,KAAK;IAACC,KAAK,EAAC;;IAWrBK,SAAS,EAAA9C,QAAA,CAClB;MAAA,OAAgCf,MAAA,SAAAA,MAAA,QAAhChB,mBAAA,CAAgC;QAA3BP,KAAK,EAAC;MAAc,2B;;IAxFvCqC,OAAA,EAAAC,QAAA,CA6EY;MAAA,OASM,CATN/B,mBAAA,CASM,OATNyF,WASM,GARJzF,mBAAA,CAKM,OALN0F,WAKM,GAJJ1F,mBAAA,CAEM,OAFN2F,WAEM,GADJxF,YAAA,CAA0CC,mBAAA;QAA/BC,GAAG,EAAEN,MAAA,CAAAO,UAAU;QAAEC,GAAG,EAAC;sEAElCP,mBAAA,CAA8C;QAAzCP,KAAK,EAAC;MAAqB,GAAC,SAAO,qB,GAE1CU,YAAA,CAAkDJ,MAAA;QAArCmF,KAAK,EAAEnF,MAAA,CAAAoE,cAAc;QAAGiB,IAAI,EAAE;0CAC3CpF,mBAAA,CAAoE,OAApE4F,WAAoE,EAAnC,YAAU,GAAAvC,gBAAA,CAAGtD,MAAA,CAAAU,UAAU,IAAG,KAAG,gB;;IArF5EiC,CAAA;kCA2FU1C,mBAAA,CAAiD;IAA5CP,KAAK,EAAC;EAAwB,GAAC,SAAO,qB,OA3FrD6D,mBAAA,gBA8F6CvD,MAAA,CAAA8F,kBAAkB,I,cAAzDlG,mBAAA,CAAyF,OAAzFmG,WAAyF,EAAAzC,gBAAA,CAA3BtD,MAAA,CAAA8F,kBAAkB,oBA9FtFvC,mBAAA,e,GAgGInD,YAAA,CAEmB4F,2BAAA;IAlGvB3D,UAAA,EAgG+BrC,MAAA,CAAA+D,IAAI;IAhGnC,uBAAA9C,MAAA,QAAAA,MAAA,gBAAAsB,MAAA;MAAA,OAgG+BvC,MAAA,CAAA+D,IAAI,GAAAxB,MAAA;IAAA;IAAE0D,IAAI,EAAC;;IAhG1ClE,OAAA,EAAAC,QAAA,CAiGM;MAAA,OAAwD,CAAxD5B,YAAA,CAAwDJ,MAAA;QAAxCkG,UAAQ,EAAAjF,MAAA,QAAAA,MAAA,gBAAAsB,MAAA;UAAA,OAAEvC,MAAA,CAAA+D,IAAI,IAAI/D,MAAA,CAAA+D,IAAI;QAAA;;;IAjG5CpB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}