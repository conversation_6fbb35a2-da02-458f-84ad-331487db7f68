{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, withCtx as _withCtx, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"AiReportGeneraList\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = [\"innerHTML\"];\nvar _hoisted_4 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"]), _createVNode(_component_el_select, {\n        modelValue: $setup.reportType,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.reportType = $event;\n        }),\n        onChange: $setup.queryChange,\n        placeholder: \"请选择报告类型\",\n        clearable: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.reportTypeData, function (item) {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.id,\n              label: item.label,\n              value: item.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"报告类型\",\n        \"min-width\": \"160\",\n        prop: \"reportTypeName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"报告内容\",\n        \"min-width\": \"280\",\n        \"class-name\": \"AiReportGeneraContent\"\n      }, {\n        default: _withCtx(function (scope) {\n          var _scope$row$content;\n          return [_createElementVNode(\"div\", {\n            innerHTML: (_scope$row$content = scope.row.content) === null || _scope$row$content === void 0 ? void 0 : _scope$row$content.replace(/<[^>]*>/g, '')\n          }, null, 8 /* PROPS */, _hoisted_3)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"创建人\",\n        \"min-width\": \"120\",\n        prop: \"createUserName\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"创建时间\",\n        \"min-width\": \"180\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString($setup.format(scope.row.createDate)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_xyl_global_table_button, {\n        data: $setup.tableButtonList,\n        onButtonClick: $setup.handleCommand\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\"])]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: $setup.id ? '编辑报告' : '新增报告'\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"SubmitAiReportGenera\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.isShow,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n      return $setup.isShow = $event;\n    }),\n    name: \"报告详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"AiReportGeneraDetails\"], {\n        id: $setup.id\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_component_el_select", "reportType", "onChange", "query<PERSON>hange", "default", "_Fragment", "_renderList", "reportTypeData", "item", "_createBlock", "_component_el_option", "key", "id", "label", "value", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "ref", "data", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "_component_el_table_column", "type", "width", "fixed", "prop", "scope", "_scope$row$content", "innerHTML", "row", "content", "replace", "_hoisted_3", "_createTextVNode", "_toDisplayString", "format", "createDate", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "handleCommand", "_hoisted_4", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "show", "name", "onCallback", "callback", "isShow"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiReportGenera\\AiReportGeneraList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"AiReportGeneraList\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n        <el-select v-model=\"reportType\" @change=\"queryChange\" placeholder=\"请选择报告类型\" clearable>\r\n          <el-option v-for=\"item in reportTypeData\" :key=\"item.id\" :label=\"item.label\" :value=\"item.id\" />\r\n        </el-select>\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <el-table-column label=\"报告类型\" min-width=\"160\" prop=\"reportTypeName\" />\r\n        <el-table-column label=\"报告内容\" min-width=\"280\" class-name=\"AiReportGeneraContent\">\r\n          <template #default=\"scope\">\r\n            <div v-html=\"scope.row.content?.replace(/<[^>]*>/g, '')\"></div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建人\" min-width=\"120\" prop=\"createUserName\" />\r\n        <el-table-column label=\"创建时间\" min-width=\"180\">\r\n          <template #default=\"scope\">{{ format(scope.row.createDate) }}</template>\r\n        </el-table-column>\r\n        <xyl-global-table-button :data=\"tableButtonList\" @buttonClick=\"handleCommand\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" :name=\"id ? '编辑报告' : '新增报告'\">\r\n      <SubmitAiReportGenera :id=\"id\" @callback=\"callback\"></SubmitAiReportGenera>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"isShow\" name=\"报告详情\">\r\n      <AiReportGeneraDetails :id=\"id\"></AiReportGeneraDetails>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'AiReportGeneraList' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport SubmitAiReportGenera from './component/SubmitAiReportGenera'\r\nimport AiReportGeneraDetails from './component/AiReportGeneraDetails'\r\nconst buttonList = [\r\n  // { id: 'new', name: '新增', type: 'primary', has: 'new' },\r\n  { id: 'del', name: '删除', type: 'primary', has: 'del' }\r\n]\r\nconst tableButtonList = [\r\n  { id: 'edit', name: '编辑', width: 80, has: 'edit' },\r\n  { id: 'view', name: '查看', width: 80, has: '' }\r\n]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst isShow = ref(false)\r\nconst reportType = ref('')\r\nconst reportTypeData = ref([])\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  tableDataArray,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset,\r\n  tableQuery\r\n} = GlobalTable({ tableApi: 'aigptReportRecordList', delApi: 'aigptReportRecordDel' })\r\n\r\nonActivated(() => {\r\n  handleQuery()\r\n  aigptReportRecordTypeList()\r\n})\r\n\r\nconst aigptReportRecordTypeList = async () => {\r\n  const { data } = await api.aigptReportRecordTypeList({ sceneCode: 'ai-general-report-main' })\r\n  reportTypeData.value = data\r\n}\r\n\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'del':\r\n      handleDel('敏感词')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    case 'view':\r\n      handleView(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst queryChange = () => {\r\n  tableQuery.value = { query: { reportType: reportType.value || null } }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  reportType.value = ''\r\n  tableQuery.value = { query: { reportType: reportType.value || null } }\r\n  handleQuery()\r\n}\r\nconst handleNew = () => {\r\n  id.value = ''\r\n  show.value = true\r\n}\r\nconst handleEdit = (item) => {\r\n  id.value = item.id\r\n  show.value = true\r\n}\r\nconst handleView = (item) => {\r\n  id.value = item.id\r\n  isShow.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n// 启用/禁用\r\nconst handleBatch = (type, text) => {\r\n  if (tableDataArray.value.length) {\r\n    ElMessageBox.confirm(`此操作将${text}当前选中的敏感词, 是否继续?`, '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }).then(() => {\r\n      AiReportGeneraListBatch({ batchType: type, AiReportGeneraListIds: tableDataArray.value.map(v => v.id) }, `${text}成功`)\r\n    }).catch(() => { ElMessage({ type: 'info', message: `已取消${text}` }) })\r\n  } else {\r\n    ElMessage({ type: 'warning', message: '请至少选择一条数据' })\r\n  }\r\n}\r\nconst AiReportGeneraListBatch = async (params, text) => {\r\n  const { code } = await api.AiReportGeneraListBatch(params)\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: text })\r\n    tableRefReset()\r\n    handleQuery()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.AiReportGeneraList {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .xyl-search-button {\r\n    .xyl-button {\r\n      width: calc(100% - 660px);\r\n    }\r\n\r\n    .xyl-search {\r\n      width: 660px;\r\n\r\n      .zy-el-select {\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n\r\n    .AiReportGeneraContent {\r\n      .cell {\r\n        width: 100%;\r\n\r\n        div {\r\n          width: 100%;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAUxBA,KAAK,EAAC;AAAa;iBAX5B;;EA4BSA,KAAK,EAAC;AAAkB;;;;;;;;;;;uBA3B/BC,mBAAA,CAsCM,OAtCNC,UAsCM,GArCJC,YAAA,CAQoBC,4BAAA;IARAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IAAGC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IAAGC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC/FC,UAAU,EAAEN,MAAA,CAAAM;;IACFC,MAAM,EAAAC,QAAA,CACf;MAAA,OAAwF,CAAxFX,YAAA,CAAwFY,mBAAA;QALhGC,UAAA,EAK2BV,MAAA,CAAAW,OAAO;QALlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAK2Bb,MAAA,CAAAW,OAAO,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAL/DC,SAAA,CAKuEhB,MAAA,CAAAC,WAAW;QAAEgB,SAAS,EAAT;0DAC5EpB,YAAA,CAEYqB,oBAAA;QARpBR,UAAA,EAM4BV,MAAA,CAAAmB,UAAU;QANtC,uBAAAP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAM4Bb,MAAA,CAAAmB,UAAU,GAAAN,MAAA;QAAA;QAAGO,QAAM,EAAEpB,MAAA,CAAAqB,WAAW;QAAEP,WAAW,EAAC,SAAS;QAACG,SAAS,EAAT;;QANpFK,OAAA,EAAAd,QAAA,CAOqB;UAAA,OAA8B,E,kBAAzCb,mBAAA,CAAgG4B,SAAA,QAP1GC,WAAA,CAOoCxB,MAAA,CAAAyB,cAAc,EAPlD,UAO4BC,IAAI;iCAAtBC,YAAA,CAAgGC,oBAAA;cAArDC,GAAG,EAAEH,IAAI,CAACI,EAAE;cAAGC,KAAK,EAAEL,IAAI,CAACK,KAAK;cAAGC,KAAK,EAAEN,IAAI,CAACI;;;;QAPpGG,CAAA;;;IAAAA,CAAA;uCAWIC,mBAAA,CAgBM,OAhBNC,UAgBM,GAfJtC,YAAA,CAcWuC,mBAAA;IAdDC,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAAEC,IAAI,EAAEtC,MAAA,CAAAuC,SAAS;IAAGC,QAAM,EAAExC,MAAA,CAAAyC,iBAAiB;IAC/EC,WAAU,EAAE1C,MAAA,CAAAyC;;IAbrBnB,OAAA,EAAAd,QAAA,CAcQ;MAAA,OAAuE,CAAvEX,YAAA,CAAuE8C,0BAAA;QAAtDC,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/DjD,YAAA,CAAsE8C,0BAAA;QAArDZ,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAACgB,IAAI,EAAC;UACnDlD,YAAA,CAIkB8C,0BAAA;QAJDZ,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAAC,YAAU,EAAC;;QAC5CT,OAAO,EAAAd,QAAA,CAChB,UAA+DwC,KADxC;UAAA,IAAAC,kBAAA;UAAA,QACvBf,mBAAA,CAA+D;YAA1DgB,SAAmD,GAAAD,kBAAA,GAA3CD,KAAK,CAACG,GAAG,CAACC,OAAO,cAAAH,kBAAA,uBAAjBA,kBAAA,CAAmBI,OAAO;kCAlBnDC,UAAA,E;;QAAArB,CAAA;UAqBQpC,YAAA,CAAqE8C,0BAAA;QAApDZ,KAAK,EAAC,KAAK;QAAC,WAAS,EAAC,KAAK;QAACgB,IAAI,EAAC;UAClDlD,YAAA,CAEkB8C,0BAAA;QAFDZ,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3BT,OAAO,EAAAd,QAAA,CAAS,UAAkCwC,KAApC;UAAA,QAvBnCO,gBAAA,CAAAC,gBAAA,CAuBwCxD,MAAA,CAAAyD,MAAM,CAACT,KAAK,CAACG,GAAG,CAACO,UAAU,kB;;QAvBnEzB,CAAA;UAyBQpC,YAAA,CAAwG8D,kCAAA;QAA9ErB,IAAI,EAAEtC,MAAA,CAAA4D,eAAe;QAAGC,aAAW,EAAE7D,MAAA,CAAA8D;;;IAzBvE7B,CAAA;4DA4BIC,mBAAA,CAIM,OAJN6B,UAIM,GAHJlE,YAAA,CAE+BmE,wBAAA;IAFRC,WAAW,EAAEjE,MAAA,CAAAkE,MAAM;IA7BhD,wBAAAtD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA6B0Cb,MAAA,CAAAkE,MAAM,GAAArD,MAAA;IAAA;IAAU,WAAS,EAAEb,MAAA,CAAAmE,QAAQ;IA7B7E,qBAAAvD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA6BqEb,MAAA,CAAAmE,QAAQ,GAAAtD,MAAA;IAAA;IAAG,YAAU,EAAEb,MAAA,CAAAoE,SAAS;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAEtE,MAAA,CAAAC,WAAW;IAAGsE,eAAc,EAAEvE,MAAA,CAAAC,WAAW;IACvGuE,KAAK,EAAExE,MAAA,CAAAyE,MAAM;IAAEC,UAAU,EAAV;qHAEpB7E,YAAA,CAEmB8E,2BAAA;IAnCvBjE,UAAA,EAiC+BV,MAAA,CAAA4E,IAAI;IAjCnC,uBAAAhE,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAiC+Bb,MAAA,CAAA4E,IAAI,GAAA/D,MAAA;IAAA;IAAGgE,IAAI,EAAE7E,MAAA,CAAA8B,EAAE;;IAjC9CR,OAAA,EAAAd,QAAA,CAkCM;MAAA,OAA2E,CAA3EX,YAAA,CAA2EG,MAAA;QAApD8B,EAAE,EAAE9B,MAAA,CAAA8B,EAAE;QAAGgD,UAAQ,EAAE9E,MAAA,CAAA+E;;;IAlChD9C,CAAA;6CAoCIpC,YAAA,CAEmB8E,2BAAA;IAtCvBjE,UAAA,EAoC+BV,MAAA,CAAAgF,MAAM;IApCrC,uBAAApE,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoC+Bb,MAAA,CAAAgF,MAAM,GAAAnE,MAAA;IAAA;IAAEgE,IAAI,EAAC;;IApC5CvD,OAAA,EAAAd,QAAA,CAqCM;MAAA,OAAwD,CAAxDX,YAAA,CAAwDG,MAAA;QAAhC8B,EAAE,EAAE9B,MAAA,CAAA8B;MAAE,gC;;IArCpCG,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}