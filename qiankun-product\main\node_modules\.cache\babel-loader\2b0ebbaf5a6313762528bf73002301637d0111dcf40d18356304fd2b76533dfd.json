{"ast": null, "code": "function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { ref, watch, onMounted, computed, nextTick, provide, onUnmounted } from 'vue';\nimport api from '@/api';\nimport { useStore } from 'vuex';\nimport config from 'common/config';\nimport { qiankunActions, loadFilterApp } from '@/qiankun';\nimport { handleCompareVersion } from 'common/js/CheckVersion';\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod';\nimport { get_font_family, change_font_family } from 'common/js/utils';\nimport elementResizeDetectorMaker from 'element-resize-detector';\nimport unauthorized from 'common/components/unauthorized';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { openConfig } from 'common/js/system_var.js';\nexport var refreshIcon = `<svg t=\"1743990581190\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"52145\" width=\"30\" height=\"30\"><path d=\"M512 128a384 384 0 1 1 0 768A384 384 0 0 1 512 128z m0 48.192C335.296 176.192 174.08 335.36 174.08 512c0 176.704 161.216 336.512 337.92 336.512S849.152 688.704 849.152 512 688.704 176.192 512 176.192z\" fill=\"#ffffff\" p-id=\"52146\"></path><path d=\"M244.352 506.688h73.6c0-123.2 86.016-220.032 196.48-215.68 36.8 0 69.568 13.248 102.272 30.848l-36.8 39.68c-20.48-8.96-45.056-17.664-69.568-17.664-77.76 0-143.232 70.4-143.232 162.816h73.6l-98.176 110.08-98.176-110.08zM705.088 515.84c0 117.248-86.848 217.28-194.368 217.28-37.248 0-70.4-17.344-103.424-34.752l37.184-39.04c20.736 8.704 45.568 17.344 70.4 17.344 78.592 0 144.768-69.504 144.768-160.768H581.056l99.328-108.608 99.2 108.544h-74.496z\" fill=\"#ffffff\" p-id=\"52147\"></path></svg>`;\nvar systemPlatform = computed(function () {\n  var _openConfig$value;\n  return ((_openConfig$value = openConfig.value) === null || _openConfig$value === void 0 ? void 0 : _openConfig$value.systemPlatform) || '';\n});\nexport var LayoutViewUnitedFront = function LayoutViewUnitedFront(route, router) {\n  var store = useStore();\n  var menuIcon = `${config.API_URL}/pageImg/open/menuIcon`;\n  var erd = elementResizeDetectorMaker();\n  var left = ref(0);\n  var width = ref('');\n  var LayoutViewBox = ref(null);\n  var LayoutViewInfo = ref(null);\n  var editPassWordShow = ref(false);\n  var verifyEditPassWord = ref('');\n  var verifyEditPassWordShow = ref(false);\n  var MicroApp = ref([]);\n  var MicroAppObj = ref({});\n  // 用户信息\n  var user = computed(function () {\n    return store.getters.getUserFn;\n  });\n  // 地区信息\n  var area = computed(function () {\n    return store.getters.getAreaFn;\n  });\n  // 角色信息\n  var role = computed(function () {\n    return store.getters.getRoleFn;\n  });\n  var openConfig = computed(function () {\n    return store.getters.getReadOpenConfig;\n  });\n  var helpShow = ref(false);\n  var suggestPopShow = ref(false);\n  var hasDuplicates = function hasDuplicates(arr1, arr2) {\n    var set = new Set(arr1);\n    return arr2.some(function (item) {\n      return set.has(item);\n    });\n  };\n  onMounted(function () {\n    var specialRoleKeys = JSON.parse(sessionStorage.getItem('user')).specialRoleKeys || [];\n    var canSee = systemPlatform.value === 'CPPCC' ? ['proposal_committee', 'suggestion_office_user', 'cppcc_member'] : ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member'];\n    suggestPopShow.value = hasDuplicates(specialRoleKeys, canSee);\n    console.log(\"🚀 ~ onMounted ~  suggestPopShow.value :\", suggestPopShow.value);\n    handleRegionSelect();\n    handleOrganizationSelect();\n    getOrganizationList();\n    nextTick(function () {\n      erd.listenTo(LayoutViewBox.value, function (element) {\n        left.value = element.offsetWidth;\n      });\n      erd.listenTo(LayoutViewInfo.value, function (element) {\n        width.value = `width: calc(100% - ${element.offsetWidth + 16}px);`;\n      });\n    });\n  });\n  var handleRegionSelect = function handleRegionSelect() {\n    var _user$value, _user$value2;\n    var oldRegionInfo = sessionStorage.getItem('oldRegionInfo') || '';\n    var isRegionSelect = sessionStorage.getItem('isRegionSelect') || '';\n    if (((_user$value = user.value) === null || _user$value === void 0 ? void 0 : _user$value.accountId) !== '1' && ((_user$value2 = user.value) === null || _user$value2 === void 0 ? void 0 : _user$value2.areaTotal) > 1 && oldRegionInfo && !isRegionSelect) {\n      isRegionSelectShow.value = true;\n    }\n  };\n  var handleCommand = function handleCommand(type) {\n    if (type === 'task') {\n      store.commit('setGlobalCentralControlObj', {\n        show: true\n      });\n    } else if (type === 'refresh') {\n      // window.location.reload(true)\n      // window.location.reload(window.location.href)\n      window.location.href = `${config.mainPath}?v=${new Date().getTime()}`;\n    } else if (type === 'locale') {\n      if (get_font_family() === 'cn') return change_font_family('tw');\n      if (get_font_family() === 'tw') return change_font_family('cn');\n    } else if (type === 'help') {\n      helpShow.value = true;\n    } else if (type === 'edit_password') {\n      verifyEditPassWord.value = '';\n      editPassWordShow.value = true;\n    } else if (type === 'exit') {\n      handleExit();\n    } else {\n      ElMessage({\n        type: 'info',\n        message: '正在开发中！'\n      });\n    }\n  };\n  var editPassWordCallback = function editPassWordCallback(type) {\n    if (type) {\n      loginOut('请使用新密码重新登录！');\n    }\n    editPassWordShow.value = false;\n  };\n  var handleExit = function handleExit() {\n    ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    }).then(function () {\n      loginOut('已安全退出！');\n    }).catch(function () {\n      ElMessage({\n        type: 'info',\n        message: '已取消退出'\n      });\n    });\n  };\n  var loginOut = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(text) {\n      var _yield$api$loginOut, code, goal_login_router_path, goal_login_router_query;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.next = 2;\n            return api.loginOut();\n          case 2:\n            _yield$api$loginOut = _context.sent;\n            code = _yield$api$loginOut.code;\n            if (code === 200) {\n              sessionStorage.clear();\n              goal_login_router_path = localStorage.getItem('goal_login_router_path');\n              if (goal_login_router_path) {\n                goal_login_router_query = localStorage.getItem('goal_login_router_query') || '';\n                router.push({\n                  path: goal_login_router_path,\n                  query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\n                });\n              } else {\n                router.push({\n                  path: '/LoginView'\n                });\n              }\n              store.commit('setState');\n              globalReadOpenConfig();\n              // store.state.socket.disconnect()\n              // store.state.socket = null\n              ElMessage({\n                message: text,\n                showClose: true,\n                type: 'success'\n              });\n            }\n          case 5:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function loginOut(_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n\n  // 地区id\n  var regionId = ref('');\n  var regionName = ref('');\n  var isRegionSelectShow = ref(false);\n  var regionSelect = function regionSelect(item) {\n    var _user$value3;\n    regionName.value = item.name;\n    isRegionSelectShow.value = false;\n    sessionStorage.setItem('AreaRow', JSON.stringify(item));\n    if (((_user$value3 = user.value) === null || _user$value3 === void 0 ? void 0 : _user$value3.areaId) === item.id) return;\n    verifyLoginUser(item);\n  };\n  var verifyLoginUser = /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(item) {\n      var _yield$api$verifyLogi, code, _user$value4;\n      return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.next = 2;\n            return api.verifyLoginUser({}, item.id);\n          case 2:\n            _yield$api$verifyLogi = _context2.sent;\n            code = _yield$api$verifyLogi.code;\n            if (code === 200) {\n              tabMenu.value = '';\n              sessionStorage.setItem('AreaId', item.id);\n              globalReadOpenConfig();\n              store.dispatch('loginUser', 'login');\n              store.commit('setBoxMessageRefresh', true);\n              store.commit('setPersonalDoRefresh', true);\n            } else {\n              regionId.value = (_user$value4 = user.value) === null || _user$value4 === void 0 ? void 0 : _user$value4.areaId;\n              unauthorized({\n                name: item.name\n              });\n            }\n          case 5:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2);\n    }));\n    return function verifyLoginUser(_x2) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  watch(function () {\n    return user.value;\n  }, function () {\n    var _user$value5, _user$value6;\n    regionId.value = (_user$value5 = user.value) === null || _user$value5 === void 0 ? void 0 : _user$value5.areaId;\n    if ((_user$value6 = user.value) !== null && _user$value6 !== void 0 && _user$value6.accountId) {\n      var _user$value7;\n      var verify = sessionStorage.getItem('verify');\n      if (verify && !Number(verify) && ((_user$value7 = user.value) === null || _user$value7 === void 0 ? void 0 : _user$value7.accountId) !== '1') {\n        var _openConfig$value2;\n        if (((_openConfig$value2 = openConfig.value) === null || _openConfig$value2 === void 0 ? void 0 : _openConfig$value2.forbidWeakPassword) === 'true') {\n          nextTick(function () {\n            verifyEditPassWord.value = 'yes';\n            verifyEditPassWordShow.value = true;\n          });\n        } else {\n          nextTick(function () {\n            verifyEditPassWord.value = 'no';\n            editPassWordShow.value = true;\n          });\n        }\n      }\n    }\n  }, {\n    immediate: true\n  });\n  // 菜单过滤\n  var _filterMenu = function filterMenu(menuList) {\n    var newMenuList = [];\n    for (var i = 0, len = menuList.length; i < len; i++) {\n      newMenuList.push({\n        id: menuList[i].menuId,\n        name: menuList[i].name,\n        routePath: menuList[i].routePath,\n        menuFunction: menuList[i].menuFunction,\n        menuRouteType: menuList[i].menuRouteType,\n        icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,\n        has: menuList[i].permissions,\n        children: _filterMenu(menuList[i].children || [])\n      });\n    }\n    return newMenuList;\n  };\n  // 顶部菜单id\n  var tabMenu = ref('');\n  // 顶部菜单data\n  var tabMenuData = computed(function () {\n    return _filterMenu(store.getters.getMenuFn || []);\n  });\n  // 是否显示左侧菜单\n  var isView = ref(false);\n  // 工作台跳转具体应用\n  var isChildView = ref(false);\n  // 具体打开页面id\n  var isOpen = ref(false);\n  // 具体打开页面id\n  var openPageId = ref('');\n  var openPageObj = ref({});\n  // 打开页面工作台应用id\n  var openPageChildId = ref('');\n  // 工作台对象\n  var WorkBenchObj = ref({});\n  // 工作台子应用data\n  var WorkBenchList = ref([]);\n  // 具体工作台子应用对象\n  var childData = ref({});\n  watch(function () {\n    return tabMenuData.value;\n  }, function (val) {\n    isView.value = false;\n    isChildView.value = false;\n    WorkBenchObj.value = {};\n    WorkBenchList.value = [];\n    childData.value = {};\n    if (tabMenuData.value.length) {\n      var query = JSON.parse(sessionStorage.getItem('query')) || {};\n      if (query.openPageValue) {\n        openPage({\n          key: query.openPageKey || 'id',\n          value: query.openPageValue\n        });\n      } else {\n        nextTick(function () {\n          var _tabMenuData$value$;\n          tabMenu.value = (_tabMenuData$value$ = tabMenuData.value[0]) === null || _tabMenuData$value$ === void 0 ? void 0 : _tabMenuData$value$.id;\n          handleClick();\n        });\n      }\n    }\n  }, {\n    immediate: true\n  });\n  // 顶部菜单切换事件\n  var handleClick = function handleClick() {\n    if (process.env.NODE_ENV !== 'development') {\n      var detection_version = openConfig.value.DetectionVersion || '';\n      if (detection_version !== 'true') handleCompareVersion();\n    }\n    menuId.value = '';\n    menuData.value = [];\n    tabData.value = [];\n    isTabData.value = [];\n    keepAliveRoute.value = [];\n    nextTick(function () {\n      var _loop = function _loop() {\n          var item = tabMenuData.value[i];\n          if (tabMenu.value === item.id) {\n            sessionStorage.setItem('has', JSON.stringify(item));\n            if (item.routePath === '/WorkBench') {\n              // 切换到工作台\n              isView.value = false;\n              router.push({\n                path: item.routePath,\n                query: routePathData(item.routePath)\n              });\n              WorkBenchObj.value = item;\n              WorkBenchList.value = item.children;\n              nextTick(function () {\n                if (openPageChildId.value) {\n                  if (openPageChildId.value === openPageId.value) {\n                    openPageId.value = '';\n                    openPageObj.value = {};\n                  }\n                  for (var r = 0, length = item.children.length; r < length; r++) {\n                    if (item.children[r].id === openPageChildId.value) {\n                      leftMenuData(item.children[r]);\n                    }\n                  }\n                }\n              });\n            } else {\n              if (item.routePath.includes('/GlobalHome')) {\n                if (openPageId.value) {\n                  WorkBenchObj.value = item;\n                  leftMenuData(item, false);\n                } else {\n                  isView.value = false;\n                  router.push({\n                    path: item.routePath,\n                    query: routePathData(item.routePath)\n                  });\n                }\n                return {\n                  v: void 0\n                };\n              }\n              // 不是工作台页面判断是否有子级菜单\n              if (item.children && item.children.length) {\n                // 有子级菜单按照左侧菜单显示\n                leftMenuData(item, true);\n              } else {\n                if (['3', '4'].includes(item.menuRouteType.value)) {\n                  isView.value = false;\n                  isChildView.value = false;\n                  router.push({\n                    path: item.routePath,\n                    query: _objectSpread(_objectSpread({}, routePathData(item.routePath)), {}, {\n                      menuRouteType: item.menuRouteType.value\n                    })\n                  });\n                } else {\n                  // const menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath\n                  // if (routePath(menuUrl) === '/') {\n                  //   isView.value = false\n                  //   const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath) }\n                  //   router.push({ path: item.routePath, query: query })\n                  //   const mainAppName = [mainRoutePath(item.routePath)]\n                  //   keepAliveRoute.value = mainAppName\n                  // } else {\n                  leftMenuData(item, true);\n                  // }\n                }\n              }\n            }\n          }\n        },\n        _ret;\n      for (var i = 0, len = tabMenuData.value.length; i < len; i++) {\n        _ret = _loop();\n        if (_ret) return _ret.v;\n      }\n    });\n  };\n  var WorkBenchMenu = function WorkBenchMenu(tabMenuId, tabMenuChildren) {\n    tabMenu.value = tabMenuId;\n    menuId.value = '';\n    menuData.value = [];\n    tabData.value = [];\n    isTabData.value = [];\n    keepAliveRoute.value = [];\n    leftMenuData(tabMenuChildren);\n  };\n  var leftMenuData = function leftMenuData(item, type) {\n    // 显示左侧菜单方法\n    // 不是工作台页面判断是否有子级菜单\n    if (item.children && item.children.length) {\n      // 有子级菜单按照左侧菜单显示\n      if (type) {\n        isView.value = true;\n        isChildView.value = false;\n      } else {\n        isView.value = true;\n        isChildView.value = true;\n        childData.value = item;\n      }\n      menuData.value = item.children;\n      var obj = openPageId.value ? _menuOpenPage(item.children) : _menuDefault(item.children);\n      menuId.value = obj.id;\n      openPageId.value = '';\n      openPageChildId.value = '';\n      menuClick(obj);\n    } else {\n      if (['3', '4'].includes(item.menuRouteType.value)) {\n        isView.value = false;\n        isChildView.value = true;\n        childData.value = item;\n        router.push({\n          path: item.routePath,\n          query: _objectSpread(_objectSpread({}, routePathData(item.routePath)), {}, {\n            menuRouteType: item.menuRouteType.value\n          })\n        });\n      } else {\n        if (type) {\n          isView.value = false;\n          isChildView.value = false;\n        } else {\n          isView.value = false;\n          isChildView.value = true;\n          childData.value = item;\n        }\n        menuData.value = [item];\n        var _obj = openPageId.value ? _menuOpenPage([item]) : _menuDefault([item]);\n        menuId.value = _obj.id;\n        openPageId.value = '';\n        openPageChildId.value = '';\n        menuClick(_obj);\n      }\n    }\n  };\n  var _menuDefault = function menuDefault(data) {\n    // 获取左侧菜单第一个菜单\n    var defaultObj = {};\n    for (var i = 0, len = data.length; i < len; i++) {\n      if (i === 0) {\n        if (data[i].children.length === 0) {\n          defaultObj = data[i];\n        } else {\n          defaultObj = _menuDefault(data[i].children);\n        }\n      }\n    }\n    return defaultObj;\n  };\n  var _menuOpenPage = function menuOpenPage(data) {\n    // 获取左侧菜单第一个菜单\n    var defaultObj = {};\n    for (var i = 0, len = data.length; i < len; i++) {\n      if (openPageId.value === data[i].id) {\n        defaultObj = data[i];\n      }\n      if (data[i].children.length) {\n        var obj = _menuOpenPage(data[i].children);\n        defaultObj = obj.id ? obj : defaultObj;\n      }\n    }\n    return defaultObj;\n  };\n  var menuId = ref('');\n  var menuData = ref([]);\n  var menuClick = function menuClick(item) {\n    // 左侧菜单点击事件\n    if (!tabData.value.length) {\n      qiankunActions.setGlobalState({\n        keepAliveRoute: []\n      });\n    }\n    if (!tabData.value.map(function (v) {\n      return v.id;\n    }).includes(item.id)) {\n      tabData.value.push(item);\n    }\n    tabClick();\n  };\n  var WorkBenchReturn = function WorkBenchReturn() {\n    // 工作台具体应用返回工作台\n    if (isChildView.value) {\n      isView.value = false;\n      isChildView.value = false;\n      handleClick();\n    }\n  };\n  var handleBreadcrumb = function handleBreadcrumb(item, index) {\n    if (index + 1 === tabData.value.length) return;\n    var newTabData = tabData.value.slice(0, index + 1);\n    var delTabData = tabData.value.slice(index + 1).map(function (v) {\n      return v.id;\n    });\n    tabData.value = newTabData;\n    isTabData.value = isTabData.value.filter(function (item) {\n      return !delTabData.includes(item.id);\n    });\n    var mainAppList = tabData.value.filter(function (v) {\n      return routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/';\n    });\n    var mainAppName = Array.from(new Set(mainAppList.map(function (v) {\n      return mainRoutePath(v.routePath);\n    })));\n    keepAliveRoute.value = mainAppName;\n    menuId.value = item.id;\n    tabClick();\n  };\n  var tabData = ref([]); // tab数据\n  var isTabData = ref([]);\n  var isMicroApp = ref('');\n  var noMicroApp = ref([]);\n  var keepAliveRoute = ref([]);\n  var add_msg = function add_msg(a, b) {\n    return a.filter(function (v) {\n      return b.indexOf(v) === -1;\n    });\n  };\n  // const delete_msg = (a, b) => b.filter(v => a.indexOf(v) === -1)\n  var tabClick = function tabClick() {\n    var microAppName = Object.keys(config.microApp);\n    var MicroAppData = Array.from(new Set(tabData.value.map(function (v) {\n      return routePath(v.routePath);\n    }))).filter(function (v) {\n      return microAppName.includes(v);\n    });\n    var addMicroApp = add_msg(MicroAppData, MicroApp.value);\n    // const delMicroApp = delete_msg(MicroAppData, MicroApp.value)\n    MicroApp.value = [].concat(_toConsumableArray(MicroApp.value), _toConsumableArray(addMicroApp));\n    if (!addMicroApp.length) {\n      menuRouterPush();\n      return;\n    }\n    nextTick(function () {\n      var _loop2 = function _loop2() {\n        var v = addMicroApp[i];\n        if (!MicroAppObj.value[v]) {\n          MicroAppObj.value[v] = loadFilterApp(v);\n          MicroAppObj.value[v].loadPromise.then(function () {\n            MicroAppObj.value[v].mountPromise.then(function () {\n              qiankunActions.setGlobalState({\n                theme: store.getters.getThemeFn,\n                user: store.getters.getUserFn,\n                menu: store.getters.getMenuFn,\n                area: store.getters.getAreaFn,\n                role: store.getters.getRoleFn,\n                readConfig: store.getters.getReadConfig,\n                readOpenConfig: store.getters.getReadOpenConfig\n              });\n            });\n          }).catch(function (err) {\n            noMicroApp.value.push(v);\n          });\n        }\n      };\n      for (var i = 0, len = addMicroApp.length; i < len; i++) {\n        _loop2();\n      }\n      setTimeout(function () {\n        menuRouterPush();\n      }, 52);\n    });\n  };\n  var menuRouterPush = function menuRouterPush() {\n    for (var i = 0, len = tabData.value.length; i < len; i++) {\n      var item = tabData.value[i];\n      if (menuId.value === item.id) {\n        sessionStorage.setItem('has', JSON.stringify(item));\n        var menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath;\n        var getMicroName = routePath(menuUrl);\n        isMicroApp.value = getMicroName;\n        if (MicroApp.value.includes(getMicroName) && !noMicroApp.value.includes(getMicroName)) {\n          var _item$menuRouteType;\n          var query = _objectSpread(_objectSpread({\n            menuRouteType: (_item$menuRouteType = item.menuRouteType) === null || _item$menuRouteType === void 0 ? void 0 : _item$menuRouteType.value\n          }, routePathData(item.routePath)), item.query);\n          router.push({\n            path: item.routePath,\n            query: query\n          });\n          MicroAppObj.value[getMicroName].mountPromise.then(function () {\n            qiankunActions.setGlobalState({\n              keepAliveRoute: tabData.value.map(function (v) {\n                return v.routePath;\n              })\n            });\n          });\n        } else {\n          if (getMicroName === '/') {\n            var _item$menuRouteType2;\n            var _query = _objectSpread(_objectSpread({\n              menuRouteType: (_item$menuRouteType2 = item.menuRouteType) === null || _item$menuRouteType2 === void 0 ? void 0 : _item$menuRouteType2.value\n            }, routePathData(item.routePath)), item.query);\n            router.push({\n              path: item.routePath,\n              query: _query\n            });\n            var mainAppList = tabData.value.filter(function (v) {\n              return routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/';\n            });\n            var mainAppName = Array.from(new Set(mainAppList.map(function (v) {\n              return mainRoutePath(v.routePath);\n            })));\n            keepAliveRoute.value = mainAppName;\n          } else {\n            router.push({\n              path: '/NotFoundPage'\n            });\n          }\n        }\n      }\n    }\n  };\n  var mainRoutePath = function mainRoutePath(url) {\n    var path = '';\n    var start = url.indexOf('/') + 1;\n    var end = url.indexOf('?');\n    if (end === -1) {\n      path = url.substring(1);\n    } else {\n      path = url.substring(start, end);\n    }\n    return path;\n  };\n  var routePathData = function routePathData(href) {\n    var params = {};\n    href = href.substring(href.indexOf('?') + 1);\n    var arr = href.split('&');\n    arr.forEach(function (item) {\n      var a = item.split('=');\n      params[a[0]] = a[1];\n    });\n    return params;\n  };\n  // 获取第一个斜杠和第二个斜杠之间的内容\n  var routePath = function routePath(url) {\n    var path = ''; // 第二个斜杠前内容\n    var first = url.indexOf('/') + 1; // 从第一个斜杠算起（+1表示不包括该斜杠）\n    var kong = url.indexOf(' ', first); // 第一个斜杠后的第一个空格\n    var heng = url.indexOf('/', first); // 第一个斜杠后的第一个斜杠（即第二个斜杠）\n    if (heng === -1) {\n      path = url.substring(1, kong);\n    } else {\n      path = url.substring(1, heng);\n    }\n    return path;\n  };\n  watch(function () {\n    return store.state.openRoute;\n  }, function (val) {\n    if (val.path) {\n      openRoute(val);\n    }\n  });\n  watch(function () {\n    return store.state.closeOpenRoute;\n  }, function (val) {\n    if (val.closeId) {\n      delRoute(val);\n    }\n  });\n  var openRoute = function openRoute(val) {\n    if (isTabData.value.map(function (v) {\n      return v.isData;\n    }).includes(JSON.stringify(val))) {\n      for (var i = 0, len = isTabData.value.length; i < len; i++) {\n        var item = isTabData.value[i];\n        if (item.isData === JSON.stringify(val)) {\n          menuId.value = item.id;\n          tabClick();\n        }\n      }\n    } else {\n      var id = guid();\n      isTabData.value.push({\n        id: id,\n        isData: JSON.stringify(val)\n      });\n      tabData.value.push({\n        id,\n        name: val.name,\n        routePath: val.path,\n        query: _objectSpread(_objectSpread({}, val.query), {}, {\n          routeId: id,\n          oldRouteId: menuId.value\n        })\n      });\n      menuId.value = id;\n      tabClick();\n    }\n    qiankunActions.setGlobalState({\n      openRoute: {\n        name: '',\n        path: '',\n        query: {}\n      }\n    });\n  };\n  var delRoute = function delRoute(val) {\n    if (val.openId) {\n      isTabData.value = isTabData.value.filter(function (item) {\n        return item.id !== val.closeId;\n      });\n      tabData.value = tabData.value.filter(function (item) {\n        return item.id !== val.closeId;\n      });\n      menuId.value = val.openId;\n      tabClick();\n    } else {\n      handleClose(val.closeId);\n    }\n    qiankunActions.setGlobalState({\n      closeOpenRoute: {\n        openId: '',\n        closeId: ''\n      }\n    });\n  };\n  var isRefresh = ref(true);\n  var handleRefresh = function handleRefresh(id) {\n    if (route.meta.moduleName === 'main') {\n      keepAliveRoute.value = keepAliveRoute.value.filter(function (v) {\n        return v !== route.name;\n      });\n      isRefresh.value = false;\n      setTimeout(function () {\n        keepAliveRoute.value.push(route.name);\n        isRefresh.value = true;\n      }, 200);\n    } else {\n      for (var i = 0, len = tabData.value.length; i < len; i++) {\n        var item = tabData.value[i];\n        if (item.id === id) {\n          qiankunActions.setGlobalState({\n            refreshRoute: item.routePath\n          });\n          setTimeout(function () {\n            qiankunActions.setGlobalState({\n              refreshRoute: ''\n            });\n          }, 222);\n        }\n      }\n    }\n  };\n  var handleClose = function handleClose(id) {\n    if (menuId.value === id) {\n      for (var i = 0, len = tabData.value.length; i < len; i++) {\n        var item = tabData.value[i];\n        if (item.id === id) {\n          menuId.value = tabData.value[i ? i - 1 : 1].id;\n          tabClick();\n        }\n      }\n    }\n    isTabData.value = isTabData.value.filter(function (item) {\n      return item.id !== id;\n    });\n    tabData.value = tabData.value.filter(function (item) {\n      return item.id !== id;\n    });\n  };\n  var handleCloseOther = function handleCloseOther(id) {\n    isTabData.value = isTabData.value.filter(function (item) {\n      return item.id === id;\n    });\n    tabData.value = tabData.value.filter(function (item) {\n      return item.id === id;\n    });\n    menuId.value = id;\n    tabClick();\n  };\n  var _openPageMenu = function openPageMenu(nodes, key, value) {\n    if (!nodes || !nodes.length) return []; // eslint-disable-line\n    var children = [];\n    var _iterator = _createForOfIteratorHelper(nodes),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var node = _step.value;\n        if (node[key] === value && !openPageId.value) {\n          openPageId.value = node.id;\n          openPageObj.value = node;\n        }\n        node = Object.assign({}, node);\n        var sub = _openPageMenu(node.children, key, value);\n        if (sub && sub.length || node[key] === value) {\n          sub.length && (node.children = sub);\n          children.push(node);\n        }\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    return children.length ? children : []; // eslint-disable-line\n  };\n  var openPage = function openPage(_ref3) {\n    var _ref3$key = _ref3.key,\n      key = _ref3$key === void 0 ? 'id' : _ref3$key,\n      value = _ref3.value;\n    if (isOpen.value) return;\n    isOpen.value = true;\n    var openPagedata = _openPageMenu(tabMenuData.value, key, value)[0] || {};\n    if (openPagedata.id) {\n      if (tabMenu.value === openPagedata.id) {\n        if (openPagedata.routePath === '/WorkBench') {\n          var _openPagedata$childre;\n          if (openPagedata.id === openPageId.value) {\n            openPageId.value = '';\n            openPageObj.value = {};\n            WorkBenchReturn();\n            return;\n          }\n          if (((_openPagedata$childre = openPagedata.children[0]) === null || _openPagedata$childre === void 0 ? void 0 : _openPagedata$childre.id) === childData.value.id) {\n            menuId.value = openPageId.value;\n            menuClick(openPageObj.value);\n            openPageId.value = '';\n            openPageObj.value = {};\n            nextTick(function () {\n              if (isOpen.value) {\n                isOpen.value = false;\n              }\n            });\n          } else {\n            WorkBenchReturn();\n            setTimeout(function () {\n              nextTick(function () {\n                var _openPagedata$childre2;\n                openPageChildId.value = (_openPagedata$childre2 = openPagedata.children[0]) === null || _openPagedata$childre2 === void 0 ? void 0 : _openPagedata$childre2.id;\n                handleClick();\n                nextTick(function () {\n                  if (isOpen.value) {\n                    isOpen.value = false;\n                  }\n                });\n              });\n            }, 200);\n          }\n        } else {\n          if (openPagedata.routePath.includes('/GlobalHome')) {\n            handleClick();\n            nextTick(function () {\n              if (isOpen.value) {\n                isOpen.value = false;\n              }\n            });\n            return;\n          }\n          menuId.value = openPageId.value;\n          menuClick(openPageObj.value);\n          openPageId.value = '';\n          openPageObj.value = {};\n          nextTick(function () {\n            if (isOpen.value) {\n              isOpen.value = false;\n            }\n          });\n        }\n      } else {\n        if (isChildView.value) {\n          WorkBenchReturn();\n          setTimeout(function () {\n            nextTick(function () {\n              tabMenu.value = openPagedata.id;\n              if (openPagedata.id === openPageId.value) {\n                openPageId.value = '';\n                openPageObj.value = {};\n              } else {\n                var _openPagedata$childre3;\n                openPageChildId.value = (_openPagedata$childre3 = openPagedata.children[0]) === null || _openPagedata$childre3 === void 0 ? void 0 : _openPagedata$childre3.id;\n              }\n              handleClick();\n              nextTick(function () {\n                if (isOpen.value) {\n                  isOpen.value = false;\n                }\n              });\n            });\n          }, 200);\n        } else {\n          tabMenu.value = openPagedata.id;\n          if (openPagedata.id === openPageId.value) {\n            openPageId.value = '';\n            openPageObj.value = {};\n          } else {\n            var _openPagedata$childre4;\n            openPageChildId.value = (_openPagedata$childre4 = openPagedata.children[0]) === null || _openPagedata$childre4 === void 0 ? void 0 : _openPagedata$childre4.id;\n          }\n          handleClick();\n          nextTick(function () {\n            if (isOpen.value) {\n              isOpen.value = false;\n            }\n          });\n        }\n      }\n    } else {\n      ElMessage({\n        type: 'warning',\n        message: '未检测到你有此菜单！'\n      });\n    }\n  };\n  var setOpenPageId = function setOpenPageId(id) {\n    openPageId.value = id;\n  };\n  var guid = function guid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      var r = Math.random() * 16 | 0,\n        v = c == 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  };\n  provide('delRoute', delRoute);\n  provide('WorkBenchList', WorkBenchList);\n  provide('leftMenuData', leftMenuData);\n  provide('WorkBenchMenu', WorkBenchMenu);\n  provide('setOpenPageId', setOpenPageId);\n  provide('openPage', openPage);\n  provide('openRoute', openRoute);\n  // 组织选择相关\n  var isOrganizationSelectShow = ref(false);\n  var currentOrganization = ref(null);\n  var organizationList = ref([]);\n\n  // 获取用户可访问的组织列表\n  var getOrganizationList = /*#__PURE__*/function () {\n    var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n      var _yield$api$userOrgani, data, savedOrgId, savedOrgData, savedOrg, defaultOrg, defaultOrgId;\n      return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n        while (1) switch (_context3.prev = _context3.next) {\n          case 0:\n            _context3.prev = 0;\n            _context3.next = 3;\n            return api.userOrganizationList();\n          case 3:\n            _yield$api$userOrgani = _context3.sent;\n            data = _yield$api$userOrgani.data;\n            organizationList.value = data || [];\n\n            // 设置默认组织\n            if (organizationList.value.length > 0) {\n              savedOrgId = sessionStorage.getItem('GlobalOrganizationId');\n              savedOrgData = sessionStorage.getItem('OrganizationRow');\n              if (savedOrgId && savedOrgData) {\n                savedOrg = JSON.parse(savedOrgData);\n                currentOrganization.value = savedOrg;\n              } else {\n                // 如果没有保存的组织信息，使用第一个组织\n                defaultOrg = organizationList.value[0];\n                currentOrganization.value = defaultOrg;\n                defaultOrgId = defaultOrg.id || defaultOrg.orgId;\n                sessionStorage.setItem('GlobalOrganizationId', defaultOrgId);\n                sessionStorage.setItem('OrganizationRow', JSON.stringify(defaultOrg));\n              }\n            } else {\n              console.warn('组织列表为空');\n            }\n            _context3.next = 12;\n            break;\n          case 9:\n            _context3.prev = 9;\n            _context3.t0 = _context3[\"catch\"](0);\n            console.error('获取组织列表失败:', _context3.t0);\n          case 12:\n          case \"end\":\n            return _context3.stop();\n        }\n      }, _callee3, null, [[0, 9]]);\n    }));\n    return function getOrganizationList() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n\n  // 处理组织选择\n  var handleOrganizationChange = /*#__PURE__*/function () {\n    var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(item) {\n      var _currentOrganization$, _currentOrganization$2;\n      var currentOrgId, newOrgId, orgId, _yield$api$setUserOrg, code;\n      return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n        while (1) switch (_context4.prev = _context4.next) {\n          case 0:\n            // 获取组织ID，兼容不同的字段名\n            currentOrgId = ((_currentOrganization$ = currentOrganization.value) === null || _currentOrganization$ === void 0 ? void 0 : _currentOrganization$.id) || ((_currentOrganization$2 = currentOrganization.value) === null || _currentOrganization$2 === void 0 ? void 0 : _currentOrganization$2.orgId);\n            newOrgId = item.id || item.orgId;\n            if (!(currentOrgId === newOrgId)) {\n              _context4.next = 5;\n              break;\n            }\n            console.log('组织未变化，跳过切换');\n            return _context4.abrupt(\"return\");\n          case 5:\n            _context4.prev = 5;\n            // 获取组织ID，兼容不同的字段名\n            orgId = item.id || item.orgId; // 调用设置用户当前组织接口\n            _context4.next = 9;\n            return api.setUserOrganization({\n              organizationId: orgId\n            });\n          case 9:\n            _yield$api$setUserOrg = _context4.sent;\n            code = _yield$api$setUserOrg.code;\n            if (!(code === 200)) {\n              _context4.next = 34;\n              break;\n            }\n            currentOrganization.value = item;\n            sessionStorage.setItem('OrganizationRow', JSON.stringify(item));\n            sessionStorage.setItem('GlobalOrganizationId', orgId);\n\n            // 更新Vuex状态\n            store.commit('setGlobalOrganizationId', orgId);\n            store.commit('setGlobalOrganizationData', item);\n            ElMessage({\n              type: 'success',\n              message: `已切换到组织：${item.orgName}`\n            });\n\n            // 清除当前菜单和标签页状态\n            tabMenu.value = '';\n            tabMenuData.value = [];\n            menuData.value = [];\n            tabData.value = [];\n            isTabData.value = [];\n            keepAliveRoute.value = [];\n            // 重新获取用户信息和权限\n            _context4.next = 26;\n            return store.dispatch('loginUser', 'login');\n          case 26:\n            // 刷新相关组件\n            store.commit('setBoxMessageRefresh', true);\n            store.commit('setPersonalDoRefresh', true);\n            // 重新获取组织列表（以防权限变化）\n            _context4.next = 30;\n            return getOrganizationList();\n          case 30:\n            // 重新获取系统配置\n            globalReadOpenConfig();\n            ElMessage({\n              type: 'success',\n              message: '组织切换成功，页面已刷新'\n            });\n            _context4.next = 35;\n            break;\n          case 34:\n            ElMessage({\n              type: 'error',\n              message: '切换组织失败'\n            });\n          case 35:\n            _context4.next = 40;\n            break;\n          case 37:\n            _context4.prev = 37;\n            _context4.t0 = _context4[\"catch\"](5);\n            ElMessage({\n              type: 'error',\n              message: '切换组织失败，请重试'\n            });\n          case 40:\n          case \"end\":\n            return _context4.stop();\n        }\n      }, _callee4, null, [[5, 37]]);\n    }));\n    return function handleOrganizationChange(_x3) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  var organizationSelect = function organizationSelect(item) {\n    isOrganizationSelectShow.value = false;\n    sessionStorage.setItem(\"OrganizationRow\", JSON.stringify(item));\n    sessionStorage.setItem(\"GlobalOrganizationId\", item.id);\n    // 更新Vuex状态\n    store.commit(\"setGlobalOrganizationId\", item.id);\n    store.commit(\"setGlobalOrganizationData\", item);\n    ElMessage({\n      type: \"success\",\n      message: `已切换到组织：${item.name}`\n    });\n  };\n\n  // 处理组织选择显示\n  var handleOrganizationSelect = function handleOrganizationSelect() {\n    var _user$value8, _user$value9;\n    var oldOrganizationInfo = sessionStorage.getItem(\"oldOrganizationInfo\") || \"\";\n    var isOrganizationSelect = sessionStorage.getItem(\"isOrganizationSelect\") || \"\";\n    if (((_user$value8 = user.value) === null || _user$value8 === void 0 ? void 0 : _user$value8.accountId) !== \"1\" && ((_user$value9 = user.value) === null || _user$value9 === void 0 ? void 0 : _user$value9.organizationTotal) > 1 && oldOrganizationInfo && !isOrganizationSelect) {\n      isOrganizationSelectShow.value = true;\n    }\n  };\n  return {\n    user,\n    area,\n    role,\n    left,\n    width,\n    LayoutViewBox,\n    LayoutViewInfo,\n    helpShow,\n    handleCommand,\n    suggestPopShow,\n    editPassWordShow,\n    verifyEditPassWord,\n    verifyEditPassWordShow,\n    editPassWordCallback,\n    regionId,\n    regionName,\n    regionSelect,\n    isRegionSelectShow,\n    organizationSelect,\n    isOrganizationSelectShow,\n    currentOrganization,\n    organizationList,\n    handleOrganizationChange,\n    isView,\n    isChildView,\n    tabMenu,\n    tabMenuData,\n    handleClick,\n    menuId,\n    menuData,\n    menuClick,\n    handleBreadcrumb,\n    WorkBenchObj,\n    WorkBenchList,\n    childData,\n    WorkBenchReturn,\n    isRefresh,\n    keepAliveRoute,\n    tabData,\n    tabClick,\n    handleRefresh,\n    handleClose,\n    handleCloseOther,\n    isMicroApp,\n    MicroApp\n  };\n};\nexport var qiankun = function qiankun(route) {\n  var isMain = ref(false);\n  var isMainPage = function isMainPage() {\n    isMain.value = route.meta.moduleName === 'main';\n  };\n  watch(function () {\n    return route;\n  }, function () {\n    isMainPage();\n  }, {\n    deep: true\n  });\n  onMounted(function () {\n    isMainPage(route);\n  });\n  return {\n    isMain\n  };\n};\nexport var ChatMethod = function ChatMethod() {\n  var store = useStore();\n  var rongCloudToken = computed(function () {\n    return store.getters.getRongCloudToken;\n  });\n  return {\n    rongCloudToken\n  };\n};\nexport var AiChatMethod = function AiChatMethod() {\n  var store = useStore();\n  var AiChatWidth = computed(function () {\n    return store.state.AiChatWidth;\n  });\n  var AiChatTargetWidth = computed(function () {\n    return `${AiChatWidth.value}px`;\n  });\n  var AiChatViewType = ref(false);\n  var AiChatWindowShow = ref(false);\n  // 自动吸附到最近的侧边\n  var handleResizeFloatingWindow = function handleResizeFloatingWindow() {\n    if (window.innerWidth > 1280 + 400) {\n      var width = window.innerWidth - 1280 > 520 ? 520 : 400;\n      store.commit('setAiChatWidth', width);\n      if (!AiChatViewType.value) AiChatWindowShow.value = false;\n      AiChatViewType.value = true;\n    } else {\n      store.commit('setAiChatWidth', 400);\n      if (AiChatViewType.value) AiChatWindowShow.value = false;\n      AiChatViewType.value = false;\n    }\n  };\n  onMounted(function () {\n    handleResizeFloatingWindow();\n    window.addEventListener('resize', handleResizeFloatingWindow);\n  });\n  onUnmounted(function () {\n    window.removeEventListener('resize', handleResizeFloatingWindow);\n  });\n  return {\n    AiChatTargetWidth,\n    AiChatViewType,\n    AiChatWindowShow\n  };\n};", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ref", "watch", "onMounted", "computed", "nextTick", "provide", "onUnmounted", "api", "useStore", "config", "qiankunActions", "loadFilterApp", "handleCompareVersion", "globalReadOpenConfig", "get_font_family", "change_font_family", "elementResizeDetectorMaker", "unauthorized", "ElMessage", "ElMessageBox", "openConfig", "refreshIcon", "systemPlatform", "_openConfig$value", "LayoutViewUnitedFront", "route", "router", "store", "menuIcon", "API_URL", "erd", "left", "width", "LayoutViewBox", "LayoutViewInfo", "editPassWordShow", "verifyEditPassWord", "verifyEditPassWordShow", "MicroApp", "MicroAppObj", "user", "getters", "getUserFn", "area", "getAreaFn", "role", "getRoleFn", "getReadOpenConfig", "helpShow", "suggestPopShow", "hasDuplicates", "arr1", "arr2", "set", "Set", "some", "item", "has", "special<PERSON><PERSON><PERSON><PERSON>s", "JSON", "parse", "sessionStorage", "getItem", "canSee", "console", "log", "handleRegionSelect", "handleOrganizationSelect", "getOrganizationList", "listenTo", "element", "offsetWidth", "_user$value", "_user$value2", "oldRegionInfo", "isRegionSelect", "accountId", "areaTotal", "isRegionSelectShow", "handleCommand", "commit", "show", "window", "location", "href", "mainP<PERSON>", "Date", "getTime", "handleExit", "message", "editPassWordCallback", "loginOut", "confirm", "confirmButtonText", "cancelButtonText", "_ref", "_callee", "text", "_yield$api$loginOut", "code", "goal_login_router_path", "goal_login_router_query", "_callee$", "_context", "clear", "localStorage", "path", "query", "showClose", "_x", "regionId", "regionName", "regionSelect", "_user$value3", "setItem", "stringify", "areaId", "id", "verifyLoginUser", "_ref2", "_callee2", "_yield$api$verifyLogi", "_user$value4", "_callee2$", "_context2", "tabMenu", "dispatch", "_x2", "_user$value5", "_user$value6", "_user$value7", "verify", "Number", "_openConfig$value2", "forbidWeakPassword", "immediate", "filterMenu", "menuList", "newMenuList", "len", "menuId", "routePath", "menuFunction", "menuRouteType", "icon", "iconUrl", "fileURL", "permissions", "children", "tabMenuData", "getMenuFn", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isOpen", "openPageId", "openPageObj", "openPageChildId", "WorkBenchObj", "WorkBenchList", "childData", "val", "openPageValue", "openPage", "key", "openPageKey", "_tabMenuData$value$", "handleClick", "process", "env", "NODE_ENV", "detection_version", "DetectionVersion", "menuData", "tabData", "isTabData", "keepAliveRoute", "_loop", "routePathData", "leftMenuData", "includes", "_objectSpread", "_ret", "WorkBenchMenu", "tabMenuId", "tabMenuChildren", "obj", "menuOpenPage", "menuDefault", "menuClick", "data", "defaultObj", "setGlobalState", "map", "tabClick", "WorkBenchReturn", "handleBreadcrumb", "index", "newTabData", "delTabData", "filter", "mainAppList", "substring", "indexOf", "mainAppName", "Array", "from", "mainRoutePath", "isMicroApp", "noMicroApp", "add_msg", "b", "microAppName", "microApp", "MicroAppData", "addMicroApp", "concat", "_toConsumableArray", "menuRouterPush", "_loop2", "loadPromise", "mountPromise", "theme", "getThemeFn", "menu", "readConfig", "getReadConfig", "readOpenConfig", "err", "setTimeout", "menuUrl", "getMicroName", "_item$menuRouteType", "_item$menuRouteType2", "url", "start", "end", "params", "arr", "split", "first", "kong", "heng", "state", "openRoute", "closeOpenRoute", "closeId", "delRoute", "isData", "guid", "routeId", "oldRouteId", "openId", "handleClose", "isRefresh", "handleRefresh", "meta", "moduleName", "refreshRoute", "handleCloseOther", "openPageMenu", "nodes", "_iterator", "_createForOfIteratorHelper", "_step", "node", "assign", "sub", "_ref3", "_ref3$key", "openPagedata", "_openPagedata$childre", "_openPagedata$childre2", "_openPagedata$childre3", "_openPagedata$childre4", "setOpenPageId", "replace", "Math", "random", "toString", "isOrganizationSelectShow", "currentOrganization", "organizationList", "_ref4", "_callee3", "_yield$api$userOrgani", "savedOrgId", "savedOrgData", "savedOrg", "defaultOrg", "defaultOrgId", "_callee3$", "_context3", "userOrganizationList", "orgId", "warn", "t0", "error", "handleOrganizationChange", "_ref5", "_callee4", "_currentOrganization$", "_currentOrganization$2", "currentOrgId", "newOrgId", "_yield$api$setUserOrg", "_callee4$", "_context4", "setUserOrganization", "organizationId", "orgName", "_x3", "organizationSelect", "_user$value8", "_user$value9", "oldOrganizationInfo", "isOrganizationSelect", "organizationTotal", "qiankun", "is<PERSON><PERSON>", "isMainPage", "deep", "ChatMethod", "rongCloudToken", "getRongCloudToken", "AiChatMethod", "AiChatWidth", "AiChatTargetWidth", "AiChatViewType", "AiChatWindowShow", "handleResizeFloatingWindow", "innerWidth", "addEventListener", "removeEventListener"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutViewUnitedFront/LayoutViewUnitedFront.js"], "sourcesContent": ["import { ref, watch, onMounted, computed, nextTick, provide, onUnmounted } from 'vue'\r\nimport api from '@/api'\r\nimport { useStore } from 'vuex'\r\nimport config from 'common/config'\r\nimport { qiankunActions, loadFilterApp } from '@/qiankun'\r\nimport { handleCompareVersion } from 'common/js/CheckVersion'\r\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod'\r\nimport { get_font_family, change_font_family } from 'common/js/utils'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nimport unauthorized from 'common/components/unauthorized'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { openConfig } from 'common/js/system_var.js'\r\nexport const refreshIcon = `<svg t=\"1743990581190\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"52145\" width=\"30\" height=\"30\"><path d=\"M512 128a384 384 0 1 1 0 768A384 384 0 0 1 512 128z m0 48.192C335.296 176.192 174.08 335.36 174.08 512c0 176.704 161.216 336.512 337.92 336.512S849.152 688.704 849.152 512 688.704 176.192 512 176.192z\" fill=\"#ffffff\" p-id=\"52146\"></path><path d=\"M244.352 506.688h73.6c0-123.2 86.016-220.032 196.48-215.68 36.8 0 69.568 13.248 102.272 30.848l-36.8 39.68c-20.48-8.96-45.056-17.664-69.568-17.664-77.76 0-143.232 70.4-143.232 162.816h73.6l-98.176 110.08-98.176-110.08zM705.088 515.84c0 117.248-86.848 217.28-194.368 217.28-37.248 0-70.4-17.344-103.424-34.752l37.184-39.04c20.736 8.704 45.568 17.344 70.4 17.344 78.592 0 144.768-69.504 144.768-160.768H581.056l99.328-108.608 99.2 108.544h-74.496z\" fill=\"#ffffff\" p-id=\"52147\"></path></svg>`\r\nconst systemPlatform = computed(() => openConfig.value?.systemPlatform || '')\r\nexport const LayoutViewUnitedFront = (route, router) => {\r\n  const store = useStore()\r\n  const menuIcon = `${config.API_URL}/pageImg/open/menuIcon`\r\n\r\n  const erd = elementResizeDetectorMaker()\r\n  const left = ref(0)\r\n  const width = ref('')\r\n  const LayoutViewBox = ref(null)\r\n  const LayoutViewInfo = ref(null)\r\n  const editPassWordShow = ref(false)\r\n  const verifyEditPassWord = ref('')\r\n  const verifyEditPassWordShow = ref(false)\r\n  const MicroApp = ref([])\r\n  const MicroAppObj = ref({})\r\n  // 用户信息\r\n  const user = computed(() => store.getters.getUserFn)\r\n  // 地区信息\r\n  const area = computed(() => store.getters.getAreaFn)\r\n  // 角色信息\r\n  const role = computed(() => store.getters.getRoleFn)\r\n  const openConfig = computed(() => store.getters.getReadOpenConfig)\r\n  const helpShow = ref(false)\r\n  const suggestPopShow = ref(false)\r\n  const hasDuplicates = (arr1, arr2) => {\r\n    const set = new Set(arr1);\r\n    return arr2.some(item => set.has(item));\r\n  }\r\n  onMounted(() => {\r\n    const specialRoleKeys = JSON.parse(sessionStorage.getItem('user')).specialRoleKeys || []\r\n    const canSee = systemPlatform.value === 'CPPCC' ? ['proposal_committee', 'suggestion_office_user', 'cppcc_member'] : ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member']\r\n    suggestPopShow.value = hasDuplicates(specialRoleKeys, canSee)\r\n    console.log(\"🚀 ~ onMounted ~  suggestPopShow.value :\", suggestPopShow.value)\r\n    handleRegionSelect()\r\n    handleOrganizationSelect()\r\n    getOrganizationList()\r\n    nextTick(() => {\r\n      erd.listenTo(LayoutViewBox.value, (element) => {\r\n        left.value = element.offsetWidth\r\n      })\r\n      erd.listenTo(LayoutViewInfo.value, (element) => {\r\n        width.value = `width: calc(100% - ${element.offsetWidth + 16}px);`\r\n      })\r\n    })\r\n  })\r\n  const handleRegionSelect = () => {\r\n    const oldRegionInfo = sessionStorage.getItem('oldRegionInfo') || ''\r\n    const isRegionSelect = sessionStorage.getItem('isRegionSelect') || ''\r\n    if (user.value?.accountId !== '1' && user.value?.areaTotal > 1 && oldRegionInfo && !isRegionSelect) {\r\n      isRegionSelectShow.value = true\r\n    }\r\n  }\r\n  const handleCommand = (type) => {\r\n    if (type === 'task') {\r\n      store.commit('setGlobalCentralControlObj', { show: true })\r\n    } else if (type === 'refresh') {\r\n      // window.location.reload(true)\r\n      // window.location.reload(window.location.href)\r\n      window.location.href = `${config.mainPath}?v=${new Date().getTime()}`\r\n    } else if (type === 'locale') {\r\n      if (get_font_family() === 'cn') return change_font_family('tw')\r\n      if (get_font_family() === 'tw') return change_font_family('cn')\r\n    } else if (type === 'help') {\r\n      helpShow.value = true\r\n    } else if (type === 'edit_password') {\r\n      verifyEditPassWord.value = ''\r\n      editPassWordShow.value = true\r\n    } else if (type === 'exit') {\r\n      handleExit()\r\n    } else {\r\n      ElMessage({ type: 'info', message: '正在开发中！' })\r\n    }\r\n  }\r\n  const editPassWordCallback = (type) => {\r\n    if (type) {\r\n      loginOut('请使用新密码重新登录！')\r\n    }\r\n    editPassWordShow.value = false\r\n  }\r\n  const handleExit = () => {\r\n    ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        loginOut('已安全退出！')\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消退出' })\r\n      })\r\n  }\r\n  const loginOut = async (text) => {\r\n    const { code } = await api.loginOut()\r\n    if (code === 200) {\r\n      sessionStorage.clear()\r\n      const goal_login_router_path = localStorage.getItem('goal_login_router_path')\r\n      if (goal_login_router_path) {\r\n        const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''\r\n        router.push({\r\n          path: goal_login_router_path,\r\n          query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\r\n        })\r\n      } else {\r\n        router.push({ path: '/LoginView' })\r\n      }\r\n      store.commit('setState')\r\n      globalReadOpenConfig()\r\n      // store.state.socket.disconnect()\r\n      // store.state.socket = null\r\n      ElMessage({ message: text, showClose: true, type: 'success' })\r\n    }\r\n  }\r\n\r\n  // 地区id\r\n  const regionId = ref('')\r\n  const regionName = ref('')\r\n  const isRegionSelectShow = ref(false)\r\n  const regionSelect = (item) => {\r\n    regionName.value = item.name\r\n    isRegionSelectShow.value = false\r\n    sessionStorage.setItem('AreaRow', JSON.stringify(item))\r\n    if (user.value?.areaId === item.id) return\r\n    verifyLoginUser(item)\r\n  }\r\n  const verifyLoginUser = async (item) => {\r\n    const { code } = await api.verifyLoginUser({}, item.id)\r\n    if (code === 200) {\r\n      tabMenu.value = ''\r\n      sessionStorage.setItem('AreaId', item.id)\r\n      globalReadOpenConfig()\r\n      store.dispatch('loginUser', 'login')\r\n      store.commit('setBoxMessageRefresh', true)\r\n      store.commit('setPersonalDoRefresh', true)\r\n    } else {\r\n      regionId.value = user.value?.areaId\r\n      unauthorized({ name: item.name })\r\n    }\r\n  }\r\n  watch(\r\n    () => user.value,\r\n    () => {\r\n      regionId.value = user.value?.areaId\r\n      if (user.value?.accountId) {\r\n        const verify = sessionStorage.getItem('verify')\r\n        if (verify && !Number(verify) && user.value?.accountId !== '1') {\r\n          if (openConfig.value?.forbidWeakPassword === 'true') {\r\n            nextTick(() => {\r\n              verifyEditPassWord.value = 'yes'\r\n              verifyEditPassWordShow.value = true\r\n            })\r\n          } else {\r\n            nextTick(() => {\r\n              verifyEditPassWord.value = 'no'\r\n              editPassWordShow.value = true\r\n            })\r\n          }\r\n        }\r\n      }\r\n    },\r\n    { immediate: true }\r\n  )\r\n  // 菜单过滤\r\n  const filterMenu = (menuList) => {\r\n    let newMenuList = []\r\n    for (let i = 0, len = menuList.length; i < len; i++) {\r\n      newMenuList.push({\r\n        id: menuList[i].menuId,\r\n        name: menuList[i].name,\r\n        routePath: menuList[i].routePath,\r\n        menuFunction: menuList[i].menuFunction,\r\n        menuRouteType: menuList[i].menuRouteType,\r\n        icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,\r\n        has: menuList[i].permissions,\r\n        children: filterMenu(menuList[i].children || [])\r\n      })\r\n    }\r\n    return newMenuList\r\n  }\r\n  // 顶部菜单id\r\n  const tabMenu = ref('')\r\n  // 顶部菜单data\r\n  const tabMenuData = computed(() => filterMenu(store.getters.getMenuFn || []))\r\n  // 是否显示左侧菜单\r\n  const isView = ref(false)\r\n  // 工作台跳转具体应用\r\n  const isChildView = ref(false)\r\n  // 具体打开页面id\r\n  const isOpen = ref(false)\r\n  // 具体打开页面id\r\n  const openPageId = ref('')\r\n  const openPageObj = ref({})\r\n  // 打开页面工作台应用id\r\n  const openPageChildId = ref('')\r\n  // 工作台对象\r\n  const WorkBenchObj = ref({})\r\n  // 工作台子应用data\r\n  const WorkBenchList = ref([])\r\n  // 具体工作台子应用对象\r\n  const childData = ref({})\r\n  watch(\r\n    () => tabMenuData.value,\r\n    (val) => {\r\n      isView.value = false\r\n      isChildView.value = false\r\n      WorkBenchObj.value = {}\r\n      WorkBenchList.value = []\r\n      childData.value = {}\r\n      if (tabMenuData.value.length) {\r\n        const query = JSON.parse(sessionStorage.getItem('query')) || {}\r\n        if (query.openPageValue) {\r\n          openPage({ key: query.openPageKey || 'id', value: query.openPageValue })\r\n        } else {\r\n          nextTick(() => {\r\n            tabMenu.value = tabMenuData.value[0]?.id\r\n            handleClick()\r\n          })\r\n        }\r\n      }\r\n    },\r\n    { immediate: true }\r\n  )\r\n  // 顶部菜单切换事件\r\n  const handleClick = () => {\r\n    if (process.env.NODE_ENV !== 'development') {\r\n      const detection_version = openConfig.value.DetectionVersion || ''\r\n      if (detection_version !== 'true') handleCompareVersion()\r\n    }\r\n    menuId.value = ''\r\n    menuData.value = []\r\n    tabData.value = []\r\n    isTabData.value = []\r\n    keepAliveRoute.value = []\r\n    nextTick(() => {\r\n      for (let i = 0, len = tabMenuData.value.length; i < len; i++) {\r\n        const item = tabMenuData.value[i]\r\n        if (tabMenu.value === item.id) {\r\n          sessionStorage.setItem('has', JSON.stringify(item))\r\n          if (item.routePath === '/WorkBench') {\r\n            // 切换到工作台\r\n            isView.value = false\r\n            router.push({ path: item.routePath, query: routePathData(item.routePath) })\r\n            WorkBenchObj.value = item\r\n            WorkBenchList.value = item.children\r\n            nextTick(() => {\r\n              if (openPageChildId.value) {\r\n                if (openPageChildId.value === openPageId.value) {\r\n                  openPageId.value = ''\r\n                  openPageObj.value = {}\r\n                }\r\n                for (let r = 0, length = item.children.length; r < length; r++) {\r\n                  if (item.children[r].id === openPageChildId.value) {\r\n                    leftMenuData(item.children[r])\r\n                  }\r\n                }\r\n              }\r\n            })\r\n          } else {\r\n            if (item.routePath.includes('/GlobalHome')) {\r\n              if (openPageId.value) {\r\n                WorkBenchObj.value = item\r\n                leftMenuData(item, false)\r\n              } else {\r\n                isView.value = false\r\n                router.push({ path: item.routePath, query: routePathData(item.routePath) })\r\n              }\r\n              return\r\n            }\r\n            // 不是工作台页面判断是否有子级菜单\r\n            if (item.children && item.children.length) {\r\n              // 有子级菜单按照左侧菜单显示\r\n              leftMenuData(item, true)\r\n            } else {\r\n              if (['3', '4'].includes(item.menuRouteType.value)) {\r\n                isView.value = false\r\n                isChildView.value = false\r\n                router.push({\r\n                  path: item.routePath,\r\n                  query: { ...routePathData(item.routePath), menuRouteType: item.menuRouteType.value }\r\n                })\r\n              } else {\r\n                // const menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath\r\n                // if (routePath(menuUrl) === '/') {\r\n                //   isView.value = false\r\n                //   const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath) }\r\n                //   router.push({ path: item.routePath, query: query })\r\n                //   const mainAppName = [mainRoutePath(item.routePath)]\r\n                //   keepAliveRoute.value = mainAppName\r\n                // } else {\r\n                leftMenuData(item, true)\r\n                // }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n  const WorkBenchMenu = (tabMenuId, tabMenuChildren) => {\r\n    tabMenu.value = tabMenuId\r\n    menuId.value = ''\r\n    menuData.value = []\r\n    tabData.value = []\r\n    isTabData.value = []\r\n    keepAliveRoute.value = []\r\n    leftMenuData(tabMenuChildren)\r\n  }\r\n  const leftMenuData = (item, type) => {\r\n    // 显示左侧菜单方法\r\n    // 不是工作台页面判断是否有子级菜单\r\n    if (item.children && item.children.length) {\r\n      // 有子级菜单按照左侧菜单显示\r\n      if (type) {\r\n        isView.value = true\r\n        isChildView.value = false\r\n      } else {\r\n        isView.value = true\r\n        isChildView.value = true\r\n        childData.value = item\r\n      }\r\n      menuData.value = item.children\r\n      const obj = openPageId.value ? menuOpenPage(item.children) : menuDefault(item.children)\r\n      menuId.value = obj.id\r\n      openPageId.value = ''\r\n      openPageChildId.value = ''\r\n      menuClick(obj)\r\n    } else {\r\n      if (['3', '4'].includes(item.menuRouteType.value)) {\r\n        isView.value = false\r\n        isChildView.value = true\r\n        childData.value = item\r\n        router.push({\r\n          path: item.routePath,\r\n          query: { ...routePathData(item.routePath), menuRouteType: item.menuRouteType.value }\r\n        })\r\n      } else {\r\n        if (type) {\r\n          isView.value = false\r\n          isChildView.value = false\r\n        } else {\r\n          isView.value = false\r\n          isChildView.value = true\r\n          childData.value = item\r\n        }\r\n        menuData.value = [item]\r\n        const obj = openPageId.value ? menuOpenPage([item]) : menuDefault([item])\r\n        menuId.value = obj.id\r\n        openPageId.value = ''\r\n        openPageChildId.value = ''\r\n        menuClick(obj)\r\n      }\r\n    }\r\n  }\r\n\r\n  const menuDefault = (data) => {\r\n    // 获取左侧菜单第一个菜单\r\n    var defaultObj = {}\r\n    for (let i = 0, len = data.length; i < len; i++) {\r\n      if (i === 0) {\r\n        if (data[i].children.length === 0) {\r\n          defaultObj = data[i]\r\n        } else {\r\n          defaultObj = menuDefault(data[i].children)\r\n        }\r\n      }\r\n    }\r\n    return defaultObj\r\n  }\r\n  const menuOpenPage = (data) => {\r\n    // 获取左侧菜单第一个菜单\r\n    var defaultObj = {}\r\n    for (let i = 0, len = data.length; i < len; i++) {\r\n      if (openPageId.value === data[i].id) {\r\n        defaultObj = data[i]\r\n      }\r\n      if (data[i].children.length) {\r\n        const obj = menuOpenPage(data[i].children)\r\n        defaultObj = obj.id ? obj : defaultObj\r\n      }\r\n    }\r\n    return defaultObj\r\n  }\r\n  const menuId = ref('')\r\n  const menuData = ref([])\r\n  const menuClick = (item) => {\r\n    // 左侧菜单点击事件\r\n    if (!tabData.value.length) {\r\n      qiankunActions.setGlobalState({ keepAliveRoute: [] })\r\n    }\r\n    if (!tabData.value.map((v) => v.id).includes(item.id)) {\r\n      tabData.value.push(item)\r\n    }\r\n    tabClick()\r\n  }\r\n  const WorkBenchReturn = () => {\r\n    // 工作台具体应用返回工作台\r\n    if (isChildView.value) {\r\n      isView.value = false\r\n      isChildView.value = false\r\n      handleClick()\r\n    }\r\n  }\r\n  const handleBreadcrumb = (item, index) => {\r\n    if (index + 1 === tabData.value.length) return\r\n    const newTabData = tabData.value.slice(0, index + 1)\r\n    const delTabData = tabData.value.slice(index + 1).map((v) => v.id)\r\n    tabData.value = newTabData\r\n    isTabData.value = isTabData.value.filter((item) => !delTabData.includes(item.id))\r\n    const mainAppList = tabData.value.filter(\r\n      (v) => routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/'\r\n    )\r\n    const mainAppName = Array.from(new Set(mainAppList.map((v) => mainRoutePath(v.routePath))))\r\n    keepAliveRoute.value = mainAppName\r\n    menuId.value = item.id\r\n    tabClick()\r\n  }\r\n\r\n  const tabData = ref([]) // tab数据\r\n  const isTabData = ref([])\r\n  const isMicroApp = ref('')\r\n  const noMicroApp = ref([])\r\n  const keepAliveRoute = ref([])\r\n  const add_msg = (a, b) => a.filter((v) => b.indexOf(v) === -1)\r\n  // const delete_msg = (a, b) => b.filter(v => a.indexOf(v) === -1)\r\n  const tabClick = () => {\r\n    const microAppName = Object.keys(config.microApp)\r\n    const MicroAppData = Array.from(new Set(tabData.value.map((v) => routePath(v.routePath)))).filter((v) =>\r\n      microAppName.includes(v)\r\n    )\r\n    const addMicroApp = add_msg(MicroAppData, MicroApp.value)\r\n    // const delMicroApp = delete_msg(MicroAppData, MicroApp.value)\r\n    MicroApp.value = [...MicroApp.value, ...addMicroApp]\r\n    if (!addMicroApp.length) {\r\n      menuRouterPush()\r\n      return\r\n    }\r\n    nextTick(() => {\r\n      for (let i = 0, len = addMicroApp.length; i < len; i++) {\r\n        const v = addMicroApp[i]\r\n        if (!MicroAppObj.value[v]) {\r\n          MicroAppObj.value[v] = loadFilterApp(v)\r\n          MicroAppObj.value[v].loadPromise\r\n            .then(() => {\r\n              MicroAppObj.value[v].mountPromise.then(() => {\r\n                qiankunActions.setGlobalState({\r\n                  theme: store.getters.getThemeFn,\r\n                  user: store.getters.getUserFn,\r\n                  menu: store.getters.getMenuFn,\r\n                  area: store.getters.getAreaFn,\r\n                  role: store.getters.getRoleFn,\r\n                  readConfig: store.getters.getReadConfig,\r\n                  readOpenConfig: store.getters.getReadOpenConfig\r\n                })\r\n              })\r\n            })\r\n            .catch((err) => {\r\n              noMicroApp.value.push(v)\r\n            })\r\n        }\r\n      }\r\n      setTimeout(() => {\r\n        menuRouterPush()\r\n      }, 52)\r\n    })\r\n  }\r\n  const menuRouterPush = () => {\r\n    for (let i = 0, len = tabData.value.length; i < len; i++) {\r\n      const item = tabData.value[i]\r\n      if (menuId.value === item.id) {\r\n        sessionStorage.setItem('has', JSON.stringify(item))\r\n        const menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath\r\n        const getMicroName = routePath(menuUrl)\r\n        isMicroApp.value = getMicroName\r\n        if (MicroApp.value.includes(getMicroName) && !noMicroApp.value.includes(getMicroName)) {\r\n          const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath), ...item.query }\r\n          router.push({ path: item.routePath, query: query })\r\n          MicroAppObj.value[getMicroName].mountPromise.then(() => {\r\n            qiankunActions.setGlobalState({ keepAliveRoute: tabData.value.map((v) => v.routePath) })\r\n          })\r\n        } else {\r\n          if (getMicroName === '/') {\r\n            const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath), ...item.query }\r\n            router.push({ path: item.routePath, query: query })\r\n            const mainAppList = tabData.value.filter(\r\n              (v) => routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/'\r\n            )\r\n            const mainAppName = Array.from(new Set(mainAppList.map((v) => mainRoutePath(v.routePath))))\r\n            keepAliveRoute.value = mainAppName\r\n          } else {\r\n            router.push({ path: '/NotFoundPage' })\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  const mainRoutePath = (url) => {\r\n    let path = ''\r\n    const start = url.indexOf('/') + 1\r\n    const end = url.indexOf('?')\r\n    if (end === -1) {\r\n      path = url.substring(1)\r\n    } else {\r\n      path = url.substring(start, end)\r\n    }\r\n    return path\r\n  }\r\n  const routePathData = (href) => {\r\n    let params = {}\r\n    href = href.substring(href.indexOf('?') + 1)\r\n    let arr = href.split('&')\r\n    arr.forEach((item) => {\r\n      let a = item.split('=')\r\n      params[a[0]] = a[1]\r\n    })\r\n    return params\r\n  }\r\n  // 获取第一个斜杠和第二个斜杠之间的内容\r\n  const routePath = (url) => {\r\n    let path = '' // 第二个斜杠前内容\r\n    const first = url.indexOf('/') + 1 // 从第一个斜杠算起（+1表示不包括该斜杠）\r\n    const kong = url.indexOf(' ', first) // 第一个斜杠后的第一个空格\r\n    const heng = url.indexOf('/', first) // 第一个斜杠后的第一个斜杠（即第二个斜杠）\r\n    if (heng === -1) {\r\n      path = url.substring(1, kong)\r\n    } else {\r\n      path = url.substring(1, heng)\r\n    }\r\n    return path\r\n  }\r\n  watch(\r\n    () => store.state.openRoute,\r\n    (val) => {\r\n      if (val.path) {\r\n        openRoute(val)\r\n      }\r\n    }\r\n  )\r\n  watch(\r\n    () => store.state.closeOpenRoute,\r\n    (val) => {\r\n      if (val.closeId) {\r\n        delRoute(val)\r\n      }\r\n    }\r\n  )\r\n\r\n  const openRoute = (val) => {\r\n    if (isTabData.value.map((v) => v.isData).includes(JSON.stringify(val))) {\r\n      for (let i = 0, len = isTabData.value.length; i < len; i++) {\r\n        const item = isTabData.value[i]\r\n        if (item.isData === JSON.stringify(val)) {\r\n          menuId.value = item.id\r\n          tabClick()\r\n        }\r\n      }\r\n    } else {\r\n      const id = guid()\r\n      isTabData.value.push({ id: id, isData: JSON.stringify(val) })\r\n      tabData.value.push({\r\n        id,\r\n        name: val.name,\r\n        routePath: val.path,\r\n        query: { ...val.query, routeId: id, oldRouteId: menuId.value }\r\n      })\r\n      menuId.value = id\r\n      tabClick()\r\n    }\r\n    qiankunActions.setGlobalState({ openRoute: { name: '', path: '', query: {} } })\r\n  }\r\n  const delRoute = (val) => {\r\n    if (val.openId) {\r\n      isTabData.value = isTabData.value.filter((item) => item.id !== val.closeId)\r\n      tabData.value = tabData.value.filter((item) => item.id !== val.closeId)\r\n      menuId.value = val.openId\r\n      tabClick()\r\n    } else {\r\n      handleClose(val.closeId)\r\n    }\r\n    qiankunActions.setGlobalState({ closeOpenRoute: { openId: '', closeId: '' } })\r\n  }\r\n  const isRefresh = ref(true)\r\n  const handleRefresh = (id) => {\r\n    if (route.meta.moduleName === 'main') {\r\n      keepAliveRoute.value = keepAliveRoute.value.filter((v) => v !== route.name)\r\n      isRefresh.value = false\r\n      setTimeout(() => {\r\n        keepAliveRoute.value.push(route.name)\r\n        isRefresh.value = true\r\n      }, 200)\r\n    } else {\r\n      for (let i = 0, len = tabData.value.length; i < len; i++) {\r\n        const item = tabData.value[i]\r\n        if (item.id === id) {\r\n          qiankunActions.setGlobalState({ refreshRoute: item.routePath })\r\n          setTimeout(() => {\r\n            qiankunActions.setGlobalState({ refreshRoute: '' })\r\n          }, 222)\r\n        }\r\n      }\r\n    }\r\n  }\r\n  const handleClose = (id) => {\r\n    if (menuId.value === id) {\r\n      for (let i = 0, len = tabData.value.length; i < len; i++) {\r\n        const item = tabData.value[i]\r\n        if (item.id === id) {\r\n          menuId.value = tabData.value[i ? i - 1 : 1].id\r\n          tabClick()\r\n        }\r\n      }\r\n    }\r\n    isTabData.value = isTabData.value.filter((item) => item.id !== id)\r\n    tabData.value = tabData.value.filter((item) => item.id !== id)\r\n  }\r\n  const handleCloseOther = (id) => {\r\n    isTabData.value = isTabData.value.filter((item) => item.id === id)\r\n    tabData.value = tabData.value.filter((item) => item.id === id)\r\n    menuId.value = id\r\n    tabClick()\r\n  }\r\n\r\n  const openPageMenu = (nodes, key, value) => {\r\n    if (!nodes || !nodes.length) return [] // eslint-disable-line\r\n    const children = []\r\n    for (let node of nodes) {\r\n      if (node[key] === value && !openPageId.value) {\r\n        openPageId.value = node.id\r\n        openPageObj.value = node\r\n      }\r\n      node = Object.assign({}, node)\r\n      const sub = openPageMenu(node.children, key, value)\r\n      if ((sub && sub.length) || node[key] === value) {\r\n        sub.length && (node.children = sub)\r\n        children.push(node)\r\n      }\r\n    }\r\n    return children.length ? children : [] // eslint-disable-line\r\n  }\r\n  const openPage = ({ key = 'id', value }) => {\r\n    if (isOpen.value) return\r\n    isOpen.value = true\r\n    const openPagedata = openPageMenu(tabMenuData.value, key, value)[0] || {}\r\n    if (openPagedata.id) {\r\n      if (tabMenu.value === openPagedata.id) {\r\n        if (openPagedata.routePath === '/WorkBench') {\r\n          if (openPagedata.id === openPageId.value) {\r\n            openPageId.value = ''\r\n            openPageObj.value = {}\r\n            WorkBenchReturn()\r\n            return\r\n          }\r\n          if (openPagedata.children[0]?.id === childData.value.id) {\r\n            menuId.value = openPageId.value\r\n            menuClick(openPageObj.value)\r\n            openPageId.value = ''\r\n            openPageObj.value = {}\r\n            nextTick(() => {\r\n              if (isOpen.value) {\r\n                isOpen.value = false\r\n              }\r\n            })\r\n          } else {\r\n            WorkBenchReturn()\r\n            setTimeout(() => {\r\n              nextTick(() => {\r\n                openPageChildId.value = openPagedata.children[0]?.id\r\n                handleClick()\r\n                nextTick(() => {\r\n                  if (isOpen.value) {\r\n                    isOpen.value = false\r\n                  }\r\n                })\r\n              })\r\n            }, 200)\r\n          }\r\n        } else {\r\n          if (openPagedata.routePath.includes('/GlobalHome')) {\r\n            handleClick()\r\n            nextTick(() => {\r\n              if (isOpen.value) {\r\n                isOpen.value = false\r\n              }\r\n            })\r\n            return\r\n          }\r\n          menuId.value = openPageId.value\r\n          menuClick(openPageObj.value)\r\n          openPageId.value = ''\r\n          openPageObj.value = {}\r\n          nextTick(() => {\r\n            if (isOpen.value) {\r\n              isOpen.value = false\r\n            }\r\n          })\r\n        }\r\n      } else {\r\n        if (isChildView.value) {\r\n          WorkBenchReturn()\r\n          setTimeout(() => {\r\n            nextTick(() => {\r\n              tabMenu.value = openPagedata.id\r\n              if (openPagedata.id === openPageId.value) {\r\n                openPageId.value = ''\r\n                openPageObj.value = {}\r\n              } else {\r\n                openPageChildId.value = openPagedata.children[0]?.id\r\n              }\r\n              handleClick()\r\n              nextTick(() => {\r\n                if (isOpen.value) {\r\n                  isOpen.value = false\r\n                }\r\n              })\r\n            })\r\n          }, 200)\r\n        } else {\r\n          tabMenu.value = openPagedata.id\r\n          if (openPagedata.id === openPageId.value) {\r\n            openPageId.value = ''\r\n            openPageObj.value = {}\r\n          } else {\r\n            openPageChildId.value = openPagedata.children[0]?.id\r\n          }\r\n          handleClick()\r\n          nextTick(() => {\r\n            if (isOpen.value) {\r\n              isOpen.value = false\r\n            }\r\n          })\r\n        }\r\n      }\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '未检测到你有此菜单！' })\r\n    }\r\n  }\r\n  const setOpenPageId = (id) => {\r\n    openPageId.value = id\r\n  }\r\n\r\n  const guid = () => {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n      var r = (Math.random() * 16) | 0,\r\n        v = c == 'x' ? r : (r & 0x3) | 0x8\r\n      return v.toString(16)\r\n    })\r\n  }\r\n\r\n  provide('delRoute', delRoute)\r\n  provide('WorkBenchList', WorkBenchList)\r\n  provide('leftMenuData', leftMenuData)\r\n  provide('WorkBenchMenu', WorkBenchMenu)\r\n  provide('setOpenPageId', setOpenPageId)\r\n  provide('openPage', openPage)\r\n  provide('openRoute', openRoute)\r\n  // 组织选择相关\r\n  const isOrganizationSelectShow = ref(false)\r\n  const currentOrganization = ref(null)\r\n  const organizationList = ref([])\r\n\r\n  // 获取用户可访问的组织列表\r\n  const getOrganizationList = async () => {\r\n    try {\r\n      const { data } = await api.userOrganizationList()\r\n      organizationList.value = data || []\r\n\r\n      // 设置默认组织\r\n      if (organizationList.value.length > 0) {\r\n        const savedOrgId = sessionStorage.getItem('GlobalOrganizationId')\r\n        const savedOrgData = sessionStorage.getItem('OrganizationRow')\r\n\r\n        if (savedOrgId && savedOrgData) {\r\n          const savedOrg = JSON.parse(savedOrgData)\r\n          currentOrganization.value = savedOrg\r\n        } else {\r\n          // 如果没有保存的组织信息，使用第一个组织\r\n          const defaultOrg = organizationList.value[0]\r\n          currentOrganization.value = defaultOrg\r\n          const defaultOrgId = defaultOrg.id || defaultOrg.orgId\r\n          sessionStorage.setItem('GlobalOrganizationId', defaultOrgId)\r\n          sessionStorage.setItem('OrganizationRow', JSON.stringify(defaultOrg))\r\n        }\r\n      } else {\r\n        console.warn('组织列表为空')\r\n      }\r\n    } catch (error) {\r\n      console.error('获取组织列表失败:', error)\r\n    }\r\n  }\r\n\r\n  // 处理组织选择\r\n  const handleOrganizationChange = async (item) => {\r\n    // 获取组织ID，兼容不同的字段名\r\n    const currentOrgId = currentOrganization.value?.id || currentOrganization.value?.orgId\r\n    const newOrgId = item.id || item.orgId\r\n\r\n    if (currentOrgId === newOrgId) {\r\n      console.log('组织未变化，跳过切换')\r\n      return\r\n    }\r\n\r\n    try {\r\n      // 获取组织ID，兼容不同的字段名\r\n      const orgId = item.id || item.orgId\r\n      // 调用设置用户当前组织接口\r\n      const { code } = await api.setUserOrganization({ organizationId: orgId })\r\n\r\n      if (code === 200) {\r\n        currentOrganization.value = item\r\n        sessionStorage.setItem('OrganizationRow', JSON.stringify(item))\r\n        sessionStorage.setItem('GlobalOrganizationId', orgId)\r\n\r\n        // 更新Vuex状态\r\n        store.commit('setGlobalOrganizationId', orgId)\r\n        store.commit('setGlobalOrganizationData', item)\r\n\r\n        ElMessage({ type: 'success', message: `已切换到组织：${item.orgName}` })\r\n\r\n        // 清除当前菜单和标签页状态\r\n        tabMenu.value = ''\r\n        tabMenuData.value = []\r\n        menuData.value = []\r\n        tabData.value = []\r\n        isTabData.value = []\r\n        keepAliveRoute.value = []\r\n        // 重新获取用户信息和权限\r\n        await store.dispatch('loginUser', 'login')\r\n\r\n        // 刷新相关组件\r\n        store.commit('setBoxMessageRefresh', true)\r\n        store.commit('setPersonalDoRefresh', true)\r\n        // 重新获取组织列表（以防权限变化）\r\n        await getOrganizationList()\r\n        // 重新获取系统配置\r\n        globalReadOpenConfig()\r\n\r\n        ElMessage({ type: 'success', message: '组织切换成功，页面已刷新' })\r\n      } else {\r\n        ElMessage({ type: 'error', message: '切换组织失败' })\r\n      }\r\n    } catch (error) {\r\n      ElMessage({ type: 'error', message: '切换组织失败，请重试' })\r\n    }\r\n  }\r\n\r\n  const organizationSelect = (item) => {\r\n    isOrganizationSelectShow.value = false\r\n    sessionStorage.setItem(\"OrganizationRow\", JSON.stringify(item))\r\n    sessionStorage.setItem(\"GlobalOrganizationId\", item.id)\r\n    // 更新Vuex状态\r\n    store.commit(\"setGlobalOrganizationId\", item.id)\r\n    store.commit(\"setGlobalOrganizationData\", item)\r\n    ElMessage({ type: \"success\", message: `已切换到组织：${item.name}` })\r\n  }\r\n\r\n  // 处理组织选择显示\r\n  const handleOrganizationSelect = () => {\r\n    const oldOrganizationInfo = sessionStorage.getItem(\"oldOrganizationInfo\") || \"\"\r\n    const isOrganizationSelect = sessionStorage.getItem(\"isOrganizationSelect\") || \"\"\r\n    if (user.value?.accountId !== \"1\" && user.value?.organizationTotal > 1 && oldOrganizationInfo && !isOrganizationSelect) {\r\n      isOrganizationSelectShow.value = true\r\n    }\r\n  }\r\n  return {\r\n    user,\r\n    area,\r\n    role,\r\n    left,\r\n    width,\r\n    LayoutViewBox,\r\n    LayoutViewInfo,\r\n    helpShow,\r\n    handleCommand,\r\n    suggestPopShow,\r\n    editPassWordShow,\r\n    verifyEditPassWord,\r\n    verifyEditPassWordShow,\r\n    editPassWordCallback,\r\n    regionId,\r\n    regionName,\r\n    regionSelect,\r\n    isRegionSelectShow,\r\n    organizationSelect,\r\n    isOrganizationSelectShow,\r\n    currentOrganization,\r\n    organizationList,\r\n    handleOrganizationChange,\r\n    isView,\r\n    isChildView,\r\n    tabMenu,\r\n    tabMenuData,\r\n    handleClick,\r\n    menuId,\r\n    menuData,\r\n    menuClick,\r\n    handleBreadcrumb,\r\n    WorkBenchObj,\r\n    WorkBenchList,\r\n    childData,\r\n    WorkBenchReturn,\r\n    isRefresh,\r\n    keepAliveRoute,\r\n    tabData,\r\n    tabClick,\r\n    handleRefresh,\r\n    handleClose,\r\n    handleCloseOther,\r\n    isMicroApp,\r\n    MicroApp\r\n  }\r\n}\r\nexport const qiankun = (route) => {\r\n  const isMain = ref(false)\r\n  const isMainPage = () => {\r\n    isMain.value = route.meta.moduleName === 'main'\r\n  }\r\n\r\n  watch(\r\n    () => route,\r\n    () => {\r\n      isMainPage()\r\n    },\r\n    { deep: true }\r\n  )\r\n  onMounted(() => {\r\n    isMainPage(route)\r\n  })\r\n  return { isMain }\r\n}\r\n\r\nexport const ChatMethod = () => {\r\n  const store = useStore()\r\n  const rongCloudToken = computed(() => store.getters.getRongCloudToken)\r\n  return { rongCloudToken }\r\n}\r\n\r\nexport const AiChatMethod = () => {\r\n  const store = useStore()\r\n  const AiChatWidth = computed(() => store.state.AiChatWidth)\r\n  const AiChatTargetWidth = computed(() => `${AiChatWidth.value}px`)\r\n  const AiChatViewType = ref(false)\r\n  const AiChatWindowShow = ref(false)\r\n  // 自动吸附到最近的侧边\r\n  const handleResizeFloatingWindow = () => {\r\n    if (window.innerWidth > 1280 + 400) {\r\n      const width = window.innerWidth - 1280 > 520 ? 520 : 400\r\n      store.commit('setAiChatWidth', width)\r\n      if (!AiChatViewType.value) AiChatWindowShow.value = false\r\n      AiChatViewType.value = true\r\n    } else {\r\n      store.commit('setAiChatWidth', 400)\r\n      if (AiChatViewType.value) AiChatWindowShow.value = false\r\n      AiChatViewType.value = false\r\n    }\r\n  }\r\n  onMounted(() => {\r\n    handleResizeFloatingWindow()\r\n    window.addEventListener('resize', handleResizeFloatingWindow)\r\n  })\r\n  onUnmounted(() => {\r\n    window.removeEventListener('resize', handleResizeFloatingWindow)\r\n  })\r\n  return { AiChatTargetWidth, AiChatViewType, AiChatWindowShow }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,SAASE,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,KAAK;AACrF,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,MAAM;AAC/B,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,cAAc,EAAEC,aAAa,QAAQ,WAAW;AACzD,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,eAAe,EAAEC,kBAAkB,QAAQ,iBAAiB;AACrE,OAAOC,0BAA0B,MAAM,yBAAyB;AAChE,OAAOC,YAAY,MAAM,gCAAgC;AACzD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,OAAO,IAAMC,WAAW,GAAG,03BAA03B;AACr5B,IAAMC,cAAc,GAAGnB,QAAQ,CAAC;EAAA,IAAAoB,iBAAA;EAAA,OAAM,EAAAA,iBAAA,GAAAH,UAAU,CAACxH,KAAK,cAAA2H,iBAAA,uBAAhBA,iBAAA,CAAkBD,cAAc,KAAI,EAAE;AAAA,EAAC;AAC7E,OAAO,IAAME,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,KAAK,EAAEC,MAAM,EAAK;EACtD,IAAMC,KAAK,GAAGnB,QAAQ,CAAC,CAAC;EACxB,IAAMoB,QAAQ,GAAG,GAAGnB,MAAM,CAACoB,OAAO,wBAAwB;EAE1D,IAAMC,GAAG,GAAGd,0BAA0B,CAAC,CAAC;EACxC,IAAMe,IAAI,GAAG/B,GAAG,CAAC,CAAC,CAAC;EACnB,IAAMgC,KAAK,GAAGhC,GAAG,CAAC,EAAE,CAAC;EACrB,IAAMiC,aAAa,GAAGjC,GAAG,CAAC,IAAI,CAAC;EAC/B,IAAMkC,cAAc,GAAGlC,GAAG,CAAC,IAAI,CAAC;EAChC,IAAMmC,gBAAgB,GAAGnC,GAAG,CAAC,KAAK,CAAC;EACnC,IAAMoC,kBAAkB,GAAGpC,GAAG,CAAC,EAAE,CAAC;EAClC,IAAMqC,sBAAsB,GAAGrC,GAAG,CAAC,KAAK,CAAC;EACzC,IAAMsC,QAAQ,GAAGtC,GAAG,CAAC,EAAE,CAAC;EACxB,IAAMuC,WAAW,GAAGvC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,IAAMwC,IAAI,GAAGrC,QAAQ,CAAC;IAAA,OAAMwB,KAAK,CAACc,OAAO,CAACC,SAAS;EAAA,EAAC;EACpD;EACA,IAAMC,IAAI,GAAGxC,QAAQ,CAAC;IAAA,OAAMwB,KAAK,CAACc,OAAO,CAACG,SAAS;EAAA,EAAC;EACpD;EACA,IAAMC,IAAI,GAAG1C,QAAQ,CAAC;IAAA,OAAMwB,KAAK,CAACc,OAAO,CAACK,SAAS;EAAA,EAAC;EACpD,IAAM1B,UAAU,GAAGjB,QAAQ,CAAC;IAAA,OAAMwB,KAAK,CAACc,OAAO,CAACM,iBAAiB;EAAA,EAAC;EAClE,IAAMC,QAAQ,GAAGhD,GAAG,CAAC,KAAK,CAAC;EAC3B,IAAMiD,cAAc,GAAGjD,GAAG,CAAC,KAAK,CAAC;EACjC,IAAMkD,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAEC,IAAI,EAAK;IACpC,IAAMC,GAAG,GAAG,IAAIC,GAAG,CAACH,IAAI,CAAC;IACzB,OAAOC,IAAI,CAACG,IAAI,CAAC,UAAAC,IAAI;MAAA,OAAIH,GAAG,CAACI,GAAG,CAACD,IAAI,CAAC;IAAA,EAAC;EACzC,CAAC;EACDtD,SAAS,CAAC,YAAM;IACd,IAAMwD,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACJ,eAAe,IAAI,EAAE;IACxF,IAAMK,MAAM,GAAGzC,cAAc,CAAC1H,KAAK,KAAK,OAAO,GAAG,CAAC,oBAAoB,EAAE,wBAAwB,EAAE,cAAc,CAAC,GAAG,CAAC,uBAAuB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,YAAY,CAAC;IAC5MqJ,cAAc,CAACrJ,KAAK,GAAGsJ,aAAa,CAACQ,eAAe,EAAEK,MAAM,CAAC;IAC7DC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEhB,cAAc,CAACrJ,KAAK,CAAC;IAC7EsK,kBAAkB,CAAC,CAAC;IACpBC,wBAAwB,CAAC,CAAC;IAC1BC,mBAAmB,CAAC,CAAC;IACrBhE,QAAQ,CAAC,YAAM;MACb0B,GAAG,CAACuC,QAAQ,CAACpC,aAAa,CAACrI,KAAK,EAAE,UAAC0K,OAAO,EAAK;QAC7CvC,IAAI,CAACnI,KAAK,GAAG0K,OAAO,CAACC,WAAW;MAClC,CAAC,CAAC;MACFzC,GAAG,CAACuC,QAAQ,CAACnC,cAAc,CAACtI,KAAK,EAAE,UAAC0K,OAAO,EAAK;QAC9CtC,KAAK,CAACpI,KAAK,GAAG,sBAAsB0K,OAAO,CAACC,WAAW,GAAG,EAAE,MAAM;MACpE,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAML,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;IAAA,IAAAM,WAAA,EAAAC,YAAA;IAC/B,IAAMC,aAAa,GAAGb,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE;IACnE,IAAMa,cAAc,GAAGd,cAAc,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE;IACrE,IAAI,EAAAU,WAAA,GAAAhC,IAAI,CAAC5I,KAAK,cAAA4K,WAAA,uBAAVA,WAAA,CAAYI,SAAS,MAAK,GAAG,IAAI,EAAAH,YAAA,GAAAjC,IAAI,CAAC5I,KAAK,cAAA6K,YAAA,uBAAVA,YAAA,CAAYI,SAAS,IAAG,CAAC,IAAIH,aAAa,IAAI,CAACC,cAAc,EAAE;MAClGG,kBAAkB,CAAClL,KAAK,GAAG,IAAI;IACjC;EACF,CAAC;EACD,IAAMmL,aAAa,GAAG,SAAhBA,aAAaA,CAAIhK,IAAI,EAAK;IAC9B,IAAIA,IAAI,KAAK,MAAM,EAAE;MACnB4G,KAAK,CAACqD,MAAM,CAAC,4BAA4B,EAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC5D,CAAC,MAAM,IAAIlK,IAAI,KAAK,SAAS,EAAE;MAC7B;MACA;MACAmK,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG3E,MAAM,CAAC4E,QAAQ,MAAM,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE;IACvE,CAAC,MAAM,IAAIxK,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI+F,eAAe,CAAC,CAAC,KAAK,IAAI,EAAE,OAAOC,kBAAkB,CAAC,IAAI,CAAC;MAC/D,IAAID,eAAe,CAAC,CAAC,KAAK,IAAI,EAAE,OAAOC,kBAAkB,CAAC,IAAI,CAAC;IACjE,CAAC,MAAM,IAAIhG,IAAI,KAAK,MAAM,EAAE;MAC1BiI,QAAQ,CAACpJ,KAAK,GAAG,IAAI;IACvB,CAAC,MAAM,IAAImB,IAAI,KAAK,eAAe,EAAE;MACnCqH,kBAAkB,CAACxI,KAAK,GAAG,EAAE;MAC7BuI,gBAAgB,CAACvI,KAAK,GAAG,IAAI;IAC/B,CAAC,MAAM,IAAImB,IAAI,KAAK,MAAM,EAAE;MAC1ByK,UAAU,CAAC,CAAC;IACd,CAAC,MAAM;MACLtE,SAAS,CAAC;QAAEnG,IAAI,EAAE,MAAM;QAAE0K,OAAO,EAAE;MAAS,CAAC,CAAC;IAChD;EACF,CAAC;EACD,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAI3K,IAAI,EAAK;IACrC,IAAIA,IAAI,EAAE;MACR4K,QAAQ,CAAC,aAAa,CAAC;IACzB;IACAxD,gBAAgB,CAACvI,KAAK,GAAG,KAAK;EAChC,CAAC;EACD,IAAM4L,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvBrE,YAAY,CAACyE,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;MAC9CC,iBAAiB,EAAE,IAAI;MACvBC,gBAAgB,EAAE,IAAI;MACtB/K,IAAI,EAAE;IACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;MACVqJ,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC,CACDpG,KAAK,CAAC,YAAM;MACX2B,SAAS,CAAC;QAAEnG,IAAI,EAAE,MAAM;QAAE0K,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC/C,CAAC,CAAC;EACN,CAAC;EACD,IAAME,QAAQ;IAAA,IAAAI,IAAA,GAAApG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA0H,QAAOC,IAAI;MAAA,IAAAC,mBAAA,EAAAC,IAAA,EAAAC,sBAAA,EAAAC,uBAAA;MAAA,OAAAnN,mBAAA,GAAAuB,IAAA,UAAA6L,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAxH,IAAA,GAAAwH,QAAA,CAAAnJ,IAAA;UAAA;YAAAmJ,QAAA,CAAAnJ,IAAA;YAAA,OACHmD,GAAG,CAACoF,QAAQ,CAAC,CAAC;UAAA;YAAAO,mBAAA,GAAAK,QAAA,CAAA1J,IAAA;YAA7BsJ,IAAI,GAAAD,mBAAA,CAAJC,IAAI;YACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;cAChBtC,cAAc,CAAC2C,KAAK,CAAC,CAAC;cAChBJ,sBAAsB,GAAGK,YAAY,CAAC3C,OAAO,CAAC,wBAAwB,CAAC;cAC7E,IAAIsC,sBAAsB,EAAE;gBACpBC,uBAAuB,GAAGI,YAAY,CAAC3C,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE;gBACrFpC,MAAM,CAAC9D,IAAI,CAAC;kBACV8I,IAAI,EAAEN,sBAAsB;kBAC5BO,KAAK,EAAEN,uBAAuB,GAAG1C,IAAI,CAACC,KAAK,CAACyC,uBAAuB,CAAC,GAAG,CAAC;gBAC1E,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL3E,MAAM,CAAC9D,IAAI,CAAC;kBAAE8I,IAAI,EAAE;gBAAa,CAAC,CAAC;cACrC;cACA/E,KAAK,CAACqD,MAAM,CAAC,UAAU,CAAC;cACxBnE,oBAAoB,CAAC,CAAC;cACtB;cACA;cACAK,SAAS,CAAC;gBAAEuE,OAAO,EAAEQ,IAAI;gBAAEW,SAAS,EAAE,IAAI;gBAAE7L,IAAI,EAAE;cAAU,CAAC,CAAC;YAChE;UAAC;UAAA;YAAA,OAAAwL,QAAA,CAAArH,IAAA;QAAA;MAAA,GAAA8G,OAAA;IAAA,CACF;IAAA,gBApBKL,QAAQA,CAAAkB,EAAA;MAAA,OAAAd,IAAA,CAAAlG,KAAA,OAAAD,SAAA;IAAA;EAAA,GAoBb;;EAED;EACA,IAAMkH,QAAQ,GAAG9G,GAAG,CAAC,EAAE,CAAC;EACxB,IAAM+G,UAAU,GAAG/G,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAM8E,kBAAkB,GAAG9E,GAAG,CAAC,KAAK,CAAC;EACrC,IAAMgH,YAAY,GAAG,SAAfA,YAAYA,CAAIxD,IAAI,EAAK;IAAA,IAAAyD,YAAA;IAC7BF,UAAU,CAACnN,KAAK,GAAG4J,IAAI,CAACnF,IAAI;IAC5ByG,kBAAkB,CAAClL,KAAK,GAAG,KAAK;IAChCiK,cAAc,CAACqD,OAAO,CAAC,SAAS,EAAEvD,IAAI,CAACwD,SAAS,CAAC3D,IAAI,CAAC,CAAC;IACvD,IAAI,EAAAyD,YAAA,GAAAzE,IAAI,CAAC5I,KAAK,cAAAqN,YAAA,uBAAVA,YAAA,CAAYG,MAAM,MAAK5D,IAAI,CAAC6D,EAAE,EAAE;IACpCC,eAAe,CAAC9D,IAAI,CAAC;EACvB,CAAC;EACD,IAAM8D,eAAe;IAAA,IAAAC,KAAA,GAAA5H,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkJ,SAAOhE,IAAI;MAAA,IAAAiE,qBAAA,EAAAtB,IAAA,EAAAuB,YAAA;MAAA,OAAAxO,mBAAA,GAAAuB,IAAA,UAAAkN,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAA7I,IAAA,GAAA6I,SAAA,CAAAxK,IAAA;UAAA;YAAAwK,SAAA,CAAAxK,IAAA;YAAA,OACVmD,GAAG,CAAC+G,eAAe,CAAC,CAAC,CAAC,EAAE9D,IAAI,CAAC6D,EAAE,CAAC;UAAA;YAAAI,qBAAA,GAAAG,SAAA,CAAA/K,IAAA;YAA/CsJ,IAAI,GAAAsB,qBAAA,CAAJtB,IAAI;YACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;cAChB0B,OAAO,CAACjO,KAAK,GAAG,EAAE;cAClBiK,cAAc,CAACqD,OAAO,CAAC,QAAQ,EAAE1D,IAAI,CAAC6D,EAAE,CAAC;cACzCxG,oBAAoB,CAAC,CAAC;cACtBc,KAAK,CAACmG,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;cACpCnG,KAAK,CAACqD,MAAM,CAAC,sBAAsB,EAAE,IAAI,CAAC;cAC1CrD,KAAK,CAACqD,MAAM,CAAC,sBAAsB,EAAE,IAAI,CAAC;YAC5C,CAAC,MAAM;cACL8B,QAAQ,CAAClN,KAAK,IAAA8N,YAAA,GAAGlF,IAAI,CAAC5I,KAAK,cAAA8N,YAAA,uBAAVA,YAAA,CAAYN,MAAM;cACnCnG,YAAY,CAAC;gBAAE5C,IAAI,EAAEmF,IAAI,CAACnF;cAAK,CAAC,CAAC;YACnC;UAAC;UAAA;YAAA,OAAAuJ,SAAA,CAAA1I,IAAA;QAAA;MAAA,GAAAsI,QAAA;IAAA,CACF;IAAA,gBAbKF,eAAeA,CAAAS,GAAA;MAAA,OAAAR,KAAA,CAAA1H,KAAA,OAAAD,SAAA;IAAA;EAAA,GAapB;EACDK,KAAK,CACH;IAAA,OAAMuC,IAAI,CAAC5I,KAAK;EAAA,GAChB,YAAM;IAAA,IAAAoO,YAAA,EAAAC,YAAA;IACJnB,QAAQ,CAAClN,KAAK,IAAAoO,YAAA,GAAGxF,IAAI,CAAC5I,KAAK,cAAAoO,YAAA,uBAAVA,YAAA,CAAYZ,MAAM;IACnC,KAAAa,YAAA,GAAIzF,IAAI,CAAC5I,KAAK,cAAAqO,YAAA,eAAVA,YAAA,CAAYrD,SAAS,EAAE;MAAA,IAAAsD,YAAA;MACzB,IAAMC,MAAM,GAAGtE,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC/C,IAAIqE,MAAM,IAAI,CAACC,MAAM,CAACD,MAAM,CAAC,IAAI,EAAAD,YAAA,GAAA1F,IAAI,CAAC5I,KAAK,cAAAsO,YAAA,uBAAVA,YAAA,CAAYtD,SAAS,MAAK,GAAG,EAAE;QAAA,IAAAyD,kBAAA;QAC9D,IAAI,EAAAA,kBAAA,GAAAjH,UAAU,CAACxH,KAAK,cAAAyO,kBAAA,uBAAhBA,kBAAA,CAAkBC,kBAAkB,MAAK,MAAM,EAAE;UACnDlI,QAAQ,CAAC,YAAM;YACbgC,kBAAkB,CAACxI,KAAK,GAAG,KAAK;YAChCyI,sBAAsB,CAACzI,KAAK,GAAG,IAAI;UACrC,CAAC,CAAC;QACJ,CAAC,MAAM;UACLwG,QAAQ,CAAC,YAAM;YACbgC,kBAAkB,CAACxI,KAAK,GAAG,IAAI;YAC/BuI,gBAAgB,CAACvI,KAAK,GAAG,IAAI;UAC/B,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EACD;IAAE2O,SAAS,EAAE;EAAK,CACpB,CAAC;EACD;EACA,IAAMC,WAAU,GAAG,SAAbA,UAAUA,CAAIC,QAAQ,EAAK;IAC/B,IAAIC,WAAW,GAAG,EAAE;IACpB,KAAK,IAAI7O,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAGF,QAAQ,CAACxK,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE,EAAE;MACnD6O,WAAW,CAAC9K,IAAI,CAAC;QACfyJ,EAAE,EAAEoB,QAAQ,CAAC5O,CAAC,CAAC,CAAC+O,MAAM;QACtBvK,IAAI,EAAEoK,QAAQ,CAAC5O,CAAC,CAAC,CAACwE,IAAI;QACtBwK,SAAS,EAAEJ,QAAQ,CAAC5O,CAAC,CAAC,CAACgP,SAAS;QAChCC,YAAY,EAAEL,QAAQ,CAAC5O,CAAC,CAAC,CAACiP,YAAY;QACtCC,aAAa,EAAEN,QAAQ,CAAC5O,CAAC,CAAC,CAACkP,aAAa;QACxCC,IAAI,EAAEP,QAAQ,CAAC5O,CAAC,CAAC,CAACoP,OAAO,GAAG,GAAG1I,GAAG,CAAC2I,OAAO,CAACT,QAAQ,CAAC5O,CAAC,CAAC,CAACoP,OAAO,CAAC,EAAE,GAAGrH,QAAQ;QAC5E6B,GAAG,EAAEgF,QAAQ,CAAC5O,CAAC,CAAC,CAACsP,WAAW;QAC5BC,QAAQ,EAAEZ,WAAU,CAACC,QAAQ,CAAC5O,CAAC,CAAC,CAACuP,QAAQ,IAAI,EAAE;MACjD,CAAC,CAAC;IACJ;IACA,OAAOV,WAAW;EACpB,CAAC;EACD;EACA,IAAMb,OAAO,GAAG7H,GAAG,CAAC,EAAE,CAAC;EACvB;EACA,IAAMqJ,WAAW,GAAGlJ,QAAQ,CAAC;IAAA,OAAMqI,WAAU,CAAC7G,KAAK,CAACc,OAAO,CAAC6G,SAAS,IAAI,EAAE,CAAC;EAAA,EAAC;EAC7E;EACA,IAAMC,MAAM,GAAGvJ,GAAG,CAAC,KAAK,CAAC;EACzB;EACA,IAAMwJ,WAAW,GAAGxJ,GAAG,CAAC,KAAK,CAAC;EAC9B;EACA,IAAMyJ,MAAM,GAAGzJ,GAAG,CAAC,KAAK,CAAC;EACzB;EACA,IAAM0J,UAAU,GAAG1J,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAM2J,WAAW,GAAG3J,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,IAAM4J,eAAe,GAAG5J,GAAG,CAAC,EAAE,CAAC;EAC/B;EACA,IAAM6J,YAAY,GAAG7J,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5B;EACA,IAAM8J,aAAa,GAAG9J,GAAG,CAAC,EAAE,CAAC;EAC7B;EACA,IAAM+J,SAAS,GAAG/J,GAAG,CAAC,CAAC,CAAC,CAAC;EACzBC,KAAK,CACH;IAAA,OAAMoJ,WAAW,CAACzP,KAAK;EAAA,GACvB,UAACoQ,GAAG,EAAK;IACPT,MAAM,CAAC3P,KAAK,GAAG,KAAK;IACpB4P,WAAW,CAAC5P,KAAK,GAAG,KAAK;IACzBiQ,YAAY,CAACjQ,KAAK,GAAG,CAAC,CAAC;IACvBkQ,aAAa,CAAClQ,KAAK,GAAG,EAAE;IACxBmQ,SAAS,CAACnQ,KAAK,GAAG,CAAC,CAAC;IACpB,IAAIyP,WAAW,CAACzP,KAAK,CAACqE,MAAM,EAAE;MAC5B,IAAM0I,KAAK,GAAGhD,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;MAC/D,IAAI6C,KAAK,CAACsD,aAAa,EAAE;QACvBC,QAAQ,CAAC;UAAEC,GAAG,EAAExD,KAAK,CAACyD,WAAW,IAAI,IAAI;UAAExQ,KAAK,EAAE+M,KAAK,CAACsD;QAAc,CAAC,CAAC;MAC1E,CAAC,MAAM;QACL7J,QAAQ,CAAC,YAAM;UAAA,IAAAiK,mBAAA;UACbxC,OAAO,CAACjO,KAAK,IAAAyQ,mBAAA,GAAGhB,WAAW,CAACzP,KAAK,CAAC,CAAC,CAAC,cAAAyQ,mBAAA,uBAApBA,mBAAA,CAAsBhD,EAAE;UACxCiD,WAAW,CAAC,CAAC;QACf,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EACD;IAAE/B,SAAS,EAAE;EAAK,CACpB,CAAC;EACD;EACA,IAAM+B,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;IACxB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1C,IAAMC,iBAAiB,GAAGtJ,UAAU,CAACxH,KAAK,CAAC+Q,gBAAgB,IAAI,EAAE;MACjE,IAAID,iBAAiB,KAAK,MAAM,EAAE9J,oBAAoB,CAAC,CAAC;IAC1D;IACAgI,MAAM,CAAChP,KAAK,GAAG,EAAE;IACjBgR,QAAQ,CAAChR,KAAK,GAAG,EAAE;IACnBiR,OAAO,CAACjR,KAAK,GAAG,EAAE;IAClBkR,SAAS,CAAClR,KAAK,GAAG,EAAE;IACpBmR,cAAc,CAACnR,KAAK,GAAG,EAAE;IACzBwG,QAAQ,CAAC,YAAM;MAAA,IAAA4K,KAAA,YAAAA,MAAA,EACiD;UAC5D,IAAMxH,IAAI,GAAG6F,WAAW,CAACzP,KAAK,CAACC,CAAC,CAAC;UACjC,IAAIgO,OAAO,CAACjO,KAAK,KAAK4J,IAAI,CAAC6D,EAAE,EAAE;YAC7BxD,cAAc,CAACqD,OAAO,CAAC,KAAK,EAAEvD,IAAI,CAACwD,SAAS,CAAC3D,IAAI,CAAC,CAAC;YACnD,IAAIA,IAAI,CAACqF,SAAS,KAAK,YAAY,EAAE;cACnC;cACAU,MAAM,CAAC3P,KAAK,GAAG,KAAK;cACpB8H,MAAM,CAAC9D,IAAI,CAAC;gBAAE8I,IAAI,EAAElD,IAAI,CAACqF,SAAS;gBAAElC,KAAK,EAAEsE,aAAa,CAACzH,IAAI,CAACqF,SAAS;cAAE,CAAC,CAAC;cAC3EgB,YAAY,CAACjQ,KAAK,GAAG4J,IAAI;cACzBsG,aAAa,CAAClQ,KAAK,GAAG4J,IAAI,CAAC4F,QAAQ;cACnChJ,QAAQ,CAAC,YAAM;gBACb,IAAIwJ,eAAe,CAAChQ,KAAK,EAAE;kBACzB,IAAIgQ,eAAe,CAAChQ,KAAK,KAAK8P,UAAU,CAAC9P,KAAK,EAAE;oBAC9C8P,UAAU,CAAC9P,KAAK,GAAG,EAAE;oBACrB+P,WAAW,CAAC/P,KAAK,GAAG,CAAC,CAAC;kBACxB;kBACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAE4E,MAAM,GAAGuF,IAAI,CAAC4F,QAAQ,CAACnL,MAAM,EAAE5E,CAAC,GAAG4E,MAAM,EAAE5E,CAAC,EAAE,EAAE;oBAC9D,IAAImK,IAAI,CAAC4F,QAAQ,CAAC/P,CAAC,CAAC,CAACgO,EAAE,KAAKuC,eAAe,CAAChQ,KAAK,EAAE;sBACjDsR,YAAY,CAAC1H,IAAI,CAAC4F,QAAQ,CAAC/P,CAAC,CAAC,CAAC;oBAChC;kBACF;gBACF;cACF,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,IAAImK,IAAI,CAACqF,SAAS,CAACsC,QAAQ,CAAC,aAAa,CAAC,EAAE;gBAC1C,IAAIzB,UAAU,CAAC9P,KAAK,EAAE;kBACpBiQ,YAAY,CAACjQ,KAAK,GAAG4J,IAAI;kBACzB0H,YAAY,CAAC1H,IAAI,EAAE,KAAK,CAAC;gBAC3B,CAAC,MAAM;kBACL+F,MAAM,CAAC3P,KAAK,GAAG,KAAK;kBACpB8H,MAAM,CAAC9D,IAAI,CAAC;oBAAE8I,IAAI,EAAElD,IAAI,CAACqF,SAAS;oBAAElC,KAAK,EAAEsE,aAAa,CAACzH,IAAI,CAACqF,SAAS;kBAAE,CAAC,CAAC;gBAC7E;gBAAC;kBAAAjN,CAAA;gBAAA;cAEH;cACA;cACA,IAAI4H,IAAI,CAAC4F,QAAQ,IAAI5F,IAAI,CAAC4F,QAAQ,CAACnL,MAAM,EAAE;gBACzC;gBACAiN,YAAY,CAAC1H,IAAI,EAAE,IAAI,CAAC;cAC1B,CAAC,MAAM;gBACL,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC2H,QAAQ,CAAC3H,IAAI,CAACuF,aAAa,CAACnP,KAAK,CAAC,EAAE;kBACjD2P,MAAM,CAAC3P,KAAK,GAAG,KAAK;kBACpB4P,WAAW,CAAC5P,KAAK,GAAG,KAAK;kBACzB8H,MAAM,CAAC9D,IAAI,CAAC;oBACV8I,IAAI,EAAElD,IAAI,CAACqF,SAAS;oBACpBlC,KAAK,EAAAyE,aAAA,CAAAA,aAAA,KAAOH,aAAa,CAACzH,IAAI,CAACqF,SAAS,CAAC;sBAAEE,aAAa,EAAEvF,IAAI,CAACuF,aAAa,CAACnP;oBAAK;kBACpF,CAAC,CAAC;gBACJ,CAAC,MAAM;kBACL;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAsR,YAAY,CAAC1H,IAAI,EAAE,IAAI,CAAC;kBACxB;gBACF;cACF;YACF;UACF;QACF,CAAC;QAAA6H,IAAA;MA7DD,KAAK,IAAIxR,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAGU,WAAW,CAACzP,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE;QAAAwR,IAAA,GAAAL,KAAA;QAAA,IAAAK,IAAA,SAAAA,IAAA,CAAAzP,CAAA;MAAA;IA8D9D,CAAC,CAAC;EACJ,CAAC;EACD,IAAM0P,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,SAAS,EAAEC,eAAe,EAAK;IACpD3D,OAAO,CAACjO,KAAK,GAAG2R,SAAS;IACzB3C,MAAM,CAAChP,KAAK,GAAG,EAAE;IACjBgR,QAAQ,CAAChR,KAAK,GAAG,EAAE;IACnBiR,OAAO,CAACjR,KAAK,GAAG,EAAE;IAClBkR,SAAS,CAAClR,KAAK,GAAG,EAAE;IACpBmR,cAAc,CAACnR,KAAK,GAAG,EAAE;IACzBsR,YAAY,CAACM,eAAe,CAAC;EAC/B,CAAC;EACD,IAAMN,YAAY,GAAG,SAAfA,YAAYA,CAAI1H,IAAI,EAAEzI,IAAI,EAAK;IACnC;IACA;IACA,IAAIyI,IAAI,CAAC4F,QAAQ,IAAI5F,IAAI,CAAC4F,QAAQ,CAACnL,MAAM,EAAE;MACzC;MACA,IAAIlD,IAAI,EAAE;QACRwO,MAAM,CAAC3P,KAAK,GAAG,IAAI;QACnB4P,WAAW,CAAC5P,KAAK,GAAG,KAAK;MAC3B,CAAC,MAAM;QACL2P,MAAM,CAAC3P,KAAK,GAAG,IAAI;QACnB4P,WAAW,CAAC5P,KAAK,GAAG,IAAI;QACxBmQ,SAAS,CAACnQ,KAAK,GAAG4J,IAAI;MACxB;MACAoH,QAAQ,CAAChR,KAAK,GAAG4J,IAAI,CAAC4F,QAAQ;MAC9B,IAAMqC,GAAG,GAAG/B,UAAU,CAAC9P,KAAK,GAAG8R,aAAY,CAAClI,IAAI,CAAC4F,QAAQ,CAAC,GAAGuC,YAAW,CAACnI,IAAI,CAAC4F,QAAQ,CAAC;MACvFR,MAAM,CAAChP,KAAK,GAAG6R,GAAG,CAACpE,EAAE;MACrBqC,UAAU,CAAC9P,KAAK,GAAG,EAAE;MACrBgQ,eAAe,CAAChQ,KAAK,GAAG,EAAE;MAC1BgS,SAAS,CAACH,GAAG,CAAC;IAChB,CAAC,MAAM;MACL,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACN,QAAQ,CAAC3H,IAAI,CAACuF,aAAa,CAACnP,KAAK,CAAC,EAAE;QACjD2P,MAAM,CAAC3P,KAAK,GAAG,KAAK;QACpB4P,WAAW,CAAC5P,KAAK,GAAG,IAAI;QACxBmQ,SAAS,CAACnQ,KAAK,GAAG4J,IAAI;QACtB9B,MAAM,CAAC9D,IAAI,CAAC;UACV8I,IAAI,EAAElD,IAAI,CAACqF,SAAS;UACpBlC,KAAK,EAAAyE,aAAA,CAAAA,aAAA,KAAOH,aAAa,CAACzH,IAAI,CAACqF,SAAS,CAAC;YAAEE,aAAa,EAAEvF,IAAI,CAACuF,aAAa,CAACnP;UAAK;QACpF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAImB,IAAI,EAAE;UACRwO,MAAM,CAAC3P,KAAK,GAAG,KAAK;UACpB4P,WAAW,CAAC5P,KAAK,GAAG,KAAK;QAC3B,CAAC,MAAM;UACL2P,MAAM,CAAC3P,KAAK,GAAG,KAAK;UACpB4P,WAAW,CAAC5P,KAAK,GAAG,IAAI;UACxBmQ,SAAS,CAACnQ,KAAK,GAAG4J,IAAI;QACxB;QACAoH,QAAQ,CAAChR,KAAK,GAAG,CAAC4J,IAAI,CAAC;QACvB,IAAMiI,IAAG,GAAG/B,UAAU,CAAC9P,KAAK,GAAG8R,aAAY,CAAC,CAAClI,IAAI,CAAC,CAAC,GAAGmI,YAAW,CAAC,CAACnI,IAAI,CAAC,CAAC;QACzEoF,MAAM,CAAChP,KAAK,GAAG6R,IAAG,CAACpE,EAAE;QACrBqC,UAAU,CAAC9P,KAAK,GAAG,EAAE;QACrBgQ,eAAe,CAAChQ,KAAK,GAAG,EAAE;QAC1BgS,SAAS,CAACH,IAAG,CAAC;MAChB;IACF;EACF,CAAC;EAED,IAAME,YAAW,GAAG,SAAdA,WAAWA,CAAIE,IAAI,EAAK;IAC5B;IACA,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB,KAAK,IAAIjS,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAGkD,IAAI,CAAC5N,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE,EAAE;MAC/C,IAAIA,CAAC,KAAK,CAAC,EAAE;QACX,IAAIgS,IAAI,CAAChS,CAAC,CAAC,CAACuP,QAAQ,CAACnL,MAAM,KAAK,CAAC,EAAE;UACjC6N,UAAU,GAAGD,IAAI,CAAChS,CAAC,CAAC;QACtB,CAAC,MAAM;UACLiS,UAAU,GAAGH,YAAW,CAACE,IAAI,CAAChS,CAAC,CAAC,CAACuP,QAAQ,CAAC;QAC5C;MACF;IACF;IACA,OAAO0C,UAAU;EACnB,CAAC;EACD,IAAMJ,aAAY,GAAG,SAAfA,YAAYA,CAAIG,IAAI,EAAK;IAC7B;IACA,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB,KAAK,IAAIjS,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAGkD,IAAI,CAAC5N,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE,EAAE;MAC/C,IAAI6P,UAAU,CAAC9P,KAAK,KAAKiS,IAAI,CAAChS,CAAC,CAAC,CAACwN,EAAE,EAAE;QACnCyE,UAAU,GAAGD,IAAI,CAAChS,CAAC,CAAC;MACtB;MACA,IAAIgS,IAAI,CAAChS,CAAC,CAAC,CAACuP,QAAQ,CAACnL,MAAM,EAAE;QAC3B,IAAMwN,GAAG,GAAGC,aAAY,CAACG,IAAI,CAAChS,CAAC,CAAC,CAACuP,QAAQ,CAAC;QAC1C0C,UAAU,GAAGL,GAAG,CAACpE,EAAE,GAAGoE,GAAG,GAAGK,UAAU;MACxC;IACF;IACA,OAAOA,UAAU;EACnB,CAAC;EACD,IAAMlD,MAAM,GAAG5I,GAAG,CAAC,EAAE,CAAC;EACtB,IAAM4K,QAAQ,GAAG5K,GAAG,CAAC,EAAE,CAAC;EACxB,IAAM4L,SAAS,GAAG,SAAZA,SAASA,CAAIpI,IAAI,EAAK;IAC1B;IACA,IAAI,CAACqH,OAAO,CAACjR,KAAK,CAACqE,MAAM,EAAE;MACzByC,cAAc,CAACqL,cAAc,CAAC;QAAEhB,cAAc,EAAE;MAAG,CAAC,CAAC;IACvD;IACA,IAAI,CAACF,OAAO,CAACjR,KAAK,CAACoS,GAAG,CAAC,UAACpQ,CAAC;MAAA,OAAKA,CAAC,CAACyL,EAAE;IAAA,EAAC,CAAC8D,QAAQ,CAAC3H,IAAI,CAAC6D,EAAE,CAAC,EAAE;MACrDwD,OAAO,CAACjR,KAAK,CAACgE,IAAI,CAAC4F,IAAI,CAAC;IAC1B;IACAyI,QAAQ,CAAC,CAAC;EACZ,CAAC;EACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAC5B;IACA,IAAI1C,WAAW,CAAC5P,KAAK,EAAE;MACrB2P,MAAM,CAAC3P,KAAK,GAAG,KAAK;MACpB4P,WAAW,CAAC5P,KAAK,GAAG,KAAK;MACzB0Q,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EACD,IAAM6B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI3I,IAAI,EAAE4I,KAAK,EAAK;IACxC,IAAIA,KAAK,GAAG,CAAC,KAAKvB,OAAO,CAACjR,KAAK,CAACqE,MAAM,EAAE;IACxC,IAAMoO,UAAU,GAAGxB,OAAO,CAACjR,KAAK,CAACqF,KAAK,CAAC,CAAC,EAAEmN,KAAK,GAAG,CAAC,CAAC;IACpD,IAAME,UAAU,GAAGzB,OAAO,CAACjR,KAAK,CAACqF,KAAK,CAACmN,KAAK,GAAG,CAAC,CAAC,CAACJ,GAAG,CAAC,UAACpQ,CAAC;MAAA,OAAKA,CAAC,CAACyL,EAAE;IAAA,EAAC;IAClEwD,OAAO,CAACjR,KAAK,GAAGyS,UAAU;IAC1BvB,SAAS,CAAClR,KAAK,GAAGkR,SAAS,CAAClR,KAAK,CAAC2S,MAAM,CAAC,UAAC/I,IAAI;MAAA,OAAK,CAAC8I,UAAU,CAACnB,QAAQ,CAAC3H,IAAI,CAAC6D,EAAE,CAAC;IAAA,EAAC;IACjF,IAAMmF,WAAW,GAAG3B,OAAO,CAACjR,KAAK,CAAC2S,MAAM,CACtC,UAAC3Q,CAAC;MAAA,OAAKiN,SAAS,CAACjN,CAAC,CAACiN,SAAS,CAAC4D,SAAS,CAAC,CAAC,EAAE7Q,CAAC,CAACiN,SAAS,CAAC6D,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI9Q,CAAC,CAACiN,SAAS,CAAC,KAAK,GAAG;IAAA,CAC7F,CAAC;IACD,IAAM8D,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIvJ,GAAG,CAACkJ,WAAW,CAACR,GAAG,CAAC,UAACpQ,CAAC;MAAA,OAAKkR,aAAa,CAAClR,CAAC,CAACiN,SAAS,CAAC;IAAA,EAAC,CAAC,CAAC;IAC3FkC,cAAc,CAACnR,KAAK,GAAG+S,WAAW;IAClC/D,MAAM,CAAChP,KAAK,GAAG4J,IAAI,CAAC6D,EAAE;IACtB4E,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,IAAMpB,OAAO,GAAG7K,GAAG,CAAC,EAAE,CAAC,EAAC;EACxB,IAAM8K,SAAS,GAAG9K,GAAG,CAAC,EAAE,CAAC;EACzB,IAAM+M,UAAU,GAAG/M,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAMgN,UAAU,GAAGhN,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAM+K,cAAc,GAAG/K,GAAG,CAAC,EAAE,CAAC;EAC9B,IAAMiN,OAAO,GAAG,SAAVA,OAAOA,CAAIlT,CAAC,EAAEmT,CAAC;IAAA,OAAKnT,CAAC,CAACwS,MAAM,CAAC,UAAC3Q,CAAC;MAAA,OAAKsR,CAAC,CAACR,OAAO,CAAC9Q,CAAC,CAAC,KAAK,CAAC,CAAC;IAAA,EAAC;EAAA;EAC9D;EACA,IAAMqQ,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;IACrB,IAAMkB,YAAY,GAAG7T,MAAM,CAACsF,IAAI,CAAC6B,MAAM,CAAC2M,QAAQ,CAAC;IACjD,IAAMC,YAAY,GAAGT,KAAK,CAACC,IAAI,CAAC,IAAIvJ,GAAG,CAACuH,OAAO,CAACjR,KAAK,CAACoS,GAAG,CAAC,UAACpQ,CAAC;MAAA,OAAKiN,SAAS,CAACjN,CAAC,CAACiN,SAAS,CAAC;IAAA,EAAC,CAAC,CAAC,CAAC0D,MAAM,CAAC,UAAC3Q,CAAC;MAAA,OAClGuR,YAAY,CAAChC,QAAQ,CAACvP,CAAC,CAAC;IAAA,CAC1B,CAAC;IACD,IAAM0R,WAAW,GAAGL,OAAO,CAACI,YAAY,EAAE/K,QAAQ,CAAC1I,KAAK,CAAC;IACzD;IACA0I,QAAQ,CAAC1I,KAAK,MAAA2T,MAAA,CAAAC,kBAAA,CAAOlL,QAAQ,CAAC1I,KAAK,GAAA4T,kBAAA,CAAKF,WAAW,EAAC;IACpD,IAAI,CAACA,WAAW,CAACrP,MAAM,EAAE;MACvBwP,cAAc,CAAC,CAAC;MAChB;IACF;IACArN,QAAQ,CAAC,YAAM;MAAA,IAAAsN,MAAA,YAAAA,OAAA,EAC2C;QACtD,IAAM9R,CAAC,GAAG0R,WAAW,CAACzT,CAAC,CAAC;QACxB,IAAI,CAAC0I,WAAW,CAAC3I,KAAK,CAACgC,CAAC,CAAC,EAAE;UACzB2G,WAAW,CAAC3I,KAAK,CAACgC,CAAC,CAAC,GAAG+E,aAAa,CAAC/E,CAAC,CAAC;UACvC2G,WAAW,CAAC3I,KAAK,CAACgC,CAAC,CAAC,CAAC+R,WAAW,CAC7BrR,IAAI,CAAC,YAAM;YACViG,WAAW,CAAC3I,KAAK,CAACgC,CAAC,CAAC,CAACgS,YAAY,CAACtR,IAAI,CAAC,YAAM;cAC3CoE,cAAc,CAACqL,cAAc,CAAC;gBAC5B8B,KAAK,EAAElM,KAAK,CAACc,OAAO,CAACqL,UAAU;gBAC/BtL,IAAI,EAAEb,KAAK,CAACc,OAAO,CAACC,SAAS;gBAC7BqL,IAAI,EAAEpM,KAAK,CAACc,OAAO,CAAC6G,SAAS;gBAC7B3G,IAAI,EAAEhB,KAAK,CAACc,OAAO,CAACG,SAAS;gBAC7BC,IAAI,EAAElB,KAAK,CAACc,OAAO,CAACK,SAAS;gBAC7BkL,UAAU,EAAErM,KAAK,CAACc,OAAO,CAACwL,aAAa;gBACvCC,cAAc,EAAEvM,KAAK,CAACc,OAAO,CAACM;cAChC,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,CAAC,CACDxD,KAAK,CAAC,UAAC4O,GAAG,EAAK;YACdnB,UAAU,CAACpT,KAAK,CAACgE,IAAI,CAAChC,CAAC,CAAC;UAC1B,CAAC,CAAC;QACN;MACF,CAAC;MAtBD,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAG2E,WAAW,CAACrP,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE;QAAA6T,MAAA;MAAA;MAuBtDU,UAAU,CAAC,YAAM;QACfX,cAAc,CAAC,CAAC;MAClB,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,CAAC;EACJ,CAAC;EACD,IAAMA,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,KAAK,IAAI5T,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAGkC,OAAO,CAACjR,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE,EAAE;MACxD,IAAM2J,IAAI,GAAGqH,OAAO,CAACjR,KAAK,CAACC,CAAC,CAAC;MAC7B,IAAI+O,MAAM,CAAChP,KAAK,KAAK4J,IAAI,CAAC6D,EAAE,EAAE;QAC5BxD,cAAc,CAACqD,OAAO,CAAC,KAAK,EAAEvD,IAAI,CAACwD,SAAS,CAAC3D,IAAI,CAAC,CAAC;QACnD,IAAM6K,OAAO,GAAG7K,IAAI,CAACqF,SAAS,CAAC4D,SAAS,CAAC,CAAC,EAAEjJ,IAAI,CAACqF,SAAS,CAAC6D,OAAO,CAAC,GAAG,CAAC,CAAC,IAAIlJ,IAAI,CAACqF,SAAS;QAC1F,IAAMyF,YAAY,GAAGzF,SAAS,CAACwF,OAAO,CAAC;QACvCtB,UAAU,CAACnT,KAAK,GAAG0U,YAAY;QAC/B,IAAIhM,QAAQ,CAAC1I,KAAK,CAACuR,QAAQ,CAACmD,YAAY,CAAC,IAAI,CAACtB,UAAU,CAACpT,KAAK,CAACuR,QAAQ,CAACmD,YAAY,CAAC,EAAE;UAAA,IAAAC,mBAAA;UACrF,IAAM5H,KAAK,GAAAyE,aAAA,CAAAA,aAAA;YAAKrC,aAAa,GAAAwF,mBAAA,GAAE/K,IAAI,CAACuF,aAAa,cAAAwF,mBAAA,uBAAlBA,mBAAA,CAAoB3U;UAAK,GAAKqR,aAAa,CAACzH,IAAI,CAACqF,SAAS,CAAC,GAAKrF,IAAI,CAACmD,KAAK,CAAE;UAC3GjF,MAAM,CAAC9D,IAAI,CAAC;YAAE8I,IAAI,EAAElD,IAAI,CAACqF,SAAS;YAAElC,KAAK,EAAEA;UAAM,CAAC,CAAC;UACnDpE,WAAW,CAAC3I,KAAK,CAAC0U,YAAY,CAAC,CAACV,YAAY,CAACtR,IAAI,CAAC,YAAM;YACtDoE,cAAc,CAACqL,cAAc,CAAC;cAAEhB,cAAc,EAAEF,OAAO,CAACjR,KAAK,CAACoS,GAAG,CAAC,UAACpQ,CAAC;gBAAA,OAAKA,CAAC,CAACiN,SAAS;cAAA;YAAE,CAAC,CAAC;UAC1F,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAIyF,YAAY,KAAK,GAAG,EAAE;YAAA,IAAAE,oBAAA;YACxB,IAAM7H,MAAK,GAAAyE,aAAA,CAAAA,aAAA;cAAKrC,aAAa,GAAAyF,oBAAA,GAAEhL,IAAI,CAACuF,aAAa,cAAAyF,oBAAA,uBAAlBA,oBAAA,CAAoB5U;YAAK,GAAKqR,aAAa,CAACzH,IAAI,CAACqF,SAAS,CAAC,GAAKrF,IAAI,CAACmD,KAAK,CAAE;YAC3GjF,MAAM,CAAC9D,IAAI,CAAC;cAAE8I,IAAI,EAAElD,IAAI,CAACqF,SAAS;cAAElC,KAAK,EAAEA;YAAM,CAAC,CAAC;YACnD,IAAM6F,WAAW,GAAG3B,OAAO,CAACjR,KAAK,CAAC2S,MAAM,CACtC,UAAC3Q,CAAC;cAAA,OAAKiN,SAAS,CAACjN,CAAC,CAACiN,SAAS,CAAC4D,SAAS,CAAC,CAAC,EAAE7Q,CAAC,CAACiN,SAAS,CAAC6D,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI9Q,CAAC,CAACiN,SAAS,CAAC,KAAK,GAAG;YAAA,CAC7F,CAAC;YACD,IAAM8D,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIvJ,GAAG,CAACkJ,WAAW,CAACR,GAAG,CAAC,UAACpQ,CAAC;cAAA,OAAKkR,aAAa,CAAClR,CAAC,CAACiN,SAAS,CAAC;YAAA,EAAC,CAAC,CAAC;YAC3FkC,cAAc,CAACnR,KAAK,GAAG+S,WAAW;UACpC,CAAC,MAAM;YACLjL,MAAM,CAAC9D,IAAI,CAAC;cAAE8I,IAAI,EAAE;YAAgB,CAAC,CAAC;UACxC;QACF;MACF;IACF;EACF,CAAC;EACD,IAAMoG,aAAa,GAAG,SAAhBA,aAAaA,CAAI2B,GAAG,EAAK;IAC7B,IAAI/H,IAAI,GAAG,EAAE;IACb,IAAMgI,KAAK,GAAGD,GAAG,CAAC/B,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;IAClC,IAAMiC,GAAG,GAAGF,GAAG,CAAC/B,OAAO,CAAC,GAAG,CAAC;IAC5B,IAAIiC,GAAG,KAAK,CAAC,CAAC,EAAE;MACdjI,IAAI,GAAG+H,GAAG,CAAChC,SAAS,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM;MACL/F,IAAI,GAAG+H,GAAG,CAAChC,SAAS,CAACiC,KAAK,EAAEC,GAAG,CAAC;IAClC;IACA,OAAOjI,IAAI;EACb,CAAC;EACD,IAAMuE,aAAa,GAAG,SAAhBA,aAAaA,CAAI7F,IAAI,EAAK;IAC9B,IAAIwJ,MAAM,GAAG,CAAC,CAAC;IACfxJ,IAAI,GAAGA,IAAI,CAACqH,SAAS,CAACrH,IAAI,CAACsH,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,IAAImC,GAAG,GAAGzJ,IAAI,CAAC0J,KAAK,CAAC,GAAG,CAAC;IACzBD,GAAG,CAAC7S,OAAO,CAAC,UAACwH,IAAI,EAAK;MACpB,IAAIzJ,CAAC,GAAGyJ,IAAI,CAACsL,KAAK,CAAC,GAAG,CAAC;MACvBF,MAAM,CAAC7U,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,OAAO6U,MAAM;EACf,CAAC;EACD;EACA,IAAM/F,SAAS,GAAG,SAAZA,SAASA,CAAI4F,GAAG,EAAK;IACzB,IAAI/H,IAAI,GAAG,EAAE,EAAC;IACd,IAAMqI,KAAK,GAAGN,GAAG,CAAC/B,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAC;IACnC,IAAMsC,IAAI,GAAGP,GAAG,CAAC/B,OAAO,CAAC,GAAG,EAAEqC,KAAK,CAAC,EAAC;IACrC,IAAME,IAAI,GAAGR,GAAG,CAAC/B,OAAO,CAAC,GAAG,EAAEqC,KAAK,CAAC,EAAC;IACrC,IAAIE,IAAI,KAAK,CAAC,CAAC,EAAE;MACfvI,IAAI,GAAG+H,GAAG,CAAChC,SAAS,CAAC,CAAC,EAAEuC,IAAI,CAAC;IAC/B,CAAC,MAAM;MACLtI,IAAI,GAAG+H,GAAG,CAAChC,SAAS,CAAC,CAAC,EAAEwC,IAAI,CAAC;IAC/B;IACA,OAAOvI,IAAI;EACb,CAAC;EACDzG,KAAK,CACH;IAAA,OAAM0B,KAAK,CAACuN,KAAK,CAACC,SAAS;EAAA,GAC3B,UAACnF,GAAG,EAAK;IACP,IAAIA,GAAG,CAACtD,IAAI,EAAE;MACZyI,SAAS,CAACnF,GAAG,CAAC;IAChB;EACF,CACF,CAAC;EACD/J,KAAK,CACH;IAAA,OAAM0B,KAAK,CAACuN,KAAK,CAACE,cAAc;EAAA,GAChC,UAACpF,GAAG,EAAK;IACP,IAAIA,GAAG,CAACqF,OAAO,EAAE;MACfC,QAAQ,CAACtF,GAAG,CAAC;IACf;EACF,CACF,CAAC;EAED,IAAMmF,SAAS,GAAG,SAAZA,SAASA,CAAInF,GAAG,EAAK;IACzB,IAAIc,SAAS,CAAClR,KAAK,CAACoS,GAAG,CAAC,UAACpQ,CAAC;MAAA,OAAKA,CAAC,CAAC2T,MAAM;IAAA,EAAC,CAACpE,QAAQ,CAACxH,IAAI,CAACwD,SAAS,CAAC6C,GAAG,CAAC,CAAC,EAAE;MACtE,KAAK,IAAInQ,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAGmC,SAAS,CAAClR,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE,EAAE;QAC1D,IAAM2J,IAAI,GAAGsH,SAAS,CAAClR,KAAK,CAACC,CAAC,CAAC;QAC/B,IAAI2J,IAAI,CAAC+L,MAAM,KAAK5L,IAAI,CAACwD,SAAS,CAAC6C,GAAG,CAAC,EAAE;UACvCpB,MAAM,CAAChP,KAAK,GAAG4J,IAAI,CAAC6D,EAAE;UACtB4E,QAAQ,CAAC,CAAC;QACZ;MACF;IACF,CAAC,MAAM;MACL,IAAM5E,EAAE,GAAGmI,IAAI,CAAC,CAAC;MACjB1E,SAAS,CAAClR,KAAK,CAACgE,IAAI,CAAC;QAAEyJ,EAAE,EAAEA,EAAE;QAAEkI,MAAM,EAAE5L,IAAI,CAACwD,SAAS,CAAC6C,GAAG;MAAE,CAAC,CAAC;MAC7Da,OAAO,CAACjR,KAAK,CAACgE,IAAI,CAAC;QACjByJ,EAAE;QACFhJ,IAAI,EAAE2L,GAAG,CAAC3L,IAAI;QACdwK,SAAS,EAAEmB,GAAG,CAACtD,IAAI;QACnBC,KAAK,EAAAyE,aAAA,CAAAA,aAAA,KAAOpB,GAAG,CAACrD,KAAK;UAAE8I,OAAO,EAAEpI,EAAE;UAAEqI,UAAU,EAAE9G,MAAM,CAAChP;QAAK;MAC9D,CAAC,CAAC;MACFgP,MAAM,CAAChP,KAAK,GAAGyN,EAAE;MACjB4E,QAAQ,CAAC,CAAC;IACZ;IACAvL,cAAc,CAACqL,cAAc,CAAC;MAAEoD,SAAS,EAAE;QAAE9Q,IAAI,EAAE,EAAE;QAAEqI,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;MAAE;IAAE,CAAC,CAAC;EACjF,CAAC;EACD,IAAM2I,QAAQ,GAAG,SAAXA,QAAQA,CAAItF,GAAG,EAAK;IACxB,IAAIA,GAAG,CAAC2F,MAAM,EAAE;MACd7E,SAAS,CAAClR,KAAK,GAAGkR,SAAS,CAAClR,KAAK,CAAC2S,MAAM,CAAC,UAAC/I,IAAI;QAAA,OAAKA,IAAI,CAAC6D,EAAE,KAAK2C,GAAG,CAACqF,OAAO;MAAA,EAAC;MAC3ExE,OAAO,CAACjR,KAAK,GAAGiR,OAAO,CAACjR,KAAK,CAAC2S,MAAM,CAAC,UAAC/I,IAAI;QAAA,OAAKA,IAAI,CAAC6D,EAAE,KAAK2C,GAAG,CAACqF,OAAO;MAAA,EAAC;MACvEzG,MAAM,CAAChP,KAAK,GAAGoQ,GAAG,CAAC2F,MAAM;MACzB1D,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACL2D,WAAW,CAAC5F,GAAG,CAACqF,OAAO,CAAC;IAC1B;IACA3O,cAAc,CAACqL,cAAc,CAAC;MAAEqD,cAAc,EAAE;QAAEO,MAAM,EAAE,EAAE;QAAEN,OAAO,EAAE;MAAG;IAAE,CAAC,CAAC;EAChF,CAAC;EACD,IAAMQ,SAAS,GAAG7P,GAAG,CAAC,IAAI,CAAC;EAC3B,IAAM8P,aAAa,GAAG,SAAhBA,aAAaA,CAAIzI,EAAE,EAAK;IAC5B,IAAI5F,KAAK,CAACsO,IAAI,CAACC,UAAU,KAAK,MAAM,EAAE;MACpCjF,cAAc,CAACnR,KAAK,GAAGmR,cAAc,CAACnR,KAAK,CAAC2S,MAAM,CAAC,UAAC3Q,CAAC;QAAA,OAAKA,CAAC,KAAK6F,KAAK,CAACpD,IAAI;MAAA,EAAC;MAC3EwR,SAAS,CAACjW,KAAK,GAAG,KAAK;MACvBwU,UAAU,CAAC,YAAM;QACfrD,cAAc,CAACnR,KAAK,CAACgE,IAAI,CAAC6D,KAAK,CAACpD,IAAI,CAAC;QACrCwR,SAAS,CAACjW,KAAK,GAAG,IAAI;MACxB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAGkC,OAAO,CAACjR,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE,EAAE;QACxD,IAAM2J,IAAI,GAAGqH,OAAO,CAACjR,KAAK,CAACC,CAAC,CAAC;QAC7B,IAAI2J,IAAI,CAAC6D,EAAE,KAAKA,EAAE,EAAE;UAClB3G,cAAc,CAACqL,cAAc,CAAC;YAAEkE,YAAY,EAAEzM,IAAI,CAACqF;UAAU,CAAC,CAAC;UAC/DuF,UAAU,CAAC,YAAM;YACf1N,cAAc,CAACqL,cAAc,CAAC;cAAEkE,YAAY,EAAE;YAAG,CAAC,CAAC;UACrD,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;EACF,CAAC;EACD,IAAML,WAAW,GAAG,SAAdA,WAAWA,CAAIvI,EAAE,EAAK;IAC1B,IAAIuB,MAAM,CAAChP,KAAK,KAAKyN,EAAE,EAAE;MACvB,KAAK,IAAIxN,CAAC,GAAG,CAAC,EAAE8O,GAAG,GAAGkC,OAAO,CAACjR,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8O,GAAG,EAAE9O,CAAC,EAAE,EAAE;QACxD,IAAM2J,IAAI,GAAGqH,OAAO,CAACjR,KAAK,CAACC,CAAC,CAAC;QAC7B,IAAI2J,IAAI,CAAC6D,EAAE,KAAKA,EAAE,EAAE;UAClBuB,MAAM,CAAChP,KAAK,GAAGiR,OAAO,CAACjR,KAAK,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACwN,EAAE;UAC9C4E,QAAQ,CAAC,CAAC;QACZ;MACF;IACF;IACAnB,SAAS,CAAClR,KAAK,GAAGkR,SAAS,CAAClR,KAAK,CAAC2S,MAAM,CAAC,UAAC/I,IAAI;MAAA,OAAKA,IAAI,CAAC6D,EAAE,KAAKA,EAAE;IAAA,EAAC;IAClEwD,OAAO,CAACjR,KAAK,GAAGiR,OAAO,CAACjR,KAAK,CAAC2S,MAAM,CAAC,UAAC/I,IAAI;MAAA,OAAKA,IAAI,CAAC6D,EAAE,KAAKA,EAAE;IAAA,EAAC;EAChE,CAAC;EACD,IAAM6I,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI7I,EAAE,EAAK;IAC/ByD,SAAS,CAAClR,KAAK,GAAGkR,SAAS,CAAClR,KAAK,CAAC2S,MAAM,CAAC,UAAC/I,IAAI;MAAA,OAAKA,IAAI,CAAC6D,EAAE,KAAKA,EAAE;IAAA,EAAC;IAClEwD,OAAO,CAACjR,KAAK,GAAGiR,OAAO,CAACjR,KAAK,CAAC2S,MAAM,CAAC,UAAC/I,IAAI;MAAA,OAAKA,IAAI,CAAC6D,EAAE,KAAKA,EAAE;IAAA,EAAC;IAC9DuB,MAAM,CAAChP,KAAK,GAAGyN,EAAE;IACjB4E,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,IAAMkE,aAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAEjG,GAAG,EAAEvQ,KAAK,EAAK;IAC1C,IAAI,CAACwW,KAAK,IAAI,CAACA,KAAK,CAACnS,MAAM,EAAE,OAAO,EAAE,EAAC;IACvC,IAAMmL,QAAQ,GAAG,EAAE;IAAA,IAAAiH,SAAA,GAAAC,0BAAA,CACFF,KAAK;MAAAG,KAAA;IAAA;MAAtB,KAAAF,SAAA,CAAAhV,CAAA,MAAAkV,KAAA,GAAAF,SAAA,CAAA7W,CAAA,IAAAiD,IAAA,GAAwB;QAAA,IAAf+T,IAAI,GAAAD,KAAA,CAAA3W,KAAA;QACX,IAAI4W,IAAI,CAACrG,GAAG,CAAC,KAAKvQ,KAAK,IAAI,CAAC8P,UAAU,CAAC9P,KAAK,EAAE;UAC5C8P,UAAU,CAAC9P,KAAK,GAAG4W,IAAI,CAACnJ,EAAE;UAC1BsC,WAAW,CAAC/P,KAAK,GAAG4W,IAAI;QAC1B;QACAA,IAAI,GAAGlX,MAAM,CAACmX,MAAM,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC;QAC9B,IAAME,GAAG,GAAGP,aAAY,CAACK,IAAI,CAACpH,QAAQ,EAAEe,GAAG,EAAEvQ,KAAK,CAAC;QACnD,IAAK8W,GAAG,IAAIA,GAAG,CAACzS,MAAM,IAAKuS,IAAI,CAACrG,GAAG,CAAC,KAAKvQ,KAAK,EAAE;UAC9C8W,GAAG,CAACzS,MAAM,KAAKuS,IAAI,CAACpH,QAAQ,GAAGsH,GAAG,CAAC;UACnCtH,QAAQ,CAACxL,IAAI,CAAC4S,IAAI,CAAC;QACrB;MACF;IAAC,SAAArC,GAAA;MAAAkC,SAAA,CAAAlX,CAAA,CAAAgV,GAAA;IAAA;MAAAkC,SAAA,CAAAjV,CAAA;IAAA;IACD,OAAOgO,QAAQ,CAACnL,MAAM,GAAGmL,QAAQ,GAAG,EAAE,EAAC;EACzC,CAAC;EACD,IAAMc,QAAQ,GAAG,SAAXA,QAAQA,CAAAyG,KAAA,EAA8B;IAAA,IAAAC,SAAA,GAAAD,KAAA,CAAxBxG,GAAG;MAAHA,GAAG,GAAAyG,SAAA,cAAG,IAAI,GAAAA,SAAA;MAAEhX,KAAK,GAAA+W,KAAA,CAAL/W,KAAK;IACnC,IAAI6P,MAAM,CAAC7P,KAAK,EAAE;IAClB6P,MAAM,CAAC7P,KAAK,GAAG,IAAI;IACnB,IAAMiX,YAAY,GAAGV,aAAY,CAAC9G,WAAW,CAACzP,KAAK,EAAEuQ,GAAG,EAAEvQ,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzE,IAAIiX,YAAY,CAACxJ,EAAE,EAAE;MACnB,IAAIQ,OAAO,CAACjO,KAAK,KAAKiX,YAAY,CAACxJ,EAAE,EAAE;QACrC,IAAIwJ,YAAY,CAAChI,SAAS,KAAK,YAAY,EAAE;UAAA,IAAAiI,qBAAA;UAC3C,IAAID,YAAY,CAACxJ,EAAE,KAAKqC,UAAU,CAAC9P,KAAK,EAAE;YACxC8P,UAAU,CAAC9P,KAAK,GAAG,EAAE;YACrB+P,WAAW,CAAC/P,KAAK,GAAG,CAAC,CAAC;YACtBsS,eAAe,CAAC,CAAC;YACjB;UACF;UACA,IAAI,EAAA4E,qBAAA,GAAAD,YAAY,CAACzH,QAAQ,CAAC,CAAC,CAAC,cAAA0H,qBAAA,uBAAxBA,qBAAA,CAA0BzJ,EAAE,MAAK0C,SAAS,CAACnQ,KAAK,CAACyN,EAAE,EAAE;YACvDuB,MAAM,CAAChP,KAAK,GAAG8P,UAAU,CAAC9P,KAAK;YAC/BgS,SAAS,CAACjC,WAAW,CAAC/P,KAAK,CAAC;YAC5B8P,UAAU,CAAC9P,KAAK,GAAG,EAAE;YACrB+P,WAAW,CAAC/P,KAAK,GAAG,CAAC,CAAC;YACtBwG,QAAQ,CAAC,YAAM;cACb,IAAIqJ,MAAM,CAAC7P,KAAK,EAAE;gBAChB6P,MAAM,CAAC7P,KAAK,GAAG,KAAK;cACtB;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLsS,eAAe,CAAC,CAAC;YACjBkC,UAAU,CAAC,YAAM;cACfhO,QAAQ,CAAC,YAAM;gBAAA,IAAA2Q,sBAAA;gBACbnH,eAAe,CAAChQ,KAAK,IAAAmX,sBAAA,GAAGF,YAAY,CAACzH,QAAQ,CAAC,CAAC,CAAC,cAAA2H,sBAAA,uBAAxBA,sBAAA,CAA0B1J,EAAE;gBACpDiD,WAAW,CAAC,CAAC;gBACblK,QAAQ,CAAC,YAAM;kBACb,IAAIqJ,MAAM,CAAC7P,KAAK,EAAE;oBAChB6P,MAAM,CAAC7P,KAAK,GAAG,KAAK;kBACtB;gBACF,CAAC,CAAC;cACJ,CAAC,CAAC;YACJ,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC,MAAM;UACL,IAAIiX,YAAY,CAAChI,SAAS,CAACsC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAClDb,WAAW,CAAC,CAAC;YACblK,QAAQ,CAAC,YAAM;cACb,IAAIqJ,MAAM,CAAC7P,KAAK,EAAE;gBAChB6P,MAAM,CAAC7P,KAAK,GAAG,KAAK;cACtB;YACF,CAAC,CAAC;YACF;UACF;UACAgP,MAAM,CAAChP,KAAK,GAAG8P,UAAU,CAAC9P,KAAK;UAC/BgS,SAAS,CAACjC,WAAW,CAAC/P,KAAK,CAAC;UAC5B8P,UAAU,CAAC9P,KAAK,GAAG,EAAE;UACrB+P,WAAW,CAAC/P,KAAK,GAAG,CAAC,CAAC;UACtBwG,QAAQ,CAAC,YAAM;YACb,IAAIqJ,MAAM,CAAC7P,KAAK,EAAE;cAChB6P,MAAM,CAAC7P,KAAK,GAAG,KAAK;YACtB;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAI4P,WAAW,CAAC5P,KAAK,EAAE;UACrBsS,eAAe,CAAC,CAAC;UACjBkC,UAAU,CAAC,YAAM;YACfhO,QAAQ,CAAC,YAAM;cACbyH,OAAO,CAACjO,KAAK,GAAGiX,YAAY,CAACxJ,EAAE;cAC/B,IAAIwJ,YAAY,CAACxJ,EAAE,KAAKqC,UAAU,CAAC9P,KAAK,EAAE;gBACxC8P,UAAU,CAAC9P,KAAK,GAAG,EAAE;gBACrB+P,WAAW,CAAC/P,KAAK,GAAG,CAAC,CAAC;cACxB,CAAC,MAAM;gBAAA,IAAAoX,sBAAA;gBACLpH,eAAe,CAAChQ,KAAK,IAAAoX,sBAAA,GAAGH,YAAY,CAACzH,QAAQ,CAAC,CAAC,CAAC,cAAA4H,sBAAA,uBAAxBA,sBAAA,CAA0B3J,EAAE;cACtD;cACAiD,WAAW,CAAC,CAAC;cACblK,QAAQ,CAAC,YAAM;gBACb,IAAIqJ,MAAM,CAAC7P,KAAK,EAAE;kBAChB6P,MAAM,CAAC7P,KAAK,GAAG,KAAK;gBACtB;cACF,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLiO,OAAO,CAACjO,KAAK,GAAGiX,YAAY,CAACxJ,EAAE;UAC/B,IAAIwJ,YAAY,CAACxJ,EAAE,KAAKqC,UAAU,CAAC9P,KAAK,EAAE;YACxC8P,UAAU,CAAC9P,KAAK,GAAG,EAAE;YACrB+P,WAAW,CAAC/P,KAAK,GAAG,CAAC,CAAC;UACxB,CAAC,MAAM;YAAA,IAAAqX,sBAAA;YACLrH,eAAe,CAAChQ,KAAK,IAAAqX,sBAAA,GAAGJ,YAAY,CAACzH,QAAQ,CAAC,CAAC,CAAC,cAAA6H,sBAAA,uBAAxBA,sBAAA,CAA0B5J,EAAE;UACtD;UACAiD,WAAW,CAAC,CAAC;UACblK,QAAQ,CAAC,YAAM;YACb,IAAIqJ,MAAM,CAAC7P,KAAK,EAAE;cAChB6P,MAAM,CAAC7P,KAAK,GAAG,KAAK;YACtB;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,MAAM;MACLsH,SAAS,CAAC;QAAEnG,IAAI,EAAE,SAAS;QAAE0K,OAAO,EAAE;MAAa,CAAC,CAAC;IACvD;EACF,CAAC;EACD,IAAMyL,aAAa,GAAG,SAAhBA,aAAaA,CAAI7J,EAAE,EAAK;IAC5BqC,UAAU,CAAC9P,KAAK,GAAGyN,EAAE;EACvB,CAAC;EAED,IAAMmI,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;IACjB,OAAO,sCAAsC,CAAC2B,OAAO,CAAC,OAAO,EAAE,UAAClX,CAAC,EAAK;MACpE,IAAIZ,CAAC,GAAI+X,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;QAC9BzV,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;MACpC,OAAOuC,CAAC,CAAC0V,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EAEDjR,OAAO,CAAC,UAAU,EAAEiP,QAAQ,CAAC;EAC7BjP,OAAO,CAAC,eAAe,EAAEyJ,aAAa,CAAC;EACvCzJ,OAAO,CAAC,cAAc,EAAE6K,YAAY,CAAC;EACrC7K,OAAO,CAAC,eAAe,EAAEiL,aAAa,CAAC;EACvCjL,OAAO,CAAC,eAAe,EAAE6Q,aAAa,CAAC;EACvC7Q,OAAO,CAAC,UAAU,EAAE6J,QAAQ,CAAC;EAC7B7J,OAAO,CAAC,WAAW,EAAE8O,SAAS,CAAC;EAC/B;EACA,IAAMoC,wBAAwB,GAAGvR,GAAG,CAAC,KAAK,CAAC;EAC3C,IAAMwR,mBAAmB,GAAGxR,GAAG,CAAC,IAAI,CAAC;EACrC,IAAMyR,gBAAgB,GAAGzR,GAAG,CAAC,EAAE,CAAC;;EAEhC;EACA,IAAMoE,mBAAmB;IAAA,IAAAsN,KAAA,GAAA/R,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqT,SAAA;MAAA,IAAAC,qBAAA,EAAA/F,IAAA,EAAAgG,UAAA,EAAAC,YAAA,EAAAC,QAAA,EAAAC,UAAA,EAAAC,YAAA;MAAA,OAAA/Y,mBAAA,GAAAuB,IAAA,UAAAyX,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAApT,IAAA,GAAAoT,SAAA,CAAA/U,IAAA;UAAA;YAAA+U,SAAA,CAAApT,IAAA;YAAAoT,SAAA,CAAA/U,IAAA;YAAA,OAEDmD,GAAG,CAAC6R,oBAAoB,CAAC,CAAC;UAAA;YAAAR,qBAAA,GAAAO,SAAA,CAAAtV,IAAA;YAAzCgP,IAAI,GAAA+F,qBAAA,CAAJ/F,IAAI;YACZ4F,gBAAgB,CAAC7X,KAAK,GAAGiS,IAAI,IAAI,EAAE;;YAEnC;YACA,IAAI4F,gBAAgB,CAAC7X,KAAK,CAACqE,MAAM,GAAG,CAAC,EAAE;cAC/B4T,UAAU,GAAGhO,cAAc,CAACC,OAAO,CAAC,sBAAsB,CAAC;cAC3DgO,YAAY,GAAGjO,cAAc,CAACC,OAAO,CAAC,iBAAiB,CAAC;cAE9D,IAAI+N,UAAU,IAAIC,YAAY,EAAE;gBACxBC,QAAQ,GAAGpO,IAAI,CAACC,KAAK,CAACkO,YAAY,CAAC;gBACzCN,mBAAmB,CAAC5X,KAAK,GAAGmY,QAAQ;cACtC,CAAC,MAAM;gBACL;gBACMC,UAAU,GAAGP,gBAAgB,CAAC7X,KAAK,CAAC,CAAC,CAAC;gBAC5C4X,mBAAmB,CAAC5X,KAAK,GAAGoY,UAAU;gBAChCC,YAAY,GAAGD,UAAU,CAAC3K,EAAE,IAAI2K,UAAU,CAACK,KAAK;gBACtDxO,cAAc,CAACqD,OAAO,CAAC,sBAAsB,EAAE+K,YAAY,CAAC;gBAC5DpO,cAAc,CAACqD,OAAO,CAAC,iBAAiB,EAAEvD,IAAI,CAACwD,SAAS,CAAC6K,UAAU,CAAC,CAAC;cACvE;YACF,CAAC,MAAM;cACLhO,OAAO,CAACsO,IAAI,CAAC,QAAQ,CAAC;YACxB;YAACH,SAAA,CAAA/U,IAAA;YAAA;UAAA;YAAA+U,SAAA,CAAApT,IAAA;YAAAoT,SAAA,CAAAI,EAAA,GAAAJ,SAAA;YAEDnO,OAAO,CAACwO,KAAK,CAAC,WAAW,EAAAL,SAAA,CAAAI,EAAO,CAAC;UAAA;UAAA;YAAA,OAAAJ,SAAA,CAAAjT,IAAA;QAAA;MAAA,GAAAyS,QAAA;IAAA,CAEpC;IAAA,gBA3BKvN,mBAAmBA,CAAA;MAAA,OAAAsN,KAAA,CAAA7R,KAAA,OAAAD,SAAA;IAAA;EAAA,GA2BxB;;EAED;EACA,IAAM6S,wBAAwB;IAAA,IAAAC,KAAA,GAAA/S,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqU,SAAOnP,IAAI;MAAA,IAAAoP,qBAAA,EAAAC,sBAAA;MAAA,IAAAC,YAAA,EAAAC,QAAA,EAAAV,KAAA,EAAAW,qBAAA,EAAA7M,IAAA;MAAA,OAAAjN,mBAAA,GAAAuB,IAAA,UAAAwY,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAAnU,IAAA,GAAAmU,SAAA,CAAA9V,IAAA;UAAA;YAC1C;YACM0V,YAAY,GAAG,EAAAF,qBAAA,GAAApB,mBAAmB,CAAC5X,KAAK,cAAAgZ,qBAAA,uBAAzBA,qBAAA,CAA2BvL,EAAE,OAAAwL,sBAAA,GAAIrB,mBAAmB,CAAC5X,KAAK,cAAAiZ,sBAAA,uBAAzBA,sBAAA,CAA2BR,KAAK;YAChFU,QAAQ,GAAGvP,IAAI,CAAC6D,EAAE,IAAI7D,IAAI,CAAC6O,KAAK;YAAA,MAElCS,YAAY,KAAKC,QAAQ;cAAAG,SAAA,CAAA9V,IAAA;cAAA;YAAA;YAC3B4G,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;YAAA,OAAAiP,SAAA,CAAAlW,MAAA;UAAA;YAAAkW,SAAA,CAAAnU,IAAA;YAKzB;YACMsT,KAAK,GAAG7O,IAAI,CAAC6D,EAAE,IAAI7D,IAAI,CAAC6O,KAAK,EACnC;YAAAa,SAAA,CAAA9V,IAAA;YAAA,OACuBmD,GAAG,CAAC4S,mBAAmB,CAAC;cAAEC,cAAc,EAAEf;YAAM,CAAC,CAAC;UAAA;YAAAW,qBAAA,GAAAE,SAAA,CAAArW,IAAA;YAAjEsJ,IAAI,GAAA6M,qBAAA,CAAJ7M,IAAI;YAAA,MAERA,IAAI,KAAK,GAAG;cAAA+M,SAAA,CAAA9V,IAAA;cAAA;YAAA;YACdoU,mBAAmB,CAAC5X,KAAK,GAAG4J,IAAI;YAChCK,cAAc,CAACqD,OAAO,CAAC,iBAAiB,EAAEvD,IAAI,CAACwD,SAAS,CAAC3D,IAAI,CAAC,CAAC;YAC/DK,cAAc,CAACqD,OAAO,CAAC,sBAAsB,EAAEmL,KAAK,CAAC;;YAErD;YACA1Q,KAAK,CAACqD,MAAM,CAAC,yBAAyB,EAAEqN,KAAK,CAAC;YAC9C1Q,KAAK,CAACqD,MAAM,CAAC,2BAA2B,EAAExB,IAAI,CAAC;YAE/CtC,SAAS,CAAC;cAAEnG,IAAI,EAAE,SAAS;cAAE0K,OAAO,EAAE,UAAUjC,IAAI,CAAC6P,OAAO;YAAG,CAAC,CAAC;;YAEjE;YACAxL,OAAO,CAACjO,KAAK,GAAG,EAAE;YAClByP,WAAW,CAACzP,KAAK,GAAG,EAAE;YACtBgR,QAAQ,CAAChR,KAAK,GAAG,EAAE;YACnBiR,OAAO,CAACjR,KAAK,GAAG,EAAE;YAClBkR,SAAS,CAAClR,KAAK,GAAG,EAAE;YACpBmR,cAAc,CAACnR,KAAK,GAAG,EAAE;YACzB;YAAAsZ,SAAA,CAAA9V,IAAA;YAAA,OACMuE,KAAK,CAACmG,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;UAAA;YAE1C;YACAnG,KAAK,CAACqD,MAAM,CAAC,sBAAsB,EAAE,IAAI,CAAC;YAC1CrD,KAAK,CAACqD,MAAM,CAAC,sBAAsB,EAAE,IAAI,CAAC;YAC1C;YAAAkO,SAAA,CAAA9V,IAAA;YAAA,OACMgH,mBAAmB,CAAC,CAAC;UAAA;YAC3B;YACAvD,oBAAoB,CAAC,CAAC;YAEtBK,SAAS,CAAC;cAAEnG,IAAI,EAAE,SAAS;cAAE0K,OAAO,EAAE;YAAe,CAAC,CAAC;YAAAyN,SAAA,CAAA9V,IAAA;YAAA;UAAA;YAEvD8D,SAAS,CAAC;cAAEnG,IAAI,EAAE,OAAO;cAAE0K,OAAO,EAAE;YAAS,CAAC,CAAC;UAAA;YAAAyN,SAAA,CAAA9V,IAAA;YAAA;UAAA;YAAA8V,SAAA,CAAAnU,IAAA;YAAAmU,SAAA,CAAAX,EAAA,GAAAW,SAAA;YAGjDhS,SAAS,CAAC;cAAEnG,IAAI,EAAE,OAAO;cAAE0K,OAAO,EAAE;YAAa,CAAC,CAAC;UAAA;UAAA;YAAA,OAAAyN,SAAA,CAAAhU,IAAA;QAAA;MAAA,GAAAyT,QAAA;IAAA,CAEtD;IAAA,gBApDKF,wBAAwBA,CAAAa,GAAA;MAAA,OAAAZ,KAAA,CAAA7S,KAAA,OAAAD,SAAA;IAAA;EAAA,GAoD7B;EAED,IAAM2T,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAI/P,IAAI,EAAK;IACnC+N,wBAAwB,CAAC3X,KAAK,GAAG,KAAK;IACtCiK,cAAc,CAACqD,OAAO,CAAC,iBAAiB,EAAEvD,IAAI,CAACwD,SAAS,CAAC3D,IAAI,CAAC,CAAC;IAC/DK,cAAc,CAACqD,OAAO,CAAC,sBAAsB,EAAE1D,IAAI,CAAC6D,EAAE,CAAC;IACvD;IACA1F,KAAK,CAACqD,MAAM,CAAC,yBAAyB,EAAExB,IAAI,CAAC6D,EAAE,CAAC;IAChD1F,KAAK,CAACqD,MAAM,CAAC,2BAA2B,EAAExB,IAAI,CAAC;IAC/CtC,SAAS,CAAC;MAAEnG,IAAI,EAAE,SAAS;MAAE0K,OAAO,EAAE,UAAUjC,IAAI,CAACnF,IAAI;IAAG,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,IAAM8F,wBAAwB,GAAG,SAA3BA,wBAAwBA,CAAA,EAAS;IAAA,IAAAqP,YAAA,EAAAC,YAAA;IACrC,IAAMC,mBAAmB,GAAG7P,cAAc,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,EAAE;IAC/E,IAAM6P,oBAAoB,GAAG9P,cAAc,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,EAAE;IACjF,IAAI,EAAA0P,YAAA,GAAAhR,IAAI,CAAC5I,KAAK,cAAA4Z,YAAA,uBAAVA,YAAA,CAAY5O,SAAS,MAAK,GAAG,IAAI,EAAA6O,YAAA,GAAAjR,IAAI,CAAC5I,KAAK,cAAA6Z,YAAA,uBAAVA,YAAA,CAAYG,iBAAiB,IAAG,CAAC,IAAIF,mBAAmB,IAAI,CAACC,oBAAoB,EAAE;MACtHpC,wBAAwB,CAAC3X,KAAK,GAAG,IAAI;IACvC;EACF,CAAC;EACD,OAAO;IACL4I,IAAI;IACJG,IAAI;IACJE,IAAI;IACJd,IAAI;IACJC,KAAK;IACLC,aAAa;IACbC,cAAc;IACdc,QAAQ;IACR+B,aAAa;IACb9B,cAAc;IACdd,gBAAgB;IAChBC,kBAAkB;IAClBC,sBAAsB;IACtBqD,oBAAoB;IACpBoB,QAAQ;IACRC,UAAU;IACVC,YAAY;IACZlC,kBAAkB;IAClByO,kBAAkB;IAClBhC,wBAAwB;IACxBC,mBAAmB;IACnBC,gBAAgB;IAChBgB,wBAAwB;IACxBlJ,MAAM;IACNC,WAAW;IACX3B,OAAO;IACPwB,WAAW;IACXiB,WAAW;IACX1B,MAAM;IACNgC,QAAQ;IACRgB,SAAS;IACTO,gBAAgB;IAChBtC,YAAY;IACZC,aAAa;IACbC,SAAS;IACTmC,eAAe;IACf2D,SAAS;IACT9E,cAAc;IACdF,OAAO;IACPoB,QAAQ;IACR6D,aAAa;IACbF,WAAW;IACXM,gBAAgB;IAChBnD,UAAU;IACVzK;EACF,CAAC;AACH,CAAC;AACD,OAAO,IAAMuR,OAAO,GAAG,SAAVA,OAAOA,CAAIpS,KAAK,EAAK;EAChC,IAAMqS,MAAM,GAAG9T,GAAG,CAAC,KAAK,CAAC;EACzB,IAAM+T,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvBD,MAAM,CAACla,KAAK,GAAG6H,KAAK,CAACsO,IAAI,CAACC,UAAU,KAAK,MAAM;EACjD,CAAC;EAED/P,KAAK,CACH;IAAA,OAAMwB,KAAK;EAAA,GACX,YAAM;IACJsS,UAAU,CAAC,CAAC;EACd,CAAC,EACD;IAAEC,IAAI,EAAE;EAAK,CACf,CAAC;EACD9T,SAAS,CAAC,YAAM;IACd6T,UAAU,CAACtS,KAAK,CAAC;EACnB,CAAC,CAAC;EACF,OAAO;IAAEqS;EAAO,CAAC;AACnB,CAAC;AAED,OAAO,IAAMG,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;EAC9B,IAAMtS,KAAK,GAAGnB,QAAQ,CAAC,CAAC;EACxB,IAAM0T,cAAc,GAAG/T,QAAQ,CAAC;IAAA,OAAMwB,KAAK,CAACc,OAAO,CAAC0R,iBAAiB;EAAA,EAAC;EACtE,OAAO;IAAED;EAAe,CAAC;AAC3B,CAAC;AAED,OAAO,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EAChC,IAAMzS,KAAK,GAAGnB,QAAQ,CAAC,CAAC;EACxB,IAAM6T,WAAW,GAAGlU,QAAQ,CAAC;IAAA,OAAMwB,KAAK,CAACuN,KAAK,CAACmF,WAAW;EAAA,EAAC;EAC3D,IAAMC,iBAAiB,GAAGnU,QAAQ,CAAC;IAAA,OAAM,GAAGkU,WAAW,CAACza,KAAK,IAAI;EAAA,EAAC;EAClE,IAAM2a,cAAc,GAAGvU,GAAG,CAAC,KAAK,CAAC;EACjC,IAAMwU,gBAAgB,GAAGxU,GAAG,CAAC,KAAK,CAAC;EACnC;EACA,IAAMyU,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA,EAAS;IACvC,IAAIvP,MAAM,CAACwP,UAAU,GAAG,IAAI,GAAG,GAAG,EAAE;MAClC,IAAM1S,KAAK,GAAGkD,MAAM,CAACwP,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;MACxD/S,KAAK,CAACqD,MAAM,CAAC,gBAAgB,EAAEhD,KAAK,CAAC;MACrC,IAAI,CAACuS,cAAc,CAAC3a,KAAK,EAAE4a,gBAAgB,CAAC5a,KAAK,GAAG,KAAK;MACzD2a,cAAc,CAAC3a,KAAK,GAAG,IAAI;IAC7B,CAAC,MAAM;MACL+H,KAAK,CAACqD,MAAM,CAAC,gBAAgB,EAAE,GAAG,CAAC;MACnC,IAAIuP,cAAc,CAAC3a,KAAK,EAAE4a,gBAAgB,CAAC5a,KAAK,GAAG,KAAK;MACxD2a,cAAc,CAAC3a,KAAK,GAAG,KAAK;IAC9B;EACF,CAAC;EACDsG,SAAS,CAAC,YAAM;IACduU,0BAA0B,CAAC,CAAC;IAC5BvP,MAAM,CAACyP,gBAAgB,CAAC,QAAQ,EAAEF,0BAA0B,CAAC;EAC/D,CAAC,CAAC;EACFnU,WAAW,CAAC,YAAM;IAChB4E,MAAM,CAAC0P,mBAAmB,CAAC,QAAQ,EAAEH,0BAA0B,CAAC;EAClE,CAAC,CAAC;EACF,OAAO;IAAEH,iBAAiB;IAAEC,cAAc;IAAEC;EAAiB,CAAC;AAChE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}