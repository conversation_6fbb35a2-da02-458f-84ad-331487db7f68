{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onActivated, onDeactivated, onUnmounted } from 'vue';\nimport { useStore } from 'vuex';\nimport { whetherAiChat } from 'common/js/system_var.js';\nimport { ElMessage } from 'element-plus';\nimport GlobalAiChat from '../../GlobalAiChat/GlobalAiChat';\nvar __default__ = {\n  name: 'DevelopContent'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var wordRef = ref();\n    var oldId = ref('');\n    var arrId = ref([]);\n    var content = ref('');\n    var checklist = ref([]);\n    var tabId = ref('correction');\n    var tabList = [{\n      id: 'correction',\n      name: '文本校正'\n    }, {\n      id: 'aiChat',\n      name: '智能小助手'\n    }];\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var typingVerification = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var _data$checklist;\n        var _yield$api$typingVeri, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.typingVerification({\n                text: content.value.replace(/&ldquo;/ig, '“').replace(/&rdquo;/ig, '”')\n              });\n            case 2:\n              _yield$api$typingVeri = _context.sent;\n              data = _yield$api$typingVeri.data;\n              checklist.value = (data === null || data === void 0 || (_data$checklist = data.checklist) === null || _data$checklist === void 0 ? void 0 : _data$checklist.map(function (v) {\n                return _objectSpread(_objectSpread({}, v), {}, {\n                  id: guid()\n                });\n              })) || [];\n              content.value = (data === null || data === void 0 ? void 0 : data.replace_text) || '';\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function typingVerification() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var handleChat = function handleChat() {\n      if (content.value === '') return ElMessage({\n        type: 'warning',\n        message: '请输入内容'\n      });\n      typingVerification();\n    };\n    var handleCopy = function handleCopy() {\n      if (!content.value.replace(/<[^>]*>/g, '')) return ElMessage({\n        message: '无复制内容',\n        type: 'warning'\n      });\n      var textarea = document.createElement('textarea');\n      textarea.readOnly = 'readonly';\n      textarea.style.position = 'absolute';\n      textarea.style.left = '-9999px';\n      textarea.value = content.value.replace(/<[^>]*>/g, '');\n      document.body.appendChild(textarea);\n      textarea.select();\n      var result = document.execCommand('Copy');\n      if (result) ElMessage({\n        message: '复制成功',\n        type: 'success'\n      });\n      document.body.removeChild(textarea);\n    };\n    var handleDownload = function handleDownload() {\n      store.commit('setExportWordHtmlObj', {\n        code: 'exportWord',\n        name: '导出内容',\n        key: 'content',\n        data: {\n          content: content.value\n        }\n      });\n    };\n    var _elAttr = function elAttr(elList, id) {\n      var elArr = [];\n      var styleObj = {};\n      for (var index = 0; index < elList.length; index++) {\n        var item = elList[index];\n        if (item.nodeName !== '#text') {\n          if (item.getAttribute('data-umpos') === id) {\n            var elParent = item.parentNode;\n            var is = 0;\n            while (elParent.style[is]) {\n              var elParentStyleKey = elParent.style[is].replace(/-([a-z])/g, function (p, m) {\n                return m.toUpperCase();\n              });\n              styleObj[elParent.style[is]] = elParent.style[elParentStyleKey];\n              is++;\n            }\n            elArr.push(item);\n          }\n        }\n        if (item.childNodes.length) {\n          var obj = _elAttr(item.childNodes, id);\n          elArr = [].concat(_toConsumableArray(elArr), _toConsumableArray(obj.elArr));\n          styleObj = _objectSpread(_objectSpread({}, styleObj), obj.styleObj);\n        }\n      }\n      return {\n        elArr,\n        styleObj\n      };\n    };\n    var handleDetails = function handleDetails(row) {\n      var elList = wordRef.value.childNodes;\n      if (oldId.value) {\n        var oldObj = _elAttr(elList, oldId.value);\n        if (oldObj.elArr.length) {\n          for (var index = 0; index < oldObj.elArr.length; index++) {\n            var item = oldObj.elArr[index];\n            item.style.color = '';\n            item.style.backgroundColor = '';\n          }\n        }\n      }\n      oldId.value = row.position + '';\n      var obj = _elAttr(elList, row.position + '');\n      if (obj.elArr.length) {\n        for (var _index = 0; _index < obj.elArr.length; _index++) {\n          var _item = obj.elArr[_index];\n          _item.style.color = '#fff';\n          _item.style.backgroundColor = 'red';\n        }\n      }\n      var htmlContent = '';\n      for (var _index2 = 0; _index2 < elList.length; _index2++) {\n        var _elList$_index;\n        htmlContent += ((_elList$_index = elList[_index2]) === null || _elList$_index === void 0 ? void 0 : _elList$_index.outerHTML) || '';\n      }\n      content.value = htmlContent;\n    };\n    var handleReplace = function handleReplace(row) {\n      var elList = wordRef.value.childNodes;\n      var obj = _elAttr(elList, row.position + '');\n      if (obj.elArr.length > 1) {\n        var styleStr = '';\n        for (var key in obj.styleObj) {\n          styleStr += `${key}:${obj.styleObj[key]};`;\n        }\n        for (var index = 0; index < obj.elArr.length; index++) {\n          var item = obj.elArr[index];\n          var elParent = item;\n          if (!index) {\n            elParent.insertAdjacentHTML('beforebegin', `<span style=\"${styleStr}\">${row.suggest[0]}</span>`);\n          }\n          elParent.parentNode.removeChild(elParent);\n        }\n      } else {\n        obj.elArr[0].insertAdjacentHTML('beforebegin', row.suggest[0]);\n        obj.elArr[0].parentNode.removeChild(obj.elArr[0]);\n      }\n      arrId.value.push(row.id);\n      if (arrId.value.length) {\n        var htmlContent = '';\n        var _elList = wordRef.value.childNodes;\n        for (var _index3 = 0; _index3 < _elList.length; _index3++) {\n          var _elList$_index2;\n          htmlContent += ((_elList$_index2 = _elList[_index3]) === null || _elList$_index2 === void 0 ? void 0 : _elList$_index2.outerHTML) || '';\n        }\n        content.value = htmlContent;\n      }\n    };\n    var handleClick = function handleClick(item) {\n      if (tabId.value === item.id) return;\n      tabId.value = item.id;\n      if (item.id === 'aiChat') {\n        store.commit('setAiChatCode', 'test_tool_chat');\n      }\n    };\n    onActivated(function () {\n      store.commit('setAiChatElShow', false);\n    });\n    onDeactivated(function () {\n      store.commit('setAiChatWindow', false);\n      store.commit('setAiChatCode', 'test_chat');\n      store.commit('setAiChatSetContent', '');\n      store.commit('setAiChatElShow', true);\n    });\n    onUnmounted(function () {\n      store.commit('setAiChatWindow', false);\n      store.commit('setAiChatCode', 'test_chat');\n      store.commit('setAiChatSetContent', '');\n      store.commit('setAiChatElShow', true);\n    });\n    var __returned__ = {\n      store,\n      wordRef,\n      oldId,\n      arrId,\n      content,\n      checklist,\n      tabId,\n      tabList,\n      guid,\n      typingVerification,\n      handleChat,\n      handleCopy,\n      handleDownload,\n      elAttr: _elAttr,\n      handleDetails,\n      handleReplace,\n      handleClick,\n      get api() {\n        return api;\n      },\n      ref,\n      onActivated,\n      onDeactivated,\n      onUnmounted,\n      get useStore() {\n        return useStore;\n      },\n      get whetherAiChat() {\n        return whetherAiChat;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get GlobalAiChat() {\n        return GlobalAiChat;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread", "arguments", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "asyncGeneratorStep", "_asyncToGenerator", "_next", "_throw", "api", "ref", "onActivated", "onDeactivated", "onUnmounted", "useStore", "whetherAiChat", "ElMessage", "GlobalAiChat", "__default__", "store", "wordRef", "oldId", "arrId", "content", "checklist", "tabId", "tabList", "id", "guid", "replace", "Math", "random", "toString", "typingVerification", "_ref2", "_callee", "_data$checklist", "_yield$api$typingVeri", "data", "_callee$", "_context", "text", "map", "replace_text", "handleChat", "message", "handleCopy", "textarea", "document", "createElement", "readOnly", "style", "position", "left", "body", "append<PERSON><PERSON><PERSON>", "select", "result", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "handleDownload", "commit", "code", "key", "elAttr", "elList", "el<PERSON>rr", "styleObj", "index", "item", "nodeName", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "is", "elParentStyleKey", "m", "toUpperCase", "childNodes", "obj", "concat", "_toConsumableArray", "handleDetails", "row", "oldObj", "color", "backgroundColor", "htmlContent", "_elList$_index", "outerHTML", "handleReplace", "styleStr", "insertAdjacentHTML", "suggest", "_elList$_index2", "handleClick"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/Intelligentize/DevelopContent/DevelopContent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DevelopContent\">\r\n    <div class=\"DevelopContentWord\" ref=\"wordRef\" v-html=\"content\"></div>\r\n    <div class=\"DevelopContentLeft\">\r\n      <div class=\"DevelopContentButton\">\r\n        <el-button type=\"primary\" @click=\"handleCopy\">复制</el-button>\r\n        <el-button type=\"primary\" @click=\"handleDownload\">下载</el-button>\r\n      </div>\r\n      <TinyMceEditor v-model=\"content\" :setting=\"{ height: '100%' }\" />\r\n    </div>\r\n    <div class=\"DevelopContentRight\">\r\n      <div class=\"DevelopContentTab\" v-if=\"whetherAiChat\">\r\n        <div class=\"DevelopContentTabItem\" v-for=\"item in tabList\" :key=\"item.id\"\r\n          :class=\"{ 'is-active': tabId === item.id }\" @click=\"handleClick(item)\">{{ item.name }}</div>\r\n      </div>\r\n      <div class=\"DevelopContentRightContent\" :class=\"{ 'DevelopContentRightContentAiChat': whetherAiChat }\">\r\n        <div class=\"DevelopContentCorrection\" v-show=\"tabId === 'correction'\">\r\n          <div class=\"DevelopContentButton\">\r\n            <el-button type=\"primary\" @click=\"handleChat()\">智能校正</el-button>\r\n          </div>\r\n          <div class=\"globalTable\">\r\n            <el-table :data=\"checklist\">\r\n              <el-table-column label=\"错误类型\" min-width=\"120\" prop=\"type.name\" />\r\n              <el-table-column label=\"错误内容\" min-width=\"120\">\r\n                <template #default=\"scope\">\r\n                  <el-link @click=\"handleDetails(scope.row)\" :disabled=\"arrId.includes(scope.row.id)\" type=\"primary\">{{\r\n                    scope.row.word }}</el-link>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"修改建议\" min-width=\"120\">\r\n                <template #default=\"scope\">{{ scope.row?.suggest[0] || scope.row?.explanation }}</template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"100\" fixed=\"right\" class-name=\"globalTableCustom\">\r\n                <template #default=\"scope\">\r\n                  <el-button @click=\"handleReplace(scope.row)\"\r\n                    :disabled=\"arrId.includes(scope.row.id) || !scope.row?.suggest?.length\" type=\"primary\"\r\n                    plain>替换</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </div>\r\n        <GlobalAiChat v-show=\"tabId === 'aiChat'\"></GlobalAiChat>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DevelopContent' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated, onDeactivated, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { whetherAiChat } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nimport GlobalAiChat from '../../GlobalAiChat/GlobalAiChat'\r\nconst store = useStore()\r\nconst wordRef = ref()\r\nconst oldId = ref('')\r\nconst arrId = ref([])\r\nconst content = ref('')\r\nconst checklist = ref([])\r\nconst tabId = ref('correction')\r\nconst tabList = [\r\n  { id: 'correction', name: '文本校正' },\r\n  { id: 'aiChat', name: '智能小助手' }\r\n]\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst typingVerification = async () => {\r\n  const { data } = await api.typingVerification({ text: content.value.replace(/&ldquo;/ig, '“').replace(/&rdquo;/ig, '”') })\r\n  checklist.value = data?.checklist?.map(v => ({ ...v, id: guid() })) || []\r\n  content.value = data?.replace_text || ''\r\n}\r\nconst handleChat = () => {\r\n  if (content.value === '') return ElMessage({ type: 'warning', message: '请输入内容' })\r\n  typingVerification()\r\n}\r\nconst handleCopy = () => {\r\n  if (!content.value.replace(/<[^>]*>/g, '')) return ElMessage({ message: '无复制内容', type: 'warning' })\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = content.value.replace(/<[^>]*>/g, '')\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) ElMessage({ message: '复制成功', type: 'success' })\r\n  document.body.removeChild(textarea)\r\n}\r\nconst handleDownload = () => {\r\n  store.commit('setExportWordHtmlObj', { code: 'exportWord', name: '导出内容', key: 'content', data: { content: content.value } })\r\n}\r\nconst elAttr = (elList, id) => {\r\n  let elArr = []\r\n  let styleObj = {}\r\n  for (let index = 0; index < elList.length; index++) {\r\n    const item = elList[index]\r\n    if (item.nodeName !== '#text') {\r\n      if (item.getAttribute('data-umpos') === id) {\r\n        const elParent = item.parentNode\r\n        let is = 0\r\n        while (elParent.style[is]) {\r\n          const elParentStyleKey = elParent.style[is].replace(/-([a-z])/g, (p, m) => m.toUpperCase())\r\n          styleObj[elParent.style[is]] = elParent.style[elParentStyleKey]\r\n          is++\r\n        }\r\n        elArr.push(item)\r\n      }\r\n    }\r\n    if (item.childNodes.length) {\r\n      const obj = elAttr(item.childNodes, id)\r\n      elArr = [...elArr, ...obj.elArr]\r\n      styleObj = { ...styleObj, ...obj.styleObj }\r\n    }\r\n  }\r\n\r\n  return { elArr, styleObj }\r\n}\r\nconst handleDetails = (row) => {\r\n  const elList = wordRef.value.childNodes\r\n  if (oldId.value) {\r\n    const oldObj = elAttr(elList, oldId.value)\r\n    if (oldObj.elArr.length) {\r\n      for (let index = 0; index < oldObj.elArr.length; index++) {\r\n        const item = oldObj.elArr[index]\r\n        item.style.color = ''\r\n        item.style.backgroundColor = ''\r\n      }\r\n    }\r\n  }\r\n  oldId.value = row.position + ''\r\n  const obj = elAttr(elList, row.position + '')\r\n  if (obj.elArr.length) {\r\n    for (let index = 0; index < obj.elArr.length; index++) {\r\n      const item = obj.elArr[index]\r\n      item.style.color = '#fff'\r\n      item.style.backgroundColor = 'red'\r\n    }\r\n  }\r\n  let htmlContent = ''\r\n  for (let index = 0; index < elList.length; index++) {\r\n    htmlContent += elList[index]?.outerHTML || ''\r\n  }\r\n  content.value = htmlContent\r\n}\r\nconst handleReplace = (row) => {\r\n  const elList = wordRef.value.childNodes\r\n  const obj = elAttr(elList, row.position + '')\r\n  if (obj.elArr.length > 1) {\r\n    let styleStr = ''\r\n    for (let key in obj.styleObj) {\r\n      styleStr += `${key}:${obj.styleObj[key]};`\r\n    }\r\n    for (let index = 0; index < obj.elArr.length; index++) {\r\n      const item = obj.elArr[index]\r\n      const elParent = item\r\n      if (!index) {\r\n        elParent.insertAdjacentHTML('beforebegin', `<span style=\"${styleStr}\">${row.suggest[0]}</span>`)\r\n      }\r\n      elParent.parentNode.removeChild(elParent)\r\n    }\r\n  } else {\r\n    obj.elArr[0].insertAdjacentHTML('beforebegin', row.suggest[0])\r\n    obj.elArr[0].parentNode.removeChild(obj.elArr[0])\r\n  }\r\n  arrId.value.push(row.id)\r\n  if (arrId.value.length) {\r\n    let htmlContent = ''\r\n    const elList = wordRef.value.childNodes\r\n    for (let index = 0; index < elList.length; index++) {\r\n      htmlContent += elList[index]?.outerHTML || ''\r\n    }\r\n    content.value = htmlContent\r\n  }\r\n}\r\nconst handleClick = (item) => {\r\n  if (tabId.value === item.id) return\r\n  tabId.value = item.id\r\n  if (item.id === 'aiChat') {\r\n    store.commit('setAiChatCode', 'test_tool_chat')\r\n  }\r\n}\r\nonActivated(() => {\r\n  store.commit('setAiChatElShow', false)\r\n})\r\nonDeactivated(() => {\r\n  store.commit('setAiChatWindow', false)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatSetContent', '')\r\n  store.commit('setAiChatElShow', true)\r\n})\r\nonUnmounted(() => {\r\n  store.commit('setAiChatWindow', false)\r\n  store.commit('setAiChatCode', 'test_chat')\r\n  store.commit('setAiChatSetContent', '')\r\n  store.commit('setAiChatElShow', true)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.DevelopContent {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  padding: 20px;\r\n\r\n  .DevelopContentWord {\r\n    position: fixed;\r\n    top: -999px;\r\n    left: -999px;\r\n    width: 660px;\r\n    min-height: 842pt;\r\n    box-shadow: 0px 3px 15px 1px rgba(0, 0, 0, 0.16);\r\n    margin: 20px auto;\r\n    padding: 99pt 52pt;\r\n\r\n    span {\r\n      span {\r\n        font-family: inherit;\r\n        font-size: inherit;\r\n      }\r\n    }\r\n  }\r\n\r\n  .DevelopContentButton {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    padding-bottom: var(--zy-distance-three);\r\n  }\r\n\r\n  .DevelopContentLeft {\r\n    width: 50%;\r\n    height: 100%;\r\n\r\n    .DevelopContentButton {\r\n      justify-content: flex-end;\r\n    }\r\n\r\n    .TinyMceEditor {\r\n      height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n    }\r\n  }\r\n\r\n  .DevelopContentRight {\r\n    width: 50%;\r\n    height: 100%;\r\n    padding-left: var(--zy-distance-two);\r\n\r\n    .DevelopContentTab {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding-bottom: var(--zy-distance-three);\r\n\r\n      .DevelopContentTabItem {\r\n        width: 50%;\r\n        height: var(--zy-height);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        border-top-left-radius: var(--el-border-radius-base);\r\n        border-bottom-left-radius: var(--el-border-radius-base);\r\n\r\n        &+.DevelopContentTabItem {\r\n          border-top-left-radius: 0;\r\n          border-bottom-left-radius: 0;\r\n          border-top-right-radius: var(--el-border-radius-base);\r\n          border-bottom-right-radius: var(--el-border-radius-base);\r\n        }\r\n\r\n        &.is-active {\r\n          color: #fff;\r\n          background: var(--zy-el-color-primary);\r\n          border: 1px solid var(--zy-el-color-primary);\r\n        }\r\n      }\r\n    }\r\n\r\n    .DevelopContentRightContent {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      &.DevelopContentRightContentAiChat {\r\n        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n      }\r\n    }\r\n\r\n    .DevelopContentCorrection {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .globalTable {\r\n        width: 100%;\r\n        height: calc(100% - (var(--zy-height) + var(--zy-distance-three)));\r\n      }\r\n    }\r\n\r\n    .GlobalAiChat {\r\n      padding: 0;\r\n\r\n      .GlobalAiChatClose {\r\n        display: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;+CAqDA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,QAAAvG,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAqG,qBAAA,QAAAjG,CAAA,GAAAJ,MAAA,CAAAqG,qBAAA,CAAAxG,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAkG,MAAA,WAAAvG,CAAA,WAAAC,MAAA,CAAAuG,wBAAA,CAAA1G,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAkC,KAAA,CAAA1G,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2G,cAAA5G,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAA2G,SAAA,CAAA/B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAA4G,SAAA,CAAA3G,CAAA,IAAA2G,SAAA,CAAA3G,CAAA,QAAAA,CAAA,OAAAqG,OAAA,CAAApG,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA4G,eAAA,CAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA4G,yBAAA,GAAA5G,MAAA,CAAA6G,gBAAA,CAAAhH,CAAA,EAAAG,MAAA,CAAA4G,yBAAA,CAAA9G,CAAA,KAAAsG,OAAA,CAAApG,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAuG,wBAAA,CAAAzG,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA8G,gBAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA+G,cAAA,CAAA/G,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAiH,eAAAhH,CAAA,QAAAS,CAAA,GAAAwG,YAAA,CAAAjH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAwG,aAAAjH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAwG,WAAA,kBAAAnH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAkH,MAAA,GAAAC,MAAA,EAAApH,CAAA;AAAA,SAAAqH,mBAAAjH,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAgH,kBAAAlH,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA6G,SAAA,aAAArB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAAwH,MAAAnH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,UAAApH,CAAA,cAAAoH,OAAApH,CAAA,IAAAiH,kBAAA,CAAA1G,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAiH,KAAA,EAAAC,MAAA,WAAApH,CAAA,KAAAmH,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,QAAQ,KAAK;AAClE,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,YAAY,MAAM,iCAAiC;AAT1D,IAAAC,WAAA,GAAe;EAAEjD,IAAI,EAAE;AAAiB,CAAC;;;;;IAUzC,IAAMkD,KAAK,GAAGL,QAAQ,CAAC,CAAC;IACxB,IAAMM,OAAO,GAAGV,GAAG,CAAC,CAAC;IACrB,IAAMW,KAAK,GAAGX,GAAG,CAAC,EAAE,CAAC;IACrB,IAAMY,KAAK,GAAGZ,GAAG,CAAC,EAAE,CAAC;IACrB,IAAMa,OAAO,GAAGb,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMc,SAAS,GAAGd,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMe,KAAK,GAAGf,GAAG,CAAC,YAAY,CAAC;IAC/B,IAAMgB,OAAO,GAAG,CACd;MAAEC,EAAE,EAAE,YAAY;MAAE1D,IAAI,EAAE;IAAO,CAAC,EAClC;MAAE0D,EAAE,EAAE,QAAQ;MAAE1D,IAAI,EAAE;IAAQ,CAAC,CAChC;IACD,IAAM2D,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAChI,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAG6I,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;UAAEvG,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;QAClE,OAAOuC,CAAC,CAACwG,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMC,kBAAkB;MAAA,IAAAC,KAAA,GAAA5B,iBAAA,cAAAxH,mBAAA,GAAAoF,IAAA,CAAG,SAAAiE,QAAA;QAAA,IAAAC,eAAA;QAAA,IAAAC,qBAAA,EAAAC,IAAA;QAAA,OAAAxJ,mBAAA,GAAAuB,IAAA,UAAAkI,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAA7D,IAAA,GAAA6D,QAAA,CAAAxF,IAAA;YAAA;cAAAwF,QAAA,CAAAxF,IAAA;cAAA,OACFyD,GAAG,CAACwB,kBAAkB,CAAC;gBAAEQ,IAAI,EAAElB,OAAO,CAAC/H,KAAK,CAACqI,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,GAAG;cAAE,CAAC,CAAC;YAAA;cAAAQ,qBAAA,GAAAG,QAAA,CAAA/F,IAAA;cAAlH6F,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZd,SAAS,CAAChI,KAAK,GAAG,CAAA8I,IAAI,aAAJA,IAAI,gBAAAF,eAAA,GAAJE,IAAI,CAAEd,SAAS,cAAAY,eAAA,uBAAfA,eAAA,CAAiBM,GAAG,CAAC,UAAAlH,CAAC;gBAAA,OAAAmE,aAAA,CAAAA,aAAA,KAAUnE,CAAC;kBAAEmG,EAAE,EAAEC,IAAI,CAAC;gBAAC;cAAA,CAAG,CAAC,KAAI,EAAE;cACzEL,OAAO,CAAC/H,KAAK,GAAG,CAAA8I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,YAAY,KAAI,EAAE;YAAA;YAAA;cAAA,OAAAH,QAAA,CAAA1D,IAAA;UAAA;QAAA,GAAAqD,OAAA;MAAA,CACzC;MAAA,gBAJKF,kBAAkBA,CAAA;QAAA,OAAAC,KAAA,CAAAxC,KAAA,OAAAE,SAAA;MAAA;IAAA,GAIvB;IACD,IAAMgD,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIrB,OAAO,CAAC/H,KAAK,KAAK,EAAE,EAAE,OAAOwH,SAAS,CAAC;QAAErG,IAAI,EAAE,SAAS;QAAEkI,OAAO,EAAE;MAAQ,CAAC,CAAC;MACjFZ,kBAAkB,CAAC,CAAC;IACtB,CAAC;IACD,IAAMa,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAI,CAACvB,OAAO,CAAC/H,KAAK,CAACqI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,OAAOb,SAAS,CAAC;QAAE6B,OAAO,EAAE,OAAO;QAAElI,IAAI,EAAE;MAAU,CAAC,CAAC;MACnG,IAAMoI,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACnDF,QAAQ,CAACG,QAAQ,GAAG,UAAU;MAC9BH,QAAQ,CAACI,KAAK,CAACC,QAAQ,GAAG,UAAU;MACpCL,QAAQ,CAACI,KAAK,CAACE,IAAI,GAAG,SAAS;MAC/BN,QAAQ,CAACvJ,KAAK,GAAG+H,OAAO,CAAC/H,KAAK,CAACqI,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MACtDmB,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,QAAQ,CAAC;MACnCA,QAAQ,CAACS,MAAM,CAAC,CAAC;MACjB,IAAMC,MAAM,GAAGT,QAAQ,CAACU,WAAW,CAAC,MAAM,CAAC;MAC3C,IAAID,MAAM,EAAEzC,SAAS,CAAC;QAAE6B,OAAO,EAAE,MAAM;QAAElI,IAAI,EAAE;MAAU,CAAC,CAAC;MAC3DqI,QAAQ,CAACM,IAAI,CAACK,WAAW,CAACZ,QAAQ,CAAC;IACrC,CAAC;IACD,IAAMa,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3BzC,KAAK,CAAC0C,MAAM,CAAC,sBAAsB,EAAE;QAAEC,IAAI,EAAE,YAAY;QAAE7F,IAAI,EAAE,MAAM;QAAE8F,GAAG,EAAE,SAAS;QAAEzB,IAAI,EAAE;UAAEf,OAAO,EAAEA,OAAO,CAAC/H;QAAM;MAAE,CAAC,CAAC;IAC9H,CAAC;IACD,IAAMwK,OAAM,GAAG,SAATA,MAAMA,CAAIC,MAAM,EAAEtC,EAAE,EAAK;MAC7B,IAAIuC,KAAK,GAAG,EAAE;MACd,IAAIC,QAAQ,GAAG,CAAC,CAAC;MACjB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,MAAM,CAACpG,MAAM,EAAEuG,KAAK,EAAE,EAAE;QAClD,IAAMC,IAAI,GAAGJ,MAAM,CAACG,KAAK,CAAC;QAC1B,IAAIC,IAAI,CAACC,QAAQ,KAAK,OAAO,EAAE;UAC7B,IAAID,IAAI,CAACE,YAAY,CAAC,YAAY,CAAC,KAAK5C,EAAE,EAAE;YAC1C,IAAM6C,QAAQ,GAAGH,IAAI,CAACI,UAAU;YAChC,IAAIC,EAAE,GAAG,CAAC;YACV,OAAOF,QAAQ,CAACrB,KAAK,CAACuB,EAAE,CAAC,EAAE;cACzB,IAAMC,gBAAgB,GAAGH,QAAQ,CAACrB,KAAK,CAACuB,EAAE,CAAC,CAAC7C,OAAO,CAAC,WAAW,EAAE,UAACxG,CAAC,EAAEuJ,CAAC;gBAAA,OAAKA,CAAC,CAACC,WAAW,CAAC,CAAC;cAAA,EAAC;cAC3FV,QAAQ,CAACK,QAAQ,CAACrB,KAAK,CAACuB,EAAE,CAAC,CAAC,GAAGF,QAAQ,CAACrB,KAAK,CAACwB,gBAAgB,CAAC;cAC/DD,EAAE,EAAE;YACN;YACAR,KAAK,CAAC1G,IAAI,CAAC6G,IAAI,CAAC;UAClB;QACF;QACA,IAAIA,IAAI,CAACS,UAAU,CAACjH,MAAM,EAAE;UAC1B,IAAMkH,GAAG,GAAGf,OAAM,CAACK,IAAI,CAACS,UAAU,EAAEnD,EAAE,CAAC;UACvCuC,KAAK,MAAAc,MAAA,CAAAC,kBAAA,CAAOf,KAAK,GAAAe,kBAAA,CAAKF,GAAG,CAACb,KAAK,EAAC;UAChCC,QAAQ,GAAAxE,aAAA,CAAAA,aAAA,KAAQwE,QAAQ,GAAKY,GAAG,CAACZ,QAAQ,CAAE;QAC7C;MACF;MAEA,OAAO;QAAED,KAAK;QAAEC;MAAS,CAAC;IAC5B,CAAC;IACD,IAAMe,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAK;MAC7B,IAAMlB,MAAM,GAAG7C,OAAO,CAAC5H,KAAK,CAACsL,UAAU;MACvC,IAAIzD,KAAK,CAAC7H,KAAK,EAAE;QACf,IAAM4L,MAAM,GAAGpB,OAAM,CAACC,MAAM,EAAE5C,KAAK,CAAC7H,KAAK,CAAC;QAC1C,IAAI4L,MAAM,CAAClB,KAAK,CAACrG,MAAM,EAAE;UACvB,KAAK,IAAIuG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGgB,MAAM,CAAClB,KAAK,CAACrG,MAAM,EAAEuG,KAAK,EAAE,EAAE;YACxD,IAAMC,IAAI,GAAGe,MAAM,CAAClB,KAAK,CAACE,KAAK,CAAC;YAChCC,IAAI,CAAClB,KAAK,CAACkC,KAAK,GAAG,EAAE;YACrBhB,IAAI,CAAClB,KAAK,CAACmC,eAAe,GAAG,EAAE;UACjC;QACF;MACF;MACAjE,KAAK,CAAC7H,KAAK,GAAG2L,GAAG,CAAC/B,QAAQ,GAAG,EAAE;MAC/B,IAAM2B,GAAG,GAAGf,OAAM,CAACC,MAAM,EAAEkB,GAAG,CAAC/B,QAAQ,GAAG,EAAE,CAAC;MAC7C,IAAI2B,GAAG,CAACb,KAAK,CAACrG,MAAM,EAAE;QACpB,KAAK,IAAIuG,MAAK,GAAG,CAAC,EAAEA,MAAK,GAAGW,GAAG,CAACb,KAAK,CAACrG,MAAM,EAAEuG,MAAK,EAAE,EAAE;UACrD,IAAMC,KAAI,GAAGU,GAAG,CAACb,KAAK,CAACE,MAAK,CAAC;UAC7BC,KAAI,CAAClB,KAAK,CAACkC,KAAK,GAAG,MAAM;UACzBhB,KAAI,CAAClB,KAAK,CAACmC,eAAe,GAAG,KAAK;QACpC;MACF;MACA,IAAIC,WAAW,GAAG,EAAE;MACpB,KAAK,IAAInB,OAAK,GAAG,CAAC,EAAEA,OAAK,GAAGH,MAAM,CAACpG,MAAM,EAAEuG,OAAK,EAAE,EAAE;QAAA,IAAAoB,cAAA;QAClDD,WAAW,IAAI,EAAAC,cAAA,GAAAvB,MAAM,CAACG,OAAK,CAAC,cAAAoB,cAAA,uBAAbA,cAAA,CAAeC,SAAS,KAAI,EAAE;MAC/C;MACAlE,OAAO,CAAC/H,KAAK,GAAG+L,WAAW;IAC7B,CAAC;IACD,IAAMG,aAAa,GAAG,SAAhBA,aAAaA,CAAIP,GAAG,EAAK;MAC7B,IAAMlB,MAAM,GAAG7C,OAAO,CAAC5H,KAAK,CAACsL,UAAU;MACvC,IAAMC,GAAG,GAAGf,OAAM,CAACC,MAAM,EAAEkB,GAAG,CAAC/B,QAAQ,GAAG,EAAE,CAAC;MAC7C,IAAI2B,GAAG,CAACb,KAAK,CAACrG,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI8H,QAAQ,GAAG,EAAE;QACjB,KAAK,IAAI5B,GAAG,IAAIgB,GAAG,CAACZ,QAAQ,EAAE;UAC5BwB,QAAQ,IAAI,GAAG5B,GAAG,IAAIgB,GAAG,CAACZ,QAAQ,CAACJ,GAAG,CAAC,GAAG;QAC5C;QACA,KAAK,IAAIK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGW,GAAG,CAACb,KAAK,CAACrG,MAAM,EAAEuG,KAAK,EAAE,EAAE;UACrD,IAAMC,IAAI,GAAGU,GAAG,CAACb,KAAK,CAACE,KAAK,CAAC;UAC7B,IAAMI,QAAQ,GAAGH,IAAI;UACrB,IAAI,CAACD,KAAK,EAAE;YACVI,QAAQ,CAACoB,kBAAkB,CAAC,aAAa,EAAE,gBAAgBD,QAAQ,KAAKR,GAAG,CAACU,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;UAClG;UACArB,QAAQ,CAACC,UAAU,CAACd,WAAW,CAACa,QAAQ,CAAC;QAC3C;MACF,CAAC,MAAM;QACLO,GAAG,CAACb,KAAK,CAAC,CAAC,CAAC,CAAC0B,kBAAkB,CAAC,aAAa,EAAET,GAAG,CAACU,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9Dd,GAAG,CAACb,KAAK,CAAC,CAAC,CAAC,CAACO,UAAU,CAACd,WAAW,CAACoB,GAAG,CAACb,KAAK,CAAC,CAAC,CAAC,CAAC;MACnD;MACA5C,KAAK,CAAC9H,KAAK,CAACgE,IAAI,CAAC2H,GAAG,CAACxD,EAAE,CAAC;MACxB,IAAIL,KAAK,CAAC9H,KAAK,CAACqE,MAAM,EAAE;QACtB,IAAI0H,WAAW,GAAG,EAAE;QACpB,IAAMtB,OAAM,GAAG7C,OAAO,CAAC5H,KAAK,CAACsL,UAAU;QACvC,KAAK,IAAIV,OAAK,GAAG,CAAC,EAAEA,OAAK,GAAGH,OAAM,CAACpG,MAAM,EAAEuG,OAAK,EAAE,EAAE;UAAA,IAAA0B,eAAA;UAClDP,WAAW,IAAI,EAAAO,eAAA,GAAA7B,OAAM,CAACG,OAAK,CAAC,cAAA0B,eAAA,uBAAbA,eAAA,CAAeL,SAAS,KAAI,EAAE;QAC/C;QACAlE,OAAO,CAAC/H,KAAK,GAAG+L,WAAW;MAC7B;IACF,CAAC;IACD,IAAMQ,WAAW,GAAG,SAAdA,WAAWA,CAAI1B,IAAI,EAAK;MAC5B,IAAI5C,KAAK,CAACjI,KAAK,KAAK6K,IAAI,CAAC1C,EAAE,EAAE;MAC7BF,KAAK,CAACjI,KAAK,GAAG6K,IAAI,CAAC1C,EAAE;MACrB,IAAI0C,IAAI,CAAC1C,EAAE,KAAK,QAAQ,EAAE;QACxBR,KAAK,CAAC0C,MAAM,CAAC,eAAe,EAAE,gBAAgB,CAAC;MACjD;IACF,CAAC;IACDlD,WAAW,CAAC,YAAM;MAChBQ,KAAK,CAAC0C,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;IACxC,CAAC,CAAC;IACFjD,aAAa,CAAC,YAAM;MAClBO,KAAK,CAAC0C,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;MACtC1C,KAAK,CAAC0C,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC;MAC1C1C,KAAK,CAAC0C,MAAM,CAAC,qBAAqB,EAAE,EAAE,CAAC;MACvC1C,KAAK,CAAC0C,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC;IACFhD,WAAW,CAAC,YAAM;MAChBM,KAAK,CAAC0C,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC;MACtC1C,KAAK,CAAC0C,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC;MAC1C1C,KAAK,CAAC0C,MAAM,CAAC,qBAAqB,EAAE,EAAE,CAAC;MACvC1C,KAAK,CAAC0C,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC;IACvC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}