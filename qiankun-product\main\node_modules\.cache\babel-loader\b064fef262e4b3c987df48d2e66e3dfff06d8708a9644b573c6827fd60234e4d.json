{"ast": null, "code": "import { ref } from 'vue';\nvar __default__ = {\n  name: 'VersionComparisonAiFileInfo'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    name: {\n      type: String,\n      default: ''\n    },\n    comparisonHtml: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['loadCallback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var loading = ref(false);\n    var loadingText = ref('');\n    var __returned__ = {\n      props,\n      emit,\n      loading,\n      loadingText,\n      ref\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "__default__", "name", "props", "__props", "emit", "__emit", "loading", "loadingText"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/Intelligentize/VersionComparison/VersionComparisonAiFileInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"VersionComparisonAiFileInfo\"\r\n       v-loading=\"loading\"\r\n       :lement-loading-text=\"loadingText\">\r\n    <div class=\"VersionComparisonFileInfoHead\">\r\n      <div class=\"VersionComparisonFileInfoHeadText\">{{ props.name }}</div>\r\n    </div>\r\n    <el-scrollbar class=\"VersionComparisonFileInfoScrollbar\">\r\n      <div class=\"VersionComparisonFileInfoWord\"\r\n           v-html=\"props.comparisonHtml\"></div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VersionComparisonAiFileInfo' }\r\n</script>\r\n<script setup>\r\nimport { ref} from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' }, name: { type: String, default: '' }, comparisonHtml: { type: String, default: '' } })\r\nconst emit = defineEmits(['loadCallback'])\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n</script>\r\n<style lang=\"scss\">\r\n.VersionComparisonAiFileInfo {\r\n  width: 100%;\r\n  // height: calc(50% - 10px);\r\n  // margin-bottom: 20px;\r\n  border: 1px solid var(--zy-el-border-color-lighter);\r\n  border-radius: 8px;\r\n  box-shadow: var(--zy-el-box-shadow);\r\n\r\n  .VersionComparisonFileInfoHead {\r\n    width: 100%;\r\n    height: 50px;\r\n    color: #fff;\r\n    background-image: linear-gradient(72deg, var(--zy-el-color-primary), var(--zy-el-color-primary-light-9));\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    padding: 4px 20px;\r\n    border-top-left-radius: 8px;\r\n    border-top-right-radius: 8px;\r\n\r\n\r\n    .VersionComparisonFileInfoHeadText {\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n  }\r\n\r\n  .VersionComparisonFileInfoScrollbar {\r\n    width: 100%;\r\n    height: calc(100% - 60px);\r\n\r\n    .VersionComparisonFileInfoWord {\r\n      padding: 0 20px;\r\n      div p span {\r\n        font-size: 16pt !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAiBA,SAASA,GAAG,QAAO,KAAK;AAHxB,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAA8B,CAAC;;;;;;;;;;;;;;;;;;;;;IAItD,IAAMC,KAAK,GAAGC,OAAsI;IACpJ,IAAMC,IAAI,GAAGC,MAA6B;IAC1C,IAAMC,OAAO,GAAGP,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAMQ,WAAW,GAAGR,GAAG,CAAC,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}