{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, computed, onMounted } from 'vue';\nimport { DocumentAdd, Position } from '@element-plus/icons-vue';\nexport default {\n  __name: 'GlobalAiChatEditor',\n  props: {\n    modelValue: [String, Number],\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['update:modelValue', 'send', 'stop', 'uploadCallback', 'fileCallback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    var props = __props;\n    var fileList = ref([]);\n    var fileData = ref([]);\n    var disabled = computed(function () {\n      return props.disabled;\n    });\n    var emit = __emit;\n    // 编辑器的 DOM 引用\n    var editorRef = ref(null);\n    // 存储编辑器内容\n    // const content = ref('')\n    var content = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(value) {\n        emit('update:modelValue', value);\n      }\n    });\n    // 跟踪是否已经进入占位符编辑模式\n    var isEditingPlaceholder = '';\n    var isMacText = function isMacText() {\n      var userAgent = navigator.userAgent.toLowerCase();\n      return userAgent.includes('macintosh') || userAgent.includes('mac os x');\n    };\n    var handleSendMessage = function handleSendMessage() {\n      if (!content.value.replace(/^\\s+|\\s+$/g, '')) return;\n      emit('send', content.value);\n      editorRef.value.innerHTML = '';\n      handleInput();\n    };\n    var handleStopMessage = function handleStopMessage() {\n      emit('stop', content.value);\n    };\n    // 初始化编辑器\n    onMounted(function () {\n      var editorEl = editorRef.value;\n      if (editorEl) {\n        editorEl.innerHTML = ''; // 清空初始内容\n        handleInput();\n      }\n    });\n    // 检查当前光标是否在占位符内部\n    var isCursorInsidePlaceholder = function isCursorInsidePlaceholder() {\n      var selection = window.getSelection();\n      if (!selection.rangeCount) return false;\n      var range = selection.getRangeAt(0);\n      var node = range.startContainer;\n      // 向上遍历 DOM 树，检查是否在占位符内部\n      while (node && node !== editorRef.value) {\n        if (node.classList && node.classList.contains('AiChatEditorPlaceholder')) return true; // 光标在占位符内部\n        node = node.parentNode;\n      }\n      return false; // 光标不在占位符内部\n    };\n    var createElPlaceholder = function createElPlaceholder(data) {\n      var elData = [];\n      for (var index = 0; index < data.length; index++) {\n        var item = data[index];\n        if (item.type) {\n          // 创建占位符容器\n          var placeholderContainer = document.createElement('span');\n          placeholderContainer.className = 'AiChatEditorPlaceholderContainer';\n          // 创建占位符标签\n          var placeholder = document.createElement('span');\n          placeholder.className = 'AiChatEditorPlaceholder';\n          placeholder.contentEditable = 'false'; // 默认不可编辑\n          placeholder.textContent = item.value;\n          placeholder.dataset.id = guid();\n          placeholderContainer.appendChild(placeholder);\n          elData.push(placeholderContainer);\n        } else {\n          elData.push(document.createTextNode(item.value));\n        }\n      }\n      return elData;\n    };\n    // 插入带样式的占位符\n    var handleInsertPlaceholder = function handleInsertPlaceholder(elDataText) {\n      if (!elDataText.length) return;\n      var editorEl = editorRef.value;\n      if (!editorEl) return;\n      editorEl.focus();\n      // 检查光标是否在占位符内部\n      if (isCursorInsidePlaceholder()) return; // 不能在占位符内部插入占位符！\n      // 获取当前选中内容\n      var selection = window.getSelection();\n      if (!selection.rangeCount) return;\n      var range = selection.getRangeAt(0);\n      // 如果选中了内容，先删除选中内容\n      if (!range.collapsed) range.deleteContents();\n      // 创建占位符容器\n      var placeholderContainer = null;\n      var elData = createElPlaceholder(elDataText);\n      for (var index = elData.length - 1; index >= 0; index--) {\n        var item = elData[index];\n        range.insertNode(item);\n        if (index === elData.length - 1) {\n          placeholderContainer = item;\n        }\n      }\n      // 插入占位符容器\n      // range.insertNode(placeholderContainer)\n      // 创建一个零宽空格节点\n      var space = document.createElement('br'); // 插入 <br> 标签\n      // const space = document.createTextNode('\\u200B') // 零宽空格\n      // 将零宽空格插入到占位符容器的后面\n      range.setStartAfter(placeholderContainer);\n      range.setEndAfter(placeholderContainer);\n      range.insertNode(space);\n      // 将光标移动到零宽空格后面\n      var newRange = document.createRange();\n      newRange.setStartAfter(space);\n      newRange.setEndAfter(space);\n      selection.removeAllRanges();\n      selection.addRange(newRange);\n      // 聚焦编辑器\n      editorEl.focus();\n      handleInput();\n    };\n    // 监听编辑器内容变化\n    var handleInput = function handleInput() {\n      var editorEl = editorRef.value;\n      if (editorEl) {\n        var _editorEl$innerHTML;\n        content.value = ((_editorEl$innerHTML = editorEl.innerHTML) === null || _editorEl$innerHTML === void 0 || (_editorEl$innerHTML = _editorEl$innerHTML.replace(/<br>/g, '\\n')) === null || _editorEl$innerHTML === void 0 ? void 0 : _editorEl$innerHTML.replace(/<[^>]*>/g, '')) || '';\n      }\n    };\n    // 处理编辑器点击事件\n    var handleEditorClick = function handleEditorClick(event) {\n      var target = event.target;\n      // 点击占位符文字时\n      if (target.classList.contains('AiChatEditorPlaceholder')) {\n        var elId = target.getAttribute('data-id');\n        if (isEditingPlaceholder !== elId) {\n          isEditingPlaceholder = elId;\n          // 第一次点击，进入编辑模式\n          target.contentEditable = 'true';\n          target.focus();\n          // 选中整个占位符内容\n          var range = document.createRange();\n          range.selectNodeContents(target);\n          var selection = window.getSelection();\n          selection.removeAllRanges();\n          selection.addRange(range);\n        } else {\n          // 再次点击，将光标设置到点击的位置\n          var clickOffset = getClickOffset(target, event);\n          var _selection = window.getSelection();\n          var _range = document.createRange();\n          // 设置光标到点击的位置\n          _range.setStart(target.firstChild, clickOffset);\n          _range.setEnd(target.firstChild, clickOffset);\n          _selection.removeAllRanges();\n          _selection.addRange(_range);\n        }\n      } else {\n        // 点击其他区域时退出编辑模式\n        isEditingPlaceholder = '';\n      }\n    };\n    // 获取点击位置在文本节点中的偏移量\n    var getClickOffset = function getClickOffset(element, event) {\n      var textNode = element.firstChild; // 占位符的文本节点\n      var range = document.createRange();\n      range.selectNodeContents(textNode);\n      var rects = range.getClientRects(); // 获取文本节点的所有矩形区域\n      var clickX = event.clientX; // 点击的 X 坐标\n      // 遍历所有矩形区域，找到点击位置对应的偏移量\n      for (var i = 0; i < rects.length; i++) {\n        var rect = rects[i];\n        if (clickX >= rect.left && clickX <= rect.right) {\n          // 计算点击位置在文本节点中的偏移量\n          var relativeX = clickX - rect.left;\n          var charWidth = rect.width / textNode.length;\n          return Math.min(Math.floor(relativeX / charWidth), textNode.length);\n        }\n      }\n      // 如果点击位置超出文本节点范围，返回文本节点的长度\n      return textNode.length;\n    };\n    var hasBrAtEnd = function hasBrAtEnd(el) {\n      var lastNode = el.lastElementChild;\n      return lastNode && lastNode.tagName && lastNode.tagName.toLowerCase() === 'br';\n    };\n    // 处理键盘事件\n    var handleKeyDown = function handleKeyDown(event) {\n      var target = event.target;\n      if (event.keyCode == 13) {\n        event.preventDefault(); // 阻止默认行为\n        if (!event.ctrlKey && !event.metaKey) {\n          handleSendMessage();\n        } else {\n          // 处理换行回车键，避免插入 <div> 或 <p>\n          var selection = window.getSelection();\n          if (selection.rangeCount > 0) {\n            var range = selection.getRangeAt(0);\n            var clonedRange = range.cloneRange();\n            clonedRange.selectNodeContents(target);\n            clonedRange.setEnd(range.endContainer, range.endOffset);\n            var text = clonedRange.toString();\n            var isPosition = text.length === editorRef.value.textContent.length;\n            var br = document.createElement('br'); // 插入 <br> 标签\n            var brOne = document.createElement('br'); // 插入 <br> 标签\n            range.deleteContents(); // 删除选中的内容（如果有）\n            if (isPosition && !hasBrAtEnd(target)) range.insertNode(brOne); // 插入 <br>\n            range.insertNode(br); // 插入 <br>\n            range.setStartAfter(br); // 将光标移动到 <br> 后面\n            range.setEndAfter(br);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            handleInput();\n          }\n        }\n      }\n    };\n    // const handleKeyDownPlaceholder = (event) => {\n    //   console.log(event)\n    //   const target = event.target\n    //   // 处理占位符内部的删除行为\n    //   if (target.classList.contains('AiChatEditorPlaceholder')) {\n    //     if (event.key === 'Backspace' || event.key === 'Delete') {\n    //       // 如果占位符内容为空，删除整个占位符\n    //       if (target.textContent === '') {\n    //         event.preventDefault()\n    //         const placeholderContainer = target.parentNode\n    //         if (placeholderContainer && placeholderContainer.parentNode) {\n    //           placeholderContainer.parentNode.removeChild(placeholderContainer)\n    //         }\n    //       }\n    //     } else if (event.key === 'Enter') {\n    //       // 如果按下 Enter 键，退出编辑模式\n    //       event.preventDefault()\n    //       target.contentEditable = 'false'\n    //       const selection = window.getSelection()\n    //       const range = document.createRange()\n    //       range.setStartAfter(target)\n    //       range.setEndAfter(target)\n    //       selection.removeAllRanges()\n    //       selection.addRange(range)\n    //       isEditingPlaceholder = false // 退出编辑模式\n    //     }\n    //   }\n    // }\n    // 处理粘贴事件\n    var handlePaste = function handlePaste(event) {\n      event.preventDefault(); // 阻止默认粘贴行为\n      // 获取粘贴的纯文本内容\n      var text = (event.clipboardData || window.clipboardData).getData('text/plain');\n      // 将纯文本插入到光标位置\n      var selection = window.getSelection();\n      if (!selection.rangeCount) return;\n      var range = selection.getRangeAt(0);\n      range.deleteContents(); // 删除选中的内容（如果有）\n      // 插入纯文本\n      var textNode = document.createTextNode(text);\n      range.insertNode(textNode);\n      // 将光标移动到插入内容的后面\n      var newRange = document.createRange();\n      newRange.setStartAfter(textNode);\n      newRange.setEndAfter(textNode);\n      selection.removeAllRanges();\n      selection.addRange(newRange);\n      handleInput();\n    };\n    var handleSetFile = function handleSetFile(data) {\n      fileData.value = data;\n      emit('fileCallback', fileData.value);\n    };\n    var handleSetContent = function handleSetContent(contentData) {\n      editorRef.value.innerHTML = contentData;\n      handleInput();\n    };\n    var handleAddContent = function handleAddContent(contentData) {\n      editorRef.value.innerHTML = editorRef.value.innerHTML + ((contentData === null || contentData === void 0 ? void 0 : contentData.replace(/<[^>]*>/g, '')) || '');\n      handleInput();\n    };\n    /**\r\n     * 限制上传附件的文件类型\r\n     */\n    var handleFile = function handleFile() {\n      // const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\n      // const isShow = ['doc', 'docx', 'pdf'].includes(fileType)\n      // if (!isShow) { ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!` }) }\n      // return isShow\n      return true;\n    };\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var onUploadProgress = function onUploadProgress(progressEvent, uid) {\n      var _progressEvent$event;\n      if (progressEvent !== null && progressEvent !== void 0 && (_progressEvent$event = progressEvent.event) !== null && _progressEvent$event !== void 0 && _progressEvent$event.lengthComputable) {\n        var progress = (progressEvent.loaded / progressEvent.total * 100).toFixed(0);\n        fileList.value.forEach(function (item) {\n          if (item.uid === uid) {\n            item.progress = parseInt(progress);\n          }\n        });\n      }\n    };\n    /**\r\n     * 上传附件请求方法\r\n     */\n    var fileUpload = function fileUpload(file) {\n      var param = new FormData();\n      param.append('file', file.file);\n      globalUpload(param, guid(), file.file.name, file.file.uid, file.file.size);\n    };\n    var globalUpload = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(params, uid, name, time, size) {\n        var fileType, _yield$api$globalUplo, data, newData, newSortData, newSucceedData, index, item;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              fileType = name.substring(name.lastIndexOf('.') + 1);\n              fileList.value.push({\n                uid,\n                fileName: name,\n                fileType,\n                fileSize: size,\n                progress: 0\n              });\n              emit('uploadCallback', fileList.value);\n              _context.next = 6;\n              return api.globalUpload(params, onUploadProgress, uid);\n            case 6:\n              _yield$api$globalUplo = _context.sent;\n              data = _yield$api$globalUplo.data;\n              fileList.value = fileList.value.filter(function (item) {\n                return item.uid !== uid;\n              });\n              emit('uploadCallback', fileList.value);\n              newData = [];\n              newSortData = [];\n              newSucceedData = [].concat(_toConsumableArray(fileData.value), [_objectSpread(_objectSpread({}, data), {}, {\n                uid: uid,\n                time: time,\n                progress: 100\n              })]);\n              for (index = 0; index < newSucceedData.length; index++) {\n                item = newSucceedData[index];\n                if (item.time) {\n                  newSortData.push(item);\n                } else {\n                  newData.push(item);\n                }\n              }\n              fileData.value = [].concat(newData, _toConsumableArray(newSortData.sort(function (a, b) {\n                return a.time - b.time;\n              })));\n              emit('fileCallback', fileData.value);\n              _context.next = 22;\n              break;\n            case 18:\n              _context.prev = 18;\n              _context.t0 = _context[\"catch\"](0);\n              fileList.value = fileList.value.filter(function (item) {\n                return item.uid !== uid;\n              });\n              emit('uploadCallback', fileList.value);\n            case 22:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 18]]);\n      }));\n      return function globalUpload(_x, _x2, _x3, _x4, _x5) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    __expose({\n      editorRef: editorRef.value,\n      handleSetFile,\n      handleSetContent,\n      handleAddContent,\n      handleInsertPlaceholder\n    });\n    var __returned__ = {\n      props,\n      fileList,\n      fileData,\n      disabled,\n      emit,\n      editorRef,\n      content,\n      get isEditingPlaceholder() {\n        return isEditingPlaceholder;\n      },\n      set isEditingPlaceholder(v) {\n        isEditingPlaceholder = v;\n      },\n      isMacText,\n      handleSendMessage,\n      handleStopMessage,\n      isCursorInsidePlaceholder,\n      createElPlaceholder,\n      handleInsertPlaceholder,\n      handleInput,\n      handleEditorClick,\n      getClickOffset,\n      hasBrAtEnd,\n      handleKeyDown,\n      handlePaste,\n      handleSetFile,\n      handleSetContent,\n      handleAddContent,\n      handleFile,\n      guid,\n      onUploadProgress,\n      fileUpload,\n      globalUpload,\n      get api() {\n        return api;\n      },\n      ref,\n      computed,\n      onMounted,\n      get DocumentAdd() {\n        return DocumentAdd;\n      },\n      get Position() {\n        return Position;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "apply", "_objectSpread", "arguments", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "Array", "from", "test", "isArray", "asyncGeneratorStep", "_asyncToGenerator", "_next", "_throw", "api", "ref", "computed", "onMounted", "DocumentAdd", "Position", "props", "__props", "fileList", "fileData", "disabled", "emit", "__emit", "editor<PERSON><PERSON>", "content", "get", "modelValue", "set", "isEditingPlaceholder", "isMacText", "userAgent", "navigator", "toLowerCase", "includes", "handleSendMessage", "replace", "innerHTML", "handleInput", "handleStopMessage", "editor<PERSON><PERSON>", "isCursorInsidePlaceholder", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "node", "startContainer", "classList", "contains", "parentNode", "createElPlaceholder", "data", "elData", "index", "item", "placeholder<PERSON><PERSON><PERSON>", "document", "createElement", "className", "placeholder", "contentEditable", "textContent", "dataset", "id", "guid", "append<PERSON><PERSON><PERSON>", "createTextNode", "handleInsertPlaceholder", "elDataText", "focus", "collapsed", "deleteContents", "insertNode", "space", "setStartAfter", "setEndAfter", "newRange", "createRange", "removeAllRanges", "addRange", "_editorEl$innerHTML", "handleEditorClick", "event", "target", "elId", "getAttribute", "selectNodeContents", "clickOffset", "getClickOffset", "setStart", "<PERSON><PERSON><PERSON><PERSON>", "setEnd", "element", "textNode", "rects", "getClientRects", "clickX", "clientX", "rect", "left", "right", "relativeX", "char<PERSON><PERSON><PERSON>", "width", "Math", "min", "floor", "hasBrAtEnd", "el", "lastNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tagName", "handleKeyDown", "keyCode", "preventDefault", "ctrl<PERSON>ey", "metaKey", "clonedRang<PERSON>", "cloneRange", "endContainer", "endOffset", "text", "isPosition", "br", "brOne", "handlePaste", "clipboardData", "getData", "handleSetFile", "handleSetContent", "contentData", "handleAddContent", "handleFile", "random", "onUploadProgress", "progressEvent", "uid", "_progressEvent$event", "lengthComputable", "progress", "loaded", "total", "toFixed", "parseInt", "fileUpload", "file", "param", "FormData", "append", "globalUpload", "size", "_ref2", "_callee", "params", "time", "fileType", "_yield$api$globalUplo", "newData", "newSortData", "newSucceedData", "_callee$", "_context", "substring", "lastIndexOf", "fileName", "fileSize", "concat", "sort", "b", "t0", "_x", "_x2", "_x3", "_x4", "_x5", "__expose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalAiChat/GlobalAiChatEditor.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChatEditorContainer\">\r\n    <el-scrollbar always class=\"GlobalAiChatEditorScroll\">\r\n      <div\r\n        ref=\"editorRef\"\r\n        contenteditable=\"true\"\r\n        class=\"GlobalAiChatEditor\"\r\n        @input=\"handleInput\"\r\n        @click=\"handleEditorClick\"\r\n        @keydown=\"handleKeyDown\"\r\n        @paste=\"handlePaste\"></div>\r\n    </el-scrollbar>\r\n    <div class=\"GlobalAiChatEditorBotton\">\r\n      <div class=\"GlobalAiChatEditorUpload\">\r\n        <el-upload action=\"/\" :before-upload=\"handleFile\" :http-request=\"fileUpload\" :show-file-list=\"false\" multiple>\r\n          <el-button :icon=\"DocumentAdd\">上传资料</el-button>\r\n        </el-upload>\r\n      </div>\r\n      <div class=\"GlobalAiChatEditorFlex\">\r\n        <span>{{ isMacText() ? 'Command + Enter 换行' : 'Ctrl + Enter 换行' }}</span>\r\n        <el-button type=\"primary\" :icon=\"Position\" circle @click=\"handleSendMessage()\" v-if=\"!disabled\"></el-button>\r\n        <el-button type=\"primary\" circle @click=\"handleStopMessage()\" v-if=\"disabled\">\r\n          <span class=\"GlobalAiChatEditorStopStream\"></span>\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { DocumentAdd, Position } from '@element-plus/icons-vue'\r\nconst props = defineProps({\r\n  modelValue: [String, Number],\r\n  disabled: { type: Boolean, default: false }\r\n})\r\nconst fileList = ref([])\r\nconst fileData = ref([])\r\nconst disabled = computed(() => props.disabled)\r\nconst emit = defineEmits(['update:modelValue', 'send', 'stop', 'uploadCallback', 'fileCallback'])\r\n// 编辑器的 DOM 引用\r\nconst editorRef = ref(null)\r\n// 存储编辑器内容\r\n// const content = ref('')\r\nconst content = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\n// 跟踪是否已经进入占位符编辑模式\r\nlet isEditingPlaceholder = ''\r\nconst isMacText = () => {\r\n  const userAgent = navigator.userAgent.toLowerCase()\r\n  return userAgent.includes('macintosh') || userAgent.includes('mac os x')\r\n}\r\nconst handleSendMessage = () => {\r\n  if (!content.value.replace(/^\\s+|\\s+$/g, '')) return\r\n  emit('send', content.value)\r\n  editorRef.value.innerHTML = ''\r\n  handleInput()\r\n}\r\nconst handleStopMessage = () => {\r\n  emit('stop', content.value)\r\n}\r\n// 初始化编辑器\r\nonMounted(() => {\r\n  const editorEl = editorRef.value\r\n  if (editorEl) {\r\n    editorEl.innerHTML = '' // 清空初始内容\r\n    handleInput()\r\n  }\r\n})\r\n// 检查当前光标是否在占位符内部\r\nconst isCursorInsidePlaceholder = () => {\r\n  const selection = window.getSelection()\r\n  if (!selection.rangeCount) return false\r\n  const range = selection.getRangeAt(0)\r\n  let node = range.startContainer\r\n  // 向上遍历 DOM 树，检查是否在占位符内部\r\n  while (node && node !== editorRef.value) {\r\n    if (node.classList && node.classList.contains('AiChatEditorPlaceholder')) return true // 光标在占位符内部\r\n    node = node.parentNode\r\n  }\r\n  return false // 光标不在占位符内部\r\n}\r\nconst createElPlaceholder = (data) => {\r\n  const elData = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    if (item.type) {\r\n      // 创建占位符容器\r\n      const placeholderContainer = document.createElement('span')\r\n      placeholderContainer.className = 'AiChatEditorPlaceholderContainer'\r\n      // 创建占位符标签\r\n      const placeholder = document.createElement('span')\r\n      placeholder.className = 'AiChatEditorPlaceholder'\r\n      placeholder.contentEditable = 'false' // 默认不可编辑\r\n      placeholder.textContent = item.value\r\n      placeholder.dataset.id = guid()\r\n      placeholderContainer.appendChild(placeholder)\r\n      elData.push(placeholderContainer)\r\n    } else {\r\n      elData.push(document.createTextNode(item.value))\r\n    }\r\n  }\r\n  return elData\r\n}\r\n// 插入带样式的占位符\r\nconst handleInsertPlaceholder = (elDataText) => {\r\n  if (!elDataText.length) return\r\n  const editorEl = editorRef.value\r\n  if (!editorEl) return\r\n  editorEl.focus()\r\n  // 检查光标是否在占位符内部\r\n  if (isCursorInsidePlaceholder()) return // 不能在占位符内部插入占位符！\r\n  // 获取当前选中内容\r\n  const selection = window.getSelection()\r\n  if (!selection.rangeCount) return\r\n  const range = selection.getRangeAt(0)\r\n  // 如果选中了内容，先删除选中内容\r\n  if (!range.collapsed) range.deleteContents()\r\n  // 创建占位符容器\r\n  let placeholderContainer = null\r\n  const elData = createElPlaceholder(elDataText)\r\n  for (let index = elData.length - 1; index >= 0; index--) {\r\n    const item = elData[index]\r\n    range.insertNode(item)\r\n    if (index === elData.length - 1) {\r\n      placeholderContainer = item\r\n    }\r\n  }\r\n  // 插入占位符容器\r\n  // range.insertNode(placeholderContainer)\r\n  // 创建一个零宽空格节点\r\n  const space = document.createElement('br') // 插入 <br> 标签\r\n  // const space = document.createTextNode('\\u200B') // 零宽空格\r\n  // 将零宽空格插入到占位符容器的后面\r\n  range.setStartAfter(placeholderContainer)\r\n  range.setEndAfter(placeholderContainer)\r\n  range.insertNode(space)\r\n  // 将光标移动到零宽空格后面\r\n  const newRange = document.createRange()\r\n  newRange.setStartAfter(space)\r\n  newRange.setEndAfter(space)\r\n  selection.removeAllRanges()\r\n  selection.addRange(newRange)\r\n  // 聚焦编辑器\r\n  editorEl.focus()\r\n  handleInput()\r\n}\r\n// 监听编辑器内容变化\r\nconst handleInput = () => {\r\n  const editorEl = editorRef.value\r\n  if (editorEl) {\r\n    content.value = editorEl.innerHTML?.replace(/<br>/g, '\\n')?.replace(/<[^>]*>/g, '') || ''\r\n  }\r\n}\r\n// 处理编辑器点击事件\r\nconst handleEditorClick = (event) => {\r\n  const target = event.target\r\n  // 点击占位符文字时\r\n  if (target.classList.contains('AiChatEditorPlaceholder')) {\r\n    const elId = target.getAttribute('data-id')\r\n    if (isEditingPlaceholder !== elId) {\r\n      isEditingPlaceholder = elId\r\n      // 第一次点击，进入编辑模式\r\n      target.contentEditable = 'true'\r\n      target.focus()\r\n      // 选中整个占位符内容\r\n      const range = document.createRange()\r\n      range.selectNodeContents(target)\r\n      const selection = window.getSelection()\r\n      selection.removeAllRanges()\r\n      selection.addRange(range)\r\n    } else {\r\n      // 再次点击，将光标设置到点击的位置\r\n      const clickOffset = getClickOffset(target, event)\r\n      const selection = window.getSelection()\r\n      const range = document.createRange()\r\n      // 设置光标到点击的位置\r\n      range.setStart(target.firstChild, clickOffset)\r\n      range.setEnd(target.firstChild, clickOffset)\r\n      selection.removeAllRanges()\r\n      selection.addRange(range)\r\n    }\r\n  } else {\r\n    // 点击其他区域时退出编辑模式\r\n    isEditingPlaceholder = ''\r\n  }\r\n}\r\n// 获取点击位置在文本节点中的偏移量\r\nconst getClickOffset = (element, event) => {\r\n  const textNode = element.firstChild // 占位符的文本节点\r\n  const range = document.createRange()\r\n  range.selectNodeContents(textNode)\r\n  const rects = range.getClientRects() // 获取文本节点的所有矩形区域\r\n  const clickX = event.clientX // 点击的 X 坐标\r\n  // 遍历所有矩形区域，找到点击位置对应的偏移量\r\n  for (let i = 0; i < rects.length; i++) {\r\n    const rect = rects[i]\r\n    if (clickX >= rect.left && clickX <= rect.right) {\r\n      // 计算点击位置在文本节点中的偏移量\r\n      const relativeX = clickX - rect.left\r\n      const charWidth = rect.width / textNode.length\r\n      return Math.min(Math.floor(relativeX / charWidth), textNode.length)\r\n    }\r\n  }\r\n  // 如果点击位置超出文本节点范围，返回文本节点的长度\r\n  return textNode.length\r\n}\r\nconst hasBrAtEnd = (el) => {\r\n  const lastNode = el.lastElementChild\r\n  return lastNode && lastNode.tagName && lastNode.tagName.toLowerCase() === 'br'\r\n}\r\n// 处理键盘事件\r\nconst handleKeyDown = (event) => {\r\n  const target = event.target\r\n  if (event.keyCode == 13) {\r\n    event.preventDefault() // 阻止默认行为\r\n    if (!event.ctrlKey && !event.metaKey) {\r\n      handleSendMessage()\r\n    } else {\r\n      // 处理换行回车键，避免插入 <div> 或 <p>\r\n      const selection = window.getSelection()\r\n      if (selection.rangeCount > 0) {\r\n        const range = selection.getRangeAt(0)\r\n        const clonedRange = range.cloneRange()\r\n        clonedRange.selectNodeContents(target)\r\n        clonedRange.setEnd(range.endContainer, range.endOffset)\r\n        const text = clonedRange.toString()\r\n        const isPosition = text.length === editorRef.value.textContent.length\r\n        const br = document.createElement('br') // 插入 <br> 标签\r\n        const brOne = document.createElement('br') // 插入 <br> 标签\r\n        range.deleteContents() // 删除选中的内容（如果有）\r\n        if (isPosition && !hasBrAtEnd(target)) range.insertNode(brOne) // 插入 <br>\r\n        range.insertNode(br) // 插入 <br>\r\n        range.setStartAfter(br) // 将光标移动到 <br> 后面\r\n        range.setEndAfter(br)\r\n        selection.removeAllRanges()\r\n        selection.addRange(range)\r\n        handleInput()\r\n      }\r\n    }\r\n  }\r\n}\r\n// const handleKeyDownPlaceholder = (event) => {\r\n//   console.log(event)\r\n//   const target = event.target\r\n//   // 处理占位符内部的删除行为\r\n//   if (target.classList.contains('AiChatEditorPlaceholder')) {\r\n//     if (event.key === 'Backspace' || event.key === 'Delete') {\r\n//       // 如果占位符内容为空，删除整个占位符\r\n//       if (target.textContent === '') {\r\n//         event.preventDefault()\r\n//         const placeholderContainer = target.parentNode\r\n//         if (placeholderContainer && placeholderContainer.parentNode) {\r\n//           placeholderContainer.parentNode.removeChild(placeholderContainer)\r\n//         }\r\n//       }\r\n//     } else if (event.key === 'Enter') {\r\n//       // 如果按下 Enter 键，退出编辑模式\r\n//       event.preventDefault()\r\n//       target.contentEditable = 'false'\r\n//       const selection = window.getSelection()\r\n//       const range = document.createRange()\r\n//       range.setStartAfter(target)\r\n//       range.setEndAfter(target)\r\n//       selection.removeAllRanges()\r\n//       selection.addRange(range)\r\n//       isEditingPlaceholder = false // 退出编辑模式\r\n//     }\r\n//   }\r\n// }\r\n// 处理粘贴事件\r\nconst handlePaste = (event) => {\r\n  event.preventDefault() // 阻止默认粘贴行为\r\n  // 获取粘贴的纯文本内容\r\n  const text = (event.clipboardData || window.clipboardData).getData('text/plain')\r\n  // 将纯文本插入到光标位置\r\n  const selection = window.getSelection()\r\n  if (!selection.rangeCount) return\r\n  const range = selection.getRangeAt(0)\r\n  range.deleteContents() // 删除选中的内容（如果有）\r\n  // 插入纯文本\r\n  const textNode = document.createTextNode(text)\r\n  range.insertNode(textNode)\r\n  // 将光标移动到插入内容的后面\r\n  const newRange = document.createRange()\r\n  newRange.setStartAfter(textNode)\r\n  newRange.setEndAfter(textNode)\r\n  selection.removeAllRanges()\r\n  selection.addRange(newRange)\r\n  handleInput()\r\n}\r\nconst handleSetFile = (data) => {\r\n  fileData.value = data\r\n  emit('fileCallback', fileData.value)\r\n}\r\nconst handleSetContent = (contentData) => {\r\n  editorRef.value.innerHTML = contentData\r\n  handleInput()\r\n}\r\nconst handleAddContent = (contentData) => {\r\n  editorRef.value.innerHTML = editorRef.value.innerHTML + (contentData?.replace(/<[^>]*>/g, '') || '')\r\n  handleInput()\r\n}\r\n/**\r\n * 限制上传附件的文件类型\r\n */\r\nconst handleFile = () => {\r\n  // const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)\r\n  // const isShow = ['doc', 'docx', 'pdf'].includes(fileType)\r\n  // if (!isShow) { ElMessage({ type: 'warning', message: `仅支持${['doc', 'docx', 'pdf'].join('、')}格式!` }) }\r\n  // return isShow\r\n  return true\r\n}\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = (Math.random() * 16) | 0,\r\n      v = c == 'x' ? r : (r & 0x3) | 0x8\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst onUploadProgress = (progressEvent, uid) => {\r\n  if (progressEvent?.event?.lengthComputable) {\r\n    const progress = ((progressEvent.loaded / progressEvent.total) * 100).toFixed(0)\r\n    fileList.value.forEach((item) => {\r\n      if (item.uid === uid) {\r\n        item.progress = parseInt(progress)\r\n      }\r\n    })\r\n  }\r\n}\r\n/**\r\n * 上传附件请求方法\r\n */\r\nconst fileUpload = (file) => {\r\n  const param = new FormData()\r\n  param.append('file', file.file)\r\n  globalUpload(param, guid(), file.file.name, file.file.uid, file.file.size)\r\n}\r\nconst globalUpload = async (params, uid, name, time, size) => {\r\n  try {\r\n    const fileType = name.substring(name.lastIndexOf('.') + 1)\r\n    fileList.value.push({ uid, fileName: name, fileType, fileSize: size, progress: 0 })\r\n    emit('uploadCallback', fileList.value)\r\n    const { data } = await api.globalUpload(params, onUploadProgress, uid)\r\n    fileList.value = fileList.value.filter((item) => item.uid !== uid)\r\n    emit('uploadCallback', fileList.value)\r\n    const newData = []\r\n    const newSortData = []\r\n    const newSucceedData = [...fileData.value, { ...data, uid: uid, time: time, progress: 100 }]\r\n    for (let index = 0; index < newSucceedData.length; index++) {\r\n      const item = newSucceedData[index]\r\n      if (item.time) {\r\n        newSortData.push(item)\r\n      } else {\r\n        newData.push(item)\r\n      }\r\n    }\r\n    fileData.value = [...newData, ...newSortData.sort((a, b) => a.time - b.time)]\r\n    emit('fileCallback', fileData.value)\r\n  } catch (err) {\r\n    fileList.value = fileList.value.filter((item) => item.uid !== uid)\r\n    emit('uploadCallback', fileList.value)\r\n  }\r\n}\r\ndefineExpose({ editorRef: editorRef.value, handleSetFile, handleSetContent, handleAddContent, handleInsertPlaceholder })\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalAiChatEditorContainer {\r\n  width: 100%;\r\n  height: 146px;\r\n  background: #fff;\r\n\r\n  .GlobalAiChatEditorScroll {\r\n    width: 100%;\r\n    height: calc(140px - var(--zy-height-routine));\r\n\r\n    .is-horizontal {\r\n      height: 9px;\r\n\r\n      .zy-el-scrollbar__thumb {\r\n        opacity: 0.5;\r\n\r\n        &:hover {\r\n          opacity: 0.9;\r\n        }\r\n      }\r\n    }\r\n\r\n    .is-vertical {\r\n      width: 6px;\r\n\r\n      .zy-el-scrollbar__thumb {\r\n        opacity: 0.5;\r\n\r\n        &:hover {\r\n          opacity: 0.9;\r\n        }\r\n      }\r\n    }\r\n\r\n    .zy-el-scrollbar__view {\r\n      padding: 6px 12px;\r\n    }\r\n\r\n    .GlobalAiChatEditor {\r\n      min-height: calc(120px - var(--zy-height-routine));\r\n      outline: none;\r\n      font-size: 14px;\r\n      line-height: 1.9;\r\n    }\r\n  }\r\n\r\n  .AiChatEditorPlaceholderContainer {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    line-height: normal;\r\n    padding: 0 2px;\r\n  }\r\n\r\n  .AiChatEditorPlaceholder {\r\n    display: inline-block;\r\n    padding: 4px 6px;\r\n    font-size: 14px;\r\n    line-height: 14px;\r\n    font-family: monospace;\r\n    color: #1976d2;\r\n    background: #e3f2fd;\r\n    border: 1px solid #90caf9;\r\n    border-radius: 4px;\r\n    user-select: none;\r\n    cursor: text;\r\n  }\r\n\r\n  .GlobalAiChatEditorBotton {\r\n    width: 100%;\r\n    height: var(--zy-height-routine);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 12px;\r\n\r\n    .GlobalAiChatEditorUpload {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .zy-el-button {\r\n        height: var(--zy-height-routine);\r\n        padding: 6px 12px;\r\n        font-weight: normal;\r\n\r\n        .zy-el-icon {\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalAiChatEditorFlex {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      & > span {\r\n        font-size: 12px;\r\n        color: var(--zy-el-text-color-regular);\r\n        padding: 0 12px;\r\n      }\r\n\r\n      .zy-el-button {\r\n        width: var(--zy-height-routine);\r\n        height: var(--zy-height-routine);\r\n        font-size: 16px;\r\n\r\n        .GlobalAiChatEditorStopStream {\r\n          width: 12px;\r\n          height: 12px;\r\n          display: inline-block;\r\n          background: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "+CA8BA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,QAAAvG,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAAqG,qBAAA,QAAAjG,CAAA,GAAAJ,MAAA,CAAAqG,qBAAA,CAAAxG,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAkG,MAAA,WAAAvG,CAAA,WAAAC,MAAA,CAAAuG,wBAAA,CAAA1G,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAkC,KAAA,CAAA1G,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAA2G,cAAA5G,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAA2G,SAAA,CAAA/B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAA4G,SAAA,CAAA3G,CAAA,IAAA2G,SAAA,CAAA3G,CAAA,QAAAA,CAAA,OAAAqG,OAAA,CAAApG,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAA4G,eAAA,CAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAA4G,yBAAA,GAAA5G,MAAA,CAAA6G,gBAAA,CAAAhH,CAAA,EAAAG,MAAA,CAAA4G,yBAAA,CAAA9G,CAAA,KAAAsG,OAAA,CAAApG,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAAuG,wBAAA,CAAAzG,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAA8G,gBAAA9G,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAA+G,cAAA,CAAA/G,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAiH,eAAAhH,CAAA,QAAAS,CAAA,GAAAwG,YAAA,CAAAjH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAwG,aAAAjH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAAwG,WAAA,kBAAAnH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAkH,MAAA,GAAAC,MAAA,EAAApH,CAAA;AAAA,SAAAqH,mBAAApH,CAAA,WAAAqH,kBAAA,CAAArH,CAAA,KAAAsH,gBAAA,CAAAtH,CAAA,KAAAuH,2BAAA,CAAAvH,CAAA,KAAAwH,kBAAA;AAAA,SAAAA,mBAAA,cAAA3D,SAAA;AAAA,SAAA0D,4BAAAvH,CAAA,EAAAU,CAAA,QAAAV,CAAA,2BAAAA,CAAA,SAAAyH,iBAAA,CAAAzH,CAAA,EAAAU,CAAA,OAAAX,CAAA,MAAA2H,QAAA,CAAA9F,IAAA,CAAA5B,CAAA,EAAA4F,KAAA,6BAAA7F,CAAA,IAAAC,CAAA,CAAA+E,WAAA,KAAAhF,CAAA,GAAAC,CAAA,CAAA+E,WAAA,CAAAC,IAAA,aAAAjF,CAAA,cAAAA,CAAA,GAAA4H,KAAA,CAAAC,IAAA,CAAA5H,CAAA,oBAAAD,CAAA,+CAAA8H,IAAA,CAAA9H,CAAA,IAAA0H,iBAAA,CAAAzH,CAAA,EAAAU,CAAA;AAAA,SAAA4G,iBAAAtH,CAAA,8BAAAS,MAAA,YAAAT,CAAA,CAAAS,MAAA,CAAAE,QAAA,aAAAX,CAAA,uBAAA2H,KAAA,CAAAC,IAAA,CAAA5H,CAAA;AAAA,SAAAqH,mBAAArH,CAAA,QAAA2H,KAAA,CAAAG,OAAA,CAAA9H,CAAA,UAAAyH,iBAAA,CAAAzH,CAAA;AAAA,SAAAyH,kBAAAzH,CAAA,EAAAU,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAV,CAAA,CAAA4E,MAAA,MAAAlE,CAAA,GAAAV,CAAA,CAAA4E,MAAA,YAAA9E,CAAA,MAAAK,CAAA,GAAAwH,KAAA,CAAAjH,CAAA,GAAAZ,CAAA,GAAAY,CAAA,EAAAZ,CAAA,IAAAK,CAAA,CAAAL,CAAA,IAAAE,CAAA,CAAAF,CAAA,UAAAK,CAAA;AAAA,SAAA4H,mBAAA5H,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAA2H,kBAAA7H,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAA6G,SAAA,aAAArB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAsG,KAAA,CAAA1G,CAAA,EAAAD,CAAA,YAAAmI,MAAA9H,CAAA,IAAA4H,kBAAA,CAAArH,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAA4H,KAAA,EAAAC,MAAA,UAAA/H,CAAA,cAAA+H,OAAA/H,CAAA,IAAA4H,kBAAA,CAAArH,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAA4H,KAAA,EAAAC,MAAA,WAAA/H,CAAA,KAAA8H,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAK;AAC9C,SAASC,WAAW,EAAEC,QAAQ,QAAQ,yBAAyB;;;;;;;;;;;;;;IAC/D,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,QAAQ,GAAGP,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMQ,QAAQ,GAAGR,GAAG,CAAC,EAAE,CAAC;IACxB,IAAMS,QAAQ,GAAGR,QAAQ,CAAC;MAAA,OAAMI,KAAK,CAACI,QAAQ;IAAA,EAAC;IAC/C,IAAMC,IAAI,GAAGC,MAAoF;IACjG;IACA,IAAMC,SAAS,GAAGZ,GAAG,CAAC,IAAI,CAAC;IAC3B;IACA;IACA,IAAMa,OAAO,GAAGZ,QAAQ,CAAC;MACvBa,GAAGA,CAAA,EAAG;QACJ,OAAOT,KAAK,CAACU,UAAU;MACzB,CAAC;MACDC,GAAGA,CAAC7I,KAAK,EAAE;QACTuI,IAAI,CAAC,mBAAmB,EAAEvI,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;IACF;IACA,IAAI8I,oBAAoB,GAAG,EAAE;IAC7B,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;MACtB,IAAMC,SAAS,GAAGC,SAAS,CAACD,SAAS,CAACE,WAAW,CAAC,CAAC;MACnD,OAAOF,SAAS,CAACG,QAAQ,CAAC,WAAW,CAAC,IAAIH,SAAS,CAACG,QAAQ,CAAC,UAAU,CAAC;IAC1E,CAAC;IACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9B,IAAI,CAACV,OAAO,CAAC1I,KAAK,CAACqJ,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE;MAC9Cd,IAAI,CAAC,MAAM,EAAEG,OAAO,CAAC1I,KAAK,CAAC;MAC3ByI,SAAS,CAACzI,KAAK,CAACsJ,SAAS,GAAG,EAAE;MAC9BC,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;MAC9BjB,IAAI,CAAC,MAAM,EAAEG,OAAO,CAAC1I,KAAK,CAAC;IAC7B,CAAC;IACD;IACA+H,SAAS,CAAC,YAAM;MACd,IAAM0B,QAAQ,GAAGhB,SAAS,CAACzI,KAAK;MAChC,IAAIyJ,QAAQ,EAAE;QACZA,QAAQ,CAACH,SAAS,GAAG,EAAE,EAAC;QACxBC,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IACF;IACA,IAAMG,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAA,EAAS;MACtC,IAAMC,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;MACvC,IAAI,CAACF,SAAS,CAACG,UAAU,EAAE,OAAO,KAAK;MACvC,IAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC,IAAIC,IAAI,GAAGF,KAAK,CAACG,cAAc;MAC/B;MACA,OAAOD,IAAI,IAAIA,IAAI,KAAKxB,SAAS,CAACzI,KAAK,EAAE;QACvC,IAAIiK,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACE,SAAS,CAACC,QAAQ,CAAC,yBAAyB,CAAC,EAAE,OAAO,IAAI,EAAC;QACtFH,IAAI,GAAGA,IAAI,CAACI,UAAU;MACxB;MACA,OAAO,KAAK,EAAC;IACf,CAAC;IACD,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,IAAI,EAAK;MACpC,IAAMC,MAAM,GAAG,EAAE;MACjB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,IAAI,CAAClG,MAAM,EAAEoG,KAAK,EAAE,EAAE;QAChD,IAAMC,IAAI,GAAGH,IAAI,CAACE,KAAK,CAAC;QACxB,IAAIC,IAAI,CAACvJ,IAAI,EAAE;UACb;UACA,IAAMwJ,oBAAoB,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;UAC3DF,oBAAoB,CAACG,SAAS,GAAG,kCAAkC;UACnE;UACA,IAAMC,WAAW,GAAGH,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;UAClDE,WAAW,CAACD,SAAS,GAAG,yBAAyB;UACjDC,WAAW,CAACC,eAAe,GAAG,OAAO,EAAC;UACtCD,WAAW,CAACE,WAAW,GAAGP,IAAI,CAAC1K,KAAK;UACpC+K,WAAW,CAACG,OAAO,CAACC,EAAE,GAAGC,IAAI,CAAC,CAAC;UAC/BT,oBAAoB,CAACU,WAAW,CAACN,WAAW,CAAC;UAC7CP,MAAM,CAACxG,IAAI,CAAC2G,oBAAoB,CAAC;QACnC,CAAC,MAAM;UACLH,MAAM,CAACxG,IAAI,CAAC4G,QAAQ,CAACU,cAAc,CAACZ,IAAI,CAAC1K,KAAK,CAAC,CAAC;QAClD;MACF;MACA,OAAOwK,MAAM;IACf,CAAC;IACD;IACA,IAAMe,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIC,UAAU,EAAK;MAC9C,IAAI,CAACA,UAAU,CAACnH,MAAM,EAAE;MACxB,IAAMoF,QAAQ,GAAGhB,SAAS,CAACzI,KAAK;MAChC,IAAI,CAACyJ,QAAQ,EAAE;MACfA,QAAQ,CAACgC,KAAK,CAAC,CAAC;MAChB;MACA,IAAI/B,yBAAyB,CAAC,CAAC,EAAE,OAAM,CAAC;MACxC;MACA,IAAMC,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;MACvC,IAAI,CAACF,SAAS,CAACG,UAAU,EAAE;MAC3B,IAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC;MACA,IAAI,CAACD,KAAK,CAAC2B,SAAS,EAAE3B,KAAK,CAAC4B,cAAc,CAAC,CAAC;MAC5C;MACA,IAAIhB,oBAAoB,GAAG,IAAI;MAC/B,IAAMH,MAAM,GAAGF,mBAAmB,CAACkB,UAAU,CAAC;MAC9C,KAAK,IAAIf,KAAK,GAAGD,MAAM,CAACnG,MAAM,GAAG,CAAC,EAAEoG,KAAK,IAAI,CAAC,EAAEA,KAAK,EAAE,EAAE;QACvD,IAAMC,IAAI,GAAGF,MAAM,CAACC,KAAK,CAAC;QAC1BV,KAAK,CAAC6B,UAAU,CAAClB,IAAI,CAAC;QACtB,IAAID,KAAK,KAAKD,MAAM,CAACnG,MAAM,GAAG,CAAC,EAAE;UAC/BsG,oBAAoB,GAAGD,IAAI;QAC7B;MACF;MACA;MACA;MACA;MACA,IAAMmB,KAAK,GAAGjB,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,EAAC;MAC3C;MACA;MACAd,KAAK,CAAC+B,aAAa,CAACnB,oBAAoB,CAAC;MACzCZ,KAAK,CAACgC,WAAW,CAACpB,oBAAoB,CAAC;MACvCZ,KAAK,CAAC6B,UAAU,CAACC,KAAK,CAAC;MACvB;MACA,IAAMG,QAAQ,GAAGpB,QAAQ,CAACqB,WAAW,CAAC,CAAC;MACvCD,QAAQ,CAACF,aAAa,CAACD,KAAK,CAAC;MAC7BG,QAAQ,CAACD,WAAW,CAACF,KAAK,CAAC;MAC3BlC,SAAS,CAACuC,eAAe,CAAC,CAAC;MAC3BvC,SAAS,CAACwC,QAAQ,CAACH,QAAQ,CAAC;MAC5B;MACAvC,QAAQ,CAACgC,KAAK,CAAC,CAAC;MAChBlC,WAAW,CAAC,CAAC;IACf,CAAC;IACD;IACA,IAAMA,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAME,QAAQ,GAAGhB,SAAS,CAACzI,KAAK;MAChC,IAAIyJ,QAAQ,EAAE;QAAA,IAAA2C,mBAAA;QACZ1D,OAAO,CAAC1I,KAAK,GAAG,EAAAoM,mBAAA,GAAA3C,QAAQ,CAACH,SAAS,cAAA8C,mBAAA,gBAAAA,mBAAA,GAAlBA,mBAAA,CAAoB/C,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,cAAA+C,mBAAA,uBAA1CA,mBAAA,CAA4C/C,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,KAAI,EAAE;MAC3F;IACF,CAAC;IACD;IACA,IAAMgD,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,KAAK,EAAK;MACnC,IAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;MAC3B;MACA,IAAIA,MAAM,CAACpC,SAAS,CAACC,QAAQ,CAAC,yBAAyB,CAAC,EAAE;QACxD,IAAMoC,IAAI,GAAGD,MAAM,CAACE,YAAY,CAAC,SAAS,CAAC;QAC3C,IAAI3D,oBAAoB,KAAK0D,IAAI,EAAE;UACjC1D,oBAAoB,GAAG0D,IAAI;UAC3B;UACAD,MAAM,CAACvB,eAAe,GAAG,MAAM;UAC/BuB,MAAM,CAACd,KAAK,CAAC,CAAC;UACd;UACA,IAAM1B,KAAK,GAAGa,QAAQ,CAACqB,WAAW,CAAC,CAAC;UACpClC,KAAK,CAAC2C,kBAAkB,CAACH,MAAM,CAAC;UAChC,IAAM5C,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;UACvCF,SAAS,CAACuC,eAAe,CAAC,CAAC;UAC3BvC,SAAS,CAACwC,QAAQ,CAACpC,KAAK,CAAC;QAC3B,CAAC,MAAM;UACL;UACA,IAAM4C,WAAW,GAAGC,cAAc,CAACL,MAAM,EAAED,KAAK,CAAC;UACjD,IAAM3C,UAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;UACvC,IAAME,MAAK,GAAGa,QAAQ,CAACqB,WAAW,CAAC,CAAC;UACpC;UACAlC,MAAK,CAAC8C,QAAQ,CAACN,MAAM,CAACO,UAAU,EAAEH,WAAW,CAAC;UAC9C5C,MAAK,CAACgD,MAAM,CAACR,MAAM,CAACO,UAAU,EAAEH,WAAW,CAAC;UAC5ChD,UAAS,CAACuC,eAAe,CAAC,CAAC;UAC3BvC,UAAS,CAACwC,QAAQ,CAACpC,MAAK,CAAC;QAC3B;MACF,CAAC,MAAM;QACL;QACAjB,oBAAoB,GAAG,EAAE;MAC3B;IACF,CAAC;IACD;IACA,IAAM8D,cAAc,GAAG,SAAjBA,cAAcA,CAAII,OAAO,EAAEV,KAAK,EAAK;MACzC,IAAMW,QAAQ,GAAGD,OAAO,CAACF,UAAU,EAAC;MACpC,IAAM/C,KAAK,GAAGa,QAAQ,CAACqB,WAAW,CAAC,CAAC;MACpClC,KAAK,CAAC2C,kBAAkB,CAACO,QAAQ,CAAC;MAClC,IAAMC,KAAK,GAAGnD,KAAK,CAACoD,cAAc,CAAC,CAAC,EAAC;MACrC,IAAMC,MAAM,GAAGd,KAAK,CAACe,OAAO,EAAC;MAC7B;MACA,KAAK,IAAIpN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiN,KAAK,CAAC7I,MAAM,EAAEpE,CAAC,EAAE,EAAE;QACrC,IAAMqN,IAAI,GAAGJ,KAAK,CAACjN,CAAC,CAAC;QACrB,IAAImN,MAAM,IAAIE,IAAI,CAACC,IAAI,IAAIH,MAAM,IAAIE,IAAI,CAACE,KAAK,EAAE;UAC/C;UACA,IAAMC,SAAS,GAAGL,MAAM,GAAGE,IAAI,CAACC,IAAI;UACpC,IAAMG,SAAS,GAAGJ,IAAI,CAACK,KAAK,GAAGV,QAAQ,CAAC5I,MAAM;UAC9C,OAAOuJ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACL,SAAS,GAAGC,SAAS,CAAC,EAAET,QAAQ,CAAC5I,MAAM,CAAC;QACrE;MACF;MACA;MACA,OAAO4I,QAAQ,CAAC5I,MAAM;IACxB,CAAC;IACD,IAAM0J,UAAU,GAAG,SAAbA,UAAUA,CAAIC,EAAE,EAAK;MACzB,IAAMC,QAAQ,GAAGD,EAAE,CAACE,gBAAgB;MACpC,OAAOD,QAAQ,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACjF,WAAW,CAAC,CAAC,KAAK,IAAI;IAChF,CAAC;IACD;IACA,IAAMkF,aAAa,GAAG,SAAhBA,aAAaA,CAAI9B,KAAK,EAAK;MAC/B,IAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;MAC3B,IAAID,KAAK,CAAC+B,OAAO,IAAI,EAAE,EAAE;QACvB/B,KAAK,CAACgC,cAAc,CAAC,CAAC,EAAC;QACvB,IAAI,CAAChC,KAAK,CAACiC,OAAO,IAAI,CAACjC,KAAK,CAACkC,OAAO,EAAE;UACpCpF,iBAAiB,CAAC,CAAC;QACrB,CAAC,MAAM;UACL;UACA,IAAMO,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;UACvC,IAAIF,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;YAC5B,IAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;YACrC,IAAMyE,WAAW,GAAG1E,KAAK,CAAC2E,UAAU,CAAC,CAAC;YACtCD,WAAW,CAAC/B,kBAAkB,CAACH,MAAM,CAAC;YACtCkC,WAAW,CAAC1B,MAAM,CAAChD,KAAK,CAAC4E,YAAY,EAAE5E,KAAK,CAAC6E,SAAS,CAAC;YACvD,IAAMC,IAAI,GAAGJ,WAAW,CAACtH,QAAQ,CAAC,CAAC;YACnC,IAAM2H,UAAU,GAAGD,IAAI,CAACxK,MAAM,KAAKoE,SAAS,CAACzI,KAAK,CAACiL,WAAW,CAAC5G,MAAM;YACrE,IAAM0K,EAAE,GAAGnE,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,EAAC;YACxC,IAAMmE,KAAK,GAAGpE,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC,EAAC;YAC3Cd,KAAK,CAAC4B,cAAc,CAAC,CAAC,EAAC;YACvB,IAAImD,UAAU,IAAI,CAACf,UAAU,CAACxB,MAAM,CAAC,EAAExC,KAAK,CAAC6B,UAAU,CAACoD,KAAK,CAAC,EAAC;YAC/DjF,KAAK,CAAC6B,UAAU,CAACmD,EAAE,CAAC,EAAC;YACrBhF,KAAK,CAAC+B,aAAa,CAACiD,EAAE,CAAC,EAAC;YACxBhF,KAAK,CAACgC,WAAW,CAACgD,EAAE,CAAC;YACrBpF,SAAS,CAACuC,eAAe,CAAC,CAAC;YAC3BvC,SAAS,CAACwC,QAAQ,CAACpC,KAAK,CAAC;YACzBR,WAAW,CAAC,CAAC;UACf;QACF;MACF;IACF,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAM0F,WAAW,GAAG,SAAdA,WAAWA,CAAI3C,KAAK,EAAK;MAC7BA,KAAK,CAACgC,cAAc,CAAC,CAAC,EAAC;MACvB;MACA,IAAMO,IAAI,GAAG,CAACvC,KAAK,CAAC4C,aAAa,IAAItF,MAAM,CAACsF,aAAa,EAAEC,OAAO,CAAC,YAAY,CAAC;MAChF;MACA,IAAMxF,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;MACvC,IAAI,CAACF,SAAS,CAACG,UAAU,EAAE;MAC3B,IAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrCD,KAAK,CAAC4B,cAAc,CAAC,CAAC,EAAC;MACvB;MACA,IAAMsB,QAAQ,GAAGrC,QAAQ,CAACU,cAAc,CAACuD,IAAI,CAAC;MAC9C9E,KAAK,CAAC6B,UAAU,CAACqB,QAAQ,CAAC;MAC1B;MACA,IAAMjB,QAAQ,GAAGpB,QAAQ,CAACqB,WAAW,CAAC,CAAC;MACvCD,QAAQ,CAACF,aAAa,CAACmB,QAAQ,CAAC;MAChCjB,QAAQ,CAACD,WAAW,CAACkB,QAAQ,CAAC;MAC9BtD,SAAS,CAACuC,eAAe,CAAC,CAAC;MAC3BvC,SAAS,CAACwC,QAAQ,CAACH,QAAQ,CAAC;MAC5BzC,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAM6F,aAAa,GAAG,SAAhBA,aAAaA,CAAI7E,IAAI,EAAK;MAC9BlC,QAAQ,CAACrI,KAAK,GAAGuK,IAAI;MACrBhC,IAAI,CAAC,cAAc,EAAEF,QAAQ,CAACrI,KAAK,CAAC;IACtC,CAAC;IACD,IAAMqP,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,WAAW,EAAK;MACxC7G,SAAS,CAACzI,KAAK,CAACsJ,SAAS,GAAGgG,WAAW;MACvC/F,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMgG,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAID,WAAW,EAAK;MACxC7G,SAAS,CAACzI,KAAK,CAACsJ,SAAS,GAAGb,SAAS,CAACzI,KAAK,CAACsJ,SAAS,IAAI,CAAAgG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjG,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,KAAI,EAAE,CAAC;MACpGE,WAAW,CAAC,CAAC;IACf,CAAC;IACD;AACA;AACA;IACA,IAAMiG,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB;MACA;MACA;MACA;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAMpE,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAAC/B,OAAO,CAAC,OAAO,EAAE,UAAChJ,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAImO,IAAI,CAAC6B,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;UAC9BzN,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;QACpC,OAAOuC,CAAC,CAACmF,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAMuI,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,aAAa,EAAEC,GAAG,EAAK;MAAA,IAAAC,oBAAA;MAC/C,IAAIF,aAAa,aAAbA,aAAa,gBAAAE,oBAAA,GAAbF,aAAa,CAAErD,KAAK,cAAAuD,oBAAA,eAApBA,oBAAA,CAAsBC,gBAAgB,EAAE;QAC1C,IAAMC,QAAQ,GAAG,CAAEJ,aAAa,CAACK,MAAM,GAAGL,aAAa,CAACM,KAAK,GAAI,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;QAChF9H,QAAQ,CAACpI,KAAK,CAACoC,OAAO,CAAC,UAACsI,IAAI,EAAK;UAC/B,IAAIA,IAAI,CAACkF,GAAG,KAAKA,GAAG,EAAE;YACpBlF,IAAI,CAACqF,QAAQ,GAAGI,QAAQ,CAACJ,QAAQ,CAAC;UACpC;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACD;AACA;AACA;IACA,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAIC,IAAI,EAAK;MAC3B,IAAMC,KAAK,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC5BD,KAAK,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAACA,IAAI,CAAC;MAC/BI,YAAY,CAACH,KAAK,EAAElF,IAAI,CAAC,CAAC,EAAEiF,IAAI,CAACA,IAAI,CAAC5L,IAAI,EAAE4L,IAAI,CAACA,IAAI,CAACT,GAAG,EAAES,IAAI,CAACA,IAAI,CAACK,IAAI,CAAC;IAC5E,CAAC;IACD,IAAMD,YAAY;MAAA,IAAAE,KAAA,GAAAlJ,iBAAA,cAAAnI,mBAAA,GAAAoF,IAAA,CAAG,SAAAkM,QAAOC,MAAM,EAAEjB,GAAG,EAAEnL,IAAI,EAAEqM,IAAI,EAAEJ,IAAI;QAAA,IAAAK,QAAA,EAAAC,qBAAA,EAAAzG,IAAA,EAAA0G,OAAA,EAAAC,WAAA,EAAAC,cAAA,EAAA1G,KAAA,EAAAC,IAAA;QAAA,OAAApL,mBAAA,GAAAuB,IAAA,UAAAuQ,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAlM,IAAA,GAAAkM,QAAA,CAAA7N,IAAA;YAAA;cAAA6N,QAAA,CAAAlM,IAAA;cAE/C4L,QAAQ,GAAGtM,IAAI,CAAC6M,SAAS,CAAC7M,IAAI,CAAC8M,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC1DnJ,QAAQ,CAACpI,KAAK,CAACgE,IAAI,CAAC;gBAAE4L,GAAG;gBAAE4B,QAAQ,EAAE/M,IAAI;gBAAEsM,QAAQ;gBAAEU,QAAQ,EAAEf,IAAI;gBAAEX,QAAQ,EAAE;cAAE,CAAC,CAAC;cACnFxH,IAAI,CAAC,gBAAgB,EAAEH,QAAQ,CAACpI,KAAK,CAAC;cAAAqR,QAAA,CAAA7N,IAAA;cAAA,OACfoE,GAAG,CAAC6I,YAAY,CAACI,MAAM,EAAEnB,gBAAgB,EAAEE,GAAG,CAAC;YAAA;cAAAoB,qBAAA,GAAAK,QAAA,CAAApO,IAAA;cAA9DsH,IAAI,GAAAyG,qBAAA,CAAJzG,IAAI;cACZnC,QAAQ,CAACpI,KAAK,GAAGoI,QAAQ,CAACpI,KAAK,CAACgG,MAAM,CAAC,UAAC0E,IAAI;gBAAA,OAAKA,IAAI,CAACkF,GAAG,KAAKA,GAAG;cAAA,EAAC;cAClErH,IAAI,CAAC,gBAAgB,EAAEH,QAAQ,CAACpI,KAAK,CAAC;cAChCiR,OAAO,GAAG,EAAE;cACZC,WAAW,GAAG,EAAE;cAChBC,cAAc,MAAAO,MAAA,CAAA7K,kBAAA,CAAOwB,QAAQ,CAACrI,KAAK,IAAAmG,aAAA,CAAAA,aAAA,KAAOoE,IAAI;gBAAEqF,GAAG,EAAEA,GAAG;gBAAEkB,IAAI,EAAEA,IAAI;gBAAEf,QAAQ,EAAE;cAAG;cACzF,KAAStF,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG0G,cAAc,CAAC9M,MAAM,EAAEoG,KAAK,EAAE,EAAE;gBACpDC,IAAI,GAAGyG,cAAc,CAAC1G,KAAK,CAAC;gBAClC,IAAIC,IAAI,CAACoG,IAAI,EAAE;kBACbI,WAAW,CAAClN,IAAI,CAAC0G,IAAI,CAAC;gBACxB,CAAC,MAAM;kBACLuG,OAAO,CAACjN,IAAI,CAAC0G,IAAI,CAAC;gBACpB;cACF;cACArC,QAAQ,CAACrI,KAAK,MAAA0R,MAAA,CAAOT,OAAO,EAAApK,kBAAA,CAAKqK,WAAW,CAACS,IAAI,CAAC,UAACxR,CAAC,EAAEyR,CAAC;gBAAA,OAAKzR,CAAC,CAAC2Q,IAAI,GAAGc,CAAC,CAACd,IAAI;cAAA,EAAC,EAAC;cAC7EvI,IAAI,CAAC,cAAc,EAAEF,QAAQ,CAACrI,KAAK,CAAC;cAAAqR,QAAA,CAAA7N,IAAA;cAAA;YAAA;cAAA6N,QAAA,CAAAlM,IAAA;cAAAkM,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEpCjJ,QAAQ,CAACpI,KAAK,GAAGoI,QAAQ,CAACpI,KAAK,CAACgG,MAAM,CAAC,UAAC0E,IAAI;gBAAA,OAAKA,IAAI,CAACkF,GAAG,KAAKA,GAAG;cAAA,EAAC;cAClErH,IAAI,CAAC,gBAAgB,EAAEH,QAAQ,CAACpI,KAAK,CAAC;YAAA;YAAA;cAAA,OAAAqR,QAAA,CAAA/L,IAAA;UAAA;QAAA,GAAAsL,OAAA;MAAA,CAEzC;MAAA,gBAzBKH,YAAYA,CAAAqB,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAvB,KAAA,CAAAzK,KAAA,OAAAE,SAAA;MAAA;IAAA,GAyBjB;IACD+L,QAAY,CAAC;MAAE1J,SAAS,EAAEA,SAAS,CAACzI,KAAK;MAAEoP,aAAa;MAAEC,gBAAgB;MAAEE,gBAAgB;MAAEhE;IAAwB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}