{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, withCtx as _withCtx } from \"vue\";\nvar _hoisted_1 = {\n  class: \"QuestionsAndAnswers\"\n};\nvar _hoisted_2 = {\n  class: \"QuestionsAndAnswersBody\"\n};\nvar _hoisted_3 = {\n  class: \"QuestionsAndAnswersChatImg\"\n};\nvar _hoisted_4 = {\n  class: \"QuestionsAndAnswersChatTextBody\"\n};\nvar _hoisted_5 = {\n  key: 0,\n  class: \"answerLoading\"\n};\nvar _hoisted_6 = {\n  class: \"QuestionsAndAnswersChatText\"\n};\nvar _hoisted_7 = {\n  class: \"QuestionsAndAnswersChatInput\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_scrollbar, {\n    always: \"\",\n    class: \"QuestionsAndAnswersChat\",\n    ref: \"scrollRef\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.dataList, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: _normalizeClass(item.className),\n          key: item.id\n        }, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n          src: item.type ? $setup.user.image : $setup.IntelligentAssistant,\n          fit: \"cover\"\n        }, null, 8 /* PROPS */, [\"src\"])]), _createElementVNode(\"div\", _hoisted_4, [item.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _toConsumableArray(_cache[1] || (_cache[1] = [_createElementVNode(\"svg\", {\n          t: \"1716976607389\",\n          viewBox: \"0 0 1024 1024\",\n          version: \"1.1\",\n          \"p-id\": \"2362\",\n          width: \"60%\",\n          height: \"60%\"\n        }, [_createElementVNode(\"path\", {\n          d: \"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\",\n          fill: \"#2c2c2c\",\n          \"p-id\": \"2363\"\n        }), _createElementVNode(\"path\", {\n          d: \"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\",\n          fill: \"#2c2c2c\",\n          \"p-id\": \"2364\"\n        }), _createElementVNode(\"path\", {\n          d: \"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\",\n          fill: \"#2c2c2c\",\n          \"p-id\": \"2365\"\n        }), _createElementVNode(\"path\", {\n          d: \"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\",\n          fill: \"#2c2c2c\",\n          \"p-id\": \"2366\"\n        }), _createElementVNode(\"path\", {\n          d: \"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\",\n          fill: \"#2c2c2c\",\n          \"p-id\": \"2367\"\n        }), _createElementVNode(\"path\", {\n          d: \"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\",\n          fill: \"#2c2c2c\",\n          \"p-id\": \"2368\"\n        }), _createElementVNode(\"path\", {\n          d: \"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\",\n          fill: \"#2c2c2c\",\n          \"p-id\": \"2369\"\n        }), _createElementVNode(\"path\", {\n          d: \"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\",\n          fill: \"#2c2c2c\",\n          \"p-id\": \"2370\"\n        })], -1 /* HOISTED */)])))) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_6, [_cache[2] || (_cache[2] = _createElementVNode(\"span\", null, null, -1 /* HOISTED */)), _createTextVNode(_toDisplayString(item.text), 1 /* TEXT */)])])], 2 /* CLASS */);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_input, {\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.content = $event;\n    }),\n    type: \"textarea\",\n    autosize: {\n      minRows: 3,\n      maxRows: 6\n    },\n    placeholder: \"请输入您的需求...\"\n  }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n    onClick: $setup.handleSend,\n    type: \"primary\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"发送\")]);\n    }),\n    _: 1 /* STABLE */\n  })])])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_scrollbar", "always", "ref", "default", "_withCtx", "_Fragment", "_renderList", "$setup", "dataList", "item", "_normalizeClass", "className", "id", "_hoisted_3", "_component_el_image", "src", "type", "user", "image", "IntelligentAssistant", "fit", "_hoisted_4", "loading", "_hoisted_5", "_toConsumableArray", "_cache", "t", "viewBox", "version", "width", "height", "d", "fill", "_createCommentVNode", "_hoisted_6", "_createTextVNode", "_toDisplayString", "text", "_", "_hoisted_7", "_component_el_input", "modelValue", "content", "$event", "autosize", "minRows", "maxRows", "placeholder", "_component_el_button", "onClick", "handleSend"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\QuestionsAndAnswers\\QuestionsAndAnswers.vue"], "sourcesContent": ["<template>\r\n  <div class=\"QuestionsAndAnswers\">\r\n    <div class=\"QuestionsAndAnswersBody\">\r\n      <el-scrollbar always class=\"QuestionsAndAnswersChat\" ref=\"scrollRef\">\r\n        <div v-for=\"item in dataList\" :class=\"item.className\" :key=\"item.id\">\r\n          <div class=\"QuestionsAndAnswersChatImg\">\r\n            <el-image :src=\"item.type ? user.image : IntelligentAssistant\" fit=\"cover\"></el-image>\r\n          </div>\r\n          <div class=\"QuestionsAndAnswersChatTextBody\">\r\n            <div class=\"answerLoading\" v-if=\"item.loading\">\r\n              <svg t=\"1716976607389\" viewBox=\"0 0 1024 1024\" version=\"1.1\" p-id=\"2362\" width=\"60%\" height=\"60%\">\r\n                <path\r\n                  d=\"M827.211075 221.676536m-54.351151 0a54.351151 54.351151 0 1 0 108.702302 0 54.351151 54.351151 0 1 0-108.702302 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2363\"></path>\r\n                <path\r\n                  d=\"M940.905298 515.399947m-67.086951 0a67.086952 67.086952 0 1 0 134.173903 0 67.086952 67.086952 0 1 0-134.173903 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2364\"></path>\r\n                <path\r\n                  d=\"M829.755035 810.595334m-78.974766 0a78.974766 78.974766 0 1 0 157.949532 0 78.974766 78.974766 0 1 0-157.949532 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2365\"></path>\r\n                <path\r\n                  d=\"M534.831643 928.64149m-91.48657 0a91.486571 91.486571 0 1 0 182.973141 0 91.486571 91.486571 0 1 0-182.973141 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2366\"></path>\r\n                <path\r\n                  d=\"M243.780191 805.955407m-101.902408 0a101.902408 101.902408 0 1 0 203.804816 0 101.902408 101.902408 0 1 0-203.804816 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2367\"></path>\r\n                <path\r\n                  d=\"M536.623615 107.870315m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2368\"></path>\r\n                <path\r\n                  d=\"M243.780191 224.220497m-107.854315 0a107.854315 107.854315 0 1 0 215.70863 0 107.854315 107.854315 0 1 0-215.70863 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2369\"></path>\r\n                <path\r\n                  d=\"M129.429978 512.008m-102.766395 0a102.766394 102.766394 0 1 0 205.532789 0 102.766394 102.766394 0 1 0-205.532789 0Z\"\r\n                  fill=\"#2c2c2c\" p-id=\"2370\"></path>\r\n              </svg>\r\n            </div>\r\n            <div class=\"QuestionsAndAnswersChatText\"><span></span>{{ item.text }}</div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <div class=\"QuestionsAndAnswersChatInput\">\r\n        <el-input v-model=\"content\" type=\"textarea\" :autosize=\"{ minRows: 3, maxRows: 6 }\" placeholder=\"请输入您的需求...\" />\r\n        <el-button @click=\"handleSend\" type=\"primary\">发送</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'QuestionsAndAnswers' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, nextTick } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { user, IntelligentAssistant } from 'common/js/system_var.js'\r\nconst route = useRoute()\r\nconst scrollRef = ref()\r\nconst content = ref('')\r\nconst dataList = ref([\r\n  { id: '1', type: 0, className: 'QuestionsAndAnswersOther', text: '您好！我是您的小助手，您可以向我提出问题，我将基于AI给我的超能力，然后根据资源库内数据做出回答，赶紧试试吧！' },\r\n  // { id: '2', type: 1, className: 'QuestionsAndAnswersOneself', text: '请根据关键词“长沙市\"' }\r\n])\r\nconst history = ref({})\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)\r\n    return v.toString(16)\r\n  })\r\n}\r\nonMounted(() => { })\r\nconst scrollDown = () => {\r\n  scrollRef.value.wrapRef.scrollTop = scrollRef.value.wrapRef.scrollHeight\r\n}\r\nconst handleSend = () => {\r\n  const id = guid()\r\n  history.value[id] = [content.value]\r\n  dataList.value.push({ id: id, type: 1, className: 'QuestionsAndAnswersOneself', text: content.value })\r\n  dataList.value.push({ id: guid(), uid: id, type: 0, loading: true, className: 'QuestionsAndAnswersOther', text: '正在思考中！' })\r\n  nextTick(() => { scrollDown() })\r\n  chatGLMDocument(content.value, id)\r\n  content.value = ''\r\n}\r\nconst chatGLMDocument = async (text, id) => {\r\n  const { data } = await api.knowledgeQuiz({ input: text, zskId: route.query.type })\r\n  const answer = data?.data?.output || data.message\r\n  if (id) {\r\n    let newArray = []\r\n    for (let index = 0; index < dataList.value.length; index++) {\r\n      const item = dataList.value[index]\r\n      if (item.uid === id) {\r\n        newArray.push({ ...item, loading: false, text: answer })\r\n      } else {\r\n        newArray.push(item)\r\n      }\r\n    }\r\n    dataList.value = newArray\r\n  } else {\r\n    dataList.value.push({ id: guid(), uid: guid(), type: 0, className: 'QuestionsAndAnswersOther', text: answer })\r\n  }\r\n  nextTick(() => { scrollDown() })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.QuestionsAndAnswers {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n  padding-top: 20px;\r\n  background-color: #fff;\r\n\r\n  .QuestionsAndAnswersBody {\r\n    width: 100%;\r\n    height: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    background: linear-gradient(180deg, #FFFBFA 0%, #FAF6F5 100%);\r\n    padding: 20px 0;\r\n\r\n    .QuestionsAndAnswersChatInput {\r\n      width: 100%;\r\n      max-width: 900px;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      padding: var(--zy-distance-five) var(--zy-distance-two);\r\n      border-radius: var(--el-border-radius-base);\r\n      background: #fff;\r\n\r\n      .zy-el-textarea__inner {\r\n        border-radius: 0;\r\n      }\r\n\r\n      .zy-el-button {\r\n        margin-left: var(--zy-distance-four);\r\n        --zy-el-button-size: var(--zy-height-routine);\r\n      }\r\n    }\r\n\r\n    .QuestionsAndAnswersChat {\r\n      flex: 1;\r\n      width: 100%;\r\n      max-width: 800px;\r\n\r\n      .QuestionsAndAnswersOther,\r\n      .QuestionsAndAnswersOneself {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: var(--zy-distance-five) var(--zy-distance-two);\r\n\r\n        .QuestionsAndAnswersChatImg {\r\n          width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n          height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n\r\n          .zy-el-image {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 50%;\r\n          }\r\n        }\r\n\r\n        .QuestionsAndAnswersChatTextBody {\r\n          display: flex;\r\n          width: calc(100% - ((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n          position: relative;\r\n\r\n          @keyframes circleRoate {\r\n            from {\r\n              transform: rotate(0deg);\r\n            }\r\n\r\n            to {\r\n              transform: rotate(360deg);\r\n            }\r\n          }\r\n\r\n          .answerLoading {\r\n            width: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            height: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            z-index: 3;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            animation: circleRoate 1s infinite linear;\r\n\r\n            path {\r\n              fill: var(--zy-el-color-primary);\r\n            }\r\n          }\r\n\r\n          .answerLoading+.QuestionsAndAnswersChatText {\r\n            color: var(--zy-el-color-primary);\r\n            padding-left: calc((var(--zy-text-font-size) * var(--zy-line-height)) + 20px);\r\n          }\r\n\r\n          .QuestionsAndAnswersChatText {\r\n            position: relative;\r\n            display: inline-block;\r\n            max-width: 100%;\r\n            padding: 10px var(--zy-distance-five);\r\n            font-size: var(--zy-text-font-size);\r\n            line-height: var(--zy-line-height);\r\n            background-color: #fff;\r\n            border-radius: var(--el-border-radius-base);\r\n            border: 1px solid var(--zy-el-border-color-light);\r\n            word-wrap: break-word;\r\n            white-space: pre-wrap;\r\n            z-index: 2;\r\n\r\n\r\n            span {\r\n              position: absolute;\r\n              width: 10px;\r\n              height: 10px;\r\n              z-index: -1;\r\n              top: calc(((var(--zy-text-font-size) * var(--zy-line-height)) / 2) + 10px);\r\n\r\n              &::after {\r\n                content: \"\";\r\n                position: absolute;\r\n                width: 10px;\r\n                height: 10px;\r\n                transform: rotate(45deg);\r\n                background: #fff;\r\n                border: 1px solid var(--zy-el-border-color-light);\r\n                box-sizing: border-box;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .QuestionsAndAnswersOther {\r\n        .QuestionsAndAnswersChatTextBody {\r\n          padding-right: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n\r\n          .QuestionsAndAnswersChatText {\r\n\r\n            span {\r\n              left: 0;\r\n              transform: translate(-50%, -50%);\r\n\r\n              &::after {\r\n                border-top-color: transparent !important;\r\n                border-right-color: transparent !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .QuestionsAndAnswersOneself {\r\n        flex-direction: row-reverse;\r\n\r\n        .QuestionsAndAnswersChatTextBody {\r\n          justify-content: flex-end;\r\n          padding-left: calc(((var(--zy-text-font-size) * var(--zy-line-height)) + 40px));\r\n\r\n          .QuestionsAndAnswersChatText {\r\n            span {\r\n              right: 0;\r\n              transform: translate(50%, -50%);\r\n\r\n              &::after {\r\n                border-left-color: transparent !important;\r\n                border-bottom-color: transparent !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;EACOA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAyB;;EAGzBA,KAAK,EAAC;AAA4B;;EAGlCA,KAAK,EAAC;AAAiC;;EARtDC,GAAA;EASiBD,KAAK,EAAC;;;EA4BNA,KAAK,EAAC;AAA6B;;EAIzCA,KAAK,EAAC;AAA8B;;;;;;uBAxC7CE,mBAAA,CA6CM,OA7CNC,UA6CM,GA5CJC,mBAAA,CA2CM,OA3CNC,UA2CM,GA1CJC,YAAA,CAqCeC,uBAAA;IArCDC,MAAM,EAAN,EAAM;IAACR,KAAK,EAAC,yBAAyB;IAACS,GAAG,EAAC;;IAH/DC,OAAA,EAAAC,QAAA,CAIa;MAAA,OAAwB,E,kBAA7BT,mBAAA,CAmCMU,SAAA,QAvCdC,WAAA,CAI4BC,MAAA,CAAAC,QAAQ,EAJpC,UAIoBC,IAAI;6BAAhBd,mBAAA,CAmCM;UAnCyBF,KAAK,EAJ5CiB,eAAA,CAI8CD,IAAI,CAACE,SAAS;UAAGjB,GAAG,EAAEe,IAAI,CAACG;YAC/Df,mBAAA,CAEM,OAFNgB,UAEM,GADJd,YAAA,CAAsFe,mBAAA;UAA3EC,GAAG,EAAEN,IAAI,CAACO,IAAI,GAAGT,MAAA,CAAAU,IAAI,CAACC,KAAK,GAAGX,MAAA,CAAAY,oBAAoB;UAAEC,GAAG,EAAC;4CAErEvB,mBAAA,CA8BM,OA9BNwB,UA8BM,GA7B6BZ,IAAI,CAACa,OAAO,I,cAA7C3B,mBAAA,CA2BM,OA3BN4B,UA2BM,EAAAC,kBAAA,CAAAC,MAAA,QAAAA,MAAA,OA1BJ5B,mBAAA,CAyBM;UAzBD6B,CAAC,EAAC,eAAe;UAACC,OAAO,EAAC,eAAe;UAACC,OAAO,EAAC,KAAK;UAAC,MAAI,EAAC,MAAM;UAACC,KAAK,EAAC,KAAK;UAACC,MAAM,EAAC;YAC1FjC,mBAAA,CAEoC;UADlCkC,CAAC,EAAC,oHAAoH;UACtHC,IAAI,EAAC,SAAS;UAAC,MAAI,EAAC;YACtBnC,mBAAA,CAEoC;UADlCkC,CAAC,EAAC,oHAAoH;UACtHC,IAAI,EAAC,SAAS;UAAC,MAAI,EAAC;YACtBnC,mBAAA,CAEoC;UADlCkC,CAAC,EAAC,oHAAoH;UACtHC,IAAI,EAAC,SAAS;UAAC,MAAI,EAAC;YACtBnC,mBAAA,CAEoC;UADlCkC,CAAC,EAAC,kHAAkH;UACpHC,IAAI,EAAC,SAAS;UAAC,MAAI,EAAC;YACtBnC,mBAAA,CAEoC;UADlCkC,CAAC,EAAC,yHAAyH;UAC3HC,IAAI,EAAC,SAAS;UAAC,MAAI,EAAC;YACtBnC,mBAAA,CAEoC;UADlCkC,CAAC,EAAC,uHAAuH;UACzHC,IAAI,EAAC,SAAS;UAAC,MAAI,EAAC;YACtBnC,mBAAA,CAEoC;UADlCkC,CAAC,EAAC,uHAAuH;UACzHC,IAAI,EAAC,SAAS;UAAC,MAAI,EAAC;YACtBnC,mBAAA,CAEoC;UADlCkC,CAAC,EAAC,sHAAsH;UACxHC,IAAI,EAAC,SAAS;UAAC,MAAI,EAAC;sCAlCtCC,mBAAA,gBAqCYpC,mBAAA,CAA2E,OAA3EqC,UAA2E,G,0BAAlCrC,mBAAA,CAAa,wCArClEsC,gBAAA,CAAAC,gBAAA,CAqCqE3B,IAAI,CAAC4B,IAAI,iB;;;IArC9EC,CAAA;4BAyCMzC,mBAAA,CAGM,OAHN0C,UAGM,GAFJxC,YAAA,CAA8GyC,mBAAA;IA1CtHC,UAAA,EA0C2BlC,MAAA,CAAAmC,OAAO;IA1ClC,uBAAAjB,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;MAAA,OA0C2BpC,MAAA,CAAAmC,OAAO,GAAAC,MAAA;IAAA;IAAE3B,IAAI,EAAC,UAAU;IAAE4B,QAAQ,EAAE;MAAAC,OAAA;MAAAC,OAAA;IAAA,CAA0B;IAAEC,WAAW,EAAC;2CAC/FhD,YAAA,CAA4DiD,oBAAA;IAAhDC,OAAK,EAAE1C,MAAA,CAAA2C,UAAU;IAAElC,IAAI,EAAC;;IA3C5Cb,OAAA,EAAAC,QAAA,CA2CsD;MAAA,OAAEqB,MAAA,QAAAA,MAAA,OA3CxDU,gBAAA,CA2CsD,IAAE,E;;IA3CxDG,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}