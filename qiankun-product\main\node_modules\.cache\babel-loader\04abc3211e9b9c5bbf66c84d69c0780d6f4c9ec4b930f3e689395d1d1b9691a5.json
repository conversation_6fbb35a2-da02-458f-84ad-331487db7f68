{"ast": null, "code": "function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"PostMam\"\n};\nvar _hoisted_2 = {\n  class: \"PostMamRequest\"\n};\nvar _hoisted_3 = {\n  class: \"PostMamBody\"\n};\nvar _hoisted_4 = {\n  class: \"PostMamBodyItemKey\"\n};\nvar _hoisted_5 = {\n  class: \"PostMamBodyItemValue\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_input, {\n    placeholder: \"请输入请求地址\",\n    modelValue: $setup.url,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.url = $event;\n    })\n  }, {\n    prepend: _withCtx(function () {\n      return [_createVNode(_component_el_select, {\n        modelValue: $setup.way,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.way = $event;\n        }),\n        placeholder: \"请选择\"\n      }, {\n        default: _withCtx(function () {\n          return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.wayData, function (item) {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.value,\n              label: item.label,\n              value: item.value\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    append: _withCtx(function () {\n      return [_createVNode(_component_el_button, {\n        onClick: $setup.handleRequest\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"发起请求\")]);\n        }),\n        _: 1 /* STABLE */\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleNewData\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"添加一行\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_scrollbar, {\n    always: \"\",\n    class: \"PostMamScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.paramsData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"PostMamBodyItem\",\n          key: item.id\n        }, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_input, {\n          modelValue: item.key,\n          \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n            return item.key = $event;\n          },\n          placeholder: \"key\"\n        }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_input, {\n          modelValue: item.value,\n          \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n            return item.value = $event;\n          },\n          placeholder: \"\"\n        }, {\n          prepend: _withCtx(function () {\n            return [_createVNode(_component_el_select, {\n              modelValue: item.type,\n              \"onUpdate:modelValue\": function onUpdateModelValue($event) {\n                return item.type = $event;\n              },\n              placeholder: \"请选择\"\n            }, {\n              default: _withCtx(function () {\n                return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.typeData, function (item) {\n                  return _openBlock(), _createBlock(_component_el_option, {\n                    key: item.value,\n                    label: item.label,\n                    value: item.value\n                  }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n                }), 128 /* KEYED_FRAGMENT */))];\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])];\n          }),\n          append: _withCtx(function () {\n            return [_createVNode(_component_el_button, {\n              onClick: function onClick($event) {\n                return $setup.handleDelData(item.id);\n              }\n            }, {\n              default: _withCtx(function () {\n                return _toConsumableArray(_cache[4] || (_cache[4] = [_createTextVNode(\"删除本行\")]));\n              }),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n          }),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\"])])]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  })])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_input", "placeholder", "modelValue", "$setup", "url", "_cache", "$event", "prepend", "_withCtx", "_component_el_select", "way", "default", "_Fragment", "_renderList", "wayData", "item", "_createBlock", "_component_el_option", "key", "value", "label", "_", "append", "_component_el_button", "onClick", "handleRequest", "_createTextVNode", "_hoisted_3", "type", "handleNewData", "_component_el_scrollbar", "always", "paramsData", "id", "_hoisted_4", "onUpdateModelValue", "_hoisted_5", "typeData", "handleDelData", "_toConsumableArray"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\PostMam\\PostMam.vue"], "sourcesContent": ["<template>\r\n  <div class=\"PostMam\">\r\n    <div class=\"PostMamRequest\">\r\n      <el-input placeholder=\"请输入请求地址\" v-model=\"url\">\r\n        <template #prepend>\r\n          <el-select v-model=\"way\" placeholder=\"请选择\">\r\n            <el-option v-for=\"item in wayData\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>\r\n          </el-select>\r\n        </template>\r\n        <template #append>\r\n          <el-button @click=\"handleRequest\">发起请求</el-button>\r\n        </template>\r\n      </el-input>\r\n    </div>\r\n    <div class=\"PostMamBody\">\r\n      <el-button type=\"primary\" @click=\"handleNewData\">添加一行</el-button>\r\n      <el-scrollbar always class=\"PostMamScrollbar\">\r\n        <div class=\"PostMamBodyItem\" v-for=\"item in paramsData\" :key=\"item.id\">\r\n          <div class=\"PostMamBodyItemKey\">\r\n            <el-input v-model=\"item.key\" placeholder=\"key\"></el-input>\r\n          </div>\r\n          <div class=\"PostMamBodyItemValue\">\r\n            <el-input v-model=\"item.value\" placeholder=\"\">\r\n              <template #prepend>\r\n                <el-select v-model=\"item.type\" placeholder=\"请选择\">\r\n                  <el-option v-for=\"item in typeData\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n              <template #append>\r\n                <el-button @click=\"handleDelData(item.id)\">删除本行</el-button>\r\n              </template>\r\n            </el-input>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'PostMam' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst url = ref('')\r\nconst way = ref('globalJson')\r\nconst wayData = ref([\r\n  { value: 'globalGet', label: 'GET' },\r\n  { value: 'globalPost', label: 'POST' },\r\n  { value: 'globalJson', label: 'JSON' }\r\n])\r\nconst typeData = ref([\r\n  { value: 'Number', label: 'Number' },\r\n  { value: 'String', label: 'String' },\r\n  { value: 'Boolean', label: 'Boolean' }\r\n])\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst paramsData = ref([\r\n  { id: 1, key: '', type: 'String', value: '' }\r\n])\r\nconst handleNewData = () => {\r\n  paramsData.value.push({ id: guid(), key: '', type: 'String', value: '' })\r\n}\r\nconst handleDelData = (id) => {\r\n  if (paramsData.value.length <= 1) return ElMessage({ type: 'warning', message: '请至少保留一个填写表单！' })\r\n  paramsData.value = paramsData.value.filter(v => v.id !== id)\r\n}\r\nconst handleRequest = async () => {\r\n  if (!url.value) return ElMessage({ type: 'warning', message: '请输入请求地址！' })\r\n  let params = {}\r\n  for (let index = 0; index < paramsData.value.length; index++) {\r\n    const item = paramsData.value[index]\r\n    params[item.key] = item.value\r\n  }\r\n  const { code, message } = await api[way.value](url.value, params)\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: message })\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.PostMam {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .PostMamRequest {\r\n    padding-top: var(--zy-distance-two);\r\n    padding-bottom: var(--zy-distance-two);\r\n\r\n    .zy-el-select {\r\n      width: 120px;\r\n    }\r\n  }\r\n\r\n  .PostMamBody {\r\n    width: 100%;\r\n    height: calc(100% - ((var(--zy-height) * 2) + (var(--zy-distance-two) * 3)));\r\n\r\n    .PostMamScrollbar {\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .el-scrollbar__wrap {\r\n        overflow-x: hidden;\r\n      }\r\n    }\r\n\r\n    .PostMamBodyItem {\r\n      padding-top: 12px;\r\n      width: 100%;\r\n      display: flex;\r\n    }\r\n\r\n    .PostMamBodyItemKey {\r\n      width: 320px;\r\n    }\r\n\r\n    .PostMamBodyItemValue {\r\n      width: calc(100% - 320px);\r\n\r\n      .zy-el-select {\r\n        width: 120px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;EACOA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAgB;;EAatBA,KAAK,EAAC;AAAa;;EAIbA,KAAK,EAAC;AAAoB;;EAG1BA,KAAK,EAAC;AAAsB;;;;;;;uBArBzCC,mBAAA,CAqCM,OArCNC,UAqCM,GApCJC,mBAAA,CAYM,OAZNC,UAYM,GAXJC,YAAA,CAUWC,mBAAA;IAVDC,WAAW,EAAC,SAAS;IAHrCC,UAAA,EAG+CC,MAAA,CAAAC,GAAG;IAHlD,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAG+CH,MAAA,CAAAC,GAAG,GAAAE,MAAA;IAAA;;IAC/BC,OAAO,EAAAC,QAAA,CAChB;MAAA,OAGY,CAHZT,YAAA,CAGYU,oBAAA;QARtBP,UAAA,EAK8BC,MAAA,CAAAO,GAAG;QALjC,uBAAAL,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAK8BH,MAAA,CAAAO,GAAG,GAAAJ,MAAA;QAAA;QAAEL,WAAW,EAAC;;QAL/CU,OAAA,EAAAH,QAAA,CAMuB;UAAA,OAAuB,E,kBAAlCb,mBAAA,CACYiB,SAAA,QAPxBC,WAAA,CAMsCV,MAAA,CAAAW,OAAO,EAN7C,UAM8BC,IAAI;iCAAtBC,YAAA,CACYC,oBAAA;cADwBC,GAAG,EAAEH,IAAI,CAACI,KAAK;cAAGC,KAAK,EAAEL,IAAI,CAACK,KAAK;cAAGD,KAAK,EAAEJ,IAAI,CAACI;;;;QANlGE,CAAA;;;IAUmBC,MAAM,EAAAd,QAAA,CACf;MAAA,OAAkD,CAAlDT,YAAA,CAAkDwB,oBAAA;QAAtCC,OAAK,EAAErB,MAAA,CAAAsB;MAAa;QAX1Cd,OAAA,EAAAH,QAAA,CAW4C;UAAA,OAAIH,MAAA,QAAAA,MAAA,OAXhDqB,gBAAA,CAW4C,MAAI,E;;QAXhDL,CAAA;;;IAAAA,CAAA;uCAeIxB,mBAAA,CAsBM,OAtBN8B,UAsBM,GArBJ5B,YAAA,CAAiEwB,oBAAA;IAAtDK,IAAI,EAAC,SAAS;IAAEJ,OAAK,EAAErB,MAAA,CAAA0B;;IAhBxClB,OAAA,EAAAH,QAAA,CAgBuD;MAAA,OAAIH,MAAA,QAAAA,MAAA,OAhB3DqB,gBAAA,CAgBuD,MAAI,E;;IAhB3DL,CAAA;MAiBMtB,YAAA,CAmBe+B,uBAAA;IAnBDC,MAAM,EAAN,EAAM;IAACrC,KAAK,EAAC;;IAjBjCiB,OAAA,EAAAH,QAAA,CAkBqC;MAAA,OAA0B,E,kBAAvDb,mBAAA,CAiBMiB,SAAA,QAnCdC,WAAA,CAkBoDV,MAAA,CAAA6B,UAAU,EAlB9D,UAkB4CjB,IAAI;6BAAxCpB,mBAAA,CAiBM;UAjBDD,KAAK,EAAC,iBAAiB;UAA6BwB,GAAG,EAAEH,IAAI,CAACkB;YACjEpC,mBAAA,CAEM,OAFNqC,UAEM,GADJnC,YAAA,CAA0DC,mBAAA;UApBtEE,UAAA,EAoB+Ba,IAAI,CAACG,GAAG;UApBvC,gCAAAiB,mBAAA7B,MAAA;YAAA,OAoB+BS,IAAI,CAACG,GAAG,GAAAZ,MAAA;UAAA;UAAEL,WAAW,EAAC;0EAE3CJ,mBAAA,CAYM,OAZNuC,UAYM,GAXJrC,YAAA,CAUWC,mBAAA;UAjCvBE,UAAA,EAuB+Ba,IAAI,CAACI,KAAK;UAvBzC,gCAAAgB,mBAAA7B,MAAA;YAAA,OAuB+BS,IAAI,CAACI,KAAK,GAAAb,MAAA;UAAA;UAAEL,WAAW,EAAC;;UAC9BM,OAAO,EAAAC,QAAA,CAChB;YAAA,OAGY,CAHZT,YAAA,CAGYU,oBAAA;cA5B5BP,UAAA,EAyBoCa,IAAI,CAACa,IAAI;cAzB7C,gCAAAO,mBAAA7B,MAAA;gBAAA,OAyBoCS,IAAI,CAACa,IAAI,GAAAtB,MAAA;cAAA;cAAEL,WAAW,EAAC;;cAzB3DU,OAAA,EAAAH,QAAA,CA0B6B;gBAAA,OAAwB,E,kBAAnCb,mBAAA,CACYiB,SAAA,QA3B9BC,WAAA,CA0B4CV,MAAA,CAAAkC,QAAQ,EA1BpD,UA0BoCtB,IAAI;uCAAtBC,YAAA,CACYC,oBAAA;oBADyBC,GAAG,EAAEH,IAAI,CAACI,KAAK;oBAAGC,KAAK,EAAEL,IAAI,CAACK,KAAK;oBAAGD,KAAK,EAAEJ,IAAI,CAACI;;;;cA1BzGE,CAAA;;;UA8ByBC,MAAM,EAAAd,QAAA,CACf;YAAA,OAA2D,CAA3DT,YAAA,CAA2DwB,oBAAA;cAA/CC,OAAK,WAALA,OAAKA,CAAAlB,MAAA;gBAAA,OAAEH,MAAA,CAAAmC,aAAa,CAACvB,IAAI,CAACkB,EAAE;cAAA;;cA/BxDtB,OAAA,EAAAH,QAAA,CA+B2D;gBAAA,OAAA+B,kBAAA,CAAIlC,MAAA,QAAAA,MAAA,OA/B/DqB,gBAAA,CA+B2D,MAAI,E;;cA/B/DL,CAAA;;;UAAAA,CAAA;;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}